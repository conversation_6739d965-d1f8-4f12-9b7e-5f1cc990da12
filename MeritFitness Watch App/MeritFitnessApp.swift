//
//  MeritFitnessApp.swift
//  MeritFitness Watch App
//
//  Created by <PERSON><PERSON> on 2024/10/17.
//

import SwiftUI
import WatchKit
import HealthKit

@main
struct MeritFitness_Watch_AppApp: App {
    @WKApplicationDelegateAdaptor(ExtensionDelegate.self) var appDelegate
    
    var body: some Scene {
        WindowGroup {
            ContentView()
        }
    }
}

// 确保 ExtensionDelegate 遵循 WKApplicationDelegate 协议
class ExtensionDelegate: NSObject, WKApplicationDelegate {
    func applicationDidFinishLaunching() {
        // 应用启动时的处理
        print("Watch app has launched.")
    }
    
    func applicationDidBecomeActive() {
        print("Watch app did become active")
    }

    func applicationWillResignActive(){
        print("Watch app will resign active")
    }

    func applicationWillEnterForeground(){
        print("Watch app will enter foreground")
    }

    func applicationDidEnterBackground(){
        print("Watch app did enter background")
    }

    // iOS app started a workout. -[HKHealthStore startWorkoutSession:] should be called from here
    func handle(_ workoutConfiguration: HKWorkoutConfiguration) {
        print("Watch app workoutConfiguration")
       
        ///
        if (workoutConfiguration.locationType == .indoor) {
            if let lapLength = workoutConfiguration.lapLength {
                let length = lapLength.doubleValue(for: HKUnit.count())
                WatchSessionConnect.shared.maxHeartRate = Int(length)
            }
            HeartRateMonitor.shared.startWorkout()
        }else {
            HeartRateMonitor.shared.stopWorkout()
        }
    }
    
    func handle(_ backgroundTasks: Set<WKRefreshBackgroundTask>) {
        // Sent when the system needs to launch the application in the background to process tasks. Tasks arrive in a set, so loop through and process each one.
        for task in backgroundTasks {
            // Use a switch statement to check the task type
            switch task {
            case let backgroundTask as WKApplicationRefreshBackgroundTask:
                // Be sure to complete the background task once you’re done.
                backgroundTask.setTaskCompletedWithSnapshot(false)
            case let snapshotTask as WKSnapshotRefreshBackgroundTask:
                // Snapshot tasks have a unique completion call, make sure to set your expiration date
                snapshotTask.setTaskCompleted(
                    restoredDefaultState: true,
                    estimatedSnapshotExpiration: Date.distantFuture,
                    userInfo: nil
                )
            case let connectivityTask as WKWatchConnectivityRefreshBackgroundTask:
                // Be sure to complete the connectivity task once you’re done.
                connectivityTask.setTaskCompletedWithSnapshot(false)
            case let urlSessionTask as WKURLSessionRefreshBackgroundTask:
                // Be sure to complete the URL session task once you’re done.
                urlSessionTask.setTaskCompletedWithSnapshot(false)
            case let relevantShortcutTask as WKRelevantShortcutRefreshBackgroundTask:
                // Be sure to complete the relevant-shortcut task once you're done.
                relevantShortcutTask.setTaskCompletedWithSnapshot(false)
            case let intentDidRunTask as WKIntentDidRunRefreshBackgroundTask:
                // Be sure to complete the intent-did-run task once you're done.
                intentDidRunTask.setTaskCompletedWithSnapshot(false)
            default:
                // make sure to complete unhandled task types
                task.setTaskCompletedWithSnapshot(false)
            }
        }
    }
}
