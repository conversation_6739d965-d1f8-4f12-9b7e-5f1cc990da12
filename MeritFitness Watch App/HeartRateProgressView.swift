//
//  HeartRateProgressView.swift
//  MeritFitness Watch App
//
//  Created by <PERSON><PERSON> on 2024/11/11.
//

import SwiftUI

// MARK: - HeartRateZone
enum HeartRateZone: Int, CaseIterable {
    case relaxed = 0        // 舒缓放松
    case warmUp = 1         // 热身放松
    case fatBurning = 2     // 脂肪燃烧
    case endurance = 3      // 耐力强化
    case anaerobic = 4      // 无氧极限
    
    var title: String {
        switch self {
        case .relaxed:     return "舒缓放松"
        case .warmUp:      return "热身放松"
        case .fatBurning:  return "脂肪燃烧"
        case .endurance:   return "耐力强化"
        case .anaerobic:   return "无氧极限"
        }
    }
    
    var color: Color {
        switch self {
        case .relaxed:     return Color(red: 23/240.0, green: 145/240.0, blue: 227/240.0)
        case .warmUp:      return Color(red: 11/240.0, green: 227/240.0, blue: 124/240.0)
        case .fatBurning:  return Color(red: 227/240.0, green: 108/240.0, blue: 23/240.0)
        case .endurance:   return Color(red: 230/240.0, green: 37/240.0, blue: 23/240.0)
        case .anaerobic:   return Color(red: 126/240.0, green: 9/240.0, blue: 0)
        }
    }
    
    // 心率区间范围（示例值，根据实际需求调整）
    var heartRateRange: ClosedRange<Int> {
        switch self {
        case .relaxed:     return 60...100
        case .warmUp:      return 101...120
        case .fatBurning:  return 121...140
        case .endurance:   return 141...160
        case .anaerobic:   return 161...180
        }
    }
}

struct HeartRateProgressView: View {
    // MARK: - Constants
    private enum Constants {
        static let progressWidth: CGFloat = 160
        static let progressHeight: CGFloat = 10
        static let textFontSize: CGFloat = 11
        static let cornerRadius: CGFloat = 16
        static let spacing: CGFloat = 1
    }
    
    // MARK: - Properties
    @Binding var targetIndex: Int
    @State private var textOffsetX: CGFloat = 0
    @State private var textWidth: CGFloat = 0
    
    private var currentZone: HeartRateZone {
        HeartRateZone(rawValue: targetIndex) ?? .fatBurning
    }
    
    // MARK: - Body
    var body: some View {
        ZStack {
            progressBars
            labelView
        }
    }
    
    // MARK: - Views
    private var progressBars: some View {
        HStack(alignment: .center, spacing: Constants.spacing) {
            ForEach(HeartRateZone.allCases, id: \.rawValue) { zone in
                GeometryReader { geometry in
                    Rectangle()
                        .frame(height: Constants.progressHeight)
                        .foregroundColor(zone.color)
                        .onAppear {
                            updateOffsetIfNeeded(zone: zone, geometry: geometry)
                        }
                        .onChange(of: targetIndex) { _, _ in
                            updateOffsetIfNeeded(zone: zone, geometry: geometry)
                        }
                }
            }
        }
        .frame(width: Constants.progressWidth, height: Constants.progressHeight)
        .clipped()
        .cornerRadius(Constants.progressHeight / 2)
        .background(Color.white, in: RoundedRectangle(cornerRadius: Constants.progressHeight / 2))
    }
    
    private var labelView: some View {
        Text(currentZone.title)
            .font(.system(size: Constants.textFontSize, weight: .medium, design: .rounded))
            .foregroundColor(.white)
            .padding(.horizontal, 6)
            .padding(.vertical, 2)
            .background(
                RoundedRectangle(cornerRadius: Constants.cornerRadius)
                    .fill(currentZone.color)
                    .animation(.easeInOut(duration: 0.3), value: currentZone.color)
            )
            .overlay(
                RoundedRectangle(cornerRadius: Constants.cornerRadius)
                    .stroke(Color.white, lineWidth: 1)
            )
            .offset(x: textOffsetX)
            .animation(.easeInOut(duration: 0.3), value: textOffsetX)
            .background(
                GeometryReader { proxy in
                    Color.clear.onAppear { textWidth = proxy.size.width }
                }
            )
    }
    
    // MARK: - Helper Methods
    private func updateOffsetIfNeeded(zone: HeartRateZone, geometry: GeometryProxy) {
        if zone.rawValue == targetIndex {
            updateTextOffset(for: zone, geometry: geometry)
        }
    }
    
    private func updateTextOffset(for zone: HeartRateZone, geometry: GeometryProxy) {
        let halfWidth = Constants.progressWidth / 2
        textOffsetX = switch zone {
        case .relaxed:     (textWidth / 2) - halfWidth
        case .warmUp:      geometry.frame(in: .local).midX - halfWidth + (geometry.size.width + 1)
        case .fatBurning:  0
        case .endurance:   geometry.size.width + 1
        case .anaerobic:   halfWidth - (textWidth / 2)
        }
    }
}

// MARK: - Preview
#Preview {
    HeartRateProgressView(targetIndex: .constant(1))
}


/**
 
 Text(getText(for: targetIndex))
     .frame(height: 16)
     .padding(EdgeInsets(top: 2, leading: 6, bottom: 2, trailing: 6))
     .font(.system(size: 11, weight: .medium, design: .rounded))
     .foregroundColor(Color.white)
     .background(
         GeometryReader { proxy in
             RoundedRectangle(cornerRadius: proxy.size.height / 2)
                 .strokeBorder(Color.white, lineWidth: 1)
                 .background(
                     RoundedRectangle(cornerRadius: proxy.size.height / 2).fill(getColor(for: targetIndex))
                 )
                 .onAppear {
                     textWidth = proxy.size.width
                 }
         }
     )
     .offset(x: textOffsetX)  // 根据计算的偏移量调整位置
     .animation(.easeInOut(duration: 0.3), value: getColor(for: targetIndex))
 */
