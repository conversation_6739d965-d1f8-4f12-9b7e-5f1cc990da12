//
//  HeartRateView.swift
//  MeritFitness Watch App
//
//  Created by <PERSON><PERSON> on 2024/10/18.
//

import SwiftUI

// MARK: - HeartRateZone
extension HeartRateZone{

    ///区间
    func threshold(for maxHeartRate: Double) -> Double {
        switch self {
        case .relaxed: return maxHeartRate * 0.6
        case .warmUp: return maxHeartRate * 0.7
        case .fatBurning: return maxHeartRate * 0.8
        case .endurance: return maxHeartRate * 0.9
        case .anaerobic: return maxHeartRate
        }
    }
}

// MARK: - HeartRateView
struct HeartRateView: View {
    // MARK: - Properties
    @StateObject private var heartRateManager = HeartRateMonitor.shared
    @StateObject private var connectivity = WatchSessionConnect.shared
    
    @State private var beatAnimation = false
    @State private var heartBeat = 0
    @State private var targetIndex = 0
    
    // MARK: - UI Components
    private var heartImageView: some View {
        ZStack {
            Image("defult_img_heart")
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: 60, height: 50)
                .opacity(beatAnimation ? 0 : 1)
            
            Image("live_img_heart")
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: 60, height: 50)
                .scaleEffect(beatAnimation ? 1.1 : 1.0)
                .animation(
                    beatAnimation ? 
                        .easeInOut(duration: 0.5)
                        .repeatForever(autoreverses: true) : 
                        .default,
                    value: beatAnimation
                )
                .opacity(beatAnimation ? 1 : 0)
        }
    }
    
    private var heartRateDisplay: some View {
        VStack(alignment: .center) {
            Text(heartBeat == 0 ? "--" : "\(heartBeat)")
                .font(.system(.largeTitle))
                .fontWeight(.medium)
                .fontDesign(.rounded)
                .contentTransition(.numericText(value: Double(heartBeat)))
                .foregroundColor(.white)
            
            Text("心率(bpm)")
                .font(.system(.caption2))
                .fontDesign(.rounded)
                .foregroundColor(.white)
        }
    }
    
    private var progressView: some View {
        Group {
            if heartRateManager.state {
                HeartRateProgressView(targetIndex: $targetIndex)
            }
        }
    }
    
    // MARK: - Body
    var body: some View {
        VStack(alignment: .center, spacing: 5) {
            heartImageView
            heartRateDisplay
            Spacer()
            progressView
        }
        .padding(EdgeInsets())
        .onReceive(heartRateManager.$state) { newState in
            handleHeartRateStateChange(newState)
        }
        .onReceive(heartRateManager.$heartRate) { newHeartRate in
            handleHeartRateUpdate(newHeartRate)
        }
        .onAppear {
            setupWatchSession()
        }
    }
    
    // MARK: - Private Methods
    private func setupWatchSession() {
        connectivity.activate()
    }
    
    private func handleHeartRateStateChange(_ newState: Bool) {
        print("heartRateManager.state changed to: \(newState)")
        DispatchQueue.main.async {
            beatAnimation = newState
        }
    }
    
    private func handleHeartRateUpdate(_ newHeartRate: Double) {
        withAnimation(.bouncy) {
            heartBeat = Int(newHeartRate)
            updateHeartRateZone()
        }
    }
    
    private func updateHeartRateZone() {
        targetIndex = calculateHeartRateZone(
            currentHeartRate: heartBeat,
            maxHeartRate: connectivity.maxHeartRate
        )
    }
    
    private func calculateHeartRateZone(currentHeartRate: Int, maxHeartRate: Int) -> Int {
        guard maxHeartRate > 0, currentHeartRate > 0 else { return 0 }
        
        let currentRate = Double(currentHeartRate)
        let maxRate = Double(maxHeartRate)
        
        return HeartRateZone.allCases.first { zone in
            currentRate <= zone.threshold(for: maxRate)
        }?.rawValue ?? HeartRateZone.anaerobic.rawValue
    }
}

// MARK: - Preview
#Preview {
    HeartRateView()
}



/**
 //                                Image(systemName: "suit.heart.fill")
 //                                    .font(.system(size: 50))
 //                                    .foregroundStyle(beatAnimation ? Color.red.gradient : Color.gray.gradient)
 //                                    .symbolEffect(.bounce, options: !beatAnimation ? .default : .repeating.speed(1), value: beatAnimation)
 //            
 */

