//
//  WatchSessionConnect.swift
//  MeritFitness Watch App
//
//  Created by Junq on 2024/11/12.
//

import Foundation
import WatchKit
import WatchConnectivity

final class WatchSessionConnect: NSObject, ObservableObject {
    static let shared = WatchSessionConnect()
    
    @Published var maxHeartRate: Int = 0
    @Published var requestHeartRate: Bool = false
    
    private lazy var session: WCSession? = {
        let watchSession = WCSession.default
        watchSession.delegate = self
        return watchSession
    }()
    
    override private init() {
        super.init()
    }
    
    /// 激活 WatchConnectivity 会话
    public func activate() {
        guard let session = session, WCSession.isSupported() else {
            print(self, #function, #line, "WCSession is not supported")
            return
        }
        if session.activationState != .activated {
            session.activate()
        }
    }
    
    /// 发送心率数据到 iPhone 端
    public func send(value: Int) {
        activate()
        
        guard let session = self.session, session.activationState == .activated else {
            print(self, #function, #line, "Session is not available or not activated")
            return
        }
        
        let userInfo: [String: Int] = ["heartRate": value]
        
        if session.isReachable {
            print(self, #function, #line, "Session is reachable, sending message")
            session.sendMessage(userInfo, replyHandler: nil, errorHandler: { error in
                print("Failed to send message: \(error.localizedDescription)")
            })
        } else {
            print(self, #function, #line, "Session is not reachable, updating application context")
            do {
                try session.updateApplicationContext(userInfo)
            } catch {
                print("Failed to send application context: \(error.localizedDescription)")
            }
        }
    }
    
    /// 处理接收到的请求
    private func updateRequest(from message: [String: Any]) {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            if let command = message["requestHeartRate"] as? String {
                switch command {
                case "start":
                    self.requestHeartRate = true
                    self.startHeartRateMonitoring()
                case "stop":
                    self.requestHeartRate = false
                    HeartRateMonitor.shared.stopWorkout()
                default:
                    break
                }
            }
            
            if let maxHeartRate = message["maxHeartRate"] as? Int {
                self.maxHeartRate = maxHeartRate
            }
        }
    }
    
    /// 启动心率监测
    private func startHeartRateMonitoring() {
        Task {
            do {
                let monitor = HeartRateMonitor.shared
                let isAuthorized = try await monitor.getAuthorization()
                print("Authorization result: \(isAuthorized)")
                if isAuthorized {
                    monitor.startWorkout()
                }
            } catch {
                print("Authorization failed: \(error.localizedDescription)")
            }
        }
    }
}

// MARK: - WCSessionDelegate
extension WatchSessionConnect: WCSessionDelegate {
    
    /// 会话激活完成
    public func session(_ session: WCSession, activationDidCompleteWith activationState: WCSessionActivationState, error: (Error)?) {
        if let error = error {
            print(self, #function, #line, "Session activation failed: \(error.localizedDescription)")
        } else {
            print(self, #function, #line, "Session activated successfully with state: \(activationState)")
        }
    }
    
    /// 接收到消息（带 `replyHandler`）
    public func session(_ session: WCSession, didReceiveMessage message: [String: Any], replyHandler: @escaping ([String: Any]) -> Void) {
        print(self, #function, #line, "Received message with replyHandler: \(message)")
        if message["action"] as? String == "wakeUp" {
            // 唤醒 Watch 应用（如果处于后台）
            WKExtension.shared().scheduleBackgroundRefresh(
                withPreferredDate: Date(),
                userInfo: nil,
                scheduledCompletion: { _ in
                    
                replyHandler(["action": "wakeUp"])
            })
        }
    }
    
    /// 接收到消息（无 `replyHandler`）
    public func session(_ session: WCSession, didReceiveMessage message: [String: Any]) {
        print(self, #function, #line, "Received message: \(message)")
        updateRequest(from: message)
    }
    
    /// 接收到文件传输
    public func session(_ session: WCSession, didReceive file: WCSessionFile) {
        print(self, #function, #line, "Received file: \(file.fileURL)")
    }

    /// 接收到应用上下文
    public func session(_ session: WCSession, didReceiveApplicationContext applicationContext: [String: Any]) {
        print(self, #function, #line, "Received application context: \(applicationContext)")
        HeartRateMonitor.shared.stopWorkout()
    }
    
    /// 伴侣 App 安装状态变化
    public func sessionCompanionAppInstalledDidChange(_ session: WCSession) {
        print(self, #function, #line, "Companion app installed state changed")
    }

    /// 设备可达状态变化
    public func sessionReachabilityDidChange(_ session: WCSession) {
        print(self, #function, #line, "Session reachability changed, isReachable: \(session.isReachable)")
    }
}

