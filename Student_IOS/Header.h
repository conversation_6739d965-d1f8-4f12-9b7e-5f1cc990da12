//
//  Header.h
//  Student_IOS
//
//  Created by MacPro on 2021/5/17.
//

#ifndef Header_h
#define Header_h


#import "BloothTool.h"
#import "BlueCommunicationProtocol.h"
#import "BluePeripheral.h"
#import "BlueBeatManager.h"

#import "BluetoothManager.h"
#import "BaseModel.h"

#import "BlueConfig.h"
#import "BlueDataManager.h"
#import "EquipmentModel.h"
#import "BlueOTAManager.h"
#import "RouteManager.h"
#import "RouteManager+Course.h"
#import "BluetoothCommondManager.h"
#import "EquipmentDetialModel.h"

#import "WebViewViewController.h"
#import "MRKRequestServiceData.h"
#import "AlertView.h"
#import "BlueTool.h"

#import "MRKEquipmentTypeData.h"
#import <AliyunPlayer/AliyunPlayer.h>
#import "AlivcMacro.h"
#import "NSAttributedString+SJMake.h"

#import "BlueDataDealManager.h"
#import "WriteFileManager.h"
#import "DataSourceModel.h"

#import "UIViewController+Addons.h"
#import "UIAlertController+Color.h"
#import "NSObject+Data.h"
#import "NSData+Overflow.h"
#import "UIButton+Layout.h"
#import "UIView+LXShadowPath.h"
#import "UIImage+Helper.h"
#import "NSObject+helper.h"
#import "MBProgressHUD+MRK.h"
#import "UIView+Empty.h"
#import "NSArray+Sudoku.h"
#import "UIButton+ClickArea.h"
#import "NSAttributedString+MRKSize.h"
#import "UITableView+Additions.h"
#import "UIApplication+Add.h"
#import "MrkAlertManager.h"
#import "MRKDBManager.h"
#import "NSObject+Model.h"

///app首页改版
#import "BlueDataStorageManager.h"
#import "NewConnectManager.h"
#import "MRKConnectStatusManager.h"
#import "MRKAutoConnectManager.h"
#import "PopView.h"
#import "GuideItemView.h"
#import "MRKShareManager.h"

#import "SQLogManager.h"
#import "MRKHealthManager.h"
#import "MRKFlutterViewController.h"
#import "NSObject+helper.h"
#import "NSDictionary+Empty.h"




#define kPageNumber                10
#define kMaxEllipticalSmp          150 //椭圆机 踏频
#define kMaxCarSmp                 200 //单车 踏频
#define kMaxBoatSmp                60  //划船机 桨频
#define kMaxHeartRate              200 //心率
#define kMaxStairClimbSmp          110 //爬楼机最大踏频




#define isHaveDynamicIsland    ({ BOOL isHave = NO; if (@available(iOS 16.0, *)) isHave = (([[UIApplication sharedApplication] delegate].window.safeAreaInsets.top >= 51)); (isHave); })
#define NavigationBarHeight    (self.navigationController.navigationBar.frame.size.height > 0 ? self.navigationController.navigationBar.frame.size.height : 44.0)
#define StatusHeight           [[UIApplication sharedApplication] statusBarFrame].size.height
#define NavigationHeight       ([[UIApplication sharedApplication] statusBarFrame].size.height + NavigationBarHeight)//
#define IS_IPHONEX             ([UIScreen instancesRespondToSelector:@selector(currentMode)] ? CGSizeEqualToSize(CGSizeMake(1125, 2436), [[UIScreen mainScreen] currentMode].size) : NO)
#define kTabbarSafeBottomMargin        (IS_IPHONEX ? 34.f : 0.f)



#define DocumentsDirectory     [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask,YES) objectAtIndex:0]
#define LocalServicePlistPath  [[NSBundle mainBundle]pathForResource:@"service.plist" ofType:@""]//基本配置两个环境的地址

static NSString * const ServiceEnvironmentKey =  @"ServiceEnvironmentKey";

CG_INLINE NSString * getServiceUrl(NSString *key){
    if (EnvironmentService.shared.selectedEnvironment) {
        NSDictionary *dic = [EnvironmentService.shared.selectedEnvironment.urls modelToDictionary];
        return dic[key];
    }
    NSArray *array = [[NSArray alloc] initWithContentsOfFile:LocalServicePlistPath];
    NSNumber *index = [[NSUserDefaults standardUserDefaults] valueForKey:ServiceEnvironmentKey];
    NSDictionary *dic = array[index.intValue];
    return dic[key];
}


typedef enum {
    BicycleEquipment     =  1,   //动感单车
    TreadmillEquipment   =  2,   //跑步机
    BoatEquipment        =  5,   //划船机
    EllipticalEquipment  =  6,   //椭圆机
    StairClimbEquiment   =  7,   //爬楼机
    JinMoQiangEquipment  =  9,   //筋膜枪
    SkipRopeEquipment    =  10,  //跳绳
    PowerEquipment       =  11,  //力量站
    KettleBellEquipment  =  12,  //壶铃
    SmallEquipment       =  3,   //小件
    HeartEquipment       =  100, //心率带
    HoopEquipment        =  21,  //呼啦圈
    FLSBEquipment        =  27,  //飞力士棒
    FatScaleEquipment    =  41,  //体脂秤
    OtherNoEquiment      =  32,  //其他
//    ShareKnowledge       =  36,  //知识分享
}EquipmentType;



//enum FitnessEquipment: Int, CaseIterable {
//    case stationaryBike   = 1    // 动感单车
//    case treadmill        = 2    // 跑步机
//    case smallEquipment   = 3    // 小件
//    case rowingMachine    = 5    // 划船机
//    case elliptical       = 6    // 椭圆机
//    case stairClimber     = 7    // 爬楼机
//    case massageGun       = 9    // 筋膜枪
//    case jumpRope         = 10   // 跳绳
//    case multiGym         = 11   // 力量站（多功能综合训练器）
//    case kettlebell       = 12   // 壶铃
//    case hulaHoop         = 21   // 呼啦圈
//    case flexiBar         = 27   // 飞力士棒
//    case other            = 32   // 其他
//    case bodyFatScale     = 41   // 体脂秤
//    case heartRateMonitor = 100  // 心率带
//}



typedef enum {
    ForceUpdate    =  2,
    SuggestUpdate  =  1
    
}OTAUpdateType;

typedef enum {
    Updating       = 1,//更新中
    UpdateSuccess  = 2,//更新成功
    UpdateFailure  = 3,//更新失败
    
}UpdateStatus;

typedef NS_ENUM (NSInteger ,RandomMode ) {
    FreedomMode            = 0x00,   /// 自由训练
    CutdownNumberMode      = 0x01,   /// 倒计数模式
    CutdownTimeMode        = 0x02,   /// 倒计时模式
    SuperBurnMode          = 0x03,   /// 麦瑞克协议 适用于J001跳绳
    GameMode               = 0x04,   /// 麦瑞克协议 游戏模式
};


typedef enum {
    TeLinkProtocol    =  1,
    BroadCom          =  2,
    DFUOTA            =  3,
    XinXYOTA          =  4,//新向远
    FURKOTA           =  5,//富芮坤
    LinkedsemiOTA     =  6,//凌思微

}OTAUpdateProtocolType;

typedef enum {
    DevicePauseStatus      = 0x0a,//暂停
    DevicelSlowDownStatus  = 0x04,//减速中
    DeviceStandbyStatus    = 0x00,//x1待机中
    DeviceX1StandbyStatus  = 0x01,//x1彩屏待机中
    DeviceRuningStatus     = 0x03,//运行中
    DeviceCutDownStatus    = 0x02,//启动中 倒计时
    DeviceunUseStatus      = 0x06,//禁用模式
    DeviceunUnknown        = 0xFF,//设备未连接
} TREAMILL_STATUS;


//typedef NS_ENUM(uint8_t, TreadmillStatus) {
//    // 通用状态
//    TreadmillStatusUnknown          = 0xFF, // 未知状态（设备未连接）
//
//    // 运行相关状态
//    TreadmillStatusStarting         = 0x02, // 启动中（倒计时）
//    TreadmillStatusRunning          = 0x03, // 运行中
//    TreadmillStatusSlowingDown      = 0x04, // 减速中
//    TreadmillStatusPaused           = 0x0A, // 暂停
//
//    // 待机状态
//    TreadmillStatusStandby          = 0x00, // 待机中
//    TreadmillStatusX1Standby        = 0x01, // X1 彩屏待机中
//
//    // 特殊状态
//    TreadmillStatusDisabled         = 0x06  // 禁用模式
//};

//typedef enum {
//    RealTreamill             = 1,//实景视频
//    VideoTreamill            = 2,//回放
//    LivingTreamill           = 3,//直播
//    FreedomTreamill          = 4,//自由训练
//    
//}TREAMILL_SOURCE;

typedef enum {
    HomeAlertPage             = 1,// 1-首页弹窗
    LanuchPage                = 2,// 2-APP广告页
    BannerPage                = 3,// 3-banner
    MessagePage               = 4,// 4-消息
    KingKongPage              = 5,//金刚区
    NotificationPage          = 100,//推送
    OpenUrlPage               = 101,//web里唤起 2.7.2版本以后web里打开本地页面使用唤起
    ActivityListPage          = 102,//活动赛事列表
}BANNER_POSITION;

typedef enum {
    MRKCommunicationProtocol        =  1,
    FTMSCommunicationProtocol       =  2,
    ZJCommunicationProtocol         =  3,
    BQCommunicationProtocol         =  4,
    FTMSANDZJCommunicationProtocol  =  5,
    JMQCommunicationProtocol        =  6,
    PowerCommunicationProtocol      =  7,//智健力量站协议 气动力量站【2024-06-06】新增
    ///三方sdk
    WLCommunicationProtocol         = 101, //沃莱体脂秤
    HTKCommunicationProtocol        = 102, //汇泰科
    HBCommunicationProtocol         = 103, //旧的心率带Heart-B2
    LFScaleCommunicationProtocol    = 104, //乐福体脂秤
    
    SportShowCommunicationProtocol  = 105, //运动秀协议
    QCCommunicationProtocol         = 106, //全创壶铃协议
}BlueDataCommunicationProtocolType;


typedef enum {
    EigenValue2A26                 =  1,
    EigenValue2A28                 =  2,
  
}BlueEigenValueType;

/// 图表的时间跨度
typedef enum {
    MRKDataChartTimeDay     =  1, //日
    MRKDataChartTimeWeek    =  2, //周
    MRKDataChartTimeMonth   =  3, //月
    MRKDataChartTimeYear    =  4, //年
    MRKDataChartTimeAll     =  0  //总
}MRKDataChartTimeType;

/// 图表的类型
typedef enum {
    MRKDataBarChart         =  1 << 0, //柱状图
    MRKDataLineChart        =  1 << 1  //折线图
}MRKDataChartType;

/// 图表的内容类型
typedef enum {
    MRKDataChartSportContentKcal          =  1 << 0, //运动消耗
    MRKDataChartSportContentTakeTime      =  1 << 1, //运动时长
    MRKDataChartSportContentDistance      =  1 << 2  //运动距离
}MRKDataChartSportContentType;

/// 图表的内容类型
typedef enum {
    MRKDataChartHealthContentWeight         =  1, //体重
    MRKDataChartHealthContentHeight         =  4, //身高
    MRKDataChartHealthContentBMI            =  3, //BMI
    MRKDataChartHealthContentBodyFatRate    =  2  //体脂率
}MRKDataChartHealthContentType;


typedef enum {
    TraceVipOpenSourceTypeMyInfo                =  1,  //我的会员信息
    TraceVipOpenSourceTypeCourse                =  5,  //课程详情
    TraceVipOpenSourceTypePlayTest              =  6,  //播放试看开通会员
    TraceVipOpenSourceTypePlayNormal            =  7,  //播放开通会员-左下角的小弹窗
    TraceVipOpenSourceTypePlayExpire            =  8,  //上课页面-到期提示语
    TraceVipOpenSourceTypeAIPlan                =  12,  //AI计划
    TraceVipOpenSourceTypeAIChat                =  13,  //AI对话
    TraceVipOpenSourceTypeAIDiet                =  14,  //AI饮食
}TraceVipOpenSourceType;

static NSString *const AdvertSkipToPageNotification = @"AdvertSkipToPageNotification"; // banner/广告页/弹窗/消息 跳转 某个页面 {model:model , position:BANNER_POSITION}
static NSString *const JudgeConnectDeviceNotification = @"JudgeConnectDeviceNotification"; //判断是否连接设备
static NSString *const SetTabBarItemIndexNotification = @"SetTabBarItemIndexNotification"; //  跳转tabbar 页面
static NSString *const UserLevelUpNotification = @"UserLevelUpNotification";        // 用户等级升级通知
static NSString *const WeightScaleUserID = @"WeightScaleUserID";                    // 存储用户体脂秤当前用户id
static NSString *const CourseSearchHistoryRecords =  @"CourseSearchHistoryRecords";


#define kAlertMaxResistance    @"当前为最大阻力"
#define kAlertMinResistance    @"当前为最小阻力"
#define kAlertMaxSpeed         @"当前为最大速度"
#define kAlertMinSpeed         @"当前为最小速度"
#define kAlertMaxSlope         @"当前为最大坡度"
#define kAlertMinSlope         @"当前为最小坡度"
#define kAlertMaxGear          @"当前为最大档位"
#define kAlertMinGear          @"当前为最小档位"

#define kTelephoneNumber       @"************"
#define kNetworkErrorMessage   @"网络未连接,请检查网络设置"


#define kTopBlankNumber            3
#define kBottomBlankNumber         3

#define kDefineSkipMAXNumber       9999
#define kDefineDistanceMAXNumber   500
#define kDefineTimeMinNumber       30
#define kDefineSkipMINNumber       50

#define kDefineSkipOTAElectric     30 //跳绳ota 最低电量
#define kDefineArmOTAElectric      20 //心率臂带ota 最低电量
 




#define Bebas_Font                           @"Bebas"
#define DOUYU_Font                           @"DOUYU-Font"

#define fontNamePing                         @"PingFangSC-Regular"
#define fontNameMeDium                       @"PingFangSC-Medium"

#define kMedium_Font(size)                   kMedium_PingFangSC(size)
#define kSystem_Font(size)                   kRegular_PingFangSC(size)

#define kMedium_PingFangSC(s)                [UIFont fontWithName:@"PingFangSC-Medium" size:DHPX(s)]
#define kRegular_PingFangSC(s)               [UIFont fontWithName:@"PingFangSC-Regular" size:DHPX(s)]
#define kSemibold_PingFangSC(s)              [UIFont fontWithName:@"PingFangSC-Semibold" size:DHPX(s)]

#define kMedium_Font_NoDHPX(s)               [UIFont fontWithName:@"PingFangSC-Medium" size:s]
#define kSystem_Font_NoDHPX(s)               [UIFont fontWithName:@"PingFangSC-Regular" size:s]
#define kSemibold_PingFangSC_NoDHPX(s)       [UIFont fontWithName:@"PingFangSC-Semibold" size:s]

#define BebasFont_Bold(s)                    [UIFont fontWithName:Bebas_Font size:DHPX(s)]
#define BebasFont_Bold_NoDHPX(s)             [UIFont fontWithName:Bebas_Font size:s]


#define kSearchTimeout                       10
#define kTwoSearchTimeout                    5

#define MRKDailyScheduleNotificationCode     @"dailySchedule_notification"
#define MRKCourseDetailNotificationCode      @"courseDetail_notification"
#define MRKHomeNotificationCode              @"home_notification"

#endif /* Header_h */



