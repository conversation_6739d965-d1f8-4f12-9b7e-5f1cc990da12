//
//  RouteManager.m
//  Student_IOS
//
//  Created by MacPro on 2021/5/26.
//

#import "RouteManager.h"
#import "AdvertModel.h"
#import "MRKToolkit.h"

#import "MRKCourseModel.h"
#import "CourseTipController.h"
//埋点
#import "ReserveCourseController.h"
#import "MRKCoachDetailController.h"
#import "AllLivingCourseController.h"
#import "MRKActivityWebController.h"
#import "MRKThemeListController.h"
#import "MRKCoursePlanController.h"


#import "DeviceSearchViewController.h"
#import "MRKCoachDetailController.h"
#import "MRKCoachFindController.h"
#import "MydetailViewController.h"

#import "MRKGeneralWebController.h"
#import "MRKMyGrowUpController.h"
#import "MRKMedalController.h"
#import "AbilityIntroduceVC.h"
#import "MRKPopupManager.h"
#import "MRKUltraTrainCourseDetailVC.h"
#import "MRKUltraThemeCourseListVC.h"
#import "MyVoiceViewController.h"
#import "MrkAlertManager.h"
#import "MRKDeviceConnectAlertView.h"
#import "MRKHrCtrlPushConfig.h"
#import "MRKUltraTrainHomeVC.h"
#import "MRKHomeGuideBuyAlertView.h"
#import "MRKDeviceURLRequest.h"
#import "MRKTraceManager.h"
#import "MRKCategoryCourseController.h"
#import "MRKDeviceDetailController.h"
#import "MRKWeightViewController.h"
#import "MRKMineDataViewController.h"
#import "NSDictionary+Empty.h"
#import "MRKUserIAPController.h"
#import "WeightMeasureViewController.h"
#import "InputWeightViewController.h"
#import "MRKHealthHistoryVC.h"
#import "AbilityTestResultController.h"

#import "MRKFramdomTrainController.h"
#import "MrkFindCourseController.h"
#import "MRKUltraCommonModel.h"
#import "MRKAllCourseViewController.h"
#import "MRKRenewPagingController.h"
#import "MRKScanQrcodeViewController.h"
#import "MRKCourseDetailController.h"
#import "ExerciseReportWebController.h"
#import "MRKAIPlanLogic.h"
#import "MRKAIPlanCheckModel.h"


@interface RouteManager ()
@end

@implementation RouteManager

static id _instance;
+ (instancetype)sharedInstance{
    @synchronized(self){
        if(_instance == nil){
            _instance = [[self alloc] init];
            [_instance loadModel];
        }
    }
    return _instance;
}

- (UIViewController *)rootController{
    UIViewController *controller = [UIViewController currentViewController];
    return controller;
}

- (void)loadModel {
    ///2023 - 8- 28 junq 剔除老版banner跳转
    
    ///蓝牙连接
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(judgeConnect:)
                                                 name:JudgeConnectDeviceNotification
                                               object:nil];
    
    ///推荐位跳转 [弹窗, banner , 金刚区etc]
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(advertSkip:)
                                                 name:AdvertSkipToPageNotification
                                               object:nil];
}

- (void)judgeConnect:(NSNotification *)notification {
    NSLog(@"judgeConnect -- %@" , notification.object);
    id data = notification.object;
    [self skipCourse:data];
}

/// Banner点击
- (void)skipAdvertModelWithJsonStr:(NSString *)jsonStr{
    AdvertModel *model = [AdvertModel modelWithJSON:jsonStr];
    [self advertModelSkip:model];
}

- (void)advertSkip:(NSNotification *)sender{
    id data = [sender.object objectForKey:@"model"];
    NSAssert([data isKindOfClass:[AdvertModel class]], @"推荐位跳转接收模型 AdvertModel 匹配异常");
    
    AdvertModel *model = (AdvertModel *)data;
    NSNumber *position = [sender.object objectForKey:@"position"];
    model.bannerType = position;
    
    [self advertModelSkip:model];
}

- (void)advertModelSkip:(AdvertModel *)model{
    ///app跳站外app 站外app链接
    if ([model.contentType isEqualToString:@"app_app"]) {
        [self openAppJumpAppUrl: model.content];
    }
    
    ///app站内跳转 app站内跳转模块
    if ([model.contentType isEqualToString:@"app_link"]) {
        NSURL *url = [NSURL URLWithString:model.content];
        [self applicationOpenURL:url];
    }
    
    ///app站内跳转 app站内跳转链接
    if ([model.contentType isEqualToString:@"app_h5"]) {
        NSURL *url = [NSURL URLWithString:model.content];
        NSLog(@"======%@",url.absoluteString);
        NSLog(@"======%@",url.scheme);
        NSLog(@"======%@",url.host);
        if ([url.host containsString:@"youzan.com"]
            || [url.host containsString:@"m.youzan.com"]) {
            ///单独处理有赞商城
            [self skipGeneralWeb:model.content];
        } else {
            [self skipWebView:model];
        }
    }
    
    ///app跳转微信小程序
    if ([model.contentType isEqualToString:@"app_mini"]) {
        NSURL *url = [NSURL URLWithString:model.content];
        NSLog(@"======%@",url.absoluteString);
        NSLog(@"======%@",url.scheme);
        NSLog(@"======%@",url.host);
    }
}

/// app_h5
- (void)openAppJumpH5Url:(NSString *)content {
    NSURL *url = [NSURL URLWithString:content];
    NSLog(@"======%@",url.absoluteString);
    NSLog(@"======%@",url.scheme);
    NSLog(@"======%@",url.host);
    if ([url.host containsString:@"youzan.com"]
        || [url.host containsString:@"m.youzan.com"]) {
        ///单独处理有赞商城
        [self skipGeneralWeb:content];
    } else {
        [self skipWeb:content hiddenNav:YES];
    }
}

/// app_app 
- (void)openAppJumpAppUrl:(NSString *)content {
    UIApplication *application = [UIApplication sharedApplication];
    NSURL *url = [NSURL URLWithString:content];
    if ([application canOpenURL:url]) {
        if (@available(iOS 10.0, *)) {
            [application openURL:url
                         options:@{}
               completionHandler:^(BOOL success) {
                if (!success) {
                    [AppDelegate errorView:@"跳转失败,请重试"];
                }
            }];
        } else {
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"
            [application openURL:url];
#pragma clang diagnostic pop
        }
    } else {
        NSString *tipStr = [MRKToolKit urlShchemesName:[NSURL URLWithString:content]];
        if ([tipStr isNotBlank]){
            [AppDelegate errorView:[NSString stringWithFormat:@"%@未安装",tipStr]];
        }else{
            UIApplication *application = [UIApplication sharedApplication];
            NSURL *url = [NSURL URLWithString:content];
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"
            [application openURL:url];
#pragma clang diagnostic pop
        }
    }
}

///跳转自由训练
- (void)skipFreedomTraining:(NSString *)productType{
    UIViewController *controller = [MRKPushManager presentingVC];
    MRKFramdomTrainController *vc = [MRKFramdomTrainController new];
    vc.equimentTypeId = productType;
    [controller.navigationController pushViewController:vc animated:YES];
}

#pragma mark - webview
- (void)skipWebView:(AdvertModel *)model {
    WebViewViewController *vc = [WebViewViewController new];
    vc.htmlURL = model.content;
    vc.titleString = model.title;
    vc.activitySource = model.bannerType;
    vc.isHiddenNav = YES;///2.7.2版本以后  默认隐藏导航条  22-05-25
    vc.hidesBottomBarWhenPushed = YES;
    [self.rootController.navigationController pushViewController:vc animated:YES];
}

#pragma mark - 扫描二维码
/// 跳转到扫描二维码接口
- (void)jumpToScanQrcodeVC:(NSString * _Nullable )scene{
    MRKScanQrcodeViewController *vc = [[MRKScanQrcodeViewController alloc]init];
    if (scene.isNotEmpty) {
        vc.scene = scene;
    }
    vc.hidesBottomBarWhenPushed = YES;
    [self.rootController.navigationController pushViewController:vc animated:YES];
}

#pragma mark - 课程详情
/// 跳转到课程详情
- (void)jumpToCourseDetailWithModel:(MRKCourseModel *)model{
    [[RouteManager sharedInstance] jumpToCourseDetailWithId:model.courseId];
}

- (void)jumpToCourseDetailWithId:(NSString *)courseId{
    MRKCourseDetailController *vc = [[MRKCourseDetailController alloc]init];
    vc.courseId = courseId;
    vc.hidesBottomBarWhenPushed = YES;
    [self.rootController.navigationController pushViewController:vc animated:YES];
}

- (void)jumpToPlanCourseDetailWithId:(NSString *)courseId{
    MRKCourseDetailController *vc = [[MRKCourseDetailController alloc]init];
    vc.courseId = courseId;
    vc.courseInAIPlan = YES;
    vc.hidesBottomBarWhenPushed = YES;
    [self.rootController.navigationController pushViewController:vc animated:YES];
}


- (void)jumpToMotionDetailWithId:(NSString *)motionId trainTarget:(NSString *)target{
   UIViewController *vc = [FlutterManager motionDetail:motionId trainTargetValue:target];
   vc.hidesBottomBarWhenPushed = YES;
   [self.rootController.navigationController pushViewController:vc animated:YES];
}




/// 跳转超燃脂自由训练
/// - Parameters:
///   - type: 设备类型
///   - skip: 跳转操作
- (void)skipUltraTrain:(NSString *)type skipAction:(void(^)(void))skip {
    // 设备连接
    if ([BluetoothManager isConnectEquipmentType:type]) {
        skip();
        return;
    }
    
    // 未连接
    [MRKDeviceConnectAlertView alertConnectDeviceView:type cancel:^(id data) {}];
}






#pragma mark - vip
- (void)skipVIP {
    MRKUserIAPController *vc = [[MRKUserIAPController alloc] init];
    vc.hidesBottomBarWhenPushed = YES;
    [self.rootController.navigationController pushViewController:vc animated:YES];
}


#pragma mark - 跳转到主题{课程/实景/训练计划}
- (void)skipThemeCoursePage:(NSDictionary *)data {
    MRKThemeListController *vc = [MRKThemeListController new];
    vc.showTitle = [NSString stringWithFormat:@"%@", [data objectForKey:@"title"]];
    vc.themeId = [NSString stringWithFormat:@"%@", [data objectForKey:@"id"]];
    vc.cover = [NSString stringWithFormat:@"%@", [data objectForKey:@"cover"]];
    vc.themeType = [[data objectForKey:@"type"] intValue];
    vc.hidesBottomBarWhenPushed = YES;
    [self.rootController.navigationController pushViewController:vc animated:YES];
}


#pragma mark - --- 有赞商城 ---
- (void)skipGeneralWeb:(NSString *)url {
    MRKGeneralWebController *vc = [[MRKGeneralWebController alloc] init];
    vc.httpUrl = url;
    vc.hidesBottomBarWhenPushed = YES;
    [self.rootController.navigationController pushViewController:vc animated:YES];
}

- (void)skipWeb:(NSString *)url hiddenNav:(BOOL)hidden {
    WebViewViewController *vc = [WebViewViewController new];
    vc.isHiddenNav = hidden;
    vc.htmlURL = url;
    vc.hidesBottomBarWhenPushed = YES;
    [self.rootController.navigationController pushViewController:vc animated:YES];
}
- (void)skipToMia:(NSString *)url text:(NSString *)text {
    WebViewViewController *vc = [WebViewViewController new];
    vc.isHiddenNav = YES;
    vc.htmlURL = url;
    vc.miaVoiceText = text;
    vc.hidesBottomBarWhenPushed = YES;
    [self.rootController.navigationController pushViewController:vc animated:YES];
}

/// 跳转到七鱼页面
- (void)skipServiceWeb {
    [self skipWeb:MRKAppH5LinkCombine(MRKUserHelpAndFeedback) hiddenNav:YES];
}


#pragma mark - open url
- (void)applicationOpenURL:(NSURL *)url{
    NSString *urlStr = [url absoluteString];
    UIViewController *controller = [MRKPushManager presentingVC];
    
    NSString *traceRoute = [[NSString getIdFromUrlParams:urlStr andCode:@"traceRoute"] URLDecode];
    if (traceRoute.length > 0) {
        [MRKTraceManager sharedInstance].traceRoute = traceRoute;
    }
    
    NSLog(@"applicationOpenURL urlStr====== %@", urlStr);
    NSLog(@"applicationOpenURL controller====== %@", controller);
    if (!controller) {
        return;
    }
    
    NSString *path = [url.path stringByReplacingOccurrencesOfString:@"/" withString:@""];;
    if ([path isEqualToString:@"courseDetail"] ||
        [path isEqualToString:@"course"]) { //课程详情
        
        [[RouteManager sharedInstance] jumpToCourseDetailWithId:[NSString getIdFromUrlParams:urlStr andCode:@"id"]];
    } else if ([path isEqualToString:@"motionDetail"]) { //动作详情
        
        NSString *motionId = [NSString getIdFromUrlParams:urlStr andCode:@"id"];
        NSString *trainTargetValue = [NSString getIdFromUrlParams:urlStr andCode:@"trainingTargetValue"];
        [[RouteManager sharedInstance] jumpToMotionDetailWithId:motionId trainTarget:trainTargetValue];
        
    } else if ([path isEqualToString:@"webview"]) {
        ///
        NSString *url = [[NSString getIdFromUrlParams:urlStr andCode:@"url"] URLDecode];
        if ([url containsString:@"youzan.com"]
            || [url containsString:@"m.youzan.com"]) {
            ///单独处理有赞商城 23-01-11 修改
            [self skipGeneralWeb:url];
            return;
        }
        
        //网页跳转网页 22-05-27
        WebViewViewController *vc = [WebViewViewController new];
        vc.isHiddenNav = YES;
        vc.htmlURL = url;
        [controller.navigationController pushViewController:vc animated:YES];
        
    } else if ([path isEqualToString:@"trainDetail"]) {
        
        //训练计划详情页
        MRKCoursePlanController *vc = [[MRKCoursePlanController alloc]init];
        vc.model = ({
            MRKCoursePlanModel *model = [MRKCoursePlanModel new];
            model.cid = [NSString getIdFromUrlParams:urlStr andCode:@"id"];
            model;
        });
        [controller.navigationController pushViewController:vc animated:YES];
        
    } else if ([path isEqualToString:@"toHomeIndex"]) {
        //tabbar 4个主页面
        NSInteger tabIndex = 0;
        NSString *tab = [NSString getIdFromUrlParams:urlStr andCode:@"tab"];
        NSString *index = [NSString getIdFromUrlParams:urlStr andCode:@"index"];
        if ([tab isNotBlank]) {
            if ([tab isEqualToString:@"home"]) {
                tabIndex = 0;
            }else if ([tab isEqualToString:@"train"]) {
                tabIndex = 1;
            }else if ([tab isEqualToString:@"discover"]) {
                tabIndex = 2;
            }else if ([tab isEqualToString:@"ucenter"]) {
                tabIndex = 3;
            }
        }else if ([index isNotBlank]){
            tabIndex = index.integerValue;
        }
        
        if (controller.navigationController.childViewControllers.count > 1) {
            [controller.navigationController popViewControllerAnimated:YES];
        } else {
            [controller dismissViewControllerAnimated:YES completion:nil];
        }
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3* NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [[NSNotificationCenter defaultCenter] postNotificationName:@"SetTabBarItemIndex" object:@(tabIndex)];
        });
        
    } else if([path isEqualToString:@"addDevice"]) {
        NSString *type = [NSString getIdFromUrlParams:urlStr andCode:@"type"];
        
        ///绑定成功
        [MRKPopupManager sharedInstance].closeAllAlert = ^(id data) {
            ///新手任务过来 绑定成功 无ota 有勋章弹窗，弹窗关闭后 需要弹出会员弹窗 22-12-09 zqp
            [[NSNotificationCenter defaultCenter] postNotificationName:@"ShowH5Alert" object:nil];
        };
        
        ///跳转到添加设备选择大类
        DeviceSearchViewController *vc = [[DeviceSearchViewController alloc] init];
        vc.hidesBottomBarWhenPushed = YES;
        vc.type = [NSNumber numberWithString:type];
        [controller.navigationController pushViewController:vc animated:YES];
        
    } else if([path isEqualToString:@"liveCourse"]) {
        ///直播列表
        AllLivingCourseController *vc = [[AllLivingCourseController alloc]init];
        [controller.navigationController pushViewController:vc animated:YES];
        
    } else if([path isEqualToString:@"vip"]) {
        ///vip
        NSString *source = [NSString getIdFromUrlParams:urlStr andCode:@"source"];
        if ([source isNotBlank]){
            [DYFStoreManager shared].appPurchaseSource = source.integerValue;
        }
        [[RouteManager sharedInstance] skipVIP];
        
    } else if([path isEqualToString:@"vipExchange"]) {
        ///(vip兑换) [本地兑换剔除了, 走网页兑换配置跳转]
        
    } else if([path isEqualToString:@"theme"]) {
        ///主题
        NSString *type = [NSString getIdFromUrlParams:urlStr andCode:@"type"];
        if ([type isEqualToString:@"planlist"]) {
            /// 金刚区训练计划列表
            NSString *isVip = [NSString getIdFromUrlParams:urlStr andCode:@"isVip"];
            MRKCategoryCourseController *vc  = [[MRKCategoryCourseController alloc] init];
            vc.type = MRKCategoryCourseTypePlan;
            if ([isVip isNotBlank]){
                vc.isVip = isVip;
            }
            vc.hidesBottomBarWhenPushed = YES;
            [controller.navigationController pushViewController:vc animated:YES];
            return;
        }
        
        [self skipThemeCoursePage:@{
            @"title" : [[NSString getIdFromUrlParams:urlStr andCode:@"title"] URLDecode],
            @"id" : [NSString getIdFromUrlParams:urlStr andCode:@"id"],
            @"cover" : [[NSString getIdFromUrlParams:urlStr andCode:@"cover"] URLDecode] ,
            @"type": [NSString getIdFromUrlParams:urlStr andCode:@"type"]
        }];
        
    } else if([path isEqualToString:@"coach"]) {
        ///教练
        MRKCoachDetailController *vc = [MRKCoachDetailController new];
        vc.showCocoaId = [NSString getIdFromUrlParams:urlStr andCode:@"id"];
        [controller.navigationController pushViewController:vc animated:YES];
        
    } else if([path isEqualToString:@"coachList"]) {
        ///教练列表
        MRKCoachFindController *vc = [MRKCoachFindController new];
        [controller.navigationController pushViewController:vc animated:YES];
        
    } else if([path isEqualToString:@"activityList"]) {
        ///活动赛事
        WebViewViewController *vc = [WebViewViewController new];
        vc.htmlURL = MRKAppH5LinkCombine(MRKActiveExercise);
        vc.isHiddenNav = YES;
        [controller.navigationController pushViewController:vc animated:YES];
        
    } else if([path isEqualToString:@"userinfoEdit"]) {
        ///完善用户信息
        MydetailViewController *vc = [[MydetailViewController alloc]init];
        vc.hidesBottomBarWhenPushed = YES;
        [controller.navigationController pushViewController:vc animated:YES];
        
    } else if([path isEqualToString:@"userGrowth"]) {
        ///用户成长页
        MRKMyGrowUpController *vc = [[MRKMyGrowUpController alloc]init];
        vc.hidesBottomBarWhenPushed = YES;
        [controller.navigationController pushViewController:vc animated:YES];
        
    } else if([path isEqualToString:@"medal"]) {
        NSString *categoryId = [NSString getIdFromUrlParams:urlStr andCode:@"categoryId"];
        ///勋章页
        [MRKRequestServiceData getUserInfo:@{} success:^(id data) {
            User *user = [User modelWithJSON:data];
            MRKGrowUpUserModel *model = [MRKGrowUpUserModel modelWithJSON:[user.levelInfo modelToJSONObject]];
            model.nickName = user.basicInfo.nickname;
            model.avatarString = user.basicInfo.avatar;
            model.medalNum = [NSString stringWithFormat:@"%d", user.medalInfo.userMedalSize];
            
            MRKMedalController *vc = [[MRKMedalController alloc] init];
            vc.growUserModel = model;
            vc.categoryId = categoryId;
            vc.hidesBottomBarWhenPushed = YES;
            [controller.navigationController pushViewController:vc animated:YES];
        } failure:^(id data) {}];
        
    } else if([path isEqualToString:@"abilityTest"] || [path isEqualToString:@"abilityTestResult"]) {//能力测评 2025.2.12修改，兼容线上跳转问题,否则线上版本会出现参数异常
//        NSString *deviceRelId = [NSString getIdFromUrlParams:urlStr andCode:@"equipId"]; // 非必要
//        NSString *equipmentTypeId = [NSString getIdFromUrlParams:urlStr andCode:@"equipType"]; // 非必要
        AbilityIntroduceVC *vc = [[AbilityIntroduceVC alloc] init];
//        vc.deviceRelId = deviceRelId;
//        vc.equipmentTypeId = equipmentTypeId;
        [controller.navigationController pushViewController:vc animated:YES];
    } else if([path isEqualToString:@"abilityTestResultPage"]) {//能力测评结果页
        AbilityTestResultController *vc = [[AbilityTestResultController alloc] init];
        [controller.navigationController pushViewController:vc animated:YES];
    } else if([path isEqualToString:@"toIntelligentDetails"]) {
        ///超燃脂自由训练详情
        MRKUltraTrainCourseDetailVC *detailvc = [[MRKUltraTrainCourseDetailVC alloc] init];
        detailvc.courseId = [NSString getIdFromUrlParams:urlStr andCode:@"id"];;
        [controller.navigationController pushViewController:detailvc animated:YES];
        
    } else if([path isEqualToString:@"toIntelligentSpecialList"]) {
        ///超燃脂自由训练专项训练
        MRKUltraThemeModel *model = [[MRKUltraThemeModel alloc] init];
        model.themeId = [NSString getIdFromUrlParams:urlStr andCode:@"id"];
        model.cover = [[NSString getIdFromUrlParams:urlStr andCode:@"cover"] URLDecode];
        model.themeName = [[NSString getIdFromUrlParams:urlStr andCode:@"title"] URLDecode];
        model.introduce = [[NSString getIdFromUrlParams:urlStr andCode:@"introduce"] URLDecode];
        
        MRKUltraThemeCourseListVC * vc = [[MRKUltraThemeCourseListVC alloc] init];
        vc.titleStr = model.themeName;
        vc.themeType = MRKUltraThemeTypeSpecial;
        [vc setSpecial:model];
        [controller.navigationController pushViewController:vc animated:YES];
        
    } else if([path isEqualToString:@"feedback"]) {
        ///意见反馈
        MyVoiceViewController *vc = [[MyVoiceViewController alloc]init];
        vc.hidesBottomBarWhenPushed = YES;
        [controller.navigationController pushViewController:vc animated:YES];
        
    } else if ([path isEqualToString:@"AllCourse"]) {
        ///全部课程
        NSString *productId = [NSString getIdFromUrlParams:urlStr andCode:@"productId"];
        [self skipToAllCourse:productId];
        
    } else if ([path isEqualToString:@"RateChooseMode"]) {
        ///心率智控
        NSString *productId = [NSString getIdFromUrlParams:urlStr andCode:@"productId"];
        [MRKHrCtrlPushConfig hrCtrlPushConfig:productId];
        
    } else if ([path isEqualToString:@"Music"]) {
        ///智能调控
        NSString *productId = [NSString getIdFromUrlParams:urlStr andCode:@"productId"];
        [self jumpToUltraHomeVC:productId];
        
    } else if ([path isEqualToString:@"DeviceTrain"]) {
        ///自由训练
        NSString *productId = [NSString getIdFromUrlParams:urlStr andCode:@"productId"];
        MRKFramdomTrainController *vc = [MRKFramdomTrainController new];
        vc.equimentTypeId = productId;
        [controller.navigationController pushViewController:vc animated:YES];

    } else if ([path isEqualToString:@"liveVideo"] ||
               [path isEqualToString:@"liveVideoList"]) {
        /// 更多实景视频
        [self jumpMoreCategoryCourse:MRKCategoryCourseTypeLive];
        
    } else if ([path isEqualToString:@"referralPlan"]) {  
        /// 更多训练计划
        NSString *isVip = [NSString getIdFromUrlParams:urlStr andCode:@"isVip"];
        MRKCategoryCourseController *vc  = [[MRKCategoryCourseController alloc] init];
        vc.type = MRKCategoryCourseTypePlan;
        if ([isVip isNotBlank]){
            vc.isVip = isVip;
        }
        vc.hidesBottomBarWhenPushed = YES;
        [controller.navigationController pushViewController:vc animated:YES];

    } else if ([path isEqualToString:@"themeCourse"]) {  
        /// 甄选主题课
        NSDictionary *dic = @{
            @"themeId":[NSString getIdFromUrlParams:urlStr andCode:@"id"]?:@"",
            @"name":[[NSString getIdFromUrlParams:urlStr andCode:@"title"] URLDecode]?:@"",
            @"introduce":[[NSString getIdFromUrlParams:urlStr andCode:@"introduce"] URLDecode]?:@"",
            @"cover":[[NSString getIdFromUrlParams:urlStr andCode:@"cover"] URLDecode]?:@"",
        };
        NSString *body = dic.dicForUrlArgument;
        NSString *Url = [NSString stringWithFormat:@"%@?%@", MRKHomePageRecommendListSubject,body];
        NSString *link = MRKAppH5LinkCombine(Url);
        [[RouteManager sharedInstance] skipWeb:link hiddenNav:YES];
        
    } else if ([path isEqualToString:@"weight"]) {  
        ///称体重
        User *user = [Login curLoginUser];
        if (user.healthInfo.weight.intValue == 0) {
            MRKFlutterFirstWeightController *vc = [[MRKFlutterFirstWeightController alloc] init];
            vc.hidesBottomBarWhenPushed = YES;
            [self.rootController.navigationController pushViewController:vc animated:YES];
            return;
        }
        
        @weakify(self);
        [self requestFatScaleInfo:^(MRKDeviceModel *model) { //体脂秤首页
            @strongify(self);
            [self jumpToWeightCenterVC: model];
        } failure:^{
            @strongify(self);
//            [self jumpDataCenter];
            
            MRKFlutterWeightManageController *vc = [[MRKFlutterWeightManageController alloc] init];
            vc.hidesBottomBarWhenPushed = YES;
            [self.rootController.navigationController pushViewController:vc animated:YES];
        }];
    } else if ([path isEqualToString:@"findCourse"]) {  //找课程
        NSString *productId = [NSString getIdFromUrlParams:urlStr andCode:@"productId"];
        MrkFindCourseController *vc = [MrkFindCourseController new];
        vc.equimentTypeId = productId;
        [controller.navigationController pushViewController:vc animated:YES];
        
    } else if ([path isEqualToString:@"DeviceBind"]) {
        ///新人链路绑定设备页面
        DeviceSearchViewController *vc = [DeviceSearchViewController new];
//        NSString *source = [NSString getIdFromUrlParams:urlStr andCode:@"source"];
//        vc.source = source;
        [controller.navigationController pushViewController:vc animated:YES];
        
    } else if ([path isEqualToString:@"CommunityWelfare"]) {
        ///社群福利
        WebViewViewController *vc = [WebViewViewController new];
        vc.htmlURL = [NSString stringWithFormat:@"%@%@", WebBase_URL, MRKTrainPart];
        vc.isHiddenNav = YES;
        [controller.navigationController pushViewController:vc animated:YES];
        
    } else if ([path isEqualToString:@"viprecord"]) { 
        ///vip开通记录
        MRKRenewPagingController *vc = [[MRKRenewPagingController alloc] init];
        [controller.navigationController pushViewController:vc animated:YES];
        
    } else if ([path isEqualToString:@"AIPlan"]){
        ///AIPlan入口
        [[MRKAIPlanLogic shared] jumpToAIPlan];
    } else if ([path isEqualToString:@"linkFoodRecord"]){
        ///饮食记录
        NSString *date = [NSString getIdFromUrlParams:urlStr andCode:@"date"];
        [[MRKDailyLogic shared] jumpToDietRecord:date index:0];
    }
}


///推送跳转
- (void)applicationOpenNotification:(NSDictionary *)info{
    UIViewController *controller = [MRKPushManager presentingVC];
    NSLog(@"applicationOpenNotification controller====== %@", controller);
    if (!controller) {
        return;
    }
    
    PushNotificationModel *model = [PushNotificationModel modelWithDictionary:info];
    switch (model.notificationTypeId) {
        case 1:{
            if (controller.navigationController.childViewControllers.count > 1) {
                [controller.navigationController popToRootViewControllerAnimated:YES];
            } else {
                [controller dismissViewControllerAnimated:YES completion:nil];
            }
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [[NSNotificationCenter defaultCenter] postNotificationName:@"SetTabBarItemIndex" object:@(model.mainTabPosition)];
            });
        }break;
        case 2:{//课程详情

            [[RouteManager sharedInstance] jumpToCourseDetailWithId: model.courseId];

        }break;
        case 3:{//训练计划详情
            
            MRKCoursePlanController *vc = [[MRKCoursePlanController alloc]init];
            vc.model = ({
                MRKCoursePlanModel *mod = [MRKCoursePlanModel new];
                mod.cid = model.planId;
                mod;
            });
            [controller.navigationController pushViewController:vc animated:YES];
        }break;
        case 4:{//活动网页
            
            MRKActivityWebController *vc = [[MRKActivityWebController alloc] init];
            vc.activityId = model.activityId;
            [controller.navigationController pushViewController:vc animated:YES];
        }break;
        case 5:{
            
        }break;
        case 6:{
            
        }break;
        case 7:{//新年活动网页
            
            [self skipToActivity:model.activityId source:@(NotificationPage)];
        }break;
        case 8:{//跳转到指定h5
            WebViewViewController *vc = [[WebViewViewController alloc] init];
            vc.htmlURL = model.httpInfo;
            vc.isHiddenNav = YES;
            [controller.navigationController pushViewController:vc animated:YES];
        }break;
        case 9:{//跳会员页面
            [[RouteManager sharedInstance] skipVIP];
        }break;
        default:
            break;
    }
}

- (void)skipToAllCourse:(NSString *)productID{
    MRKAllCourseViewController *allCourse = [[MRKAllCourseViewController alloc] init];
    allCourse.equimentTypeID = productID;
    [self.rootController.navigationController pushViewController:allCourse animated:YES];
}

- (void)skipToActivity:(NSString *)activityId source:(NSNumber *)source{
    WebViewViewController *vc = [WebViewViewController new];
    vc.activityId = activityId;
    vc.isHiddenNav = YES;//2.7.2版本以后  默认隐藏导航条  22-05-25
    vc.activitySource = source;
    vc.htmlURL = MRKAppH5LinkCombine(MRKLinkNewWebActivity);
    [self.rootController.navigationController pushViewController:vc animated:YES];
}

/// 引导购买设备
- (void)guideBuyDevice {
    MRKHomeGuideBuyAlertView *alert = [[MRKHomeGuideBuyAlertView alloc] initWithAnimationStyle:MRKActionAlertViewTransitionStyleSlideFromBottom];
    alert.opaquess = 0.6;
    alert.buttonClickBlock = ^(int i) {
        if (i == 1) {
            // 购买设备 跳转到发现页
            UIViewController *controller = [MRKPushManager presentingVC];
            if (controller.navigationController.childViewControllers.count > 1) {
                [controller.navigationController popViewControllerAnimated:YES];
            } else {
                [controller dismissViewControllerAnimated:YES completion:nil];
            }
            
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [[NSNotificationCenter defaultCenter] postNotificationName:@"SetTabBarItemIndex" object:@(2)];
            });
        }else {
            // 绑定设备
            [MRKDeviceConnectAlertView gotoSearchControllerVC:@""];
        }
    };
    [[MRKPopupManager sharedInstance] showAlertView:alert level:MRKPopupViewLevelHeight callback:nil];
}

/// 跳转到超燃脂自由训练
- (void)jumpToUltraHomeVC:(NSString *)productID {
    MRKUltraTrainHomeVC *ultraVC = [[MRKUltraTrainHomeVC alloc] init];
    ultraVC.ultraTrainId = productID;
    [self.rootController.navigationController pushViewController:ultraVC animated:YES];
}

/// 更多实景视频 / 训练计划 页面
- (void)jumpMoreCategoryCourse:(MRKCategoryCourseType )type {
    MRKCategoryCourseController *courseVC  = [[MRKCategoryCourseController alloc] init];
    courseVC.type = type;
    [self.rootController.navigationController pushViewController:courseVC animated:YES];
}

/// 数据中心
- (void)jumpDataCenter {
    ///
    NSUInteger count = self.rootController.navigationController.childViewControllers.count;
    if (count != 0){
        [self.rootController.navigationController popToRootViewControllerAnimated:YES];
    }
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [[NSNotificationCenter defaultCenter] postNotificationName:@"SetTabBarItemIndex" object:@2];
    });
    
//    MRKMineDataViewController *vc = [[MRKMineDataViewController alloc]init];
//    vc.scrollToHealth = YES;
//    vc.hidesBottomBarWhenPushed = YES;
//    [self.rootController.navigationController pushViewController:vc animated:YES];
}

///  日程
- (void)jumpDailySchedule{
    ///
    NSUInteger count = self.rootController.navigationController.childViewControllers.count;
    if (count != 0){
        [self.rootController.navigationController popToRootViewControllerAnimated:YES];
    }
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [[NSNotificationCenter defaultCenter] postNotificationName:@"SetTabBarItemIndex" object:@1];
    });
}

/// 去设备详情页
/// - Parameter model: 设备模型
- (void)jumpToDeviceDetail:(MRKDeviceModel *)model {
    MRKDeviceDetailController *vc = [[MRKDeviceDetailController alloc] init];
    if (model) {
        vc.model = model;
    }
    [self.rootController.navigationController pushViewController:vc animated:YES];
}

/**
 去体脂秤首页页面
 判断体脂秤是四电级还是八电级
 四电级体脂秤跳原生的体脂秤首页
 八电级体脂秤跳flutter体脂秤首页
 */
- (void)jumpToWeightCenterVC:(MRKDeviceModel *)deviceModel {
    if (deviceModel.electrodeType.intValue == 2){ // 8电级
        // FIXME: —————— flutter 八电级体脂秤跳flutter体脂秤首页
        [FlutterManager scaleHomeToVC];
    } else {
        MRKWeightViewController *vc = [[MRKWeightViewController alloc] init];
        vc.hidesBottomBarWhenPushed = YES;
        [self.rootController.navigationController pushViewController:vc animated:YES];
    }
}

/* 去测量体重页面
   判断体脂秤是四电级还是八电级
   四电级体脂秤直接去称量页面  原生
   八电级体脂秤需要判断是否有体脂秤用户，没有需要创建体脂秤用户，有的时候需要判断是否走过八电级的新手引导，没有走新手引导，有的话直接跳到八级体脂秤称量页面 flutter
 */
- (void)jumpToWeightMeasureVC:(MRKDeviceModel *)deviceModel {
    [self jumpToWeightMeasure:deviceModel avatar:nil];
}

- (void)jumpToWeightMeasure:(MRKDeviceModel *)deviceModel avatar:(NSString * _Nullable)avaterUrl {
    if (deviceModel.electrodeType.intValue == 2){ // 8电级
        [FlutterManager sacelMeasureToVC];
    } else {
        WeightMeasureViewController *vc = [WeightMeasureViewController new];
        vc.deviceModel = deviceModel;
        if ([avaterUrl isNotBlank]) {
            vc.avatarUrl = avaterUrl;
        }
        [self.rootController.navigationController pushViewController:vc animated:YES];
    }
}

/// 去测量体重页面,没有设备模型，看是否传头像
- (void)jumpToWeightMeasureVCWith:(NSString * _Nullable)avaterUrl {
    @weakify(self);
    [self requestFatScaleInfo:^(MRKDeviceModel *model) {
        @strongify(self);
        [self jumpToWeightMeasure:model avatar:avaterUrl];
    } failure:^{ 
        
    }];
}

/// 新建成员成功，去体脂秤页面 （8电极-返回上一页 4电极-测量页面）
- (void)addHealthJumpToWeightVCWith:(NSString * _Nullable)avaterUrl {
    @weakify(self);
    [self requestFatScaleInfo:^(MRKDeviceModel *model) {
        @strongify(self);
        [self jumpToWeightVCMeasureOrBack:model avatar:avaterUrl];
    } failure:^{ }];
}
- (void)jumpToWeightVCMeasureOrBack:(MRKDeviceModel *)deviceModel avatar:(NSString * _Nullable)avaterUrl {
    if (deviceModel.electrodeType.intValue == 2){ // 8电级返回上一页
        NSMutableArray *tempArr = [[NSMutableArray alloc] initWithArray:self.rootController.navigationController.viewControllers];
        __block FlutterScaleController *vc;
        [tempArr enumerateObjectsUsingBlock:^(id  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
            UIViewController *controller = (UIViewController *)obj;
            if ([controller isKindOfClass:NSClassFromString([FlutterScaleController sc_className])]) {
                vc = (FlutterScaleController *)controller;
            }
        }];
        if (vc) {
            [vc homeBean];
        }
        [self.rootController.navigationController popViewControllerAnimated:YES];
    } else { //4电极 测量页
        WeightMeasureViewController *vc = [WeightMeasureViewController new];
        vc.deviceModel = deviceModel;
        if ([avaterUrl isNotBlank]) {
            vc.avatarUrl = avaterUrl;
        }
        [self.rootController.navigationController pushViewController:vc animated:YES];
    }
}

/// 请求绑定的体脂秤列表，只能绑定一台，有列表就返回绑定的体脂秤设备信息（successBlock），否则就没有体脂秤（failureBlock）
- (void)requestFatScaleInfo:(void(^)(MRKDeviceModel *model))successBlock failure:(void(^)(void))failureBlock {
    [self.rootController.view beginLoading];
    @weakify(self);
    [MRKDeviceURLRequest requestMyDevice:@{@"productId":@(FatScaleEquipment)} success:^(NSArray * data) {
        @strongify(self);
        [self.rootController.view endLoading];
        if (data.count > 0) {
            MRKDeviceModel *model = [data firstObject];
            successBlock(model);
        } else {
            failureBlock();
        }
    } fail:^(NSError * data) {
        @strongify(self);
        [self.rootController.view endLoading];
        failureBlock();
    }];
}


/// 去体脂秤报告h5页面
- (void)skipScaleSportWeb:(NSString *)healthReportId {
    [self skipScaleSportWeb:healthReportId completion:^{
        
    }];
}

- (void)skipScaleSportWeb:(NSString *)healthReportId completion: (void (^)(void))completion {
    WebViewViewController *vc = [WebViewViewController new];
    vc.isHiddenNav = YES;
    NSString *Url = [NSString stringWithFormat:@"%@?healthReportId=%@", MRKHealthReport,healthReportId];
    vc.htmlURL = MRKAppH5LinkCombine(Url);
    vc.hidesBottomBarWhenPushed = YES;
    [self.rootController.navigationController pushViewController:vc animated:YES completion:completion];
}

- (void)skipScaleHistoryVC {
    MRKHealthHistoryVC *vc = [[MRKHealthHistoryVC alloc] init];
    [self.rootController.navigationController pushViewController:vc animated:YES];
}

- (void)skipWeightHistoryVC {
    MRKHealthHistoryVC *vc = [[MRKHealthHistoryVC alloc] init];
    vc.fromDataPage = YES;
    vc.hidesBottomBarWhenPushed = YES;
    UIViewController *controller = self.rootController;
    [controller.navigationController pushViewController:vc animated:YES];
}

- (void)jumpScaleDetailVC {
    @weakify(self);
    [self requestFatScaleInfo:^(MRKDeviceModel *model) {
        @strongify(self);
        [self jumpToDeviceDetail:model];
    } failure:^{
        
        
    }];
}

/// 心率带/体脂秤去新手指南
/// - Parameter equipmentType: 设备大类
- (void)jumpToDeviceFilterVC:(NSString * _Nullable)equipmentType {
    
}

- (void)skipSportReportWeb:(NSString *)reportId fromTrainingView:(BOOL)fromTrainingView {
    ExerciseReportWebController *vc = [[ExerciseReportWebController alloc] init];
    vc.exerciseID = reportId;
    vc.fromTrainingView = fromTrainingView;
    vc.hidesBottomBarWhenPushed = YES;
    [self.rootController.navigationController pushViewController:vc animated:YES];
}

- (void)skipCoachDetail:(NSString *)coachId {
    MRKCoachDetailController * vc = [[MRKCoachDetailController alloc]init];
    vc.showCocoaId = coachId;
    vc.hidesBottomBarWhenPushed = YES;
    [self.rootController.navigationController pushViewController:vc animated:YES];
}

- (void)skipAIPlanTrainDone:(NSString *)josnStr{
    MRKAIPlanCheckModel *model = [MRKAIPlanCheckModel modelWithJSON:josnStr];
    [MRKAIPlanLogic.shared jumpToAIPlanDone:model];
}


///用户协议
- (void)userProtocol {
    WebViewViewController *vc = [WebViewViewController new];
    vc.htmlURL = MRKAppH5LinkCombine(MRKLinkUserProtocol);
    vc.titleString = @"用户协议";
    MRKNavigationController *nav = [[MRKNavigationController alloc] initWithRootViewController:vc];
    nav.modalPresentationStyle = UIModalPresentationFullScreen;
    [[UIViewController currentViewController] presentViewController:nav animated:YES completion:nil];
}

///会员协议
- (void)autoRenewProtocal {
    MRKBaseController *base = (MRKBaseController *)[UIViewController currentViewController];
    [[MRKTraceManager sharedInstance] manualUploadTraceType:2 pageTitle:base.navTitle pageId:base.tracePageId eventId:@"btn_profile_vip_services_vip" route:base.tracePageRoute duration:0 extendPara:@{}];
    
    WebViewViewController *vc = [WebViewViewController new];
    vc.htmlURL = MRKAppH5LinkCombine(MRKLinkAutoRenewalProtocol);
    vc.titleString = @"会员服务协议";
    MRKNavigationController *nav = [[MRKNavigationController alloc] initWithRootViewController:vc];
    nav.modalPresentationStyle = UIModalPresentationFullScreen;
    [[UIViewController currentViewController] presentViewController:nav animated:YES completion:nil];
}


@end



@implementation PushNotificationModel
@end



