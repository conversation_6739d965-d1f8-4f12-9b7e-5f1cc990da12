//
//  MRKNavigationBar.h
//  Student_IOS
//
//  Created by merit on 2021/7/9.
//

#import <UIKit/UIKit.h>



NS_ASSUME_NONNULL_BEGIN

@class MRKNavigationBar;
// 主要处理导航条
@protocol  MRKNavigationBarDataSource<NSObject>
@optional
/**
 头部标题
 */
- (NSMutableAttributedString*)mrkNavigationBarTitle:(MRKNavigationBar *)navigationBar;

/**
 背景图片
 */
- (UIImage *)mrkNavigationBarBackgroundImage:(MRKNavigationBar *)navigationBar;

/**
 背景色
 */
- (UIColor *)mrkNavigationBarBackgroundColor:(MRKNavigationBar *)navigationBar;

/**
 是否显示底部黑线
 */
- (BOOL)mrkNavigationBarIsHideBottomLine:(MRKNavigationBar *)navigationBar;

/**
 导航条的高度
 */
- (CGFloat)mrkNavigationBarHeight:(MRKNavigationBar *)navigationBar;

/**
 导航条的左边的 view
 */
- (UIView *)mrkNavigationBarLeftView:(MRKNavigationBar *)navigationBar;

/**
 导航条右边的 view
 */
- (UIView *)mrkNavigationBarRightView:(MRKNavigationBar *)navigationBar;

/**
 导航条中间的 View
 */
- (UIView *)mrkNavigationBarTitleView:(MRKNavigationBar *)navigationBar;

/**
 导航条左边的按钮
 */
- (UIImage *)mrkNavigationBarLeftButtonImage:(UIButton *)leftButton navigationBar:(MRKNavigationBar *)navigationBar;

/**
 导航条右边的按钮图片
 */
- (UIImage *)mrkNavigationBarRightButtonImage:(UIButton *)rightButton navigationBar:(MRKNavigationBar *)navigationBar;

/**
 导航条右边的按钮文字
 */
- (NSString *)mrkNavigationBarRightButtonTitle:(UIButton *)rightButton navigationBar:(MRKNavigationBar *)navigationBar;
@end


@protocol MRKNavigationBarDelegate <NSObject>
@optional
/**
 左边的按钮的点击
 */
- (void)leftButtonEvent:(UIButton *)sender navigationBar:(MRKNavigationBar *)navigationBar;

/**
 右边的按钮的点击
 */
- (void)rightButtonEvent:(UIButton *)sender navigationBar:(MRKNavigationBar *)navigationBar;
@end


@interface MRKNavigationBar : UIView
@property (weak, nonatomic) id<MRKNavigationBarDataSource> dataSource;
@property (weak, nonatomic) id<MRKNavigationBarDelegate> delegate;

@property (weak, nonatomic) UIView *bottomBlackLineView;
@property (weak, nonatomic) __kindof UIView *titleView;
@property (weak, nonatomic) __kindof UIView *leftView;
@property (weak, nonatomic) __kindof UIView *rightView;

@property (copy, nonatomic) NSMutableAttributedString *title;
@property (weak, nonatomic) UIImage *backgroundImage;
@end


NS_ASSUME_NONNULL_END
