//
//  MRKBaseController.m
//  Student_IOS
//
//  Created by merit on 2021/7/9.
//

#import "MRKBaseController.h"
#import "AppDelegate.h"
#import "MRKVideoController.h"
#import "MRKGameVideoController.h"
#import "MrkPlotVideoController.h"
#import "MRKHrCtrlSportsController.h"
#import "UIDevice+YYAdd.h"


@interface MRKBaseController ()

@end

@implementation MRKBaseController

/** 是否自动旋转
   /// 在16以下版本shouldAutorotate决定当前界面是否开启自动转屏，如果返回未NO，后面的两个方法也不会调用，
   /// 🚦在16及以上系统，这里的API有较大的变化，需要特别注意，shouldAutorotate方法在16及以上系统会不起作用，而是需要控制supportedInterfaceOrientations方法才有效果，系统中代码注释如下。
   /// 巨坑
   /// 🚦强制某一方向横屏只能再model模式下实现，push模式下不行，push进入到一个页面的时候，是不会触发任何旋转类的方法的，只有旋转手机才会调用。
     采用push进入横屏页面是为了统一 navagation , 旋转方式相对好看一点
     所以强制旋转任然保留 [self forceOrientationLandscape]
 */
- (BOOL)shouldAutorotate {
//    if ([UIDevice currentDevice].isPad){
        return YES;
//    }
//    return NO;
}

/// 当前控制器支持的屏幕方向
- (UIInterfaceOrientationMask)supportedInterfaceOrientations {
//    if ([UIDevice currentDevice].isPad){
        return UIInterfaceOrientationMaskAll;
//    }
//    return UIInterfaceOrientationMaskPortrait;
}

/// 优先的屏幕方向 - 只会在 presentViewController:animated:completion时被调用
- (UIInterfaceOrientation)preferredInterfaceOrientationForPresentation {
    return UIInterfaceOrientationPortrait;
}

- (void)viewWillTransitionToSize:(CGSize)size withTransitionCoordinator:(id<UIViewControllerTransitionCoordinator>)coordinator {
    [super viewWillTransitionToSize:size withTransitionCoordinator:coordinator];
    NSLog(@"viewWillTransitionToSize ======== %@", NSStringFromCGSize(size));
}


- (void)dealloc {
     NSLog(@"😊😊😊-----dealloc-----😊😊😊%@" , NSStringFromClass([self class]));
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

#pragma mark - 设置状态栏样式
- (UIStatusBarStyle)preferredStatusBarStyle{
    if (@available(iOS 13.0, *)) {
        if (_statusBarStyle == UIStatusBarStyleDefault) {
            return UIStatusBarStyleDarkContent;
        }
    }
    return _statusBarStyle;
}

/// 动态更新状态栏颜色
- (void)setStatusBarStyle:(UIStatusBarStyle)statusBarStyle{
    _statusBarStyle = statusBarStyle;
    [self setNeedsStatusBarAppearanceUpdate];
}

#pragma 处理界面的来向
- (BOOL)handleFromViewControllers {
    NSArray *viewControllers = self.navigationController.childViewControllers;
    if (viewControllers.count > 2) {
        UIViewController *vc = viewControllers[viewControllers.count -2];
        if ([vc isKindOfClass:[MRKVideoController class]]) {
            return YES;
        }
        if ([vc isKindOfClass:[MRKGameVideoController class]]) {
            return YES;
        }
        
        if ([vc isKindOfClass:[MrkPlotVideoController class]]) {
            return YES;
        }
        
        if ([vc isKindOfClass:[MRKHrCtrlSportsController class]]) {
            return YES;
        }
    }
    return NO;
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
    //手势
    self.fd_interactivePopDisabled = [self handleFromViewControllers];
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    
    if (self.fd_interactivePopDisabled) {
        self.fd_interactivePopDisabled = NO;
    }
}

- (void)viewDidAppear:(BOOL)animated{
    [super viewDidAppear:animated];
    MLog(@"viewDidAppear== %@", self.className);
    
    ///刷新StatusBar
    [self setNeedsStatusBarAppearanceUpdate];
}

- (void)viewDidDisappear:(BOOL)animated {
    [super viewDidDisappear:animated];
    MLog(@"viewDidDisappear== %@", self.className);
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = [UIColor colorWithHexString:@"#F8F8FA"];
    self.navTitleColor = [UIColor colorWithHexString:@"#363A44"];
    self.fd_prefersNavigationBarHidden = YES;
    self.fd_interactivePopMaxAllowedInitialDistanceToLeftEdge = 50;
    dispatch_async(dispatch_get_main_queue(), ^{
        self.statusBarStyle = [self navControllerStatusBarStyle:self];
    });
    
    [self.view addSubview:self.mrk_navgationBar];
    [self.mrk_navgationBar mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.view.mas_top);
        make.centerX.mas_equalTo(self.view.mas_centerX);
        make.size.mas_equalTo(CGSizeMake(RealScreenWidth, kNavBarHeight));
    }];
    
    [self.view addSubview:self.mrkContentView];
    [self.mrkContentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.view.mas_top);
        make.centerX.mas_equalTo(self.view.mas_centerX);
        make.width.mas_equalTo(RealScreenWidth);
        make.bottom.mas_equalTo(self.view.mas_bottom);
    }];
}

- (void)viewDidLayoutSubviews {
    [super viewDidLayoutSubviews];
    [self.view bringSubviewToFront:self.mrk_navgationBar];
}





- (void)tabBarItemClicked{
    NSLog(@"\ntabBarItemClicked : %@", NSStringFromClass([self class]));
}

- (UIView *)mrkContentView {
    if (!_mrkContentView) {
        _mrkContentView = [[UIView alloc] init];
    }
    return _mrkContentView;
}

- (NSMutableAttributedString *)changeTitle:(NSString *)curTitle{
    return nil;
}

- (MRKNavigationBar *)mrk_navgationBar{
    if (![self.parentViewController isKindOfClass:[UINavigationController class]]) {
        return nil;
    }
    if (![self viewControllerIsNeedNavBar:self]) {
        return nil;
    }
    // 父类控制器必须是导航控制器
    if(!_mrk_navgationBar) {
        MRKNavigationBar *navigationBar = [[MRKNavigationBar alloc] init];
        navigationBar.dataSource = self;
        navigationBar.delegate = self;
        _mrk_navgationBar = navigationBar;
    }
    return _mrk_navgationBar;
}

- (void)setTitle:(NSString *)title {
    NSAssert(![title isNotBlank], @"页面设置title 请使用navTitle, 谢谢 [二级页面赋值title, push可能会导致tabbar 色值闪烁]");
    self.mrk_navgationBar.title = [self changeNavTitle:title];
}

- (void)setNavTitle:(NSString *)navTitle {
    _navTitle = navTitle;
    self.mrk_navgationBar.title = [self changeNavTitle:navTitle];
}

- (void)setNavTitleColor:(UIColor *)navTitleColor {
    _navTitleColor = navTitleColor;
    if ([self.mrk_navgationBar.titleView isKindOfClass:[UILabel class]]) {
        UILabel *lab = (UILabel *)self.mrk_navgationBar.titleView;
        lab.textColor = navTitleColor;
    }
}

- (NSMutableAttributedString *)changeNavTitle:(NSString *)curTitle{
    NSMutableAttributedString *attributedStr = [[NSMutableAttributedString alloc] initWithString:curTitle ?: @""];
    attributedStr.color = self.navTitleColor;
    attributedStr.font = [UIFont systemFontOfSize:WKDHPX(16) weight:UIFontWeightMedium];
    return attributedStr;
}


#pragma mark - JNavUIBaseViewControllerDataSource
- (BOOL)viewControllerIsNeedNavBar:(MRKBaseController *)viewController {
    return NO;
}
- (BOOL)mrkNavigationBarIsHideBottomLine:(MRKNavigationBar *)navigationBar {
    return YES;
}
- (UIColor *)mrkNavigationBarBackgroundColor:(MRKNavigationBar *)navigationBar {
    return [UIColor whiteColor];
}
- (UIStatusBarStyle)navControllerStatusBarStyle:(MRKBaseController *)viewController {
    return self.statusBarStyle;
}
- (NSMutableAttributedString*)mrkNavigationBarTitle:(MRKNavigationBar *)navigationBar {
    return [self changeNavTitle:self.navTitle?:self.navigationItem.title];
}

#pragma mark - Delegate
- (UIImage *)mrkNavigationBarLeftButtonImage:(UIButton *)leftButton navigationBar:(MRKNavigationBar *)navigationBar {
    return [UIImage imageNamed:@"icon_back"];
}
- (void)leftButtonEvent:(UIButton *)sender navigationBar:(MRKNavigationBar *)navigationBar {
    [self backClick:sender];
}

- (void)backClick:(UIButton *)sender {
    sender.traceEventId = @"btn_all_return";
    if (self.navigationController) {
        if (self.navigationController.childViewControllers.count == 1) {
            [self dismissViewControllerAnimated:YES completion:nil];
        }else {
            [self.navigationController popViewControllerAnimated:YES];
        }
    }
}



///需要强制横屏的页面调用此方法, 强制刷新controller的旋转方法
///present跳转不用调用, push跳转进去的需要调用
- (void)forceOrientationLandscape {
    jxt_getSafeMainQueue(^{
        AppDelegate *appdelegate = (AppDelegate *)[UIApplication sharedApplication].delegate;
        [appdelegate application:[UIApplication sharedApplication] supportedInterfaceOrientationsForWindow:self.view.window];
        
        //横屏
        if (@available(iOS 16.0, *)) {
            [self.navigationController setNeedsUpdateOfSupportedInterfaceOrientations];
            NSArray *array = [[[UIApplication sharedApplication] connectedScenes] allObjects];
            UIWindowScene *ws = (UIWindowScene *)array[0];
            UIWindowSceneGeometryPreferencesIOS *geometryPreferences = [[UIWindowSceneGeometryPreferencesIOS alloc] init];
            geometryPreferences.interfaceOrientations = UIInterfaceOrientationMaskLandscapeRight;
            [ws requestGeometryUpdateWithPreferences:geometryPreferences errorHandler:^(NSError * _Nonnull error) {
                //业务代码
            }];
        } else {
            [[UIDevice currentDevice] setValue:@(UIInterfaceOrientationLandscapeRight) forKey:@"orientation"];
            [UIViewController attemptRotationToDeviceOrientation];
        }
    });
}

- (void)forceOrientationPortrait {
    jxt_getSafeMainQueue(^{
        AppDelegate *appdelegate = (AppDelegate *)[UIApplication sharedApplication].delegate;
        [appdelegate application:[UIApplication sharedApplication] supportedInterfaceOrientationsForWindow:self.view.window];
        if (@available(iOS 16.0, *)) {
            [self.navigationController setNeedsUpdateOfSupportedInterfaceOrientations];
            NSArray *array = [[[UIApplication sharedApplication] connectedScenes] allObjects];
            UIWindowScene *ws = (UIWindowScene *)array[0];
            UIWindowSceneGeometryPreferencesIOS *geometryPreferences = [[UIWindowSceneGeometryPreferencesIOS alloc] init];
            geometryPreferences.interfaceOrientations = UIInterfaceOrientationMaskPortrait;
            [ws requestGeometryUpdateWithPreferences:geometryPreferences errorHandler:^(NSError * _Nonnull error) {
                //业务代码
            }];
        } else {
            // [[UIDevice currentDevice] setValue:[NSNumber numberWithInteger:UIDeviceOrientationUnknown] forKey:@"orientation"];
            [[UIDevice currentDevice] setValue:@(UIDeviceOrientationPortrait) forKey:@"orientation"];
            [UIViewController attemptRotationToDeviceOrientation];
        }
    });
}

@end



/**
 
 ///强制横屏
 - (void)forceOrientationLandscape {
     jxt_getSafeMainQueue(^{
         AppDelegate *appdelegate = (AppDelegate *)[UIApplication sharedApplication].delegate;
         appdelegate.allowOrentitaionRotation = YES;
         [appdelegate application:[UIApplication sharedApplication] supportedInterfaceOrientationsForWindow:self.view.window];
         
         //横屏
         if (@available(iOS 16.0, *)) {
             [self.navigationController setNeedsUpdateOfSupportedInterfaceOrientations];
             NSArray *array = [[[UIApplication sharedApplication] connectedScenes] allObjects];
             UIWindowScene *ws = (UIWindowScene *)array[0];
             UIWindowSceneGeometryPreferencesIOS *geometryPreferences = [[UIWindowSceneGeometryPreferencesIOS alloc] init];
             geometryPreferences.interfaceOrientations = UIInterfaceOrientationMaskLandscapeRight;
             [ws requestGeometryUpdateWithPreferences:geometryPreferences errorHandler:^(NSError * _Nonnull error) {
                 //业务代码
             }];
         } else {
             //            [[UIDevice currentDevice] setValue:[NSNumber numberWithInteger:UIDeviceOrientationUnknown] forKey:@"orientation"];
             [[UIDevice currentDevice] setValue:@(UIInterfaceOrientationLandscapeRight) forKey:@"orientation"];
             [UIViewController attemptRotationToDeviceOrientation];
         }
     });
 }

 ///强制竖屏
 - (void)forceOrientationPortrait {
     
     ///用于横屏跳横屏, 不刷新竖屏
     if(self.notRefreshRotation){
         return;
     }
     
     AppDelegate *appdelegate = (AppDelegate *)[UIApplication sharedApplication].delegate;
     if (appdelegate.allowOrentitaionRotation)
     {
         jxt_getSafeMainQueue(^{
             appdelegate.allowOrentitaionRotation = NO;
             [appdelegate application:[UIApplication sharedApplication] supportedInterfaceOrientationsForWindow:self.view.window];
             if (@available(iOS 16.0, *)) {
                 [self.navigationController setNeedsUpdateOfSupportedInterfaceOrientations];
                 NSArray *array = [[[UIApplication sharedApplication] connectedScenes] allObjects];
                 UIWindowScene *ws = (UIWindowScene *)array[0];
                 UIWindowSceneGeometryPreferencesIOS *geometryPreferences = [[UIWindowSceneGeometryPreferencesIOS alloc] init];
                 geometryPreferences.interfaceOrientations = UIInterfaceOrientationMaskPortrait;
                 [ws requestGeometryUpdateWithPreferences:geometryPreferences errorHandler:^(NSError * _Nonnull error) {
                     //业务代码
                 }];
             } else {
                 //                [[UIDevice currentDevice] setValue:[NSNumber numberWithInteger:UIDeviceOrientationUnknown] forKey:@"orientation"];
                 [[UIDevice currentDevice] setValue:@(UIDeviceOrientationPortrait) forKey:@"orientation"];
                 [UIViewController attemptRotationToDeviceOrientation];
             }
         });
     }
 }
 */
