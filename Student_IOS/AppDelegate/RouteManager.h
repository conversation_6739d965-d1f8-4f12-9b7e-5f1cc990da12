//
//  RouteManager.h
//  Student_IOS
//
//  Created by MacPro on 2021/5/26.
//

#import <Foundation/Foundation.h>
#import "LiveModel.h"
#import "AdvertModel.h"


@class MRKCourseModel;

NS_ASSUME_NONNULL_BEGIN
@interface RouteManager : NSObject
/// 当前首页展示的设备
@property (nonatomic, strong) MRKDeviceModel *homeShowDevice;

+ (instancetype)sharedInstance;


/// Banner点击
- (void)advertModelSkip:(AdvertModel *)model;
/// Banner点击
- (void)skipAdvertModelWithJsonStr:(NSString *)jsonStr;

#pragma mark - 连接设备

/// app_app
- (void)openAppJumpAppUrl:(NSString *)content;

/// app_h5
- (void)openAppJumpH5Url:(NSString *)content;


- (void)skipVIP;

///跳转自由训练
- (void)skipFreedomTraining:(NSString *)productType;

- (void)skipThemeCoursePage:(NSDictionary *)data;

///跳转活动（url固定）
- (void)skipToActivity:(NSString *)activityId source:(NSNumber *)source;

///外部链接跳转app
- (void)applicationOpenURL:(NSURL *)url;

///推送跳转app
- (void)applicationOpenNotification:(NSDictionary *)info;

/// 跳转超燃脂自由训练
/// - Parameters:
///   - type: 设备类型
///   - skip: 跳转操作
- (void)skipUltraTrain:(NSString *)type skipAction:(void(^)(void))skip;

/// 跳转到扫描二维码接口
- (void)jumpToScanQrcodeVC:(NSString * _Nullable )scene;

///跳转到课程详情
- (void)jumpToCourseDetailWithModel:(MRKCourseModel *)model;
- (void)jumpToCourseDetailWithId:(NSString *)courseId;
- (void)jumpToPlanCourseDetailWithId:(NSString *)courseId;///计划课程训练时带入

///跳转到动作详情
- (void)jumpToMotionDetailWithId:(NSString *)motionId trainTarget:(NSString *)target;

/// 跳转有赞商城
- (void)skipGeneralWeb:(NSString *)url;

/// 跳转到网页
- (void)skipWeb:(NSString *)url hiddenNav:(BOOL)hidden;
/// 跳转到mia对话窗口
- (void)skipToMia:(NSString *)url text:(NSString *)text;

/// 跳转到七鱼页面
- (void)skipServiceWeb;

/// 引导购买设备
- (void)guideBuyDevice;

/// 跳转到超燃脂自由训练
- (void)jumpToUltraHomeVC:(NSString *)productID;

/// 去设备详情页
/// - Parameter model: 设备模型
- (void)jumpToDeviceDetail:(MRKDeviceModel *)model;

/// 去体脂秤首页页面
/// - Parameter deviceModel: 设备模型
- (void)jumpToWeightCenterVC:(MRKDeviceModel *)deviceModel;

/// 去测量体重页面
/// - Parameter deviceModel: 设备模型
- (void)jumpToWeightMeasureVC:(MRKDeviceModel *)deviceModel;

/// 去测量体重页面,没有设备模型，看是否传头像
- (void)jumpToWeightMeasureVCWith:(NSString * _Nullable)avaterUrl;
/// 新建成员成功，去体脂秤页面 （8电极-首页 4电极-测量页面）
- (void)addHealthJumpToWeightVCWith:(NSString * _Nullable)avaterUrl;
    
/// 去体脂秤报告h5页面
- (void)skipScaleSportWeb:(NSString *)healthReportId;
- (void)skipScaleSportWeb:(NSString *)healthReportId completion: (void (^)(void))completion;

/// 去体脂秤设备详情页 （不传设备信息）
- (void)jumpScaleDetailVC;

/// 去体脂秤历史记录
- (void)skipScaleHistoryVC;
/// 去体重历史记录
- (void)skipWeightHistoryVC;

/// 请求绑定的体脂秤列表，只能绑定一台，有列表就返回绑定的体脂秤设备信息（successBlock），否则就没有体脂秤（failureBlock）
- (void)requestFatScaleInfo:(void(^)(MRKDeviceModel *model))successBlock failure:(void(^)(void))failureBlock;

/// 跳转训练报告
- (void)skipSportReportWeb:(NSString *)reportId fromTrainingView:(BOOL)fromTrainingView;

/// 去教练详情
- (void)skipCoachDetail:(NSString *)coachId;

/// 跳到aiplan完成页面
- (void)skipAIPlanTrainDone:(NSString *)josnStr;


///用户协议
- (void)userProtocol;

///会员续费你协议
- (void)autoRenewProtocal ;
    
@end



///后端app推送结构
@interface PushNotificationModel : NSObject
@property (nonatomic, assign) NSInteger notificationTypeId;
@property (nonatomic, assign) NSInteger mainTabPosition;
@property (nonatomic, copy) NSString *courseId;
@property (nonatomic, copy) NSString *planId;
@property (nonatomic, copy) NSString *activityId;
@property (nonatomic, copy) NSString *httpInfo;
@end
NS_ASSUME_NONNULL_END
