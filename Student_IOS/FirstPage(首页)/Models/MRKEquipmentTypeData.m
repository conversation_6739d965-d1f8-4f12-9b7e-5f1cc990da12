//
//  MRKEquipmentTypeData.m
//  Student_IOS
//
//  Created by MacPro on 2021/7/6.
//

#import "MRKEquipmentTypeData.h"

@implementation MRKEquipmentTypeData

+ (NSMutableArray *)dataArrayFromType:(NSString *)toolType model:(NSInteger)model{
    NSMutableArray *title  = [NSMutableArray array];
    switch (toolType.intValue) {
        case BicycleEquipment:
        case EllipticalEquipment:
        case TreadmillEquipment:
        case BoatEquipment:
        case StairClimbEquipment:
        {
            if (model == CutdownNumberMode) {
                title = @[
                    @"1.00",
                    @"2.00",
                    @"3.00",
                    @"5.00",
                    @"8.00",
                    @"10.00",
                    @"15.00",
                    @"21.0975",
                    @"42.195"
                ].mutableCopy;
                NSMutableArray *arr = [NSMutableArray array];
                for (int i = 0; i < title.count; i++) {
                    TrainTargetModel *m = [TrainTargetModel new];
                    m.number = title[i];
                    m.userDefine = NO;
                    m.unit = @"";
                    if (toolType.intValue == TreadmillEquipment) {
                        m.unit =  i == (title.count - 1) ? @"全马" : (i == title.count - 2 ) ? @"半马" :  @"";
                    }
                    m.type = @(model);
                    m.showText = [NSString stringWithFormat:@"%@", m.number];
                    [arr addObject:m];
                }
                
                title = arr;
                
            } else if (model == CutdownTimeMode){
                title = @[
                    [@(10 * 60) stringValue],
                    [@(15 * 60) stringValue],
                    [@(20 * 60) stringValue],
                    [@(30 * 60) stringValue],
                    [@(45 * 60) stringValue],
                    [@(1 * 60 * 60) stringValue],
                    [@(30 * 60 + 1 * 3600) stringValue],
                    [@(12 * 60 * 60) stringValue]
                ].mutableCopy;
                NSMutableArray *arr = [NSMutableArray array];
                for (int i = 0; i < title.count; i++) {
                    TrainTargetModel *m = [TrainTargetModel new];
                    m.number = title[i];
                    m.userDefine = NO;
                    m.unit = @"";
                    m.type = @(model);
                    m.showText = [MRKToolKit HMSTimeStrFromSecond:m.number.intValue];
                    [arr addObject:m];
                }
                
                title = arr;
            }
        }  break;
            
        case SkipRopeEquipment:
        case HoopEquipment:
        case FLSBEquipment:
        {
            if (model == CutdownNumberMode) {
                title = @[
                    @"100",
                    @"150",
                    @"200",
                    @"300",
                    @"400",
                    @"500",
                    @"750",
                    @"1000"
                ].mutableCopy;
                NSMutableArray *arr = [NSMutableArray array];
                for (int i = 0; i < title.count; i++) {
                    TrainTargetModel *m = [TrainTargetModel new];
                    m.number = title[i];
                    m.userDefine = NO;
                    m.unit = @"";
                    m.showText = m.number;
                    m.type = @(model);
                    [arr addObject:m];
                }
                
                title = arr;
                
            } else if (model == CutdownTimeMode){
                title = @[
                    [@(10 * 60) stringValue],
                    [@(15 * 60) stringValue],
                    [@(20 * 60) stringValue],
                    [@(30 * 60) stringValue],
                    [@(45 * 60) stringValue],
                    [@(1 * 60 * 60) stringValue],
                    [@(30 * 60 + 1 * 3600) stringValue],
                    [@(12 * 60 * 60) stringValue]
                ].mutableCopy;
                NSMutableArray *arr = [NSMutableArray array];
                for (int i = 0; i < title.count; i++) {
                    TrainTargetModel *m = [TrainTargetModel new];
                    m.number = title[i];
                    m.userDefine = NO;
                    m.unit = @"";
                    m.type = @(model);
                    m.showText = [MRKToolKit HMSTimeStrFromSecond:m.number.intValue];
                    [arr addObject:m];
                }
                
                title = arr;
            } else {
                
            }
        }  break;
            
        default:
            break;
    }
    
//    NSDictionary *dic = [[NSUserDefaults standardUserDefaults] objectForKey:toolType];
//    TrainTargetModel *local = [TrainTargetModel modelWithJSON:[dic objectForKey:[@(model) stringValue]]] ;
//    if (local.type.intValue == model) {
//        [title insertObject:local atIndex:0];
//    }
    
    return title;
}

+ (NSString *)exceriseTitleFromType:(NSString *)toolType model:(NSInteger)model  {
    NSString *title;
    if (model == FreedomMode) {
        title = [NSString stringWithFormat:@"自由训练-%@" , [MRKEquipmentTypeData nameFromProductId:toolType]];
    } else if (model == CutdownNumberMode) {
        if (toolType.intValue == SkipRopeEquipment) {
            title = @"定数计时";
        } else if (toolType.intValue == TreadmillEquipment) {
            title = @"目标跑";
        }  else if (toolType.intValue == BicycleEquipment) {
            title = @"目标骑";
        } else{
            title = @"目标练";
        }
    }else if (model == CutdownTimeMode) {
        if (toolType.intValue == SkipRopeEquipment) {
            title = @"定时计数";
        } else if (toolType.intValue == TreadmillEquipment) {
            title = @"定时跑";
        }  else if (toolType.intValue == BicycleEquipment) {
            title = @"定时骑";
        }  else{
            title = @"定时练";
        }
    }
    return title;
}

+ (NSString *)titleFromType:(NSString *)toolType model:(NSInteger)model {
    NSString *title ;
    switch (toolType.intValue) {
        case BicycleEquipment:
        case EllipticalEquipment:
        case TreadmillEquipment:
        case BoatEquipment:
        case StairClimbEquipment:
        {
            if (model == CutdownNumberMode) {
                title = @"设定距离";
            } else if (model == CutdownTimeMode){
                title = @"设定时间";
            } else {
                title = @"自由训练";
            }
        } break;
            
        case SkipRopeEquipment:
        {
            if (model == CutdownNumberMode) {
                title = @"设定个数";
            } else if (model == CutdownTimeMode){
                title = @"设定时间";
            } else {
                title = @"自由训练";
            }
        } break;
            
        case HoopEquipment:
        {
            if (model == CutdownNumberMode) {
                title = @"设定圈数";
            } else if (model == CutdownTimeMode){
                title = @"设定时间";
            } else {
                title = @"自由训练";
            }
        } break;
            
        case FLSBEquipment:
        {
            if (model == CutdownNumberMode) {
                title = @"设定个数";
            } else if (model == CutdownTimeMode){
                title = @"设定时间";
            } else {
                title = @"自由训练";
            }
        } break;
            
        default:  break;
    }
    return title;
}





+ (NSString *)nameFromProductId:(NSString *)productId {
    NSString *name = @"";
    switch (productId.intValue) {
        case BicycleEquipment:
            name = @"动感单车";
            break;
        case TreadmillEquipment:
            name = @"跑步机";
            break;
        case BoatEquipment:
            name = @"划船机";
            break;
        case EllipticalEquipment:
            name = @"椭圆机";
            break;
        case StairClimbEquipment:
            name = @"爬楼机";
            break;
        case JinMoQiangEquipment:
            name = @"筋膜枪";
            break;
        case SkipRopeEquipment:
            name = @"跳绳";
            break;
        case HoopEquipment:
            name = @"呼啦圈";
            break;
        case SmallEquipment:
            name = @"小件";
            break;
        case FLSBEquipment:
            name = @"飞力士棒";
            break;
        case FatScaleEquipment:
            name = @"体脂秤";
            break;
        case PowerEquipment:
            name = @"力量站";
            break;
        case KettleBellEquipment:
            name = @"智能壶铃";
            break;
        default:  break;
    }
    return name;
}

+ (NSString *)targetImageFromType:(NSString *)type {
    NSString *imageName ;
    switch (type.intValue) {
        case BicycleEquipment:
            imageName = @"icon_live_spoken_car";
            break;
        case BoatEquipment:
            imageName = @"icon_live_spoken_boat";
            break;;
        case TreadmillEquipment:
            imageName = @"icon_run";
            break;
        case EllipticalEquipment:
            imageName = @"icon_elliptical_machine";
            break;
        case StairClimbEquipment:
            imageName = @"icon_stairclimb_machine";
            break;
        case SkipRopeEquipment:
            imageName = @"icon_rope_skipping";
            break;
        case HoopEquipment:
            imageName = @"icon_hula_Hoop";
            break;
        case FLSBEquipment:
            imageName = @"icon_flix_stick";
            break;
        default: break;
    }
    return imageName;
}

// 1 白色  2 黑色
+ (NSString *)targetImageFromIconType:(NSString *)type color:(int)index {
    NSString *imageName ;
    switch (type.intValue) {
        case BicycleEquipment:
            imageName = [NSString stringWithFormat:@"icon_type_spoken_car_%d", index];
            break;
        case BoatEquipment:
            imageName = [NSString stringWithFormat:@"icon_type_live_spoken_%d", index];
            break;;
        case TreadmillEquipment:
            imageName = [NSString stringWithFormat:@"icon_type_run_%d", index];
            break;
        case EllipticalEquipment:
            imageName = [NSString stringWithFormat:@"icon_type_elliptical_%d", index];
            break;
        case StairClimbEquipment:
            imageName = [NSString stringWithFormat:@"icon_type_stairclimb_%d", index];
            break;
        case SkipRopeEquipment:
            imageName = [NSString stringWithFormat:@"icon_type_skipping_%d", index];
            break;
        case HoopEquipment:
            imageName = [NSString stringWithFormat:@"icon_type_hulaquan_%d", index];
            break;
        case FLSBEquipment:
            imageName = [NSString stringWithFormat:@"icon_type_feilishibang_%d", index];
            break;
        case PowerEquipment:
            imageName = [NSString stringWithFormat:@"icon_type_power_%d", index];
            break;
        case KettleBellEquipment:
            imageName = [NSString stringWithFormat:@"Icon_type_kettlebell_%d", index];
            break;
        default:
            imageName = [NSString stringWithFormat:@"icon_type_tushou_%d", index];
            break;
    }
    return imageName;
}

+ (NSString *)deviceImageFromIconFont:(NSString *)productId{
    NSString *imageName = @"";
    switch (productId.intValue) {
        case BicycleEquipment:
            imageName = @"\u{000e639}";
            break;
        case BoatEquipment:
            imageName = @"\u{000e638}";
            break;;
        case TreadmillEquipment:
            imageName = @"\u{000e635}";
            break;
        case EllipticalEquipment:
            imageName = @"\u{000e63a}";
            break;
        case StairClimbEquipment:
            imageName = @"\u{000e69c}";
            break;
        case SkipRopeEquipment:
            imageName = @"\u{000e63c}";
            break;
        case HoopEquipment:
            imageName = @"\u{000e63b}";
            break;
        case FLSBEquipment:
            imageName = @"\u{000e636}";
            break;
        case PowerEquipment:
            imageName = @"\u{000e68b}";
            break;
        case KettleBellEquipment:
            imageName = @"\u{000e68e}";
            break;
        default:
            imageName = @"\u{000e637}";
            break;
    }
    return imageName;
}


@end

@implementation TrainTargetModel

@end
