//
//  MRKCourseDetailBottomVue.m
//  Student_IOS
//
//  Created by merit on 2024/3/7.
//

#import "MRKCourseDetailBottomVue.h"
#import "UIView+AZGradient.h"
#import "MRKVipDiscountVue.h"




@interface MRKCourseDetailBottomVue()

/// 开始上课按钮
@property (nonatomic, strong) UIButton *startBtn;
/// 开通vip
@property (nonatomic, strong) UIButton *openBtn;
@property (nonatomic, strong) UIButton *averageDailyVue;
@property (nonatomic, strong) MRKVipDiscountVue *discountVue;
@property (nonatomic, strong) MRKMainCourseDetailModel *model;

@end

@implementation MRKCourseDetailBottomVue

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if ( !self ) return nil;
    [self _setupView];
    return self;
}

- (void)_setupView{
    self.startBtn.cornerRadius = WKDHPX(48)/2;
    [self addSubview:self.startBtn];
    [self.startBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self).offset(WKDHPX(16));
        make.right.equalTo(self).offset(-WKDHPX(16));
        make.top.equalTo(self).offset(WKDHPX(18));
        make.height.mas_equalTo(WKDHPX(48));
    }];
    
    self.openBtn.cornerRadius = WKDHPX(48)/2;
    [self addSubview:self.openBtn];
    [self.openBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self).offset(-WKDHPX(16));
        make.top.equalTo(self).offset(WKDHPX(18));
        make.height.mas_equalTo(WKDHPX(48));
        make.width.mas_equalTo(WKDHPX(216));
    }];
    self.openBtn.hidden = YES;
    
    [self addSubview:self.averageDailyVue];
    [self.averageDailyVue mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self).offset(-WKDHPX(15));
        make.top.equalTo(self).offset(WKDHPX(9));
        make.height.mas_equalTo(WKDHPX(18));
    }];
    self.averageDailyVue.hidden = YES;
    
    [self addSubview:self.discountVue];
    [self.discountVue mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self).offset(WKDHPX(16));
        make.right.equalTo(self).offset(-WKDHPX(258));
        make.top.equalTo(self).offset(WKDHPX(16));
        make.height.mas_equalTo(WKDHPX(52));
    }];
    self.discountVue.hidden = YES;
}

- (void)layoutSubviews {
    [super layoutSubviews];
    [self.averageDailyVue partCornerRadius:8 byRoundingCorners:UIRectCornerTopLeft | UIRectCornerBottomRight];
}

- (void)setVipModel:(MRKVipCheapestModel *)vipModel {
    [self.openBtn setTitle:[NSString stringWithFormat:@"¥%@开通会员畅练%@卡", vipModel.discountPrice, vipModel.skuName] forState:UIControlStateNormal];
    self.discountVue.vipModel = vipModel;
    [self.averageDailyVue setTitle:[NSString stringWithFormat:@" 低至¥%@起/天 ", vipModel.dailyStartPrice] forState:UIControlStateNormal];
}

- (void)updateBottomVue:(MRKMainCourseDetailModel *)model {
    self.model = model;
    self.startBtn.userInteractionEnabled = YES;
    [self.startBtn mas_updateConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self).offset(WKDHPX(16));
        make.right.equalTo(self).offset(-WKDHPX(16));
    }];
    self.startBtn.borderWidth = 1;
    self.startBtn.borderColor = [UIColor clearColor];
    self.openBtn.hidden = YES;
    self.averageDailyVue.hidden = YES;
    self.discountVue.hidden = YES;
    
    switch (model.videoType) {
        case MrkVideoTypeLivePrep: { // 直播准备中
            ReportMrkLogParms(1, @"课程详情（直播）", @"page_Coursedetails_living", @"", nil, 0, @{@"course_id": self.model.courseId});
            NSString *btnTitle = self.model.isMake ? @"已预约" : @"立即预约";
            UIColor *btnColor = self.model.isMake ? UIColorHex(D4D6D8) : UIColorHex(16D2E3);
            
            [self.startBtn setTitle:btnTitle forState: UIControlStateNormal];
            self.startBtn.backgroundColor = btnColor;
            self.startBtn.traceEventId = @"btn_Coursedetails_order_class";
            self.startBtn.tracePara = @{ @"course_id": self.model.courseId };
        }  break;
        case MrkVideoTypeLive: {
            ReportMrkLogParms(1, @"课程详情（直播）", @"page_Coursedetails_living", @"", nil, 0, @{@"course_id": self.model.courseId});
            NSString *btnTitle = self.model.type.integerValue == 3 ? @"比赛中" : @"直播中";
            UIColor *btnColor = self.model.type.integerValue == 3 ? UIColorHex(16D2E3) : UIColorHex(FE2550);
            self.startBtn.traceEventId = @"btn_Coursedetails_living";
            if ( !self.model.canPlayCourse && UserInfo.isNormalMember ) {
                self.startBtn.traceEventId = @"btn_living_vip_open";
                btnTitle = self.model.type.integerValue == 3 ? @"比赛中，成为会员后训练" : @"直播中，成为会员后训练";
                [self.startBtn mas_updateConstraints:^(MASConstraintMaker *make) {
                    make.left.equalTo(self).offset(WKDHPX(133));
                }];
                self.discountVue.hidden = NO;
            }
            [self.startBtn setTitle:btnTitle forState: UIControlStateNormal];
            self.startBtn.backgroundColor = btnColor;
            self.startBtn.tracePara = @{ @"course_id": self.model.courseId };
        }  break;
        default: {
            ReportMrkLogParms(1, @"课程详情（录播）", @"page_Coursedetails_recorded", @"", nil, 0, @{@"course_id": self.model.courseId});
            NSString *btnTitle = @"开始上课";
            if (self.model.isVipCourse && self.model.videoType == MrkVideoTypeVOD) {
                if (!UserInfo.isMember || UserInfo.vipType < self.model.vipType.intValue){
                    btnTitle = @"试看课程";
                    if (UserInfo.isNormalMember){
                        [self.startBtn mas_updateConstraints:^(MASConstraintMaker *make) {
                            make.right.equalTo(self).offset(-WKDHPX(244));
                        }];
                        self.openBtn.hidden = NO;
                        self.averageDailyVue.hidden = NO;
                    }
                }
            }
            [self.startBtn setTitle:btnTitle forState:UIControlStateNormal];
            if ([btnTitle isEqualToString:@"开始上课"]) {
                self.startBtn.backgroundColor = UIColorHex(16D2E3);
                [self.startBtn setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
                self.startBtn.traceEventId = @"btn_Coursedetails_attend_class";
                self.startBtn.tracePara = @{ @"course_id": self.model.courseId };
            } else {
                if (UserInfo.isNormalMember){
                    [self.startBtn setTitleColor:UIColorHex(16D2E3) forState:UIControlStateNormal];
                    self.startBtn.borderColor = UIColorHex(16D2E3);
                    self.startBtn.backgroundColor = [UIColor whiteColor];
                } else {
                    [self.startBtn setTitleColor:UIColorHex(#672F15) forState:UIControlStateNormal];
                    [self.startBtn az_setGradientBackgroundWithColors:@[[UIColor colorWithHexString:@"#FFECDF"],
                                                                        [UIColor colorWithHexString:@"#F0BA8A"]]
                                                            locations:@[@0,@0.5]
                                                           startPoint:CGPointMake(0, 0.5)
                                                             endPoint:CGPointMake(1, 0.5)];
                }
                self.startBtn.traceEventId = @"btn_Coursedetails_trial";
                self.startBtn.tracePara = @{ @"course_id": self.model.courseId };
            }
        }  break;
    }
}

/// 开始上课
- (void)startAction:(UIButton *)sender{
    if (self.buttonBlock) {
        self.buttonBlock();
    }
}

- (void)vipAction:(UIButton *)sender{
    sender.traceEventId = @"btn_recorded_vip_open";
    sender.tracePara = @{ @"course_id": self.model.courseId};
    [DYFStoreManager shared].appPurchaseSource = TraceVipOpenSourceTypeCourse;
//    [[RouteManager sharedInstance] skipVIP];
    
    UIViewController *controller = UIViewController.currentViewController;
    MRKVIpPurchaseSubscribeAlert *alert = [[MRKVIpPurchaseSubscribeAlert alloc] initWithAnimationStyle:MRKAlertViewTransitionStyleSlideFromBottom];
    alert.backgroundStyle = MRKAlertViewBackgroundStyleGradientHeavy;
    alert.isAutoHidden = true;
    alert.alertType = PurchaseSubscribeAlertTypeCourse;
    [alert showEmbeddedIn:controller.view];
}

- (UIButton *)startBtn {
    if (!_startBtn) {
        _startBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        _startBtn.backgroundColor = UIColorHex(16D2E3);
        _startBtn.titleLabel.font = kMedium_Font_NoDHPX(WKDHPX(17));
        [_startBtn setTitle:@"开始上课" forState: UIControlStateNormal];
        [_startBtn setTitleColor:[UIColor whiteColor] forState: UIControlStateNormal];
        [_startBtn addTarget:self action:@selector(startAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _startBtn;
}

- (UIButton *)openBtn {
    if (!_openBtn) {
        _openBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        _openBtn.backgroundColor = UIColorHex(16D2E3);
        _openBtn.titleLabel.font = kMedium_Font_NoDHPX(WKDHPX(17));
        [_openBtn setTitleColor:UIColorHex(#672F15) forState:UIControlStateNormal];
        [_openBtn az_setGradientBackgroundWithColors:@[[UIColor colorWithHexString:@"#FFECDF"],
                                                            [UIColor colorWithHexString:@"#F0BA8A"]]
                                                locations:@[@0,@0.5]
                                               startPoint:CGPointMake(0, 0.5)
                                                 endPoint:CGPointMake(1, 0.5)];
        [_openBtn addTarget:self action:@selector(vipAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _openBtn;
}

- (UIButton *)averageDailyVue {
    if (!_averageDailyVue) {
        _averageDailyVue = [UIButton buttonWithType:UIButtonTypeCustom];
        _averageDailyVue.userInteractionEnabled = NO;
        _averageDailyVue.titleLabel.font = kSystem_Font_NoDHPX(WKDHPX(11));
        [_averageDailyVue setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        [_averageDailyVue az_setGradientBackgroundWithColors:@[[UIColor colorWithHexString:@"#EF3473"],[UIColor colorWithHexString:@"#FE2550"]]
                                                locations:@[@0,@0.5]
                                               startPoint:CGPointMake(0, 0.5)
                                                 endPoint:CGPointMake(1, 0.5)];
    }
    return _averageDailyVue;
}

- (MRKVipDiscountVue *)discountVue {
    if (!_discountVue) {
        _discountVue = [MRKVipDiscountVue new];
    }
    return _discountVue;
}

@end
