//
//  MRKCourseDetailHeaderCell.m
//  Student_IOS
//
//  Created by merit on 2023/3/23.
//

#import "MRKCourseDetailHeaderCell.h"
#import "MRKMainCourseDetailModel.h"
#import "WKHeadBubbleView.h"
#import "MrkTagView.h"

@interface MRKCourseDetailHeaderCell()
@property (nonatomic, strong) MrkTagView *tagsView;
@property (nonatomic, strong) MRKCourseDetailVipTipView *vipTipView;
/// 标题
@property (nonatomic, strong) UILabel *titleLabel;
/// 直播开始时间
@property (nonatomic, strong) UIView *timeVue;
/// 直播开始时间
@property (nonatomic, strong) UILabel *timeLabel;
/// 运动种类图片
@property (nonatomic, strong) UIImageView *sportImgv;
/// 运动种类-进阶-时间
@property (nonatomic, strong) UILabel *sportInfoLabel;
/// 练习人数
@property (nonatomic, strong) UILabel *practisePeopleLabel;
/// 练习过的头像动画效果
@property (nonatomic, strong) WKHeadBubbleView *bubbleView;
/// 标签——练过模块
@property (nonatomic, strong) UIView *practicedVue;
@end

@implementation MRKCourseDetailHeaderCell

// 顶部去掉12的高度-在topView里
- (void)createUI {

    [self.contentView addSubview:self.tagsView];
    [self.tagsView mas_makeConstraints: ^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.contentView.mas_top).offset(WKDHPX(16)-12);
        make.left.mas_equalTo(self.contentView.mas_left);
        make.right.mas_equalTo(self.contentView.mas_right);
    }];
    
    [self.contentView addSubview:self.vipTipView];
    [self.vipTipView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView).offset(WKDHPX(16));
        make.right.equalTo(self.contentView).offset(-WKDHPX(16));
        make.height.mas_equalTo(WKDHPX(30));
    }];
    
    [self.contentView addSubview:self.titleLabel];
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView).offset(WKDHPX(16));
        make.right.equalTo(self.contentView).offset(-WKDHPX(16));
    }];
    [self.contentView addSubview:self.timeVue];
    [self.timeVue mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.titleLabel.mas_bottom).offset(WKDHPX(10));
        make.left.right.equalTo(self.titleLabel);
    }];
    [self.timeVue addSubview:self.timeLabel];
    [self.timeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.timeVue);
        make.left.mas_equalTo(WKDHPX(22));
    }];
    [self.contentView addSubview:self.sportImgv];
    [self.sportImgv mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.titleLabel.mas_bottom).offset(WKDHPX(10)); // 10\33
        make.left.equalTo(self.titleLabel);
        make.width.height.mas_equalTo(WKDHPX(18));
        make.bottom.mas_equalTo(-WKDHPX(8));
    }];
    [self.contentView addSubview:self.sportInfoLabel];
    [self.sportInfoLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.sportImgv);
        make.left.equalTo(self.sportImgv.mas_right).offset(WKDHPX(4));
    }];
    [self.contentView addSubview:self.practicedVue];
    [self.practicedVue mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.titleLabel);
        make.top.equalTo(self.sportImgv.mas_bottom).offset(WKDHPX(17));
        make.height.mas_equalTo(WKDHPX(22));
        make.right.equalTo(self.contentView).offset(-WKDHPX(175));
    }];
    
    [self.contentView addSubview:self.practisePeopleLabel];
    [self.practisePeopleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.sportImgv);
        make.right.equalTo(self.titleLabel);
    }];
    [self.contentView addSubview:self.bubbleView];
    [self.bubbleView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.practisePeopleLabel.mas_left);
        make.centerY.equalTo(self.practisePeopleLabel);
        make.size.mas_equalTo(CGSizeMake(WKDHPX(64), WKDHPX(20)));
    }];
}

#pragma mark - 赋值操作

- (void)configWithItem:(MRKMainCourseDetailModel *)item {
    if (item == nil) return;
    
    // 顶部区域处理——tag
    [self.tagsView removeAllTags];
    
    //Add free Tags
    if (item.isFree) {
        MrkTag *tag = [MrkTag tagWithText: @"限时免费"];
        tag.textColor = [UIColor whiteColor];
        tag.font = kMedium_Font_NoDHPX(WKDHPX(11));
        tag.enable = NO;
        tag.padding = UIEdgeInsetsMake(3, 4, 3, 4);
        tag.bgColor = [UIColor colorWithHexString:@"#F54C4C"];
        tag.cornerRadius = WKDHPX(4);
        [self.tagsView addTag:tag];
    }
    
    ///Add VIP Tags vip课程并且不是限时免费
    if (!item.isFree) {
        NSString *tagStr = @"";
        UIColor *tagTextColor = nil;
        UIImage *tagBackImage = nil;
        switch (item.vipType.intValue) {
            case 10:
                tagStr = @"VIP专享";
                tagTextColor = UIColorHex(#644821);
                tagBackImage = [UIImage gradientColorImageFromColors:@[UIColorHex(#FFECDF), UIColorHex(#F0BA8A)]
                                                        gradientType:GradientTypeLeftToRight
                                                             imgSize:CGSizeMake(50, 18)];
                break;
            case 30:
                tagStr = @"绝影尊享";
                tagTextColor = UIColorHex(#FCECE1);
                tagBackImage = [UIImage gradientColorImageFromColors:@[UIColorHex(#0F397E), UIColorHex(#081532)]
                                                        gradientType:GradientTypeLeftToRight
                                                             imgSize:CGSizeMake(50, 18)];
                break;
            default:
                break;
        }
        
        if ([tagStr isNotBlank]) {
            MrkTag *tag = [MrkTag tagWithText: tagStr];
            tag.textColor = tagTextColor;
            tag.font = kMedium_Font_NoDHPX(WKDHPX(11));
            tag.enable = NO;
            tag.padding = UIEdgeInsetsMake(3, 4, 3, 4);
            tag.bgImg = tagBackImage;
            tag.cornerRadius = WKDHPX(4);
            [self.tagsView addTag:tag];
        }
    }
    
    //Add Merit Tags
    if (item.isMeritCourse) {
        MrkTag *tag = [MrkTag tagWithText:@" 超燃脂"];
        tag.bgColor = [UIColor colorWithHexString:@"#FE2550"];
        tag.textColor = [UIColor whiteColor];
        tag.font = kMedium_Font_NoDHPX(WKDHPX(11));
        
        UIImage *image = [UIImage imageNamed:@"courseTag_fire"];
        tag.image = [image imageByResizeToSize:CGSizeMake(12, 12)];
        tag.enable = NO;
        tag.padding = UIEdgeInsetsMake(3, 4, 3, 4);
        tag.cornerRadius = WKDHPX(4);
        [self.tagsView addTag:tag];
    }
    
    //Add Tags
    [item.tags enumerateObjectsUsingBlock: ^(NSString *text, NSUInteger idx, BOOL *stop) {
        MrkTag *tag = [MrkTag tagWithText: text];
        tag.bgColor = UIColorHex(16D2E3);
        tag.textColor = [UIColor whiteColor];;
        tag.font = kMedium_Font_NoDHPX(WKDHPX(11)) ;
        tag.enable = NO;
        tag.padding = UIEdgeInsetsMake(3, 4, 3, 4);
        tag.cornerRadius = WKDHPX(4);
        [self.tagsView addTag:tag];
    }];
    
    //标记布局无效
    [self.tagsView setNeedsLayout];
    [self.tagsView layoutIfNeeded];
    
    
    BOOL flag = self.tagsView.subviews.count > 0;
    if (UserInfo.isMember && UserInfo.vipDays > 10){
        self.vipTipView.hidden = YES;
    }else{
        if ([self vipTipView:item]){
            self.vipTipView.hidden = NO;
            [self.vipTipView reload];
            [self.vipTipView mas_updateConstraints:^(MASConstraintMaker *make) {
                if (flag) {
                    make.top.mas_equalTo(self.tagsView.mas_bottom).offset(WKDHPX(8));
                }else {
                    make.top.mas_equalTo(self.contentView.mas_top).offset(WKDHPX(16)-12);
                }
            }];
        }else{
            self.vipTipView.hidden = YES;
        }
    }
    
    self.titleLabel.text = item.name;
    if (self.vipTipView.hidden == NO){
        [self.titleLabel mas_updateConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.vipTipView.mas_bottom).offset(WKDHPX(16));
        }];
    }else{
        [self.titleLabel mas_updateConstraints:^(MASConstraintMaker *make) {
            if (flag) {
                make.top.mas_equalTo(self.tagsView.mas_bottom).offset(WKDHPX(16));
            }else {
                make.top.mas_equalTo(self.contentView.mas_top).offset(WKDHPX(16)-12);
            }
        }];
    }
    
    
    self.sportImgv.image = [UIImage imageNamed:[MRKEquipmentTypeData targetImageFromIconType:item.equipmentId color: 2]];
    if (item.videoType == MrkVideoTypeLivePrep) { // 直播准备中
        self.sportInfoLabel.text = [NSString stringWithFormat:@"%@ | %@", item.equipmentName?:@"", item.gradeDesc?:@""];
        self.timeVue.hidden = NO;
        NSTimeInterval startTime = [MRKTimeManager stringDatesToTimeInterval:item.liveTime format:@"yyyy-MM-dd HH:mm"] * 1000; 
        NSTimeInterval endTime = startTime + (item.courseTime?:@"0").longLongValue * 60 * 1000;
        self.timeLabel.text = [NSString stringWithFormat:@"%@~%@", [MRKTimeManager getTimeInterval:startTime format:@"MM-dd HH:mm"], [MRKTimeManager getTimeInterval:endTime format:@"HH:mm"]];
        [self.sportImgv mas_updateConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.titleLabel.mas_bottom).offset(WKDHPX(27)); // 4\33
        }];
    } else {
        self.sportInfoLabel.text = [NSString stringWithFormat:@"%@ | %@ | %@分钟", item.equipmentName?:@"", item.gradeDesc?:@"", item.courseTime?:@""];
        self.timeVue.hidden = YES;
        [self.sportImgv mas_updateConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.titleLabel.mas_bottom).offset(WKDHPX(10)); // 10\33
        }];
    }
    
    NSMutableArray *practiceds = [NSMutableArray array];
    if (item.lastTrainTime.length > 0) {
        [practiceds appendObject:item.lastTrainTime];
    }
    if (item.trainCount.length > 0 && item.trainCount.intValue > 0) {
        [practiceds appendObject:[NSString stringWithFormat:@"练过%@次",item.trainCount]];
    }
    self.practicedVue.hidden = practiceds.count == 0;
    [self updateTagViewsItem:practiceds bgVue:self.practicedVue color:UIColorHexAlpha(000000, 0.4) font:kMedium_Font_NoDHPX(WKDHPX(11)) radiu:2.4 ];
    [self.sportImgv mas_updateConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_equalTo(practiceds.count > 0 ? -WKDHPX(47) : -WKDHPX(8));
    }];
    [self.practisePeopleLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo( practiceds.count > 0 ? self.practicedVue : self.sportImgv);
        make.right.equalTo(self.titleLabel);
    }];
    self.practisePeopleLabel.attributedText = ({
        NSString *numStr = [NSString stringWithFormat:@"%@", item.num?:@""];
        NSString *peopleStr = [NSString stringWithFormat:@"%@人练过", numStr]; // 录播
        if (item.videoType == MrkVideoTypeLivePrep) { // 直播准备中
            peopleStr = [NSString stringWithFormat:@"%@人已预约", numStr];
        } else if (item.videoType == MrkVideoTypeLive){
            peopleStr = [NSString stringWithFormat:@"%@人在线", numStr];
        }
        NSRange numRange = [peopleStr rangeOfString:numStr];
        NSMutableAttributedString *attrStr = [[NSMutableAttributedString alloc] initWithString:peopleStr];
        [attrStr attributedColor:UIColorHex(363A44) font:kSystem_Font_NoDHPX(WKDHPX(11))];
        [attrStr setFont: kSemibold_PingFangSC_NoDHPX(WKDHPX(16)) range: numRange];
        attrStr;
    });
    
    self.bubbleView.headImage = item.avatars;
}

- (void)updateTagViewsItem:(NSArray<NSString*>*)tags bgVue:(UIView *)bgVue color:(UIColor *)color font:(UIFont *)font radiu:(CGFloat)radiu {
    [bgVue removeAllSubviews];
    UIView *frontVue = nil;
    for (NSString * tag in tags) {
        UILabel *label = [UILabel new];
        label.text = [NSString stringWithFormat:@" %@ ", tag];
        label.backgroundColor = color;
        label.font = font;
        label.cornerRadius = radiu;
        label.textColor = [UIColor whiteColor];
        [bgVue addSubview:label];
        [label mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.bottom.height.equalTo(bgVue);
            make.left.equalTo(frontVue == nil ? bgVue.mas_left : frontVue.mas_right).offset(frontVue == nil ? 0 : WKDHPX(8));
        }];
        frontVue = label;
    }
}

- (BOOL)vipTipView:(MRKMainCourseDetailModel *)model{
    MemberInfoDataDTO *memberInfo = [Login curLoginUser].memberInfo;
    if (memberInfo.isAutoRenewal) {
        return NO;
    }
    BOOL canAlert = NO;
    if (!memberInfo.isPaid) {
        canAlert = YES;
    } else {
        if (memberInfo.days <= 9){
            canAlert = YES;
        }
    }
    return model.isVipCourse && canAlert;
}

#pragma mark - lazy init

- (UIView *)practicedVue {
    if (!_practicedVue) {
        _practicedVue = [[UIView alloc] init];
    }
    return _practicedVue;
}

- (MrkTagView *)tagsView {
    if (!_tagsView) {
        MrkTagView *view = [MrkTagView new];
        view.backgroundColor = [UIColor whiteColor];
        view.padding = UIEdgeInsetsMake(0, 16, 0, 16);
        view.interitemSpacing = 10;
        view.lineSpacing = 10;
        _tagsView = view;
    }
    return _tagsView;
}

- (MRKCourseDetailVipTipView *)vipTipView {
    if (!_vipTipView) {
        MRKCourseDetailVipTipView *view = [MRKCourseDetailVipTipView new];
        view.backgroundColor = UIColorHex(#FFF4EB);
        view.layer.cornerRadius = 2.0f;
        view.layer.masksToBounds = YES;
        [view addTarget:self action:@selector(skipVipPurchase:) forControlEvents:UIControlEventTouchUpInside];
        _vipTipView = view;
    }
    return _vipTipView;
}

- (void)skipVipPurchase:(UIControl *)sender{
    if (self.vipTipView.purchaseSource > 0){
        [DYFStoreManager shared].appPurchaseSource = self.vipTipView.purchaseSource;
    }
    [[RouteManager sharedInstance] skipVIP];
    
    NSInteger t = self.vipTipView.purchaseSource - 1;
    ReportMrkLogParms(2, @"提示语", @"page_Coursedetails", @"btn_LiveCourses_instruction_id_click", nil, 0, @{@"type":@(t)});
}

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [UILabel new];
        _titleLabel.font = kSemibold_PingFangSC_NoDHPX(WKDHPX(24));
        _titleLabel.textColor = UIColorHex(363A44);
        _titleLabel.numberOfLines = 0;
        _titleLabel.text = @"------";
    }
    return _titleLabel;
}

- (UIView *)timeVue {
    if (!_timeVue) {
        _timeVue = [[UIView alloc] init];
        UIImageView *timeImgv = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"icon_exercise_record_time"]];
        [_timeVue addSubview:timeImgv];
        [timeImgv mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.centerY.mas_equalTo(0);
            make.width.height.mas_equalTo(WKDHPX(18));
        }];
    }
    return _timeVue;
}

- (UILabel *)timeLabel {
    if (!_timeLabel) {
        _timeLabel = [UILabel new];
        _timeLabel.font = kMedium_Font_NoDHPX(WKDHPX(13));
        _timeLabel.textColor = UIColorHex(363A44);
    }
    return _timeLabel;
}

- (UIImageView *)sportImgv {
    if (!_sportImgv) {
        _sportImgv = [[UIImageView alloc] init];
        _sportImgv.clipsToBounds = YES;
    }
    return _sportImgv;
}

- (UILabel *)sportInfoLabel {
    if (!_sportInfoLabel) {
        _sportInfoLabel = [UILabel new];
        _sportInfoLabel.font = kMedium_Font_NoDHPX(WKDHPX(13));
        _sportInfoLabel.textColor = UIColorHex(363A44);
    }
    return _sportInfoLabel;
}

- (UILabel *)practisePeopleLabel {
    if (!_practisePeopleLabel) {
        _practisePeopleLabel = [UILabel new];
    }
    return _practisePeopleLabel;
}

- (WKHeadBubbleView *)bubbleView {
    if (!_bubbleView) {
        _bubbleView = [[WKHeadBubbleView alloc] init];
        _bubbleView.headSize = WKDHPX(20);
        _bubbleView.headNumber = 3;
    }
    return _bubbleView;
}

@end





@interface MRKCourseDetailVipTipView()
@property (nonatomic, strong) UIImageView *iconImageView;
@property (nonatomic, strong) UILabel *titleLab;
@property (nonatomic, strong) UILabel *vipChargeLab;
@end

@implementation MRKCourseDetailVipTipView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self addSubview:self.iconImageView];
        [self.iconImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(self.mas_centerY);
            make.left.equalTo(self.mas_left).offset(WKDHPX(4));
            make.width.height.mas_equalTo(WKDHPX(20));
        }];
        
        [self addSubview:self.titleLab];
        [self.titleLab mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(self.mas_centerY);
            make.left.mas_equalTo(self.iconImageView.mas_right).offset(WKDHPX(4));
        }];
        
        [self addSubview:self.vipChargeLab];
        [self.vipChargeLab mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(self.mas_centerY);
            make.right.equalTo(self.mas_right).offset(-WKDHPX(4));
        }];
    }
    return self;
}

- (void)layoutSubviews{
    [super layoutSubviews];
   
}

- (void)reload {
    int vipDays = UserInfo.vipDays;
    if (vipDays <= 10 && vipDays > 0 ) {
        self.titleLab.text = [NSString stringWithFormat:@"会员权益还有%d天到期～", vipDays];
        self.purchaseSource = 3;
        self.vipChargeLab.attributedText = ({
            NSString *deviceDescrip = [NSString stringWithFormat:@"%@", @"去开通"];
            NSMutableAttributedString *deviceText = [[NSMutableAttributedString alloc] initWithString:deviceDescrip];
            deviceText.font = kSystem_Font_NoDHPX(WKDHPX(13));
            NSMutableAttributedString *attachText = [[NSMutableAttributedString alloc] initWithString:@"\u{000e605}"];
            attachText.font = [UIFont fontWithName:@"iconfont" size:WKDHPX(13)];
            [deviceText appendAttributedString:attachText];
            deviceText;
        });
        ReportMrkLogParms(1, @"到期提示语", @"page_Coursedetails", @"btn_LiveCourses_instruction_id_listing", nil, 0, @{@"type":@"2"});
    }
    
    if (vipDays == 0 && UserInfo.isMember) {
        self.titleLab.text = @"会员今日到期，给自己再充充能吧！";
        self.purchaseSource = 3;
        self.vipChargeLab.attributedText = ({
            NSString *deviceDescrip = [NSString stringWithFormat:@"%@", @"去开通"];
            NSMutableAttributedString *deviceText = [[NSMutableAttributedString alloc] initWithString:deviceDescrip];
            deviceText.font = kSystem_Font_NoDHPX(WKDHPX(13));
            NSMutableAttributedString *attachText = [[NSMutableAttributedString alloc] initWithString:@"\u{000e605}"];
            attachText.font = [UIFont fontWithName:@"iconfont" size:WKDHPX(13)];
            [deviceText appendAttributedString:attachText];
            deviceText;
        });
        ReportMrkLogParms(1, @"到期提示语", @"page_Coursedetails", @"btn_LiveCourses_instruction_id_listing", nil, 0, @{@"type":@"2"});
    }
    
    if (!UserInfo.isMember){
        MemberInfoDataDTO *memberInfo = [Login curLoginUser].memberInfo;
        self.titleLab.text = [memberInfo.expireDate isNotBlank]?@"会员已到期，但运动不能到期哦～":@"新人开卡享折扣";
        self.purchaseSource = [memberInfo.expireDate isNotBlank]?4:2;
        
        self.vipChargeLab.attributedText = ({
            NSString *deviceDescrip = [NSString stringWithFormat:@"%@", [memberInfo.expireDate isNotBlank]?@"去开通":@"去购卡"];
            NSMutableAttributedString *deviceText = [[NSMutableAttributedString alloc] initWithString:deviceDescrip];
            deviceText.font = kSystem_Font_NoDHPX(WKDHPX(13));
            NSMutableAttributedString *attachText = [[NSMutableAttributedString alloc] initWithString:@"\u{000e605}"];
            attachText.font = [UIFont fontWithName:@"iconfont" size:WKDHPX(13)];
            [deviceText appendAttributedString:attachText];
            deviceText;
        });

        NSInteger t = self.purchaseSource - 1;
        NSString *tipStr = [memberInfo.expireDate isNotBlank]?@"过期提示语":@"新人提示语";
        ReportMrkLogParms(1, tipStr, @"page_Coursedetails", @"btn_LiveCourses_instruction_id_listing", nil, 0, @{@"type":@(t)});
    }
}

- (UIImageView *)iconImageView {
    if (!_iconImageView) {
        _iconImageView = [UIImageView new];
        _iconImageView.contentMode = UIViewContentModeScaleAspectFit;
        _iconImageView.image = [UIImage imageNamed:@"course_detail_vip"];
    }
    return _iconImageView;
}

- (UILabel *)titleLab {
    if (!_titleLab) {
        _titleLab = [UILabel new];
        _titleLab.font = kSystem_Font_NoDHPX(WKDHPX(13));
        _titleLab.textColor = UIColorHex(#672F15);
        _titleLab.text = @"新人开卡享受折扣";
    }
    return _titleLab;
}

- (UILabel *)vipChargeLab {
    if (!_vipChargeLab) {
        _vipChargeLab = [UILabel new];
        _vipChargeLab.font = kSystem_Font_NoDHPX(WKDHPX(13));
        _vipChargeLab.textColor = UIColorHex(#672F15);
        _vipChargeLab.attributedText = ({
            NSString *deviceDescrip = [NSString stringWithFormat:@"%@", @"去购卡"];
            NSMutableAttributedString *deviceText = [[NSMutableAttributedString alloc] initWithString:deviceDescrip];
            deviceText.font = kSystem_Font_NoDHPX(WKDHPX(13));
            NSMutableAttributedString *attachText = [[NSMutableAttributedString alloc] initWithString:@"\u{000e605}"];
            attachText.font = [UIFont fontWithName:@"iconfont" size:WKDHPX(13)];
            [deviceText appendAttributedString:attachText];
            deviceText;
        });
    }
    return _vipChargeLab;
}

@end
