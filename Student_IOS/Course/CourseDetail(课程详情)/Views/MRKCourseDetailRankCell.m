//
//  MRKCourseDetailRankCell.m
//  Student_IOS
//
//  Created by merit on 2023/3/24.
//

#import "MRKCourseDetailRankCell.h"
#import "MRKMainCourseDetailModel.h"

@interface MRKCourseDetailRankCell()
/// 排行图片
@property (nonatomic, strong) UIImageView *rankImageView;
/// 排行数字
@property (nonatomic, strong) UILabel *rankLabel;
/// 头像
@property (nonatomic, strong) UIImageView *avatarImageView;
@property (nonatomic, strong) UIImageView *avatarBorderImageView;
/// 昵称
@property (nonatomic, strong) UILabel *nameLabel;
/// 完成度——百分比
@property (nonatomic, strong) UILabel *percentLabel;
/// 热度
@property (nonatomic, strong) UILabel *hotLabel;
/// VIP类型
@property (nonatomic, strong) UIImageView *vipTypeImageView;
@end

@implementation MRKCourseDetailRankCell

- (void)createUI {
    [self.contentView addSubview:self.rankImageView];
    [self.rankImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView).offset(WKDHPX(16));
        make.centerY.equalTo(self.contentView);
        make.size.mas_equalTo(CGSizeMake(WKDHPX(25), WKDHPX(24)));
    }];
    [self.contentView addSubview:self.rankLabel];
    [self.rankLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.centerX.equalTo(self.rankImageView);
    }];
    
    self.avatarImageView.cornerRadius = WKDHPX(19);
    [self.contentView addSubview:self.avatarImageView];
    [self.avatarImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView).offset(WKDHPX(52));
        make.centerY.equalTo(self.contentView);
        make.size.mas_equalTo(CGSizeMake(WKDHPX(38), WKDHPX(38)));
    }];
    self.avatarBorderImageView.cornerRadius = WKDHPX(25);
    [self.contentView addSubview:self.avatarBorderImageView];
    [self.avatarBorderImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView).offset(WKDHPX(46));
        make.centerY.equalTo(self.contentView);
        make.size.mas_equalTo(CGSizeMake(WKDHPX(50), WKDHPX(50)));
    }];
    
    [self.contentView addSubview:self.percentLabel];
    [self.percentLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.contentView).offset(-WKDHPX(16));
        make.centerY.equalTo(self.contentView);
    }];
    
    
    UILabel *middle = [UILabel new];
    middle.text = @"｜ 完成度";
    middle.font = kSystem_Font_NoDHPX(WKDHPX(13));
    middle.textColor = UIColorHex(#464953);
    [self.contentView addSubview:middle];
    [middle mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.percentLabel.mas_left).offset(-WKDHPX(4));
        make.centerY.equalTo(self.contentView);
    }];
    [self.contentView addSubview:self.hotLabel];
    [self.hotLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(middle.mas_left).offset(-WKDHPX(4));
        make.centerY.equalTo(self.contentView);
    }];
    UIImageView *hotImage = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"img_hot"]];
    [self.contentView addSubview:hotImage];
    [hotImage mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.hotLabel.mas_left).offset(-WKDHPX(3));
        make.centerY.equalTo(self.contentView);
        make.size.mas_equalTo(CGSizeMake(WKDHPX(19), WKDHPX(18)));
    }];
    
    [self.contentView addSubview:self.nameLabel];
    [self.nameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.avatarImageView.mas_right).offset(WKDHPX(12));
        make.right.lessThanOrEqualTo(hotImage.mas_left).offset(-WKDHPX(55));
        make.centerY.equalTo(self.contentView);
    }];
    
    [self.contentView addSubview:self.vipTypeImageView];
    [self.vipTypeImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.nameLabel.mas_right).offset(WKDHPX(2));
        make.centerY.equalTo(self.contentView);
        make.size.mas_equalTo(CGSizeMake(WKDHPX(45), WKDHPX(19)));
    }];
}

#pragma mark - 赋值操作
- (void)configWithItem:(MRKCourseDetailRankModel *)item indexPath:(nonnull NSIndexPath *)indexPath {
    NSInteger rank = indexPath.row+1;
    BOOL isImage = rank < 4; // 前三是图片，后面是数字
    self.rankImageView.hidden = !isImage;
    self.rankLabel.hidden = isImage;
    self.rankImageView.image = isImage ? [UIImage imageNamed:[NSString stringWithFormat:@"live_img_rank_%ld", rank]] : nil;
    self.rankLabel.text = [NSString stringWithFormat:@"%ld", rank];
    
    NSString *clipUrl = [NSString imageUrlClip:item.avatar andSize:CGSizeMake(WKDHPX(38), WKDHPX(38))];
    [self.avatarImageView sd_setImageWithURL:[NSURL URLWithString:clipUrl] placeholderImage:UserInfo.avatarHoldingImage];
    self.avatarBorderImageView.hidden = !item.avatarBox.isNotEmpty;
    NSString *avatarBoxUrl = [item.avatarBox imageUrlAdaptReSize:CGSizeMake(60, 60)];
    [self.avatarBorderImageView setImageURL:[NSURL URLWithString:avatarBoxUrl]];
    
    self.nameLabel.text = item.nickName;
    self.hotLabel.text = item.flameNum;
    self.percentLabel.text = [NSString stringWithFormat:@"%@%%", item.meritRate];
 
    self.vipTypeImageView.image = nil;
    NSString *imageName = nil;
    NSInteger viptype = item.vipType.intValue;
    switch (viptype) {
        case 10: case 20:
            imageName = @"mine_top_vip";
            break;
        case 30:
            imageName = @"mine_top_xenjoy";
            break;
        default:
            break;
    }
    if ([imageName isNotBlank]) {
        self.vipTypeImageView.image = [UIImage imageNamed:imageName];
    }
}

#pragma mark - lazy init

- (UIImageView *)rankImageView {
    if (!_rankImageView) {
        _rankImageView = [UIImageView new];
        _rankImageView.clipsToBounds = YES;
    }
    return _rankImageView;
}

- (UIImageView *)avatarBorderImageView {
    if (!_avatarBorderImageView) {
        _avatarBorderImageView = [UIImageView new];
        _avatarBorderImageView.clipsToBounds = YES;
    }
    return _avatarBorderImageView;
}

- (UILabel *)rankLabel {
    if (!_rankLabel) {
        _rankLabel = [UILabel new];
        _rankLabel.font = kMedium_Font_NoDHPX(WKDHPX(13));
        _rankLabel.textColor = UIColorHex(363A44);
    }
    return _rankLabel;
}

- (UIImageView *)avatarImageView {
    if (!_avatarImageView) {
        _avatarImageView = [UIImageView new];
        _avatarImageView.clipsToBounds = YES;
    }
    return _avatarImageView;
}

- (UIImageView *)vipTypeImageView {
    if (!_vipTypeImageView) {
        _vipTypeImageView = [UIImageView new];
        _vipTypeImageView.clipsToBounds = YES;
    }
    return _vipTypeImageView;
}

- (UILabel *)nameLabel {
    if (!_nameLabel) {
        _nameLabel = [UILabel new];
        _nameLabel.font = kMedium_Font_NoDHPX(WKDHPX(13));
        _nameLabel.textColor = UIColorHex(363A44);
    }
    return _nameLabel;
}

- (UILabel *)hotLabel {
    if (!_hotLabel) {
        _hotLabel = [UILabel new];
        _hotLabel.font = BebasFont_Bold_NoDHPX(15);
        _hotLabel.textColor = UIColorHex(363A44);
    }
    return _hotLabel;
}

- (UILabel *)percentLabel {
    if (!_percentLabel) {
        _percentLabel = [UILabel new];
        _percentLabel.font = BebasFont_Bold_NoDHPX(15);
        _percentLabel.textColor = UIColorHex(363A44);
    }
    return _percentLabel;
}

@end
