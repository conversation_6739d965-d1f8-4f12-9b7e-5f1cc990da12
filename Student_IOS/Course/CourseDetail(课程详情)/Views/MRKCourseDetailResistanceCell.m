//
//  MRKCourseDetailResistanceCell.m
//  Student_IOS
//
//  Created by merit on 2023/9/21.
//

#import "MRKCourseDetailResistanceCell.h"
#import "MRKUltraDetaiAdviceView.h"
#import "MRKMainCourseDetailModel.h"
#import "MRKUltraCommonModel.h"
#import "UIView+Colors.h"
#import "UIView+AZGradient.h"

@interface MRKCourseDetailResistanceCell()

@property (nonatomic, strong) MRKUltraDetaiResistanceView *chartView;

@end

@implementation MRKCourseDetailResistanceCell

- (void)createUI {
    // 建议图表1
    self.chartView = [[MRKUltraDetaiResistanceView alloc] init];
    self.chartView.backgroundColor = UIColor.whiteColor;
    [self.contentView addSubview:self.chartView];
    [self.chartView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(WKDHPX(12));
        make.left.right.mas_equalTo(0);
        make.height.mas_equalTo(WKDHPX(278));
    }];
}

#pragma mark - 赋值操作
- (void)configWithItem:(MRKMainCourseDetailModel *)item {
    [self.chartView setTableDetail:[self adviceView1Data:item]];
}

#pragma mark - 整理表格数据
/*
 * 单车 ，椭圆机 展示 阻力 踏频
 * 划船机           阻力 桨频
 * 跑步机           速度 坡度
 * 阻力 / 坡度  y轴展示高中低
 * 桨频 踏频 速度 y轴展示具体值
 */
- (MRKUltraAdviceTableModel *)adviceView1Data:(MRKMainCourseDetailModel *)detailModel {
    MRKUltraAdviceTableModel *model = [[MRKUltraAdviceTableModel alloc] init];
    NSInteger equimentID = detailModel.equipmentId.intValue;
    
    // 整理表格需要用到的数据
    NSMutableArray *section = [NSMutableArray array];
    // 大节
    for (MRKCourseCataloguePOSModel *pos in detailModel.courseCataloguePOS) {
        NSMutableArray *datas = [NSMutableArray array];
        // 小节
        for (MRKCourseNodeModel *node in pos.courseLinks) {
            int adviceNum;
            if(TreadmillEquipment == equimentID){
                adviceNum = node.maxNum.intValue / 10;
            }else {
                adviceNum = node.adviseNum.intValue;
            }
            NSLog(@"第一个view的建议值：%d",adviceNum);
            WKAdviceTableData *data = [[WKAdviceTableData alloc] initStart:node.beginTime.intValue end:node.endTime.intValue advice:adviceNum];
            [datas addObject:data];
        }
        [section addObject:datas];
    }
    
    model.tableData = section;
    
    switch (equimentID) {
        case BicycleEquipment:  //动感单车
        case EllipticalEquipment:  //椭圆机
        case BoatEquipment:     //划船机
        case PowerEquipment:    //力量站
        {
            NSNumber *max = [detailModel.nodesArray valueForKeyPath:@"@max.adviseNum.intValue"];
            int maxadvice = max.intValue <= 0 ? 32 : max.intValue;
//            maxadvice = ceil(maxadvice / 10.0) * 10;  //不满10的倍数。向上取整
            NSLog(@"最大阻力：%d",maxadvice);
            model.title = @"建议阻力";
            model.maxYAxis = maxadvice;
            model.yTitle = @[@"高",@"中",@"低"];
        }
            break;
             
        case TreadmillEquipment://跑步机
        {
            // 取出最大速度值
            NSNumber *max = [detailModel.nodesArray valueForKeyPath:@"@max.maxNum.intValue"];
            int maxspeed = max.intValue / 10 <= 0 ? 20 : max.intValue / 10;
//            maxspeed = ceil(maxspeed / 10.0) * 10;  //不满10的倍数。向上取整
            maxspeed = ceil(maxspeed / 2.0) * 2;  //不满10的倍数。向上取整
            NSLog(@"最大速度：%d",maxspeed);
            model.title = @"建议速度";
            model.maxYAxis = maxspeed;
            int jiange = 2;
            NSMutableArray *arr = [NSMutableArray array];
            for (int i = maxspeed; i >= 0; i = i -jiange) {
                [arr addObject:[NSString stringWithFormat:@"%d",i]];
            }
            model.yTitle = arr;
        }
            break;
            
        default:
            break;
    }
    
    return model;
}

@end

@interface MRKUltraDetaiResistanceView() {
    int tableHeight;     //表格高度
    int lineHeight;      //表格内容线的高度
    float secondWidth;   //每一秒对应的宽度
    float adviceHeight;  //每一建议值对应的高度
}

@property (nonatomic, strong) UILabel *titleLabel;      //标题
@property (nonatomic, strong) UIView *colorView;        //渐变色值
@property (nonatomic, strong) UIView *contentView;      //内容承载
@property (nonatomic, strong) CAShapeLayer *lineLayer;  //虚线
@property (nonatomic, strong) UIView *yView;            //y轴
@property (nonatomic, strong) UIView *xView;            //x轴
@end

@implementation MRKUltraDetaiResistanceView
- (instancetype)init {
    if (self = [super init]) {
        tableHeight = WKDHPX(192);
        lineHeight = WKDHPX(3);
        
        [self initUI];
    }
    return self;
}
- (void)initUI {
    [self addSubview:self.titleLabel];
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(WKDHPX(16));
        make.top.mas_equalTo(WKDHPX(12));
        make.height.mas_equalTo(WKDHPX(26));
    }];
    
    self.colorView.frame = CGRectMake(0, 0, WKDHPX(5), tableHeight);
    [self addSubview:self.colorView];
    [self.colorView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.titleLabel.mas_bottom).offset(WKDHPX(12));
        make.left.mas_equalTo(WKDHPX(31));
        make.size.mas_equalTo(CGSizeMake(WKDHPX(5), tableHeight));
    }];
    
    [self addSubview:self.yView];
    [self.yView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.colorView.mas_left).offset(-WKDHPX(2));
        make.top.equalTo(self.colorView.mas_top);
        make.bottom.equalTo(self.colorView.mas_bottom);
        make.width.mas_greaterThanOrEqualTo(WKDHPX(20));
    }];
    
    [self addSubview:self.contentView];
    [self.contentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(self.colorView.mas_right).offset(0);
        make.right.mas_equalTo(-WKDHPX(16));
        make.top.equalTo(self.colorView.mas_top);
        make.bottom.equalTo(self.colorView.mas_bottom);
    }];
    
    [self addSubview:self.xView];
    [self.xView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(self.colorView.mas_right).offset(0);
        make.top.equalTo(self.contentView.mas_bottom).offset(0);
        make.right.equalTo(self.contentView.mas_right);
        make.height.mas_equalTo(WKDHPX(30));
    }];
}

// 填充表格数据
- (void)setTableDetail:(MRKUltraAdviceTableModel *)data {
    if (data.tableData.count <= 0) {
        return;
    }
    
    WKAdviceTableData * model = [[data.tableData lastObject] lastObject];
    NSInteger t = model.end ?: 1; //时间
    secondWidth = (MainWidth - WKDHPX(31) - WKDHPX(5) - WKDHPX(16)) * 1.0 / t; //每一秒的宽度
    adviceHeight = (tableHeight - lineHeight) * 1.0 / data.maxYAxis;   //建议值对应的高度
    
    self.titleLabel.text = data.title;
    // 开始绘制数据
    // x轴
    [self drawTimeAndBack:data.tableData];
    // y轴
    [self drawYLabel:data.yTitle];
    
    // 数据合并
    NSMutableArray *tmparr = [NSMutableArray array];
    for (NSArray *arr in data.tableData) {
        [tmparr addObjectsFromArray:arr];
    }
    //色值数据
    [self drawAdviceNumber:tmparr];
    //虚线
    [self drawDottedLine:tmparr];

}


- (void)setdetail {
    NSMutableArray *section = [NSMutableArray array];
    // 制造假数据
    NSMutableArray *data = [NSMutableArray array];
    [data addObject:[[WKAdviceTableData alloc] initStart:0 end:30 advice:0]];
    [data addObject:[[WKAdviceTableData alloc] initStart:30 end:150 advice:30]];
    [data addObject:[[WKAdviceTableData alloc] initStart:150 end:240 advice:70]];
    [data addObject:[[WKAdviceTableData alloc] initStart:240 end:360 advice:110]];
    [section addObject:data];
    
    NSMutableArray *data1 = [NSMutableArray array];
    [data1 addObject:[[WKAdviceTableData alloc] initStart:360 end:450 advice:110]];
    [data1 addObject:[[WKAdviceTableData alloc] initStart:450 end:650 advice:30]];
    [data1 addObject:[[WKAdviceTableData alloc] initStart:650 end:950 advice:70]];
    [data1 addObject:[[WKAdviceTableData alloc] initStart:950 end:1130 advice:110]];
    [section addObject:data1];

    
    NSMutableArray *data2 = [NSMutableArray array];
    [data2 addObject:[[WKAdviceTableData alloc] initStart:1130 end:1450 advice:80]];
    [data2 addObject:[[WKAdviceTableData alloc] initStart:1450 end:1650 advice:30]];
    [data2 addObject:[[WKAdviceTableData alloc] initStart:1650 end:1950 advice:70]];
    [data2 addObject:[[WKAdviceTableData alloc] initStart:1950 end:2130 advice:160]];
    [data2 addObject:[[WKAdviceTableData alloc] initStart:2130 end:2330 advice:60]];
    [section addObject:data2];

    int y = 160;   //y轴最大是 30
    WKAdviceTableData *model = [[section lastObject] lastObject];
    NSInteger t = model.end; //时间
    secondWidth = (MainWidth - WKDHPX(31) - WKDHPX(5) - WKDHPX(16)) * 1.0 / t; //每一秒的宽度
    adviceHeight = (tableHeight - lineHeight) * 1.0 / y;   //建议值对应的高度
    
    // x轴
    [self drawTimeAndBack:section];
    // y轴
    NSMutableArray *ytitle = [NSMutableArray array];
    for (int i = 160; i >=0; i = i -20) {
        [ytitle addObject:[NSString stringWithFormat:@"%d",i]];
    }
    [self drawYLabel:ytitle];

    NSMutableArray *tmparr = [NSMutableArray array];
    for (NSArray *arr in section) {
        [tmparr addObjectsFromArray:arr];
    }
    [self drawAdviceNumber:tmparr];
    [self drawDottedLine:tmparr];
    
}


// 绘制 x轴
- (void)drawTimeAndBack:(NSArray *)data {
    [self.xView removeAllSubviews];
    for (int i = 0; i < data.count; i ++) {
        // 背景
        if (i % 2 != 0) {
            NSArray *arr = data[i];
            WKAdviceTableData *star = arr.firstObject;
            WKAdviceTableData *end = arr.lastObject;
            UIView *view = [[UIView alloc] init];
            view.frame = CGRectMake(star.start * secondWidth, 0, (end.end - star.start) * secondWidth, tableHeight);
            view.backgroundColor = [UIColor colorWithHexString:@"#F2F2F9"];
            [self.contentView addSubview:view];
        }
    }
    
    
    NSMutableArray *times = [[NSMutableArray alloc] init];
    NSInteger beginTime = 0; //时间
    NSInteger endTime = 0; //时间
    if (data.count > 0) {
        NSArray *section1 = data[0];
        WKAdviceTableData *model1 = section1.firstObject;
        [times appendObject:@(model1.start)];
        beginTime = model1.start;
        
        NSArray *section2 = data[data.count - 1];
        WKAdviceTableData *model2 = section2.lastObject;
        [times appendObject:@(model2.end)];
        endTime = model2.end;
    }
    CGFloat preWidth = (MainWidth - WKDHPX(31) - WKDHPX(5) - WKDHPX(16)) * 1.0 / endTime; //每一秒的宽度
    
    if (endTime > beginTime) {
        // 四等份
        NSInteger first = (endTime + beginTime)/3 + beginTime;
        NSInteger second = (endTime + beginTime)/3 * 2 + beginTime;
        [times insertObject:@(second) atIndex:1];
        [times insertObject:@(first) atIndex:1];
    }
    
    for (int i = 0; i < times.count; i ++) {
        NSTimeInterval ti = [times[i] intValue];
        UILabel *label = [[UILabel alloc] init];
        label.font = kSystem_Font_NoDHPX(WKDHPX(10));
        label.textColor = [UIColor colorWithHexString:@"#999999"];
        label.text = [NSString mrk_takeTimeSecondTransForMin:ti];
        [self.xView addSubview:label];
        if (i == 0) {
            [label mas_makeConstraints:^(MASConstraintMaker *make) {
                make.top.mas_equalTo(WKDHPX(4));
                make.left.mas_equalTo(0);
            }];
        } else if (i == times.count -1) {
            [label mas_makeConstraints:^(MASConstraintMaker *make) {
                make.top.mas_equalTo(WKDHPX(4));
                make.right.mas_equalTo(0);
            }];
        } else {
            [label mas_makeConstraints:^(MASConstraintMaker *make) {
                make.top.mas_equalTo(WKDHPX(4));
                make.centerX.equalTo(self.xView.mas_left).offset(ti * secondWidth);
            }];
        }
    }
}

// 绘制y轴数据
- (void)drawYLabel:(NSArray<NSString *> *)data {
    [self.yView removeAllSubviews];
    for (NSString *str in data) {
        UILabel *label = [[UILabel alloc] init];
        label.textColor = [UIColor colorWithHexString:@"#999999"];
        label.font = kSystem_Font_NoDHPX(WKDHPX(10));
        label.textAlignment = NSTextAlignmentCenter;
        label.text = str;
        [self.yView addSubview:label];
    }
    if (self.yView.subviews.count == 1) {
        [self.yView.subviews mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.mas_equalTo(0);
            make.top.bottom.mas_equalTo(0);
        }];
        
    }else {
        [self.yView.subviews mas_distributeViewsAlongAxis:MASAxisTypeVertical withFixedSpacing:4 leadSpacing:0 tailSpacing:0];
        [self.yView.subviews mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.mas_equalTo(0);
        }];
    }
}

//绘制建议数据
- (void)drawAdviceNumber:(NSArray *)data {
    for (int i = 0; i < data.count; i++) {
        WKAdviceTableData *model = data[i];
        UIView *view = [[UIView alloc] init];
        view.cornerRadius = 1.5;
        CGFloat y = (tableHeight - lineHeight) - (model.advice * adviceHeight);
        
        UIColor *color = [self.colorView colorOfPoint:CGPointMake(2, y)];
        view.backgroundColor = color;
        
        view.frame = CGRectMake(model.start * secondWidth, y, (model.end - model.start) *secondWidth, 3);
        [self.contentView addSubview:view];
    }
}

//绘制虚线
- (void)drawDottedLine:(NSArray *)data {
    [self.contentView.layer addSublayer:self.lineLayer];
    self.lineLayer.frame = CGRectMake(0, 0, MainWidth - 29 - 5 - 14, tableHeight);
    UIBezierPath *path = [UIBezierPath bezierPath];
    for (int i = 0; i < data.count; i++) {
        if (i < data.count -1) {
            WKAdviceTableData *model = data[i];
            WKAdviceTableData *next = data[i+1];
            NSInteger h = labs(model.advice - next.advice);  //高度差
            NSInteger advice = MAX(model.advice, next.advice);
            
            CGFloat y = (tableHeight - lineHeight) - (advice * adviceHeight);
            [path moveToPoint:CGPointMake(next.start * secondWidth, y)];
            [path addLineToPoint:CGPointMake(next.start * secondWidth, y + h * adviceHeight)];
        }
    }
    self.lineLayer.path = path.CGPath;
}

#pragma mark - lazy

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        UILabel *label = [[UILabel alloc] init];
        label.font = kMedium_Font_NoDHPX(WKDHPX(17));
        label.textColor = UIColorHex(363A44);
        _titleLabel = label;
    }
    return _titleLabel;
}

- (UIView *)colorView {
    if (!_colorView) {
        _colorView = [[UIView alloc] init];
        [_colorView az_setGradientBackgroundWithColors:@[
            [UIColor colorWithHexString:@"#FF0044"],
            [UIColor colorWithHexString:@"#CE64F1"],
            [UIColor colorWithHexString:@"#44DEFF"],
            [UIColor colorWithHexString:@"#9DFF98"]
        ] locations:@[
            @(0), @(0.3f), @(0.7f), @(1.0f)
        ] startPoint:CGPointMake(0, 0) endPoint:CGPointMake(0, 1)];
    }
    return _colorView;
}

- (UIView *)contentView {
    if (!_contentView) {
        _contentView = [[UIView alloc] init];
        _contentView.backgroundColor = [UIColor colorWithHexString:@"#F8F8FA"];
    }
    return _contentView;
}

- (UIView *)yView {
    if (!_yView) {
        _yView = [[UIView alloc] init];
    }
    return _yView;
}
- (UIView *)xView {
    if (!_xView) {
        _xView = [[UIView alloc] init];
    }
    return _xView;
}

- (CAShapeLayer *)lineLayer {
    if (!_lineLayer) {
        CAShapeLayer *layer = [CAShapeLayer layer];
        [layer setFillColor:[UIColor clearColor].CGColor];
        //  设置虚线颜色为blackColor
        [layer setStrokeColor:[UIColor colorWithHexString:@"#CCCCCC"].CGColor];
        [layer setLineWidth:0.5];
        [layer setLineJoin:kCALineJoinRound];
        //  设置线宽，线间距
        [layer setLineDashPattern:[NSArray arrayWithObjects:[NSNumber numberWithInt:4], [NSNumber numberWithInt:2], nil]];

        _lineLayer = layer;
    }
    return _lineLayer;
}

@end
