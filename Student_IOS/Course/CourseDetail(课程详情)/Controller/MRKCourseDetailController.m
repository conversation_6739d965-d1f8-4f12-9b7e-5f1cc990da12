//
//  MRKCourseDetailController.m
//  Student_IOS
//
//  Created by merit on 2023/3/23.
//

#import "MRKCourseDetailController.h"
#import "MRKCourseDetailTopView.h"

#import "MRKCourseDetailHeaderCell.h"
#import "MRKCourseDetailInfoCell.h"
#import "MRKCourseDetailTeacherCell.h"
#import "MRKCourseDetailContentCell.h"
#import "MRKCourseDetailIntroCell.h"
#import "MRKCourseDetailResistanceCell.h"
#import "MRKCourseDetailRankCell.h"

#import "MRKCourseDetailTableFooterView.h"
#import "CourseTipController.h"
#import "MRKPopupManager.h"
#import "MRKCourseDetailRankRuleAlertView.h"
#import "MRKCourseDetailShareView.h"
#import "MRKShareImageController.h"
#import "UIScrollView+Screenshot.h"
#import "UIViewController+ShareImage.h"
#import "MRKMainCourseDetailModel.h"
#import "MRKToolKit.h"
#import "MRKTraceManager.h"
#import "MRKShareButtonsView.h"
#import "UIView+AZGradient.h"
#import "MRKVideoExperienceController.h"
#import "MRKCourseDetailBottomVue.h"
#import "MRKVipCardModel.h"
#import "FlutterModels.h"
#import "MRKHomeAlertManager.h"
#import "MRKCourseDetailPageModel.h"
#import "MRKSubscribeMembershipAlert.h"




@interface MRKCourseDetailController ()<UITableViewDelegate, UITableViewDataSource, UIScrollViewDelegate>
/// 导航栏视图
@property (nonatomic, strong) UIView *navigationBarView;
/// 返回按钮
@property (nonatomic, strong) UIButton *backBtn;
/// 标题
@property (nonatomic, strong) UILabel *titleLabel;
/// 收藏按钮
@property (nonatomic, strong) UIButton *collectBtn;
/// 分享按钮
@property (nonatomic, strong) UIButton *shareBtn;
/// 主视图列表
@property (nonatomic, strong) UITableView *tableView;
/// 头部视图_tableheaderview
@property (nonatomic, strong) MRKCourseDetailTopView *topView;
/// 排行榜的头部区域-section headerview
@property (nonatomic, strong) UIView *rankHeaderView;
/// 课程尾部区域——协议
@property (nonatomic, strong) MRKCourseDetailTableFooterView *tableFooterView;
/// 底部显示
@property (nonatomic, strong) MRKCourseDetailBottomVue *bottomVue;
/// 分享的视图
@property (nonatomic, strong) MRKCourseDetailShareView *shareView;
/// 详情数据
@property (nonatomic, strong) MRKMainCourseDetailModel *model;

@property (nonatomic, assign) BOOL isShowNotificationPop;

@property (nonatomic, assign) BOOL hasReloaded;

@property (nonatomic, strong) MRKCourseDetailPageModel *pageModel;
@end

@implementation MRKCourseDetailController
- (MRKCourseDetailPageModel *)pageModel{
    if (!_pageModel){
        _pageModel = [[MRKCourseDetailPageModel alloc] init];
    }
    return _pageModel;
}

- (void)viewDidLoad {
    self.tracePageId = @"page_Coursedetails";
    self.tracePara = @{@"course_id": self.courseId};
    [super viewDidLoad];
    self.view.backgroundColor = [UIColor whiteColor];
    
    self.pageModel.courseId = self.courseId;
    @weakify(self);
    MrkEmptyView *emptyView = [[MrkEmptyView alloc] init];
    emptyView.fullCoverSuperView = YES;
    emptyView.tapEmptyViewBlock = ^{
        [self_weak_ requestData];
    };
    emptyView.errorBtnClickBlock = ^{
        [self_weak_ requestData];
    };
    self.tableView.pageEmptyView = emptyView;
    
    [self addAllSubviews];
    /// registercell
    [self tableRegisterCell];
    
    //添加跳转训练报告后返回课程详情
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(showNotificationCode)
                                                 name:@"UpdateReportComplete"
                                               object:nil];
    
    //会员开通刷新用户信息
    //有可能会员会延时到账
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(updateUserInfo)
                                                 name:@"UpdateUserInfo"
                                               object:nil];
    
//    self.tableView.mj_header = [MJDIYHeader headerWithRefreshingTarget:self refreshingAction:@selector(requestData)];
//    self.tableView.mj_header.automaticallyChangeAlpha = YES;
    
    [self.view beginLoading];
    [self requestData];
}

- (void)updateUserInfo{
    [self requestData];
}

- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    
    ///
    if (self.hasReloaded){
        [self requestData];
    }
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear: animated];
    if (self.isShowNotificationPop) {
        [[MRKHomeAlertManager shareManager] showNotificationOpenAlert:MRKCourseDetailNotificationCode];
        self.isShowNotificationPop = NO;
    }
}

/// 展示通知权限弹窗
- (void)showNotificationCode{
    self.isShowNotificationPop = YES;
}

- (void)viewWillDisappear:(BOOL)animated{
    [super viewWillDisappear: animated];
    [self.topView stopPlayVideo];
}

/// registercell
- (void)tableRegisterCell {
    [_tableView registerClass:[MRKCourseDetailHeaderCell class] forCellReuseIdentifier:[MRKCourseDetailHeaderCell sc_className]];
    [_tableView registerClass:[MRKCourseDetailInfoCell class] forCellReuseIdentifier:[MRKCourseDetailInfoCell sc_className]];
    [_tableView registerClass:[MRKCourseDetailTeacherCell class] forCellReuseIdentifier:[MRKCourseDetailTeacherCell sc_className]];
    [_tableView registerClass:[MRKCourseDetailContentCell class] forCellReuseIdentifier:[MRKCourseDetailContentCell sc_className]];
    [_tableView registerClass:[MRKCourseDetailIntroCell class] forCellReuseIdentifier:[MRKCourseDetailIntroCell sc_className]];
    [_tableView registerClass:[MRKCourseDetailResistanceCell class] forCellReuseIdentifier:[MRKCourseDetailResistanceCell sc_className]];
    [_tableView registerClass:[MRKCourseDetailRankCell class] forCellReuseIdentifier:[MRKCourseDetailRankCell sc_className]];
}

#pragma mark - 接口请求
- (void)requestData {
    MLog(@"课程详情接口调用");
    @weakify(self);
    [self.pageModel.updateDetailSignal subscribeNext:^(id x) {
        @strongify(self);
        [self.view endLoading];
        [self.tableView hiddenEmptyView];
        self.hasReloaded = YES;
        
        if (self.pageModel.courseModel != nil){
            ///白色返回
            [self.backBtn setImage:[UIImage imageNamed:@"icon_back_search"] forState:UIControlStateNormal];
            self.model = self.pageModel.courseModel;
            [self handleCourseDetailRequestResult];
        }else{
            /// 黑色返回
            [self.backBtn setImage:[UIImage imageNamed:@"icon_back"] forState:UIControlStateNormal];
            [self.tableView mrkShowNetworkErrorEmptyView];
        }
        
        BOOL courseNotesTip = [[MRKToolKit getUserDefaultsObjectForKey:@"CourseNotesTip"] boolValue];
        [self.tableFooterView readStates:courseNotesTip];
        
        self.bottomVue.vipModel = self.pageModel.cheapestModel;
    }];
    
    ///第一次请求数据，要求先刷设备列表，再刷页面数据
    [self.pageModel refreshPageData];
}

/// 处理详情请求的数据
- (void)handleCourseDetailRequestResult{
  
    /// 更新页面显示
    if (self.bottomVue.isHidden) {
        self.tableView.tableHeaderView = self.topView;
        self.tableView.tableFooterView = self.tableFooterView;
        self.bottomVue.hidden = NO;
    }
    
    self.titleLabel.text = self.model.name;
    self.topView.model = self.model;
    self.collectBtn.selected = self.model.isCollect;
    [self.bottomVue updateBottomVue:self.model];
    [self.shareView configData:self.model];
    [self.tableView reloadData];
}

#pragma mark - 事件交互
/// 返回
- (void)backAction:(UIButton *)sender{
    sender.traceEventId = @"btn_all_return";
    [self.navigationController popViewControllerAnimated:YES];
}

/// 收藏/取消收藏
- (void)collectAction:(UIButton *)sender{
    sender.traceEventId = self.collectBtn.selected ? @"btn_Coursedetails_cancle_collect": @"btn_Coursedetails_collected";
    sender.tracePara = @{@"course_id": self.courseId};
    
    [MBProgressHUD showLodingWithMessage:@"" view:self.view];
    NSDictionary *tempDic = @{
        @"courseId":self.courseId?:@"",
        @"operation": self.collectBtn.selected ? @"0": @"1"
    };

    [[MRKRequestData sharedInstance] getCourseCollectWithPara:tempDic ComBlock:^(BOOL sucess, id  _Nonnull result) {
        [MBProgressHUD hideHUDForView:self.view];
        if (sucess) {
            MRKResponse *response = [MRKResponse modelWithDictionary:result];
            if (response.status.intValue == 200) {
                self.collectBtn.selected = !self.collectBtn.isSelected;
                [MBProgressHUD showMessage: self.collectBtn.selected ? @"收藏成功" : @"已取消收藏"];
            }
        }
    }];
}


/// 分享
- (void)shareAction:(UIButton *)sender{
    sender.traceEventId = @"btn_all_share";
    sender.tracePara = @{@"course_id": self.courseId?:@""};
    [self.topView stopPlayVideo];
    
    // 先请求接口
    [MBProgressHUD showLodingWithMessage:@"" view:self.view];
    NSDictionary *parm = @{
        @"params":@{@"courseId":self.courseId?:@""}.modelToJSONString,
        @"jumpType":@"course_detail"
    };
    @weakify(self);
    [MRKBaseRequest mrkRequestType:YTKRequestMethodPOST
                               url:@"/app/log-share/generateShareLink"
                           andParm:parm
          completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        @strongify(self);
        [MBProgressHUD hideHUDForView:self.view];
        NSString *linkUrl = [request.responseObject objectForKey:@"data"];
//        [self createSharePicWithLink:linkUrl];
        [self flutterShareLink:linkUrl];
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        @strongify(self);
        [MBProgressHUD hideHUDForView:self.view];
    }];
}

- (void)createSharePicWithLink:(NSString *)url{
    if (url.isNotEmpty) {
        self.shareView.shareUrl = url;
        self.shareView.hidden = NO;
        @weakify(self);
        
        UIImage *img = [self.shareView.scrollView snapshotScreen];  //截长图
        MRKShareImageController *vc = [[MRKShareImageController alloc] init];
        vc.closeBtn.traceEventId = @"btn_all_share_close";
        vc.closeBtn.tracePara = @{@"course_id": self.courseId};
        vc.shareClick = ^(NSInteger index) {
            @strongify(self);
            [[MRKTraceManager sharedInstance] manualUploadTraceType:2 
                                                          pageTitle:self.title
                                                             pageId:self.tracePageId
                                                            eventId:@"btn_all_share_click"
                                                              route:self.tracePageRoute
                                                           duration:0
                                                         extendPara:@{@"control_result": [MRKShareButtonsView userClickName: index]}];
        };
        vc.shareImage = img;
        UIImage *image = [img imageByBlurRadius:10 tintColor:nil tintMode:0 saturation:0.7 maskImage:img];
        UIImageView *bgimgv = [[UIImageView alloc] initWithImage:image];
        bgimgv.contentMode = UIViewContentModeTop;
        [self.navigationController presentSemiViewController:vc
                                                 withOptions:@{
            KNSemiModalOptionKeys.pushParentBack : @(YES),
            KNSemiModalOptionKeys.transitionStyle : @(KNSemiModalTransitionStyleShareImage),
            KNSemiModalOptionKeys.disableCancel : @(YES),
            KNSemiModalOptionKeys.backgroundView:bgimgv,
            KNSemiModalOptionKeys.shareImage : img,
            KNSemiModalOptionKeys.parentScale : @(0.8),
            KNSemiModalOptionKeys.animationDuration : @(0.5),
            KNSemiModalOptionKeys.parentAlpha : @(1.0),
            KNSemiModalOptionKeys.shadowOpacity : @(0.3)
        } completion:^{
            @strongify(self);
            self.shareView.hidden = YES;
        } dismissBlock:^{
            @strongify(self);
            self.shareView.hidden = YES;
        }];
    }
}

// flutter 分享
- (void)flutterShareLink:(NSString *)url{
    if (url.isNotEmpty) {
//        NSString *json = self.model.modelToJSONString;
//        CourseDetailShareModel *model = [CourseDetailShareModel modelWithJSON:json];
        NSString *user = Login.curLoginUser.basicInfo.modelToJSONString;
        NSString *avatarBox = Login.curLoginUser.levelInfo.avatarBox?:@"";
        NSDictionary *dic = @{
            @"shareLink":url,
            @"courseId":self.model.courseId,
//            @"courseDetailBeanJson":model.modelToJsonString,
            @"userBasicDataDto":user,
            @"avatarBox":avatarBox
        };
        [FlutterManager shareCourseDetailVCWithDic:dic];
    }
}

/// 点击排行榜的问号
- (void)questionAction:(UIButton *)sender{
    sender.traceEventId = @"btn_Coursedetails_view_rankingrule";
    sender.tracePara = @{ @"course_id": self.model.courseId };
    
    MRKCourseDetailRankRuleAlertView *alert = [[MRKCourseDetailRankRuleAlertView alloc] initWithAnimationStyle:MRKActionAlertViewTransitionStyleFade];
    alert.opaquess = 0.6;
    alert.courseId = self.model.courseId;
    [[MRKPopupManager sharedInstance] showAlertView:alert level:MRKPopupViewLevelHeight callback:nil];
}

/// 开始上课
- (void)startAction {
    MRKSubscribeMembershipAlert *alert = [[MRKSubscribeMembershipAlert alloc] initWithAnimationStyle:MRKActionAlertViewTransitionStyleSlideFromBottom];
    alert.alertType = MembershipAlertTypeAIPlan;
    [alert show];
    return;
    
    
    
    
    [self.topView stopPlayVideo];
    if (self.model.videoType == MrkVideoTypeLivePrep) {
        @weakify(self);
        [MRKRequestData makeButtonClick:self.model scene:@"coursedetail" atController:self succeed:^{
            @strongify(self);
            //在主线程中刷新UI
            jxt_getSafeMainQueue(^{
                [self requestData];
            });
        }];
        return;
    }
    
    
    if (self.model.videoType == MrkVideoTypeLiveEnd) {
        [MBProgressHUD showMessage:@"课程正在准备中，请稍后观看"];
        return;
    }
    
    /// 直播课、vip课程此时非免费状态，需要引导普通用户去付费
    /// 🔥🔥🔥 直播可不可看由后端字段canPlayCourse控制, 本地不做判断 [canPlayCourse不适用录播]
    if (self.model.videoType == MrkVideoTypeLive && !self.model.canPlayCourse) {
        [DYFStoreManager shared].appPurchaseSource = TraceVipOpenSourceTypeCourse;
        [[RouteManager sharedInstance] skipVIP];///直接跳转到会员页
        return;
    }
    
    NSDictionary *parms = @{
        ParametersKey: @{
            @"model" : self.model,
            @"connectType": @"2",
            @"courseInAIPlan" : @(self.courseInAIPlan)
        },
        @"fromePage" : NSStringFromClass([self class]),
        BlueDeviceType : self.model.equipmentId
    };
    [[NSNotificationCenter defaultCenter] postNotificationName:JudgeConnectDeviceNotification object:parms];
}

#pragma mark - UITableViewDelegate & UITableViewDataSource
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return self.model == nil ? 0 : ((self.model.courseRank.count?:0) == 0 ? 6 : 7);
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    if (section == MRKCourseDetailCellSectionTeacher) {
        return self.model.coachPO != nil ? 1 : 0;
    }
    if (section == MRKCourseDetailCellSectionContent) {
        return self.model.courseCataloguePOS.count > 0 ? 1 : 0;
    }
    if (section == MRKCourseDetailCellSectionResistance) {
        return (self.model.courseCataloguePOS.count > 0 && (self.model.equipmentId.intValue == PowerEquipment)) ? 1 : 0;
    }
    if (section == MRKCourseDetailCellSectionRank) { // 仅指展示10名
        return MIN(self.model.courseRank.count?:0, 10);
    }
    return 1;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    MRKCourseDetailCellSection section = indexPath.section;
    switch (section) {
        case MRKCourseDetailCellSectionHeader:  //课程详情头部数据-标题
            return UITableViewAutomaticDimension;
        case MRKCourseDetailCellSectionInfo:    //课程data 距离、消耗、速度
            return self.model.viewPO.isView ? WKDHPX(68) : WKDHPX(1) ;
        case MRKCourseDetailCellSectionTeacher: //课程教练
            return WKDHPX(56);
        case MRKCourseDetailCellSectionContent: //课程内容
            return UITableViewAutomaticDimension;
        case MRKCourseDetailCellSectionIntro:   //课程介绍
            return UITableViewAutomaticDimension;
        case MRKCourseDetailCellSectionResistance:
            return WKDHPX(290);
        case MRKCourseDetailCellSectionRank:    //排行榜
            return WKDHPX(54);
    }
    return 0.01;
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section{
    if (section == MRKCourseDetailCellSectionHeader) {
        return 0.01;
    }
    if (section == MRKCourseDetailCellSectionInfo) {
        return self.model.viewPO.isView ? WKDHPX(20) : 0.01;
    }
    if (section == MRKCourseDetailCellSectionTeacher) {
        return self.model.viewPO.isView ? WKDHPX(28) : WKDHPX(20);
    }
    if (section == MRKCourseDetailCellSectionContent) {
        return self.model.courseCataloguePOS.count > 0 ? WKDHPX(28) : 0.01;
    }
    if (section == MRKCourseDetailCellSectionResistance) {
        return 0.01;
    }
    if (section == MRKCourseDetailCellSectionRank) {
        return WKDHPX(35);
    }
    return WKDHPX(28);
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section{
    return 0.01;
}

- (nullable UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section{
    if (section == MRKCourseDetailCellSectionRank) {
        return self.rankHeaderView;
    }
    return [UIView new];
}

- (nullable UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section{
    return [UIView new];
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (indexPath.section == MRKCourseDetailCellSectionHeader) { //课程详情头部数据-标题
        MRKCourseDetailHeaderCell *cell = [tableView dequeueReusableCellWithIdentifier:[MRKCourseDetailHeaderCell sc_className] forIndexPath:indexPath];
        [cell configWithItem:self.model];
        return cell;
    }
    if (indexPath.section == MRKCourseDetailCellSectionInfo) { //课程data 距离、消耗、速度
        if (self.model.viewPO.isView){
            MRKCourseDetailInfoCell *cell = [tableView dequeueReusableCellWithIdentifier:[MRKCourseDetailInfoCell sc_className] forIndexPath:indexPath];
            [cell configWithItem:self.model];
            return cell;
        } else {
            return [UITableViewCell new];
        }
    }
    if (indexPath.section == MRKCourseDetailCellSectionTeacher) { //课程教练
        MRKCourseDetailTeacherCell *cell = [tableView dequeueReusableCellWithIdentifier:[MRKCourseDetailTeacherCell sc_className] forIndexPath:indexPath];
        [cell configWithItem:self.model];
        return cell;
    }
    if (indexPath.section == MRKCourseDetailCellSectionIntro) {
        MRKCourseDetailIntroCell *cell = [tableView dequeueReusableCellWithIdentifier:[MRKCourseDetailIntroCell sc_className] forIndexPath:indexPath];
        [cell configWithItem:self.model];
        return cell;
    }
    if (indexPath.section == MRKCourseDetailCellSectionContent) { //课程内容
        MRKCourseDetailContentCell *cell = [tableView dequeueReusableCellWithIdentifier:[MRKCourseDetailContentCell sc_className] forIndexPath:indexPath];
        [cell configWithItem:self.model];
        return cell;
    }
    if (indexPath.section == MRKCourseDetailCellSectionResistance) { //建议阻力
        MRKCourseDetailResistanceCell *cell = [tableView dequeueReusableCellWithIdentifier:[MRKCourseDetailResistanceCell sc_className] forIndexPath:indexPath];
        [cell configWithItem:self.model];
        return cell;
    }
    if (indexPath.section == MRKCourseDetailCellSectionRank) { //排行榜
        MRKCourseDetailRankCell *cell = [tableView dequeueReusableCellWithIdentifier:[MRKCourseDetailRankCell sc_className] forIndexPath:indexPath];
        [cell configWithItem:self.model.courseRank[indexPath.row] indexPath:indexPath];
        return cell;
    }
    return [UITableViewCell new];
}




#pragma mark - UIScrollViewDelegate
- (void)scrollViewDidScroll:(UIScrollView *)scrollView{
    float alpha = MIN(scrollView.contentOffset.y / (64 + kStatusBarHeight), 1);
    self.navigationBarView.backgroundColor = UIColorHexAlpha(#4D5361, alpha);
    self.titleLabel.alpha = alpha;
}

#pragma mark - 布局
- (void)addAllSubviews {
    [self.mrkContentView addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsMake(0, 0, 0, 0));
    }];
    self.tableView.contentInset = UIEdgeInsetsMake(0, 0, DHPX(64)+kSafeArea_Bottom, 0);
    
    
    [self.mrkContentView addSubview:self.navigationBarView];
    [self.navigationBarView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.right.mas_equalTo(0);
        make.height.mas_equalTo(kNavBarHeight);
    }];
    [self.navigationBarView addSubview:self.titleLabel];
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(_navigationBarView);
        make.top.equalTo(_navigationBarView).offset(44 - statusLength);
        make.size.mas_equalTo(CGSizeMake(WKDHPX(166), 44));
    }];
    [self.navigationBarView addSubview:self.shareBtn];
    [self.shareBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.titleLabel);
        make.right.mas_equalTo(-8);
        make.size.mas_equalTo(CGSizeMake(34, 44)); // 8 18 8
    }];
    [self.navigationBarView addSubview:self.collectBtn];
    [self.collectBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.titleLabel);
        make.right.equalTo(self.shareBtn.mas_left);
        make.size.mas_equalTo(CGSizeMake(34, 44));
    }];
    [self.navigationBarView addSubview:self.backBtn];
    [self.backBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.titleLabel);
        make.left.mas_equalTo(0);
        make.size.mas_equalTo(CGSizeMake(50, 44)); // 16 18 16
    }];
    
    [self.mrkContentView addSubview:self.bottomVue];
    [self.bottomVue mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.left.right.equalTo(self.mrkContentView);
        make.height.mas_equalTo(WKDHPX(74)+kSafeArea_Bottom);
    }];
    
    [self.mrkContentView addSubview:self.shareView];
    [self.shareView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.centerY.equalTo(self.mrkContentView);
        make.width.mas_equalTo(RealScreenWidth);
    }];
}

#pragma mark - lazy init
- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style: UITableViewStyleGrouped];
        _tableView.backgroundColor = [UIColor whiteColor];
        _tableView.delegate = self;
        _tableView.dataSource = self;
        _tableView.estimatedRowHeight = 100;
        _tableView.showsVerticalScrollIndicator = NO;
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    }
    return _tableView;
}

- (MRKCourseDetailTopView *)topView {
    if (!_topView) {
        CGFloat height = floor(RealScreenWidth *0.75);
        _topView = [[MRKCourseDetailTopView alloc] initWithFrame:CGRectMake(0, 0, RealScreenWidth, height)]; // 12 是底部白边 323
    }
    return _topView;
}

- (UIView *)navigationBarView {
    if (!_navigationBarView) {
        _navigationBarView = [[UIView alloc] init];
        _navigationBarView.backgroundColor = UIColorHexAlpha(#4D5361, 0);
        _navigationBarView.clipsToBounds = YES;
    }
    return _navigationBarView;
}

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [UILabel new];
        _titleLabel.font = kMedium_Font_NoDHPX(18);
        _titleLabel.textAlignment = NSTextAlignmentCenter;
        _titleLabel.textColor = [UIColor whiteColor];
        _titleLabel.alpha = 0;
    }
    return _titleLabel;
}

- (UIButton *)backBtn {
    if (!_backBtn) {
        _backBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_backBtn setImage:[UIImage imageNamed:@"icon_back"] forState:UIControlStateNormal];
        [_backBtn addTarget:self action:@selector(backAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _backBtn;
}

- (UIButton *)collectBtn {
    if (!_collectBtn) {
        _collectBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_collectBtn setImage:[UIImage imageNamed:@"icon_collect_o"] forState:UIControlStateNormal];
        [_collectBtn setImage:[UIImage imageNamed:@"icon_collect"] forState:UIControlStateSelected];
        [_collectBtn addTarget:self action:@selector(collectAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _collectBtn;
}

- (UIButton *)shareBtn {
    if (!_shareBtn) {
        _shareBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_shareBtn setImage:[UIImage imageNamed:@"icon_share"] forState:UIControlStateNormal];
        [_shareBtn addTarget:self action:@selector(shareAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _shareBtn;
}

- (UIView *)rankHeaderView {
    if (!_rankHeaderView) {
        _rankHeaderView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, RealScreenWidth, WKDHPX(50))];
        
        UILabel *title = [UILabel new];
        title.text = @"排行榜";
        title.font = kMedium_Font_NoDHPX(WKDHPX(17));
        title.textColor = UIColorHex(363A44);
        [_rankHeaderView addSubview:title];
        [title mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(_rankHeaderView).offset(WKDHPX(16));
            make.top.equalTo(_rankHeaderView);
        }];
        
        UIButton *questionButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [questionButton setImage:[[UIImage imageNamed:@"icon_answer"] imageByResizeToSize:CGSizeMake(16, 16)] forState:UIControlStateNormal];
        [questionButton addTarget:self action:@selector(questionAction:) forControlEvents:UIControlEventTouchUpInside];
        [_rankHeaderView addSubview:questionButton];
        [questionButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(_rankHeaderView).offset(WKDHPX(50));
            make.centerY.equalTo(title);
            make.width.mas_equalTo(WKDHPX(55));
        }];
    }
    return _rankHeaderView;
}

- (MRKCourseDetailTableFooterView *)tableFooterView{
    if (!_tableFooterView) {
        _tableFooterView = [[MRKCourseDetailTableFooterView alloc] init];
        @weakify(self);
        _tableFooterView.selectBlock = ^{
            @strongify(self);
            CourseTipController *tipVC = [[CourseTipController alloc] init];
            tipVC.hidesBottomBarWhenPushed = YES;
            [self.navigationController pushViewController:tipVC animated:YES];
            
            [[MRKTraceManager sharedInstance] manualUploadTraceType:2 pageTitle:self.title pageId:self.tracePageId eventId:@"btn_Coursedetails_view_coursenotes" route:self.tracePageRoute duration:0 extendPara:@{}];
        };
    }
    return _tableFooterView;
}

- (MRKCourseDetailShareView *)shareView {
    if (!_shareView) {
        _shareView = [[MRKCourseDetailShareView alloc] init];
        _shareView.hidden = YES;
    }
    return _shareView;
}

- (UIView *)bottomVue {
    if (!_bottomVue) {
        _bottomVue = [MRKCourseDetailBottomVue new];
        _bottomVue.backgroundColor = [UIColor whiteColor];
        _bottomVue.hidden = YES;
        @weakify(self);
        _bottomVue.buttonBlock = ^{
            @strongify(self);
            [self startAction];
        };
    }
    return _bottomVue;
}

- (UIStatusBarStyle)navControllerStatusBarStyle:(MRKBaseController *)viewController {
    return UIStatusBarStyleLightContent;
}

@end
