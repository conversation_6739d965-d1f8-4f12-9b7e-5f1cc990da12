//
//  ExerciseListViewController.m
//  Student_IOS
//
//  Created by MacPro on 2021/6/16.
//

#import "ExerciseListViewController.h"
#import "TrainPlanTableViewCell.h"
#import "ReserveCourseTableCell.h"
#import "MRKAIPlanRecordCell.h"
#import "PageViewModel.h"
#import "MRKCoursePlanModel.h"
#import "MRKTrainingPlanDetailVC.h"
#import "MJDIYHeader.h"
#import "MJDIYAutoFooter.h"
#import "MRKUltraTrainCourseDetailVC.h"
#import "MRKCourseListModel.h"
#import "MRKAIPlanRecordModel.h"
#import "MRKAIPlanLogic.h"


@interface ExerciseListViewController ()<UITableViewDelegate>
@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) DataSourceModel *viewModel;
@property (nonatomic, strong) NSMutableArray *dataArray;
@property (nonatomic, assign) int page;
@property (nonatomic, strong) PageViewModel *pageModel;
@end

@implementation ExerciseListViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = [UIColor whiteColor];
    
    self.page = 1;
    self.dataArray = [NSMutableArray array];
    [self setUI];
}

- (void)setUI {
    self.tableView.contentInset = UIEdgeInsetsMake(0, 0, SafeAreaBottom, 0);
    self.tableView.backgroundColor = [UIColor colorWithHexString:@"#F8F8FA"];
    [self.view addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view.mas_top).offset(0);
        make.left.right.bottom.equalTo(@0);
    }];
   
    
    NSString *cellID = [self registerCell];
    self.viewModel = [[DataSourceModel alloc] initWithCellID:cellID
                                          configureCellBlock:^(BaseTableViewCell *cell, id  _Nonnull item, NSIndexPath * _Nonnull indexPath) {
        cell.selectionStyle = UITableViewCellSelectionStyleNone;
        [cell configWithItem:self.dataArray[indexPath.row]];
    }];
    
    
    self.tableView.dataSource = self.viewModel;
    RAC(self.viewModel,dataSource) = RACObserve(self, dataArray);
    
    
    self.pageModel = [PageViewModel new];
    RAC(self.pageModel, page) = RACObserve(self, page);
    
    
    @weakify(self);
    MrkEmptyView *emptyView = [MrkEmptyView emptyViewWithImage:[UIImage imageNamed:@"icon_notes_holder"]
                                                      titleStr:@""
                                                     detailStr:@"暂无数据"];
    emptyView.fullCoverSuperView = YES;
    emptyView.tapEmptyViewBlock = ^{
        [self_weak_.tableView.mj_header beginRefreshing];
    };
    emptyView.errorBtnClickBlock = ^{
        [self_weak_.tableView.mj_header beginRefreshing];
    };
    self.tableView.pageEmptyView = emptyView;
    
    
    MJDIYHeader *header = [MJDIYHeader headerWithRefreshingBlock:^{
        self_weak_.page = 1;
        [self_weak_ requestData];
    }];
    header.automaticallyChangeAlpha = YES;
    self.tableView.mj_header = header;
    
    MJDIYAutoFooter *footer = [MJDIYAutoFooter footerWithRefreshingBlock:^{
        self_weak_.page++;
        [self_weak_ requestData];
    }];
    footer.automaticallyChangeAlpha = YES;
    self.tableView.mj_footer = footer;
    
    ///request data
    [self requestData];
}

- (void)requestData {
    RACSignal *dataSingal;
    switch (self.index.intValue) {
        case 0: {
            ///AI计划
            dataSingal = [self.pageModel trainingAIPlanData];
        }break;
            
        case 1: {
            ///常规计划
            dataSingal = [self.pageModel trainingPlanData];
        } break;
            
        case 2: {
            ///课程
            dataSingal = [self.pageModel trainingCourseData:@"1"];
        } break;
            
        case 3: {
            ///动作
            dataSingal = [self.pageModel trainingCourseData:@"2"];
        } break;
            
        default:
            break;
    }
    
    @weakify(self);
    [dataSingal subscribeNext:^(id x) {
        @strongify(self);
        [self endRefresh];
        
        if (self.page == 1) {
            [self.dataArray removeAllObjects];
        }
        
        NSArray *array = [NSArray modelArrayWithClass:self.registerCellClass json:[x objectForKey:@"records"]];
        ///课程和动作model设置type
        if (self.index.intValue == 2 || self.index.intValue == 3){
            for (MRKCourseListModel *m in array) {
                m.type = @(self.index.intValue - 1).stringValue;
            }
        }
       
        [self.dataArray addObjectsFromArray:array];
        [self.tableView reloadData];
        
        NSInteger total = [[x objectForKey:@"total"] intValue];
        self.tableView.mj_footer.hidden = self.dataArray.count == total;
        
        [self.tableView hiddenEmptyView:self.dataArray.count > 0];
    } error:^(NSError *error) {
        @strongify(self);
        [self endRefresh];
        
        if ([self.tableView tableViewIsEmptyData]){
            [self.tableView mrkShowNetworkErrorEmptyView];
        }
    }];
}

- (void)endRefresh{
    if (self.tableView.mj_header.isRefreshing) {
        [self.tableView.mj_header endRefreshing];
    }
    
    if (self.tableView.mj_footer.isRefreshing) {
        [self.tableView.mj_footer endRefreshing];
    }
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    
    switch (self.index.intValue) {
        case 0:{
            ///参与的AI计划
            ///目前状态： 1-未开始；2-进行中；3-请假中；4-已完成；5-中途结束   6-未完成
            MRKAIPlanRecordModel *model = self.dataArray[indexPath.row];
            switch (model.status) {
                case 4:  case 5:  case 6:///报告
                    [MRKAIPlanLogic.shared jumpToAIReport:NO planId:model.cid version:model.version];
                    break;
                default:
                    ///详情
                    [MRKAIPlanLogic.shared jumpToAIPlanDetailPage:model.cid version:model.version isNewUserLink:NO];
                    break;
            }
        } break;
        case 1:{
            ///参与的计划
            MRKTrainingPlanDetailVC *vc = [MRKTrainingPlanDetailVC new];
            MRKCoursePlanModel *model = self.dataArray[indexPath.row];
            vc.model = model;
            [self.navigationController pushViewController:vc animated:YES];
        } break;
        case 2:{
            ///练过的课程
            MRKCourseListModel *model = self.dataArray[indexPath.row];
            if (model.courseChannel.intValue == 3) {
                MRKUltraTrainCourseDetailVC *vc = [[MRKUltraTrainCourseDetailVC alloc] init];
                vc.courseId = model.cid;
                [self.navigationController pushViewController:vc animated:YES];
            }else {
                [[RouteManager sharedInstance] jumpToCourseDetailWithId:model.cid];
            }
        } break;
        case 3:{
            ///动作详情
            MRKCourseListModel *model = self.dataArray[indexPath.row];
            [[RouteManager sharedInstance] jumpToMotionDetailWithId:model.cid trainTarget:@""];
        } break;
        default:
            break;
    }
}


#pragma mark - lazy
- (UITableView *)tableView {
    if (!_tableView) {
        NSString *cellID = [self registerCell];
        _tableView = [[UITableView alloc] initWithFrame:self.view.bounds style:UITableViewStylePlain];
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.backgroundColor = [UIColor clearColor];
        _tableView.estimatedRowHeight = self.rowHeight;
        _tableView.delegate = self;
        [_tableView registerClass:NSClassFromString(cellID) forCellReuseIdentifier:cellID];
    }
    return  _tableView;
}

- (CGFloat)rowHeight {
    switch (self.index.intValue) {
        case 0:
            return WKDHPX(140);
            break;
        case 1:
            return WKDHPX(254);
            break;
        case 2:
            return WKDHPX(106);
            break;
        case 3:
            return WKDHPX(87);
            break;
        default:
            break;
    }
    return 0;
}

- (NSString *)registerCell {
    switch (self.index.intValue) {
        case 0:
            return NSStringFromClass([MRKAIPlanRecordCell class]);
            break;
        case 1:
            return NSStringFromClass([TrainPlanTableViewCell class]);
            break;
        case 2:
            return NSStringFromClass([ReserveCourseTableCell class]);
            break;
        case 3:
            return NSStringFromClass([ReserveCourseTableCell class]);
            break;
        default:
            break;
    }
    return NSStringFromClass([UITableViewCell class]);
}

- (Class)registerCellClass {
    switch (self.index.intValue) {
        case 0:
            return [MRKAIPlanRecordModel class];
            break;
        case 1:
            return [MRKCoursePlanModel class];
            break;
        case 2:
            return [MRKCourseListModel  class];
            break;
        case 3:
            return [MRKCourseListModel  class];
            break;
        default:
            break;
    }
    return nil;
}

#pragma mark - JXCategoryListContentViewDelegate

/**
 实现 <JXCategoryListContentViewDelegate> 协议方法，返回该视图控制器所拥有的「视图」
 */
- (UIView *)listView {
    return self.view;
}
@end
