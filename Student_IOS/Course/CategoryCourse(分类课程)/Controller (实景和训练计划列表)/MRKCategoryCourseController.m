//
//  MRKCategoryCourseController.m
//  Student_IOS
//
//  Created by merit on 2023/3/25.
//

#import "MRKCategoryCourseController.h"
#import "MRKCategoryCourseLiveController.h"
#import "MRKCategoryCoursePlanController.h"
#import "InstrumentModel.h"

@interface MRKCategoryCourseController ()
@property (nonatomic, strong) NSMutableArray<InstrumentModel *> *dataArray;
@end

@implementation MRKCategoryCourseController
- (BOOL)viewControllerIsNeedNavBar:(MRKBaseController *)viewController {
    return YES;
}
-(UIImage *)mrkNavigationBarRightButtonImage:(UIButton *)rightButton navigationBar:(MRKNavigationBar *)navigationBar {
    rightButton.contentHorizontalAlignment = UIControlContentHorizontalAlignmentRight;
        return [UIImage imageNamed:@"icon_share_B"];
}
- (void)rightButtonEvent:(UIButton *)sender navigationBar:(MRKNavigationBar *)navigationBar {
    switch (self.type) {
        case MRKCategoryCourseTypeLive:
        {
            UIViewController *vc = [self getSupreViewController];
            [MRKShareManager shareLinkWithJumpType:@"real_scene_list" params:@{} controller:vc?:self completion:^{}];
        }
            break;
        case MRKCategoryCourseTypePlan:
        {
            UIViewController *vc = [self getSupreViewController];
            [MRKShareManager shareLinkWithJumpType:@"referral_plan" params:@{} controller:vc?:self completion:^{}];
        }
            break;
        default:
            break;
    }
}

- (void)viewDidLoad {
    if (self.type == MRKCategoryCourseTypeLive) { // 实景视频
        self.tracePageId = @"page_LiveVideoCourses";
    } else if (self.type == MRKCategoryCourseTypePlan) { // 训练计划
        self.tracePageId = @"page_RecommendPlan";
    }
    if([self.productID isNotEmpty]){
        self.tracePara = @{ @"product_id": self.productID };
    }
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    
    
    
    @weakify(self);
    MrkEmptyView *emptyView = [[MrkEmptyView alloc] init];
    emptyView.fullCoverSuperView = YES;
    emptyView.tapEmptyViewBlock = ^{
        [self_weak_ requestTitles];
    };
    emptyView.errorBtnClickBlock = ^{
        [self_weak_ requestTitles];
    };
    self.view.pageEmptyView = emptyView;
    
    [self initUI];
    [self initData];
}

- (void)initUI {
    self.categoryView.titleSelectedColor = [UIColor colorWithHexString:@"#363A44"];
    self.categoryView.titleColor = [UIColor colorWithHexString:@"#848A9B"];
    self.categoryView.titleSelectedFont = [UIFont fontWithName:fontNameMeDium size:WKDHPX(15)];
    self.categoryView.titleFont = [UIFont fontWithName:fontNamePing size:WKDHPX(15)];
    self.categoryView.titleLabelZoomEnabled = NO;
    self.categoryView.titleLabelStrokeWidthEnabled = NO;
    JXCategoryIndicatorLineView *lineView = [[JXCategoryIndicatorLineView alloc] init];
    lineView.indicatorColor = [UIColor colorWithHexString:@"#16D2E3"];
    lineView.indicatorWidth = 20;
    lineView.indicatorHeight = 4;
    self.categoryView.indicators = @[lineView];
    
    [self.mrkContentView addSubview:self.categoryView];
    [self.categoryView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(kNavBarHeight);
        make.left.right.mas_equalTo(0);
        make.height.mas_equalTo(WKDHPX(50));
    }];
    
    [self.mrkContentView addSubview:self.listContainerView];
    [self.listContainerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.categoryView.mas_bottom);
        make.left.right.bottom.mas_equalTo(0);
    }];
    
    // 关联到 categoryView
    self.categoryView.listContainer = self.listContainerView;
    
    switch (self.type) {
        case MRKCategoryCourseTypeLive:
            self.navTitle = @"实景挑战";
            break;
        case MRKCategoryCourseTypePlan:
            self.navTitle = @"训练计划";
            break;
        default:
            break;
    }
}

- (void)initData {
    [self requestTitles];
}

- (void)refreshUI {
    InstrumentModel *allModel = [[InstrumentModel alloc] init];
    allModel.typeName = @"全部";
    [self.dataArray insertObject:allModel atIndex:0];
    
    NSMutableArray *titles = [NSMutableArray array];
    NSMutableArray *traceParas = [NSMutableArray array]; // 埋的是一个列表的数据
    int defaut = 0;
    for (int i = 0; i < self.dataArray.count; i++) {
        InstrumentModel *model = self.dataArray[i];
        [titles addObject:model.typeName];
        if ([self.productID isEqualToString:model.cid.stringValue]) {
            defaut = i;
        }
        if (model.cid != nil) {
            [traceParas addObject:@{@"product_id" : model.cid}];
        } else {
            [traceParas addObject:@{}];
        }
    }
    self.categoryView.titles = titles;
    if (self.type == MRKCategoryCourseTypeLive) { // 实景视频
        self.categoryView.traceEventId = @"btn_LiveVideoCourses_classify";
        self.categoryView.tracePara = @{@"trace_paras": traceParas};
    } else if (self.type == MRKCategoryCourseTypePlan) { // 训练计划
        self.categoryView.traceEventId = @"btn_RecommendPlan_classify";
        self.categoryView.tracePara = @{@"trace_paras": traceParas};
    }
    // 默认选中首页设备
    [self.categoryView setDefaultSelectedIndex:defaut];
    [self.categoryView reloadData];
    
}

#pragma mark - ----- 请求设备大类 -----
- (void)requestTitles {
    switch (self.type) {
        case MRKCategoryCourseTypeLive:
            [self requestLiveTitles];
            break;
        case MRKCategoryCourseTypePlan:
            [self requestPlanTitles];
            break;
        default:
            break;
    }
}

/// 实景视频
- (void)requestLiveTitles {
    [self.view beginLoading];
    [MRKBaseRequest mrkGetRequestUrl:@"/course/LiveVideoController/equipNameList"
                             andParm:@{}
            completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        [self.view endLoading];
        [self.view hiddenEmptyView];
        
        id data = [request.responseObject valueForKeyPath:@"data"];
        NSArray *models = [NSArray modelArrayWithClass:[InstrumentModel class] json:data];
        self.dataArray = [NSMutableArray arrayWithArray:models];
        [self refreshUI];
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        [self.view endLoading];
        if (self.dataArray.count <= 0){
            [self.view mrkShowNetworkErrorEmptyView];
        }
    }];
}

/// 训练计划
- (void)requestPlanTitles {
    [self.view beginLoading];
    [MRKBaseRequest mrkGetRequestUrl:@"/course/courseTrainingPlanController/equipNameList"
                             andParm:@{}
            completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        [self.view endLoading];
        [self.view hiddenEmptyView];
        
        id data = [request.responseObject valueForKeyPath:@"data"];
        NSArray *models = [NSArray modelArrayWithClass:[InstrumentModel class] json:data];
        self.dataArray = [NSMutableArray arrayWithArray:models];
        [self refreshUI];
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        [self.view endLoading];
        if (self.dataArray.count <= 0){
            [self.view mrkShowNetworkErrorEmptyView];
        }
    }];
}

#pragma mark - JXCategoryListContainerViewDelegate
- (id<JXCategoryListContentViewDelegate>)listContainerView:(JXCategoryListContainerView *)listContainerView initListForIndex:(NSInteger)index {
    switch (self.type) {
        case MRKCategoryCourseTypeLive:
        {
            MRKCategoryCourseLiveController *vc = [[MRKCategoryCourseLiveController alloc] init];
            InstrumentModel *model = self.dataArray[index];
            vc.equipTypeId = model.cid.stringValue;
            return vc;
        }
        case MRKCategoryCourseTypePlan: {
            MRKCategoryCoursePlanController *vc = [[MRKCategoryCoursePlanController alloc] init];
            InstrumentModel *model = self.dataArray[index];
            vc.equipTypeId = model.cid.stringValue;
            vc.isVip = self.isVip;
            return vc;
        }
        default:
            break;
    }
}

@end
