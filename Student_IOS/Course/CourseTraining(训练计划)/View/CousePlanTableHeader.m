//
//  CousePlanTableHeader.m
//  Student_IOS
//
//  Created by merit on 2021/8/18.
//

#import "CousePlanTableHeader.h"
#import "UIImage+Helper.h"
#import "MRKTabLabel.h"
#import "MRKPlanLabel.h"

@interface CousePlanTableHeader ()
@property (nonatomic, strong) UIView *backView;

@property (nonatomic, strong) UILabel *titleLab;
@property (nonatomic, strong) YYLabel *despLab;
@property (nonatomic, strong) MRKPlanLabel *tipLabel;
@property (nonatomic, strong) UIStackView *stackView;
@property (nonatomic, strong) UILabel *openBtn;
@end



@implementation CousePlanTableHeader

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.frame = CGRectMake(0, 0, kScreenWidth, DHPX(250));
        
        [self addSubview:self.tipLabel];
        [self.tipLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(self.mas_bottom).offset(-DHPX(20));
            make.left.mas_equalTo(DHPX(16));
            make.height.mas_equalTo(24);
        }];
        
        [self addSubview:self.stackView];
        [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.tipLabel.mas_right).offset(8);
            make.centerY.equalTo(self.tipLabel.mas_centerY);
            make.height.mas_equalTo(24);
        }];
        
        [self addSubview:self.despLab];
        [self.despLab mas_makeConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(self.tipLabel.mas_top).offset(-DHPX(15));
            make.left.equalTo(self.mas_left).offset(DHPX(16));
            make.right.equalTo(self.mas_right).offset(-DHPX(16));
        }];
        
        [self addSubview:self.titleLab];
        [self.titleLab mas_makeConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(self.despLab.mas_top).offset(-DHPX(10));
            make.left.equalTo(self.mas_left).offset(DHPX(16));
            make.right.equalTo(self.mas_right).offset(-DHPX(16));
        }];
    }
    return self;
}

- (void)layoutSubviews {
    [super layoutSubviews];
}

- (void)setModel:(MRKCourseDetailModel *)model{
    _model = model;
    self.tipLabel.cycletime = model.planningCycleName;
    self.tipLabel.describe = [NSString stringWithFormat:@"%@",model.difficultyName];;
    
    //布局标签
    NSMutableArray *array = [NSMutableArray array];
    for (NSDictionary *dic in model.courseTagList) {
        [array addObject:dic[@"name"]];
    }
    [self.stackView removeAllSubviews];
    for (int i = 0; i < array.count; i++) {
        MRKTabLabel *tabLabel = [[MRKTabLabel alloc] init];
        tabLabel.text = array[i];
        tabLabel.backgroundColor = [UIColor clearColor];
        tabLabel.textColor = [UIColor whiteColor];
        tabLabel.layer.cornerRadius = 12.0f;
        tabLabel.layer.masksToBounds = YES;
        tabLabel.layer.borderColor = [UIColor colorWithWhite:1 alpha:0.5].CGColor;
        tabLabel.layer.borderWidth = CGFloatFromPixel(1.0f);
        [self.stackView addArrangedSubview:tabLabel];
    }
    
    
//    self.despLab.text = model.subtitle;
    
    self.despLab.attributedText = ({
        NSMutableAttributedString *attachText = [[NSMutableAttributedString alloc] initWithString:model.subtitle?:@""];
        attachText.color = [UIColor whiteColor];
        attachText.font = [UIFont systemFontOfSize:14];;
        
        NSString *imageName = nil;
        NSInteger viptype = model.vipType.intValue;
        switch (viptype) {
            case 10:
                imageName = @"courseTag_vip";
                break;
            case 30:
                imageName = @"courseTag_enjoyvip";
                break;
            default:
                break;
        }
        
        if ([imageName isNotBlank]) {
            UIImage *newImage = [UIImage imageNamed:imageName];
            NSMutableAttributedString *ImageText = [NSMutableAttributedString attachmentStringWithContent:newImage
                                                                                              contentMode:UIViewContentModeCenter
                                                                                           attachmentSize:CGSizeMake(newImage.size.width +10, newImage.size.height)
                                                                                              alignToFont:[UIFont systemFontOfSize:14]
                                                                                                alignment:YYTextVerticalAlignmentCenter];
            ImageText.baseWritingDirection = NSWritingDirectionLeftToRight;
            ImageText.writingDirection = @[@(NSWritingDirectionLeftToRight | NSWritingDirectionOverride)];
//            [ImageText appendAttributedString:[[NSMutableAttributedString alloc] initWithString:@" "]];
            [attachText insertAttributedString:ImageText atIndex:0];
        }
        attachText.lineSpacing = 5;
        attachText;
    });

    
    
    
    self.titleLab.text = model.title;
}


- (UIImageView *)backImageView{
    if (!_backImageView) {
        _backImageView = [[UIImageView alloc] init];
        _backImageView.contentMode = UIViewContentModeScaleToFill;
    }
    return _backImageView;
}

- (UIView *)backView{
    if (!_backView) {
        _backView = [[UIView alloc] init];
        _backView.backgroundColor = UIColorHex(#F8F8FA);
    }
    return _backView;
}

- (UILabel *)titleLab{
    if (!_titleLab) {
        _titleLab = [[UILabel alloc] init];
        _titleLab.font = [UIFont systemFontOfSize:25 weight:UIFontWeightMedium];
        _titleLab.textColor = [UIColor whiteColor];
        _titleLab.text = @"训练计划名称";
    }
    return _titleLab;
}

- (YYLabel *)despLab{
    if (!_despLab) {
        _despLab = [[YYLabel alloc] init];
        _despLab.text = @"训练计划名称副标题";
        _despLab.textAlignment = NSTextAlignmentCenter;
        _despLab.textVerticalAlignment = YYTextVerticalAlignmentCenter;
        _despLab.numberOfLines = 0;
        _despLab.textColor = UIColorHex(#363A44);
        _despLab.font = [UIFont systemFontOfSize:14.0];
        _despLab.preferredMaxLayoutWidth = kScreenWidth - DHPX(16)*2;
    }
    return _despLab;
}

- (MRKPlanLabel *)tipLabel{
    if (!_tipLabel) {
        _tipLabel = [[MRKPlanLabel alloc] init];
        _tipLabel.type = PlanLabTypeWhite;
    }
    return _tipLabel;
}

#pragma mark - lazy load
- (UIStackView *)stackView {
    if (!_stackView) {
        _stackView = [[UIStackView alloc]init];
        _stackView.spacing = 8;
        _stackView.axis = UILayoutConstraintAxisHorizontal;
        _stackView.alignment = UIStackViewAlignmentFill;
        _stackView.distribution = UIStackViewDistributionFillProportionally;
    }
    return _stackView;
}

- (UILabel *)openBtn{
    if (!_openBtn) {
        _openBtn = [[UILabel alloc]init];
        _openBtn.textAlignment = NSTextAlignmentCenter;
        _openBtn.font = [UIFont fontWithName:fontNamePing size:12];
        _openBtn.textColor = [UIColor colorWithHexString:@"#4C5362"];
        _openBtn.layer.borderColor = [UIColor colorWithHexString:@"#D8DADE"].CGColor;
        _openBtn.layer.borderWidth = 1.0;
        _openBtn.layer.cornerRadius = 14;
        _openBtn.layer.masksToBounds = YES;
    }
    return _openBtn;
}

@end




