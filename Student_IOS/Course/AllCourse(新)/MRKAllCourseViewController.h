//
//  MRKAllCourseViewController.h
//  Student_IOS
//
//  Created by <PERSON><PERSON> on 2025/2/17.
//

#import <UIKit/UIKit.h>
#import "MRKBaseController.h"
#import "MRKCourseFilterModels.h"
#import "MRKCourseFilterModels.h"

NS_ASSUME_NONNULL_BEGIN

@interface MRKAllCourseViewController : MRKBaseController
@property (nonatomic, strong) NSString *equimentTypeID;
@property (nonatomic, strong) NSString *index;
@property (nonatomic, strong) MRKCourseConditionModel *conditionModel;
@property (nonatomic, strong) MRKConditionTagModel *tagModel;
@end

NS_ASSUME_NONNULL_END
