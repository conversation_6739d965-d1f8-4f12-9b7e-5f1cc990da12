//
//  MRKAllCourseViewController.m
//  Student_IOS
//
//  Created by <PERSON><PERSON> on 2025/2/17.
//

#import "MRKAllCourseViewController.h"
#import "UIView+LBExtension.h"
#import "JXCategoryView.h"
#import "JXCategoryListContainerView.h"
#import "MRKTrainedRecordListController.h"
#import "MRKAllCourseController.h"
#import "MRKAiPlanEditAlert.h"


@interface MRKAllCourseViewController ()<JXCategoryViewDelegate , JXCategoryListContainerViewDelegate>
@property (nonatomic, strong) NSArray *dataArray;
@property (nonatomic, strong) JXCategoryTitleView *categoryView;
@property (nonatomic, strong) JXCategoryListContainerView *listContainerView;
@end

@implementation MRKAllCourseViewController

- (BOOL)viewControllerIsNeedNavBar:(MRKBaseController *)viewController {
    return YES;
}
/**
 导航条中间的 View
 */
- (UIView *)mrkNavigationBarTitleView:(MRKNavigationBar *)navigationBar{
    return self.categoryView;
}

- (UIImage *)mrkNavigationBarRightButtonImage:(UIButton *)rightButton navigationBar:(MRKNavigationBar *)navigationBar {
    rightButton.contentHorizontalAlignment = UIControlContentHorizontalAlignmentRight;
    rightButton.size = CGSizeMake(WKDHPX(70), WKDHPX(30));
    return [UIImage imageNamed:@"ai_search_icon"];;
}

- (void)rightButtonEvent:(UIButton *)sender navigationBar:(MRKNavigationBar *)navigationBar {
    sender.traceEventId = @"btn_mia_search";
    [MRKAIPlanLogic.shared jumpToAIChatFirstTip];
}

- (void)viewDidLoad {
    self.tracePageId = @"page_profile_workout";
    [super viewDidLoad];
    self.mrkContentView.backgroundColor = [UIColor whiteColor];
  
    [self initCategoryView];
    [self reloadDatas];
    // Do any additional setup after loading the view.
}

- (void)reloadDatas {
    [self.view beginLoading];
    [MRKBaseRequest mrkSilenceGetRequestUrl:@"/course/motion/switch-status"
                                    andParm:nil
                   completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        [self.view endLoading];
        
        // 数据解析独立成方法
        id status = [request.responseObject valueForKeyPath:@"data"];
        self.dataArray = [status boolValue] ? @[
            @{@"title": @"课程", @"type": @"1"},
            @{@"title": @"动作库", @"type": @"2"}
        ] : @[@{@"title": @"课程", @"type": @"1"}];
        
        [self reloadCategoryView];
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        [self.view endLoading];
        self.dataArray = @[@{@"title": @"课程", @"type": @"1"}];
        [self reloadCategoryView];
    }];
}

/// 确保主线程操作
- (void)reloadCategoryView {
    dispatch_async(dispatch_get_main_queue(), ^{
        self.categoryView.titles = [self.dataArray valueForKey:@"title"];
        self.categoryView.defaultSelectedIndex = self.index.isEmpty ? 0 : [self.index integerValue];
        [self.categoryView reloadData];
    });
}


- (void)initCategoryView {
    self.categoryView.delegate = self;
//    [self.mrk_navgationBar addSubview:self.categoryView];
//    [self.categoryView mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.bottom.equalTo(self.mrk_navgationBar.mas_bottom);
//        make.width.mas_equalTo(200);
//        make.height.mas_equalTo(44);
//    }];
//    self.mrk_navgationBar.titleView = self.categoryView;
    
    [self.mrkContentView addSubview:self.listContainerView];
    [self.listContainerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.mrk_navgationBar.mas_bottom);
        make.left.right.equalTo(self.mrkContentView);
        make.bottom.mas_equalTo(self.mrkContentView.mas_bottom);
    }];
    
    self.categoryView.listContainer = self.listContainerView;
    self.categoryView.contentScrollView.scrollEnabled = NO;
}


#pragma mark - Public
- (CGFloat)preferredCategoryViewHeight {
    return 44;
}

#pragma mark - JXCategoryViewDelegate

///点击选中或者滚动选中都会调用该方法。适用于只关心选中事件，不关心具体是点击还是滚动选中的。
- (void)categoryView:(JXCategoryBaseView *)categoryView didSelectedItemAtIndex:(NSInteger)index {
    NSLog(@"%@", NSStringFromSelector(_cmd));
    NSLog(@"JXCategoryBaseView__didSelectedItemAtIndex__%ld" , index);
    if (index == 1){
        ReportMrkLogParms(2, @"点击动作导航", @"page_action", @"button_action_list", nil, 0, nil);
    }
}

///滚动选中的情况才会调用该方法
- (void)categoryView:(JXCategoryBaseView *)categoryView didScrollSelectedItemAtIndex:(NSInteger)index {
    NSLog(@"%@", NSStringFromSelector(_cmd));
}

#pragma mark - JXCategoryListContainerViewDelegate

///返回列表的数量
- (NSInteger)numberOfListsInlistContainerView:(JXCategoryListContainerView *)listContainerView {
    return self.dataArray.count;
}

///返回各个列表菜单下的实例，该实例需要遵守并实现 <JXCategoryListContentViewDelegate> 协议
- (id<JXCategoryListContentViewDelegate>)listContainerView:(JXCategoryListContainerView *)listContainerView initListForIndex:(NSInteger)index {
    NSLog(@"JXCategoryListContentViewDelegate_initListForIndex---%ld",(long)index);
    
    if (index == 0) {
        MRKAllCourseController *vc = [[MRKAllCourseController alloc] init];
        vc.equimentTypeID = self.equimentTypeID;
        vc.conditionModel = self.conditionModel;
        vc.tagModel = self.tagModel;
        return vc;
    }else{
        MRKFlutterMotionListController *vc = [[MRKFlutterMotionListController alloc] init];
        return vc;
    }
}


#pragma mark - lazy
/// 分页菜单视图
- (JXCategoryTitleView *)categoryView {
    if(!_categoryView) {
        _categoryView = [[JXCategoryTitleView alloc] init];
        _categoryView.backgroundColor = [UIColor whiteColor];
        _categoryView.titleSelectedColor = [UIColor colorWithHexString:@"#4C5362"];
        _categoryView.titleColor = [UIColor colorWithHexString:@"#666666"];
        _categoryView.titleFont = [UIFont fontWithName:fontNamePing size:14.0];
        _categoryView.titleSelectedFont = [UIFont fontWithName:fontNameMeDium size:16.0];
        _categoryView.delegate = self;
        _categoryView.titleColorGradientEnabled = YES;
        _categoryView.titleLabelZoomEnabled = YES;
        _categoryView.titleLabelZoomScale = 1.10;
        _categoryView.titleLabelStrokeWidthEnabled = YES;
        _categoryView.indicators = @[self.lineView];
        _categoryView.size = CGSizeMake(kScreenWidth - 100, 44);
    }
    return  _categoryView;
}

///列表容器视图
- (JXCategoryListContainerView *)listContainerView {
    if (!_listContainerView) {
        _listContainerView = [[JXCategoryListContainerView alloc] initWithType:JXCategoryListContainerType_ScrollView delegate:self];
    }
    return _listContainerView;
}

///
- (JXCategoryIndicatorLineView *)lineView{
    JXCategoryIndicatorLineView *lineView = [[JXCategoryIndicatorLineView alloc] init];
    lineView.indicatorColor = [UIColor colorWithHexString:@"#16D2E3"];
    lineView.indicatorWidth = 20;
    lineView.indicatorHeight = 4;
    lineView.scrollStyle = JXCategoryIndicatorScrollStyleSameAsUserScroll;
    lineView.verticalMargin = 4;
    return lineView;
}
@end




