//
//  MRKAllCourseController.m
//  Student_IOS
//
//  Created by Junq on 2024/6/24.
//

#import "MRKAllCourseController.h"
#import "MJDIYHeader.h"
#import "MRKAllCourseFilterView.h"
#import "MRKAllCourseFilterAlert.h"

#import "MRKCourseCells.h"
#import "MRKCourseViews.h"
#import "MRKCourseTagsAlertView.h"
#import "MRKCourseDetailController.h"
#import "MRKCourseFilterViewModel.h"
#import "MRKCourseSearchController.h"


@interface MRKAllCourseController ()<UITableViewDelegate, UITableViewDataSource>
//课程筛选标签 （都是操作这个数据里面的数据，包括选中，非选中, 弹窗展示tag等）
@property (nonatomic, strong) NSMutableArray<MRKCourseConditionModel *> *conditionsArray;
@property (nonatomic, strong) MRKAllCourseFilterView *fliterView;
@property (nonatomic, strong) MRKAllCourseFilterAlert *fliterAlertView;
@property (nonatomic, assign) NSInteger page;
@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) NSMutableArray *dataArray;
@property (nonatomic, strong) CourseFilterListAPI *listApi;
@property (nonatomic, copy) NSString *categoryID;  ///分类id
@property (nonatomic, copy) NSString *type;        ///1最新 2最热
@property (nonatomic, assign) CGFloat paddingTop;
@end

@implementation MRKAllCourseController
- (void)dealloc {
     NSLog(@"😊😊😊-----dealloc-----😊😊😊%@" , NSStringFromClass([self class]));
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    [self.fliterAlertView tapHidden];
}

- (void)listViewDidAppear {
    // 滚动到最上面
    [self.tableView scrollToTopAnimated:NO];
    // 请求数据
    [self requestFirstData];
}

- (void)viewDidLoad {
    self.tracePageId = @"page_AllCourses";
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    
    [self UiLayoutView];
    [self initData];
}

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    CGFloat contentOffsetY = scrollView.contentOffset.y;
    if (contentOffsetY <= 0) {
        [self.fliterView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.mrkContentView.mas_top).offset(0);
        }];
    }else{
        
        if (contentOffsetY <= WKDHPX(52)) {
            self.paddingTop = WKDHPX(52)+WKDHPX(44)+0 -contentOffsetY;
        }else{
            self.paddingTop = 0 + WKDHPX(44);
        }
        
        float topPadding = 0 - MIN(contentOffsetY, WKDHPX(52));
        [self.fliterView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.mrkContentView.mas_top).offset(topPadding);
        }];
    }
}

- (void)UiLayoutView{
    self.navTitle = @"全部课程";
    self.mrkContentView.backgroundColor = UIColorHex(#F7F7FA);
    self.paddingTop = WKDHPX(52)+WKDHPX(44)+0;
    
    self.tableView.contentInset = UIEdgeInsetsMake(WKDHPX(52), 0, WKDHPX(16), 0);
    [self.mrkContentView addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.mrkContentView.mas_top).offset(0+WKDHPX(44));
        make.left.right.equalTo(self.mrkContentView);
        make.bottom.equalTo(self.mrkContentView.mas_bottom).offset(0);
    }];
  
    [self.mrkContentView addSubview:self.fliterView];
    [self.fliterView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.mrkContentView.mas_top).offset(0);
        make.left.right.equalTo(self.mrkContentView);
        make.height.mas_equalTo(WKDHPX(52)+WKDHPX(44));
    }];

    @weakify(self);
    MrkEmptyView *emptyView = [MrkEmptyView emptyViewWithImage:[UIImage imageNamed:@"icon_search_holder"]
                                                      titleStr:@""
                                                     detailStr:@"没有匹配的结果"];
    emptyView.errorBtnClickBlock = ^{
        [self_weak_.tableView.mj_header beginRefreshing];
    };
    self.tableView.pageEmptyView = emptyView;
    
    [self initRefresh];
}

- (void)initRefresh {
    @weakify(self);
    MJDIYHeader *header = [MJDIYHeader headerWithRefreshingBlock:^{
        @strongify(self);
        [self requestFirstData];
    }];
    header.automaticallyChangeAlpha = YES;
    self.tableView.mj_header = header;
    
    MJDIYAutoFooter *footer = [MJDIYAutoFooter footerWithRefreshingBlock:^{
        @strongify(self);
        self.page ++;
        [self requestData];
    }];
    footer.automaticallyChangeAlpha = YES;
    self.tableView.mj_footer = footer;
}


- (void)initData {
    // 填充刷选头数据
    self.dataArray = [NSMutableArray array];
   
    [self.view beginLayoutLoading];
    [self requestFirstData];
}

///  请求第一页数据
- (void)requestFirstData {
    self.page = 1;
    [self requestData];
}

/// 请求数据
- (void)requestData {
    
    if (self.conditionsArray.count == 0){
        
        [MRKBaseRequest mrkGetRequestUrl:@"/course/search/course_condition/v2"
                                 andParm:nil
                completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
            id conditions = [request.responseObject valueForKeyPath:@"data.conditions"];
            NSArray *tags = [NSArray modelArrayWithClass:[MRKCourseConditionModel class] json:conditions];
            self.conditionsArray = tags.mutableCopy;
            
            { ///补全4.4.0.0版本新增排序数据
                MRKConditionTagModel *mod1 = [[MRKConditionTagModel alloc] init];
                mod1.cid = @"1";
                mod1.name = @"最新";
                mod1.avatar = @"";
                mod1.remark = @"";
                mod1.isSelect = NO;
                mod1.isSelectConfirm = NO;
                mod1.ignoreConditions = @[];
                
                MRKConditionTagModel *mod2 = [[MRKConditionTagModel alloc] init];
                mod2.cid = @"2";
                mod2.name = @"最热";
                mod2.avatar = @"";
                mod2.remark = @"";
                mod2.isSelect = NO;
                mod2.isSelectConfirm = NO;
                mod2.ignoreConditions = @[];
                
                MRKCourseConditionModel *model = [[MRKCourseConditionModel alloc] init];
                model.key = @"filter";
                model.name = @"排序";
                model.options = @[
                    mod1, mod2
                ];
            
                [self.conditionsArray addObject:model];
            }
            
            if (self.conditionModel != nil)
            {
                for (MRKCourseConditionModel *m in self.conditionsArray) {
                    if ([m.key isEqualToString:self.conditionModel.key]) {
                        for (MRKConditionTagModel *t in m.options) {
                            if ([t.name isEqualToString:self.tagModel.name]) {
                                t.isSelect = YES;
                                t.isSelectConfirm = YES;
                            }
                        }
                    }
                }
            }
            
            if (self.equimentTypeID != nil)
            {
                for (MRKCourseConditionModel *m in self.conditionsArray) {
                    if ([m.key isEqualToString:@"product"]) {
                        for (MRKConditionTagModel *t in m.options) {
                            if ([t.cid isEqualToString:self.equimentTypeID]) {
                                t.isSelect = YES;
                                t.isSelectConfirm = YES;
                            }
                        }
                    }
                }
            }
            
            [self.fliterView setHeadFilterDetail:self.conditionsArray];
            [self requestData];
        } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
            [self.tableView hiddenEmptyView:NO];
        }];
        return;
    }
    
    
    NSMutableDictionary *tagDic = [NSMutableDictionary dictionary];
    NSMutableDictionary *nameDic = [NSMutableDictionary dictionary];
    for (MRKCourseConditionModel *model in self.conditionsArray) {
        NSArray *selectArr = [model selectOptions];
        
        NSMutableArray *idArray = [NSMutableArray array];
        NSMutableArray *nameArray = [NSMutableArray array];
        for (MRKConditionTagModel *tag in selectArr) {
            [idArray addObject:tag.cid];
            [nameArray addObject:tag.name];
        }
        [tagDic setValue:idArray forKey:model.key];
        [nameDic setValue:nameArray forKey:model.key];
    }
    
    /// 刷新title
    [self.fliterView resetButtonTitles:nameDic];
    
    /// 把排序挑选出来
    NSString *type = nil;
    if ([tagDic.allKeys containsObject:@"filter"]){
        NSArray *arr = tagDic[@"filter"];
        if (arr.count > 0) {
            type = arr.firstObject;
        }
        [tagDic removeObjectForKey:@"filter"];
    }
    self.type = type?:@"1";
    
    /// 把分类挑选出来
    if ([tagDic.allKeys containsObject:@"courseCategory"]){
        NSArray *arr = tagDic[@"courseCategory"];
        if (arr.count > 0) {
            self.categoryID = arr.firstObject;
        } else {
            self.categoryID = @"";
        }
        [tagDic removeObjectForKey:@"courseCategory"];
    }
    
    /// 停止上一次请求
    if (self.listApi) {
        [self.listApi stop];
    }
 
    /// 请求数据
    @weakify(self);
    self.listApi = [MRKCourseFilterViewModel requestCourseList:@""
                                                          type:self.type
                                                      category:self.categoryID
                                                          tags:tagDic
                                                          page:self.page
                                                          size:10
                                                       success:^(MRKCourseFilterPageModel * _Nonnull pageModel) {
        @strongify(self);
        [self.view endLoading];
        [self.tableView endRefresh];
        [self.tableView hiddenEmptyView];
        
        if (self.page == 1) {
            [self.tableView scrollToTopAnimated:NO];
            [self.dataArray removeAllObjects];
        }
        
        [self.dataArray addObjectsFromArray:pageModel.records];
        self.tableView.mj_footer.hidden = self.dataArray.count >= pageModel.total.intValue;
        [self.tableView reloadData];
        
        /// 暂无数据
        BOOL haveData = self.dataArray.count > 0;
        [self.tableView hiddenEmptyView:haveData];
        
        if (!haveData) {
            [MBProgressHUD showMessage:@"筛选无结果，换一个试试吧"];
        }
    } failure:^(NSError * _Nonnull error) {
        [self.tableView endRefresh];
        
        if ([self.tableView tableViewIsEmptyData]){
            [self.tableView mrkShowNetworkErrorEmptyView];
        }
    }];
}

#pragma mark -  ------ 刷新按钮状态 ------
- (void)refreshButtonTitles {
    NSMutableDictionary *nameDic = [NSMutableDictionary dictionary];
    for (MRKCourseConditionModel *model in self.conditionsArray) {
        NSArray *selectArr = [model showOptions];
        NSMutableArray *nameArray = [NSMutableArray array];
        for (MRKConditionTagModel *tag in selectArr) {
            [nameArray addObject:tag.name];
        }
        [nameDic setValue:nameArray forKey:model.key];
    }
    // 刷新title
    [self.fliterView resetButtonTitles:nameDic];
}

#pragma mark -  ------ 弹窗操作 ------
- (void)selectHeadTag:(NSString *)tag select:(BOOL)select{
    if (select) {
        [self refreshButtonTitles];
        
        MRKCourseConditionModel *model = [self findCurrentKeyModel:tag];
        self.fliterAlertView.dataArray = @[model];
        [self.fliterAlertView showInView:self.mrkContentView topMargin:self.paddingTop];
    } else {
        [self.fliterAlertView tapHidden];
    }
}

- (void)moreTagSelect:(BOOL)select {
    if (select) {
        [self refreshButtonTitles];
   
        ///更多按钮点击， 只展示vio和coach
        NSMutableArray *array = [NSMutableArray array];
        [self.conditionsArray enumerateObjectsUsingBlock:^(MRKCourseConditionModel * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
            if ([obj.key isEqualToString:@"isVip"] ||
                [obj.key isEqualToString:@"filter"] ||
                [obj.key isEqualToString:@"coach"]){
                [array addObject:obj];
            }
        }];
        self.fliterAlertView.dataArray = array;
        [self.fliterAlertView showInView:self.mrkContentView topMargin:self.paddingTop];
    }else {
        [self.fliterAlertView tapHidden];
    }
}

#pragma mark - ------ 标签数据操作 ------
/// 每次出现，都还原tag
- (void)resetTags {
    for (MRKCourseConditionModel *condition in self.conditionsArray) {
        for (MRKConditionTagModel *tag in condition.options) {
            tag.isSelect = NO;
        }
    }
}

/// 根据key 找到下面的tag
- (MRKCourseConditionModel *)findCurrentKeyModel:(NSString *)key {
    for (MRKCourseConditionModel *model in self.conditionsArray) {
        if ([model.key isEqualToString:key]) {
            return model;
        }
    }
    return nil;
}

- (NSArray *)currentConditionModel {
    return self.fliterAlertView.dataArray;
}


#pragma mark - UITableViewDelegate & UITableViewDataSource
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.dataArray.count;
}
- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    NSString *Identifier = [MRKCourseFilterListCell sc_className];
    MRKCourseFilterListCell *cell = [tableView dequeueReusableCellWithIdentifier:Identifier];
    if (!cell) {
        cell = [[MRKCourseFilterListCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:Identifier];
    }
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    cell.backgroundColor = UIColor.clearColor;
    MRKCourseFilterListModel *model = self.dataArray[indexPath.row];
    [cell setCourseListDetail:model];
    return cell;
}
- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    MRKCourseFilterListModel *model = self.dataArray[indexPath.row];
    tableView.traceEventId = @"btn_AllCourses_course";
    tableView.tracePara = @{@"course_id": model.cid};
    [[RouteManager sharedInstance] jumpToCourseDetailWithId:model.cid];
}




- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
        _tableView.backgroundColor = [UIColor whiteColor];
        _tableView.tableFooterView = [UIView new];
        _tableView.estimatedSectionHeaderHeight = 0;
        _tableView.estimatedSectionFooterHeight = 0;
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.showsHorizontalScrollIndicator = NO;
        _tableView.showsVerticalScrollIndicator = NO;
        
        int cellwidth = (RealScreenWidth -WKDHPX(16)*2)*0.465;
        _tableView.rowHeight = cellwidth + WKDHPX(12);
        _tableView.dataSource = self;
        _tableView.delegate = self;
        [_tableView registerClass:[MRKCourseFilterListCell class] forCellReuseIdentifier:[MRKCourseFilterListCell sc_className]];
    }
    return _tableView;
}

- (MRKAllCourseFilterView *)fliterView {
    if (!_fliterView) {
        _fliterView = [[MRKAllCourseFilterView alloc] init];
        _fliterView.backgroundColor = UIColor.whiteColor;
        @weakify(self);
        _fliterView.searchFieldClick = ^{
            MRKCourseSearchController *search = [[MRKCourseSearchController alloc] init];
            [self_weak_.navigationController pushViewController:search animated:YES];
        };
        _fliterView.selectBlock = ^(NSString * _Nonnull key, BOOL select) {
            [self_weak_ selectHeadTag:key select:select];
        };
        _fliterView.moreTagBlock = ^(BOOL select) {
            [self_weak_ moreTagSelect:select];
        };
    }
    return _fliterView;
}

- (MRKAllCourseFilterAlert *)fliterAlertView {
    if (!_fliterAlertView) {
        _fliterAlertView = [[MRKAllCourseFilterAlert alloc] init];
        @weakify(self);
        /// 消失
        _fliterAlertView.dismissBlock = ^{
            [self_weak_.fliterView resetButtonState];
            [self_weak_.fliterView resetMoreButtonState];
        };
        /// 取消筛选
        _fliterAlertView.cancelBlock = ^{
            [MRKCourseFilterViewModel restoreData:self_weak_.conditionsArray];
            [self_weak_ refreshButtonTitles];
        };
        /// 重置
        _fliterAlertView.resetBlock = ^{
            [MRKCourseFilterViewModel resetData:[self_weak_ currentConditionModel]];
        };
        /// 确认
        _fliterAlertView.confirmBlock = ^NSArray * _Nonnull{
            NSArray *arr = [MRKCourseFilterViewModel selectData:self_weak_.conditionsArray];
            [self_weak_ requestFirstData];
            return arr;
        };
    }
    return _fliterAlertView;
}



/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/

#pragma mark - WKClassifyListViewDelegate
- (UIView *)listView {
    return self.view;
}

@end

