//
//  MRKCourseApi.m
//  Student_IOS
//
//  Created by merit on 2023/3/30.
//

#import "MRKCourseApi.h"

@implementation MRKCourseApi

/// 实景视频
+ (void)liveVideoCourse:(NSInteger)page
             equimentID:(NSString *)equimentID
           topRankCount:(NSInteger)topRankCount
                success:(void(^)(id data))success
                failure:(void(^)(NSError * error))failure {

    NSMutableDictionary *parms = @{
        @"current" : @(page),
        @"size" : @"10",
        @"equipTypeId" : equimentID?:@""
    }.mutableCopy;
    
    if (topRankCount > 0) {
        parms[@"topRankCount"] = @(topRankCount);
    }
    
    [MRKBaseRequest mrkGetRequestUrl:@"/course/LiveVideoController/getLiveVideoList"
                             andParm:parms
            completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        id data = [request.responseObject valueForKeyPath:@"data"];
        success(data);
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        failure(request.error);
    }];
}

/// 训练计划
+ (void)planDataParms:(NSDictionary *)parms
              success:(void(^)(id data))success
              failure:(void(^)(NSError * error))failure {
    [MRKBaseRequest mrkGetRequestUrl:@"course/coursePlanUserAssociatedController/allCoursePlanUserAssociatedPage"
                             andParm:parms
            completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        id data = [request.responseObject valueForKeyPath:@"data"];
        success(data);
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        failure(request.error);
    }];
}

/// 主题课程 合集
+ (void)requestThemeCoursePage:(int)page equimentID:(NSString *)equimentID displayForm:(int)displayForm
                    completion:(void(^)(MRKThemeModel *model))completion
                       failure:(void(^)(NSError * error))failure {
    NSMutableDictionary *data = @{}.mutableCopy;
    if (![equimentID isNotBlank])  {
        data = @{@"displayLocation" : @"1",
                 @"displayForm" : @(displayForm),
                 @"current" : @(page),
                 @"size" : @10
        }.mutableCopy;
    }else{
        data =  @{@"displayLocation" : @"2",
                  @"displayForm" : @(displayForm),
                  @"current" : @(page),
                  @"size" : @10,
                  @"equipmentIds" : @[equimentID?:@""]}.mutableCopy;
    }
    
    [MRKBaseRequest mrkPostRequestUrl:@"/course/theme"
                             andParm:data
            completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        id data = [request.responseObject valueForKeyPath:@"data"];
        MRKThemeModel *model = [MRKThemeModel modelWithJSON:data];
        completion(model);
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        failure(request.error);
    }];
}

/// 首页推荐专题
+ (void)requestHomeThemeCourse:(NSString *)equimentID
                    completion:(void(^)(MRKThemeModel *model))completion
                       failure:(void(^)(NSError * error))failure {
    NSMutableDictionary *data = @{}.mutableCopy;
    if (![equimentID isNotBlank])  {
    }else{
        data =  @{@"equipmentIds":@[equimentID?:@""]}.mutableCopy;
    }
    
    [MRKBaseRequest mrkPostRequestUrl:@"/course/theme"
                             andParm:data
            completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        id data = [request.responseObject valueForKeyPath:@"data"];
        MRKThemeModel *model = [MRKThemeModel modelWithJSON:data];
        completion(model);
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        failure(request.error);
    }];
}

/// 燃友都在练
+ (void)topCourseEquimentID:(NSString *)equimentID
                    success:(void (^)(NSArray<HomeTopCourseModel *> * _Nonnull))success
                    failure:(void (^)(NSError * _Nonnull))failure {
    
    NSDictionary *dic = @{
        @"productId":equimentID?:@"",
    }.mutableCopy;
    [MRKBaseRequest mrkGetRequestUrl:@"/course/popular/courses"
                             andParm:dic
            completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        id data = [request.responseObject valueForKeyPath:@"data"];
        NSArray *array = [NSArray modelArrayWithClass:[HomeTopCourseModel class] json:data].mutableCopy;
        success(array);
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        failure(request.error);
    }];
}

/// 为你推荐
+ (void)requestRecommendCourse:(NSString *)productId
                    completion:(void(^)(NSArray<MRKRecommendCourseModel *> *array))completion
                       failure:(void(^)(NSError * error))failure {
    NSDictionary *dic = @{
        @"equipmentId":productId?:@"",
        @"showLive":@1
    }.mutableCopy;
    [MRKBaseRequest mrkGetRequestUrl:@"/course/recommend/courses"
                             andParm:dic
            completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSArray *dataArray = [request.responseObject valueForKeyPath:@"data"];
        NSArray *array = [NSArray modelArrayWithClass:[MRKRecommendCourseModel class] json:dataArray].mutableCopy;
        completion(array);
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        failure(request.error);
    }];
}

/// 放松拉伸
+ (void)requestStretchCourse:(NSString *)productId
                  completion:(void(^)(MRKCourseModel *model))completion
                     failure:(void(^)(NSError * error))failure {
    NSDictionary *parms = @{
        @"productId":productId?:@""
    }.mutableCopy;
    [MRKBaseRequest mrkGetRequestUrl:@"/course/relax_course"
                             andParm:parms
            completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        id data = [request.responseObject valueForKeyPath:@"data"];
        MRKCourseModel *model = [MRKCourseModel modelWithJSON:data];
        completion(model);
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        failure(request.error);
    }];
}

/// 绝影专属
+ (void)requestXenjoyCourse:(NSString *)productId
                  completion:(void(^)(NSArray<MRKRecommendCourseModel *> *array))completion
                     failure:(void(^)(NSError * error))failure {
    NSDictionary *parms = @{
        @"current" :@"1",
        @"size" : @"10",
        @"product" : @[productId?:@""]
    }.mutableCopy;
    [MRKBaseRequest mrkRequestType:YTKRequestMethodPOST
                               url:@"/course/search/course/pagexvip"
                           andParm:parms
                      notShowError:YES
          completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        id data = [request.responseObject valueForKeyPath:@"data.records"];
        NSArray *array = [NSArray modelArrayWithClass:[MRKCourseModel class] json:data].mutableCopy;
        completion(array);
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        failure(request.error);
    }];
}

@end








#pragma mark - -----------主题课程列表请求-------------
@interface MRKThemeCourseListApi()
@property (nonatomic, strong) RACSubject *listDataSignal;
@property (nonatomic, assign) NSInteger total;
@end

@implementation MRKThemeCourseListApi

- (instancetype)init {
    if (self = [super init]) {
        self.page = 1;
        self.listDataSignal = [RACSubject subject];
    }
    return self;
}

/// 请求第一页数据
- (void)requestFirstCourse {
    self.page = 1;
    [self requestThemeCourseListSuccess:^(NSArray<MRKCourseModel *> *data) {
        self.courseArray = [NSMutableArray arrayWithArray:data];
        [(RACSubject *)self.listDataSignal sendNext:@"endRefresh"];
    } failer:^{
        [(RACSubject *)self.listDataSignal sendNext:@"endRefresh"];
    }];
}

/// 请求下一页数据
- (void)requestNextCourse {
    self.page ++;
    [self requestThemeCourseListSuccess:^(NSArray<MRKCourseModel *> *data) {
        [self.courseArray addObjectsFromArray:data];
        [(RACSubject *)self.listDataSignal sendNext:@"endRefresh"];
    } failer:^{
        [(RACSubject *)self.listDataSignal sendNext:@"endRefresh"];
        self.page--;
    }];
}

- (void)requestThemeCourseListSuccess:(void (^)(NSArray<MRKCourseModel *> *data))success
                               failer:(void (^)(void))failer {
    NSDictionary *parm = @{@"current":@(self.page),
                           @"size":@"10",
                           @"themeId":self.themeID?:@"",
                           @"useType":@1 }.mutableCopy;
    [MRKBaseRequest mrkGetRequestUrl:@"/course/theme/course" andParm:parm completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        id total = request.responseObject[@"data"][@"total"];
        if (total && self.page == 1) {
            self.total = [NSString stringWithFormat:@"%@",total].integerValue;
        }
        
        NSArray *dataArray = [request.responseObject valueForKeyPath:@"data.records"];
        NSLog(@"%@",dataArray);
        NSArray *models = [NSArray modelArrayWithClass:[MRKCourseModel class] json:dataArray];
        success(models);
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        failer();
    }];
}
@end

