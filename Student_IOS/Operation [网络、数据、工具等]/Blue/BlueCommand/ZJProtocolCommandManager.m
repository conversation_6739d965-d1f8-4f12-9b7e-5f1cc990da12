//
//  ZJProtocolCommandManager.m
//  Student_IOS
//
//  Created by MacPro on 2022/4/18.
//

#import "ZJProtocolCommandManager.h"
#import "BlueCommandData.h"

@implementation ZJProtocolCommandManager


+ (void)startSportWithType:(NSString *)type {
    BluetoothModel *bModel = [BlueDataStorageManager connectBMFromProductID:type];
    BaseBlueCommandManager *model = bModel.commandManager; // [BaseBlueCommandManager shareManager];
    switch (type.intValue) {
        case SkipRopeEquipment:
        {
            NSLog(@"跳绳");
            [model startTimer];
            
        }
            break;
        case EllipticalEquipment:
        case BicycleEquipment:
        case BoatEquipment:
        case PowerEquipment:
        case StairClimbEquipment:
        {
    
            [[BluetoothManager sharedInstance]sendData:[ZJCommandData getIDCmd] type:type];
            NSString *log = [NSString stringWithFormat:@"&&智健-车标类协议-getIDCmd&&：%@ ， 并且开始获取数据",[ZJCommandData getIDCmd]];
            MLog(log);
          
            [model startTimer];
            
        }
            break;
        case TreadmillEquipment:
        {
            
            //X3跑步机 必须先发送一条指令，再获取状态才可以拿到数据 22-06-14
            [[BluetoothManager sharedInstance]sendData:[ZJCommandData getIDCmd] type:type];
            NSString *log = [NSString stringWithFormat:@"&&智健-跑步机协议-getIDCmd&&：%@ ， 并且开始获取数据",[ZJCommandData getIDCmd]];
            MLog(log);
          
            [NSThread sleepForTimeInterval:0.5];
            //先获取设备状态，后续根据当前状态做判断
            //            [[BluetoothManager sharedInstance]sendData:[ZJCommandData treadmillData] type:type];
            [model startTimer];
            
        }
            break;
            
        default:
            break;
    }
    
}


+ (void)startSkip {
    [[BluetoothManager sharedInstance]sendData:[ZJCommandData skipStartData] type:[@(SkipRopeEquipment) stringValue]];
}

+ (void)treamillReady {
    NSString *type = [NSString stringWithFormat:@"%@" , @(TreadmillEquipment)];
    BluetoothModel *bModel = [BlueDataStorageManager connectBMFromProductID:type];
    
    [[BluetoothManager sharedInstance] sendData:[ZJCommandData treamillReady] type:type];
    BaseBlueCommandManager *model = bModel.commandManager; // [BaseBlueCommandManager shareManager];
    [model startTimer];
    
    NSString *log = [NSString stringWithFormat:@"&&智健-跑步机协议-发送就绪&&：%@ ",[ZJCommandData treamillReady]];
    MLog(log);
}

+ (void)treamillGoon {
    NSString *type = [NSString stringWithFormat:@"%@" , @(TreadmillEquipment)];
    [[BluetoothManager sharedInstance] sendData:[ZJCommandData treadmillStartData] type:type];
    
    NSString *log = [NSString stringWithFormat:@"&&智健-跑步机协议-发送继续&&：%@",[ZJCommandData treadmillStartData]];
    MLog(log);
}

+ (void)treamillPause {
    NSString *type = [NSString stringWithFormat:@"%@" , @(TreadmillEquipment)];
    [[BluetoothManager sharedInstance] sendData:[ZJCommandData treadmillPauseData] type:type];
    NSString *log = [NSString stringWithFormat:@"&&智健-跑步机协议-发送暂停&&：%@",[ZJCommandData treadmillPauseData]];
    MLog(log);
}

+ (void)getDataWithType:(NSString *)type {
    switch (type.intValue) {
        case SkipRopeEquipment:
        {
            [[BluetoothManager sharedInstance]sendData:[ZJCommandData skipDataCommand] type:type];
            [[BluetoothManager sharedInstance]sendData:[ZJCommandData skipElectricCommand] type:type];
        }
            break;
        case EllipticalEquipment:
        case BicycleEquipment:
        case BoatEquipment:
        case PowerEquipment:
        case StairClimbEquipment:
        {
            NSLog(@"车表类协议");
            [[BluetoothManager sharedInstance]sendData:[ZJCommandData getDeviceStatusData] type:type];
            [[BluetoothManager sharedInstance]sendData:[ZJCommandData getSportData] type:type];
        }
            break;
        case TreadmillEquipment:
        {
            NSLog(@"跑步机");
            [[BluetoothManager sharedInstance] sendData:[ZJCommandData treadmillData] type:type];
        }
            break;
            
        default:
            break;
    }
}


+ (void)endSportWithType:(NSString *)type {
    switch (type.intValue) {
        case SkipRopeEquipment:
        {
            [[BluetoothManager sharedInstance]sendData:[ZJCommandData skipEndData] type:type];
            
        }
            break;
        case EllipticalEquipment:
        case BicycleEquipment:
        case BoatEquipment:
        case PowerEquipment:
        case StairClimbEquipment:
        {
            
            [[BluetoothManager sharedInstance]sendData:[ZJCommandData endSportCmd] type:type];
            NSString *log = [NSString stringWithFormat:@"&&智健-车标类协议-发送结束&&：%@",[ZJCommandData endSportCmd]];
            MLog(log);
            
        }
            break;
        case TreadmillEquipment:
        {
            NSLog(@"跑步机__endSportWithType");
            [[BluetoothManager sharedInstance] sendData:[ZJCommandData treadmillStopData] type:type];
            NSString *log = [NSString stringWithFormat:@"&&智健-跑步机协议-发送结束&&：%@",[ZJCommandData treadmillStopData]];
            MLog(log);
        }
            break;
            
        default:
            break;
    }
}

+ (void)setMode:(NSDictionary *)sender {
    NSNumber *target = [sender objectForKey:@"target"];
    NSNumber *mode = [sender objectForKey:@"mode"];
    NSString *type = [NSString stringWithFormat:@"%@",[sender objectForKey:BlueDeviceType]];
    NSData *data;
    if (type.intValue == SkipRopeEquipment) {
        if (mode.intValue == FreedomMode) {
            //自由训练模式
            data = [ZJCommandData setJumpTargetNumber:0 time:0 model:mode.intValue];
            
        } else if (mode.intValue == CutdownNumberMode) {
            data = [ZJCommandData setJumpTargetNumber:target.intValue time:0 model:mode.intValue];
        } else if(mode.intValue == CutdownTimeMode) {
            data = [ZJCommandData setJumpTargetNumber:0 time:target.intValue model:mode.intValue];
        }
    }
    
    [[BluetoothManager sharedInstance] sendData:data type:type];
}

+ (void)setDeviceParameters:(NSDictionary *)para {
    NSString *type = [NSString stringWithFormat:@"%@",[para objectForKey:BlueDeviceType]];
    NSNumber *drag = [para objectForKey:Resistance];
    NSNumber *slop = [para objectForKey:Slope];
    NSNumber *speed = [para objectForKey:Speed];
    NSData *data ;
    switch (type.intValue) {
        case BicycleEquipment:
        case BoatEquipment:
        case EllipticalEquipment:
        case PowerEquipment:
        case StairClimbEquipment:{
            data = [ZJCommandData setDrag:drag.intValue gradient:slop.intValue];
        }
            break;
        case TreadmillEquipment: {
            NSNumber *status = [para objectForKey:@"status"];
            if (speed.intValue == 0 && status.intValue != DevicePauseStatus) {//速度为0 发送暂停指令
                data = [ZJCommandData treadmillPauseData];
            } else {
                data = [ZJCommandData setSpeed:((int)(speed.doubleValue)) gradient:slop.intValue];
            }
        }
            break;
            
        default:
            break;
    }
    
    NSLog(@"%@_____setResistance == %@" , NSStringFromClass([self class]) , data);
    if (!isDistribute) {
        BLog(@"%@_____setResistance == %@" , NSStringFromClass([self class]) ,[para modelToJSONString]);
    }
    
    [[BluetoothManager sharedInstance] sendData:data type:type];
}


+ (void)startCarSport:(NSString *)type {
    NSData *data = [ZJCommandData startSportCmdData];
    [[BluetoothManager sharedInstance] sendData:data type:type];
}

+ (void)goonCarSport:(NSString *)type {
    NSData *data = [ZJCommandData goonSportCmdData];
    [[BluetoothManager sharedInstance] sendData:data type:type];
}

@end
