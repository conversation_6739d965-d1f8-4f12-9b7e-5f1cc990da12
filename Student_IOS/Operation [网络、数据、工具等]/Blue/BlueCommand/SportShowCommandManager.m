//
//  SportShowCommandManager.m
//  ZBlueSDK
//
//  Created by MacPro on 2024/10/21.
//

#import "SportShowCommandManager.h"
#import "BlueCommandData.h"


@implementation SportShowCommandManager


+(void)startSportWithType:(NSString *)type {
    
    BluetoothModel *bModel = [BlueDataStorageManager connectBMFromProductID:type];
   
    BaseBlueCommandManager *model = bModel.commandManager; // [BaseBlueCommandManager shareManager];
    
    
    switch (type.intValue) {

        case EllipticalEquipment:
        case BicycleEquipment:
        case BoatEquipment:
        {
            NSLog(@"运动秀协议==startSportWithType");
            ///获取设备参数信息
//            [self getConfigInfo:type];
//            [NSThread sleepForTimeInterval:0.3];
            
            //发送就绪指令
//            [self sportReady:type];
            //发送开始指令
            [self sportResume:type];
            [NSThread sleepForTimeInterval:0.3];
            
            //开始主动获取数据
            [model startTimer];
            
        }
            break;

        
        default:
            break;
    }
    
}


+(void)getDataWithType:(NSString *)type {
    switch (type.intValue) {
        
        case BicycleEquipment:
        {
            NSLog(@"运动秀协议==getDataWithType");
            [[BluetoothManager sharedInstance] sendData:[SportShowCommandData getStatusAndDataCmd] type:type];
            [NSThread sleepForTimeInterval:0.1];
            [[BluetoothManager sharedInstance] sendData:[SportShowCommandData getSportDataCmd] type:type];
        }
            break;
      
            
        default:
            break;
    }
}


+(void)endSportWithType:(NSString *)type {
    
    [self pauseTimer:type];
    
    switch (type.intValue) {
        case EllipticalEquipment:
        case BicycleEquipment:
        case BoatEquipment:
        {
            NSLog(@"运动秀协议");
            [[BluetoothManager sharedInstance] sendData:[SportShowCommandData sportEndCmd] type:type];
        }
            break;
    
            
        default:
            break;
    }
    
    [self resumeTimer:type];
}

+(void)setDeviceParameters:(NSDictionary *)para {
    
    
    NSString *type = [NSString stringWithFormat:@"%@" , [para objectForKey:BlueDeviceType]];
    NSNumber *drag = [para objectForKey:Resistance];
    NSNumber *slop = [para objectForKey:Slope];
//    NSNumber *speed = [para objectForKey:kCTLSpeed];
    NSData *data ;
    
    [self pauseTimer:type];
    
    switch (type.intValue) {
        case BicycleEquipment:
        {
            data = [SportShowCommandData setDrag:drag.intValue gradient:slop.floatValue];
        }
            break;
      
            
        default:
            break;
    }
    NSLog(@"%@_____setResistance==%@" , NSStringFromClass([self class]) , data);
    
    [[BluetoothManager sharedInstance] sendData:data type:type];
    
    [self resumeTimer:type];
}


+(void)getConfigInfo:(NSString *)type {
    NSLog(@"运动秀协议===getConfigInfo");
    
    [self pauseTimer:type];
    
    ///获取设备参数信息
    switch (type.intValue) {
        case BicycleEquipment:
        {
            [[BluetoothManager sharedInstance] sendData:[SportShowCommandData getConfigDataCmd] type:type];
        }
            break;
     
        default:
            break;
    }
   
    [self resumeTimer:type];
}

//暂停设备
+(void)sportPause:(NSString *)type {
    NSLog(@"运动秀协议===sportPause");
    [self pauseTimer:type];
    [[BluetoothManager sharedInstance]  sendData:[SportShowCommandData sportPauseCmd] type:type];
    [self resumeTimer:type];
}

+(void)sportResume:(NSString *)type {
    NSLog(@"运动秀协议===sportResume");
    [self pauseTimer:type];
    [[BluetoothManager sharedInstance]  sendData:[SportShowCommandData sportStartCmd] type:type];
    [self resumeTimer:type];
}
+(void)sportReady:(NSString *)type {
    NSLog(@"运动秀协议===sportReadyAction");
    [self pauseTimer:type];
    [[BluetoothManager sharedInstance]  sendData:[SportShowCommandData sportReadyCmd] type:type];
    [self resumeTimer:type];
}


+(void)pauseTimer:(NSString *)type {
    BluetoothModel *bModel = [BlueDataStorageManager connectBMFromProductID:type];
    BaseBlueCommandManager *model = bModel.commandManager;
    [model suspendTimer];
    [NSThread sleepForTimeInterval:0.3];
}
+(void)resumeTimer:(NSString *)type {
    BluetoothModel *bModel = [BlueDataStorageManager connectBMFromProductID:type];
    BaseBlueCommandManager *model = bModel.commandManager;
    [NSThread sleepForTimeInterval:0.3];
    [model resumeTimer];
}

@end
