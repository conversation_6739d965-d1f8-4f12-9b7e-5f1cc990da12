//
//  MRKOTAUpdateViewController.m
//  Student_IOS
//
//  Created by MacPro on 2021/6/29.
//

#import "MRKOTAUpdateViewController.h"
#import "BlueOTAManager.h"
#import "CircleView.h"
#import "BlueCheckOTAManager.h"
#import "FreedomDataView.h"
#import "MrkMeritHRDealManager.h"
#import "NewBluePeripheralManager.h"

#define kUpdateProgressInterval 0.1

@interface MRKOTAUpdateViewController ()<BlueDataDealManagerDelegate>
@property (nonatomic , strong) UITextView *logLabel;
@property (nonatomic , strong) UILabel *statusLabel;
@property (nonatomic , strong) UIButton *updateButton;
@property (nonatomic , strong) UILabel *titleLabel;
@property (nonatomic , strong) UILabel *progresslabel;
@property (nonatomic , strong) UILabel *versionLabel;
@property (nonatomic , strong) UILabel *currentVLabel;
@property (nonatomic , assign) UpdateStatus updateStatus;

@property (nonatomic , strong) CircleView *circleProgressView;
@property (nonatomic , strong) JMQPressAlertView *updatingView;//更新中不要使用设备提示view
@property (nonatomic , strong) BlueDataDealManager *dataManager;
@property (nonatomic , strong) BaseEquipDataModel *tyModel;

//为了防止刷新频率过多，导致绘制进度有误，定时刷新进度条 间隔0.01
@property (nonatomic , strong) RACDisposable *updateProgressDisposable;
///是否开始了获取数据
@property (nonatomic , assign) BOOL isOpenData;
@end

@implementation MRKOTAUpdateViewController

- (void)viewDidLoad {
    self.tracePageId = @"page_equipment_Firmware_Update";
    [super viewDidLoad];
    
    [self setUI];
    [self loadData];
    if (self.model.productId.intValue == KettleBellEquipment){ // 智能壶铃
        self.updatingView.alertString = @"更新过程中，请不要操作智能壶铃！";
    }else{
        self.updatingView.alertString = @"请注意更新过程中，不要调节阻力或速度！";
    }
}

- (void)loadData {
    [BlueCommunicationProtocol setBlueOTAServiceFromProductID:self.model.productId];
    
    //    [BlueCommunicationProtocol setBlueOTAServiceFromPeripheral:[BluePeripheral periphralFromType:self.model.productId] otaType:self.otaM.otaType];//设置ota 服务
    
    
    NSString *cVersion = self.model.firmwareVersion ;
    if ([self.model.bluetoothName isEqualToString:[BluePeripheral connectDeviceNameOfType:self.model.productId]]) {
        cVersion = [BluePeripheral peripheralSoftVersionFromType:self.model.productId eigenValue:self.model.versionEigenValue];
    }
    
    self.logLabel.text = self.otaM.updateLog;
    self.titleLabel.text = [NSString stringWithFormat:@"新版本%@更新日志：" , self.otaM.versionNumber];
    self.versionLabel.text = self.otaM.versionNumber;
    self.currentVLabel.text = [cVersion isNotBlank] ?  [NSString stringWithFormat:@"当前版本：%@",cVersion] : @"";
    
    
    self.updateButton.hidden = NO;
    self.statusLabel.hidden = YES;
    
    if (self.model.productId.intValue == SkipRopeEquipment
        || self.model.productId.intValue == HeartEquipment) {
        self.dataManager = [[BlueDataDealManager alloc] initWithType:self.model.productId source:self];
        self.dataManager.delegate = self;
        [self.dataManager startGetData];
        self.isOpenData = YES;
    }
    
    if (self.otaM.updateType.intValue == ForceUpdate) {
        //强制更新 直接开始
        [self enterOTA];
    }
    
}

- (void)startUpdatingUI {
    if (self.updateStatus != Updating) {
        self.updateStatus = Updating;
        self.statusLabel.text = @"更新中...";
        self.statusLabel.hidden = NO;
        self.updateButton.hidden = YES;
    }
}


- (void)enterOTA {
    ///心率臂带hw401ota
    if (self.model.productId.intValue == HeartEquipment
        && self.otaM.otaProtocol.intValue == DFUOTA
        && [self.model.modelId isEqualToString:@"1565241401025425410"]
        ) {
        
        [self startUpdatingUI];
        
        ///进入ota模式
        [MrkMeritHRDealManager  enterOTAMode];
        
        @weakify(self);
        __block RACDisposable *dispose = [RACObserve([BlueDataStorageManager sharedInstance], connectDictionary) subscribeNext:^(NSDictionary * x) {
            @strongify(self);
            if([x.allKeys containsObject:self.model.productId]) {
                BluetoothModel *bm = [x objectForKey:self.model.productId];
                if ([[NewBluePeripheralManager sharedInstance].connectEquipmentPrefix isNotBlank]
                    && [bm.localName hasPrefix:[NewBluePeripheralManager sharedInstance].connectEquipmentPrefix]) {
                    
                    [NewBluePeripheralManager sharedInstance].connectEquipmentPrefix = @"";
                    [self beginDown];
                    [dispose dispose];
                }
            } else {
                NSLog(@"进入ota模式了");
                [NewBluePeripheralManager sharedInstance].connectEquipmentPrefix = @"HW401U";
                [[NewBluePeripheralManager sharedInstance] startScan];
                /*
                 //23-04-08
                 [BluetoothManager sharedInstance].connectEquipmentPrefix = @"HW401U";
                 [BluetoothManager sharedInstance].equipmentType = self.model.productId;
                 [[BluetoothManager sharedInstance] scanBluetooth];
                 */
                
            }
        }];
    } else {
        //直接开始
        [self beginDown];
        
    }
}

- (void)updateUI {
    NSLog(@"%@_____elec==%@" ,NSStringFromClass(self.class) , self.dataManager.tyModel.electric);
    if (self.dataManager.tyModel.electric) {
        [self.dataManager endGetData];
        self.tyModel = self.dataManager.tyModel;
        [self dealUpdate];
    }
}
#pragma mark - 处理更新逻辑
-(void)dealUpdate {
    dispatch_async(dispatch_get_main_queue(), ^{
        if (self.isOpenData) {
            //开启了数据交互，需要结束数据交互
            [self.dataManager clearAll];
        }
        
        if ((self.model.productId.intValue == SkipRopeEquipment && self.tyModel.electric.intValue < kDefineSkipOTAElectric) ||
            (self.model.productId.intValue == HeartEquipment && self.tyModel.electric.intValue < kDefineArmOTAElectric)) {
            
            //如果已经开始更新 则 取消更新
            if (self.updateStatus == Updating) {
                BlueOTAManager *manager = [BlueOTAManager shareManager];
                [manager cancelUpadte];
                [self updateFail];
            }
            
            @weakify(self);
            [MrkAlertManager showAlert:@"温馨提示"
                               message:@"电量过低，请充电完成后更新固件"
                                ensure:@"返回至首页"
                           handleIndex:^(NSInteger index) {
                @strongify(self);
                
                if (self.otaM.updateType.intValue == ForceUpdate) {
                    // 强制更新 直接断开连接
                    [[NSNotificationCenter defaultCenter] postNotificationName:kBlueDisConnectNotification
                                                                        object:@{@"name":self.model.bluetoothName,
                                                                                 BlueDeviceType : self.model.productId}];
                }
                
                //返回首页
                [self.navigationController popToRootViewControllerAnimated:YES];
            }];
        }
    });
    
}

- (void)beginDown {
    
    if (![BlueDataStorageManager isConnectDeviceWithProductID:self.model.productId]) {
        [AppDelegate errorView:@"请重新连接设备" superView: self.view];
        self.updateStatus = UpdateFailure;
        [self updateFail];
        return;
    }
    //21-12-16 判断是否包含ota特征值
    
    NSArray  *arr = [BlueCommunicationProtocol otaCharacteristicFromType:self.model.productId];
    if (self.otaM.otaProtocol.intValue != DFUOTA && arr.count == 0) {
        [AppDelegate errorView:@"更新失败" superView: self.view];
        return;
    }
    [self startUpdatingUI];
    
    
    [MBProgressHUD showLodingWithMessage:@"" view:self.view];
    @weakify(self);
    [BlueTool downloadFileFromUrl:self.otaM.downloadLink progress:^(NSProgress *progress) {
        
    } success:^(id response) {
        NSLog(@"start update --- %@" , response);
        @strongify(self);
        dispatch_async(dispatch_get_main_queue(), ^{
            [MBProgressHUD hideHUDForView:self.view];
            [self startOta:response];
        });
        
    } failure:^(NSURLSessionDataTask *operation, NSError *error) {
        [MBProgressHUD hideHUDForView:self.view];
        [self updateFail];
    }];
}

- (void)startOta:(NSString *)filePath {
    
    if (![BlueDataStorageManager isConnectDeviceWithProductID:self.model.productId]) {
        [AppDelegate errorView:@"蓝牙已断开" superView:self.view];
        [self updateFail];
        return;
    }
    
    if (self.updateStatus == UpdateFailure) {
        return;
    }
    
    BlueOTAManager *manager = [BlueOTAManager shareManager];
    __block CGFloat value = 0.0;
    @weakify(self);
    self.updateProgressDisposable =  [[RACSignal interval:kUpdateProgressInterval onScheduler:[RACScheduler mainThreadScheduler]] subscribeNext:^(NSDate * _Nullable x) {
        [self_weak_ updateProgress:value];
    }];
    
    
    self.updateStatus = Updating;
    dispatch_async(dispatch_get_global_queue(0, 0), ^{
        manager.beginBlock = ^(CGFloat progress) {
            value = progress;
        };
        manager.success = ^(id data) {
            @strongify(self);
            [self updateSuccess];
        };
        manager.failure = ^(id data) {
            @strongify(self);
            [self updateFail];
        };
        [manager startWithPath:filePath model:self.otaM];
    });
    
}

- (void)updateProgress:(CGFloat)progress {
    dispatch_async(dispatch_get_main_queue(), ^{
        [self.circleProgressView setValue:progress];
        self.progresslabel.text = [NSString stringWithFormat:@"%.f" , progress * 100];
    });
}

- (void)updateFail{
    
    dispatch_async(dispatch_get_main_queue(), ^{
        [self.updateProgressDisposable dispose];
        
        //如果因为电量过低失败，则会直接将状态设置为失败，不需要提示更新失败，否则提示
        if (self.updateStatus != UpdateFailure) {
            [AppDelegate errorView:@"固件更新失败，请重新更新" superView:self.view];
            self.updateStatus = UpdateFailure;
        }
        [self updateProgress:0.0];
        self.statusLabel.hidden = YES;
        self.updateButton.hidden = NO;
    });
}

- (void)updateSuccess {
    dispatch_async(dispatch_get_main_queue(), ^{
        [self.updateProgressDisposable dispose];
        
        [AppDelegate errorView:@"固件更新成功" superView:self.view];
        
        self.updateStatus = UpdateSuccess;
        self.statusLabel.hidden = NO ;
        self.updateButton.hidden = YES;
        self.statusLabel.text = @"当前固件已经升级至最新版本";;
        self.currentVLabel.text = [NSString stringWithFormat:@"当前版本：%@" , self.otaM.versionNumber];
    });
}

- (void)back {
    if (self.updateStatus == Updating) {
        ///2.7.1 选择更新返回逻辑同步强制更新
        [AppDelegate errorView:@"正在更新固件，请耐心等待"];
    } else {
        [self.navigationController popViewControllerAnimated:YES];
    }
}


- (void)setUI {
    self.view.backgroundColor = [UIColor whiteColor];
    self.navTitle = @"固件更新";
    [self.mrkContentView removeFromSuperview];
    self.mrkContentView = nil;
    
    [self.view addSubview:self.updatingView];
    [self.updatingView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.mrk_navgationBar.mas_bottom);
        make.left.right.equalTo(@0);
        make.height.equalTo(@(40));
    }];
    
    
    CircleView *circleView = [[CircleView alloc]initWithFrame:CGRectMake((MainWidth - DHPX(184)) / 2.0, CGRectGetMaxY(self.mrk_navgationBar.frame) + (40),  DHPX(184), DHPX(184)) lineW:16.0];
    [circleView setLineColr:[UIColor redColor]];
    circleView.bgLayerColr =  [UIColor colorWithHexString:@"#f8f8f8"];
    circleView.lineColr = [UIColor colorWithHexString:@"#65D6FF"];
    circleView.colors = @[(id)[UIColor colorWithHexString:@"#65D6FF"].CGColor , (id)[UIColor colorWithHexString:@"#91E2FF"].CGColor];
    [self.view addSubview:circleView];
    [circleView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.updatingView.mas_bottom).offset(20);
        make.centerX.equalTo(self.view.mas_centerX);
        make.size.mas_equalTo(CGSizeMake(DHPX(184), DHPX(184)));
    }];
    
    
    UILabel *progressLabel = [UILabel new];
    progressLabel.textColor = [UIColor colorWithHexString:@"#4C5362"];
    progressLabel.text = @"0";
    progressLabel.font = [UIFont fontWithName:fontNamePing size:DHPX(68)];
    [self.view addSubview:progressLabel];
    [progressLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(circleView.mas_centerX).offset(DHPX(-8));
        make.centerY.equalTo(circleView.mas_centerY);
    }];
    
    
    UILabel *persentLabel = [UILabel new];
    persentLabel.textColor = [UIColor colorWithHexString:@"#4C5362"];
    persentLabel.text = @"%";
    persentLabel.font = [UIFont fontWithName:fontNamePing size:DHPX(32)];
    [self.view addSubview:persentLabel];
    [persentLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(progressLabel.mas_right);
        make.bottom.equalTo(progressLabel.mas_bottom).offset(-16);
    }];
    
    
    UILabel *latestVersionLabel = [UILabel new];
    latestVersionLabel.textColor = [UIColor colorWithHexString:@"#999999"];
    latestVersionLabel.font = [UIFont fontWithName:@"DINPro" size:DHPX(14)];
    [self.view addSubview:latestVersionLabel];
    [latestVersionLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(progressLabel.mas_bottom).offset((10));
        make.centerX.equalTo(circleView.mas_centerX).offset(0);
    }];
    
    
    UILabel *currentVersionLabel = [UILabel new];
    currentVersionLabel.textColor = [UIColor colorWithHexString:@"#4C5362"];
    currentVersionLabel.text = @"v2.2.3";
    currentVersionLabel.font = [UIFont fontWithName:fontNamePing size:DHPX(14)];
    [self.view addSubview:currentVersionLabel];
    [currentVersionLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(circleView.mas_bottom).offset((20));
        make.centerX.equalTo(self.view.mas_centerX).offset(0);
    }];
    
    
    UILabel *statusLabel = [UILabel new];
    statusLabel.textColor = [UIColor colorWithHexString:@"#00BBFF"];
    statusLabel.text = @"更新中...";
    statusLabel.font = [UIFont fontWithName:fontNamePing size:DHPX(18)];
    statusLabel.hidden = YES;
    [self.view addSubview:statusLabel];
    [statusLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(currentVersionLabel.mas_bottom).offset((20));
        make.centerX.equalTo(circleView.mas_centerX).offset(0);
    }];
    
    
    UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
    [button setTitle:@"更新固件" forState:UIControlStateNormal];
    [button setTitleColor:[UIColor colorWithHexString:@"#ffffff"] forState:UIControlStateNormal];
    button.backgroundColor = [UIColor colorWithHexString:@"#00BBFF"];
    button.layer.cornerRadius = DHPX(20);
    button.hidden = YES;
    [button addTarget:self action:@selector(enterOTA) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:button];
    [button mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(currentVersionLabel.mas_bottom).offset((20));
        make.centerX.equalTo(circleView.mas_centerX).offset(0);
        make.height.equalTo(@(DHPX(40)));
        make.width.equalTo(@(DHPX(140)));
    }];
    
    
    UIView *contentView = [UIView new];
    contentView.layer.cornerRadius = (20);
    contentView.backgroundColor = [UIColor colorWithHexString:@"#F8F8FA"];
    [self.view addSubview:contentView];
    [contentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.view.mas_centerX);
        make.top.equalTo(button.mas_bottom).offset((20));
        make.bottom.equalTo(@((-40)));
        make.left.equalTo(@(14));
        make.right.equalTo(@(DHPX(-14)));
    }];
    
    
    UILabel *titleLabel = [UILabel new];
    titleLabel.textColor = [UIColor colorWithHexString:@"#333333"];
    titleLabel.font = [UIFont fontWithName:fontNamePing size:DHPX(14)];
    [contentView addSubview:titleLabel];
    [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(contentView.mas_top).offset((25));
        make.left.equalTo(contentView.mas_left).offset(DHPX(20));
    }];
    
    
    UITextView *contentLabel = [UITextView new];
    contentLabel.textColor = [UIColor colorWithHexString:@"#333333"];
    contentLabel.font = [UIFont fontWithName:fontNamePing size:DHPX(14)];
    contentLabel.backgroundColor = [UIColor clearColor];
    contentLabel.editable = NO;
    [contentView addSubview:contentLabel];
    [contentLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(titleLabel.mas_bottom).offset((10));
        make.left.equalTo(contentView.mas_left).offset(DHPX(20));
        make.right.equalTo(contentView.mas_right).offset(DHPX(-20));
        make.bottom.equalTo(contentView.mas_bottom).offset(DHPX(0));
    }];
    
    
    self.circleProgressView = circleView;
    self.logLabel = contentLabel;
    self.statusLabel = statusLabel;
    self.updateButton = button;
    self.titleLabel = titleLabel;
    self.progresslabel = progressLabel;
    self.versionLabel = latestVersionLabel;
    self.currentVLabel = currentVersionLabel;
}

- (JMQPressAlertView *)updatingView {
    if (!_updatingView) {
        _updatingView = [[JMQPressAlertView alloc]initWithFrame:CGRectMake(0, 0, MainWidth, 40)];
        _updatingView.layer.backgroundColor = [UIColor colorWithRed:230/255.0 green:69/255.0 blue:72/255.0 alpha:0.1].CGColor;
        _updatingView.alertString = @"请注意更新过程中，不要调节阻力或速度！";
    }
    return _updatingView;
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
    self.fd_interactivePopDisabled = YES;
    self.navigationController.fd_fullscreenPopGestureRecognizer.enabled = YES;
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    
    self.fd_interactivePopDisabled = NO;
    self.navigationController.fd_fullscreenPopGestureRecognizer.enabled = NO;
}

- (void)dealloc {
    NSLog(@"____%@_____dealloc" , NSStringFromClass([self class]));
    [self.dataManager clearAll];
    [BlueOTAManager attempDealloc];
}

- (BOOL)viewControllerIsNeedNavBar:(MRKBaseController *)viewController{
    return YES;
}

- (void)leftButtonEvent:(UIButton *)sender navigationBar:(MRKNavigationBar *)navigationBar {
    [self back];
}

@end
