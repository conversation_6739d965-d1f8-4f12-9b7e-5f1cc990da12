//
//  BluetoothManager.m
//  Student_IOS
//
//  Created by MacPro on 2021/5/17.
//


#import "BluetoothManager.h"
#import "BaseBlueCommandManager.h"

@interface BluetoothManager ()<CBPeripheralDelegate, CBCentralManagerDelegate> {
    NSNumber *flags;
}
@property (nonatomic, strong) dispatch_queue_t queue;
@property (nonatomic, assign) BOOL isGetDeviceInfo;//2a24,f8c4
@property (nonatomic, assign) BOOL isConnectDevice;//遍历完所有的服务
@end

@implementation BluetoothManager

static BluetoothManager *_instance = nil;

+ (instancetype)sharedInstance {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        _instance = [[self alloc] init];
        [_instance initBluetooth];
    });
    return _instance;
}


- (void)initBluetooth {
    _queue = dispatch_queue_create("sendData",DISPATCH_QUEUE_CONCURRENT);
    
    _connectDic = [NSDictionary dictionary];
    _peripheralCharacteristicDictionary = [NSMutableDictionary dictionary];
    
    /*
     _peripheralArray = [[NSArray alloc] init];
     _peripheralNameArray = [NSArray array];
     _allPeripheralArray = [NSMutableArray array];
     _allPeripheralNameArray = [NSMutableArray array];
     _blueModelArray = [NSArray array];
    _centralManager = [[CBCentralManager alloc] initWithDelegate:self queue:nil];//创建CBCentralManager对象
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(scanBluetooth)
                                                 name:kStartScanBlueNotification
                                               object:nil];
    
    ///退出登录 断开蓝牙连接
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(clearAllPeripheral)
                                                 name:kLogout_Notification
                                               object:nil];
    
    @weakify(self);
    [RACObserve(self, connectDic) subscribeNext:^(NSDictionary * x) {
        @strongify(self);
        self.isConnect = x.allKeys > 0;
    }];
    
    
    [[RACSignal combineLatest:@[[RACObserve(self, isConnectDevice) distinctUntilChanged],
                                [RACObserve(self, isGetDeviceInfo) distinctUntilChanged]]] subscribeNext:^(RACTuple * x) {
        if ([x.first boolValue] && [x.last boolValue]) {
            @strongify(self);
            
            NSLog(@"设备连接成功，开始初始化设备数据============%@",self.periphral) ;
            NSString *localName = [BluePeripheral peripheralLocalName:self.periphral];
            BluetoothModel *model = [self.peripheralCharacteristicDictionary objectForKey:localName];
            NSString *type = model.type;
            NSDictionary *response = @{@"name":localName , BlueDeviceType : type , @"model" : model};
            
            [[NSNotificationCenter defaultCenter] postNotificationName:@"kBlueDeviceInitNotification" object:response];
        }
    }];
    
    
    [[[NSNotificationCenter defaultCenter] rac_addObserverForName:@"DeviceInitSuccessNotification" object:nil] subscribeNext:^(NSNotification * x) {
        @strongify(self);
        //设备初始化成功
        NSLog(@"初始化设备数据成功============%@",x.object) ;
        NSString *localName = [BluePeripheral peripheralLocalName:self.periphral];
        self.connectEquipmentName = @"";
        BluetoothModel *model = [self.peripheralCharacteristicDictionary objectForKey:localName];
        NSLog(@"before-----%@" , model.equipmentInfo);
        if (x.object) {
            //重新设置数据协议
            model.equipmentInfo = x.object;
            NSLog(@"after-----%@" , model.equipmentInfo);
        } else {
            //绑定，在绑定成功后重新设置
        }
        NSString *type = model.type;
        NSMutableDictionary *dic = [NSMutableDictionary dictionaryWithDictionary:self.connectDic];
        [dic setValue:localName forKey:type];
        self.connectDic = [dic copy];
        NSDictionary *response = @{@"name":localName ?:@"",
                                   BlueDeviceType : type ?:@""};
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(BLEConnectSucceed:)]) {
            [self.delegate BLEConnectSucceed:response];
        }
    }];
    
    [[[NSNotificationCenter defaultCenter] rac_addObserverForName:@"DeviceInitFailNotification" object:nil] subscribeNext:^(NSNotification * x) {
        @strongify(self);
        NSLog(@"初始化设备数据失败============%@",self.periphral) ;
        //初始化失败
        BluetoothModel *model = x.object;
        //断开连接
        [self cancleConnectPeripheralName:@{@"peripheral" :model.peripheral}];
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(connectFailWithError:)]) {
            [self.delegate connectFailWithError:ConnectInitFail];
        }
    }];
     */
}
/*
- (void)scanBluetooth
{
    NSLog(@"scanBluetooth-scanBluetooth-scanBluetooth-scanBluetooth");
    
    if ([self.centralManager isScanning]) {
        [self.centralManager stopScan];
    
    }
    
    if(self.peripheralArray.count >0){
        self.peripheralArray = [NSArray array];
        self.peripheralNameArray = [NSArray array];
        self.blueModelArray = [NSArray array];
    }
    
    
    //CBCentralManagerScanOptionAllowDuplicatesKey值为 No，表示不重复扫描已发现的设备
    NSDictionary *optionDic = [NSDictionary dictionaryWithObject:[NSNumber numberWithBool:NO] forKey:CBCentralManagerScanOptionAllowDuplicatesKey];
    [_centralManager scanForPeripheralsWithServices:nil options:optionDic];//如果你将第一个参数设置为nil，Central Manager就会开始寻找所有的服务。
}

- (void)stopScanBluetooth
{
    if (self.centralManager.isScanning) {
        [self.centralManager stopScan];
    }
    
    NSLog(@"BluetoothBase stopScanBluetooth，已经连接外设停止扫描或者手动停止扫描");
}

//连接设备
- (void)connectPeripheral:(CBPeripheral *)peripheral
{
    ///为了防止连接一个设备以后 ，又连第二个设备，而第一个设备连接成功以后 这两个值应该会被置为YES，所以连接成功以后重置一下这两个参数，以免影响后续设备连接
    self.isConnectDevice = NO;
    self.isGetDeviceInfo = NO;
    [self.centralManager connectPeripheral:peripheral options:nil];
}

-(void)connectPeripheralName:(NSDictionary *)para {
    
    if ([para.allKeys containsObject:@"peripheral"]) {
        [self connectPeripheral:[para objectForKey:@"peripheral"]];
        return;
    }
    NSString *name = [para objectForKey:@"name"];
    
    [BluetoothManager sharedInstance].connectEquipmentName = name;
    [BluetoothManager sharedInstance].connectDeviceInfo = [para objectForKey:@"dic"];
    
    CBPeripheral *p ;
    for (BluetoothModel *model in self.blueModelArray) {
        if([model.localName isEqualToString:name]) {
            p = model.peripheral;
            break;
        }
    }
    
    if(p) {
        [self connectPeripheral:p];
        return;
    }
    
    [self scanBluetooth];
    
}
//取消连接某个设备 防止连接失败回调后又连接成功 21-12-17
-(void)cancleConnectPeripheralName:(NSDictionary *)para{
    
    [self.centralManager stopScan];
    //已经有设备了 直接取消连接
    if ([para.allKeys containsObject:@"peripheral"]) {
        [self.centralManager cancelPeripheralConnection:[para objectForKey:@"peripheral"]];
        return;
    }
    NSString *type = [para objectForKey:BlueDeviceType];
    if (![BluetoothManager isConnectEquipmentType:type]) {
        return;
    }
    BluetoothModel *blue = [self.peripheralCharacteristicDictionary objectForKey:[self.connectDic objectForKey:[para objectForKey:BlueDeviceType]]];
    CBPeripheral *peripheral = blue.peripheral;
    if (peripheral && peripheral.state == CBPeripheralStateDisconnected) {
        //已经是断连状态
        [self removeConnectDevice:peripheral];
        return;
    }
    if (peripheral ) {
        [self.centralManager cancelPeripheralConnection:peripheral];
    }
    
    
}


//断开连接
- (void)disConnectPeripheral:(CBPeripheral *)peripheral
{
    NSLog(@"disConnectPeripheral");
    self.connectDeviceInfo = @{};
    
    if (peripheral.state == CBPeripheralStateDisconnected) {
        
        [self removeConnectDevice:peripheral];
    }
    
}


#pragma mark - 蓝牙状态更新
- (void)centralManagerDidUpdateState:(CBCentralManager *)central
{
    NSLog(@"centralManagerDidUpdateState:%ld",(long)central.state);
    self.blueState = central.state;
    switch (central.state) {
            
        case CBManagerStateUnknown: {
            self.isOpen = NO;
            NSLog(@"CBCentralManagerStateUnknown");
        } break;
            
        case CBManagerStateResetting: {
            self.isOpen = NO;
            NSLog(@"CBCentralManagerStateResetting");
        } break;
            
        case CBManagerStateUnsupported: {
            self.isOpen = NO;
            NSLog(@"CBCentralManagerStateUnsupported");
        } break;
            
        case CBManagerStatePoweredOff:
        case CBManagerStateUnauthorized: {
            NSLog(@"CBCentralManagerStateUnauthorized");
            
            self.isOpen = NO;
            [self clearAllPeripheral];
            
            if (self.delegate && [self.delegate respondsToSelector:@selector(connectFailWithError:)]) {
                [self.delegate connectFailWithError:ConnectUnauthFail];
            }
            
        } break;
            
        case CBManagerStatePoweredOn: {
            //[[BluetoothManager sharedInstance] scanBluetooth]; //很重要，当蓝牙处于打开状态，开始扫描。
            self.isOpen = YES;
        }break;
            
        default:{
            self.isOpen = NO;
            NSLog(@"蓝牙未工作在正确状态");
        } break;
    }
}

#pragma mark ------------------------------------------ 蓝牙连接相关 ---------------------------------------------
-(void)peripheral:(CBPeripheral *)peripheral didModifyServices:(NSArray<CBService *> *)invalidatedServices {
    NSLog(@"peripheral___didModifyServices");
}
#pragma mark - 扫描到外设 添加到数组保存
//扫描到外设，停止扫描，连接设备(每扫描到一个外设都会调用一次这个函数，若要展示搜索到的蓝牙，可以逐一保存 peripheral 并展示)
- (void)centralManager:(CBCentralManager *)central didDiscoverPeripheral:(CBPeripheral *)peripheral advertisementData:(NSDictionary<NSString *,id> *)advertisementData RSSI:(NSNumber *)RSSI {
    
    NSString *localName = [advertisementData objectForKey:@"kCBAdvDataLocalName"];
    
    if ([BluetoothManager isSupportEquipment:localName]
        ||[localName hasPrefix:@"MERACH-"]
        ||[localName hasPrefix:@"ESLinker"]
        ||[localName hasPrefix:@"i-console"]
        ||[localName hasPrefix:@"i-Console"]
        ||[localName hasPrefix:@"iConsole"]
        ||[localName hasPrefix:@"MRK"]
        ||[localName hasPrefix:@"Merach-MR"]
        ||[localName hasPrefix:@"Merach MR"]
        ||[localName hasPrefix:@"MERACH-MR"]
        ||[localName hasPrefix:@"Merach"]
        ||[localName hasPrefix:@"TF"]
        ||[localName hasPrefix:@"FS-"]//华为 电动版 581 椭圆机
        ||[localName hasPrefix:@"Hi-"]//筋膜枪
        ||[localName hasPrefix:@"Heart-B2"]//心率带
        ) {
        
        if (advertisementData) {
            NSLog(@"advertisementData -- %@" , advertisementData);
        }
        
        if (![self.peripheralArray containsObject:peripheral]) {
            
            NSMutableArray *arr = [NSMutableArray arrayWithArray:self.peripheralArray];
            [arr addObject:peripheral];
            self.peripheralArray = [arr copy];
            
            NSMutableArray *names = [NSMutableArray arrayWithArray:self.peripheralNameArray];
            [names addObject:localName];
            self.peripheralNameArray = names;
            
            NSLog(@"peripheralArray== %@ , %@" , self.peripheralArray , self.peripheralNameArray);
            
            NSMutableArray *models = self.blueModelArray.mutableCopy;
            BluetoothModel *model = [BluetoothModel new];
            model.localName = localName;
            model.peripheral = peripheral;
            model.advertisementData = advertisementData;
            [models addObject:model];
            self.blueModelArray = models.copy;
            
            [self.allPeripheralArray addObject:peripheral];
            [self.allPeripheralNameArray addObject:localName];
            
            ///心率臂带进入ota以后自动连接
            if ([self.connectEquipmentPrefix isNotEmpty] && [localName hasPrefix:self.connectEquipmentPrefix]) {
                [self stopScanBluetooth];
                [self connectPeripheral:peripheral];
                
            }
            
            if ([self.connectEquipmentName isNotEmpty] && [self.connectEquipmentName isEqualToString:localName]) {
                
                [self connectPeripheral:peripheral];
            }
            
        }
    }
    
    NSLog(@"%@",localName);
}

#pragma mark - 当中心端连接上外设时触发
- (void)centralManager:(CBCentralManager *)central didConnectPeripheral:(CBPeripheral *)peripheral {
    NSLog(@"连接上外设");
    
    [BluetoothManager sharedInstance].periphral = peripheral;

    
    peripheral.delegate = self;
    
    NSString *localName = [BluePeripheral peripheralLocalName:peripheral];
    
    [self.centralManager stopScan];
    
    NSString *type = [self typeOfPer];
    
    BluetoothModel *model = [BluetoothModel new];
    model.type = type;
    model.localName = localName;
    model.peripheral = peripheral;
    model.equipmentInfo = self.connectDeviceInfo;
    [self.peripheralCharacteristicDictionary setObject:model forKey:localName];
    
    ///22-09-19
    ///为了防止设备已经连接，但是在扫描服务的时候超时，状态显示可能会先出现失败再成功，所以连接设备以后 发出通知，相关页面可以暂停连接失败的处理
    [[NSNotificationCenter defaultCenter] postNotificationName:@"DeviceDidConnect" object:model];
    
    
    ///兼容心率臂带ota模式连接 22-09-14
    if ([self.connectEquipmentPrefix isNotEmpty]) {
        NSMutableDictionary *dic = [NSMutableDictionary dictionaryWithDictionary:self.connectDic];
        [dic setValue:localName forKey:type];
        self.connectDic = [dic copy];
        
        return;
    }
    
    [peripheral discoverServices:nil];
}

//连接失败
- (void)centralManager:(CBCentralManager *)central didFailToConnectPeripheral:(CBPeripheral *)peripheral error:(NSError *)error
{
    self.connectDeviceInfo = @{};
    self.connectEquipmentName = @"";
    NSLog(@"连接失败%@",error.localizedDescription);
    
    if ([self.delegate respondsToSelector:@selector(connectFailWithError:)]) {
        //code:14 , error:Peer removed pairing information
        [self.delegate connectFailWithError:error.code == 14 ? ConnectPairFail : ConnectOtherFail];
    }
    
    
}

//如果连接上的两个设备突然断开了，程序里面会自动回调下面的方法
- (void)centralManager:(CBCentralManager *)central didDisconnectPeripheral:(CBPeripheral *)peripheral error:(NSError *)error
{
    NSLog(@"已经断开蓝牙连接");
    [self removeConnectDevice:peripheral];
    
}

#pragma mark - CBPeripheralDelegate 外设端发现了服务时触发
- (void)peripheral:(CBPeripheral *)peripheral didDiscoverServices:(NSError *)error
{
    if (error){
        NSLog(@"%@",error.localizedDescription);
        return;
    }
    
    NSArray *svs = [BluePeripheral services];//需要获取特征值的所有的服务
    
    NSMutableDictionary *serviceDic = [NSMutableDictionary dictionary];
    
    for (CBService *service in peripheral.services)
    {
        NSLog(@"service -- %@" ,service.UUID.UUIDString );
        if ([svs containsObject:service.UUID.UUIDString.uppercaseString]) {
            [serviceDic setObject:[NSArray array] forKey:service.UUID.UUIDString.uppercaseString];
            [peripheral discoverCharacteristics:nil forService:service];
        }
        
    }
    NSString *localName = [BluePeripheral peripheralLocalName:peripheral];
    
    BluetoothModel *model = [self.peripheralCharacteristicDictionary objectForKey:localName];
    model.services = serviceDic.copy;
    
    
}

#pragma mark - 从服务获取特征
-(void)peripheral:(CBPeripheral *)peripheral didDiscoverCharacteristicsForService:(CBService *)service error:(NSError *)error
{
    
    NSLog(@"service.characteristics--%@",service.characteristics);
    if (error) {//报错直接返回退出
        NSLog(@"didDiscoverCharacteristicsForService error : %@", [error localizedDescription]);
        return;
    }
    
    NSMutableArray *otaArr = [NSMutableArray array];//ota特征数组
    
    NSMutableDictionary *servicesDic = [NSMutableDictionary dictionary];//所有服务信息{service:[characteristic]}
    NSMutableArray *chars = [NSMutableArray array];
    
    NSString *localName = [BluePeripheral peripheralLocalName:peripheral];
    BluetoothModel *model = [self.peripheralCharacteristicDictionary objectForKey:localName];
    
    if (model.services.allKeys.count) {
        servicesDic = [model.services mutableCopy];
    }
    if (model.otaCharacteristics.count) {
        otaArr = model.otaCharacteristics.mutableCopy;
    }
    
    //OTA 服务
    [service.characteristics enumerateObjectsUsingBlock:^(CBCharacteristic * _Nonnull characteristic, NSUInteger idx, BOOL * _Nonnull stop) {
        
        if ([[BluePeripheral otaServiceArray] containsObject:service.UUID.UUIDString.uppercaseString]) {
            [otaArr addObject:characteristic];
        }
        //获取设备版本号，模块版本号
        if ([service.UUID.UUIDString.uppercaseString isEqual:kDeviceInfomationService]
            || [service.UUID.UUIDString.uppercaseString isEqual:kZJDeviceInfoServiceUUID] ) {
            if ([[BluePeripheral peripheralInfoCharacteristicsArray] containsObject:characteristic.UUID.UUIDString.uppercaseString]) {
                [peripheral readValueForCharacteristic:characteristic];
            }
            
        }
        [chars addObject:characteristic];
        
    }];
    
    [servicesDic setObject:[chars copy] forKey:service.UUID.UUIDString.uppercaseString];
    model.services = servicesDic;
    model.otaCharacteristics = otaArr;
    [self.peripheralCharacteristicDictionary setObject:model forKey:localName];
    
    BOOL end = YES;
    for (NSArray *array in servicesDic.allValues) {
        if (array.count == 0) {
            end = NO;
            break;
        }
    }
    if (end) {
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            
            self.isConnectDevice = YES;
            //            [self isConnectSuccess:peripheral];
            
        });
    }
    BlueServiceNSLog(@"特征属性遍历%@完成===", end ? @"" : @"未");
    
    
    
}

#pragma mark ------------------------------------------ 蓝牙通信相关 ---------------------------------------------
//当订阅状态发生变化的时候会回调
-(void)peripheral:(CBPeripheral *)peripheral didUpdateNotificationStateForCharacteristic:(CBCharacteristic *)characteristic error:(NSError *)error {
    
    //    NSLog(@"characteristic==%@,error==%@" , characteristic.UUID.UUIDString , error.localizedDescription);
    
}
*/

#pragma mark 写入成功
- (void)peripheral:(CBPeripheral *)peripheral didWriteValueForCharacteristic:(CBCharacteristic *)characteristic  error:(NSError *)error {
    if (error) {
        NSLog(@"Error writing characteristic value: %@",
              [error localizedDescription]);
        return;
    }
    BlueNSLog(@"写入成功");
}

/*
#pragma mark - blue response 收到数据,并且通知代理接受数据，并实现相关功能
- (void)peripheral:(CBPeripheral *)peripheral didUpdateValueForCharacteristic:(CBCharacteristic *)characteristic error:(NSError *)error
{
    //    BlueNSLog(@"CBCharacteristic --- %@" , characteristic.UUID);
    
    
    //    [self writeFile:[NSString stringWithFormat:@"data:%@,characteristic:%@" , [characteristic.value hexString] , characteristic.UUID.UUIDString] type:@"response"];
    
    NSLog(@"didUpdateValueForCharacteristic -- %@" , [characteristic.value hexString]);
    
    if (!characteristic.value) {
        return;
    }
    //是ota返回数据
    if ([characteristic.service.UUID.UUIDString.uppercaseString isEqualToString:kFRKOTAServiceUUID.uppercaseString]
        || [characteristic.service.UUID.UUIDString.uppercaseString isEqualToString:kFRKOTAServiceUUID2.uppercaseString]) {
        [[NSNotificationCenter defaultCenter] postNotificationName:@"kFRKOTADataNotification" object:characteristic.value];
        return;
    }
    
    if ([characteristic.service.UUID.UUIDString.uppercaseString isEqualToString:kLINKEDOTAServiceUUID.uppercaseString]) {
        [[NSNotificationCenter defaultCenter] postNotificationName:@"kLinkedsemiOTADataNotification" object:characteristic];
        return;
    }
    
    NSString *localName = [BluePeripheral peripheralLocalName:peripheral];
    NSArray *array = [BluePeripheral peripheralInfoCharacteristicsArray];
    
    if ([array containsObject: characteristic.UUID.UUIDString.uppercaseString]) {
        
        NSString *str = [NSObject hexToAscString:characteristic.value];
        NSLog(@"characteristic===%@,value==%@" , characteristic , str);
        BluetoothModel *model = [self.peripheralCharacteristicDictionary objectForKey:localName];
        
        NSMutableDictionary *info = model.infomation ? [model.infomation mutableCopy] : [NSMutableDictionary dictionary];
        [info setObject:str forKey:characteristic.UUID.UUIDString.uppercaseString];
        model.infomation = info.copy;
        
        [self.peripheralCharacteristicDictionary setObject:model forKey:model.localName];
        
        [self isConnectSuccess:peripheral];
        
        return;
    }
    
    
    NSDictionary *dic  = @{
        BlueCharacteristic : characteristic,
        BlueDevice : peripheral,
        @"data" : characteristic.value,
        BlueDeviceType: ([characteristic.UUID.UUIDString isEqualToString:kHeartBatteryCharacteristics] || [characteristic.UUID.UUIDString isEqualToString:kHeartDataCharacteristics]) ? [@(HeartEquipment) stringValue] : [BluetoothManager sharedInstance].equipmentType,
    };
    
    [[NSNotificationCenter defaultCenter] postNotificationName:BLEDataNotification object:dic];
    
    
    
}


#pragma mark ------------------------------------------ 蓝牙通信相关结束 ---------------------------------------------

//为了在连接成功后能后判断是否有ota更新，必须保证已经获取到设备模块号，版本号，ota服务
-(void)isConnectSuccess:(CBPeripheral *)peripheral {
  
    
    
}


//断开连接/解除绑定/手机蓝牙关闭（清除所有类型下的连接设备）
#pragma mark - 从已经连接的字典中移除断开连接的设备
-(void)removeConnectDevice:(CBPeripheral *)peripheral  {
    
    self.isConnectDevice = NO;
    self.isGetDeviceInfo = NO;
    
    void(^didDisconnectBlock)(NSString *) =  ^(NSString *localName){
        NSLog(@"removeConnectDevice_type -- %@" , localName);
        NSMutableDictionary *dic = [NSMutableDictionary dictionaryWithDictionary:self.connectDic];
        
        NSLog(@"before_removeConnectDevice_type -- %@" , self.connectDic);
        if ([dic.allValues containsObject:localName]) {
            NSInteger index = [dic.allValues indexOfObject:localName];
            NSString *key = [dic.allKeys objectAtIndex:index];
            [dic removeObjectForKey:key];
        }
        BluetoothModel *bm;
        self.connectDic = [dic copy];
        if ([self.peripheralCharacteristicDictionary.allKeys containsObject:localName]) {
            bm = [self.peripheralCharacteristicDictionary objectForKey:localName];
            [self.peripheralCharacteristicDictionary removeObjectForKey:localName];
        }
        
        
        [[NSNotificationCenter defaultCenter]postNotificationName:kBlueDisConnectSuccessNotification object:@{@"model" : bm , BlueDeviceType:bm.type}];
       
        NSLog(@"after_removeConnectDevice_type -- %@" , self.connectDic);
    };
    
    
    NSString *localName ;
    for (BluetoothModel *model in self.peripheralCharacteristicDictionary.allValues) {
        if (model.peripheral.state == CBPeripheralStateDisconnected) {
            NSLog(@"remove__model_name==%@,state==%@" , model.localName , @(model.peripheral.state));
            localName = model.localName;
            didDisconnectBlock(localName);
        }
        //        NSLog(@"model.state==%@" , @(model.peripheral.state));
    }
    
    
}


#pragma mark - 蓝牙关闭后，断开所有设备连接
-(void)clearAllPeripheral {
    
    if ([self.connectDic.allKeys containsObject:[@(FatScaleEquipment) stringValue]]) {
        //连着体脂秤 断开
        NSString *type = [@(FatScaleEquipment) stringValue];
        [[NSNotificationCenter defaultCenter] postNotificationName:kBlueDisConnectNotification object:@{@"macAddress" : [self.connectDic valueForKey:type] , BlueDeviceType : type}];
    }
    
    for (BluetoothModel *m in self.peripheralCharacteristicDictionary.allValues) {
        if(m.peripheral) {
            [self.centralManager cancelPeripheralConnection:m.peripheral];
        }
    }
    
    self.connectDic = [NSDictionary dictionary];
    
    
    self.peripheralArray = [NSArray array];
    self.peripheralNameArray = [NSArray array];
    
    self.isGetDeviceInfo = NO;
    self.isConnectDevice = NO;
    
    
    self.peripheralCharacteristicDictionary = [NSMutableDictionary dictionary];
}

*/



- (BOOL)isConnectPeripheralName:(NSString *)name {
    return [[BluetoothManager sharedInstance].connectDic.allValues containsObject:name];
}

#pragma mark - 提示打开蓝牙权限
-(void)alertOpenBlue {
    [AppDelegate errorView:@"蓝牙未开启,请开启蓝牙权限"];
}

- (void)sendData:(NSData *)data type:(NSString *)type {
    BluetoothModel *model = [BlueDataStorageManager connectBMFromProductID:type];
    if (model && model.peripheral && data.length) {
        ///发送
        if (!model.writeCharacteristic) {
            [BluePeripheral setWriteCharacteristicFromType:type];
        }
        
        CBCharacteristic *write = model.writeCharacteristic;
        if (write) {
            if ((write.properties & CBCharacteristicPropertyWrite) == CBCharacteristicPropertyWrite) {
                [model.peripheral writeValue:data forCharacteristic:write type:CBCharacteristicWriteWithResponse];
//                BLog(@"write:%@:%@:%@",@((long)[[NSDate date] timeIntervalSince1970] * 1000) ,write.UUID.UUIDString, [data hexString]);
                if (!isDistribute) {
                    NSLog(@"CBCharacteristicWriteWithResponse: ---");
                }
            } else {
                [model.peripheral writeValue:data forCharacteristic:write type:CBCharacteristicWriteWithoutResponse];
//                BLog(@"write:%    @:%@:%@",@((long)[[NSDate date] timeIntervalSince1970] * 1000) ,write.UUID.UUIDString, [data hexString]);
                if (!isDistribute) {
                    NSLog(@"CBCharacteristicWriteWithoutResponse: ---");
                }
            }
        }
    }
    
    return;
    
    //    [self writeFile:[NSString stringWithFormat:@"data:%@,characteristic:%@" , data , write.UUID.UUIDString] type:@"send"];
}





/*
#pragma mark - 是否包含ota服务
- (BOOL)hasOTAService:(NSString *)type name:(NSString *)name {
    BluetoothModel *model;
    if([name isNotEmpty]) {
        model = [self.peripheralCharacteristicDictionary objectForKey:name];
    } else {
        model =[BluetoothModel modelFromEquipmentType:type];// [self.peripheralCharacteristicDictionary objectForKey:name];
    }
    return model.otaCharacteristics.count > 0;
}



- (NSString *)typeOfPer {
    if ([[self.connectDeviceInfo objectForKey:@"oneLevelTypeId"] isEqual:[@(HeartEquipment) stringValue]] ) {
        return [self.connectDeviceInfo objectForKey:@"oneLevelTypeId"] ;
    }
    
    if ([[self.connectDeviceInfo objectForKey:BlueDeviceType] isEqual:[@(HeartEquipment) stringValue]] ) {
        return [self.connectDeviceInfo objectForKey:BlueDeviceType] ;
    }
    
    if ([[self.connectDeviceInfo objectForKey:@"productId"] isEqual:[@(HeartEquipment) stringValue]] ) {
        return [self.connectDeviceInfo objectForKey:@"productId"] ;
    }
    return self.equipmentType;
}
*/

+ (BOOL)isConnectEquipmentType:(NSString *)type {
    //    NSLog(@"isConnectEquipmentType==%@" , [BluetoothManager sharedInstance].connectDic);
    return [BlueDataStorageManager isConnectDeviceWithProductID:type];
}

//-(void)setEquipmentType:(NSString *)equipmentType {
//    if(equipmentType.intValue != HeartEquipment) {
//        _equipmentType = equipmentType;
//    }
//}

/*
+ (BOOL)isSupportEquipment:(NSString *)name {
    BOOL res = NO;
    NSArray *arr = [BluetoothManager sharedInstance].prefixArray;
    for (NSString *pre in arr) {
        if ([name hasPrefix:pre]) {
            res = YES;
            break;
        }
    }
    
    return res;
}
 */
@end
