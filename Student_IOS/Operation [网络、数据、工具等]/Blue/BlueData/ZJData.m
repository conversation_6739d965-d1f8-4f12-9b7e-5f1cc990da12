//
//  ZJData.m
//  Student_IOS
//
//  Created by MacPro on 2023/3/2.
//  智健协议解析

#import "ZJData.h"


//智健协议 不同类型数据长度 2021-12-10
#define kZJTreamillDataLength       17
#define kZJCarDeviceDataLength      15
#define kZJCarSportDataLength       13


@implementation ZJData

+ (id)modelFromData:(NSData *)data type:(NSString *)type {
    BaseEquipDataModel *model;
    switch (type.intValue) {
        case TreadmillEquipment:
        {
            model = [ZJData treamillModelWithParaseData:data];
        }
            break;
        case EllipticalEquipment:
        case BicycleEquipment:
        case BoatEquipment:
        case PowerEquipment:
        case StairClimbEquipment:
        {
            model = [ZJData carModelWithParaseData:data];
        }
            break;
        case SkipRopeEquipment:
        {
            model = [ZJData skipModelWithParaseData:data];
        }
            break;
        default:
            break;
    }
    
    model.type = @(type.intValue);
    model.deviceOriginData = data;
    return model;
}

+ (id)treamillModelWithParaseData:(NSData *)data {
    BaseEquipDataModel *m;
    if ([BloothTool isSamePrefix:data other:[BloothTool treadmillData] start:0 offset:2] && data.length == kZJTreamillDataLength) {
        m = [BaseEquipDataModel new];
        m.speed = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(3, 1)]] / 10.0);
        BlueNSLog(@"speed === %@" , m.speed);
//        m.gradient = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(4, 1)]]);
        m.gradient = @([NSObject input0x16String:[data subdataWithRange:NSMakeRange(4, 1)].hexString]);
        m.totalTimeSecond = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(5, 2)]]);
        
        int distance  = [BaseEquipData convertData:[data subdataWithRange:NSMakeRange(7, 2)]];
        int real_distance = [BaseEquipData realDistance:distance];
        m.totalDistance = @(real_distance);
        m.energy = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(9, 2)]] / 10.0);
        
        m.count = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(11, 2)]]);
        m.deviceRate = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(13, 1)]]);
    }
    return m;
}

+ (id)carModelWithParaseData:(NSData *)data {
    BaseEquipDataModel *m ;
    if ([BloothTool isSamePrefix:data other:[BloothTool getDeviceStatusData] start:0 offset:2] && data.length == kZJCarDeviceDataLength) {
        //15个字节
        m = [BaseEquipDataModel new];
        m.speed = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(3, 2)]] * 0.01);
        m.drag = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(5, 1)]]);
        m.spm = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(6, 2)]]);
        m.deviceRate = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(8, 1)]]);
        BlueNSLog(@"power === %d" , [BaseEquipData convertData:[data subdataWithRange:NSMakeRange(9, 2)]]);
        m.power = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(9, 2)]] * 0.1);
        m.gradient = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(11, 1)]]);
        
    } else if([BloothTool isSamePrefix:data other:[BloothTool getSportData] start:0 offset:2] && data.length == kZJCarSportDataLength) {
        //13个字节
        m = [BaseEquipDataModel new];
        m.totalTimeSecond = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(3, 2)]]);
        int distance  = [BaseEquipData convertData:[data subdataWithRange:NSMakeRange(5, 2)]];
        int real_distance = [BaseEquipData realDistance:distance];
        
        BlueNSLog(@"real_distance ---- %d" , real_distance);
        m.totalDistance = @(real_distance);
        m.energy = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(7, 2)]] * 0.1);
        BlueNSLog(@"enery---%@" , m.energy);
        m.count = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(9, 2)]]);
    }
    return m;
}

+ (id)skipModelWithParaseData:(NSData *)data {
    BaseEquipDataModel *model;
    if ([BloothTool isSamePrefix:data other:[BloothTool jumpElectric] start:1 offset:2]) {
        //电量
        model = [BaseEquipDataModel new];
        model.electric = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(3, 1)]]);
    } else if([BloothTool isSamePrefix:data other:[BloothTool passCountAndTimeOrder] start:1 offset:2]){
        long len = [BaseEquipData convertData:[data subdataWithRange:NSMakeRange(0, 1)]];
        if (data.length < len) {//判断跳绳长度是否符合协议 21-12-13
            return model;
        }
        model = [BaseEquipDataModel new];
        //其他数据
        model.count = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(3, 2)]]);
        model.totalTimeSecond = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(5, 2)]]);
        model.targetNumber = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(8, 2)]]);
        model.targetTime = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(10, 2)]]);
        model.mode = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(7, 1)]]);
        model.finishTarget = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(12, 1)]]);
        model.speed = @(model.totalTimeSecond.intValue == 0 ? 0 : model.count.intValue * 1.0 / model.totalTimeSecond.intValue * 60);
    }
    return model;
}

@end
