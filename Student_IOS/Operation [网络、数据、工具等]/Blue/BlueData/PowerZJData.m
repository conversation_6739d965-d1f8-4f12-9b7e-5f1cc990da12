//
//  PowerZJData.m
//  ZBlueSDK
//
//  Created by MacPro on 2024/5/13.
//

#import "PowerZJData.h"

#import "ZJData.h"
#import "BlueConfig.h"
#import "BaseEquipData.h"
#import "BlueCommandData.h"
#import "NSObject+Data.h"
#import "BluetoothModel.h"

//智健协议 不同类型数据长度 2021-12-10
#define kZJTreamillDataLength       17
#define kZJCarDeviceDataLength      15
#define kZJCarSportDataLength       13




@implementation PowerZJData


+ (id)modelFromData:(NSData *)data type:(NSString *)type {
    BaseEquipDataModel *model ;
    switch (type.intValue) {
        case PowerEquipment:
        {
            model = [PowerZJData powerModelWithParaseData:data];
        }
        
        default:
            break;
    }
    
    model.type = @(type.intValue);
    return model;
    
}


+(id)powerModelWithParaseData:(NSData *)data {
    
    BaseEquipDataModel *m ;
    if ([BloothTool isSamePrefix:data other:[ZJCommandData getDeviceStatusData] start:0 offset:2] && data.length == kZJCarDeviceDataLength) {
        //15个字节
        m = [BaseEquipDataModel new];
        m.status =  @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(2, 1)]]);
        m.weight = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(3, 2)]] * 0.01);
        m.drag = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(5, 1)]]);
        m.spm = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(6, 2)]]);
        m.deviceRate = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(8, 1)]]);
        m.power = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(9, 2)]] * 0.1);
        m.gradient = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(11, 1)]]);
        
        
    } else if([BloothTool isSamePrefix:data other:[ZJCommandData getSportData] start:0 offset:2] && data.length == kZJCarSportDataLength) {
        //13个字节
        m = [BaseEquipDataModel new];
        m.totalTimeSecond = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(3, 2)]]);
        int distance  = [BaseEquipData convertData:[data subdataWithRange:NSMakeRange(5, 2)]];
        int real_distance = [BaseEquipData realDistance:distance];
        m.totalDistance = @(real_distance);
        m.energy = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(7, 2)]] * 0.1);
        m.count = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(9, 2)]]);
        
    }
    return m;
}


//
+(NSDictionary *)deviceInfoParasing:(NSNotification *)notification {
    NSData *data = [notification.object objectForKey:@"data"];
    NSString *type = [notification.object objectForKey:@"type"];
    NSString *localName = [notification.object objectForKey:@"localName"];
    NSData *unitData = [data subdataWithRange:NSMakeRange(5, 1)];
    //8个字节，第一个字节是单位
    NSArray *arr = [self arrayFromData:unitData];
    
    NSMutableDictionary *info = [NSMutableDictionary dictionary];
    if(type.intValue == TreadmillEquipment) {
    
        [info setObject:@([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(3, 1)]]) forKey:@"maxSlope"];
        [info setObject:@([arr.firstObject intValue]) forKey:@"unit"];
        
    } else if (type.intValue == BicycleEquipment
              || type.intValue == EllipticalEquipment
              || type.intValue == BoatEquipment) {
        
        [info setObject:@([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(3, 1)]]) forKey:@"maxResistance"];
        [info setObject:@([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(4, 1)]]) forKey:@"maxSlope"];
        [info setObject:@([arr.firstObject intValue]) forKey:@"unit"];
        
    }
    [info setObject:localName forKey:@"localName"];
    [info setObject:type forKey:@"type"];
    
    return info;
}


+(NSArray *)arrayFromData:(NSData *)data {
    
    @autoreleasepool {
        
        
        NSData *small = [NSObject dataTransfromBigOrSmall:data];
//        BlueNSLog(@"small -- %@" , small);
        NSString *hex = [small hexString];
//        BlueNSLog(@"十六进制———— %@" ,hex);
        NSString *binary = [NSObject getBinaryByHex:hex];
//        BlueNSLog(@"binary -- %@" , binary);
        
        NSMutableArray *arr = [NSMutableArray array];
        for (int i = 0; i<binary.length; i++) {
            [arr addObject:[binary substringWithRange:NSMakeRange(i, 1)]];
        }
        
        
        NSArray * reversedArray = [[arr reverseObjectEnumerator] allObjects];
        
        return reversedArray;
    }
    
}

@end
