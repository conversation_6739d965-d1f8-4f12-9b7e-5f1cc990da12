//
//  FTMSData.m
//  Student_IOS
//
//  Created by MacPro on 2021/7/28.
//

#import "FTMSData.h"
#import "BaseEquipData.h"

@implementation FTMSData


+ (id)modelFromData:(NSData *)data type:(NSString *)type {
    BaseEquipDataModel *model ;
    switch (type.intValue) {
        case BicycleEquipment: {
            model = [FTMSData BicyleWithParaseData:data];
        } break;
            
        case EllipticalEquipment: {
            model = [FTMSData CrossTrainerWithParaseData:data];
        } break;
            
        case BoatEquipment: {
            model = [FTMSData RowerWithParaseData:data];
        } break;
            
        case TreadmillEquipment: {
            model = [FTMSData treadmillWithParaseData:data];
        } break;
            
        default:
            break;
    }
    
    model.type = @(type.intValue);
    model.deviceOriginData = data;
    return model;
}

+ (id)treadmillWithParaseData:(NSData *)data {
    
    NSData *flags = [data subdataWithRange:NSMakeRange(0, 2)];
    NSArray *array = [FTMSData arrayFromData:flags];
    BaseEquipDataModel *model = [BaseEquipDataModel new];
    
    NSArray *bicyArray = @[
        @(TREADMILL_TYPE_MORE_DATA         ),//(U16)0x0001U    /* 其他数据 - 瞬时速度 */
        @(TREADMILL_TYPE_AVERAGE_SPEED     ),//(U16)0x0002U    /* 平均速度 */
        @(TREADMILL_TYPE_TOTAL_DISTANCE    ),//(U16)0x0004U    /* 总距离 */
        @(TREADMILL_TYPE_INC_AND_RAMP_SET  ),//(U16)0x0008U    /* 坡度 */
        @(TREADMILL_TYPE_ELEVATION_GAIN    ),//(U16)0x0010U    /* 坡度增益 */
        @(TREADMILL_TYPE_INSTANTANEOUS_PACE),//(U16)0x0020U    /* 瞬时步速 */
        @(TREADMILL_TYPE_AVERAGE_PACE      ),//(U16)0x0040U    /* 平均步速 */
        @(TREADMILL_TYPE_EXPENDED_ENERGY   ),//(U16)0x0080U    /* 消耗能量 */
        @(TREADMILL_TYPE_HEART_RATE        ),//(U16)0x0100U    /* 心率 */
        @(TREADMILL_TYPE_METABOLIC_EQU     ),//(U16)0x0200U    /* 代谢当量 */
        @(TREADMILL_TYPE_ELAPSED_TIME      ),//(U16)0x0400U    /* 运动时间 */
        @(TREADMILL_TYPE_REMAINING_TIME    ),//(U16)0x0800U    /* 剩余时间 */
        @(TREADMILL_TYPE_FORCE_AND_POWEROUT),//(U16)0x1000U    /* 皮带动力输出的力和功率输出 */
        @(TREADMILL_TYPE_STEPS             ),//(U16)0x2000U    /* 步数 */
    ];
    
    NSInteger dataTempIndex = 2;
    for (int i = 0; i < bicyArray.count; i++) {
        NSString *flag = array[i];
        
        if (([bicyArray[i] intValue] == TREADMILL_TYPE_MORE_DATA && (!flag.boolValue))
            || ([bicyArray[i] intValue] != TREADMILL_TYPE_MORE_DATA && flag.boolValue)) {
            
            switch ([bicyArray[i] intValue]) {
                {
                case TREADMILL_TYPE_MORE_DATA:
                    
                    model.speed = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(dataTempIndex, 2)]] * 0.01);
                    dataTempIndex  = dataTempIndex + 2;
                }
                    break;
                case TREADMILL_TYPE_AVERAGE_SPEED:
                {
                    model.avgSpeed = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(dataTempIndex, 2)]] * 0.01);
                    dataTempIndex  = dataTempIndex + 2;
                }
                    break;
                case TREADMILL_TYPE_TOTAL_DISTANCE:
                {
                    NSData *sub = [data subdataWithRange:NSMakeRange(dataTempIndex, 3)];
                    model.totalDistance = @([BaseEquipData convertData:sub]);
                    dataTempIndex  = dataTempIndex + 3;
                }
                    break;
                case TREADMILL_TYPE_INC_AND_RAMP_SET:
                {
                    model.gradient = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(dataTempIndex, 2)]] / 10);
                    dataTempIndex = dataTempIndex + 2 + 2;
                    //Inclination sint16  Ramp Angle Setting sint16
                }
                    break;
                case TREADMILL_TYPE_ELEVATION_GAIN:
                {
                    ///坡度增益 正 负
                    dataTempIndex = dataTempIndex + 2 + 2;
                    ///Positive Elevation Gain uint16 Negative Elevation Gain  uint16
                }
                    break;
                case TREADMILL_TYPE_INSTANTANEOUS_PACE :
                {
                    ///瞬时步速
                    model.spm = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(dataTempIndex, 1)]] * 0.1);
                    dataTempIndex  = dataTempIndex + 1;
                }
                    break;
                case TREADMILL_TYPE_AVERAGE_PACE:
                {
                    model.avgSpm = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(dataTempIndex, 1)]] * 0.1);
                    dataTempIndex  = dataTempIndex + 1;
                }
                    break;
                case TREADMILL_TYPE_EXPENDED_ENERGY:
                {
                    model.energy = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(dataTempIndex, 2)]]);
                    dataTempIndex  = dataTempIndex + 2 + 2 + 1;//多两个数据位（Energy Per Hour：2；Energy Per Minute：1）
                }
                    break;
                case TREADMILL_TYPE_HEART_RATE:
                {
                    model.deviceRate = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(dataTempIndex, 1)]]);
                    dataTempIndex = dataTempIndex + 1;
                }
                    break;
                case TREADMILL_TYPE_METABOLIC_EQU:
                {
                    ///代谢当量 0.1
                    dataTempIndex = dataTempIndex + 1;
                }
                    break;
                case TREADMILL_TYPE_ELAPSED_TIME:
                {
                    model.totalTimeSecond = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(dataTempIndex, 2)]]);
                    dataTempIndex = dataTempIndex + 2;
                }
                    break;
                case TREADMILL_TYPE_REMAINING_TIME:
                {
                    model.remainingTimeSecond = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(dataTempIndex, 2)]]);
                    dataTempIndex = dataTempIndex + 2;
                }
                    break;
                case TREADMILL_TYPE_FORCE_AND_POWEROUT :
                {
                    ///皮带动力输出的力和功率输出 sint16
                    dataTempIndex = dataTempIndex + 2;
                }
                    break;
                    
                case TREADMILL_TYPE_STEPS :
                {
                    ///步数 uint24
                    model.count = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(dataTempIndex, 3)]]);
                    dataTempIndex = dataTempIndex + 3;
                }
                    break;
                    
                default:
                    break;
            }
        }
    }
    
    return model;
}

+ (id)BicyleWithParaseData:(NSData *)data {
    
    NSData *flags = [data subdataWithRange:NSMakeRange(0, 2)];
    NSArray *array = [FTMSData arrayFromData:flags];
    BaseEquipDataModel *model = [BaseEquipDataModel new];
    
    NSArray *bicyArray = @[
        @(INDOOR_BIKE_TYPE_MORE_DATA),                // (U16)0x0001U    /* 其他数据 - 瞬时速度 */
        @(INDOOR_BIKE_TYPE_AVERAGE_SPEED ),           // (U16)0x0002U    /* 平均速度 */
        @(INDOOR_BIKE_TYPE_INSTANTANEOUS_CADENCE),    // (U16)0x0004U    /* 瞬时踏频 */
        @(INDOOR_BIKE_TYPE_AVERAGE_CADENCE),          // (U16)0x0008U    /* 平均踏频 */
        @(INDOOR_BIKE_TYPE_TOTAL_DISTANCE),           // (U16)0x0010U    /* 总距离 */
        @(INDOOR_BIKE_TYPE_RESISTANCE_LEVEL),         // (U16)0x0020U    /* 阻力等级 */
        @(INDOOR_BIKE_TYPE_INSTANTANEOUS_POWER),      // (U16)0x0040U    /* 瞬时功率 */
        @(INDOOR_BIKE_TYPE_AVERAGE_POWER),            // (U16)0x0080U    /* 平均功率 */
        @(INDOOR_BIKE_TYPE_EXPENDED_ENERGY),          // (U16)0x0100U    /* 消耗能量 */
        @(INDOOR_BIKE_TYPE_HEART_RATE),               // (U16)0x0200U    /* 心率 */
        @(INDOOR_BIKE_TYPE_METABOLIC_EQU),            // (U16)0x0400U    /* 代谢当量 */
        @(INDOOR_BIKE_TYPE_ELAPSED_TIME),             // (U16)0x0800U    /* 运动时间 */
        @(INDOOR_BIKE_TYPE_REMAINING_TIME),           // (U16)0x1000U    /* 剩余时间 */
    ];
    
    
    NSInteger dataTempIndex = 2;
    
    for (int i = 0; i < bicyArray.count; i++) {
        NSString *flag = array[i];
        
        if (([bicyArray[i] intValue] == INDOOR_BIKE_TYPE_MORE_DATA && (!flag.boolValue))
            || ([bicyArray[i] intValue] != INDOOR_BIKE_TYPE_MORE_DATA && flag.boolValue)) {
            
            switch ([bicyArray[i] intValue]) {
                case INDOOR_BIKE_TYPE_MORE_DATA:
                {
                    model.speed = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(dataTempIndex, 2)]] * 0.01);
                    dataTempIndex  = dataTempIndex + 2;
                }
                    break;
                case INDOOR_BIKE_TYPE_AVERAGE_SPEED:
                {
                    model.avgSpeed = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(dataTempIndex, 2)]] * 0.01);
                    dataTempIndex  = dataTempIndex + 2;
                }
                    break;
                case INDOOR_BIKE_TYPE_INSTANTANEOUS_CADENCE:
                {
                    NSLog(@"sub_spm_data -- %@" ,[data subdataWithRange:NSMakeRange(dataTempIndex, 2)] );
                    model.spm = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(dataTempIndex, 2)]] / 2);
                    dataTempIndex  = dataTempIndex + 2;
                    
                }
                    break;
                case INDOOR_BIKE_TYPE_AVERAGE_CADENCE:
                {
                    model.avgSpm = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(dataTempIndex, 2)]]);
                    dataTempIndex  = dataTempIndex + 2;
                }
                    break;
                case INDOOR_BIKE_TYPE_TOTAL_DISTANCE:
                {
                    NSData *sub = [data subdataWithRange:NSMakeRange(dataTempIndex, 3)];
                    
                    model.totalDistance = @([BaseEquipData convertData:sub]);
                    //                    model.count = @((int)(model.totalDistance.intValue / 4.6));
                    dataTempIndex  = dataTempIndex + 3;
                }
                    break;
                case INDOOR_BIKE_TYPE_RESISTANCE_LEVEL:
                {
                    model.drag = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(dataTempIndex, 2)]]);
                    dataTempIndex = dataTempIndex + 2;
                }
                    break;
                case INDOOR_BIKE_TYPE_INSTANTANEOUS_POWER:
                {
                    model.power = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(dataTempIndex, 2)]]);
                    dataTempIndex = dataTempIndex + 2;
                }
                    break;
                case INDOOR_BIKE_TYPE_AVERAGE_POWER:
                {
                    model.avgPower = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(dataTempIndex, 2)]]);
                    dataTempIndex  = dataTempIndex + 2;
                }
                    break;
                case INDOOR_BIKE_TYPE_EXPENDED_ENERGY:
                {
                    model.energy = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(dataTempIndex, 2)]]);
                    dataTempIndex  = dataTempIndex + 2 + 2 + 1;//多两个数据位（Energy Per Hour：2；Energy Per Minute：1）
                    
                }
                    break;
                case INDOOR_BIKE_TYPE_HEART_RATE:
                {
                    model.deviceRate = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(dataTempIndex, 1)]]);
                    dataTempIndex = dataTempIndex + 1;
                }
                    break;
                case INDOOR_BIKE_TYPE_ELAPSED_TIME:
                {
                    model.totalTimeSecond = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(dataTempIndex, 2)]]);
                    dataTempIndex = dataTempIndex + 2;
                }
                    break;
                case INDOOR_BIKE_TYPE_REMAINING_TIME:
                {
                    model.remainingTimeSecond = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(dataTempIndex, 2)]]);
                    dataTempIndex = dataTempIndex + 2;
                }
                    break;
                    
                default:
                    break;
            }
        }
    }
    
    return model;
}

+ (id)CrossTrainerWithParaseData:(NSData *)data {
    
    NSData *flags = [data subdataWithRange:NSMakeRange(0, 2)];
    NSArray *array = [FTMSData arrayFromData:flags];
    BaseEquipDataModel *model = [BaseEquipDataModel new];
    
    NSArray *crossArray = @[
        @(CROSS_TRAINER_TYPE_MORE_DATA),            // (U16)0x0001U    /* 其他数据 - 瞬时速度 */
        @(CROSS_TRAINER_TYPE_AVERAGE_SPEED),        // (U16)0x0002U    /* 平均速度 */
        @(CROSS_TRAINER_TYPE_TOTAL_DISTANCE),       // (U16)0x0004U    /* 总距离 */
        @(CROSS_TRAINER_TYPE_STEP_COUNT ),          // (U16)0x0008U    /* 总步数 */
        @(CROSS_TRAINER_TYPE_STRIDE_COUNT),         // (U16)0x0010U    /* 总大步数（两个Step是一个Stride） */
        @(CROSS_TRAINER_TYPE_ELEVATION_GAIN),       // (U16)0x0020U    /* 坡度增益 */
        @(CROSS_TRAINER_TYPE_INC_AND_RAMP_SET),     // (U16)0x0040U    /* 坡度 */
        @(CROSS_TRAINER_TYPE_RESISTANCE_LEVEL),     // (U16)0x0080U    /* 阻力等级 */
        @(CROSS_TRAINER_TYPE_INSTANTANEOUS_POWER),  // (U16)0x0100U    /* 瞬时功率 */
        @(CROSS_TRAINER_TYPE_AVERAGE_POWER),        // (U16)0x0200U    /* 平均功率 */
        @(CROSS_TRAINER_TYPE_EXPENDED_ENERGY),      // (U16)0x0400U    /* 消耗能量 */
        @(CROSS_TRAINER_TYPE_HEART_RATE),           // (U16)0x0800U    /* 心率 */
        @(CROSS_TRAINER_TYPE_METABOLIC_EQU),        // (U16)0x1000U    /* 代谢当量 */
        @(CROSS_TRAINER_TYPE_ELAPSED_TIME ),        // (U16)0x2000U    /* 运动时间 */
        @(CROSS_TRAINER_TYPE_REMAINING_TIME ),      // (U16)0x4000U    /* 剩余时间 */
        @(CROSS_TRAINER_TYPE_MOVEMENT_DIRECTION),   // (U16)0x8000U    /* 运行方向 00H：向前    01H：向后 */
    ];
    
    
    NSInteger dataTempIndex = 2;
    dataTempIndex += 1;// 方向 固定存在，不用flag位判断
    
    for (int i = 0; i < crossArray.count; i++) {
        NSString *flag = array[i];
        
        if (([crossArray[i] intValue] == CROSS_TRAINER_TYPE_MORE_DATA && (!flag.boolValue))
            || ([crossArray[i] intValue] != CROSS_TRAINER_TYPE_MORE_DATA && flag.boolValue)) {
            
            switch ([crossArray[i] intValue]) {
                    
                case CROSS_TRAINER_TYPE_MORE_DATA:
                {
                    model.speed = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(dataTempIndex, 2)]] * 0.01);
                    dataTempIndex  = dataTempIndex + 2;
                }
                    break;
                case CROSS_TRAINER_TYPE_AVERAGE_SPEED:
                {
                    model.avgSpeed = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(dataTempIndex, 2)]] * 0.01);
                    dataTempIndex  = dataTempIndex + 2;
                }
                    break;
                case CROSS_TRAINER_TYPE_TOTAL_DISTANCE:
                {
                    model.totalDistance = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(dataTempIndex, 3)]]);
                    dataTempIndex  = dataTempIndex + 3;
                }
                    break;
                case CROSS_TRAINER_TYPE_STEP_COUNT:
                {
                    model.spm = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(dataTempIndex, 2)]]);
                    dataTempIndex  = dataTempIndex + 2 ;//(Step Per Minute , Average Step Rate)
                    model.avgSpm = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(dataTempIndex, 2)]]);
                    dataTempIndex  = dataTempIndex + 2 ;
                }
                    break;
                case CROSS_TRAINER_TYPE_STRIDE_COUNT:
                {
                    model.count = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(dataTempIndex, 2)]] / 10);
                    dataTempIndex  = dataTempIndex + 2;
                }
                    break;
                case CROSS_TRAINER_TYPE_ELEVATION_GAIN:
                {
                    ///23-08-30 坡度增益 代表海拔增益 （分为 正负增益两个指标）
                    dataTempIndex = dataTempIndex + 2 + 2;
                    // Positive Elevation Gain:2;Negative Elevation Gain:2
                }
                    break;
                case CROSS_TRAINER_TYPE_INC_AND_RAMP_SET:
                {
                    ///23-08-30 坡度  代表坡度 （分为 坡度 和  Ramp Angle Setting两个指标）
                    model.gradient = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(dataTempIndex, 2)]] / 10);
                    dataTempIndex = dataTempIndex + 2  + 2;
                    //Ramp Angle Setting:2;
                }
                    break;
                case CROSS_TRAINER_TYPE_RESISTANCE_LEVEL:
                {
                    model.drag = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(dataTempIndex, 2)]] / 10);
                    dataTempIndex  = dataTempIndex + 2;
                }
                    break;
                case CROSS_TRAINER_TYPE_INSTANTANEOUS_POWER:
                {
                    model.power = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(dataTempIndex, 2)]]);
                    dataTempIndex  = dataTempIndex + 2 ;
                }
                    break;
                case CROSS_TRAINER_TYPE_AVERAGE_POWER:
                {
                    model.avgPower = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(dataTempIndex, 2)]]);
                    dataTempIndex = dataTempIndex + 2;
                }
                    break;
                case CROSS_TRAINER_TYPE_EXPENDED_ENERGY:
                {
                    model.energy = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(dataTempIndex, 2)]]);
                    dataTempIndex = dataTempIndex + 2 + 2 + 1;
                    //Total Energy:2;Energy Per Hour:2;Energy Per Minute:1
                }
                    break;
                case CROSS_TRAINER_TYPE_HEART_RATE:
                {
                    model.deviceRate = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(dataTempIndex, 1)]]);
                    dataTempIndex = dataTempIndex + 1;
                }
                    break;
                case CROSS_TRAINER_TYPE_METABOLIC_EQU:
                {
                    
                    dataTempIndex = dataTempIndex + 1;
                    //Metabolic Equivalent :1  0.1
                }
                    break;
                case CROSS_TRAINER_TYPE_ELAPSED_TIME:
                {
                    model.totalTimeSecond = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(dataTempIndex, 2)]]);
                    dataTempIndex = dataTempIndex + 2;
                }
                    break;
                    
                case CROSS_TRAINER_TYPE_REMAINING_TIME:
                {
                    model.remainingTimeSecond = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(dataTempIndex, 2)]]);
                    dataTempIndex = dataTempIndex + 2;
                }
                    break;
                    
                default:
                    break;
            }
        }
    }
    
    return model;
}

+ (id)RowerWithParaseData:(NSData *)data {
    
    NSData *flags = [data subdataWithRange:NSMakeRange(0, 2)];
    NSArray *array = [FTMSData arrayFromData:flags];
    BaseEquipDataModel *model = [BaseEquipDataModel new];
    
    NSArray *rowerArray = @[
        @(ROWER_TYPE_MORE_DATA),             // (U16)0x0001U     /* 其他数据 - 桨频 */
        @(ROWER_TYPE_AVERAGE_STROKE),        // (U16)0x0002U     /* 平均桨频 */
        @(ROWER_TYPE_TOTAL_DISTANCE),        // (U16)0x0004U     /* 总距离 */
        @(ROWER_TYPE_INSTANTANEOUS_PACE),    // (U16)0x0008U     /* 瞬时速度 */
        @(ROWER_TYPE_AVERAGE_PACE),          // (U16)0x0010U     /* 平均速度 */
        @(ROWER_TYPE_INSTANTANEOUS_POWER),   // (U16)0x0020U     /* 瞬时功率 */
        @(ROWER_TYPE_AVERAGE_POWER),         // (U16)0x0040U     /* 平均功率 */
        @(ROWER_TYPE_RESISTANCE_LEVEL),      // (U16)0x0080U     /* 阻力等级 */
        @(ROWER_TYPE_EXPENDED_ENERGY),       // (U16)0x0100U     /* 消耗能量 */
        @(ROWER_TYPE_HEART_RATE ),           // (U16)0x0200U     /* 心率 */
        @(ROWER_TYPE_METABOLIC_EQU),         // (U16)0x0400U     /* 代谢当量 */
        @(ROWER_TYPE_ELAPSED_TIME),          // (U16)0x0800U     /* 运动时间 */
        @(ROWER_TYPE_REMAINING_TIME),        // (U16)0x1000U     /* 剩余时间 */
    ];
    
    
    NSInteger dataTempIndex = 2;
    
    for (int i = 0; i < rowerArray.count; i++) {
        NSString *flag = array[i];
        
        if (([rowerArray[i] intValue] == ROWER_TYPE_MORE_DATA && (!flag.boolValue))
            || ([rowerArray[i] intValue] != ROWER_TYPE_MORE_DATA && flag.boolValue)) {
            
            switch ([rowerArray[i] intValue]) {
                case ROWER_TYPE_MORE_DATA:
                {
                    
                    model.spm = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(dataTempIndex, 1)]] / 2);
                    dataTempIndex  = dataTempIndex + 1;
                }
                    break;
                case ROWER_TYPE_AVERAGE_PACE:
                {
                    model.avgSpeed = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(dataTempIndex, 2)]] );
                    dataTempIndex  = dataTempIndex + 2;
                }
                    
                    break;
                case ROWER_TYPE_INSTANTANEOUS_PACE:
                {
                    model.speed = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(dataTempIndex, 2)]] );
                    dataTempIndex  = dataTempIndex + 2;
                }
                    break;;
                case ROWER_TYPE_AVERAGE_STROKE:
                {
                    model.count = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(dataTempIndex, 2)]]);
                    dataTempIndex = dataTempIndex + 2;
                    model.avgSpm = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(dataTempIndex, 1)]] / 2);
                    dataTempIndex  = dataTempIndex + 1;
                    //Stroke Count:2 ,Average Stroke Rate:1
                }
                    break;
                case ROWER_TYPE_TOTAL_DISTANCE:
                {
                    NSData *sub = [data subdataWithRange:NSMakeRange(dataTempIndex, 3)];
                    
                    model.totalDistance = @([BaseEquipData convertData:sub]);
                    dataTempIndex  = dataTempIndex + 3;
                }
                    break;
                case ROWER_TYPE_RESISTANCE_LEVEL:
                {
                    model.drag = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(dataTempIndex, 2)]]);
                    dataTempIndex = dataTempIndex + 2;
                }
                    break;
                case ROWER_TYPE_INSTANTANEOUS_POWER:
                {
                    model.power = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(dataTempIndex, 2)]]);
                    dataTempIndex = dataTempIndex + 2;
                }
                    break;
                case ROWER_TYPE_AVERAGE_POWER:
                {
                    model.avgPower = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(dataTempIndex, 2)]]);
                    dataTempIndex  = dataTempIndex + 2;
                }
                    break;
                case ROWER_TYPE_EXPENDED_ENERGY:
                {
                    model.energy = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(dataTempIndex, 2)]]);
                    dataTempIndex = dataTempIndex + 2 + 2 + 1;
                    //Total Energy:2;Energy Per Hour:2;Energy Per Minute:1
                    
                }
                    break;
                case ROWER_TYPE_HEART_RATE:
                {
                    model.deviceRate = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(dataTempIndex, 1)]]);
                    dataTempIndex = dataTempIndex + 1;
                }
                    break;
                case ROWER_TYPE_ELAPSED_TIME:
                {
                    model.totalTimeSecond = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(dataTempIndex, 2)]]);
                    dataTempIndex = dataTempIndex + 2;
                }
                    break;
                case ROWER_TYPE_REMAINING_TIME:
                {
                    model.remainingTimeSecond = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(dataTempIndex, 2)]]);
                    dataTempIndex = dataTempIndex + 2;
                }
                    break;
                case ROWER_TYPE_METABOLIC_EQU:
                {
                    dataTempIndex = dataTempIndex + 1;
                }
                    break;
                    
                default:
                    break;
            }
        }
    }
    
    return model;
}


+ (NSArray *)arrayFromData:(NSData *)data {
    @autoreleasepool {
//        NSLog(@"origin --- %@" , data);

        NSData *small = [NSObject dataTransfromBigOrSmall:data];
        //        NSLog(@"small -- %@" , small);
        NSString *hex = [small hexString];
        //        NSLog(@"十六进制———— %@" ,hex);
        NSString *binary = [NSObject getBinaryByHex:hex];
        //        NSLog(@"binary -- %@" , binary);
        
        NSMutableArray *arr = [NSMutableArray array];
        for (int i = 0; i<binary.length; i++) {
            [arr addObject:[binary substringWithRange:NSMakeRange(i, 1)]];
        }
        
        NSArray * reversedArray = [[arr reverseObjectEnumerator] allObjects];
        return reversedArray;
    }
}


+(void)fitnessMachineStatusChange:(NSData *)data type:(NSString *)type {
    NSLog(@"kFitnessMachineStatusCharacteristics2ADA____%@" , data);
    int status = [BaseEquipData convertData:[data subdataWithRange:NSMakeRange(0, 1)]];
//    [[NSNotificationCenter defaultCenter] postNotificationName:@"FTMSFitnessMachineStatusChangeNotification" object:@{@"status" : @(status) , @"type" : type}];
    
    switch (status) {
        case FM_STATUS_RFU:
        {
            NSLog(@"FM_STATUS_RFU==保留 ");
        } break;
        case FM_STATUS_STOPPED_OR_PAUSED:
        {
            NSLog(@"FM_STATUS_STOPPED_OR_PAUSED==已经停止或暂停 ");
            int control = [BaseEquipData convertData:[data subdataWithRange:NSMakeRange(1, 1)]];
            //0x01:stop;0x02:pause
            if (control == FM_CTL_TYPE_STOP) {
                //上次运动已经结束（发送结束命令，或者用户主动结束）
                NSLog(@"ftms_stop");
//                [[NSNotificationCenter defaultCenter] postNotificationName:@"TreamillStatusNotification" object:@(TreamillStandbyStatus)];
                [[NSNotificationCenter defaultCenter] postNotificationName:@"FTMSTreamillStatusNotification" object:@{@"status":@(DeviceStandbyStatus) , @"ch" : kFitnessMachineStatusCharacteristics}];
                
            }else if (control == FM_CTL_TYPE_PAUSE){
                NSLog(@"ftms_pause");
//                [[NSNotificationCenter defaultCenter] postNotificationName:@"TreamillStatusNotification" object:@(TreamillPauseStatus)];
                [[NSNotificationCenter defaultCenter] postNotificationName:@"FTMSTreamillStatusNotification" object:@{@"status":@(DevicePauseStatus) , @"ch" : kFitnessMachineStatusCharacteristics}];
            }
        }
            break;
        case FM_STATUS_STOP_BY_SAFETYKEY:
        {
            NSLog(@"FM_STATUS_STOP_BY_SAFETYKEY==跑步机由安全扣停止 ");
        }
            break;
        case FM_STATUS_STARTED_OR_RESUMED:
        {
            NSLog(@"FM_STATUS_STARTED_OR_RESUMED==已经开始或继续");
//            [[NSNotificationCenter defaultCenter] postNotificationName:@"TreamillStatusNotification" object:@(TreamillRuningStatus)];
            [[NSNotificationCenter defaultCenter] postNotificationName:@"FTMSTreamillStatusNotification" object:@{@"status":@(DeviceRuningStatus) , @"ch" : kFitnessMachineStatusCharacteristics}];
        }
            break;
        case FM_STATUS_SPEED_CHANGED:
        {
            NSLog(@"FM_STATUS_SPEED_CHANGED==目标速度已更新");
        }
            break;
        case FM_STATUS_INCLINE_CHANGED:
        {
            NSLog(@"FM_STATUS_INCLINE_CHANGED==目标坡度已更新 ");
        }
            break;
        case FM_STATUS_RES_LEVEL_CHANGED:
        {
            NSLog(@"FM_STATUS_RES_LEVEL_CHANGED==目标阻力等级已更新 ");
        }
            break;
        case FM_STATUS_TARGET_POWER_CHANGED:
        {
            NSLog(@"FM_STATUS_TARGET_POWER_CHANGED==目标功率改变");
        }
            break;
        case FM_STATUS_TARGET_HEART_RATE_CHANGED:
        {
            NSLog(@"FM_STATUS_TARGET_HEART_RATE_CHANGED==目标心率改变");
        }
            break;
        case FM_STATUS_TARGET_ENERGY_CHANGED:
        {
            NSLog(@"FM_STATUS_TARGET_ENERGY_CHANGED==目标消耗能量改变 ");
        }
            break;
        case FM_STATUS_TARGET_NUMBER_OF_STEPS_CHANGED:
        {
            NSLog(@"FM_STATUS_TARGET_NUMBER_OF_STEPS_CHANGED==目标步数改变 ");
        }
            break;
        case FM_STATUS_TARGET_NUMBER_OF_STRIDES_CHANGED:
        {
            NSLog(@"FM_STATUS_TARGET_NUMBER_OF_STRIDES_CHANGED==目标大步数改变 ");
        }
            break;
            
        case FM_STATUS_TARGET_DISTANCE_CHANGED:
        {
            NSLog(@"FM_STATUS_TARGET_DISTANCE_CHANGED==目标总距离改变");
        }
            break;
        case FM_STATUS_TARGET_TRAINING_TIME_CHANGED:
        {
            NSLog(@"FM_STATUS_TARGET_TRAINING_TIME_CHANGED==目标运动时间改变 ");
        }
            break;
        case FM_STATUS_TARGET_TIME_IN_TWO_HEART_CHANGED:
        {
            NSLog(@"FM_STATUS_TARGET_TIME_IN_TWO_HEART_CHANGED==两个心率间的目标时间改变");
        }
            break;
            
        case FM_STATUS_TARGET_TIME_IN_THREE_HEART_CHANGED:
        {
            NSLog(@"FM_STATUS_TARGET_TIME_IN_THREE_HEART_CHANGED==三个心率间的目标时间改变 ");
        }
            break;
        case FM_STATUS_TARGET_TIME_IN_FIVE_HEART_CHANGED:
        {
            NSLog(@"FM_STATUS_TARGET_TIME_IN_FIVE_HEART_CHANGED==五个心率间的目标时间改变 ");
        }
            break;
        case FM_STATUS_BIKE_SIMULATION_PARAMETERS_CHANGED:
        {
            NSLog(@"FM_STATUS_BIKE_SIMULATION_PARAMETERS_CHANGED==单车模拟参数改变 ");
        }
            break;
            
        case FM_STATUS_WHEEL_CIRCUMFERENCE_CHANGED:
        {
            NSLog(@"FM_STATUS_WHEEL_CIRCUMFERENCE_CHANGED==轮径改变 ");
        }
            break;
        case FM_STATUS_SPIN_DOWN:
        {
            NSLog(@"FM_STATUS_SPIN_DOWN==旋转状态 ");
        }
            break;
        case FM_STATUS_TARGET_CADENCE_CHANGED:
        {
            NSLog(@"FM_STATUS_TARGET_CADENCE_CHANGED==目标踏频改变 ");
        }
            break;
            
        case FM_STATUS_CONTROL_PERMISSION_LOST:
        {
            NSLog(@"FM_STATUS_CONTROL_PERMISSION_LOST==控制权限丢失（跑步机收回了控制权，需要重新通过Opcode = 0x00申请控制）");
        }
            break;

            
        default:
            break;
    }
    
}

+(void)trainingStatusChange:(NSData *)data type:(NSString *)type{
//    NSLog(@"kTrainingStatusCharacteristics2AD3____%@" , data);
    int status = [BaseEquipData convertData:[data subdataWithRange:NSMakeRange(1, 1)]];
    ////初始 时 获取设备当前状态 22-10-28 兼容636d
    ///636D 在使用 需保留 [2024-12-18]
    [[NSNotificationCenter defaultCenter] postNotificationName:@"FTMSTrainingStatusNotification" object:@{@"status":@(status) , @"type" : type}];
    
    switch (status) {
        case TS_TYPE_OTHER:
            {
                NSLog(@"TS_TYPE_OTHER===其他未知状态");
            }
            break;
        case TS_TYPE_IDLE:
            {
                NSLog(@"TS_TYPE_IDLE===空闲 - 必须支持");
//                [[NSNotificationCenter defaultCenter] postNotificationName:@"TreamillStatusNotification" object:@(TreamillStandbyStatus)];
                [[NSNotificationCenter defaultCenter] postNotificationName:@"FTMSTreamillStatusNotification" object:@{@"status":@(DeviceStandbyStatus) , @"ch" : kTrainingStatusCharacteristics}];
            }
            break;
        case TS_TYPE_WARMING_UP:
            {
                NSLog(@"TS_TYPE_WARMING_UP===暖机");
            }
            break;
        case TS_TYPE_LOW_INT_INTERVAL:
            {
                NSLog(@"TS_TYPE_LOW_INT_INTERVAL===低强度间歇");
            }
            break;
        case TS_TYPE_HIGH_INT_INTERVAL:
            {
                NSLog(@"TS_TYPE_HIGH_INT_INTERVAL===高强度间歇");
            }
            break;
        case TS_TYPE_RECOVERY_INTERVAL:
            {
                NSLog(@"TS_TYPE_RECOVERY_INTERVAL===恢复区间");
            }
            break;
        case TS_TYPE_ISOMETRIC:
            {
                NSLog(@"TS_TYPE_ISOMETRIC===等距");
            }
            break;
        case TS_TYPE_HEART_RATE_CTL:
            {
                NSLog(@"TS_TYPE_HEART_RATE_CTL===心率控制");
            }
            break;
        case TS_TYPE_FITNESS_TEST:
            {
                NSLog(@"TS_TYPE_FITNESS_TEST===体能测试");
            }
            break;
        case TS_TYPE_SPEED_OUTSIDE_OF_CTL_LOW:
            {
                NSLog(@"TS_TYPE_SPEED_OUTSIDE_OF_CTL_LOW===控制区域外的速度 - 低（增加速度返回可控区域）");
            }
            break;
        case TS_TYPE_SPEED_OUTSIDE_OF_CTL_HIGH:
            {
                NSLog(@"TS_TYPE_SPEED_OUTSIDE_OF_CTL_HIGH===控制区域外的速度 - 高（降低速度返回可控区域）");
            }
            break;
        case TS_TYPE_COOL_DOWN:
            {
                NSLog(@"TS_TYPE_COOL_DOWN===恢复 ");
            }
            break;
        case TS_TYPE_WATT_CONTROL:
            {
                NSLog(@"TS_TYPE_WATT_CONTROL===功率控制");
            }
            break;
        case TS_TYPE_QUICK_START:
            {
                NSLog(@"TS_TYPE_QUICK_START===正在跑步时的状态/手动启动、快速启动 - 必须支持");
//                [[NSNotificationCenter defaultCenter] postNotificationName:@"TreamillStatusNotification" object:@(TreamillRuningStatus)];
                [[NSNotificationCenter defaultCenter] postNotificationName:@"FTMSTreamillStatusNotification" object:@{@"status":@(DeviceRuningStatus) , @"ch" : kTrainingStatusCharacteristics}];
            }
            break;
        case TS_TYPE_PRE_WORKOUT:
            {
                NSLog(@"TS_TYPE_PRE_WORKOUT===预锻炼  （启动之前倒计时）");
            }
            break;
        case TS_TYPE_POST_WORKOUT:
            {
                NSLog(@"TS_TYPE_POST_WORKOUT===后锻炼  （停止之后倒计时）");
            }
            break;
       
        default:
            break;
    }
    
}


@end
