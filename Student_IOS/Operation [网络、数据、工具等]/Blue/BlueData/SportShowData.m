//
//  SportShowData.m
//  ZBlueSDK
//
//  Created by MacPro on 2024/10/21.
//

#import "SportShowData.h"
#import "BlueConfig.h"
#import "BaseEquipData.h"
#import "BlueCommandData.h"
#import "NSObject+Data.h"
#import "BluetoothModel.h"
#import "BloothTool.h"

@implementation SportShowData

+ (id)modelFromData:(NSData *)data type:(NSString *)type {
    BaseEquipDataModel *model ;
    switch (type.intValue) {
 
        case EllipticalEquipment:
        case BicycleEquipment:
        case BoatEquipment:
        case PowerEquipment:
        {
            model = [SportShowData carModelWithParaseData:data];
        }
            break;
      
            
        default:
            break;
    }
    
    model.type = @(type.intValue);
    return model;
    
    
}


+(id)carModelWithParaseData:(NSData *)data {
    BaseEquipDataModel *m ;
    if ([BloothTool isSamePrefix:data other:[SportShowCommandData getStatusAndDataCmd] start:1 offset:1]) {
        
        m = [BaseEquipDataModel new];
        //0x00 待机；0x01 启动中；0x02 运行中；0x03 暂停；0x14 睡眠；0x15 故障
        m.status =  @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(2, 1)]]);
        if(m.status.intValue == 0x00) {
            return m;
        }
        m.speed = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(3, 2)]] * 0.01);
        m.drag = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(5, 1)]]);
        m.spm = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(6, 2)]]);
        m.deviceRate = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(8, 1)]]);
        m.power = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(9, 2)]] * 0.1);
        m.gradient = @([BaseEquipData convertSignalData:[data subdataWithRange:NSMakeRange(11, 1)]]); // 有符号
        
        
        
    } else if([BloothTool isSamePrefix:data other:[SportShowCommandData getSportDataCmd] start:1 offset:2] ) {
        
        m = [BaseEquipDataModel new];
        m.totalTimeSecond = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(3, 2)]]);
        int distance  = [BaseEquipData convertData:[data subdataWithRange:NSMakeRange(5, 2)]];
        int real_distance = [BaseEquipData realDistance:distance];
        m.totalDistance = @(real_distance);
        m.energy = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(7, 2)]] * 0.1);
        m.count = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(9, 2)]]);
        
        
    } else if ([BloothTool isSamePrefix:data other:[SportShowCommandData getConfigDataCmd] start:1 offset:2]) {
        m = [BaseEquipDataModel new];
//        m.distanceUnit = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(5, 1)]]);
        int number = [BaseEquipData convertData:[data subdataWithRange:NSMakeRange(5, 1)]];
        
        int drag = [BaseEquipData convertData:[data subdataWithRange:NSMakeRange(3, 1)]];
        NSNumber * supportMaxSlpoe= @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(4, 1)]]);///返回0 默认不支持
        NSNumber * supportMinSlpoe = @([BaseEquipData convertSignalData:[data subdataWithRange:NSMakeRange(6, 1)]]);///返回0 默认不支持
        NSLog(@"运动秀配置====%@==%@==%@" , [NSObject getBinaryByDecimal:number] ,supportMaxSlpoe , supportMinSlpoe);
    }
    return m;
}

@end
