//
//  BlueDataManager.m
//  Student_IOS
//
//  Created by MacPro on 2021/5/23.
//

#import "BlueDataManager.h"
#import "BaseEquipData.h"
#import "WriteFileManager.h"
#import "MRKBlueData.h"
#import "BQProtocolCommandManager.h"
#import "ZJProtocolCommandManager.h"
#import "FTMSData.h"
#import "BlueCommandData.h"
#import "SportShowData.h"
#import "QCData.h"

@interface BlueDataManager ()

@property (nonatomic, strong) dispatch_group_t group;
@property (nonatomic, strong) dispatch_queue_t queue;
@property (nonatomic, strong) dispatch_queue_t serailQueue;
@property (nonatomic, strong) NSLock *myLock;
@property (nonatomic , strong) NSMutableArray *receiviedData;

@end

@implementation BlueDataManager

static BlueDataManager *_share = nil;

+ (instancetype)shareManager {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        
        _share = [[self alloc] init];
        _share.group = dispatch_group_create();
        _share.queue = dispatch_queue_create("queue",DISPATCH_QUEUE_CONCURRENT);
        _share.serailQueue = dispatch_queue_create("serialQueue", DISPATCH_QUEUE_SERIAL);
        
        [[NSNotificationCenter defaultCenter] addObserver:_share
                                                 selector:@selector(dealBlueOriginData:)
                                                     name:BLEDataNotification
                                                   object:nil];
        _share.myLock = [[NSLock alloc]init];
        _share.receiviedData = [NSMutableArray array];
        
//        [[NSNotificationCenter defaultCenter] addObserver:_share selector:@selector(iconsoleStart:) name:IconsoleSendStartNotification object:nil];

    });
    return _share;
}

-(void)receivedData:(NSNotification *)notification {
    NSLog(@"receivedData==%@" , notification);
    [_receiviedData addObject:notification];
    [self processDataNotification];
    
}

-(void)processDataNotification {
    @weakify(self);
    dispatch_barrier_async(self.queue, ^{
        @strongify(self);
        while (self.receiviedData.count > 0) {
            NSNotification *noti = self.receiviedData.firstObject;
            [self.receiviedData removeObjectAtIndex:0];
            [self dealBlueOriginData:noti];
        }
        
        if(self.receiviedData.count > 0) {
            [self processDataNotification];
        }
        
    });
}


#pragma mark --------------------------------新的数据处理开始-------------------------------------------
- (void)dealBlueOriginData:(NSNotification *)notification  {
//    [_myLock lock];
    NSString *type  = [notification.object objectForKey:BlueDeviceType];
    NSNumber *blueDataType = [BluePeripheral blueDataTypeFromPeripheralType:type];
    CBCharacteristic *characteristic = [notification.object objectForKey:BlueCharacteristic];
    
//    NSLog(@"dealBlueOriginData____blueDataType==%@==%@" , blueDataType , characteristic);
    
    dispatch_async(self.queue, ^{
        
        switch (blueDataType.intValue) {
            case FTMSCommunicationProtocol: {
                if ([characteristic.UUID.UUIDString isEqualToString:kFitnessMachineStatusCharacteristics]) {
                    //设备状态变化 21-12-21
                    NSData *data = characteristic.value;
//                    NSLog(@"kFitnessMachineStatusCharacteristics____==%@"  , characteristic);
                    [FTMSData fitnessMachineStatusChange:data type:type];
                    
                } else if ([characteristic.UUID.UUIDString.uppercaseString isEqualToString:kTrainingStatusCharacteristics]) {
                    ////初始 时 获取设备当前状态 22-10-28 兼容636d
                    NSData *data = characteristic.value;
                    [FTMSData trainingStatusChange:data type:type];
                    
                } else {
                    
                    [self convertModel:notification];
                }
            } break;
                
            case ZJCommunicationProtocol:
            case PowerCommunicationProtocol: {
                
                [self ZJProtocolDeal:notification];
            } break;
                
            case BQCommunicationProtocol: {
                
                [self iconsoleDeal:notification];
            } break;
                
                
            case MRKCommunicationProtocol: {
                
                [self MRKDeal:notification];
            } break;
                
                
            case FTMSANDZJCommunicationProtocol: {
                
                if ([characteristic.service.UUID.UUIDString.uppercaseString isEqualToString:kFirstServiceUUID]) {
                    [self convertModel:notification];
                } else {
                    [self ZJProtocolDeal:notification];
                }
            } break;
               
                
            case JMQCommunicationProtocol: {
                
                [self JMQDeal:notification];
            } break;
                
            case SportShowCommunicationProtocol: {
                
                [self sportShowDataDeal:notification];
            }break;
            case QCCommunicationProtocol:
            {
                [self qcDataDeal:notification];
            }
                break;
                
            //其他
            default: {
               
                [self convertModel:notification];
            } break;
        }
    });
    
//    [_myLock unlock];
    
}

- (void)convertModel:(NSNotification *)notification {
    NSString *type = [notification.object objectForKey:BlueDeviceType];
    NSNumber *blueDataType = [BluePeripheral blueDataTypeFromPeripheralType:type];
    CBCharacteristic *characteristic = [notification.object objectForKey:BlueCharacteristic];
    
    NSData *data = [notification.object objectForKey:@"data"];
    BaseEquipDataModel *model;
    if (type.intValue != HeartEquipment && data.length < 4) {
        return;
    }
    //    NSLog(@"dealBlueOriginData____blueDataType==%@" , blueDataType);
    
    switch (blueDataType.intValue) {
        case FTMSCommunicationProtocol:
        case ZJCommunicationProtocol:
        case BQCommunicationProtocol:
        case MRKCommunicationProtocol:
        case JMQCommunicationProtocol:
        case PowerCommunicationProtocol: {
            
            model = [BaseEquipDataModel modelFromData:data type:type blueDataType:blueDataType characteristic:characteristic];
        } break;
            
        case FTMSANDZJCommunicationProtocol: {
            
            NSString *string = characteristic.service.UUID.UUIDString.uppercaseString;
            if ([string isEqualToString:kFirstServiceUUID]) {
                model = [BaseEquipDataModel modelFromData:data type:type blueDataType:@(FTMSCommunicationProtocol) characteristic:characteristic];
            } else {
                model = [BaseEquipDataModel modelFromData:data type:type blueDataType:@(ZJCommunicationProtocol) characteristic:characteristic];
            }
        } break;
            
        default: {
            
            model = [BaseEquipDataModel modelFromData:data type:type blueDataType:blueDataType characteristic:characteristic];
        } break;
    }
    
    [self sendModel:model type:type ch:characteristic];
}

- (void)MRKDeal:(NSNotification *)notification{
    
    CBCharacteristic *characteristic = [notification.object objectForKey:BlueCharacteristic];
    if ([characteristic.UUID.UUIDString.uppercaseString isEqualToString:kMRKOTACharacteristics]) {
        NSLog(@"心跳包数据————————");
        return;
    }
    
    if (![MRKBlueData checkPackage:characteristic]) {
        NSLog(@"数据不完整——————————-");
        return;
    }
    
    NSData *dataTypeData = [characteristic.value subdataWithRange:NSMakeRange(2, 1)];
    NSInteger dataType = [BaseEquipData convertData:dataTypeData];
    if (dataType == 0x02) {
        
        //控制指令
        [MRKBlueData checkCTLStatus:characteristic];
//        NSData *statusCodeData = [characteristic.value subdataWithRange:NSMakeRange(3, 1)];
//        NSInteger statusCode = ([BaseEquipData convertData:statusCodeData] & 0x7f);
//        NSInteger status = ([BaseEquipData convertData:[characteristic.value subdataWithRange:NSMakeRange(4, 1)]]);
//        NSLog(@"mrk_control_____%@,status__%@" , @(statusCode) , @(status));
//        if (status != 0x01) {
//            NSLog(@"cmd:%@控制失败了～～～～～～，status=%@" , @(statusCode) , @(status));
//            
//            [[NSNotificationCenter defaultCenter] postNotificationName:@"MRKControlModeFailureNotification" object:nil];
//            return;
//        }
//        
//        NSLog(@"cmd:%@控制成功了～～～～～～" , @(statusCode));
//        switch (statusCode) {
//            case MRKSetTimeControlStatus:
//            case MRKSetNumberControlStatus:
//            {
//                
//                [[NSNotificationCenter defaultCenter] postNotificationName:@"MRKSetCutDownModeSuccessNotification" object:nil];
//            } break;
//                
//            case MRKSetNormalControlStatus:
//            {
//                [[NSNotificationCenter defaultCenter] postNotificationName:@"MRKSetNormalModeSuccess" object:nil];
//            } break;
//                
//            case MRKResetControlStatus:
//            {
//                //复位成功
//                NSLog(@"复位成功_____________________________________");
//                [[NSNotificationCenter defaultCenter] postNotificationName:@"MRKResetSuccessNotification" object:nil];
//            } break;
//                
//            default:
//                break;
//        }
        
    } else if(dataType == 0x01) {
        //运动数据
        [self convertModel:notification];
        
    } else if(dataType == 0x00) {
        //设备信息
        NSData *dev_id = [characteristic.value subdataWithRange:NSMakeRange(4, 1)];
        NSLog(@"设备ID === %@", dev_id);
        
        //        if ([BaseEquipData convertData:dev_id] == 0x03) {
        //            NSData *data = [BloothTool MRKControlMeasureRate:YES];
        //            [[BluetoothManager sharedInstance] sendData:data type: [notification.object objectForKey:BlueDeviceType]];
        //        }
    }
}


-(void)JMQDeal:(NSNotification *)notification {
    
    NSString *type  = [notification.object objectForKey:BlueDeviceType];
    BluetoothModel *bModel = [BlueDataStorageManager connectBMFromProductID:type];
    
    CBCharacteristic *characteristic = [notification.object objectForKey:BlueCharacteristic];
    CBPeripheral *periphral = bModel.peripheral;
    
    NSString *localName = bModel.localName;
    
    NSData *statusData = [characteristic.value subdataWithRange:NSMakeRange(2, 1)];
    if (statusData != nil) {
        int status = [BaseEquipData convertData:statusData];
        NSLog(@"jmq_status -- %@" , @(status));
        
        switch (status) {
            case 1:
            {
                NSLog(@"运行中");
                [self convertModel:notification];
            }
                break;
            case 2:
            {
                //开始
                NSLog(@"HR连接发开始");
                [BluetoothCommondManager startJMQ:type];
                
            }
                break;
            case 3:
            {
                NSLog(@"HR开始设置挡位/暂停");
                [[BluetoothManager sharedInstance] sendData:[BloothTool setJMQData:1] type:type];
                [[NSNotificationCenter defaultCenter] postNotificationName:JMQPauseNotification object:nil];
                
            }
                break;
            case 4:
                NSLog(@"低电量");
                break;
            case 0:
            {
                NSLog(@"待机中");
                
            }
                break;
            default:
                break;
        }
        
    }
}

- (void)ZJProtocolDeal:(NSNotification *)notification {
    
    NSString *type = [notification.object objectForKey:BlueDeviceType];
    CBCharacteristic *characteristic = [notification.object objectForKey:BlueCharacteristic];
    
    switch (type.intValue) {
        case SkipRopeEquipment:
        {
            if ([BloothTool isSamePrefix:characteristic.value other:[BloothTool jumpElectric] start:1 offset:2]){
                
                [self convertModel:notification];
                
            } else if ([BloothTool isSamePrefix:characteristic.value other:[BloothTool passCountAndTimeOrder] start:1 offset:2]){
                
                [self convertModel:notification];
                
            } else if ([BloothTool isSamePrefix:characteristic.value other:[BloothTool setJumpTargetNumber:0 time:0 model:0] start:1 offset:2]){
                
                [[NSNotificationCenter defaultCenter] postNotificationName:@"jumpStringDataCutDown" object:characteristic.value];
            }
        }
            break;
            
        case TreadmillEquipment:
        {
            if ([self isSamePrefix:characteristic.value other:[BloothTool treadmillData] offset:2]) {
                int status = [BaseEquipData convertData:[characteristic.value subdataWithRange:NSMakeRange(2, 1)]];
                //  NSLog(@"TREAMILL_STATUS==%@" , @(status));
                [[NSNotificationCenter defaultCenter] postNotificationName:@"TreamillStatusNotification" object:@(status)];
            }
            

//            if ([self isSamePrefix:characteristic.value other:[BloothTool startDataFromType:type]  offset:3]) {
//                
//                [[NSNotificationCenter defaultCenter] postNotificationName:TreamillGoOnSportNotification object:nil];
//                // 开始发送数据
//                //  [[NSNotificationCenter defaultCenter] postNotificationName:StartWriteDataNotification object:@{BlueDeviceType:type}];
//                
//            } else if ([self isSamePrefix:characteristic.value other:[BloothTool endDataFromType:type]  offset:3]) {
//                //结束数据
//                
//            } else
            if ([self isSamePrefix:characteristic.value other:[BloothTool treadmillData] offset:2] &&
                [BaseEquipData convertData:[characteristic.value subdataWithRange:NSMakeRange(2, 1)]] == DeviceRuningStatus) {
                
                [[NSNotificationCenter defaultCenter] postNotificationName:TreamillUpdateDataNotification object:nil];
                [self convertModel:notification];
                NSLog(@"运行中-----");
                
            } else if ([self isSamePrefix:characteristic.value other:[BloothTool treadmillData] offset:2] 
                       && [BaseEquipData convertData:[characteristic.value subdataWithRange:NSMakeRange(2, 1)]] == DevicePauseStatus) {
                NSLog(@"已经成功暂停");
                
                
            } else if ([self isSamePrefix:characteristic.value other:[BloothTool treadmillData] offset:2] 
                       && [BaseEquipData convertData:[characteristic.value subdataWithRange:NSMakeRange(2, 1)]] == DeviceCutDownStatus) {
                
                NSLog(@"启动中");
                int time = [BaseEquipData convertData:[characteristic.value subdataWithRange:NSMakeRange(3, 1)]];
                [[NSNotificationCenter defaultCenter] postNotificationName:TreamillStartCutdownNotification object:@(time)];
                
            } else if ([self isSamePrefix:characteristic.value other:[BloothTool treadmillData] offset:2] 
                       && [BaseEquipData convertData:[characteristic.value subdataWithRange:NSMakeRange(2, 1)]] == DeviceStandbyStatus) {
                NSLog(@"待机中");
                
            } else if ([self isSamePrefix:characteristic.value other:[BloothTool treadmillData] offset:2] 
                       && [BaseEquipData convertData:[characteristic.value subdataWithRange:NSMakeRange(2, 1)]] == DeviceX1StandbyStatus) {
                NSLog(@"有弹窗___待机中");//彩屏x1 发送就绪指令
                
            } else if ([self isSamePrefix:characteristic.value other:[BloothTool treadmillData] offset:2] 
                       && [BaseEquipData convertData:[characteristic.value subdataWithRange:NSMakeRange(2, 1)]] == DevicelSlowDownStatus) {
                NSLog(@"暂停减速中-------");//x1有，彩屏没有
                
            }
        }
            break;
        case BicycleEquipment:
        case EllipticalEquipment:
        case BoatEquipment:
        case PowerEquipment:
        case StairClimbEquipment:
        {
             if (characteristic.value.length == 6 && [self isSamePrefix:characteristic.value other:[BloothTool getDeviceStatusData] offset:2]) {
                
                
            } else if (([self isSamePrefix:characteristic.value other:[BloothTool getDeviceStatusData]  offset:2] )
                       || ([self isSamePrefix:characteristic.value other:[BloothTool getSportData]  offset:2])) {
                //&& [BaseEquipData convertData:[characteristic.value subdataWithRange:NSMakeRange(2, 1)]] == 2 //8-30 2.4.0版本修改 为了解决Q1暂停阻力不一致
                
                [self convertModel:notification];
                
            }
        }
            break;
            
        default:
            break;
    }
    
}

- (void)iconsoleDeal:(NSNotification *)notification {
    
    NSData * data = [notification.object objectForKey:@"data"];
    NSString *type  = [notification.object objectForKey:BlueDeviceType];
    
    if(data.length < kMINDataLength) {
        return;
    }
    
    //柏群
    if ([BloothTool isSamePrefix:data other:[BloothTool connectResponseData] start:0 offset:2]) {
        
        //连接成功 发送开始
        NSData *subData = [data subdataWithRange:NSMakeRange(2, 2)];
        [BQProtocolCommandManager bqStartWithCmd:subData type:type];
        /*
         NSData *subData = [data subdataWithRange:NSMakeRange(2, 2)];
         NSData *sendData = [BloothTool startDataFromCmdData:subData];
         [[BluetoothManager sharedInstance] sendData:sendData type:type];
         */
        
    } else if ([BloothTool isSamePrefix:data other:[BloothTool startResponseData] start:0 offset:2]) {
        
        NSData *statusData = [data subdataWithRange:NSMakeRange(4, 1)];
        if ([BaseEquipData convertData:statusData minus:kMinValue] == 1) {
            //开始获取数据
            NSData *subData = [data subdataWithRange:NSMakeRange(2, 2)];
            [BQProtocolCommandManager bqGetDataWithCmd:subData type:type];
            /*
             NSData *subData = [data subdataWithRange:NSMakeRange(2, 2)];
             NSData *sendData = [BloothTool getDataFromCmdData:subData];
             //        sendData = [BloothTool getData];
             [[NSNotificationCenter defaultCenter]postNotificationName:@"boqun" object:@{BlueDeviceType:type , @"data" : sendData}];
             */
            
        } else if([BaseEquipData convertData:statusData minus:kMinValue] == 3){
            //reset
            NSLog(@"iconsle_reset_success");
            
            [[NSNotificationCenter defaultCenter] postNotificationName:IconsoleResetDataSuccessNotification object:@{@"data" : data , @"type":type}];
        }
        
    } else if ([BloothTool isSamePrefix:data other:[BloothTool errorResponseData] start:0 offset:2]) {
        //f0b7
        NSData *subData = [data subdataWithRange:NSMakeRange(2, 2)];
        [BQProtocolCommandManager bqConnetWithCmd:subData type:type];
        /*
         NSData *subData = [data subdataWithRange:NSMakeRange(2, 2)];
         NSData *sendData = [BloothTool connectDataFromCmdData:subData];
         [[BluetoothManager sharedInstance] sendData:sendData type:type];
         */
        
    } else if ([BloothTool isSamePrefix:data other:[BloothTool getDataResponseData] start:0 offset:2]) {
        //处理数据
        
        [self convertModel:notification];
        
        
    }
    
}

-(void)sportShowDataDeal:(NSNotification *)notification {
    
    NSString *type = [notification.object objectForKey:@"type"];
    NSData *data = [notification.object objectForKey:@"data"];

    switch (type.intValue) {
        case BicycleEquipment:
        case EllipticalEquipment:
        case BoatEquipment:
        {
            if ([BloothTool isSamePrefix:data other:[SportShowCommandData sportStartCmd] start:1  offset:2]) {
                //开始发送数据
                BlueNSLog(@"运动秀设备==sport_start_or_resume");
                
            } else if ([BloothTool isSamePrefix:data other:[SportShowCommandData sportEndCmd] start:1 offset:2]) {
                //结束数据
                BlueNSLog(@"运动秀设备==sport_end");
            } else if (([BloothTool isSamePrefix:data other:[SportShowCommandData getSportDataCmd] start:1 offset:2] )
                       || ([BloothTool isSamePrefix:data other:[SportShowCommandData getStatusAndDataCmd] start:1  offset:1])) {
                
                BlueNSLog(@"运动秀设备===sport_data ==%@" ,data);
                [self convertModel:notification];
                
            } else if ([BloothTool isSamePrefix:data other:[SportShowCommandData getConfigDataCmd] start:1 offset:2]) {
                // 获取设备配置信息
                [SportShowData modelFromData:data type:type];
                BlueNSLog(@"运动秀设备获取设备参数==%@" , data);
            } else if ([BloothTool isSamePrefix:data other:[SportShowCommandData sportReadyCmd] start:1 offset:2]) {
                int cutdown = [BaseEquipData convertData:[data subdataWithRange:NSMakeRange(3, 1)]];
                BlueNSLog(@"运动秀设备就绪指令响应==%@,time==%@" , data , @(cutdown));
            } else if ([BloothTool isSamePrefix:data other:[SportShowCommandData sportPauseCmd] start:1 offset:2]) {
                BlueNSLog(@"运动秀设备暂停指令响应==%@" , data);
            } else if ([BloothTool isSamePrefix:data other:[SportShowCommandData getIDCmd] start:1 offset:2]) {
                BlueNSLog(@"运动秀设备设备ID==%@" , data);
            }else if ([BloothTool isSamePrefix:data other:[SportShowCommandData dragPrefix] start:1 offset:2]) {
                BlueNSLog(@"运动秀设备设置阻力坡度==%@" , data);
            }
        }
            break;
            
        default:
            break;
    }
    
}
-(void)qcDataDeal:(NSNotification *)notification {
    NSData *data = [notification.object objectForKey:@"data"];
    NSString *type = [notification.object objectForKey:@"type"];
    CBCharacteristic *characteristic = [notification.object objectForKey:@"BlueCharacteristic"];

    BaseEquipDataModel *model = [QCData dealBlueDataCode:data type:type];
    if(model) {
        [self sendModel:model type:type ch:characteristic];
    }
    
}


#pragma mark --------------------------------新的数据处理结束-------------------------------------------

- (void)sendModel:(BaseEquipDataModel *)model type:(NSString *)type ch:(CBCharacteristic *)characteristic{
    
    model.type = @(type.intValue);
    model.csName = characteristic.UUID.UUIDString;
    
    //和安卓统一，时间>0 才更新数据 2021-08-11 2.3.5版本修改
    //为了防止有些数据 和正常的数据位 位数不同，导致部分参数缺失，统一判断一下 model 是否为空（如果有属性值为-1，说明转换过程中data是空，数据丢弃）  2021-12-10 2.4.9
    if (model
        && [model isNotEmpty]
//        && ((model.totalTimeSecond && model.totalTimeSecond.longValue >= 0)
//            || (!model.totalTimeSecond) // 智健协议的非跳绳数据 && model.type.intValue != SkipRopeEquipment,跳绳电量数据需要更新 所以删除非跳绳的判断22-3-17
//            || model.electric.intValue > 0 //有电量 可以更新 22-04-08 麦瑞克协议 跳绳 ota 获取电量
//            )
        ) {
//        model.isDeviceRate = model.deviceRate ? @1 : @0;//如果设备心率不为空，即默认是设备心率 22-04-14
        model.deviceTimestamp = @([MRKToolKit current13TimeInterval]);
        
        NSString *notificationName = type.intValue == HeartEquipment ?  HeartRateDataNotification : BlueDeviceDataNotification;
        [[NSNotificationCenter defaultCenter] postNotificationName:notificationName object:model];
        
        BlueNSLog(@"origin_model:spm=%@, time=%@, distance=%@, enery=%@, drag=%@, rate=%@, type=%@", model.spm, model.totalTimeSecond, model.totalDistance, model.energy, model.drag , model.rate, model.type);
    }
}


- (BOOL)isSamePrefix:(NSData *)first other:(NSData *)second offset:(int)offset{
    return [BloothTool isSamePrefix:first other:second start:0 offset:offset];
}


//#pragma mark - 柏群 发送开始指令
//-(void)iconsoleStart:(NSNotification *)notification {
//
//    NSData *data = [notification.object objectForKey:@"data"];
//    NSString *type = [notification.object objectForKey:@"type"];
//
//    NSLog(@"iconsole--- %@" , data);
//
//    NSData *subData = [data subdataWithRange:NSMakeRange(2, 2)];
//    NSData *sendData = [BloothTool startDataFromCmdData:subData];
//
//    [[BluetoothManager sharedInstance] sendData:sendData type:type];
//
//}
@end
