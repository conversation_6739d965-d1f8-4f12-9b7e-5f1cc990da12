//
//  BlueCommunicationProtocol.m
//  Student_IOS
//
//  Created by MacPro on 2021/12/2.
//

#import "BlueCommunicationProtocol.h"

@implementation BlueCommunicationProtocol

#pragma mark - 设置设备的OTA服务
+ (void)setBlueOTAServiceFromProductID:(NSString *)productID {
    BluetoothModel *model = [BlueDataStorageManager connectBMFromProductID:productID];
    MRKDeviceModel *dModel = model.equipmentModel;
    
    NSString *otaSeriviceName;
    switch (dModel.otaProtocol.intValue) {
        case TeLinkProtocol: {
            otaSeriviceName = kOTAServiceUUID;
        }  break;
                
        case BroadCom: {
            otaSeriviceName = [BluePeripheral btServiceName:model];
        }  break;
            
        case DFUOTA: {
            otaSeriviceName = kDFUOTAServiceUUID;
        }  break;
            
        case XinXYOTA: {
            otaSeriviceName = kXXYOTAServiceUUID;
        }  break;
            
        case FURKOTA: {
            otaSeriviceName = [BluePeripheral frkServiceName:model];
            NSArray *otas = [model.services objectForKey:otaSeriviceName];
            for (CBCharacteristic *ch in otas) {
                if ([ch.UUID.UUIDString.uppercaseString isEqualToString:kFRKOTANotifyCharacteristics.uppercaseString]
                    || [ch.UUID.UUIDString.uppercaseString isEqualToString:kFRKOTANotifyCharacteristics2.uppercaseString]) {
                    [model.peripheral setNotifyValue:YES forCharacteristic:ch];
                    break;
                }
            }
        }  break;
            
        case LinkedsemiOTA: {
            otaSeriviceName = kLINKEDOTAServiceUUID;
            NSArray *otas = [model.services objectForKey:otaSeriviceName];
            for (CBCharacteristic *ch in otas) {
                if ([ch.UUID.UUIDString.uppercaseString isEqualToString:kLINKEDOTAControlCharacteristics.uppercaseString]) {
                    [model.peripheral setNotifyValue:YES forCharacteristic:ch];
                    break;
                }
            }
        }  break;
            
        default:
            break;
    }
    model.otaCharacteristics = [model.services objectForKey:otaSeriviceName];
    model.otaCharacteristic = [model.otaCharacteristics firstObject];
    
}


#pragma mark - 获取 ota 特征值数组
+ (NSArray *)otaCharacteristicFromType:(NSString *)type {
    BluetoothModel *model = [BlueDataStorageManager connectBMFromProductID:type];
    NSArray *chars = model.otaCharacteristics;
    
    if (type.intValue == HeartEquipment && [model.services.allKeys containsObject:kHeartDeviceOTAServiceUUID]) {
        NSArray *chs  = [model.services objectForKey:kHeartDeviceOTAServiceUUID];
        for(CBCharacteristic *ch in chs) {
            if ([ch.UUID.UUIDString.uppercaseString isEqualToString:kHeartDeviceOTACharacteristicsUUID]) {
                chars = @[ch];
            }
        }
    }
    return  chars;
}


#pragma mark --------------------订阅/取消订阅 数据服务------------------------------------
+ (void)subscribeCharacteristicFromType:(NSString *)type isSubscribe:(BOOL)subscribe{
    
//    BluetoothManager *manager = [BluetoothManager sharedInstance];
    BluetoothModel *model = [BlueDataStorageManager connectBMFromProductID:type];
    CBPeripheral *peripheral = model.peripheral;
    NSNumber *dataServiceType = model.dataServiceType;
    NSDictionary *chars = model.services;
    
    if (dataServiceType.intValue == FTMSCommunicationProtocol) {
        NSArray *cs = [NSArray array];
        if ([chars.allKeys containsObject:kFirstServiceUUID]) {
            cs = [chars valueForKey:kFirstServiceUUID];
        } else if([chars.allKeys containsObject:k667BicycleServiceUUID]) {
            //防止旧的667 协议没有1826 21-12-14
            cs = [chars valueForKey:k667BicycleServiceUUID];
        }
        
        NSString *currentCSName = [BlueCommunicationProtocol characteristicName:type];
        for (CBCharacteristic *characteristic in cs) {
            if([characteristic.UUID.UUIDString.uppercaseString isEqual:currentCSName]
               || [characteristic.UUID.UUIDString.uppercaseString isEqual:kFitnessMachineStatusCharacteristics]
               || [characteristic.UUID.UUIDString.uppercaseString isEqual:kFitnessMachineControlPointCharacteristics]
               || [characteristic.UUID.UUIDString.uppercaseString isEqual:kTrainingStatusCharacteristics]){
                [peripheral setNotifyValue:subscribe forCharacteristic:characteristic];
            }
        }
        
        if (!subscribe && [BloothTool is667Bicycle:type]) {
            //取消订阅， 667 断开连接
            [[NewBluePeripheralManager sharedInstance] disconnectModel:model];
//            [manager cancleConnectPeripheralName:@{BlueDeviceType : model.type}];
        }
        return;
    }
    
    if (dataServiceType.intValue == ZJCommunicationProtocol 
        || dataServiceType.intValue == JMQCommunicationProtocol
        || dataServiceType.intValue == PowerCommunicationProtocol
        || dataServiceType.intValue == SportShowCommunicationProtocol) {
        
        NSArray *cs = [chars valueForKey:kSecondServiceUUID];
        for (CBCharacteristic *characteristic in cs) {
            if ([BlueCommunicationProtocol isNotifyCharacteristic:characteristic]) {
                [peripheral setNotifyValue:subscribe forCharacteristic:characteristic];
                break;
            }
        }
        return;
    }

    if ((dataServiceType.intValue == BQCommunicationProtocol)) {
        NSString *dataService = [chars.allKeys containsObject:kSecondServiceUUID] ? kSecondServiceUUID : kConsoleServiceUUID;
        NSArray *cs = [chars valueForKey:dataService];
        for (CBCharacteristic *characteristic in cs) {
            if ([BlueCommunicationProtocol isNotifyCharacteristic:characteristic]) {
                [peripheral setNotifyValue:subscribe forCharacteristic:characteristic];
                break;
            }
        }
       
        return;
    }
    
    if (dataServiceType.intValue == MRKCommunicationProtocol) {
        NSArray *cs = [chars valueForKey:[kMRKServiceUUID uppercaseString]];
        for (CBCharacteristic *characteristic in cs) {
            //因为协议里的服务都需要订阅，因为有ota和数据，properties是一样的，都需要订阅，所以全部遍历
            if ([BlueCommunicationProtocol isNotifyCharacteristic:characteristic]) {
                [peripheral setNotifyValue:subscribe forCharacteristic:characteristic];
            }
        }
        
        return;
    }
    if(dataServiceType.intValue == QCCommunicationProtocol) {
//        [BlueCommunicationProtocol subcribeQCCharacteristic:subscribe type:type];
        NSArray *cs = [chars valueForKey:kQCServiceUUID];
        for (CBCharacteristic *characteristic in cs) {
            if ([BlueCommunicationProtocol isNotifyCharacteristic:characteristic]) {
                [peripheral setNotifyValue:subscribe forCharacteristic:characteristic];
                break;
            }
        }
        return;
    }
    
    /*
    NSNumber *blueDataType = [BluePeripheral blueDataTypeFromPeripheralType:type];
    CBPeripheral *peripheral = [BluePeripheral periphralFromType:type];

    NSDictionary *valueDic = [BluePeripheral peripheralValueDictory:type];

    NSDictionary *chars = [valueDic valueForKey:BlueCharacteristic];

    NSString *currentCSName = [BlueCommunicationProtocol characteristicName:type];
    NSString *dataService = [[valueDic valueForKey:BlueDataService] uppercaseString];
    
    
    NSLog(@"subscribeCharacteristicFromType___________________%@" , dataService);
    
    if ([dataService isEqualToString:kFirstServiceUUID]) {
        //1826
        NSArray *cs = [NSArray array];
        if ([chars.allKeys containsObject:kFirstServiceUUID]) {
            cs = [chars valueForKey:kFirstServiceUUID];
        }else if([chars.allKeys containsObject:k667BicycleServiceUUID]) {//防止旧的667 协议没有1826 21-12-14
            cs = [chars valueForKey:k667BicycleServiceUUID];
        }
        
        for (CBCharacteristic *characteristic in cs) {
            if([characteristic.UUID.UUIDString.uppercaseString isEqual:currentCSName] || [characteristic.UUID.UUIDString.uppercaseString isEqual:kFitnessMachineStatusCharacteristics]){
                [peripheral setNotifyValue:subscribe forCharacteristic:characteristic];
            }
        }
        
        if (!subscribe && [BloothTool is667Bicycle]) {
            //取消订阅， 667 断开连接
//            NSLog(@"取消订阅， 667 断开连接");
            [manager cancleConnectPeripheralName:@{BlueDeviceType : model.type}];

        }
        
        return;
    }
    
    
    if ([dataService isEqualToString:kSecondServiceUUID]) {
        //fff0
        NSArray *cs = [chars valueForKey:kSecondServiceUUID];
        for (CBCharacteristic *characteristic in cs) {
            if ([BlueCommunicationProtocol isNotifyCharacteristic:characteristic]) {
                [peripheral setNotifyValue:subscribe forCharacteristic:characteristic];
                break;
            }
        }
        
        return;
    }
    
    if ([dataService isEqualToString:kConsoleServiceUUID.uppercaseString]) {
        //柏群
        NSArray *cs = [chars valueForKey:[kConsoleServiceUUID uppercaseString]];
        for (CBCharacteristic *characteristic in cs) {
            if ([BlueCommunicationProtocol isNotifyCharacteristic:characteristic]) {
                [peripheral setNotifyValue:subscribe forCharacteristic:characteristic];
                break;
            }

        }
       
        return;
    }
    
    if ([dataService.uppercaseString isEqualToString:kMRKServiceUUID.uppercaseString]) {
        NSLog(@"wushaoxindexieyi");
        NSArray *cs = [chars valueForKey:[kMRKServiceUUID uppercaseString]];
        for (CBCharacteristic *characteristic in cs) {
            //因为协议里的服务都需要订阅，因为有ota和数据，properties是一样的，都需要订阅，所以全部遍历
            if ([BlueCommunicationProtocol isNotifyCharacteristic:characteristic]) {
                [peripheral setNotifyValue:subscribe forCharacteristic:characteristic];
            }
        }
        
        
        return;
        
    }
    
    */
}


#pragma mark - 心率带 数据服务 订阅/取消订阅
+ (void)subscribeHeartCharacteristic:(BOOL)subscribe {
    NSString *type = [@(HeartEquipment) stringValue];
    if (![BluetoothManager isConnectEquipmentType:type]) {
        return;
    }
    BluetoothModel *bModel = [BlueDataStorageManager connectBMFromProductID:type];
    ///心率数据
    for (CBCharacteristic *characteristic in [bModel.services objectForKey:kHeartDataServiceUUID]) {
        if([characteristic.UUID.UUIDString.uppercaseString isEqual:kHeartDataCharacteristics]){
            [bModel.peripheral setNotifyValue:subscribe forCharacteristic:characteristic];
            break;
        }
    }
    
    ///电量
    NSArray *cs2 = [bModel.services valueForKey:kBatteryServiceUUID];
    for (CBCharacteristic *characteristic in cs2) {
        if([characteristic.UUID.UUIDString.uppercaseString isEqual:kHeartBatteryCharacteristics]){
            [bModel.peripheral setNotifyValue:subscribe forCharacteristic:characteristic];
            break;
        }
    }
}


#pragma mark - 1826 协议 不同设备 读取数据 对应特征值
+ (NSString *)characteristicName:(NSString *)type {
    NSString *currentCSName;
    switch (type.intValue) {
        case BoatEquipment:
            currentCSName = kFirstServiceBoatCharacteristics;
            break;
        case BicycleEquipment:
            currentCSName = kFirstServiceBicycleCharacteristics;
            break;
        case EllipticalEquipment:
            currentCSName = kFirstServiceTuoyuanCharacteristics;
            break;
        case TreadmillEquipment:
            currentCSName = kFirstServiceTreadmillCharacteristics;
            break;
            
        default:
            break;
    }
    return currentCSName;
}


+(BOOL)isNotifyCharacteristic:(CBCharacteristic *)characteristic {
    return (characteristic.properties & CBCharacteristicPropertyNotify) == CBCharacteristicPropertyNotify
                    || (characteristic.properties & CBCharacteristicPropertyIndicate) == CBCharacteristicPropertyIndicate;
}

+(BOOL)isWriteCharacteristic:(CBCharacteristic *)characteristic {
    return (characteristic.properties & CBCharacteristicPropertyWrite) == CBCharacteristicPropertyWrite
                    || (characteristic.properties & CBCharacteristicPropertyWriteWithoutResponse) == CBCharacteristicPropertyWriteWithoutResponse;
}

@end
