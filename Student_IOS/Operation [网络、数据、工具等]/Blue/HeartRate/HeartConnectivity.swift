//
//  HeartConnectivity.swift
//  Student_IOS
//
//  Created by <PERSON><PERSON> on 2024/11/11.
//


/**
import Foundation
import WatchConnectivity
import HealthKit

@objc final class HeartConnectivity: NSObject{
    @objc static let shared = HeartConnectivity()
    
    @objc dynamic var heartRate:Int = 0
    @objc dynamic var maxHeartRate:Int = 0
    @objc var session: WCSession?
    @objc dynamic var isStartWatch:Bool = false
    @objc dynamic var connectErrorString:String = ""
    
    @objc override private init() {
        super.init()
        // 注册app销毁通知
       NotificationCenter.default.addObserver(self, selector: #selector(handleNotification), name: UIApplication.willTerminateNotification, object: nil)
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
        print("HeartConnectivity deinit")
    }

    ///stop warkout
    @objc func handleNotification() {
        print("HeartConnectivity willTerminateNotification")
        
        guard let session = self.session else {
            print(self, #function, #line, "there is no session")
            return
        }
        
        if (session.isReachable){
            let parms: [String: AnyObject] = [
                "requestHeartRate": "stop" as AnyObject
            ]
            do {
                print("HeartConnectivity updateApplicationContext")
                try session.updateApplicationContext(parms)
            } catch let error as NSError {
                print(self, #function, #line, error.description)
            }
        }
    }
    
    @objc func clear() {
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) { [weak self] in
            self?.heartRate = 0
            self?.session = nil
            self?.isStartWatch = false
        }
    }
    
    @objc public func activate() {
        if session?.activationState == .activated {
            if (!session!.isPaired){
                connectErrorString = "未检测到Watch"
            }else if (!session!.isWatchAppInstalled){
                connectErrorString = "Watch端Merit应用未安装"
            }else{
                connectErrorString = ""
            }
            return
        }
        
        if WCSession.isSupported() { //makes sure it's not an iPad or iPod
            let watchSession = WCSession.default
            watchSession.delegate = self
            watchSession.activate()
            if watchSession.isPaired && watchSession.isWatchAppInstalled {
                session = watchSession
            }
        }
    }
    
    /// wakeUpWatchApp
    @objc func wakeUpWatchApp () {
        DispatchQueue.main.async { [weak self] in
            let workoutConfiguration = HKWorkoutConfiguration()
            workoutConfiguration.activityType = .other
            workoutConfiguration.locationType = .indoor
            workoutConfiguration.lapLength = HKQuantity(unit: HKUnit.count(), doubleValue: Double(self!.maxHeartRate))
            HKHealthStore().startWatchApp(with: workoutConfiguration) { (success, error) in
                if success {
                    print("Success starting workout")
                    self?.startWatch()
                }else{
                    print("error starting workout")
                }
            }
        }
    }
    
    @objc func scheduleLocalNotification() {
        let content = UNMutableNotificationContent()
        content.title = "Watch 应用提醒"
        content.body = "打开应用查看详细信息"
        content.sound = .default

        let trigger = UNTimeIntervalNotificationTrigger(timeInterval: 5, repeats: false)
        let request = UNNotificationRequest(identifier: "watchNotification", content: content, trigger: trigger)
        
        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("添加通知失败: \(error.localizedDescription)")
            }
        }
    }
    
    @objc func startWatch() {
        /// 设置心率通道的参数
        let parms: [String: AnyObject] = [
            "maxHeartRate": self.maxHeartRate as AnyObject,
            "requestHeartRate": "start" as AnyObject
        ]
        self.sendParms(parms)
    }
        
    @objc public func sendParms(_ parms: [String: AnyObject]) {
        activate()
        
        guard let session = self.session else {
            print(self, #function, #line, "there is no session")
            return
        }
        
        if let cmd = parms["requestHeartRate"] as? String,  cmd == "start" {
            print(self, #function, #line, "start there is \(session.isReachable ? "reachable" : "not reachable")")
            isStartWatch = session.isReachable;
            session.sendMessage(parms, replyHandler: nil, errorHandler: nil)
        }
        
        if let cmd = parms["requestHeartRate"] as? String,  cmd == "stop" {
            print(self, #function, #line, "stop there is \(session.isReachable ? "reachable" : "not reachable")")
            if (session.isReachable){
                session.sendMessage(parms, replyHandler: nil, errorHandler: nil)
            }else{
                DispatchQueue.main.async {
                    let workoutConfiguration = HKWorkoutConfiguration()
                    workoutConfiguration.activityType = .other
                    workoutConfiguration.locationType = .outdoor
                    HKHealthStore().startWatchApp(with: workoutConfiguration) { (success, error) in
                        if success {
                            print("Success starting workout")
                        }else{
                            print("error starting workout")
                        }
                    }
                }
            }
        }
        
//        if (!session.isReachable){
//            print(self, #function, #line, "there is no Reachable")
//            if let cmd = parms["requestHeartRate"] as? String,  cmd == "start" {
//                isStartWatch = false;
//            }
//        }else{
//            if let cmd = parms["requestHeartRate"] as? String,  cmd == "start" {
//                isStartWatch = true;
//            }
//        }
//        session.sendMessage(parms, replyHandler: nil, errorHandler: nil)
//        
//        do {
//            try session.updateApplicationContext(parms)
//        } catch let error as NSError {
//            print(self, #function, #line, error.description)
//        }
    }

    @objc private func updateHeartRate(from dictionary: [String: Any]) {
        guard let value = dictionary["heartRate"] as? Int else {
            return
        }
        print(self, #function, #line, "❤️updateHeartRate ==== \(value)")
        DispatchQueue.main.async { [weak self] in
            self?.heartRate = value
        }
    }
}


// MARK: - WCSessionDelegate
extension HeartConnectivity: WCSessionDelegate {
    @objc public func session(_ session: WCSession, activationDidCompleteWith activationState: WCSessionActivationState, error: (any Error)?) {
        if (activationState == .activated){
            if (!session.isPaired){
                connectErrorString = "未检测到Watch"
            }else if (!session.isWatchAppInstalled){
                connectErrorString = "Watch端Merit应用未安装"
            }else{
                connectErrorString = ""
            }
        }
    }
  
    @objc func sessionWatchStateDidChange(_ session: WCSession) {
        if (!session.isPaired){
            connectErrorString = "未检测到Watch"
        }else if (!session.isWatchAppInstalled){
            connectErrorString = "Watch端Merit应用未安装"
        }else{
            connectErrorString = ""
        }
    }

    @objc public func sessionDidBecomeInactive(_ session: WCSession) {
        
    }
    
    @objc public func sessionDidDeactivate(_ session: WCSession) {
        // If the person has more than one watch, and they switch,
        // reactivate their session on the new device.
        session.activate()
    }

    // This method is called when a message is sent with failable priority
    // *and* a reply was requested.
    @objc public func session(_ session: WCSession, didReceiveMessage message: [String: Any], replyHandler: @escaping ([String: Any]) -> Void) {
        print(self, #function, #line, "didReceiveMessage replyHandler====== \(message)")
        updateHeartRate(from: message)
    }
    
    // This method is called when a message is sent with failable priority
    // and a reply was *not* request.
    @objc public func session(_ session: WCSession, didReceiveMessage message: [String: Any]) {
        print(self, #function, #line, "didReceiveMessage ====== \(message)")
        updateHeartRate(from: message)
    }
    
    @objc public func session(_ session: WCSession, didReceiveApplicationContext applicationContext: [String : Any]) {
        print(self, #function, #line, "didReceiveApplicationContext ====== \(applicationContext)")
        updateHeartRate(from: applicationContext)
    }
}

*/


//     @objc func wakeUpWatchApp() {
//         DispatchQueue.main.async { [weak self] in
//             guard let self = self else { return }
//             let configuration = self.createWorkoutConfiguration()
//             self.healthStore.startWatchApp(with: configuration) { [weak self] success, error in
//                 if success {
//                     print("Success starting workout")
//                     self?.startWatch()
//                 } else if let error = error {
//                     print("Error starting workout: \(error.localizedDescription)")
//                 }
//             }
//         }
//     }
     
//
//private func createWorkoutConfiguration(activityType: HKWorkoutActivityType = .other,
//                                     locationType: HKWorkoutSessionLocationType = .indoor) -> HKWorkoutConfiguration {
//    let configuration = HKWorkoutConfiguration()
//    configuration.activityType = activityType
//    configuration.locationType = locationType
//    configuration.lapLength = HKQuantity(unit: .count(), doubleValue: Double(maxHeartRate))
//    return configuration
//}

 
 import Foundation
 import WatchConnectivity
 import HealthKit
 import UserNotifications

 // MARK: - HeartRateCommand
 enum HeartRateCommand: String {
     case start = "start"
     case stop = "stop"
 }

 // MARK: - HeartConnectivityError
 enum HeartConnectivityError: LocalizedError {
     case watchNotPaired
     case watchAppNotInstalled
     case sessionNotReachable
     
     var errorDescription: String? {
         switch self {
         case .watchNotPaired:
             return "未检测到Watch"
         case .watchAppNotInstalled:
             return "Watch端Merit应用未安装"
         case .sessionNotReachable:
             return "无法连接到Watch"
         }
     }
 }

 @objc final class HeartConnectivity: NSObject {
     // MARK: - Properties
     @objc static let shared = HeartConnectivity()
     
     @objc dynamic private(set) var heartRate: Int = 0
     @objc dynamic var maxHeartRate: Int = 0
     @objc private(set) var session: WCSession?
     @objc dynamic private(set) var isStartWatch: Bool = false
     @objc dynamic private(set) var connectErrorString: String = ""
     
     private let healthStore = HKHealthStore()
     
     // MARK: - Lifecycle
     @objc override private init() {
         super.init()
         setupNotifications()
     }
     
     deinit {
         NotificationCenter.default.removeObserver(self)
         print("HeartConnectivity deinit")
     }
     
     // MARK: - Private Methods
     private func setupNotifications() {
         NotificationCenter.default.addObserver(
             self,
             selector: #selector(stopWatch),
             name: UIApplication.willTerminateNotification,
             object: nil
         )
     }
     
     private func updateConnectionError(_ error: HeartConnectivityError?) {
         connectErrorString = error?.localizedDescription ?? ""
     }
     
     private func checkWatchStatus(_ session: WCSession) -> HeartConnectivityError? {
         if !session.isPaired {
             return .watchNotPaired
         }
         if !session.isWatchAppInstalled {
             return .watchAppNotInstalled
         }
         if !session.isReachable {
             return .sessionNotReachable
         }
         return nil
     }
     
     // MARK: - Public Methods
     @objc func clear() {
         DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) { [weak self] in
             self?.heartRate = 0
             self?.session = nil
             self?.isStartWatch = false
         }
     }
     
     @objc func activate() {
         if let session = session, session.activationState == .activated {
             updateConnectionError(checkWatchStatus(session))
             return
         }
         
         guard WCSession.isSupported() else { 
             return
         }
         
         let watchSession = WCSession.default
         watchSession.delegate = self
         watchSession.activate()
         if watchSession.isPaired && watchSession.isWatchAppInstalled {
             session = watchSession
         }
     }
     
     @objc func wakeUpWatchApp() {
         guard let session = session, session.isReachable else {
             print("设备传输不可达")
             return
         }
         
         let message = ["action": "wakeUp"]
         session.sendMessage(message, replyHandler: {[weak self] replyHandler in
            print("发送成功")
            self?.startWatch()
         }) { error in
            print("发送失败: \(error.localizedDescription)")
         }
     }
     
     @objc func startWatch() {
         let params: [String: AnyObject] = [
             "maxHeartRate": maxHeartRate as AnyObject,
             "requestHeartRate": HeartRateCommand.start.rawValue as AnyObject
         ]
         sendParms(params)
     }
     
     @objc func stopWatch() {
         let params: [String: AnyObject] = [
             "requestHeartRate": HeartRateCommand.stop.rawValue as AnyObject
         ]
         sendParms(params)
     }
     
     @objc public func sendParms(_ params: [String: AnyObject]) {
         activate()
         
         guard let session = session, session.isReachable else {
             print("设备传输不可达")
             return
         }
         
         isStartWatch = session.isReachable
         session.sendMessage(params, replyHandler: nil, errorHandler: nil)
     }
     
     @objc private func updateHeartRate(from dictionary: [String: Any]) {
         guard let value = dictionary["heartRate"] as? Int else { return }
         print(self, #function, #line, "❤️updateHeartRate ==== \(value)")
         DispatchQueue.main.async { [weak self] in
             self?.heartRate = value
         }
     }
 }

 // MARK: - WCSessionDelegate
 extension HeartConnectivity: WCSessionDelegate {
     @objc public func session(_ session: WCSession,
                              activationDidCompleteWith activationState: WCSessionActivationState,
                              error: (any Error)?) {
         if activationState == .activated {
             updateConnectionError(checkWatchStatus(session))
         }
     }
     
     @objc func sessionWatchStateDidChange(_ session: WCSession) {
         updateConnectionError(checkWatchStatus(session))
     }
     
     @objc public func sessionDidBecomeInactive(_ session: WCSession) {
         // Handle session becoming inactive if needed
     }
     
     @objc public func sessionDidDeactivate(_ session: WCSession) {
         session.activate()
     }
     
     @objc public func session(_ session: WCSession,
                              didReceiveMessage message: [String: Any],
                              replyHandler: @escaping ([String: Any]) -> Void) {
         updateHeartRate(from: message)
     }
     
     @objc public func session(_ session: WCSession,
                              didReceiveMessage message: [String: Any]) {
         updateHeartRate(from: message)
     }
     
     @objc public func session(_ session: WCSession,
                              didReceiveApplicationContext applicationContext: [String: Any]) {
         updateHeartRate(from: applicationContext)
     }
 }
 

