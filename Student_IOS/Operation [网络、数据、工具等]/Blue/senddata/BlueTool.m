//
//  BlueTool.m
//  Student_IOS
//
//  Created by MacPro on 2021/6/30.
//

#import "BlueTool.h"
#import "User.h"

#define kMETValues @[@11.5 , @10,@8,@6,@4,@2.5]

@implementation BlueTool


+ (void)downloadFileFromUrl:(NSString *)urlStr
                   progress:(ProgressBlock)downloadProgressBlock
                    success:(SuccessBlock)success
                    failure:(FailureBlock)failure {
    /* 创建网络下载对象 */
    AFURLSessionManager *manager = [[AFURLSessionManager alloc] initWithSessionConfiguration:[NSURLSessionConfiguration defaultSessionConfiguration]];
    
    /* 下载地址 */
    NSURL *url = [NSURL URLWithString:urlStr];
    NSURLRequest *request = [NSURLRequest requestWithURL:url];
    
    /* 下载路径 */
    NSString *path = [NSHomeDirectory() stringByAppendingPathComponent:@"Documents"];
    __block NSString *filePath = [path stringByAppendingPathComponent:url.lastPathComponent];
    
    
    /* 开始请求下载 */
    NSURLSessionDownloadTask *downloadTask = [manager downloadTaskWithRequest:request progress:^(NSProgress * _Nonnull downloadProgress) {
        
        downloadProgressBlock(downloadProgress);
        NSLog(@"下载进度：%.0f％", downloadProgress.fractionCompleted * 100);
        
    } destination:^NSURL * _Nonnull(NSURL * _Nonnull targetPath, NSURLResponse * _Nonnull response) {
        
        /* 设定下载到的位置 */
        return [NSURL fileURLWithPath:filePath];
        
    } completionHandler:^(NSURLResponse * _Nonnull response, NSURL * _Nullable filePathUrl, NSError * _Nullable error) {
        
        NSLog(@"download error ---- %@" , error.localizedDescription);
        if (error) {
            NSLog(@"下载失败");
            failure(nil,error);
            
            [BlueTool removeDocumentWithFilePath:filePath];
            
            return;
        }
        
        NSLog(@"下载完成");
        NSLog(@"download success ---- %@" , response);
        success(filePath);
        
    }];
    [downloadTask resume];
}


//删除文件
+ (BOOL)removeDocumentWithFilePath:(NSString*)filePath{
    BOOL isRemove = false;
    NSFileManager* fileManager=[NSFileManager defaultManager];
    if([[NSFileManager defaultManager]fileExistsAtPath:filePath]) {
        isRemove = [fileManager removeItemAtPath:filePath error:nil];
    
    }
    return isRemove;
}

+(BOOL)isIConsleWithPeripheralName:(NSString *)localName {
    for (NSString *p in kIConsolePeripheralNamePrefixArray) {
        if ([localName hasPrefix:p]) {
            for (NSString *obj in kZJPeripheralNamePrefixArray) {
                if ([localName hasPrefix:obj]) {
                    return NO;
                }
            }
            
            return  YES;
        }
    }
    
    return NO;
}

+(BOOL)isIConsle:(NSString *)type {
    BluetoothModel *bModel = [BlueDataStorageManager connectBMFromProductID:type];
    return bModel.dataServiceType.intValue == BQCommunicationProtocol;
}


#pragma mark - 根据心率数据计算
+(double)calculateIncreaseEneryFromHeart:(NSNumber *)rate {
    double increase = 0;
    
    //算法1: 当前心率 * (0.05 + (当前心率 - 50%目标心率) / 800) / 100
    //    NSNumber *age = [User getUserAge];
    //    increase = rate.intValue * (0.05 + (rate.intValue - (220 - age.intValue) * 0.5)/800.0)/100.0;
    
    //算法2:9-25
    //(体重*MET*17.5)/60000
    NSNumber *weight = UserInfo.weight;
    NSLog(@"weight==%@" , weight);
    double met = [BlueTool getMETFromRate:rate];
    increase = (weight.doubleValue * met * 17.5 ) / 60000;
    NSLog(@"calculateIncreaseEneryFromHeart increase:%@" , @(increase));
    return increase;
}



#pragma mark - 根据设备数据计算
+(double)calculateIncreaseEneryFromDevice:(BaseEquipDataModel *)model equip:(EquipmentDetialModel *)eqModel{
    double increase = 0;
    
    NSNumber *weight = UserInfo.weight;
    
    //算法1:
    /*
     if (model.type.intValue == TreadmillEquipment) {
     CGFloat increase = 0;
     //    体重：如果用户没有输入，男性默认设置成60kg，女性默认设置成50kg，如果没有性别就设置成55kg
     //    体重kg * 1.25 * 瞬时速度km/h / 3600
     increase = weight.doubleValue * 1.25 * model.speed.floatValue / 3600;
     
     
     return increase;
     }
     */
    
    //算法2
    double met = [BlueTool getMETFromDevice:model equip:eqModel];
    //    float tmp = (float)([self getRandomNumber:10 to:14] / 100.0);//外挂算法
    //    increase = (weight.doubleValue * met * 17.5 ) / 60000 + tmp;
    increase = (weight.doubleValue * met * 17.5 ) / 60000 ;
    NSLog(@"calculateIncreaseEneryFromDevice increase:%@" , @(increase));
    
    return increase;
    
}

+ (double)getMETFromDevice:(BaseEquipDataModel *)model equip:(EquipmentDetialModel *)eqModel{
    
    double met = 1.0;
    NSArray *arr = [NSArray array];
    
    switch (model.type.intValue) {
        case TreadmillEquipment:
        {
            if (model.speed.doubleValue == 0.0) {
                met = 0;
            } else {
                
                arr = @[@8.5, @7.5 , @6 , @5, @3];
                met = [BlueTool getMETFromParameter:model.speed grades:arr];
            }
        }
            break;
        case BicycleEquipment:
        {
            if (model.spm.intValue == 0) {
                met = 0;
            } else {
                
                double str = [BlueTool strengthRatio:model equip:eqModel];
                arr = @[@10, @9 , @7];
                met = [BlueTool getMETFromParameter:@(str) grades:arr];
            }
            
        }
            break;
        case EllipticalEquipment:
        {
            if (model.spm.intValue == 0) {
                met = 0;
            } else {
                double str = [BlueTool strengthRatio:model equip:eqModel];
                arr = @[@9, @8 , @6.5 ];
                met = [BlueTool getMETFromParameter:@(str) grades:arr];
            }
            
        }
            break;
        case BoatEquipment:
        {
            if (model.spm.intValue == 0) {
                met = 0;
            } else {
                if (model.spm.intValue >=28 || model.drag.intValue>= 16) {
                    met = 6.0;
                } else {
                    met = 4.0;
                }
            }
        }
            break;
            
        default:
            met = 0;
            break;
    }
    
    return met;
}


#pragma mark - 计算运动强度系数（单车/椭圆机）
+ (double)strengthRatio:(BaseEquipDataModel *)model equip:(EquipmentDetialModel *)eqModel {
    double dragFactor = 0;//阻力系数
    if (eqModel.maxResistance.intValue == 0 || model.drag.intValue == 0) {
        dragFactor = 2.5;
    } else {
        dragFactor = model.drag.doubleValue / eqModel.maxResistance.doubleValue * 10;
    }
    double strengthRatio = model.spm.intValue / 10.0 + dragFactor;
    return strengthRatio;
    
}


#pragma mark - rate -> met
+ (double)getMETFromRate:(NSNumber *)rateNumber{
    double met = 1.0;
    
    int age = UserInfo.age;
    NSLog(@"AGE==%d" , age);
    int targetRate = (220 - age);
    
    NSArray *arr = @[@(0.9 * targetRate ) ,
                     @(0.8 * targetRate) ,
                     @(0.7 * targetRate),
                     @(0.6 * targetRate),
                     @(0.5 * targetRate)];
    met = [BlueTool getMETFromParameter:rateNumber grades:arr];
    return met;
}

+ (double)getMETFromParameter:(NSNumber *)param grades:(NSArray *)arr {
    double met = 1.0;
    NSArray *values ;
    
    if (arr.count == 5) {
        values = @[@11.5 ,
                   @10 ,
                   @8 ,
                   @6 ,
                   @4 ,
                   @2.5];
        
    } else if (arr.count == 3) {
        values = @[@10 , @8 , @6 , @4 ];
    }
    
    int index = [BlueTool sectionOfnumber:param inArray:arr];
    met = [values[index] doubleValue];
    return met;
}


/// 目标数字在这个数组的哪个区间，返回index
/// @param number 目标数据
/// @param array 倒序数组
+ (int)sectionOfnumber:(NSNumber *)number inArray:(NSArray *)array {
    int result = 0;
    for (int i = 0; i < array.count; i++) {
        double temp = [array[i] doubleValue];
        if (number.doubleValue >= temp) {
            result = i;
            break;
        }
        
        if ((i + 1) == array.count && number.doubleValue <= temp) {
            result = i + 1;
            break;
        }
        
        double next = [array[i + 1] doubleValue];
        if (number.doubleValue < temp && number.doubleValue >= next) {
            result = i + 1;
            break;
        }
    }
    NSLog(@"number==%@ ,result === %d" , number , result);
    return  result;
}


+ (int)getRandomNumber:(int)from to:(int)to {
    return (int)(arc4random() % (to - from + 1) + from);
}



//+(BOOL)isTreamillX1:(NSString *)type {
//    if (type.intValue != TreadmillEquipment) {
//        return NO;
//    }
//    if (![BluetoothManager isConnectEquipmentType:type]) {
//        return NO;
//    }
//    if ([[BluetoothManager sharedInstance].connectDic objectForKey:type]) {
//        <#statements#>
//    }
//    
//}


#pragma mark - 根据心率数据计算
+ (double)calculateIncreaseEneryFromHeart:(NSNumber *)rate withModel:(HeartKcalConfig *)model {
    if (rate.intValue <= 0){
        return 0;
    }
    
    ///兜底
    if (model == nil) {
        return [BlueTool calculateIncreaseEneryFromHeart:rate];
    }
    
    double increase = 0;
    double d = model.weight.doubleValue;
    
    NSInteger a = 220 - model.age.integerValue;
    double b = rate.doubleValue;
    
    double c = 0;
    for (NSString *key in model.factor.allKeys) {
        double value = a * key.doubleValue;
        if ( b >= value) {
            c = [[model.factor objectForKey:key] doubleValue];
            break;
        }
    }
    
    increase = (d * c * 17.5 )/60000;
    NSLog(@"calculateIncreaseEneryFromHeart increase:%@", @(increase));
    return increase;
}

@end



@implementation HeartKcalConfig

@end

