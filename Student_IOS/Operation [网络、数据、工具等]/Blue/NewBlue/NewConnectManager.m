//
//  NewConnectManager.m
//  Student_IOS
//
//  Created by MacPro on 2023/3/15.
//

#import "NewConnectManager.h"

#import "BlueDataStorageManager.h"
#import "MRKConnectStatusManager.h"
#import "MRKAutoConnectManager.h"
#import "MRKDeviceModel.h"
#import "MRKDeviceURLRequest.h"
#import "BlueCheckOTAManager.h"
#import "MRKDeviceConnectAlertView.h"
#import "MRKOTAUpdateViewController.h"
#import "MRKBuglyManager.h"
#import "BlufiManager.h"
#import "MRKDeviceManager.h"
#import "QCProtocolCommandManager.h"

#define  kConnectTimeout 10.0

@interface NewConnectManager ()
///解绑设备的信息 解绑成功/失败后置为nil
@property (nonatomic, strong, nullable) MRKDeviceModel *unbindModel;
///loading 加载到当前页面，如果连接过程中返回，连接失败弹窗不再提示
///使用weak,因为当前控制器可能会持有该类，strong会循环引用
@property (nonatomic, weak) UIViewController *currentVC;

@end

@implementation NewConnectManager

- (instancetype)init {
    self = [super init];
    if(self) {
        [self initData];
    }
    return self;
}

- (void)initData {
    self.currentVC = [UIViewController currentViewController];
    
    self.bpMananger = [NewBluePeripheralManager sharedInstance];
    
    self.modelList = [NSArray array];
    
    NSLog(@"NewConnectManager___init===%@" , self);
    @weakify(self);
    [[[RACObserve(self, status) filter:^BOOL(NSNumber * value) {
        return value.intValue > 0;
    }] distinctUntilChanged] subscribeNext:^(NSNumber * x) {
        @strongify(self);
        if(self.connectModel){
            [[NSNotificationCenter defaultCenter] postNotificationName:@"kDeviceConnectingNotifcation"
                                                                object:@{@"cModel":self.connectModel, @"status" : x}];
        }
    }];
}

- (void)startScan {
    @weakify(self);
    self.bpMananger.discoverBlock = ^(id data) {
        @strongify(self);
        [self didDiscoverPeripheral:data];
    };
    [self.bpMananger startScan];
}

- (void)stopScan {
    [self.bpMananger stopScan];
}

///连接具体的某个设备
- (void)connectDeviceModel:(MRKDeviceModel *)dModel {
    ///取消所有的自动重连
    if (dModel.productType.intValue == 1) {
        NSLog(@"我手动点击连接了,并且连接的是运动设备,取消所有的自动重连");
        [[MRKAutoConnectManager sharedInstance] cancelAllAutoConnect];
    }
    
    //    if([MRKAutoConnectManager autoModelFromProductID:dModel.productId]) {
    //        ///将要连接的设备是正在重连的设备 取消重连
    //        [[MRKAutoConnectManager sharedInstance] cancelAutoConnect:dModel.productId];
    //    }
    
    ConnectModel *cm = [[ConnectModel alloc] init];
    cm.type = dModel.productId;
    cm.name = dModel.name;
    cm.info = dModel.modelToJSONObject;
    cm.bindStatus = @1;
    cm.hiddenFailAlert = dModel.hiddenFailAlert;
    [self connectDevice:cm];
}
          
///连接
- (void)connectDevice:(ConnectModel *)cModel {
    ///判断蓝牙权限
    if([NewBluePeripheralManager sharedInstance].state != CBManagerStatePoweredOn){
        self.connectModel = cModel;
        [self connectDeviceFail:[NSError errorWithDomain:@"" code:ConnectingBleAuthFailCode userInfo:@{NSLocalizedDescriptionKey : @"未获取到蓝牙权限"}] type:cModel.type];
        return;
    }
    
    ///判断是否可以继续连接
    if(![self isNeedConnectDevice:cModel.type name:cModel.name]) {
        return;
    }
    
    ///如果该设备存储的主动断开连接的设备中，现在重新连接，肯定是需要删除主动断开的设备存储的
    [BlueDataStorageManager deletedisBluetoothModel:[BluetoothModel bModelFromCModel:cModel]];
    
    self.connectModel = cModel;
    [BlueDataStorageManager saveConnectingDevice:cModel];
    
    if(cModel.type) {
        NSMutableDictionary *dic = [NSMutableDictionary dictionaryWithDictionary:[BlueDataStorageManager sharedInstance].connectingDictionary];
        NSString *log = [NSString stringWithFormat:@"&&开始连接&&：%@",dic];
        MLog(log);
    }
    
    BOOL res = [self dealDifferentProductIDConnect:cModel];
    if (res) {
        ///单独处理连接
        return;
    }
    
    NSLog(@"%@_________________connectDevice==%@" , self , cModel.name);
    
    for (BluetoothModel *bModel in self.modelList) {
        ///已经搜索到指定设备 直接连接
        if (self.connectModel && [self.connectModel.name isEqualToString:bModel.localName]) {
            bModel.equipmentInfo = self.connectModel.info;
            bModel.type = self.connectModel.type;
            _connectModel.peripheral = bModel.peripheral;
            
            NSLog(@"不用搜索了，我要开始连接==%@==%@" , self.connectModel.name , self);
            [self connectBluetoothModel:bModel];
            return;
        }
    }
    
    NSLog(@"我要开始搜索连接==%@==%@" , self.connectModel.name , self);
    jxt_getSafeMainQueue(^{
        NSLog(@"我要添加搜索超时监听了==%@==%@" , self , [NSThread currentThread]);
        [self performSelector:@selector(scanConnectDeviceTimeout:) withObject:cModel.type afterDelay:kConnectTimeout];
    });
    self.status = DeviceScaning;
    [self startScan];
}

- (BOOL)dealDifferentProductIDConnect:(ConnectModel *)cModel {
    NSString *type = cModel.type;
    MRKDeviceModel *dModel = [MRKDeviceModel modelWithDictionary:cModel.info];
//    if(type.intValue == FatScaleEquipment) {
//        ///体脂秤
//        NSString *mac = [cModel.info objectForKey:@"mac"];
//        @weakify(self);
//        ///连接超时
//        __block RACDisposable *countDispose = [[[[[RACSignal interval:1 onScheduler:[RACScheduler mainThreadScheduler]] startWith:[NSDate date]] scanWithStart:@(kConnectTimeout)reduce:^id(NSNumber*running,id next) {
//            NSLog(@"running = %@",running);
//            return @(running.integerValue-1);
//        }] takeUntilBlock:^BOOL(NSNumber*x) {
//            return x.integerValue<0;
//        }] subscribeNext:^(id x) {
//            NSLog(@"countsingal===%@" , x);
//            if([x intValue] == 0) {
//                [[WeightDeviceManager shareManager] stopConnectDevice:mac];
//            }
//        }];
//
//        __block RACDisposable *dis = [[[NSNotificationCenter defaultCenter] rac_addObserverForName:@"kWeightDeviceConnectSuccess" object:nil] subscribeNext:^(NSNotification * x) {
//            @strongify(self);
//            [dis dispose];
//            [countDispose dispose];
//
//            NSLog(@"体脂秤连接成功==%@" ,x.object);
//            [self connectSuccess:x.object];
//        }];
//
//        __block RACDisposable *dis2 = [[[RACObserve([WeightDeviceManager shareManager], status) distinctUntilChanged] takeUntil:[self rac_willDeallocSignal]] subscribeNext:^(NSNumber * x) {
//            @strongify(self);
//            self.status = x.intValue;
//            if(x.intValue == DeviceConnected || x.intValue == DeviceDisconnect) {
//                [dis2 dispose];
//                [countDispose dispose];
//                if(x.intValue == DeviceDisconnect) {
//                    ///连接失败 不提示
//                    [self connectDeviceFail:nil type:cModel.type];
//                }
//                ///成功/失败 连接中数据存储删除
//                [BlueDataStorageManager deleteConnectingDevice:type];
//                self.connectModel = nil;
//            }
//        }];
//
//        [[WeightDeviceManager shareManager] connectDevice:dModel];
//
//        return YES;
//    }
    
    return NO;
}


///已经搜索到某个设备，开始连接
- (void)connectBluetoothModel:(BluetoothModel *)bModel {
    self.connectModel = [BluetoothModel cModelFromBModel:bModel];
    
    ///防止已经搜索到设备，在连接过程中，触发连接指定设备超时事件
    jxt_getSafeMainQueue(^{
        NSLog(@"我要取消搜索超时监听了==%@==%@" , self , [NSThread currentThread]);
        [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(scanConnectDeviceTimeout:) object:bModel.type];
    });
    
    ///判断是否可以继续连接
    if(![self isNeedConnectDevice:bModel.type name:bModel.localName]) {
        return;
    }
    
    NSLog(@"已经搜索到要连接的设备，开始连接");
    [self stopScan];
    self.status = DeviceConnecting;
    
    // 彩屏单车
    if ([[MRKDeviceManager shareManager] isJudgeBlufiDevice:((MRKDeviceModel *)bModel.equipmentModel).communicationType]) {

        @weakify(self);
        [BlufiManager shareManager].connectBlock = ^(BOOL flag, BluetoothModel * _Nullable model) {
            NSLog(@"连接小彩屏==%@==%@" , @(flag) , model);
            @strongify(self);
            //此处不走blufi的成功回调，所以只需要做失败处理
            if (!flag)  { // 失败
                [self connectDeviceFail:[NSError errorWithDomain:@"" code:10000 userInfo:@{NSLocalizedDescriptionKey : @"小彩屏连接失败，原因未知"}] type:model.type];
            } else {
                NSLog(@"小彩屏 成功了=====开始走自定义SDK连接");
                //本地sdk连接
                self.bpMananger.connectSuccessBlock = ^(id data) {
                    @strongify(self);
                    NSLog(@"自定义SDK连接成功了");
                    [self connectSuccess:data];
                };
                self.bpMananger.connectFailBlock = ^(NSError * error) {
                    @strongify(self);
                    NSLog(@"自定义SDK连接失败了,断开小彩屏SDK的连接");
                    [[BlufiManager shareManager] closeClient];
                    [self connectDeviceFail:error type:bModel.type];
                };
                [self.bpMananger connectModel:model];
            }
        };
        
        [[BlufiManager shareManager] connect:bModel];
    } else {
        // 其他
        @weakify(self);
        self.bpMananger.connectSuccessBlock = ^(id data) {
            @strongify(self);
            [self connectSuccess:data];
        };
        self.bpMananger.connectFailBlock = ^(NSError * error) {
            @strongify(self);
            [self connectDeviceFail:error type:bModel.type];
        };
        
        [self.bpMananger connectModel:bModel];
    }
    
    ///连接超时
    jxt_getSafeMainQueue(^{
        [self performSelector:@selector(connectTimeout:) withObject:bModel.type afterDelay:kConnectTimeout ];
    });
}

- (void)cancelConnectDeviceModel:(MRKDeviceModel *)dModel {
    NSLog(@"我手动点击断开连接了，不再开启自动重连");
    
    ///健康设备
    if (dModel.productType.intValue == 3) {
        ///健康设备 断开连接 通知心率带记录不再重连
        NSDictionary *parms = @{
            @"name" : dModel.bluetoothName,
            @"macAddress" : dModel.mac,
            BlueDeviceType : dModel.productId
        };
        [[NSNotificationCenter defaultCenter] postNotificationName:kBlueDisConnectNotification object:parms];
    }
    
//    //体脂秤
//    if(dModel.productId.intValue == FatScaleEquipment) {
//        [[WeightDeviceManager shareManager] stopConnectDevice:dModel.mac];
//        return;
//    }
    
    BluetoothModel *bModel = [BluetoothModel new];
    bModel.type = dModel.productId;
    bModel.localName = dModel.bluetoothName;
    bModel.equipmentModel = dModel;
    bModel.autoDisconnect = YES;
    [self cancelConnectDevice:bModel];
}


///取消/断开连接 某个设备 必须传入设type,name
- (void)cancelConnectDevice:(BluetoothModel *)bModel {
    MLog(@"取消/断开连接 == %@ == %@", bModel.localName, bModel.peripheral);
//    if([[MRKDeviceManager  shareManager] isJudgeBlufiDevice:((MRKDeviceModel *)bModel.equipmentModel) .communicationType]) {
//        //小彩屏
//        [[BlufiManager shareManager] disconnectDevice];
//        return;
//    }
    if (bModel.autoDisconnect) {
        ///主动断开连接 记录 不再重连
        [BlueDataStorageManager savedisBluetoothModel:bModel];
    }
    
    ///1.判断该设备是否连接
    if ([BlueDataStorageManager isConnectDeviceWithProductID:bModel.type name:bModel.localName]) {
        bModel = [BlueDataStorageManager connectBMFromProductID:bModel.type];
        ///是 ， 直接断开
        if(bModel.peripheral) {
            NSLog(@"已经连接，断开连接");
            [self.bpMananger disconnectModel:bModel];
        }
        return;
    }
    
    ///否
    ///2.判断连接状态,要么是搜索中，要么是连接中
    if (self.status == DeviceScaning) {
        ///如果当前正在尝试连接设备，还在搜索中，则取消搜索超时机制
        [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(scanConnectDeviceTimeout:) object:bModel.type];
        NSLog(@"取消搜索超时" );
    }
    
    if (self.status == DeviceConnecting) {
        ///如果当前正在尝试连接设备，已经正在连接中，需要取消，则取消超时机制
        [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(connectTimeout:) object:bModel.type];
        NSLog(@"取消连接超时" );
    }
    
    if( _connectModel
       && _connectModel.peripheral
       && [_connectModel.type isEqualToString:bModel.type]
       && [_connectModel.name isEqualToString:bModel.localName]) {
        
        NSLog(@"取消，已经搜到，指定连接的设备的连接");
        bModel.peripheral = _connectModel.peripheral;
    }
    
    ///删除连接中存储
    [BlueDataStorageManager deleteConnectingDevice:bModel.type];
    
    if (bModel.peripheral) {
        [self.bpMananger disconnectModel:bModel];
        NSLog(@"正在连接,取消连接");
    } else {
        [self.bpMananger stopScan];
        self.connectModel = nil;
        NSLog(@"还没搜索到，取消连接，停止搜索，我把connectModel置为nil了");
    }
}

///先搜索 再连接 超时
- (void)scanConnectDeviceTimeout:(NSString *)type {
    /// 2024.1.11 tzjdh 添加停止搜索逻辑   （心率臂带在播放视频的时候自动重连超时后并未停止搜索）
    [self stopScan];
    [self connectDeviceFail:[NSError errorWithDomain:@"" code:ConnectSearchTimeoutFailCode userInfo:@{NSLocalizedDescriptionKey : @"连接指定设备时，搜索超时了还未搜到指定设备"}] type:type];
}

///连接超时
- (void)connectTimeout:(NSString *)type {
    [self connectDeviceFail:[NSError errorWithDomain:@"" code:ConnectingTimeoutFailCode userInfo:@{NSLocalizedDescriptionKey : @"搜到设备，设备连接超时"}] type:type];
    // 彩屏单车
    MRKDeviceModel *dModel = [MRKDeviceModel modelWithJSON:self.connectModel.info];
    if ([[MRKDeviceManager shareManager] isJudgeBlufiDevice:dModel.communicationType]) {
        [[BlufiManager shareManager] closeClient];
    }
}

- (void)connectDeviceFail:(nullable NSError *)error type:(NSString *)type {
    ///已经连接失败 取消连接超时的监测
    jxt_getSafeMainQueue(^{
        [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(connectTimeout:) object:type];
    });
    self.status = DeviceDisconnect;
    ConnectModel *connectModel = [BlueDataStorageManager connectManagerName:type];
    [BlueDataStorageManager deleteConnectingDevice:type];
    
    ///连接失败原因上报bugly 23-05-19
    if(error){
        NSString *errorStr = [NSString stringWithFormat:@"设备连接失败====deviceName:%@==== userId:%@,原因==%@", self.connectModel.name, UserInfo.userId ,error.localizedDescription ? : @""];
        NSString *log = [NSString stringWithFormat:@"&&设备连接失败&&：%@",errorStr];
        MLog(log);
    }
    
    if (self.connectModel.hiddenFailAlert) {
        self.connectModel = nil;
        if (self.connectStatusBlock) {
            self.connectStatusBlock(@0);
        }
        return;
    }
    
    self.connectModel = nil;
    
    if(error
       && self.connectMode == ManualDeviceConnectMode
       && self.currentVC && (!connectModel || !connectModel.hiddenFailAlert)) {
        NSLog(@"连接失败==%@" ,error.localizedDescription);
        ///手动连接失败 提示原因
        [MRKDeviceConnectAlertView alertConnectFailView:error];
    }
    
    if (self.connectStatusBlock) {
        self.connectStatusBlock(@0);
    }
}

#pragma mark - NewBluePeripheralManager block
- (void)didDiscoverPeripheral:(BluetoothModel *)bModel {
    MLog(@"didDiscoverPeripheral:name==%@,state:==%ld" ,bModel.localName , bModel.peripheral.state);
    if ([[self.modelList valueForKeyPath:@"localName"] containsObject:bModel.localName]) {
        ///列表已经有了
        NSLog(@"%@__didDiscoverPeripheral==%@" ,self, [self.modelList valueForKeyPath:@"localName"]);
        return;
    }
    
    NSMutableArray *array = self.modelList.mutableCopy;
    [array addObject:bModel];
    self.modelList = array.copy;
    
    NSLog(@"我搜到设备==%@，当前连接的设备是==%@==%@==%@" ,bModel.localName, self.connectModel.name , self , self.delegate);
    
    if (self.delegate && [self.delegate respondsToSelector:@selector(discoverBluetoothModel:)]) {
        [self.delegate discoverBluetoothModel:bModel];
    }
    
    ///搜索到需要连接的指定设备，开始连接
    if (self.connectModel && [self.connectModel.name isEqualToString:bModel.localName]) {
        bModel.equipmentInfo = self.connectModel.info;
        bModel.type = self.connectModel.type;
        _connectModel.peripheral = bModel.peripheral;
        
        [self connectBluetoothModel:bModel];
    }
}


#pragma mark -
- (void)connectSuccess:(BluetoothModel *)bModel {
    ///先取消连接超时监听，防止因接口延迟导致先走了连接失败回调，又走了成功回调
    jxt_getSafeMainQueue(^{
        [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(connectTimeout:) object:bModel.type];
    });
    
    MRKDeviceModel *dModel = (MRKDeviceModel *)bModel.equipmentModel;
    bModel.infomations = bModel.infomationsDic.allValues;
    NSDictionary *para = @{
        @"bluetoothName"      : bModel.localName ?:@"", ///蓝牙广播名
        @"deviceUserRelId"    : dModel.deviceUserRelId ?:@"", ///用户设备关联id（原设备id字段）
        @"productId"          : dModel.productId ?: (bModel.type ?: @""),
        @"modelId"            : dModel.modelId ?: @"",
        @"mac"                : @"",
        @"snCode"             : dModel.snCode ?: @"",
        @"characteristic"     : [bModel.infomations isNotEmpty] ? bModel.infomations :@[], ///所有特征值数据
        @"uniqueModelIdentify": [bModel.uniqueInfomations isNotEmpty] ? bModel.uniqueInfomations :@[],///唯一型号确认json
        @"autoReward"         : @"0" ///*******版本以后默认不领取设备绑定的10天会员
    };
    
    
    /// 线上bModel.infomations里2A23里包含特殊字符, 此处埋点个别设备容易崩
    NSString *log = [NSString stringWithFormat:@"&&设备连接成功para&&：%@", [para modelToJSONString]];
    @try {
        MLog(log);
    } @catch (NSException *exception) {

    } @finally {

    }
    [MRKDeviceURLRequest requestDeviceConnect:para success:^(id data) {
        id json = [data valueForKeyPath:@"data"];
        MRKDeviceModel *dModel = [MRKDeviceModel modelWithJSON:json];
        ///连接成功后 拿到确定信息 设置model
        bModel.equipmentModel = dModel;
        //存储类 存储已连接信息
        [BlueDataStorageManager saveBluetoothModel:bModel];
        
        ///删除连接中的存储数据
        [BlueDataStorageManager deleteConnectingDevice:bModel.type];
        
        self.status = DeviceConnected;
        self.connectModel = nil;
        NSLog(@"设备连接/绑定成功，我把connectModel置为nil了");
        NSLog(@"modelDescription===%@", [BlueDataStorageManager connectBMFromProductID:dModel.productId]);
        
        //是否绑定操作 1是0否
        NSString *bind = [NSString stringWithFormat:@"%@", json[@"isBind"]?:@"0"];
        ///通知刷新重连设备接口
        [[NSNotificationCenter defaultCenter] postNotificationName:@"kUpdateHomeShowDeviceNotification" object:@{@"bind":bind,
                                                                                                                 @"productID":dModel.productId,
                                                                                                                 @"productType":dModel.productType}];
        ///检查ota
        [self checkOTA:dModel];
        
        ///心率带连接成功通知 [通知注册socket 上报通道]
        if (dModel.productId.intValue == HeartEquipment) {
            NSString *originId = dModel.deviceUserRelId;
            [[NSNotificationCenter defaultCenter] postNotificationName:@"OpenSocketNotification" object:originId];
        }
        
        if (dModel.productId.intValue == KettleBellEquipment) {
            // 智能壶铃连接成功同步时间
            NSDate *current = [NSDate date];
            NSArray *dateArray = [[MRKTimeManager getDateTimeString:current formatter:@"yyyy MM dd HH mm ss"] componentsSeparatedByString:@" "];
            NSMutableArray *array = [NSMutableArray arrayWithArray:dateArray];
            NSString *week = [MRKTimeManager intDateToWeek:current];
            [array appendObject:week];
            [QCProtocolCommandManager syncTime:dModel.productId para:@{@"time": [array copy]}];
        }
        
        if (self.connectStatusBlock) {
            self.connectStatusBlock(@1);
        }
        
        //小彩屏，设置首页数据
        if([[MRKDeviceManager shareManager] isJudgeBlufiDevice:dModel.communicationType]) {
            MLog(@"小彩屏连接成功，设置首页数据 ");
            [[NSNotificationCenter defaultCenter] postNotificationName:@"kUpdateHomeSportDataNotification" object:@{@"productId": dModel.productId}];
        }
        
        ///添加提示窗
        [MBProgressHUD showConnectSuccessText:dModel.productName view:nil];
        
        NSString *log = [NSString stringWithFormat:@"&&设备连接接口成功&&：%@", json];
        MLog(log);
    } fail:^(id data) {
        ///连接失败
        [self connectDeviceFail:[NSError errorWithDomain:@"" code:ConnectingServiceFailCode userInfo:@{NSLocalizedDescriptionKey : @"调用连接接口报错"}] type:bModel.type];
        ///连接失败 断开连接
        [self cancelConnectDevice:bModel];
        NSString *log = [NSString stringWithFormat:@"&&设备连接接口失败&&：%@",data];
        MLog(log);
    }];
    
}

#pragma mark - 检查ota
////连接成功 检查ota ，如果需要弹出更新 ，则判断当前页面是否是首页/设备详情页面，如果是 弹出弹窗并且通知服务端记录弹出状态，如果不是 不弹出
- (void)checkOTA:(MRKDeviceModel *)dModel {
    BOOL canAlertOta = NO;
    UIViewController *currentVC = [UIViewController currentViewController];
    if( [currentVC isKindOfClass:NSClassFromString(@"MRKDeviceDetailController")]
       || [currentVC isKindOfClass:NSClassFromString(@"MRKDeviceDetailController")]
       || [currentVC isKindOfClass:NSClassFromString(@"MRKMainPageController")] ) {
        canAlertOta = YES;
    }
    
    if (dModel.isOta.boolValue && canAlertOta) {
        NSLog(@"连接成功了，开始检查ota弹窗了");
        
        ///支持ota 开始检查
        [BlueCheckOTAManager checkDeviceOTA:dModel success:^(id data) {
            OTAModel *oModel = data;
            if(oModel.isPop.boolValue)
            {
                [MRKDeviceConnectAlertView alertOTAUpdateView:oModel action:^(id data) {
                    if([data boolValue]) {
                        NSLog(@"固件更新");
                        MRKOTAUpdateViewController *vc = [MRKOTAUpdateViewController new];
                        vc.model = dModel;
                        vc.otaM = oModel;
                        [currentVC.navigationController pushViewController:vc animated:YES];
                    }else {
                        NSLog(@"取消固件更新");
                    }
                }];
                
                ///弹窗弹出，通知服务端记录
                NSDictionary *parms = @{
                    @"deviceUserRelId" : dModel.deviceUserRelId?:@"",
                    @"firmwareId" : oModel.firmwareId?:@""
                };
                [MRKDeviceURLRequest requestDeviceOTAAlert:parms success:^(id data) {
                    
                } fail:^(id data) {
                    
                }];
            }
        } failure:^(id data) {
            
        }];
    }
}

#pragma mark - 绑定/解绑相关
- (void)unBindService:(MRKDeviceModel *)dModel{
    self.unbindModel = dModel;
    NSDictionary *para = @{
        @"deviceUserRelId" : dModel.deviceUserRelId ? : @""
    };
    
    [MRKDeviceURLRequest requestDeviceDisConnect:para success:^(id data) {
        NSLog(@"服务器解绑成功");
        [[NSNotificationCenter defaultCenter] removeObserver:self name:kBlueDisConnectSuccessNotification object:nil];
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(disconnectPeripheral:) name:kBlueDisConnectSuccessNotification object:nil];
        
        ///服务器解绑成功后，是否连接了当前设备
        if ([BlueDataStorageManager isConnectDeviceWithProductID:dModel.productId name:dModel.name]) {
            NSLog(@"解绑服务器后，断开连接");
            if (dModel.productType.intValue == 3) {
                ///健康设备 断开连接 通知心率带记录不再重连
                NSDictionary *parms = @{
                    @"name" : dModel.bluetoothName,
                    @"macAddress" : dModel.mac,
                    BlueDeviceType : dModel.productId
                };
                [[NSNotificationCenter defaultCenter] postNotificationName:kBlueDisConnectNotification object:parms];
            }
            
            ///连接了设备 主动断开连接
            BluetoothModel *bModel = [BlueDataStorageManager connectBMFromProductID:dModel.productId];
            bModel.autoDisconnect = YES;
            [self cancelConnectDevice:bModel];
            return;
        }
        
        NSDictionary *parms = @{
            BlueDeviceType : dModel.productId ,
            @"status" : @(DeviceDisconnect)
        };
        [[NSNotificationCenter defaultCenter] postNotificationName:kBlueDisConnectSuccessNotification  object:parms];
        
    } fail:^(id data) {
        NSLog(@"服务器解绑失败");
        
        self.unbindModel = nil;
        if (self.unbindStatusBlock) {
            self.unbindStatusBlock(@0);
        }
    }];
}

- (void)unbindDeviceModel:(MRKDeviceModel *)dModel {
    ///1.判断是否连接了当前设备
    if([BlueDataStorageManager isConnectDeviceWithProductID:dModel.productId]) {
        ///1.1 已连接， 则不会存在重连 ，则直接 解绑服务器（取消设备连接）
        [self unBindService:dModel];
        return;
    }
    
    ///1.2 未连接，接2
    ///2.判断解绑的设备是否是正在主动重连的设备
    MRKAutoConnectModel *autoModel = [MRKAutoConnectManager autoModelFromProductID:dModel.productId];
    if(autoModel && [autoModel.connectModel.name isEqualToString:dModel.bluetoothName]) {
        ///2.1 是，则停止自动重连 ，然后解绑服务器
        ///主动解绑 记录 不再重连
        autoModel.connectModel.autoDisconnect = YES;
        [[MRKAutoConnectManager sharedInstance] cancelAutoConnect:dModel.productId];
    }
    
    ///2.2否，则直接解绑服务器
    [self unBindService:dModel];
}

- (void)disconnectPeripheral:(NSNotification *)notification {
    [[NSNotificationCenter defaultCenter] removeObserver:self name:notification.name object:nil];
    
    ///解绑成功
    BluetoothModel *model = [notification.object objectForKey:@"model"];
    NSString *type = [notification.object objectForKey:BlueDeviceType]  ? : model.type;
    NSLog(@"%@_____解绑成功==%@===%@", type , self.unbindModel , self);
    ///主动解绑，并且断开连接过来的大类和主动解绑的大类相同，默认时解绑设备成功
    if (self.unbindModel && [type isEqualToString:self.unbindModel.productId]) {
        
//        if(type.intValue == FatScaleEquipment){
//            //体脂秤 解绑成功 清除缓存
//            [HealthPersonModel removeScaleUserID];
//        }
        
        self.unbindModel = nil;
        NSLog(@"解绑成功，通知首页刷新获取显示大类，根据返回数据判断是否需要刷新首页其他接口");
        
        /// 解绑成功 ，通知刷新自动连接的设备
        [[NSNotificationCenter defaultCenter] postNotificationName:@"kUpdateHomeShowDeviceNotification" object:nil];
        
        if (self.unbindStatusBlock) {
            self.unbindStatusBlock(@1);
        }
        
    } else {
        NSLog(@"kBlueDisConnectSuccessNotification====并非主动解绑导致断开连接==%@" ,notification.object);
    }
}


- (void)alertHadConnected:(NSString *)productID {
    [MRKDeviceConnectAlertView alertHadConnectDeviceView:productID action:^(id data) {
        if([data intValue] == 1) {
            //断开已经连接的设备
            BluetoothModel *bModel = [BlueDataStorageManager connectBMFromProductID:productID];
            bModel.autoDisconnect = YES;
            
            __block RACDisposable *dispose = [[[NSNotificationCenter defaultCenter] rac_addObserverForName:kBlueDisConnectSuccessNotification object:nil] subscribeNext:^(id x) {
                //断开连接成功
                [AppDelegate errorView:@"已断开连接"];
                [dispose dispose];
            }];
            [self cancelConnectDevice:bModel];
        }
    }];
}

///判断是否可以继续连接当前设备
///已经连接同类型设备 返回不需要继续连接，否则 可以继续连接
///并且是当前设备，直接成功；如果不是当前设备则提示先断开连接
- (BOOL)isNeedConnectDevice:(NSString *)type name:(NSString *)name {
    ///判断是否已经连接同类型的设备
    if([BlueDataStorageManager isConnectDeviceWithProductID:type]) {
        BluetoothModel *model = [BlueDataStorageManager connectBMFromProductID:type];
        ///并且连接的设备不是当前设备
        if(![model.localName isEqualToString:name]) {
            NSLog(@"提示当前大类已经连接其他设备，请先断开再重连");
            [self alertHadConnected:type];
            [self connectDeviceFail:nil type:type];
            
        } else {
            ///已经连接的设备是当前设备
            [self connectSuccess:model];
        }
        return NO;
    }
    return YES;
}

- (void)setConnectModel:(ConnectModel *)connectModel {
    _connectModel = connectModel;
}

- (void)dealloc {
     NSLog(@"😊😊😊-----dealloc-----😊😊😊%@" , NSStringFromClass([self class]));
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}


+ (void)showConnectingLoading:(UIView *)targetView {
    loadingView *loadview = [[loadingView alloc] initWithFrame:targetView.frame text:@"连接中..."];
    loadview.tag = 0x101010;
    [targetView addSubview:loadview];
    [targetView bringSubviewToFront:loadview];
}

+ (void)dismssConnectingLoading:(UIView *)targetView {
    if ([targetView viewWithTag:0x101010]){
        [[targetView viewWithTag:0x101010] removeFromSuperview];
    }
}


@end
