//
//  NewBluePeripheralManager.m
//  Student_IOS
//
//  Created by MacPro on 2023/3/14.
//

#import "NewBluePeripheralManager.h"
#import "NewBluetoothManager.h"
#import "BlueDataStorageManager.h"
#import "MRKAutoConnectManager.h"

@interface NewBluePeripheralManager ()<NewBluetoothManagerDelegate >
@property (nonatomic, strong) NSMutableArray *peripheralArray;
@property (nonatomic, strong) NewBluetoothManager *bm;
@property (nonatomic, strong, nullable) BluetoothModel *connectingBModel;
@end

@implementation NewBluePeripheralManager

static NewBluePeripheralManager *_instance = nil;

+ (instancetype)sharedInstance {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        _instance = [[self alloc] init];
        [_instance initData];
    });
    return _instance;
}

- (void)initData {
    self.peripheralArray = [NSMutableArray array];
    self.bm = [NewBluetoothManager sharedInstance];
    self.bm.delegate = self;
}

- (void)startScan {
    NSLog(@"%@======startScan====" ,NSStringFromClass([self class]) );
    [[NewBluetoothManager sharedInstance] startScan];
    
    for(BluetoothModel *model in self.peripheralArray) {
        NSLog(@"startScan___had___searched==%@ " , model.localName);
        [self discoverBModel:model];
    }
}

- (void)stopScan {
    [[NewBluetoothManager sharedInstance] stopScan];
    NSLog(@"%@======stopScan ====delegate:%@" ,NSStringFromClass([self class]) , self.delegate);
}

- (void)connectModel:(BluetoothModel *)model {
    ///停止搜索
    [self stopScan];
    
    NSLog(@"%@===before===connectModel====%@" ,self,self.connectingBModel.localName);
    NSLog(@"connectingDictionary==%@" , [BlueDataStorageManager sharedInstance].connectingDictionary);
    if([BlueDataStorageManager sharedInstance].connectingDictionary.allKeys.count > 1) {
        ///多个设备在同时重连，一个一个处理
        NSLog(@"多个设备在同时重连，一个一个处理");
    }
    
    if(self.connectingBModel) {
        NSLog(@"当前有正在连接的设备，正在连接的设备是==%@,即将需要连接的设备是==%@" ,self.connectingBModel.localName ,model.localName);
    }
    
    self.connectingBModel = model;
    ///开始连接
    NSLog(@"%@===after===connectModel====%@", self,self.connectingBModel.localName);
    [[NewBluetoothManager sharedInstance] connectPeripheral:model.peripheral];
    
    //    NSLog(@"connectModel:%@===%@" ,[self.peripheralArray valueForKeyPath:@"type"] , [self.peripheralArray valueForKeyPath:@"localName"]);
}


- (void)disconnectModel:(BluetoothModel *)model {
    if (model.peripheral) {
        [[NewBluetoothManager sharedInstance] disconnectPeripheral:model.peripheral];
    }
}





#pragma mark - delegate -
- (void)didDiscoverPeripheral:(CBPeripheral *)peripheral advertisementData:(NSDictionary<NSString *,id> *)advertisementData RSSI:(NSNumber *)RSSI {
    ///这个字段 烧完后会修改掉 //peripheral.name 不会
    NSString *localName = [advertisementData objectForKey:@"kCBAdvDataLocalName"];
    if([localName isNotBlank]) {
        NSLog(@"%@====%@" , NSStringFromClass([self class]), advertisementData);
    }
    
    if([localName isNotBlank] && ![[self.peripheralArray valueForKeyPath:@"localName"] containsObject:localName]) {
        NSLog(@"discover====%@" ,  localName);
        BluetoothModel *model = [BluetoothModel new];
        model.localName = localName;
        model.peripheral = peripheral;
        model.advertisementData = advertisementData;
        
        [self.peripheralArray addObject:model];
        [self discoverBModel:model];
    }
}

//        [[RACObserve(peripheral, state) distinctUntilChanged] subscribeNext:^(NSNumber * x) {
//            NSLog(@"搜索到的设备状态监听====peripheral:%@,state:%@" , localName , x);
//            if(x.intValue == CBPeripheralStateDisconnected) {
//                NSLog(@"检测到设备状态是断开连接，如果本地已连接，更新其状态");
//                BluetoothModel *bb = [self bmFromPeripheralArray:peripheral];
//                [BlueDataStorageManager deleteBluetoothModel:bb];
//                NSLog(@"disconnect__peripheral:%@,state:%@" , bb.localName , @(bb.peripheral.state));
//            }
//        }];

///回调出去发现某个设备
- (void)discoverBModel:(BluetoothModel *)model {
    ///心率臂带进入ota以后自动连接
    if ([self.connectEquipmentPrefix isNotBlank]) {
        [self stopScan];
        [self connectModel:model];
    }
    
    if (self.discoverBlock) {
        self.discoverBlock(model);
        NSLog(@"discoverBlock==%@" , model.localName);
    }
    
    if (self.delegate && [self.delegate respondsToSelector:@selector(didDiscoverPeripheral:)]) {
        [self.delegate didDiscoverPeripheral:model];
    }
}

///连接成功
- (void)didConnectPeripheral:(CBPeripheral *)peripheral {
    [self stopScan];
    BluetoothModel *model = [self bmFromPeripheralArray:peripheral];
    NSString *log = [NSString stringWithFormat:@"&&didConnectPeripheral&&：%@",model.localName];
    MLog(log);
    NSLog(@"didConnectPeripheral===%@===%@" , peripheral.name ,  model.advertisementData);
    [peripheral discoverServices:nil];
}

///连接失败
- (void)didFailToConnectPeripheral:(CBPeripheral *)peripheral error:(NSError *)error {
    NSLog(@"didFailToConnectPeripheral==%@,error==%@" , peripheral , error.localizedDescription);
    [[NSNotificationCenter defaultCenter] postNotificationName:@"kConnectDeviceFailNotification" object:peripheral];
    
    if(self.connectFailBlock) {
        self.connectFailBlock(error);
    }
    
    if(self.delegate && [self.delegate respondsToSelector:@selector(connectFail)]) {
        [self.delegate connectFail];
    }
}

///发现服务
- (void)peripheral:(CBPeripheral *)peripheral didDiscoverServices:(NSError *)error {
    NSLog(@"peripheral service===%@" , peripheral.services);
    
    for (CBService *sv in peripheral.services) {
        [peripheral discoverCharacteristics:nil forService:sv];
    }
}

///发现特征
- (void)peripheral:(CBPeripheral *)peripheral didDiscoverCharacteristicsForService:(CBService *)service error:(NSError *)error {
    NSLog(@"didDiscoverCharacteristicsForService==%@==%@" , service , service.characteristics );
    BluetoothModel *model = [self bmFromPeripheralArray:peripheral] ;/// self.connectingBModel;
    
    NSMutableDictionary *svs = model.services ? model.services.mutableCopy : [NSMutableDictionary dictionary];
    NSMutableDictionary *info = model.infomation ? model.infomation.mutableCopy : [NSMutableDictionary dictionary];
    
    NSArray *mustSvs = [BluetoothModel uniqueModelServices:model];
    NSArray *mustChs = [BluetoothModel uniqueModelCharacteristics:model];
    
    for (CBCharacteristic *characteristic in service.characteristics) {
        [svs setObject:service.characteristics forKey:service.UUID.UUIDString.uppercaseString];
        ///180A 设备信息服务
        ///F8C0 智健设备相关信息服务
        if ([mustSvs containsObject:service.UUID.UUIDString.uppercaseString] ) {
            if ([mustChs containsObject:characteristic.UUID.UUIDString.uppercaseString]) {
                [info setObject:@"-1" forKey:characteristic.UUID.UUIDString.uppercaseString];
                [peripheral readValueForCharacteristic:characteristic];
            }
        }
    }
    
    model.infomation = info.copy;
    model.services = svs.copy;
}

///收到蓝牙数据
- (void)peripheral:(CBPeripheral *)peripheral didUpdateValueForCharacteristic:(CBCharacteristic *)characteristic error:(NSError *)error {
    NSLog(@"获取到蓝牙数据====%@" ,characteristic);
    if (!characteristic.value) {
        return;
    }
    
    BLog(@"%@:%@",characteristic.UUID.UUIDString, [characteristic.value hexString]);
    BluetoothModel *model = [self bmFromPeripheralArray:peripheral];/// self.connectingBModel ? : [self bmFromPeripheralArray:peripheral];
    NSArray *array = [BluetoothModel uniqueModelCharacteristics:model];
    
    ///设备信息
    if ([array containsObject:characteristic.UUID.UUIDString.uppercaseString]) {
        
//        NSString *str = [NSObject hexToAscString:characteristic.value];
        NSString *str = [characteristic.UUID.UUIDString isEqualToString:kDeviceMacAddressCharacteristics] ? [NSObject convertDataToHexStr:characteristic.value] : [NSObject hexToAscString:characteristic.value];
        
        NSMutableDictionary *info = model.infomation ? [model.infomation mutableCopy] : [NSMutableDictionary dictionary];
        [info setObject:str forKey:characteristic.UUID.UUIDString.uppercaseString];
        model.infomation = info.copy;
        
        NSMutableDictionary *infosDic = model.infomationsDic ? model.infomationsDic.mutableCopy : [NSMutableDictionary dictionary];
        NSDictionary *obj = @{
            @"service" : characteristic.service.UUID.UUIDString,
            @"characteristicProperties" : characteristic.UUID.UUIDString ,
            @"characteristicValue" : str
        };
        
        NSString *key = [NSString stringWithFormat:@"%@%@", characteristic.service.UUID.UUIDString.uppercaseString, characteristic.UUID.UUIDString.uppercaseString];
        [infosDic setObject:obj forKey:key];
        
        model.infomationsDic = infosDic.copy;
        
        BOOL res = YES;
        for (NSString *value in model.infomation.allValues) {
            if([value isEqualToString:@"-1"]) {
                res = NO;
                break;
            }
        }
        
        if (res) {
            NSLog(@"所有的设备信息都获取到了==%@==%@==%@===%@",model.infomation , model.infomationsDic, model.advertisementData, self.connectingBModel);
            ///可以在此处设置相应的服务
            [BluePeripheral setWriteCharacteristicFromType:model.type];
            
            self.connectingBModel = nil;
            if (self.connectSuccessBlock){
                self.connectSuccessBlock(model);
            }
            
//            if(self.delegate && [self.delegate respondsToSelector:@selector(connectSuccess:)]) {
//                [self.delegate connectSuccess:model];
//            }
        }
        return;
    }
    
    ///ota数据
    NSString *uuid = characteristic.service.UUID.UUIDString.uppercaseString;
    if ([uuid isEqualToString:kFRKOTAServiceUUID.uppercaseString] ||
        [uuid isEqualToString:kFRKOTAServiceUUID2.uppercaseString]) {
        [[NSNotificationCenter defaultCenter] postNotificationName:@"kFRKOTADataNotification" object:characteristic.value];
        return;
    }
    
    if ([uuid isEqualToString:kLINKEDOTAServiceUUID.uppercaseString]) {
        [[NSNotificationCenter defaultCenter] postNotificationName:@"kLinkedsemiOTADataNotification" object:characteristic];
        return;
    }
    
    
    ///设备数据
    NSDictionary *data = @{
        BlueCharacteristic : characteristic,
        BlueDevice : peripheral,
        BlueData : characteristic.value,
        BlueDeviceType : model.type ?:@"",
    };
    [[NSNotificationCenter defaultCenter] postNotificationName:BLEDataNotification object:data];
}

//([characteristic.UUID.UUIDString isEqualToString:kHeartBatteryCharacteristics] || [characteristic.UUID.UUIDString isEqualToString:kHeartDataCharacteristics]) ? [@(HeartEquipment) stringValue] : [BluetoothManager sharedInstance].equipmentType,

- (void)didDisconnectPeripheral:(CBPeripheral *)peripheral error:(NSError *)error {
    NSLog(@"didDisconnectPeripheral====%@" , error ? error.localizedDescription : @"success");
    MLog(@"设备断连didDisconnectPeripheralName ==== %@", peripheral.name);

    BluetoothModel *model = [[NewBluePeripheralManager sharedInstance] bmFromPeripheralArray:peripheral];
    model.infomations = [NSArray array];
    model.infomationsDic = [NSDictionary dictionary];
    model.equipmentModel = nil;
    model.equipmentInfo = nil;
    
    BOOL res = [BlueDataStorageManager isConnectDeviceWithProductID:model.type name:model.localName];///当前断开的设备之前是否连接
    if(res) {
        ///如果之前是连接的设备 则移除存储的相关信息 ，检查是否触发重连，
        ///如果之前就未连接成功，可能就是在重连超时的时候取消的连接触发，无需移除信息
        [self removeConnectDeviceInfo:model];
    } else {
        NSLog(@"unconnected==didDisconnectPeripheral===%@" , peripheral.name);
        BluetoothModel *bm = [self bmFromPeripheralArray:peripheral];
        NSDictionary *parms = @{
            BlueDeviceType : bm.type ,
            @"model" : bm ,
            @"status" : @(DeviceDisconnect)
        };
        ///断开连接通知
        [[NSNotificationCenter defaultCenter] postNotificationName:kBlueDisConnectSuccessNotification object:parms];
    }
}

- (void)centralManagerDidUpdateState:(CBManagerState)state {
    self.state = state;
    self.isOpen = (state == CBManagerStatePoweredOn);
    
    if(state == CBManagerStatePoweredOn) {
       
    } else {
        /// 如果 蓝牙关闭 移除已经连接的所有的设备
        for (BluetoothModel *model in [BlueDataStorageManager sharedInstance].connectDictionary.allValues) {
            [self removeConnectDeviceInfo:model];
        }
        
        ///停止所有的自动重连
        [[MRKAutoConnectManager sharedInstance] cancelAllAutoConnect];
    }
}








#pragma mark -- 共有方法
///断开连接以后 ，移除本地存储的相关信息
- (void)removeConnectDeviceInfo:(BluetoothModel *)model {
    if(!model) {
        NSLog(@"removeConnectDeviceInfo===%@" , model);
        return;
    }
    
    ///新的存储类
    [BlueDataStorageManager deleteBluetoothModel:model];
    
    ///断开连接通知
    NSDictionary *parms = @{
        BlueDeviceType : model.type,
        @"model" : model,
        @"status" : @(DeviceConnected)
    };
    [[NSNotificationCenter defaultCenter] postNotificationName:kBlueDisConnectSuccessNotification object:parms];
}

- (BluetoothModel *)bmFromPeripheralArray:(CBPeripheral *)per {
    for (BluetoothModel *bm in self.peripheralArray) {
        if ([bm.peripheral.identifier isEqual:per.identifier]) {
            return bm;
        }
    }
    return nil;
}

- (void)dealloc {
     NSLog(@"😊😊😊-----dealloc-----😊😊😊%@" , NSStringFromClass([self class]));
}

@end
