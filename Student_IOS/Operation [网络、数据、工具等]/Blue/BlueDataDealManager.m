//
//  BlueDataDealManager.m
//  Student_IOS
//
//  Created by MacPro on 2021/9/18.
//

#import "BlueDataDealManager.h"
#import "MRKReportManager.h"
#import "DeviceConnectStatusManager.h"
#import "BaseBlueCommandManager.h"
#import "FTMSProtocolCommandManager.h"

#import "MrkHeartRateManager.h"
#import "ZJProtocolCommandManager.h"
#import "MRKDeviceManager.h"


@interface BlueDataDealManager ()<MRKAutoConnectManagerDelegate>
@property (nonatomic, strong) dispatch_source_t uploadTimer;
@property (nonatomic, assign) BOOL isSuspend;                    ///定时器是否暂停
@property (nonatomic, assign) BOOL isFirst;                      ///是否第一次过来的数据
@property (nonatomic, copy) NSString *type;                      ///练的设备类型
@property (nonatomic, copy) NSString *equipmentName;             ///

@property (nonatomic, strong) NSNumber *skipElectic;             ///跳绳电量
///
@property (nonatomic, assign) BOOL isEnd;

@property (nonatomic, weak) id sourceClass;                      ///哪个类持有本管理类
@property (nonatomic, assign) BOOL isClearAll;                   ///是否已经清除监听

//筋膜枪 自己计时
@property (nonatomic, strong) dispatch_source_t timeTimer;
@property (nonatomic, assign) int jmqTime;
@property (nonatomic, assign) BOOL isTimeSuspend;                ///筋膜枪定时器是否暂停

@property (nonatomic, assign) NSInteger connectCount;            ///重连次数
///
@property (nonatomic, strong) NSNumber *rate;                    ///当前心率
@property (nonatomic, assign) double heartKcal;
@property (nonatomic, strong) HeartKcalConfig *heartKcalConfig;  ///心率消耗数据配置

///跑步机装态
@property (nonatomic, assign) TREAMILL_STATUS treamillConnectStatus;
///防止因设备重连重新setmodel导致的标识位重置掉
@property (nonatomic, assign) BOOL firstSetModel;
@end

@implementation BlueDataDealManager
@synthesize tyModel = _tyModel;

pthread_rwlock_t lock;

- (instancetype)initWithType:(NSString *)type source:(nonnull id)source {
    self = [super init];
    if (self) {
        self.uploadType = HttpUploadType;
        self.type = type;
        self.sourceClass = source;
        self.jmqTime = 0;
        
        self.equipmentName = [BluePeripheral connectDeviceNameOfType:type];
        [self loadModel];
    }
    return self;
}

- (instancetype)initWithType:(NSString *)type dataType:(NSInteger)dataType {
    self = [super init];
    if (self) {
        self.uploadType = HttpUploadType;
        self.type = type;
        self.randomType = dataType;
        self.jmqTime = 0;
        
        self.equipmentName = [BluePeripheral connectDeviceNameOfType:type];
        [self loadModel];
        
        //添加跳转训练报告后清空数据
        [[NSNotificationCenter defaultCenter] addObserver:self
                                                 selector:@selector(clearAll)
                                                     name:@"UpdateReportComplete"
                                                   object:nil];
    }
    return self;
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
    NSLog(@"😊😊😊-----dealloc-----😊😊😊%@" , NSStringFromClass([self class]));
}


- (void)loadModel {
    pthread_rwlock_init(&lock, NULL);//初始化 lock
    
    self.rate = @0;
    self.heartKcal = 0;
    self.isSuspend = YES;
    self.isRefresh = YES;
    self.isGOON = YES;
    self.isTimeSuspend = YES;
    self.tyModel = [BaseEquipDataModel new];
    
    ///筋膜枪，或者是力量站的P03设备，须要自己单独记时间
    if (self.type.intValue == JinMoQiangEquipment || [self isPowerP03]) {
        [self createTimeTimer];
        [[NSNotificationCenter defaultCenter] addObserver:self
                                                 selector:@selector(suspendTimeTimer)
                                                     name:JMQPauseNotification
                                                   object:nil];
    }
    
    ///跑步机状态
    if (self.type.intValue == TreadmillEquipment) {
        [[NSNotificationCenter defaultCenter] addObserver:self
                                                 selector:@selector(setTreamillStatus:)
                                                     name:@"TreamillStatusNotification"
                                                   object:nil];
    }
    
    ///添加蓝牙设备数据监听
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(getBlueDeviceData:)
                                                 name:BlueDeviceDataNotification
                                               object:nil];
    
//    ///添加心率数据监听
//    [[NSNotificationCenter defaultCenter] addObserver:self
//                                             selector:@selector(getHeartRateData:)
//                                                 name:HeartRateNotification
//                                               object:nil];
    
    
    if (self.type.intValue == SkipRopeEquipment && [self.sourceClass isKindOfClass:NSClassFromString(@"MRKOTAUpdateViewController")]) {
        //跳绳设备 && 固件更新类过来 =>不需要连接心率带，只为获取跳绳电量
        return;
    }
    
    ///重置重连次数
    self.connectCount = 0;
    
    ///进入运动模式 取消其他所有重连
    [[MRKAutoConnectManager sharedInstance] cancelAllAutoConnect];
    
    ///设置自动重连代理
    [MRKAutoConnectManager sharedInstance].delegate = self;
    
    ///设置自动连接模式
    [MRKAutoConnectManager sharedInstance].autoMode = SportAutoConnectMode;
    
    ///设备状态连接管理
    [self deviceConnectManager];
}


///设备蓝牙连接状态管理
- (void)deviceConnectManager {
    @weakify(self);
    RACSignal *signal = [[MRKConnectStatusManager sharedInstance] connectStatusWithProductID:self.type];
    [[[signal distinctUntilChanged] takeUntil:[self rac_willDeallocSignal]] subscribeNext:^(NSNumber * x) {
        @strongify(self);
        NSLog(@"%@___deviceConnectManager---- %@", self, x);
        self.connectStatus = [x intValue];
        
        if (x.intValue == DeviceConnected) {
            ///连接成功以后在此重置重连次数
            self.connectCount = 0;
            
            ///有些运动模式，可不连设备进入，所以连接成功以后记录该设备名称，方便断开连接以后开启重连
            self.equipmentName = [BluePeripheral connectDeviceNameOfType:self.type];
            
            //连接设备，开启上传
            [self startUploadDataTimer];
            
            //接收到数据 启动计时器
            if (self.type.intValue == JinMoQiangEquipment || [self isPowerP03]) {
                [self resumeTimeTimer];
            }
            
            ///小彩屏重连以后发送继续指令
            if ([[MRKDeviceManager shareManager] isJudgeBlufiDevice:self.eqModel.productType]) {
                [ZJProtocolCommandManager goonCarSport:self.type];
            }
            
        } else {
            ///暂停上传
            [self suspendUploadDataTimer];
            
            ///暂停时间计时
            [self suspendTimeTimer];
        }
        
        ///如果是开启了 自动重连 && 第一次断开连接，则不通知更新状态，直接开启重连
        if (self.isOpenAutoConnect && x.intValue == DeviceDisconnect && self.connectCount == 0) {
            [MRKAutoConnectManager sharedInstance].autoMode = SportAutoConnectMode;
            return;
        }
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(updateDeviceStatus:)]) {
            [self.delegate updateDeviceStatus:x.intValue];
        }
    }];
}

- (void)setTreamillStatus:(NSNotification *)sender {
     ///DeviceRuningStatus 运动中
    self.treamillConnectStatus = [sender.object intValue];
}



#pragma mark - 开始获取设备数据
- (void)startGetData {
    NSLog(@"开始获取数据了");
    if (![BluetoothManager isConnectEquipmentType:self.type]) {
        return;
    }
    
    ///连接设备，开启上传
    if (self.isSuspend && self.connectStatus == DeviceConnected) {
        [self startUploadDataTimer];
    }
    
    ///心率设备刷新数据标识微
    [[MrkHeartRateManager shareManager] reloadHeartIdentifier];
    
    ///开始获取数据
    [BaseBlueCommandManager startSportWithType:self.type];

//    //运动秀 发送开始指令
//    //就绪指令方法sportReady，可根据业务需求判断调用哪个方法
//    if(self.eqModel.communicationProtocol.intValue == SportShowCommunicationProtocol) {
//        [BaseBlueCommandManager sportResume:self.type];
//    }
    //心跳

    [BlueBeatManager shareManager].type = self.type;
    [[BlueBeatManager shareManager] startPing];
}


- (void)heartEquipmentDataUpdate:(BaseEquipDataModel *)model {
    ///23-01-04 心率设备非0心率更新UI，设备心率非nil更新UI
    if (model.rate.intValue == 0) return;
    
    self.rate = model.rate;
    self.tyModel.rate = model.rate;
    ///  self.tyModel.isDeviceRate = @0;
    
    ///判断心率消耗,
    if (self.enableHeartbeatKcal && self.connectStatus == DeviceConnected){
        if (self.type.intValue == TreadmillEquipment){
            if (self.treamillConnectStatus == DeviceRuningStatus){
                self.heartKcal += [BlueTool calculateIncreaseEneryFromHeart:model.rate withModel:self.heartKcalConfig];
            }
        } else {
            self.heartKcal += [BlueTool calculateIncreaseEneryFromHeart:model.rate withModel:self.heartKcalConfig];
        }
        self.tyModel.rateKcal = @(self.heartKcal);
    }
    
    [self delegateUpdateRate];
}


///获取蓝牙数据
- (void)getHeartRateData:(NSNotification *)sender {
    NSLog(@"getHeartRateData --- %@", sender.object);
    NSNumber *heartRate = sender.object;
    
    self.rate = [MrkHeartRateManager shareManager].heartRate;
    
    
//    HeartDataIdentifier identifier = [MrkHeartRateManager shareManager].heartIdentifier;
//    switch (identifier) {
//        case HeartDataIdentifier_appleWatch:
//            self.rate = [MrkHeartRateManager shareManager].heartRate;
//            break;
//        case HeartDataIdentifier_merit:{
//            self.rate = [MrkHeartRateManager shareManager].heartRate;
//            
//            ///判断心率消耗,
//            if (self.enableHeartbeatKcal && self.connectStatus == DeviceConnected){
//                if (self.type.intValue == TreadmillEquipment){
//                    if (self.treamillConnectStatus == TreamillRuningStatus){
//                        self.heartKcal += [BlueTool calculateIncreaseEneryFromHeart:self.tyModel.rate withModel:self.heartKcalConfig];
//                    }
//                } else {
//                    self.heartKcal += [BlueTool calculateIncreaseEneryFromHeart:self.tyModel.rate withModel:self.heartKcalConfig];
//                }
//            }
//        } break;
//        default:
//            break;
//    }
}


///获取蓝牙数据
- (void)getBlueDeviceData:(NSNotification *)sender {
  
    
    BaseEquipDataModel *m = sender.object;
//    if (m.type.intValue == HeartEquipment) {
//        [self heartEquipmentDataUpdate:m];
//        return;
//    }
    NSLog(@"BaseEquipDataModel --- %@" , m.description);
    
    /// 课程 && 不支持连接设备的课程 直接更新
    if (self.courseModel && !self.courseModel.isSupportConnection) {
        [self setLatestModel:m];
        return;
    }
    
    if (!self.eqModel) {
        MLog(@"运动模式还没拿到eqmodel");
        return;
    }
    
    if (self.hasFinishTarget && !self.isGOON) {
        return;
    }
    
    ///如果跳绳设备 并且 是智健协议
    if (self.type.intValue == SkipRopeEquipment && self.eqModel.communicationProtocol.intValue == ZJCommunicationProtocol) {
        [self jump:sender];
        return;
    }
    
    if (self.type.intValue == JinMoQiangEquipment || [self isPowerP03]) {
        //接收到数据 启动计时器
        [self resumeTimeTimer];
    }

    if (self.type.intValue == BicycleEquipment && m.spm.intValue > kMaxCarSmp) {
        m.spm = @(kMaxCarSmp);
    }
    if (self.type.intValue == EllipticalEquipment && m.spm.intValue > kMaxEllipticalSmp) {
        m.spm = @(kMaxEllipticalSmp);
    }
    if (self.type.intValue == StairClimbEquipment && m.spm.intValue > kMaxStairClimbSmp) {
        m.spm = @(kMaxStairClimbSmp);
    }
    if (self.type.intValue == BoatEquipment && m.spm.intValue > kMaxBoatSmp) {
        m.spm = @(kMaxBoatSmp);
    }
    
    if (m.deviceRate.intValue > kMaxHeartRate) {
        m.deviceRate = @(kMaxHeartRate);
    }
    
    //防止 筋膜枪调节档位 电量跳动
    if (self.tyModel.electric.intValue > 0 && m.electric.intValue > self.tyModel.electric.intValue) {
        m.electric = self.tyModel.electric;
    }
    
    NSLog(@"self.tyModel.energy ==== %@",self.tyModel.energy);
    NSLog(@"m.energy ==== %@",m.energy);
    
    m = [self.tyModel updateNotNilValueFrom:m];
    
    NSLog(@"after self.tyModel.energy ==== %@",self.tyModel.energy);
    if (self.type.intValue == JinMoQiangEquipment || [self isPowerP03]) {
        m.totalTimeSecond = @(self.jmqTime);
    }
    
    NSLog(@"getBlueDeviceData --- %@" , m.description);
    
    
    [self setLatestModel:m];
}


#pragma mark - 跳绳数据处理
- (void)jump:(NSNotification *)sender {
    BaseEquipDataModel *newModel = sender.object;
    if (newModel.electric) {
        NSLog(@"此条跳绳数据只有电量数据哦。。。。。。。。");
        self.skipElectic = newModel.electric;
        return;
    }
    NSLog(@"engry---%@ , time == %@" , self.tyModel.energy , [MRKToolKit MSTimeStrFromSecond:self.tyModel.totalTimeSecond.intValue]);
    float costFloat;
    int x = arc4random() % 3;
    if (x == 0) {
        costFloat = 0.05;
    }else if(x == 1){
        costFloat = 0.06;
    }else{
        costFloat = 0.07;
    }
    
    newModel.energy = @(self.tyModel.energy.floatValue + costFloat * ((newModel.count.intValue - self.tyModel.count.intValue) > 0 ? (newModel.count.intValue - self.tyModel.count.intValue) : 0));
    newModel.electric = self.skipElectic;
    [self setLatestModel:newModel];
}

- (void)setLatestModel:(BaseEquipDataModel *)m {
    m.equipmentName = self.equipmentName;

    ///
    [MrkHeartRateManager shareManager].deviceHeartRate = m.deviceRate;
    
    
//    HeartDataIdentifier identifier = [MrkHeartRateManager shareManager].heartIdentifier;
//    switch (identifier) {
//        case HeartDataIdentifier_appleWatch: 
//        case HeartDataIdentifier_merit:
//            m.rate = self.rate;
//            break;
//        default:
//            m.rate = m.deviceRate;
//            break;
//    }
//    m.rateKcal = @(self.heartKcal);
    
//    if ([BlueDataStorageManager isConnectDeviceWithProductID:[@(HeartEquipment) stringValue]]) {
//        //连接心率带 不采用设备心率
////        m.isDeviceRate = @0;
//        m.rate = self.rate;
//        NSLog(@"当前使用的是心率带的心率%@" , m.rate);
//    } else {
//        //没有连接心率带 设备有心率 使用设备心率 21-12-13
//        self.rate = 0;
////        if (m.deviceRate) {
////            m.isDeviceRate = @(1);
////            BlueNSLog(@"当前使用的是设备的心率%@" , m.deviceRate);
////        }
//    }
    
    self.tyModel = m;
    self.reportManager.deviceModel = m;
    [self update];
}

- (void)update {
//    ///23-01-04 心率设备非0心率更新UI，设备心率非nil更新UI
//    ///23-01-06 如果心率设备有心率 会直接更新，此处是设备数据更新的时候判断是否需要更新心率值，所以只需要判断设备是否有心率值，有则更新
//    if (self.tyModel.deviceRate ) {
//        [self delegateUpdateRate];
//    }
    
//    ///判断心率数据只有运动设备上有心率
//    if (self.rate <= 0  &&  self.tyModel.deviceRate > 0) {
//        [self delegateUpdateRate];
//    }
    
    [self delegateUpdate];
}

- (void)delegateUpdate {
    ///需要连接设备的课程直接刷新
    BlueNSLog(@"🐼🐼🐼🐼🐼🐼🐼🐼🐼🐼🐼🐼🐼🐼🐼🐼🐼🐼🐼🐼🐼🐼🐼🐼🐼🐼🐼___%@" , self.tyModel);
    NSLog(@"delegateUpdated===============");
    
    ///小件课，不需要连接设备的
    if (self.courseModel != nil && !self.courseModel.isSupportConnection) {
        if (_delegate && [_delegate respondsToSelector:@selector(updateUI)]) {
            [_delegate updateUI];
        }
        return;
    }
    
    ///针对超燃脂训练单独刷新UI
    if (self.isUItraTraining){
        if (_delegate && [_delegate respondsToSelector:@selector(updateUI)]) {
            [_delegate updateUI];
        }
        return;
    }
    
//    ///其他需要连接设备
//    if ((![BluetoothManager isConnectEquipmentType:self.type]) && 
//        (![BluetoothManager isConnectEquipmentType:[@(HeartEquipment) stringValue]]))  {
//        return;
//    }
    
    if (self.tyModel == nil) {
        return;
    }
    
    if (!self.isRefresh) {
        return;
    }
    
    if (_delegate && [_delegate respondsToSelector:@selector(updateUI)]) {
        [_delegate updateUI];
    }
}

- (void)delegateUpdateRate {
//    if ((![BluetoothManager isConnectEquipmentType:self.type]) &&
//        (![BluetoothManager isConnectEquipmentType:[@(HeartEquipment) stringValue]]))  {
//        return;
//    }
    
    if (self.isEnd) {
        return;
    }
    
    ///刷新心率
    if (_delegate && [_delegate respondsToSelector:@selector(updateHeartRate)]) {
        [_delegate updateHeartRate];
    }
}


#pragma mark - 清空数据判断
- (void)judegClearData {
    //是否是小彩屏
    if ([[MRKDeviceManager shareManager] isJudgeBlufiDevice: self.eqModel.productType]) {
        MLog(@"小彩屏已发就绪指令，不用清零");
        self.isRefresh = YES;
        MLog(@"给小彩屏发送就绪");
        [NSThread sleepForTimeInterval:0.05];
        [ZJProtocolCommandManager startCarSport:self.type];
        return;
    }
    dispatch_async(dispatch_get_main_queue(), ^{
        if (self.eqModel.isClean) {
            if (self.type.intValue == SkipRopeEquipment && self.reportType == MrkReportTypeFreedomTraining && self.tyModel.mode.intValue != self.randomType) {
                //因为此时会弹出是否切换模式的弹窗，点击「暂不」，直接生成训练报告，「切换」会先保存数据，然后切换模式，所以不用弹出下边的弹窗10-30
                MLog(@"当前模式不匹配,set==%@,device=%@" , @(self.randomType) , self.tyModel.mode);
                return;
            }
            //支持清零
            self.isRefresh = NO;
            //定时练/目标练中，用于判断空练数据直接满足目标条件是否需要弹出完成弹窗的判断
            self.hasNeedClear = NO;
            MLog(@"支持清零，准备上报");
            @weakify(self);
            [self.reportManager uploadDataDetialSuccess:^(id data) {
                @strongify(self);
                MLog(@"上报完成，准备清零");
                [self sendClearData];
            }];
        } else {
            self.hasNeedClear = YES;
            self.isRefresh = YES;
        }
    });
}


- (void)sendClearData{
    BOOL res = self.eqModel.communicationProtocol.intValue == BQCommunicationProtocol;
    @weakify(self);
    __block RACDisposable *Disposable;
    Disposable = [[[NSNotificationCenter defaultCenter] rac_addObserverForName:res ? IconsoleResetDataSuccessNotification : ClearDatSendSuccessNotification object:nil] 
                  subscribeNext:^(id x) {
        @strongify(self);
        [Disposable dispose];
        MLog(@"设备数据清零完成");
        
        if(self.isEnd) return;
        
        ///等待清零完成后再更新UI 2024/08/02 junq
        [self resetData];
        
        if (self.type) {
            [BaseBlueCommandManager startSportWithType:self.type];
//            //运动秀 发送开始指令
//            //就绪指令方法sportReady，可根据业务需求判断调用哪个方法
//            if(self.eqModel.communicationProtocol.intValue == SportShowCommunicationProtocol) {
//                [BaseBlueCommandManager sportResume:self.type];
//            }
        }
        
        if (self.clearDataSuccess) {
            self.clearDataSuccess(@1);
        }
    }];
    
   
    MLog(@"开始清零sendClearDataCmd");
    [BluetoothCommondManager sendClearDataCmd:self.type];
    
}

- (void)clearData {
    if (!self.isFirst && self.eqModel && self.tyModel.totalTimeSecond) {
        self.isFirst = YES;
        if (self.eqModel.isClean) {
            [self sendClearData];
        }
        MLog(@"socket上报数据，首次清零");
    }
}

- (void)resetData {
    self.tyModel = nil;
    self.tyModel = [[BaseEquipDataModel alloc] init];
    
    self.hasNeedClear = YES;
    self.isRefresh = YES;
    
    self.uploadReport = NO;
}

///生成 并跳转 训练报告
- (void)addRecordToReport {
    self.isRefresh = NO;//暂停更新UI
    [self suspendUploadDataTimer];//暂停上传数据
    
    ///
    self.reportManager.takeTime = self.tyModel.totalTimeSecond.stringValue;
    self.reportManager.distance = self.tyModel.totalDistance.stringValue;
    self.reportManager.kcal = self.tyModel.energy.stringValue;

    if (self.reportManager.firstInto){
        self.reportManager.isJump = NO;
        @weakify(self);
        [self.reportManager addReport:^(id data) {
            @strongify(self);
            [self.reportManager skipReport];
        } fail:^(id data) {
            @strongify(self);
            [self.reportManager skipReport];
        }];
    } else {
        self.reportManager.isJump = YES;
        @weakify(self);
        [self.reportManager addReport:^(id data) {
            @strongify(self);
            [self.reportManager skipReport];
        } fail:^(id data) {
            
        }];
    }
    MLog(@"运动结束生成训练报告");
}

#pragma mark - end
- (void)endGetData {
    //防止发送两次结束 21-12-22
    if (self.isEnd) {
        return;
    }
    self.isEnd = YES;
    
    //取消上报数据
    [self suspendUploadDataTimer];
    
    //关闭筋膜枪计时器
    [self closeTimeTimer];
    
    //结束运动
    [BaseBlueCommandManager endSportWithType:self.type];
    
    //停止心跳包
    [[BlueBeatManager shareManager] stopPing];
    
    ///从运动模式出去，取消所有的自动连接
    [[MRKAutoConnectManager sharedInstance] cancelAllAutoConnect];
    
    ///设置自动连接模式为非运动模式
    [MRKAutoConnectManager sharedInstance].autoMode = NormalAutoConnectMode;
    
    MLog(@"准备结束了结束了");
}

- (void)clearAll {
    if (self.isClearAll) {
        return;
    }
    self.isClearAll = YES;
    
    [self resetData];
    
    [self suspendUploadDataTimer];
    
    [self endGetData];
    
    MLog(@"我要清除数据监听了～～～～～～");
    
    [BlueBeatManager attempDealloc];//释放心跳包管理类
    
    //释放处理器
    [BaseBlueCommandManager attempDeallocDevice:self.type];
    
    [[NSNotificationCenter defaultCenter] removeObserver:self];
    
    [self closeUploadDataTimer];
    
    [self closeTimeTimer];
    
    ///从运动模式出去，取消所有的自动连接
    [[MRKAutoConnectManager sharedInstance] cancelAllAutoConnect];
    
    ///设置自动连接模式为非运动模式
    [MRKAutoConnectManager sharedInstance].autoMode = NormalAutoConnectMode;
    
//    ///开启非运动模式自动连接
//    [[MRKAutoConnectManager sharedInstance] manualStartAutoConnect];
}

#pragma mark - init

- (MRKReportManager *)reportManager {
    if (!_reportManager) {
        _reportManager = [[MRKReportManager alloc] init];
    }
    return _reportManager;
}

/*
 - (DeviceConnectStatusManager *)heartStatus {
 if (!_heartStatus) {
 _heartStatus = [[DeviceConnectStatusManager alloc] initWithType:[@(HeartEquipment) stringValue]];
 }
 return _heartStatus;
 }
 */
/*
 - (DeviceConnectStatusManager *)deviceStatusManager {
 if (!_deviceStatusManager) {
 _deviceStatusManager = [[DeviceConnectStatusManager alloc] initWithType:self.type];
 }
 return _deviceStatusManager;
 }
 */

//上传数据 启动计时器
- (void)startUploadDataTimer {
    if(self.uploadType == SocketUploadType) {
        MLog(@"socket上传数据，不用启动定时器");
        return;
    }
    
    if (_uploadTimer == nil) {
        [self initTimer];
    }
    
    if (self.isSuspend) {
        dispatch_resume(_uploadTimer);
        self.isSuspend = NO;
    }
}

//暂停
- (void)suspendUploadDataTimer {
    if (_uploadTimer && !self.isSuspend) {
        dispatch_suspend(_uploadTimer);
        self.isSuspend = YES;
    }
}

//关闭时间timer
- (void)closeUploadDataTimer {
    if (_uploadTimer) {
        if (self.isSuspend) {
            dispatch_resume(_uploadTimer);
        }
        dispatch_source_cancel(_uploadTimer);
        _uploadTimer = nil;
    }
}

#pragma mark - 上报数据
- (void)initTimer {
    //创建全局队列
    dispatch_queue_t queue = dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0);
    //使用全局队列创建计时器
    _uploadTimer = dispatch_source_create(DISPATCH_SOURCE_TYPE_TIMER, 0, 0, queue);
    //定时器延迟时间
    NSTimeInterval delayTime = 0.0f;
    //定时器间隔时间
    NSTimeInterval timeInterval = 1.0f;
    //设置开始时间
    dispatch_time_t startDelayTime = dispatch_time(DISPATCH_TIME_NOW, (int64_t)(delayTime * NSEC_PER_SEC));
    //设置计时器
    dispatch_source_set_timer(_uploadTimer, startDelayTime, timeInterval*NSEC_PER_SEC, 0.1*NSEC_PER_SEC);
    //执行事件
    dispatch_source_set_event_handler(_uploadTimer, ^{
        if(self.uploadType == SocketUploadType) {
            MLog(@"socket上传数据，不用启动定时器");
            return;
        }
        
        if (self.isEnd) {//已经结束 不再上报
            return;
        }
        
        //        self.reportManager.deviceModel = self.tyModel.copy;
        
        if (!self.isFirst && self.eqModel && self.tyModel.totalTimeSecond) {
            self.isFirst = YES;
            [self judegClearData];
            return;
        }
        
        //筋膜枪需要自己开始
        if (!self.isFirst && self.type.intValue == JinMoQiangEquipment) {
            self.isFirst = YES;
        }
        
        //UI不更新 则不上传 2.7.0
        if (!self.isRefresh) {
            return;
        }
        
        //上报数据
        [self.reportManager uploadDataDetialSuccess:^(id data) {}];
    });
}




#pragma mark - 筋膜枪/P03力量站 计时
- (void)createTimeTimer{
    dispatch_queue_t queue = dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0);
    _timeTimer = dispatch_source_create(DISPATCH_SOURCE_TYPE_TIMER, 0, 0, queue);
    NSTimeInterval delayTime = 0.0f;
    NSTimeInterval timeInterval = 1.0f;
    dispatch_time_t startDelayTime = dispatch_time(DISPATCH_TIME_NOW, (int64_t)(delayTime * NSEC_PER_SEC));
    dispatch_source_set_timer(_timeTimer,startDelayTime,timeInterval*NSEC_PER_SEC,0.1*NSEC_PER_SEC);
    dispatch_source_set_event_handler(_timeTimer,^{
        dispatch_async(dispatch_get_main_queue(), ^{
            self.jmqTime ++;
             ///P03力量站不走下面
            if (self.type.intValue == JinMoQiangEquipment){
                self.tyModel.totalTimeSecond = @(self.jmqTime);
                [self delegateUpdate];
            }
        });
    });
}

//暂停时间timer
- (void)suspendTimeTimer{
    if (_timeTimer && !_isTimeSuspend) {
        dispatch_suspend(_timeTimer);
        _isTimeSuspend = YES;
    }
}

//恢复时间timer
- (void)resumeTimeTimer{
    if (_isTimeSuspend) {
        dispatch_resume(_timeTimer);
        _isTimeSuspend = NO;
    }
}

//关闭时间timer
- (void)closeTimeTimer {
    if (_timeTimer) {
        if (_isTimeSuspend) {
            dispatch_resume(_timeTimer);
        }
        dispatch_source_cancel(_timeTimer);
        _timeTimer = nil;
    }
}


- (BOOL)isPowerP03{
    return self.type.intValue == PowerEquipment && [self.equipmentName localizedCaseInsensitiveContainsString:@"-P03"];
}

#pragma mark - 数据加锁

- (BaseEquipDataModel *)tyModel {
    BaseEquipDataModel *model ;
    pthread_rwlock_rdlock(&lock);
    model = _tyModel;
    //    NSLog(@"get___________%s", __func__);
    pthread_rwlock_unlock(&lock);
    return model;
}

-(void)setTyModel:(BaseEquipDataModel *)tyModel {
    pthread_rwlock_wrlock(&lock);
    _tyModel = tyModel;
    //    NSLog(@"set___________%s", __func__);
    pthread_rwlock_unlock(&lock);
}

- (void)setReportType:(MrkReportType)reportType {
    _reportType = reportType;
    self.reportManager.reportType = reportType;
}

- (void)setCourseModel:(MRKCourseModel *)courseModel {
    _courseModel = courseModel;
    self.reportManager.courseModel = courseModel;
    
    //如果不需要连接设备 直接开始
    if (self.isSuspend && !courseModel.isSupportConnection) {
        [self startUploadDataTimer];
    }
}

- (void)setEqModel:(EquipmentDetialModel *)eqModel {
    _eqModel = eqModel;
    if (eqModel == nil) return;
    
    self.reportManager.modelId = eqModel.idd;
    if (eqModel.isClean && !self.firstSetModel) {
        self.firstSetModel = YES;
        //支持清零设备，先屏蔽数据回调，等清零后设置isRefresh = YES
        self.isRefresh = NO;
        //定时练/目标练中，用于判断空练数据直接满足目标条件是否需要弹出完成弹窗的判断
        self.hasNeedClear = NO;
    }
}

- (void)setType:(NSString *)type {
    _type = type;
    self.reportManager.equipmentId = type;
}

//- (DEVICE_CONNECT_STATUS)DEVICE_CONNECT_STATUS {
//    return self.deviceStatusManager.status;
//}

- (void)setUploadType:(DeviceDataUploadType)uploadType {
    _uploadType = uploadType;
}


#pragma mark - MRKAutoConnectManagerDelegate
- (void)disconnectBModel:(BluetoothModel *)bModel {
    NSLog(@"%@____disconnectBModel==%@" , self,bModel.debugDescription);
    if(!self.isOpenAutoConnect){
        MLog(@"该场景未开启自动重连功能");
        return;
    }
    
    if([self.type isEqualToString:bModel.type]
       && [self.equipmentName isEqualToString:bModel.localName]) {
        
        if(self.connectCount == 0) {
            MLog(@"当前设备第一次中断了,自动连接");
            ///是当前设备 自动连接
            self.connectCount = 1;
            ///回调状态 自动重连中
            if (self.delegate && [self.delegate respondsToSelector:@selector(updateDeviceStatus:)]) {
                [self.delegate updateDeviceStatus:DeviceAutoConnecting];
            }
            
            ///设置重连模式是运动模式
            ///开启自动重连 特定场景需要加 连接中loading
            if (self.autoConnectNeedLoading) {
                UIViewController *currentVC = [UIViewController currentViewController];
                //                UIView *window = [UIApplication mrk_window];
                [NewConnectManager showConnectingLoading:currentVC.view];
            }
            [MRKAutoConnectManager sharedInstance].autoMode = SportAutoConnectMode;
            [[MRKAutoConnectManager sharedInstance] startAutoConnetDeviceModel:bModel.equipmentModel];
        } else {
            MLog(@"当前设备不是第一次中断了，弹窗提示吧");
        }
        
    } else {
        MLog(@"断开的不是当前设备，不做处理");
    }
}

- (void)autoConnectFailure {
    UIViewController *currentVC = [UIViewController currentViewController];
    //    UIView *window = [UIApplication mrk_window];
    [NewConnectManager dismssConnectingLoading:currentVC.view];
    if(self.connectCount == 1) {
        MLog(@"自动重连失败了=====弹窗提示吧");
        ///重连状态回调显示
        if (self.delegate && [self.delegate respondsToSelector:@selector(updateDeviceStatus:)]) {
            [self.delegate updateDeviceStatus:DeviceDisconnect];
        }
    }
}

- (void)autoConnectSuccess {
    UIViewController *currentVC = [UIViewController currentViewController];
    //    UIView *window = [UIApplication mrk_window];
    [NewConnectManager dismssConnectingLoading:currentVC.view];
    MLog(@"自动重连成功了=====");
}


- (void)setEnableHeartbeatKcal:(BOOL)enableHeartbeatKcal {
    _enableHeartbeatKcal = enableHeartbeatKcal;
    ///请求心率消耗计算配置
    if (enableHeartbeatKcal) {
        [MRKBaseRequest mrkGetRequestUrl:@"/app/activity/kcal_config"
                                 andParm:nil
                completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
            id data = [request.responseObject valueForKeyPath:@"data"];
            self.heartKcalConfig = [HeartKcalConfig modelWithJSON:data];
        } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
            
        }];
    }
}

@end

