//
//  UIApplication+Add.m
//  Student_IOS
//
//  Created by merit on 2021/7/23.
//

#import "UIApplication+Add.h"
#import <pthread.h>
#import <objc/message.h>
#import <sys/sysctl.h>
#import <mach/mach.h>
#import <objc/runtime.h>

#ifndef SYNTH_DYNAMIC_PROPERTY_OBJECT

#define SYNTH_DYNAMIC_PROPERTY_OBJECT(_getter_, _setter_, _association_, _type_) \
- (void)_setter_ : (_type_)object { \
[self willChangeValueForKey:@#_getter_]; \
objc_setAssociatedObject(self, _cmd, object, OBJC_ASSOCIATION_ ## _association_); \
[self didChangeValueForKey:@#_getter_]; \
} \
- (_type_)_getter_ { \
return objc_getAssociatedObject(self, @selector(_setter_:)); \
}

#endif

#define kNetworkIndicatorDelay (1/30.0)

@interface _UIApplicationNetworkIndicatorInfo : NSObject
@property (nonatomic, assign) NSInteger count;
@end

@implementation _UIApplicationNetworkIndicatorInfo
@end


@implementation UIApplication (Add)

+ (BOOL)mrk_isSceneApp{
    return [MRKScene defaultPackage].isSceneApp;
}

- (UIWindow *)mrkKeyWindow{
    return [MRKScene defaultPackage].keyWindow;
}

+ (CGRect)mrk_statusBarFrame{
    return [MRKScene defaultPackage].statusBarFrame;
}

+ (id)mrk_currentScene{
    return [MRKScene defaultPackage].currentScene;
}

+ (id)mrk_currentSceneDelegate{
    if ([self mrk_currentScene]) {
        return ((id (*)(id, SEL))objc_msgSend)([self mrk_currentScene],sel_registerName("delegate"));
    }
    return nil;
}

+ (UIWindow *)mrk_currentWindow{
    id wi = nil;
    for (UIWindow *window in [MRKScene defaultPackage].windows) {
        if (window.hidden == NO) {
            wi = window;
            break;
        }
    }
    return wi;
}

+ (UIWindow *)mrk_currentKeyWindow{
    return [MRKScene defaultPackage].keyWindow;
}

+ (UIWindow *)mrk_window{
    return [MRKScene defaultPackage].window;
}

+ (UIWindow *)mrk_keyWindow{
    return [MRKScene defaultPackage].keyWindow;
}

+ (id)delegate{
    return [UIApplication sharedApplication].delegate;
}

+ (__kindof UIViewController *)rootViewController{
    return ([self mrk_keyWindow]?:[self mrk_window]).rootViewController;
}


+ (__kindof UIViewController *)currentTopViewController{
    UIViewController *vc = [self rootViewController];
    Class naVi = [UINavigationController class];
    Class tabbarClass = [UITabBarController class];
    BOOL isNavClass = [vc isKindOfClass:naVi];
    BOOL isTabbarClass = NO;
    if (!isNavClass) {
        isTabbarClass = [vc isKindOfClass:tabbarClass];
    }
    while (isNavClass || isTabbarClass) {
        UIViewController * top;
        if (isNavClass) {
          top = [(UINavigationController *)vc topViewController];
        }else{
          top = [(UITabBarController *)vc selectedViewController];
        }
        if (top) {
            vc = top;
        }else{
            break;
        }
        isNavClass = [vc isKindOfClass:naVi];
        if (!isNavClass) {
            isTabbarClass = [vc isKindOfClass:tabbarClass];
        }
    }
    return vc;
}

+ (UINavigationController *)currentToNavgationController{
    return [self currentTopViewController].view.navigationController;
}

+ (void)displayAppPrivacySettings{
    // 跳进app设置
    if (UIApplicationOpenSettingsURLString != NULL) {
        UIApplication *application = [UIApplication sharedApplication];
        NSURL *URL = [NSURL URLWithString:UIApplicationOpenSettingsURLString];
        if ([application respondsToSelector:@selector(openURL:options:completionHandler:)]) {
            if (@available(iOS 10.0, *)) {
                [application openURL:URL options:@{} completionHandler:nil];
            } else {
                // Fallback on earlier versions
            }
        } else {
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"
            [application openURL:URL];
#pragma clang diagnostic pop
        }
    }
}

- (void)openMrkUrl:(NSString *)url{
    if (url.isEmpty) return;
    
    UIApplication *application = [UIApplication sharedApplication];
    NSURL *URL = [NSURL URLWithString:url];
    if ([application respondsToSelector:@selector(openURL:options:completionHandler:)]) {
        if (@available(iOS 10.0, *)) {
            [application openURL:URL options:@{} completionHandler:nil];
        } else {
            // Fallback on earlier versions
        }
    } else {
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"
        [application openURL:URL];
#pragma clang diagnostic pop
    }
    
}

@end



@implementation UIView (Navigation_Chain)

- (UINavigationController *_Nonnull)navigationController {
    UIResponder *next = self.nextResponder;
    do {
        if ([next isKindOfClass:[UINavigationController class]]) {
            return (UINavigationController *)next;
        }
        next = next.nextResponder;
    } while (next);
    return nil;
}

@end
