//
//  UIApplication+Add.h
//  Student_IOS
//
//  Created by merit on 2021/7/23.
//

#import <UIKit/UIKit.h>
#import "MRKScene.h"

NS_ASSUME_NONNULL_BEGIN

@interface UIApplication (Add)

@property (nonatomic, assign, class, readonly) BOOL mrk_isSceneApp;

@property (nullable, nonatomic, readonly) UIWindow *mrkKeyWindow;

/**
 * statusBar的frame
 */
+ (CGRect)mrk_statusBarFrame;

/**
 * 当前的windowScene
 */
+ (id)mrk_currentScene;

/**
 * 当前windowSceneDelegate
 */
+ (id)mrk_currentSceneDelegate;

/**
 * 最上面的window
 */
+ (UIWindow *)mrk_currentWindow;

/**
 * 当前关键视图
 */
+ (UIWindow *)mrk_currentKeyWindow;

/**
 * 当前操作的window
 */
+ (UIWindow *)mrk_window;


+ (UIWindow *)mrk_keyWindow;

/**
 * app的 delegate
 */
+ (id<UIApplicationDelegate>)delegate;

/**
 * 跟控制器-- window
 */
+ (__kindof UIViewController *)rootViewController;

/**
 * 最上层的非TabbarController和NavigationBarController的控制器
 */
+ (__kindof  UIViewController *)currentTopViewController;


+ (__kindof UINavigationController *)currentToNavgationController;


+ (void)displayAppPrivacySettings;

///添加跳转
- (void)openMrkUrl:(NSString *)url;

@end



@interface UIView (Navigation_Chain)

- (UINavigationController *_Nonnull)navigationController;

@end

NS_ASSUME_NONNULL_END
