//
//  UIImage+Helper.h
//  Student_IOS
//
//  Created by merit on 2021/8/6.
//

#import <UIKit/UIKit.h>
#import <AssetsLibrary/AssetsLibrary.h>


NS_ASSUME_NONNULL_BEGIN

///渐变色的方向
typedef NS_ENUM(NSUInteger, GradientType) {
    GradientTypeTopToBottom = 0,//从上到小
    GradientTypeLeftToRight = 1,//从左到右
    GradientTypeUpleftToLowright = 2,//左上到右下
    GradientTypeUprightToLowleft = 3,//右上到左下
};


@interface UIImage (Helper)

+ (UIImage *)imageWithColor:(UIColor *)aColor;
+ (UIImage *)imageWithColor:(UIColor *)aColor withFrame:(CGRect)aFrame;



+ (UIImage *)imageWithFileType:(NSString *)fileType;
- (NSData *)dataSmallerThan:(NSUInteger)dataLength;
- (NSData *)dataForCodingUpload;

/// 无损更改图片尺寸
- (UIImage *)scaleToNewSize:(CGSize)targetSize;
- (UIImage *)scaleToSize:(CGSize)size;
///图片切角
+ (UIImage *)circleImage:(UIImage*)image withParam:(CGFloat)inset;
///图片等比例压缩
+ (UIImage *)scaleToSize:(UIImage *)img size:(CGSize)size;


/**
 改变图片的缩放倍率
 @param TargetWidth 目标Width
 @param filePah 图片路径
 @return 改变缩放倍率的图片
 */
+ (UIImage *)imageChangeScaleForTargetWidth:(CGFloat)TargetWidth filePath:(NSString *)filePah;

/**
 view转图片
 */
+ (UIImage *)getImageFromView:(UIView *)view;

+ (UIImage *)haveClolorGFrom:(UIColor *)color1 toColor:(UIColor *)color2 andButton:(UIView *)sender;

+ (UIImage *)encodeQRImageWithContent:(NSString *)content size:(CGSize)size;

- (UIImage *)yp_blurryImage:(UIImage *)image withBlurLevel:(CGFloat)blur;

/**
 base64 字符串转图片
 @param base 字符串数据
 */
+ (UIImage *)imageWithBase64Data:(id)base;

/**
 返回一张渐变色的图片
 @param colors 渐变颜色
 @param gradientType 渐变色方向
 @param imgSize 图片大小
 */
+ (UIImage *)gradientColorImageFromColors:(NSArray*)colors gradientType:(GradientType)gradientType imgSize:(CGSize)imgSize;

//右上到左下
+ (UIImage *_Nullable)gradientImageWithConfigsize:(CGSize)size colorArray:(NSArray *)array;
/**
 返回一张拉伸图片
 */
+ (UIImage *)stretchableImageWithLocalName:(NSString *)imageName;

// 图片变为灰色
- (UIImage *)grayImage;

/**
 修改图片透明度
 */
+ (UIImage *)imageByApplyingAlpha:(CGFloat)alpha image:(UIImage*)image;

/**
 *  获取当前图片的均色，原理是将图片绘制到1px*1px的矩形内，再从当前区域取色，得到图片的均色。
 *  @link http://www.bobbygeorgescu.com/2011/08/finding-average-color-of-uiimage/ @/link
 *
 *  @return 代表图片平均颜色的UIColor对象
 */
- (UIColor *)qmui_averageColor;

- (void)getSubjectColor:(void(^)(UIColor*))callBack;

- (UIImage *)avc_drawCircleImage;

- (NSString *)toBase64;
- (NSString *)toBase64Quality:(CGFloat)quality;
@end

NS_ASSUME_NONNULL_END
