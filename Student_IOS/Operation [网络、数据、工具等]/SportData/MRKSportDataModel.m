//
//  MRKSportDataModel.m
//  Student_IOS
//
//  Created by merit on 2024/11/21.
//

#import "MRKSportDataModel.h"

@implementation MRKSportDataModel
@end



@implementation MRKTrainingInfo
@end


@implementation MRKUnsettleSportDataModel
+ (NSDictionary *)modelContainerPropertyGenericClass {
    return @{@"trainingIncrementData" : TrainingIncrementData.class};
}
@end



@implementation TrainingTrackData
+ (instancetype)initWithEquipModel:(BaseEquipDataModel *)model {
    TrainingTrackData *item = [[TrainingTrackData alloc] init];
    item.speed = !isnan(model.speed.doubleValue) ? model.speed.stringValue : @"0";
    item.distance = model.totalDistance ? model.totalDistance.stringValue : @"0";
    item.deviceTime = model.totalTimeSecond ? model.totalTimeSecond.stringValue : @"0";
    item.spm = model.spm ? model.spm.stringValue : @"0";
    item.num = model.count ? model.count.stringValue : @"0";
    item.resistance = model.drag ? model.drag.stringValue: @"0";
    item.gradient = model.gradient ? model.gradient.stringValue: @"0";
    item.kcal = model.energy ? model.energy.stringValue : @"0";
    item.gear = model.grade ? model.grade.stringValue: @"0";
    item.devicePower = model.power ? model.power.stringValue: @"0";
    item.originalTrackData = [NSObject convertDataToHexStr:model.deviceOriginData] ?:@"";
    item.timestamp = model.deviceTimestamp.stringValue;
    
    item.weight = model.weight.stringValue;
    item.mode = model.mode.stringValue;
    item.electric = model.electric.stringValue;
    item.finishTarget = model.finishTarget.stringValue;

    item.productId = model.type;
    return item;
}
@end

@implementation TrainingIncrementData
@end



@implementation TrainingShowData

/// 整理每一秒所需要用到的热力数据  2022-4-19 wk  v2.7.0
/// 跑步机取速度，其他取阻力）
- (NSNumber *)hotProgressValue {
    NSNumber *value = @0;
    switch (self.productId.intValue) {
        case BicycleEquipment:
        case BoatEquipment:
        case EllipticalEquipment:
        case PowerEquipment:
            value = @(self.resistance.intValue);
            break;
        case TreadmillEquipment:
            if (![self.speed isEqualToString:@"--"]){
                value = [NSNumber numberWithString:self.speed];
            }
            break;
        default:
            break;
    }
    return value;
}

+ (instancetype)initWithTrackModel:(TrainingTrackData *)trackData incrementData:(TrainingIncrementData *)incrementData{
    TrainingShowData *item = [[TrainingShowData alloc] init];
    item.speed = trackData.speed.length > 0 ? [NSString stringWithFormat:@"%.1f", trackData.speed.floatValue] : @"--";
    item.distance = trackData.distance.length > 0 ? [NSString convertDecimalNumber:[NSString stringWithFormat:@"%@", trackData.distance] num:2 dividing:@"1000"] : @"--";
    item.deviceTime = [MRKToolKit MSTimeStrFromSecond:trackData.deviceTime.intValue];
    item.spm = trackData.spm.length > 0 ? trackData.spm : @"--";
    item.num = trackData.num.length > 0 ? trackData.num : @"--";
    item.resistance = trackData.resistance.length > 0 ? trackData.resistance : @"--";
    item.gradient = trackData.gradient.length > 0 ? trackData.gradient : @"--";
    item.kcal = trackData.kcal.length > 0 ? [NSString convertDecimalNumber:trackData.kcal num:1] : @"--";
    item.gear = trackData.gear.length > 0 ? trackData.gear : @"--";
    item.devicePower = trackData.devicePower.length > 0 ? [NSString convertDecimalNumber: [NSString stringWithFormat:@"%@", trackData.devicePower] num:1] : @"--";
    
    /**
        item.speed = trackData.speed ? (trackData.speed.doubleValue == 0 ? @"--" : [NSString stringWithFormat:@"%.1f", trackData.speed.doubleValue] )  : @"--";
        item.distance = trackData.distance.length > 0 ? [NSString convertDecimalNumber:[NSString stringWithFormat:@"%@", trackData.distance] num:2 dividing:@"1000"] : @"--";
        item.deviceTime = [MRKToolKit MSTimeStrFromSecond:trackData.deviceTime.intValue];
        item.spm = trackData.spm ? (trackData.spm.intValue == 0 ? @"--" :  trackData.spm)  : @"--";
        item.num = trackData.num.length > 0 ? trackData.num : @"--";
        item.resistance = trackData.resistance ? (trackData.resistance.intValue == 0 ? @"--" :  trackData.resistance)  : @"--";
        item.gradient = trackData.gradient ? (trackData.gradient.intValue == 0 ? @"--" :  trackData.gradient)  : @"--";
        item.kcal = trackData.kcal.length > 0 ? [NSString convertDecimalNumber:trackData.kcal num:1] : @"--";
        item.gear = trackData.gear ? (trackData.gear.intValue == 0 ? @"--" :  trackData.gear)  : @"--";
        item.devicePower = trackData.devicePower.length > 0 ? [NSString convertDecimalNumber: [NSString stringWithFormat:@"%@", trackData.devicePower] num:1] : @"--";
     */
    item.originalTrackData = trackData.originalTrackData;
    item.timestamp = trackData.timestamp;
    item.totalDistance = [NSString convertDecimalNumber:[NSString stringWithFormat:@"%lf", incrementData.totalDistance] num:2 dividing:@"1000"];
    item.totalTime = [MRKToolKit MSTimeStrFromSecond:incrementData.totalTime];
    item.totalNum = [NSString stringWithFormat:@"%d", incrementData.totalNum];
    item.totalKcal = [NSString convertDecimalNumber:[NSString stringWithFormat:@"%lf", incrementData.totalKcal] num:1];
    
    item.weight = trackData.weight.length > 0 ? [NSString convertDecimalNumber:trackData.weight num:1] : @"--";
    item.mode = trackData.mode;
    item.electric = [NSString stringWithFormat:@"%@%%", trackData.electric ? : @"0"];
    item.isShowElectric = trackData.electric.length > 0;
    item.isShowElectricTip = trackData.electric.length > 0 && trackData.electric.doubleValue < 30;
    item.finishTarget = trackData.finishTarget;
    
    item.productId = trackData.productId;
    return item;
}

@end
