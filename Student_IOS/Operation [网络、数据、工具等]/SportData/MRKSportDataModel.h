//
//  MRKSportDataModel.h
//  Student_IOS
//
//  Created by merit on 2024/11/21.
//

#import <Foundation/Foundation.h>
#import "BaseEquipData.h"

NS_ASSUME_NONNULL_BEGIN

///上报类型
typedef NS_ENUM(NSInteger, MRKTrainingType)  {
    MRKTrainingTypeCourse       = 1,    ///课程训练
    MRKTrainingTypeFreedom      = 2,    ///自由训练
    MRKTrainingTypeRealVideo    = 6,    ///实景视频
    MRKTrainingTypeLandPath     = 7,    ///路径训练
    MRKTrainingTypeMotion       = 9,    ///动作训练
    ///
    ///
    MRKTrainingTypeDrink        = 101,  ///饮食任务()
};


@class TrainingTrackData;
@class TrainingIncrementData;

@interface MRKSportDataModel : NSObject
@end



@interface MRKTrainingInfo : NSObject
@property (nonatomic, assign) MRKTrainingType trainingType;    ///训练类型：
///
/// 自由训练（0-无任何模式，1-定距练，2-定时练）
///
@property (nonatomic, copy) NSString *trainingMode;            ///训练模式
@property (nonatomic, copy, nullable) NSString *trainingTarget;///训练目标值
@property (nonatomic, copy, nullable) NSString *courseId;      ///课程id
@end




@interface MRKUnsettleSportDataModel : MRKBaseModel
///产品大类id
@property (nonatomic, copy) NSString *productId;
@property (nonatomic, copy) NSString *productImg;
///设备型号id
@property (nonatomic, copy) NSString *productModelId;
///设备信息id 【同deviceUserRealId】
@property (nonatomic, copy) NSString *equipmentInfoId;
@property (nonatomic, copy) NSString *bluetoothName;
@property (nonatomic, copy) NSString *deviceAlias;
@property (nonatomic, copy) NSString *communicationProtocol;
@property (nonatomic, copy) NSString *communicationType;
///用户的终端设备类型, 1-手机，2-平板
@property (nonatomic, strong) NSNumber *platformType;
/// 训练记录id
@property (nonatomic, copy) NSString *trainingRecordId;
/// 用户ID
@property (nonatomic, copy) NSString *userId;
/// 1 :训练进行中     10 :训练完成     11 :报告生成中      20 :报告已生成
@property (nonatomic, copy) NSString *status;
/// 训练类型：1 :课程训练    2 :自由训练    6 :实景视频    20 :绝影之竞 - 游戏    5 :顽鹿
@property (nonatomic, assign) MRKTrainingType trainingType;
/// 训练模式：0 :自由练   1 :定距   2 :定时
@property (nonatomic, copy) NSString *trainingMode;
/// 训练目标值 trainingMode=0可为空,trainingMode=1为距离米，trainingMode=2为时间秒
@property (nonatomic, copy) NSString *trainingTarget;
/// 上一次的瞬时数据
@property (nonatomic, strong) TrainingTrackData *trainingTrackData;
/// 累加数据
@property (nonatomic, strong) TrainingIncrementData *trainingIncrementData;
/// 新链路是否第一次打开训练报告页面
@property (nonatomic, assign) BOOL firstInto;
/// 有没有重连成功过
@property (nonatomic, copy) NSString *isReconnect;
///
@property (nonatomic, copy) NSString *clearableEquipment;
/// 是否计入排名：0-否，1-是（默认0）
@property (nonatomic, assign) BOOL isCountRank;
/// 用于实景竞技排名的时长
@property (nonatomic, copy) NSString *matchDuration;
/// 本次训练报告排名快照（超过99存99+）
@property (nonatomic, copy) NSString *rank;

@property (nonatomic, assign) NSInteger scene;

/// 上报课程信息
@property (nonatomic, strong, nullable) MRKCourseModel *courseModel;
/// 视频课程是否中途退出
@property (nonatomic, assign) BOOL exitOnPlaying;
/// 播放时长  [课程训练，计划训练，活动训练 必传]
@property (nonatomic, strong, nullable) NSNumber *playTime;
@end




/// ===========================================================================

@interface TrainingTrackData : MRKBaseModel
@property (nonatomic, copy) NSString *kcal;                   ///消耗
@property (nonatomic, copy) NSString *distance;               ///距离：米
@property (nonatomic, copy) NSString *deviceTime;             ///设备时长
@property (nonatomic, copy) NSString *speed;                  ///速度
@property (nonatomic, copy) NSString *spm;                    ///踏频/桨频
@property (nonatomic, copy) NSString *num;                    ///总踏数/总桨数/总个数/总圈数
@property (nonatomic, copy) NSString *resistance;             ///阻力
@property (nonatomic, copy) NSString *gradient;               ///坡度
@property (nonatomic, copy) NSString *gear;                   ///档位
@property (nonatomic, copy) NSString *devicePower;            ///设备功率
@property (nonatomic, copy) NSString *timestamp;              ///上报时间戳【巧盼塞的本地时间】
@property (nonatomic, copy) NSString *originalTrackData;      ///原始数据
///=======================================================================
@property (nonatomic, copy) NSString *weight;                 ///重量
@property (nonatomic, copy) NSString *electric;               ///电量
@property (nonatomic, copy) NSString *mode;                   ///模式
@property (nonatomic, copy) NSString *finishTarget;           ///完成目标标识位
///
@property (nonatomic, copy) NSString *linkId;                 ///课程小节id
@property (nonatomic, strong) NSNumber *playTime;             ///视频实际播放时长
@property (nonatomic, strong) NSNumber *productId;            ///类型.手动.大类
@property (nonatomic, copy, nullable) NSString *meritRate;    ///每秒燃脂率（保留4位小数，向上取整）
//@property (nonatomic, copy) NSString *isReconnect;
//@property (nonatomic, copy) NSString *clearableEquipment;
//@property (nonatomic, assign) NSInteger scene;

/// 初始化转换
+(instancetype)initWithEquipModel:(BaseEquipDataModel *)model;
@end




@interface TrainingIncrementData : MRKBaseModel
@property (nonatomic, assign) double totalDistance;               ///累加距离
@property (nonatomic, assign) int totalTime;                      ///累加设备时长
@property (nonatomic, assign) int totalNum;                       ///累加踏数/桨数/个数/圈数
@property (nonatomic, assign) double totalKcal;                   ///累加消耗
@property (nonatomic, copy, nullable) NSString *totalMeritBurning;   ///累加消耗
@end





@interface TrainingShowData : MRKBaseModel
@property (nonatomic, copy) NSString *speed;                ///跳绳）速度 （跑步机）速度
@property (nonatomic, copy) NSString *distance;             ///距离
@property (nonatomic, copy) NSString *deviceTime;           ///设备时长
@property (nonatomic, copy) NSString *spm;                  ///踏频/桨频
@property (nonatomic, copy) NSString *num;                  ///总踏数/总桨数/总个数/总圈数
@property (nonatomic, copy) NSString *resistance;           ///阻力
@property (nonatomic, copy) NSString *gradient;             ///坡度
@property (nonatomic, copy) NSString *kcal;                 ///消耗
@property (nonatomic, copy) NSString *gear;                 ///档位
@property (nonatomic, copy) NSString *weight;               ///重力
@property (nonatomic, copy) NSString *electric;             ///电量
@property (nonatomic, assign) BOOL isShowElectric;
@property (nonatomic, assign) BOOL isShowElectricTip;
@property (nonatomic, copy) NSString *mode;                 ///模式
@property (nonatomic, copy) NSString *finishTarget;         ///完成目标标识位
@property (nonatomic, copy) NSString *devicePower;          ///设备功率
@property (nonatomic, copy) NSString *originalTrackData;    ///原始数据
@property (nonatomic, copy) NSString *timestamp;            ///上报时间戳
@property (nonatomic, copy) NSString *totalDistance;        ///累加距离
@property (nonatomic, copy) NSString *totalTime;            ///累加设备时长
@property (nonatomic, copy) NSString *totalNum;             ///累加踏数/桨数/个数/圈数
@property (nonatomic, copy) NSString *totalKcal;            ///累加消耗
@property (nonatomic, strong) NSNumber *productId;          ///类型.手动.大类
@property (nonatomic, strong) NSNumber *hotProgressValue;   ///热力值
///
/// 初始化转换
+(instancetype)initWithTrackModel:(TrainingTrackData *)trackData incrementData:(TrainingIncrementData *)incrementData;

@end

NS_ASSUME_NONNULL_END
