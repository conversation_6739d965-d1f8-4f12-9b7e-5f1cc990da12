//
//  MrkEquipmentInfo.m
//  Student_IOS
//
//  Created by Junq on 2024/11/21.
//

#import "MrkEquipmentInfo.h"
#import "NewConnectManager.h"
#import "TreamillStatusManager.h"
#import "ZJProtocolCommandManager.h"
#import "QCProtocolCommandManager.h"


#define DATA_LOCK(...) dispatch_semaphore_wait(self->_lock, DISPATCH_TIME_FOREVER); \
__VA_ARGS__; \
dispatch_semaphore_signal(self->_lock);


@interface MrkEquipmentInfo (){
    dispatch_semaphore_t _lock; ///< lock for _buffer
    CFAbsoluteTime _startTime;
}
@property (nonatomic, strong, nullable) EquipmentDetialModel *detailModel;
@property (nonatomic, assign) BOOL deviceOnCleanStates; ///标识设备是否在清零过程中
///筋膜枪 自己计时
@property (nonatomic, strong) dispatch_source_t timeTimer;
@property (nonatomic, assign) BOOL isTimerSuspend;
///
@property (nonatomic, strong) NewConnectManager *connectManager;
@property (nonatomic, strong) TreamillStatusManager *treamillStatusManager; ///跑步机运动状态管理类
@property (nonatomic, assign) int totalTimeSecond; ///筋膜枪和P03力量站时间本地计算
@property (nonatomic, strong) NSNumber *skipElectic; ///跳绳电量
///
@property (nonatomic, assign) DEVICE_CONNECT_STATUS connectStatus;
@property (nonatomic, strong) NSNumber *treamillStatus;
@property (nonatomic, assign) NSInteger treamillCutdown;
///
@property (nonatomic, strong, nullable) BaseEquipDataModel *dataModel;

@property (nonatomic, assign) BOOL isEndGetData;
@end

@implementation MrkEquipmentInfo

- (BOOL)isPowerP03{
    if (!self.model) return NO;
    NSString *productName = self.model.productName;
    return [productName localizedCaseInsensitiveContainsString:@"-P03"];
}
///判断是否小彩屏
- (BOOL)isJudgeBlufiDevice{
    if (!self.model) return NO;
    return [[MRKDeviceManager shareManager] isJudgeBlufiDevice:self.model.communicationType];
}

- (NSString *)productId{
    if (!self.model) return @"";
    return self.model.productId ?:@"";
}

- (NSString *)productName{
    if (!self.model) return @"";
    return self.model.productName ?:@"";
}

///是否需要计时器
- (BOOL)deviceNeedTimer{
    if (self.model == nil) return NO;
    if (self.productId.intValue == JinMoQiangEquipment || [self isPowerP03]) {
        return YES;
    }
    return NO;
}

- (NewConnectManager *)connectManager {
    if (!_connectManager) {
        _connectManager = [[NewConnectManager alloc]init];
        _connectManager.connectMode = AutoDeviceConnectMode;
    }
    return _connectManager;
}

- (TreamillStatusManager *)treamillStatusManager {
    if (!_treamillStatusManager) {
        _treamillStatusManager = [TreamillStatusManager new];
    }
    return _treamillStatusManager;
}

- (void)dealloc {
    NSLog(@"😊😊😊-----dealloc-----😊😊😊%@" , NSStringFromClass([self class]));
    [[NSNotificationCenter defaultCenter] removeObserver:self];
    
    ///销毁定时器
    [self closeTimeTimer];
    
    ///释放心跳包管理类
    [BlueBeatManager attempDealloc];
    
    ///释放处理器
    [BaseBlueCommandManager attempDeallocDevice:self.productId];
}

- (instancetype)init {
    self = [super init];
    if (self) {
        _lock = dispatch_semaphore_create(1);
        self.dataModel = [[BaseEquipDataModel alloc] init];
        
        @weakify(self);
        RACSignal *signal = [RACObserve(self, model) takeUntil:[self rac_willDeallocSignal]];
        [signal subscribeNext:^(id  _Nullable x) {
            [self_weak_ configEquipmentInfo];
        }];
    }
    return self;
}

- (void)setModel:(MRKDeviceModel *)model{
    _model = model;
}

- (void)configEquipmentInfo{
    if (self.model == nil) return;
    
    NSDictionary *parms = @{
        @"equipName" : self.model.bluetoothName ?:@"",
        @"oneLevelTypeId" : self.model.productId ?:@""
    };
    [MRKBaseRequest mrkSilenceGetRequestUrl:@"/equip/equipment/equipmentInfoController/getEquipTypeInfoByName/v2"
                                    andParm:parms
                   completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        id data = [request.responseObject valueForKeyPath:@"data"];
        EquipmentDetialModel *m = [EquipmentDetialModel modelWithJSON:data];
        m.productID = self.productId; ///接口没传
        self.detailModel = m;
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        MLog(@"configEquipmentInfo error");
    }];
    
    [self startConfigListener];
    ///
    [self deviceConnectManager];
}

- (void)startConfigListener{
    
    NSString *productId = self.productId;
    switch (productId.intValue) {
        case JinMoQiangEquipment: {
            
            [self creatTimer];
        } break;
        case TreadmillEquipment: {
            ///跑步机倒计时
            [[NSNotificationCenter defaultCenter] addObserver:self 
                                                     selector:@selector(treamillCutdown:)
                                                         name:TreamillStartCutdownNotification
                                                       object:nil];
            
            @weakify(self);
            [[[RACObserve(self.treamillStatusManager, currentStatus) distinctUntilChanged] takeUntil:[self rac_willDeallocSignal]] subscribeNext:^(NSNumber *x) {
                @strongify(self);
                if (!x) {return;}
                self.treamillStatus = x;
                
                dispatch_async(dispatch_get_main_queue(), ^{
                    TREAMILL_STATUS satus = x.intValue;
                    switch (satus) {
                        case DeviceStandbyStatus: case DeviceX1StandbyStatus:
                        { ///待机中
                            NSLog(@"treamillStatusManager=========待机中=========");
                        } break;
                            
                        case DeviceCutDownStatus:
                        {///启动中
                            NSLog(@"reamillStatusManager=========启动中=========");
                        } break;
                            
                        case DeviceRuningStatus:
                        {///运行中
                            NSLog(@"reamillStatusManager=========运行中=========");
                        } break;
                            
                        case DevicelSlowDownStatus:
                        {///减速中
                            NSLog(@"reamillStatusManager=========减速中=========");
                        } break;
                            
                        case DevicePauseStatus:
                        {///暂停中
                            NSLog(@"reamillStatusManager=========暂停中=========");
                            
                            ///暂停后清空瞬时数据。保证视频超燃脂率计算不按原速度继续计算
                            [self clearDeviceInstantaneousData];
                        } break;
                        default: break;
                    }
                });
            }];
        } break;
        case PowerEquipment: {
           
            [self creatTimer];
        } break;
        default:
            break;
    }
    
    ///添加蓝牙设备数据监听
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(getBlueDeviceData:)
                                                 name:BlueDeviceDataNotification
                                               object:nil];
}

- (void)creatTimer{
    if (self.model == nil) { return; }
    if ([self deviceNeedTimer]) {
        [self initTimer];
        
        [[NSNotificationCenter defaultCenter] addObserver:self
                                                 selector:@selector(suspendTimeTimer)
                                                     name:JMQPauseNotification
                                                   object:nil];
    }
}

- (void)treamillCutdown:(NSNotification *)sender{
    NSNumber *time = sender.object;
    NSLog(@"treamillCutdown========%@",time);
    self.treamillCutdown = time.integerValue;
    if (self.treamillCutdownBlock) {
        self.treamillCutdownBlock(time.integerValue);
    }
}


///设备蓝牙连接状态管理
- (void)deviceConnectManager {
    @weakify(self);
    RACSignal *signal = [[[MRKConnectStatusManager sharedInstance] connectStatusWithProductID:self.productId] filter:^BOOL(NSNumber *status) {
        return status.intValue > 0; // 过滤
    }];
    [[[signal distinctUntilChanged] takeUntil:[self rac_willDeallocSignal]] subscribeNext:^(NSNumber * x) {
        @strongify(self);
        
        NSLog(@"MRKConnectStatusManager status ======== %@", x);
        
        self.connectStatus = x.intValue;
        dispatch_async(dispatch_get_main_queue(), ^{
            switch (x.intValue) {
                case DeviceConnecting:
                { ///连接中
                    
                } break;
                case DeviceDisconnect:
                {///未连接
                    self->_startTime = 0;
                    
                    if ([self deviceNeedTimer]) {
                        [self suspendTimeTimer];
                    }
                    
                    ///清除设备瞬时数据
                    [self clearDeviceInstantaneousData];
                } break;
                    
                case DeviceConnected:
                {///已连接
                    
                    ///小彩屏重连以后发送继续指令
                    if (self.isJudgeBlufiDevice) {
                        [ZJProtocolCommandManager goonCarSport:self.productId];
                    }
                } break;
                    
                case DeviceAutoConnecting:
                {///自动重连中
                    
                } break;
                    
                case DeviceScaning:
                {///设备搜索中
                    
                } break;
                default: break;
            }
        });
    }];
}

#pragma mark - device not connect clear instantaneous data
- (void)clearDeviceInstantaneousData{
    if (self.dataModel == nil){ return;}
    
    BaseEquipDataModel *model = self.dataModel.copy;
    NSInteger productId = model.type.integerValue;
    switch (productId) {
        case TreadmillEquipment: {
            model.speed = @(0.0);
            model.gradient = @(0);
            model.deviceTimestamp = @([MRKToolKit current13TimeInterval]);
            if (self.equipmentInfoDataBlock){
                self.equipmentInfoDataBlock(model);
            }
        } break;
            
        case BicycleEquipment:
        case EllipticalEquipment:
        case StairClimbEquipment:
        case BoatEquipment:{
            model.spm = @(0);
            model.drag = @(0);
            model.deviceTimestamp = @([MRKToolKit current13TimeInterval]);
            if (self.equipmentInfoDataBlock){
                self.equipmentInfoDataBlock(model);
            }
        } break;
            
        default:
            break;
    }
}




#pragma mark - device data
- (void)getBlueDeviceData:(NSNotification *)sender {
    BaseEquipDataModel *data = sender.object;
    data.equipmentName = self.productName;
    
    if (!self.model || data.type.intValue != self.productId.intValue) {
        return;
    }
    
    if (self.isEndGetData) {
        return;
    }
    
    ///第一条数据开始塞值
    if (_startTime == 0) {
        _startTime = CFAbsoluteTimeGetCurrent();
    }
    
    NSInteger productId = data.type.integerValue;
    switch (productId) {
        case SkipRopeEquipment: {
            ///如果跳绳设备 并且 是智健协议,
            if (self.model.communicationProtocol.intValue == ZJCommunicationProtocol){
                [self ropeSkipping:data];
                return;
            }
            
        } break;
        case JinMoQiangEquipment: {
            data.totalTimeSecond = @(self.totalTimeSecond);
            
            ///防止筋膜枪调节档位 电量跳动
            if (data.electric.intValue < self.dataModel.electric.intValue) {
                data.electric = self.dataModel.electric;
            }
        } break;
        case PowerEquipment: {
            if ([self isPowerP03]){
                data.totalTimeSecond = @(self.totalTimeSecond);
            }
        } break;
        case BicycleEquipment: {
            if (data.spm.intValue > kMaxCarSmp) {
                data.spm = @(kMaxCarSmp);
            }
        } break;
        case TreadmillEquipment: {
            
        } break;
        case BoatEquipment: {
            if (data.spm.intValue > kMaxBoatSmp) {
                data.spm = @(kMaxBoatSmp);
            }
        } break;
        case EllipticalEquipment: {
            if (data.spm.intValue > kMaxEllipticalSmp) {
                data.spm = @(kMaxEllipticalSmp);
            }
        } break;
        case StairClimbEquipment: {
            if (data.spm.intValue > kMaxStairClimbSmp) {
                data.spm = @(kMaxStairClimbSmp);
            }
        } break;
            
        default:
            break;
    }
    
    ///设备心率
    if (data.deviceRate.intValue > kMaxHeartRate) {
        data.deviceRate = @(kMaxHeartRate);
    }
    
    DATA_LOCK(
        ///数据刷新
        data = [self.dataModel mergeWithModel:data];
        self.dataModel = data;
    );
    
    ///清零过程中...
    if (self.deviceOnCleanStates) return;
    if (self.connectStatus != DeviceConnected) return;
    if (CFAbsoluteTimeGetCurrent() - _startTime >= 1.0){
        _startTime = CFAbsoluteTimeGetCurrent();
        if (self.equipmentInfoDataBlock){
            self.equipmentInfoDataBlock(self.dataModel);
        }
    }
}

#pragma mark - 跳绳数据处理
- (void)ropeSkipping:(BaseEquipDataModel *)data {
    ///此条跳绳数据只有电量数据
    if (data.electric) {
        self.skipElectic = data.electric;
        return;
    }
    
    int x = arc4random() % 3;
    float costFloat = (x == 0) ? 0.05 : (x == 1 ? 0.06 : 0.07);
    
    double count = 0;
    if (data.count.intValue > self.dataModel.count.intValue){
        count = data.count.intValue - self.dataModel.count.intValue;
    }
    
    data.energy = @(self.dataModel.energy.doubleValue + costFloat * count);
    data.electric = self.skipElectic;
    self.dataModel = data;
    
    ///清零过程中...
    if (self.deviceOnCleanStates) return;
    if (self.connectStatus != DeviceConnected) return;
    if (CFAbsoluteTimeGetCurrent() - _startTime > 1.0){
        _startTime = CFAbsoluteTimeGetCurrent();
        if (self.equipmentInfoDataBlock){
            self.equipmentInfoDataBlock(self.dataModel);
        }
    }
}



#pragma mark - 筋膜枪/P03力量站 计时器
- (void)initTimer{
    _isTimerSuspend = YES;///默认暂停状态
    dispatch_queue_t queue = dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0);
    _timeTimer = dispatch_source_create(DISPATCH_SOURCE_TYPE_TIMER, 0, 0, queue);
    NSTimeInterval delayTime = 0.0f;
    NSTimeInterval timeInterval = 1.0f;
    dispatch_time_t startDelayTime = dispatch_time(DISPATCH_TIME_NOW, (int64_t)(delayTime * NSEC_PER_SEC));
    dispatch_source_set_timer(_timeTimer, startDelayTime, timeInterval*NSEC_PER_SEC, 0.1*NSEC_PER_SEC);
    dispatch_source_set_event_handler(_timeTimer,^{
        dispatch_async(dispatch_get_main_queue(), ^{
            self.totalTimeSecond ++;
        });
    });
}
- (void)suspendTimeTimer{
    if (_timeTimer && !_isTimerSuspend) {
        dispatch_suspend(_timeTimer);
        _isTimerSuspend = YES;
    }
}
- (void)resumeTimeTimer{
    if (_timeTimer && _isTimerSuspend) {
        dispatch_resume(_timeTimer);
        _isTimerSuspend = NO;
    }
}
- (void)closeTimeTimer {
    if (_timeTimer) {
        if (_isTimerSuspend) {
            dispatch_resume(_timeTimer);
        }
        dispatch_source_cancel(_timeTimer);
        _timeTimer = nil;
    }
}



#pragma mark - 开始获取设备数据
- (void)startGetData {
    NSLog(@"开始获取数据了");
    if (self.connectStatus != DeviceConnected) return;
    if ([self.productId isEmpty]) return;
    
    ///接收到数据 启动计时器
    if ([self deviceNeedTimer]) {
        [self resumeTimeTimer];
    }
    
    self.isEndGetData = NO;
    
    ///开始获取数据
    [BaseBlueCommandManager startSportWithType:self.productId];
    
    ///开启设备心跳包
    [BlueBeatManager shareManager].type = self.productId;
    [[BlueBeatManager shareManager] startPing];
    
    /// 开始指令 进入课程 设置用户信息
    /// 智能壶铃
    if (self.productId.intValue == KettleBellEquipment) {
        [NSThread sleepForTimeInterval:0.01];
        [QCProtocolCommandManager setCourseStartOrEnd:self.productId status:1];
        
        /// 设置用户信息
        [NSThread sleepForTimeInterval:0.01];
        NSDictionary *parms = @{
            @"height": UserInfo.height?:@0,
            @"weight": UserInfo.weight?:@0,
            @"age": @(UserInfo.age)
        };
        [QCProtocolCommandManager setUserInfo:self.productId para:parms];
    }
}

- (void)endGetData{
    NSLog(@"结束获取数据了");
    if (self.connectStatus != DeviceConnected) return;
    if ([self.productId isEmpty]) return;
    if (self.isEndGetData) return;
    self.isEndGetData = YES;
    
    if ([self deviceNeedTimer]) {
        [self suspendTimeTimer];
    }
    
    ///智能壶铃 结束课程
    if (self.productId.intValue == KettleBellEquipment) {
        [QCProtocolCommandManager setCourseStartOrEnd:self.productId status:0];
        [NSThread sleepForTimeInterval:0.01];
    }
    
    ///结束运动
    [BaseBlueCommandManager endSportWithType:self.productId];
    ///订阅/取消订阅 数据服务-
    [BlueCommunicationProtocol subscribeCharacteristicFromType:self.productId isSubscribe:NO];
    ///停止心跳包
    [[BlueBeatManager shareManager] stopPing];
}




// MARK: 交互 【tips：暂时只支持跑步机，其他设备无响应】
- (void)equipmentStart{
    if (self.productId.intValue != TreadmillEquipment) return;
    [self.treamillStatusManager treamillStart];
}

- (void)equipmentPause{
    if (self.productId.intValue != TreadmillEquipment) return;
    [self.treamillStatusManager treamillPause];
}

- (void)equipmentContinue{
    if (self.productId.intValue != TreadmillEquipment) return;
    [self.treamillStatusManager treamillGoon];
}

- (void)equipmentEnd{
    if (self.productId.intValue != TreadmillEquipment) return;
//    [self.treamillStatusManager treamillEnd];
}



/// MARK: 设备指令下发
- (void)equipmentControl:(NSDictionary *)parms{
    NSMutableDictionary *newParm = [NSMutableDictionary dictionaryWithDictionary:parms];
    if(![newParm.allKeys containsObject:@"type"]){
        newParm[@"type"] = self.productId;
    }
    
    [BaseBlueCommandManager setDeviceParameters:newParm];
}



/// MARK: 设备数据清零
- (void)equipmentClean:(nullable ResultBlock)completion{
    if (!self.detailModel.isClean) {
        MLog(@"设备不支持清零");
        if (completion) {
            completion(NO, nil);
        }
        return;
    }
    
    self.deviceOnCleanStates = YES;
    ///是否是小彩屏
    if (self.isJudgeBlufiDevice) {
        MLog(@"给小彩屏发送就绪");
        [ZJProtocolCommandManager startCarSport:self.productId];
        [NSThread sleepForTimeInterval:0.05];
        
        self.deviceOnCleanStates = NO;
        if (completion) {
            completion(YES, nil);
        }
        return;
    }
    
    @weakify(self);
    NSString *name = ClearDatSendSuccessNotification;
    if (self.model.communicationProtocol.intValue == BQCommunicationProtocol){
        name = IconsoleResetDataSuccessNotification;
    }
    __block RACDisposable *Disposable = [[[NSNotificationCenter defaultCenter] rac_addObserverForName:name object:nil] subscribeNext:^(id x) {
        @strongify(self);
        [Disposable dispose];
        
        self.deviceOnCleanStates = NO;
        
        if (self.isEndGetData) {
            return;
        }
        
        if (completion) {
            completion(YES, nil);
        }
    }];
    MLog(@"开始清零sendClearDataCmd");
    [BluetoothCommondManager sendClearDataCmd:self.productId];
}




/// 设置模式
/// @param completion 操作结果回调
- (void)equipmentMode:(NSDictionary *)params completion:(nullable ResultBlock)completion{
    
    EquipmentType deviceType = (EquipmentType)self.productId.intValue;
    if (deviceType == SkipRopeEquipment ||
        deviceType == HoopEquipment ||
        deviceType == FLSBEquipment ) {
        
        ///打开数据通道
        [BaseBlueCommandManager startSportWithType:self.productId];
        
        NSString *observerName = nil;
        BluetoothModel *model = [BluetoothModel modelFromEquipmentType:self.productId];
        NSNumber *dataService = model.dataServiceType;
        if (model.dataServiceType.intValue == ZJCommunicationProtocol) {
            if (deviceType == SkipRopeEquipment) {
                observerName = @"jumpStringDataCutDown";
            }
        } else if (dataService.intValue == MRKCommunicationProtocol) {
            observerName = @"MRKSetCutDownModeSuccessNotification";
        }
        
        if ([observerName isNotBlank]) {
            @weakify(self);
            __block id observer = nil;
            observer = [[NSNotificationCenter defaultCenter] addObserverForName:observerName object:nil queue:nil usingBlock:^(NSNotification * _Nonnull notification) {
                @strongify(self);
                dispatch_async(dispatch_get_main_queue(), ^{
                    [[NSNotificationCenter defaultCenter] removeObserver:observer];
                    [[NSNotificationCenter defaultCenter] removeObserver:self name:@"MRKControlModeFailureNotification" object:nil];
                });
                
                ///跳绳需要发送start
                if ([observerName isEqualToString:@"jumpStringDataCutDown"]) {
                    [[BluetoothManager sharedInstance] sendData:[BloothTool skipStartData] type:@(SkipRopeEquipment).stringValue];
                }
                
                [MBProgressHUD hideHUDForView:nil];
                if (completion){
                    completion(YES, nil);
                }
            }];
            
            [[NSNotificationCenter defaultCenter] addObserverForName:@"MRKControlModeFailureNotification" object:nil queue:nil usingBlock:^(NSNotification * _Nonnull notification) {
                @strongify(self);
                dispatch_async(dispatch_get_main_queue(), ^{
                    [[NSNotificationCenter defaultCenter] removeObserver:observer];
                    [[NSNotificationCenter defaultCenter] removeObserver:self name:@"MRKControlModeFailureNotification" object:nil];
                });
                
                [MBProgressHUD hideHUDForView:nil];
                [MBProgressHUD showMessage:@"模式设置失败, 请重试"];
            }];
        }
        
        [[NSNotificationCenter defaultCenter] postNotificationName:SetModeNotification object:params];
        [MBProgressHUD showLodingWithMessage:@"" view:nil];
    }
}




/// MARK: 设备连接
- (void)equipmentConnect{
    [self equipmentConnect:nil];
}
- (void)equipmentConnect:(nullable ResultBlock)completion{
    if (self.connectStatus == DeviceConnected) {
        NSError *error = [NSError errorWithDomain:@"" code:ConnectingBleAuthFailCode userInfo:@{NSLocalizedDescriptionKey : @"设备已连接"}];
        if (completion){
            completion(NO, error);
        }
    }
    
    ///去连接
    self.connectManager.connectStatusBlock = ^(NSNumber *data) {
        if (completion){
            completion(data.boolValue, nil);
        }
    };
    [self.connectManager connectDeviceModel:self.model];
}


/// MARK: 设备断连
- (void)equipmentDisconnect{
    [self.connectManager cancelConnectDeviceModel:self.model];
}



/// MARK: 解绑
- (void)equipmentUnbind{
    [self equipmentUnbind:nil];
}
- (void)equipmentUnbind:(nullable ResultBlock)completion{
    self.connectManager.unbindStatusBlock = ^(NSNumber *data) {
        if (completion){
            completion(data.boolValue, nil);
        }
    };
    [self.connectManager unbindDeviceModel:self.model];
}

@end


