//
//  MRKLogManager.h
//  Student_IOS
//
//  Created by merit on 2023/9/12.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN
// 日志压缩包文件名
static NSString* ZipFileName = @"Log.zip";

@interface MRKLogManager : NSObject

/// 压缩日志
+ (void)zipLog:(void(^)(NSString *path))completed;

/// 删除压缩日志
+ (void)deleteZipLog;

+ (void)clearLog;

///上传的log名称
+ (NSString *)uploadLogName;

///
+ (void)judgeAndUploadLog;
@end

NS_ASSUME_NONNULL_END
