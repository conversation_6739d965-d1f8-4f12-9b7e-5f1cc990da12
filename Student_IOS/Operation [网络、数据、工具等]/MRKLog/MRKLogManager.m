//
//  MRKLogManager.m
//  Student_IOS
//
//  Created by merit on 2023/9/12.
//

#import "MRKLogManager.h"
#import "SQLogManager.h"
#import <SSZipArchive.h>
#import "OSSManager.h"

@implementation MRKLogManager

+ (NSString *)zipPath {
    NSString *cachesDir = [NSSearchPathForDirectoriesInDomains(NSCachesDirectory, NSUserDomainMask, YES) firstObject];
    NSString *path = [cachesDir stringByAppendingPathComponent:ZipFileName];
    return path;
}

/// 压缩日志
+ (void)zipLog:(void(^)(NSString *path))completed {
    // 获取所有log 路径
    NSMutableArray *log = [NSMutableArray array];
    NSArray *file = [[SQLogManager sharedInstance] filePaths];
    NSArray *blue = [[SQLogManager sharedInstance] bluePaths];
    [log addObjectsFromArray:file];
    [log addObjectsFromArray:blue];
    [self addFatScaleLog:log];
    
    if (log.count > 0) {
        // 压缩之前，先删除压缩
        [self deleteZipLog];
        
        // 压缩文件
        [SSZipArchive createZipFileAtPath:[self zipPath] withFilesAtPaths:log withPassword:nil progressHandler:^(NSUInteger entryNumber, NSUInteger total) {
            if (entryNumber == total) {
                completed([self zipPath]);
            }
        }];
    }else {
        completed(@"");
    }
}


/// 删除压缩日志
+ (void)deleteZipLog {
    NSString *zipFilePath = [self zipPath];
    if ([[NSFileManager defaultManager] fileExistsAtPath:zipFilePath]) {
        [[NSFileManager defaultManager] removeItemAtPath:zipFilePath error:nil];
    }
}

+ (void)clearLog {
    NSString *file = [SQLogManager fileLogsDirectory];
    NSString *blue = [SQLogManager blueLogsDirectory];
    if ([[NSFileManager defaultManager] fileExistsAtPath:file]) {
        [[NSFileManager defaultManager] removeItemAtPath:file error:nil];
    }
    if ([[NSFileManager defaultManager] fileExistsAtPath:blue]) {
        [[NSFileManager defaultManager] removeItemAtPath:blue error:nil];
    }
}


/// 添加体脂秤log
+ (void)addFatScaleLog:(NSMutableArray *)log {
    NSString *documentsDirectory = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) firstObject];
    NSString *path = [documentsDirectory stringByAppendingPathComponent:@"icomonlogs"];
    // 判断有没有这个路径
    NSFileManager *fm =[NSFileManager defaultManager];
    if ([fm fileExistsAtPath:path]) {
        NSArray *files = [fm subpathsAtPath:path];
        NSMutableArray *paths = [NSMutableArray array];
        [files enumerateObjectsUsingBlock:^(NSString *  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
            [paths addObject:[path stringByAppendingPathComponent:obj]];
        }];
        [log addObjectsFromArray:paths];
    }
}

+ (NSString *)uploadLogName {
    return [NSString stringWithFormat:@"%@_%@_%@_%@",@(kTerminal), @(kDeviceType),[MRKTimeManager getNowTimeDate],ZipFileName];
}





/// 判断是否上传日志
+ (void)judgeAndUploadLog {
    [MRKBaseRequest mrkRequestType:YTKRequestMethodGET
                               url:@"/user/user-setting/get"
                           andParm:@{@"type": @3} // 1-空练设置开关，2-阻力映射设置开关， 3-用户日志上传开关
                      notShowError:YES
          completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        BOOL status = [[request.responseObject valueForKeyPath:@"data"] boolValue];
        if (status) {
            [MRKLogManager uploadLogFile];
        }
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        
    }];
}

/// 上传日志文件，先压缩成zip
+ (void)uploadLogFile{
    @weakify(self);
    void(^uploadFileBlock)(NSString *) = ^(NSString *path){
        @strongify(self);
        OSSUploadFileModel *model = [[OSSUploadFileModel alloc] initWithLocalPath:path
                                                                         fileName:[MRKLogManager uploadLogName]
                                                             businessResourceType:UploadBusinessResourceType_Log
                                                                         fileType:UploadFileType_Zip];
        [[OSSManager sharedManager] uploadFile:model success:^{
            [MRKLogManager deleteZipLog]; // 删除本地压缩文件
        } failure:^(NSString * _Nonnull errorStr) {
            [MRKLogManager deleteZipLog]; // 删除本地压缩文件
        }];
    };
    
    // 压缩log
    [MRKLogManager zipLog:^(NSString * _Nonnull path) {
        uploadFileBlock(path);
    }];
}


@end
