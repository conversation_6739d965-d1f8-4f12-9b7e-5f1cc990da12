//
//  BaseEquipData.h
//  Student_IOS
//
//  Created by MacPro on 2021/5/27.
//

#import <Foundation/Foundation.h>

#define kMinValue      1

NS_ASSUME_NONNULL_BEGIN

@interface BaseEquipData : NSObject
+ (int)convertData:(NSData *)data;
///有符号数据
+(int)convertSignalData:(NSData *)data ;

+ (int)realDistance:(int)distance;

+ (int)convertData:(NSData *)data minus:(int)value;

+ (NSArray *)arrayFromData:(NSData *)data;
@end

@interface BaseEquipDataModel : MRKBaseModel
@property (nonatomic, copy) NSString *csName;                  ///特征值
@property (nonatomic, copy) NSString *equipmentName;           ///设备名称
@property (nonatomic, copy) NSString *modelId;                 ///设备二级ID
@property (nonatomic, strong) NSNumber *type;                  ///类型.手动.大类
///
@property (nonatomic, strong) NSNumber *speed;                 ///速度
@property (nonatomic, strong) NSNumber *avgSpeed;              ///平均速度
@property (nonatomic, strong) NSNumber *totalDistance;         ///距离
@property (nonatomic, strong) NSNumber *spm;                   ///踏频/桨频
@property (nonatomic, strong) NSNumber *avgSpm;                ///平均踏频/桨频'
@property (nonatomic, strong) NSNumber *count;                 ///总踏数/总个数/总步数  跑步机总步数不一定准确(会出现200米返回步数2的情况)，建议不要使用 tzjdh
@property (nonatomic, strong) NSNumber *drag;                  ///阻力
///
@property (nonatomic, strong) NSNumber *power;
@property (nonatomic, strong) NSNumber *avgPower;
@property (nonatomic, strong) NSNumber *energy;
@property (nonatomic, strong) NSNumber *totalTimeSecond;       ///设备运行时间
///
@property (nonatomic, strong) NSNumber *rate;                  ///心率设备心率
@property (nonatomic, strong) NSNumber *rateKcal;              ///根据设备/心率带计算的消耗值
///
@property (nonatomic, strong) NSNumber *deviceRate;            ///运动设备心率
///
@property (nonatomic, strong) NSNumber *gradient;              ///坡度
@property (nonatomic, strong) NSNumber *grade;                 ///档位 [筋膜枪]
@property (nonatomic, strong) NSNumber *electric;              ///电量
@property (nonatomic, strong) NSNumber *remainingTimeSecond;   ///剩余时间
///
@property (nonatomic, strong) NSNumber *mode;                  ///模式
@property (nonatomic, strong) NSNumber *targetNumber;          ///倒计数模式 目标值
@property (nonatomic, strong) NSNumber *targetTime;            ///倒计时模式，目标时间
@property (nonatomic, strong) NSNumber *finishTarget;          ///完成目标标识位
@property (nonatomic, strong) NSNumber *status;                ///设备状态

///麦瑞克协议 设备按压信号 01代表有按压
@property (nonatomic , strong) NSNumber *press;
///麦瑞克协议 摇摆方向 目前适用于摇摆单车:00H：直线;01H：左; 02H：右
@property (nonatomic , strong) NSNumber *direction;
///游戏手柄 按键数据
@property (nonatomic , strong) NSNumber *keyNum;
///游戏手柄 按键状态 0x00:短按；0x01:长按
@property (nonatomic , strong) NSNumber *keyTime;

@property (nonatomic, strong) NSNumber *equipmentType;         ///设备类型 1:大件； 2:小件
@property (nonatomic, strong) NSNumber *deviceTimestamp;       ///设备数据上报时间戳

@property (nonatomic, strong) NSData *deviceOriginData;        ///设备上报的原始数据

///S28S 一体化手柄
///扳机序号，板机1，板机1......
@property (nonatomic , strong) NSNumber *trigValNumber;
///扳机值
@property (nonatomic , strong) NSNumber *trigValValue;
///震动档位
@property (nonatomic , strong) NSNumber *shakeLevel;

///气动力量站【2024-05-13】
///重量
@property (nonatomic, strong) NSNumber *weight;

///盟拓-力量站
/**
 设备报警信息
 02H:电机线圈温度高报警
 03H:电池电量低报警
 05H:电机失速保护报警
 06H:电机过载保护报警
 07H:电池低温报警
 08H:电池过温报警
 */
@property (nonatomic, strong) NSNumber *equiAlamInfo;
///左手拉伸值 次
@property (nonatomic, strong) NSNumber *leftStretchCount;
///右手拉伸值 次
@property (nonatomic, strong) NSNumber *rightStretchCount;
///电池温度值 °C
@property (nonatomic, strong) NSNumber *battTemp;
///电池电压值 V
@property (nonatomic, strong) NSNumber *battVolt;

//2024-12-17 壶铃新增错误计数
@property(nonatomic , strong) NSNumber *errorCount;

//小飞鸟力量站【2025-02-12】
///单次重量 kg
@property (nonatomic , strong) NSNumber *singleWeight;
///总重量 kg
@property (nonatomic , strong) NSNumber *totalWeight;
///拉动高度 mm
@property (nonatomic , strong) NSNumber *pullHeight;
///拉动频次  次/min
@property (nonatomic , strong) NSNumber *pullSpm;
///拉动速度 cm/s
@property (nonatomic , strong) NSNumber *pullSpeed;

+ (id)modelFromData:(NSData *)data;
+ (id)modelFromiConsleData:(NSData *)originData;
+ (BaseEquipDataModel *)modelFromData:(NSData *)data type:(NSString *)type;

//21-12-04 根据数据协议封装model
+ (BaseEquipDataModel *)modelFromData:(NSData *)data
                                 type:(NSString *)type
                         blueDataType:(NSNumber *)dataType
                       characteristic:(CBCharacteristic *)characteristic;

- (BOOL)isNotEmpty;//判断model 里的属性值 是否有-1的情况，如果有 作为无效数据处理 2021-12-10

- (id)updateNotNilValueFrom:(BaseEquipDataModel *)model;//从新的model获取非空数据

- (id)mergeWithModel:(BaseEquipDataModel *)newModel;
/// 热力图用
/// 整理每一秒所需要用到的热力数据  2022-4-19 wk  v2.7.0
/// 跑步机取速度，其他取阻力）
@property (nonatomic, strong) NSNumber *hotProgressValue;

/// 超燃脂自由训练热力图数据
@property (nonatomic, strong) NSNumber *ultraProgressValue;

@end

/*
@interface ZJCarDataModel : BaseEquipDataModel
@end


@interface ZJTreadmillDataModel : BaseEquipDataModel
@end


@interface ZJSkipRopeDataModel : BaseEquipDataModel
@end


@interface BQEquipmentDataModel :BaseEquipDataModel
@end
*/

@interface EquipmentTool : NSObject

///添加设备时 是否需要先选择类型
+ (BOOL)isNeedChooseType:(NSString *)type;

@end

NS_ASSUME_NONNULL_END
