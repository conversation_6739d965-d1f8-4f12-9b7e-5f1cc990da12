//
//  BaseEquipData.m
//  Student_IOS
//
//  Created by MacPro on 2021/5/27.
//

#import "BaseEquipData.h"
#import "FTMSData.h"
#import <objc/runtime.h>
#import "MRKBlueData.h"
#import "BaseModel.h"
#import "ZJData.h"
#import "BQData.h"
#import "HeartData.h"
#import "PowerZJData.h"
#import "SportShowData.h"

////智健协议 不同类型数据长度 2021-12-10
//#define kZJTreamillDataLength       17
//#define kZJCarDeviceDataLength      15
#define kDataNilNumber       -1000000

@implementation BaseEquipData
//有符号数据
+(int)convertSignalData:(NSData *)data {
    if (data.length == 0) {
        return kDataNilNumber;
    }
    
    NSData *temp = [NSObject dataTransfromBigOrSmall:data];
    return  [NSObject input0x16String:temp.hexString];
}

//无符号数据
+ (int)convertData:(NSData *)data {
    if (data.length == 0) {
        return kDataNilNumber;
    }
    
    int result = 0;
    [data getBytes:&result length:sizeof(int)];
    //    NSLog(@"result == %d " , result);
    return  result;
}

+ (int)convertData:(NSData *)data minus:(int)value{
    int result = 0;
    [data getBytes:&result length:sizeof(int)];
    result = result - value;
    return  result < 0 ? 0 : result;
}

//static func convertData(_ data: Data, minus value: Int) -> Int {
//    var result = 0
//    data.copyBytes(to: &result, count: MemoryLayout<Int>.size)
//    result -= value
//    return max(result, 0)
//}

+ (int)realDistance:(int)distance {
    int real_distance = 0;
    if ((distance & 0x8000) != 0x8000 ) {
        real_distance = distance;
    } else {
        //        real_distance = (distance & 0x7FFF);
        real_distance = (distance & 0x7FFF) * 10;//22-05-30 绍鑫说最高位是1 代表距离单位是10 需要乘以10
    }
    return real_distance;
}

+ (NSArray *)arrayFromData:(NSData *)data {
    @autoreleasepool {
        NSData *small = [NSObject dataTransfromBigOrSmall:data];
        NSString *hex = [small hexString];
        NSString *binary = [NSObject getBinaryByHex:hex];
        NSMutableArray *arr = [NSMutableArray array];
        for (int i = 0; i<binary.length; i++) {
            [arr addObject:[binary substringWithRange:NSMakeRange(i, 1)]];
        }
        NSArray *reversedArray = [[arr reverseObjectEnumerator] allObjects];
        return reversedArray;
    }
}

@end




@interface BaseEquipDataModel ()

@end


@implementation BaseEquipDataModel

+ (NSDictionary *)modelCustomPropertyMapper {
    return @{
        @"totalTimeSecond" : @"deviceTime",
        @"totalDistance" : @"distance" ,
    };
}

/// 整理每一秒所需要用到的热力数据  2022-4-19 wk  v2.7.0
/// 跑步机取速度，其他取阻力）
- (NSNumber *)hotProgressValue {
    NSNumber *value = @0;
    switch (self.type.intValue) {
        case BicycleEquipment:
        case BoatEquipment:
        case EllipticalEquipment:
        case PowerEquipment:
            value = self.drag.copy;
            break;
        case TreadmillEquipment:
            value = self.speed.copy;
            break;
        default:
            break;
    }
    return value;
}

/// 超燃脂自由训练数据
/// 跑步机取速度，其他取踏频/桨频）
- (NSNumber *)ultraProgressValue {
        NSNumber *value = @0;
        switch (self.type.intValue) {
            case BicycleEquipment:
            case BoatEquipment:
            case EllipticalEquipment:
            case PowerEquipment:
                value = self.spm.copy;
                break;
            case TreadmillEquipment:
                value = self.speed.copy;
                break;
            default:
                break;
        }
        return value;
}

- (BOOL)isNotEmpty {
    if (self == nil) {
        return NO;
    }
    
    BOOL res = YES;
    unsigned int count = 0;
    objc_property_t *properties = class_copyPropertyList([self class], &count);
    for (int i = 0; i < count; i++) {
        const char *name = property_getName(properties[i]);
        NSString *propertyName = [NSString stringWithUTF8String:name];
        id propertyValue = [self valueForKey:propertyName];
        if ([propertyValue respondsToSelector:@selector(intValue)] && [propertyValue intValue] == kDataNilNumber) {
            res = NO;
            break;
        }
    }
    free(properties);
    return res;
}

- (id)updateNotNilValueFrom:(BaseEquipDataModel *)model {
    NSArray *array = [model allPropertyNames] ;
    for (NSString *key in array) {
        id value = [model valueForKey:key];
        if ([value isNotEmpty]) {
            [self setValue:value forKey:key];
        }
    }
    return self;
}


- (id)mergeWithModel:(BaseEquipDataModel *)newModel{
    unsigned int propertyCount;
    objc_property_t *properties = class_copyPropertyList([self class], &propertyCount);
    for (unsigned int i = 0; i < propertyCount; i++) {
        objc_property_t property = properties[i];
        const char *name = property_getName(property);
        NSString *key = [NSString stringWithUTF8String:name];

        // 使用 KVC 获取值
        id newValue = [newModel valueForKey:key];
        if ([newValue isNotEmpty]) {
            [self setValue:newValue forKey:key];
        }
    }
    free(properties);
    return self;
}



+ (id)modelFromData:(NSData *)data {
    return [BaseEquipDataModel new];
}

+ (id)modelFromiConsleData:(NSData *)originData {
    return [BaseEquipDataModel new];
}

+ (BaseEquipDataModel *)modelFromData:(NSData *)data type:(NSString *)type {
    return [BaseEquipDataModel new];
}

#pragma mark ----------------------------------------新的model方法开始--------------------------------------
+ (BaseEquipDataModel *)modelFromData:(NSData *)data type:(NSString *)type blueDataType:(NSNumber *)dataType characteristic:(CBCharacteristic *)characteristic{
    switch (dataType.intValue) {
        case FTMSCommunicationProtocol:
            return [BaseEquipDataModel modelFTMSFromData:data type:type];
            break;
        case ZJCommunicationProtocol:
            return [BaseEquipDataModel modelZJFromData:data type:type];
            break;
        case BQCommunicationProtocol:
            return [BaseEquipDataModel modelBQFromData:data type:type];
            break;
        case MRKCommunicationProtocol:
        {
            BaseEquipDataModel *model ;
            model = [MRKBlueData modelFromData:data type:type];
            if (type.intValue == SkipRopeEquipment) {
                model.speed = model.avgSpeed;
            }
            if (type.intValue == FLSBEquipment) {
                //飞力士棒没有速率字段，自己计算 22-05-12
                model.speed = @(model.totalTimeSecond.intValue == 0 ? 0 : model.count.intValue * 1.0 / model.totalTimeSecond.intValue * 60);
                BlueNSLog(@"model.speed==%@" , model.speed);
            }
            return model;
        }
            break;
        case JMQCommunicationProtocol:
        {
            BaseEquipDataModel *model = [JMQData modelFromData:data type:type];
            /*
            [BaseEquipDataModel new];
            if (type.intValue == JinMoQiangEquipment) {
                model.grade =  @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(4, 1)]]);
                model.electric =  @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(3, 1)]]);
            }
            model.type = @(JinMoQiangEquipment);
             */
            return model;
        }
            break;
            
        case HBCommunicationProtocol:
        case HTKCommunicationProtocol : {
            BaseEquipDataModel *model;
            if (type.intValue == HeartEquipment) {
                //心率带
                model = [HeartData modelFromData:data type:type ch:characteristic];
                /*
                [BaseEquipDataModel new];
                if ([characteristic.UUID.UUIDString isEqualToString:kHeartDataCharacteristics]) {
                    NSLog(@"HeartEquipment__%@" , data);
                    int flag = [BaseEquipData convertData:[data subdataWithRange:NSMakeRange(0, 1)]];
                    NSLog(@"rate_flag == %@" , @(flag));
                    if (( flag & 0x01) == 0) {
                        
                        model.rate = @([BaseEquipData convertData: [data subdataWithRange:NSMakeRange(1, 1)]]);
                    } else {
                        model.rate = @([BaseEquipData convertData: [data subdataWithRange:NSMakeRange(1, 2)]]);
                    }
                    NSLog(@"heart_rate --- %@" , model.rate);
                }
                if ([characteristic.UUID.UUIDString isEqualToString:kHeartBatteryCharacteristics]) {
                    NSLog(@"heart_battery --- %@" , @([BaseEquipData convertData: data]));
                    model.electric = @([BaseEquipData convertData: data]);
                }
                model.type = @(HeartEquipment);
                 */
            }
            return model;
        }
            break;
        case PowerCommunicationProtocol:
        {
            BaseEquipDataModel *model = [PowerZJData modelFromData:data type:type];
            return  model;
        }
            break;
        case SportShowCommunicationProtocol:
        {
            BaseEquipDataModel *model = [SportShowData modelFromData:data type:type];
            //【2024-11-05】方便业务需求，把所有设备状态按照跑步机状态包装
            if (model.status.intValue == 2) {
                model.status = @(DeviceRuningStatus);
            } else if (model.status.intValue == 0) {
                model.status = @(DeviceStandbyStatus);
            } else if (model.status.intValue == 3) {
                model.status = @(DevicePauseStatus);
            }
            return  model;
        }
            
        default:
        {
            BaseEquipDataModel *model;
            if (type.intValue == HeartEquipment) {
                //心率带
                model = [HeartData modelFromData:data type:type ch:characteristic];
                model.type = @(HeartEquipment);
            }
            return model;
        }
            break;
    }
}

+ (BaseEquipDataModel *)modelFTMSFromData:(NSData *)data type:(NSString *)type  {
    NSData *flags = [data subdataWithRange:NSMakeRange(0, 2)];
    BaseEquipDataModel *model = [FTMSData modelFromData:data type:type];
    model.type = @(type.intValue);
    switch (type.intValue) {
        case BicycleEquipment:
        {
            NSString *localName = [BluePeripheral connectDeviceNameOfType:type];
            if ([flags isEqualToData:[BloothTool bicycleFE0Blags]]
                && [localName containsString:@"Merach-MR636"] //华为 636 单车 踏频 算法/2 21-12-09
                && (![localName containsString:@"Merach-MR636D"]) ) {//22-10-24 636D需求
                model.spm = @(model.spm.intValue / 2);
                model.avgSpm =  @(model.avgSpm.intValue / 2);//算法 / 2
            }
            if ([BloothTool is667Bicycle:type]) {//667 消耗根据距离计算
                //                model.energy = @(model.totalDistance.longValue * 0.025); //2022-01-18 667 取设备消耗显示
            }
            //22-04-15 2.7.0历史数据会上传，后端可根据数据计算平均踏频，所以不再计算踏数
            //22-05-27 雯雯说把总踏数放出来 2.7.2版本
            //23-12-13 MR667 2.0.5固件分包发送，有一包数据没有距离字段，所以增加一层判断
            if (model.totalDistance){
                model.count = @((int)floor([model.totalDistance floatValue] / 4.6));//总踏数
            }
        }
            break;
        case BoatEquipment:
        {
            //划船机取spm;
            model.speed = model.spm;
        }
            break;
            
        case EllipticalEquipment:
        {
            //为了与华为单位统一，单位：圈数/min ；1圈 = 2步 ， 故 算法 /2 ;21-12-28
            model.spm = @(model.spm.intValue / 2);//算法 / 2
            model.avgSpm = @(model.avgSpm.intValue / 2);//算法 / 2
        }
            break;
        default:
            break;
    }
    
    //    NSLog(@"modelFTMSFromData___%@" , model);
    return model;
}

+ (BaseEquipDataModel *)modelZJFromData:(NSData *)data type:(NSString *)type  {
    BaseEquipDataModel *model = [ZJData modelFromData:data type:type];
    //【2024-11-05】方便业务需求，把所有设备状态按照跑步机状态包装
    if(type.intValue == BicycleEquipment
       || type.intValue == EllipticalEquipment
       || type.intValue == StairClimbEquipment
       || type.intValue == BoatEquipment) {
        
        if (model.status.intValue == 2) {
            model.status = @(DeviceRuningStatus);
        } else if (model.status.intValue == 0) {
            model.status = @(DeviceStandbyStatus);
        } else if (model.status.intValue == 3) {
            model.status = @(DevicePauseStatus);
        }
    }
    
    return model;
}

+ (BaseEquipDataModel *)modelBQFromData:(NSData *)data type:(NSString *)type  {
    BaseEquipDataModel *model = [BQData modelFromData:data type:type];
    return model;
}

#pragma mark ----------------------------------------新的model方法结束--------------------------------------
/*
 + (BaseEquipDataModel *)modelFromData:(NSData *)data type:(NSString *)type {
 NSData *flags = [data subdataWithRange:NSMakeRange(0, 2)];
 BaseEquipDataModel *model = [BaseEquipDataModel new];
 model.type = @(type.intValue);
 switch (type.intValue) {
 case BoatEquipment:
 {
 BaseEquipDataModel *ftms = [FTMSData modelFromData:data type:type];
 model = ftms;
 //划船机取spm;
 model.speed = model.spm;
 NSLog(@"ftms --- %@" , ftms);
 NSLog(@"model -- %@" , model);
 
 return model;
 }
 break;
 case EllipticalEquipment:
 {
 BaseEquipDataModel *ftms = [FTMSData modelFromData:data type:type];
 model = ftms;
 model.spm = @(model.spm.intValue / 2);//算法 / 2
 
 NSLog(@"ftms --- %@" , ftms);
 NSLog(@"model -- %@" , model);
 
 return model;
 }
 break;
 case JinMoQiangEquipment:
 {
 model.grade =  @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(4, 1)]]);
 model.electric =  @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(3, 1)]]);
 return model;
 }
 
 break;
 case BicycleEquipment:
 {
 
 BaseEquipDataModel *ftms = [FTMSData modelFromData:data type:type];
 NSString *localName = [BluePeripheral connectDeviceNameOfType:type];
 model = ftms;
 if ([flags isEqualToData:[BloothTool bicycleFE0Blags]] && [localName containsString:@"Merach-MR636"]) {//华为 636 单车 踏频 算法/2 21-12-09
 model.spm = @(ftms.spm.intValue / 2);
 
 }
 model.count = @((int)floor([model.totalDistance floatValue] / 4.6));//总踏数
 
 
 return  model;
 
 }
 break;
 
 case SkipRopeEquipment:
 {
 model.count = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(3, 2)]]);
 model.totalTimeSecond = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(5, 2)]]);
 model.targetNumber = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(8, 2)]]);
 model.targetTime = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(10, 2)]]);
 model.mode = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(7, 1)]]);
 model.finishTarget = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(12, 1)]]);
 model.speed = @(model.totalTimeSecond.intValue == 0 ? 0 : model.count.intValue * 1.0 / model.totalTimeSecond.intValue * 60);
 model.type = @(SkipRopeEquipment);
 
 return model;
 }
 break;
 
 case HoopEquipment:
 {
 model = [MRKBlueData modelFromData:data type:type];
 model.speed = @(model.totalTimeSecond.intValue == 0 ? 0 : model.count.intValue * 1.0 / model.totalTimeSecond.intValue * 60);
 return model;
 }
 break;
 
 
 default:
 break;
 }
 
 return [BaseEquipDataModel new];
 }
 */
- (NSString *)description {
    if ([self isKindOfClass:[NSArray class]] ||
        [self isKindOfClass:[NSDictionary class]] ||
        [self isKindOfClass:[NSNumber class]] ||
        [self isKindOfClass:[NSString class]]) {
        
        return self.debugDescription;
    }
    //初始化一个字典
    NSMutableDictionary *dictionary = [NSMutableDictionary dictionary];
    //得到当前class的所有属性
    uint count;
    objc_property_t *properties = class_copyPropertyList([self class], &count);
    for (int i = 0; i<count; i++) { //循环并用KVC得到每个属性的值
        objc_property_t property = properties[i];
        NSString *name = @(property_getName(property));
        id value = [self valueForKey:name]?:@"nil";//默认值为nil字符串
        [dictionary setObject:value forKey:name];//装载到字典里
    }
    //释放
    free(properties);
    //return
    return [NSString stringWithFormat:@"<%@: %p> -- %@",[self class],self,dictionary];
}

@end




/*

#pragma mark - 智健
@implementation ZJCarDataModel

+(id)modelFromData:(NSData *)data {
    
    BaseEquipDataModel *m ;
    if ([BloothTool isSamePrefix:data other:[BloothTool getDeviceStatusData] start:0 offset:2] && data.length == kZJCarDeviceDataLength) {
        //15个字节
        m = [BaseEquipDataModel new];
        m.avgSpeed = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(3, 2)]] * 0.01);
        m.drag = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(5, 1)]]);
        m.spm = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(6, 2)]]);
        m.deviceRate = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(8, 1)]]);
        BlueNSLog(@"power === %d" , [BaseEquipData convertData:[data subdataWithRange:NSMakeRange(9, 2)]]);
        m.power = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(9, 2)]] * 0.1);
        m.gradient = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(11, 1)]]);
        
    } else if([BloothTool isSamePrefix:data other:[BloothTool getSportData] start:0 offset:2] && data.length == kZJCarSportDataLength) {
        //13个字节
        m = [BaseEquipDataModel new];
        m.totalTimeSecond = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(3, 2)]]);
        int distance  = [BaseEquipData convertData:[data subdataWithRange:NSMakeRange(5, 2)]];
        int real_distance = [BaseEquipData realDistance:distance];
        
        BlueNSLog(@"real_distance ---- %d" , real_distance);
        m.totalDistance = @(real_distance);
        m.energy = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(7, 2)]] * 0.1);
        BlueNSLog(@"enery---%@" , m.energy);
        m.count = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(9, 2)]]);
    }
    return m;
}



@end




@implementation ZJTreadmillDataModel
//x1
+ (id)modelFromData:(NSData *)data {
    BaseEquipDataModel *m;
    if ([BloothTool isSamePrefix:data other:[BloothTool treadmillData] start:0 offset:2] && data.length == kZJTreamillDataLength) {
        m = [BaseEquipDataModel new];
        m.speed = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(3, 1)]] / 10.0);
        BlueNSLog(@"speed === %@" , m.speed);
        m.gradient = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(4, 1)]]);
        m.totalTimeSecond = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(5, 2)]]);
        
        int distance  = [BaseEquipData convertData:[data subdataWithRange:NSMakeRange(7, 2)]];
        int real_distance = [BaseEquipData realDistance:distance];
        m.totalDistance = @(real_distance);
        m.energy = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(9, 2)]] / 10.0);
        
        m.count = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(11, 2)]]);
        m.deviceRate = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(13, 1)]]);
    }
    return m;
}

@end


@implementation ZJSkipRopeDataModel

+ (BaseEquipDataModel *)modelFromData:(NSData *)data {
    BaseEquipDataModel *model;
    
    if ([BloothTool isSamePrefix:data other:[BloothTool jumpElectric] start:1 offset:2]) {
        //电量
        model = [BaseEquipDataModel new];
        model.electric = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(3, 1)]]);
    } else if([BloothTool isSamePrefix:data other:[BloothTool passCountAndTimeOrder] start:1 offset:2]){
        long len = [BaseEquipData convertData:[data subdataWithRange:NSMakeRange(0, 1)]];
        if (data.length < len) {//判断跳绳长度是否符合协议 21-12-13
            return model;
        }
        model = [BaseEquipDataModel new];
        //其他数据
        model.count = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(3, 2)]]);
        model.totalTimeSecond = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(5, 2)]]);
        model.targetNumber = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(8, 2)]]);
        model.targetTime = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(10, 2)]]);
        model.mode = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(7, 1)]]);
        model.finishTarget = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(12, 1)]]);
        model.speed = @(model.totalTimeSecond.intValue == 0 ? 0 : model.count.intValue * 1.0 / model.totalTimeSecond.intValue * 60);
        
    }
    model.type = @(SkipRopeEquipment);
    return model;
}

@end


@implementation BQEquipmentDataModel

+ (id)modelFromiConsleData:(NSData *)originData {
    
    BaseEquipDataModel *m = [BaseEquipDataModel new];
    
    NSData * data = originData;
    NSData *typeData = [data subdataWithRange:NSMakeRange(3, 1)];
    NSInteger type = [BaseEquipData convertData:typeData];
    switch (type) {
        case kICONSOLEBOAT:
        {
            
            m.totalTimeSecond = @(([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(4, 1)] minus:kMinValue]) * 60 + ([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(5, kMinValue)] minus:kMinValue]));
            m.count = @(([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(6, 1)] minus:kMinValue]) * 100 + ([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(7, 1)] minus:kMinValue]));
            m.spm = @(([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(8, 1)] minus:kMinValue]) * 100 + ([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(9, 1)] minus:kMinValue]));
            
            m.totalDistance = @((([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(10, 1)] minus:kMinValue]) * 100 + ([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(11, 1)] minus:kMinValue])));
            m.energy = @(([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(12, 1)] minus:kMinValue]) * 100 + ([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(13, 1)] minus:kMinValue]));
            m.deviceRate =@(([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(14, 1)] minus:kMinValue]) * 100 + ([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(15, 1)] minus:kMinValue]));
            m.power = @(([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(16, 1)]minus:kMinValue]) * 10 + ([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(17, 1)] minus:kMinValue])/ 10.0);
            
            m.drag = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(20, 1)] minus:kMinValue]);
        }
            break;
        case kICONSOLECIRCLE:
        {
            
            m.totalTimeSecond = @(([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(4, 1)] minus:kMinValue]) * 60 + ([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(5, kMinValue)] minus:kMinValue]));
            m.speed = @(([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(6, 1)] minus:kMinValue]) * 10 + ([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(7, 1)] minus:kMinValue]) / 10.0);
            m.spm = @(([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(8, 1)] minus:kMinValue]) * 100 + ([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(9, 1)] minus:kMinValue] ) );
            
            m.totalDistance = @((([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(10, 1)] minus:kMinValue]) * 10 + ([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(11, 1)] minus:kMinValue]) / 10.0) * 1000);
            m.energy = @(([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(12, 1)] minus:kMinValue]) * 100 + ([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(13, 1)] minus:kMinValue]));
            m.deviceRate =@(([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(14, 1)] minus:kMinValue]) * 100 + ([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(15, 1)] minus:kMinValue]));
            m.power = @(([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(16, 1)]minus:kMinValue]) * 10 + ([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(17, 1)] minus:kMinValue])/ 10.0);
            m.drag = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(18, 1)] minus:kMinValue]);
            
            m.status = @([BaseEquipData convertData:[data subdataWithRange:NSMakeRange(19, 1)] minus:kMinValue]);
            BlueNSLog(@"kICONSOLECIRCLE__status==%@" , m.status);
        }
            break;
            
        default:
            break;
    }
    
    BlueNSLog(@"gggggg---spm=%@,count=%@ , energy = %@,distance = %@,drag  = %@" , m.spm , m.count,m.energy , m.totalDistance , m.drag);
    
    
    return m;
}

@end

*/


@implementation EquipmentTool
+ (NSArray *)needChooseTypes {
    return  @[
        @(FatScaleEquipment),
        @(HeartEquipment),
    ];
}
///添加设备时 是否需要先选择类型
+ (BOOL)isNeedChooseType:(NSString *)type {
    return [[EquipmentTool needChooseTypes] containsObject:@(type.intValue)];
}
@end
