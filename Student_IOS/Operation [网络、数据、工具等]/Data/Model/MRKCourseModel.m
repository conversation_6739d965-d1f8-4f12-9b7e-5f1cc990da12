//
//  MRKCourseModel.m
//  Student_IOS
//
//  Created by merit on 2021/6/18.
//

#import "MRKCourseModel.h"

@implementation MRKCourseModel

+ (MRKCourseModel *)modelFromLiveModel:(LiveModel *)model{
    MRKCourseModel *m = [MRKCourseModel new];
    m.courseId = model.idd; ///待询问
    m.name = model.title;
    m.courseName = model.title;
    m.videosList = [NSArray modelArrayWithClass:[VideoListModel class] json:model.videoInfo];
    m.equipmentId = model.equipTypeId;
    m.equipmentName = model.equipName;
    m.courseTime = model.videoDuration;
    m.courseStatus = @"60";
    m.videoType = MrkVideoTypeVOD;
    m.cover = model.image;
    m.type = @"6";
    m.isRealVideoCourse = YES;
    m.isSupportConnection = YES;  ///支持连接设备
    m.vipType = @(model.vipType.intValue);
    m.isVipCourse = model.vipType.intValue > 0;
    m.musicUrl = model.musicUrl;
    return m;
}

+ (NSDictionary *)modelCustomPropertyMapper {
    return @{@"videosList" : @"videos",
             @"isVipCourse" : @"isVip",
             @"canPlayCourse" : @"isPlay",
             @"courseName" : @[@"name",@"courseName"],
             @"courseId" : @[@"id",@"courseId"]};
}

+ (NSDictionary *)modelContainerPropertyGenericClass {
    return @{@"videosList" : VideoListModel.class,
             @"coachPO" : MRKCoachModel.class,
             @"courseActivityPO" : ActivityEquipmentModel.class
    };
}


- (BOOL)modelCustomTransformFromDictionary:(NSDictionary *)dic {
    NSInteger status = _courseStatus.integerValue;
    switch (status) {
        case 30:
            _videoType = MrkVideoTypeLivePrep;
            break;
        case 35: case 40:
            _videoType = MrkVideoTypeLive;
            break;
        case 50:
            _videoType = MrkVideoTypeLiveEnd;
            break;
        case 60:
            _videoType = MrkVideoTypeVOD;
            break;
        default: break;
    }
    
    ///
    if ([_name isNotBlank] && _courseName == nil) {
        _courseName = _name;
    }
    
    ///
    _isMeritCourse = [_type isEqualToString:@"2"] || [_type isEqualToString:@"4"];
    
    return YES;
}


- (NSString *)description{
    return [NSString stringWithFormat:@"courseStatus:%@, isMake:%@, liveTime:%@",self.courseStatus, self.isMake?@"YES":@"NO", self.liveTime];
}

/// 是否显示热力图
///（四大件，超燃脂课程 时才显示）
- (BOOL)isShowHotProgress {
    return @(self.isMeritCourse && self.isFourBigDevice).boolValue;
}

///（四大件）
- (BOOL)isFourBigDevice {
    BOOL show = NO;
    switch (self.equipmentId.intValue) {
        case BicycleEquipment:
        case TreadmillEquipment:
        case BoatEquipment:
        case EllipticalEquipment:
            show = YES;
            break;
        default:
            break;
    }
    return show;
}

- (BOOL)isShowRankControl {
    return @(!self.isRealVideoCourse && self.isFourBigDevice).boolValue;
}

@end





@implementation ActivityEquipmentModel
@end
