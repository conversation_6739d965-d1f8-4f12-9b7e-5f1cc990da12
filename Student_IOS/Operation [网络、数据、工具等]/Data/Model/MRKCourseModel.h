//
//  MRKCourseModel.h
//  Student_IOS
//
//  Created by merit on 2021/6/18.
//

#import <Foundation/Foundation.h>
#import "LiveModel.h"
#import "MRKCoachModel.h"
#import "VideoListModel.h"


NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, MrkVideoType)  {
    MrkVideoTypeNormal,
    MrkVideoTypeLivePrep,   ///直播准备中
    MrkVideoTypeLive,       ///直播中
    MrkVideoTypeLiveEnd,    ///直播完成(转码中)
    MrkVideoTypeVOD,        ///转码完成   (回放,录播 ⚠️⚠️⚠️⚠️⚠️⚠️实景也会是这个状态 ) 点播
    MrkVideoTypeDefault = MrkVideoTypeNormal
};


@class ActivityEquipmentModel;

@interface MRKCourseModel : NSObject
+ (MRKCourseModel *)modelFromLiveModel:(LiveModel *)model;

@property (nonatomic, strong) NSArray<VideoListModel*> *videosList;          ///课程播放视频列表
@property (nonatomic, strong) MRKCoachModel *coachPO;                        ///教练信息
@property (nonatomic, strong) ActivityEquipmentModel *courseActivityPO;      ///视频活动配置信息
@property (nonatomic, strong) NSNumber *channel;                             ///⭐️⭐️⭐️课程渠道[课程渠道：1-直播，2-录播录入,3.超燃脂自由练]
@property (nonatomic, copy) NSString *courseStatus;                          ///课程状态：30-等待直播，[35-提前进入，40-直播中]，50-直播结束，等待视频转码，60-转码完成
@property (nonatomic, assign) MrkVideoType videoType;                        ///课程状态
@property (nonatomic, assign) NSTimeInterval liveTimestamp;                  ///开播时间戳
@property (nonatomic, copy) NSString *coachName;                             ///教练名称
@property (nonatomic, copy) NSString *coachId;                               ///教练Id
@property (nonatomic, copy) NSString *makeLiveTime;
@property (nonatomic, copy) NSString *courseId;                              ///课程id
@property (nonatomic, copy) NSString *name;                                  ///课程名称
@property (nonatomic, copy) NSString *courseName;                            ///课程名称
@property (nonatomic, copy) NSString *courseTime;                            ///课程时长：分钟
@property (nonatomic, copy) NSString *introduce;                             ///课程介绍
@property (nonatomic, copy) NSString *time;                                  ///直播时间/上次训练时间
@property (nonatomic, copy) NSString *liveTime;                              ///直播时间[]
@property (nonatomic, copy) NSString *cover;                                 ///图片
@property (nonatomic, strong) NSNumber *videoMediaType;                      ///视频类型, 1,横版 , 2 竖版
@property (nonatomic, strong) NSNumber *equipmentType;                       ///为2的时候是小件
@property (nonatomic, copy) NSString *equipmentName;                         ///设备名称
@property (nonatomic, copy) NSString *equipmentId;                           ///设备大类Id
@property (nonatomic, copy) NSString *gradeDesc;                             ///课程难度
@property (nonatomic, copy) NSString *icon;
@property (nonatomic, copy) NSString *type;                                  ///课程类型：1-普通课程，2-Merit课程 ，3-大乱斗课程,  4-剧情课程
@property (nonatomic, copy) NSString *kcal;
@property (nonatomic, assign) BOOL isMeritCourse;                            ///是否Merit课程
@property (nonatomic, assign) BOOL isVipCourse;                              ///是否VIP课程
@property (nonatomic, strong) NSNumber *vipType;                             ///课程VIP类型
@property (nonatomic, assign) BOOL isCollect;                                ///是否收藏：0-否，1-是
@property (nonatomic, assign) BOOL isMake;                                   ///是否预约：0-否，1-是
@property (nonatomic, assign) BOOL isFree;                                   ///是否免费
@property (nonatomic, assign) BOOL isRealVideoCourse;                        ///是否实景
@property (nonatomic, assign) BOOL canPlayCourse;                            ///视频能否播放,///目前只在直播使用[后端控制]
@property (nonatomic, assign) BOOL isSupportConnection;                      ///是否支持连接设备
///
@property (nonatomic, copy) NSString *tagIcon;                               ///图片标签
@property (nonatomic, copy) NSString *tagTitle;                              ///状态标签
@property (nonatomic, copy) NSString *levelDesc;                             ///
@property (nonatomic, copy) NSString *takeTime;
@property (nonatomic, copy) NSString *liveIcon;                              ///直播课程需要用到的运动图标  2022-3-3 wk
@property (nonatomic, assign) BOOL isShowHotProgress;                        ///是否显示热力图进度条。2022-4-19 wk 2.7.0  判断直播间热力图是否显示
@property (nonatomic, assign) BOOL isShowRankControl;                        ///是否显示排行榜
@property (nonatomic, assign) BOOL isFourBigDevice;                          ///四大件
///
@property (nonatomic, strong) NSMutableArray<NSString*> *raceTargetTips;
@property (nonatomic, assign) NSInteger raceTarget;                          ///目标值
@property (nonatomic, assign) NSInteger raceStatus;
@property (nonatomic, copy) NSString *raceStartTime;
@property (nonatomic, assign) NSInteger trainNum;                            ///训练总人数
@property (nonatomic, assign) NSTimeInterval trainTime;                      ///上次训练时长，单位：秒。为 0 或者为空值则表示未训练过*
///22-11-16 为你推荐新增字段 zqp
@property (nonatomic, copy) NSString *tagName;                               ///标签name
@property (nonatomic, copy) NSString *equipmentTagIcon;                      ///
/// 2023-01-31 --wk
@property (nonatomic, strong) NSNumber *courseChannel;                       ///课程渠道  3超燃脂自由训练课程
@property (nonatomic, copy) NSString *lastTrainTime;                         ///练过的时间
@property (nonatomic, copy) NSString *trainCount;                            ///练过的次数
/// 2023-08-02 --junq [今日日程加]
@property (nonatomic, assign) BOOL unlock;                                   ///是否解锁        
@property (nonatomic, assign) BOOL training;                                 ///是否训练
@property (nonatomic, strong) NSNumber *userScheduleChannel;                 ///日程渠道：1-直播课，2-计划
@property (nonatomic, copy) NSString *trainId;                               ///训练ID''
@property (nonatomic, copy) NSString *musicUrl;                              ///音乐播放路径  实景添加
@property (nonatomic, copy) NSString *previewVideo;                          ///课程预览地址
@end




@interface ActivityEquipmentModel : BaseModel
@property (nonatomic, strong) NSNumber *isHeartRate;             ///是否需要心率带
@property (nonatomic, strong) NSNumber *isActivity;              ///是否在活动中：0-否，1-是
@property (nonatomic, strong) NSNumber *isPlan;                  ///是否在计划中：0-否，1-是
@property (nonatomic, strong) NSNumber *isBindRate;              ///是否绑定心率带：0-否，1-是
@property (nonatomic, strong) NSNumber *isBindEquipment;         ///是否绑定设备：0-否，1-是
@property (nonatomic, copy) NSString *activityId;                ///活动ID
@property (nonatomic, copy) NSString *activityName;              ///活动名称
@property (nonatomic, copy) NSString *planId;                    ///计划ID
@property (nonatomic, copy) NSString *planUserId;                ///用户计划ID
@property (nonatomic, copy) NSString *planName;                  ///计划名称
@property (nonatomic, copy) NSArray *models;                     ///活动支持的设备类型
///计算得分用
@property (nonatomic, strong) NSNumber *burnCalories;            ///录播卡路里消耗
@property (nonatomic, strong) NSNumber *caloriesScore;           ///录播卡路里消耗兑换得分
@property (nonatomic, strong) NSNumber *liveBurnCalories;        ///直播课卡路里消耗
@property (nonatomic, strong) NSNumber *liveCaloriesScore;       ///直播课卡路里消耗兑换得分
@property (nonatomic, strong) NSNumber *bonusPoints;             ///直播课程完成2/3额外得分
@end
NS_ASSUME_NONNULL_END
