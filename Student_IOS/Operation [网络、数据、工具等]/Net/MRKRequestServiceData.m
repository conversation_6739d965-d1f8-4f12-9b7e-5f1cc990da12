//
//  MRKRequestServiceData.m
//  Student_IOS
//
//  Created by MacPro on 2021/6/23.
//

#import "MRKRequestServiceData.h"
#import "MRKUserInfoRequest.h"
#import "MRKNetworkCache.h"

@implementation MRKRequestServiceData

+ (void)unbindUserNumber:(NSDictionary *)parameters success:(successData)success failure:(failedData)failure {
    [MRKBaseRequest mrkGetRequestUrl:@"/user/unbind"
                             andParm:parameters
            completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"unbind success -- %@" , request.responseObject);
        id data = [request.responseObject valueForKeyPath:@"data"];
        success(data);
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"unbind failure -- %@" , request.error) ;
        failure(request.error);
    }];
}

+ (void)updateEquipmentInfo:(NSDictionary *)parameters success:(successData)success failure:(failedData)failure {
    //updateType 修改类型，1.连接;2.解绑
    NSLog(@"updateEquipmentInfo_parameters -- %@" , parameters);
    [MRKBaseRequest mrkRequestType:YTKRequestMethodPUT
                               url:@"/equip/equipment/equipmentInfoController/equipment"
                           andParm:parameters
                      notShowError:YES
          completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"updateEquipmentInfo_parameters -- %@" , request.responseObject);
        id data = [request.responseObject valueForKeyPath:@"data"];
        success(data);
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"updateEquipmentInfo_parameters -- %@" , request.error) ;
        failure(request.error);
    }];
}

+ (void)bindBlueConnect:(NSDictionary *)parameters success:(successData)success failure:(failedData)failure {
    [MRKBaseRequest mrkPostRequestUrl:@"/equip/equipment/equipmentInfoController/equipment/v2"
                             andParm:parameters
            completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"bindBlueConnect_response -- %@" , request.responseObject);
        id data = [request.responseObject valueForKeyPath:@"data"];
        success(data);
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"bindBlueConnect_response -- %@" , request.error) ;
        failure(request.error);
    }];
}


+ (void)requestDeviceOTAInfo:(NSDictionary *)parameters success:(successData)success failure:(failedData)failure  {
    [MRKBaseRequest mrkGetRequestUrl:@"/equip/firmwareVersionController/FirmwareVersionLast"
                             andParm:parameters
            completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"requestDeviceOTAInfo_response -- %@" , request.responseObject);
        id data = [request.responseObject valueForKeyPath:@"data"];
        success(data);
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"requestDeviceOTAInfo_response -- %@" , request.error) ;
        failure(request.error);
    }];
}

+(void)requestLiveVideoList:(NSDictionary *)parameters success:(successData)success failure:(failedData)failure   {
    [MRKBaseRequest mrkGetRequestUrl:@"/course/LiveVideoController/getLiveVideoList"
                             andParm:parameters
            completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"requestLiveVideoList_response -- %@" , request.responseObject);
        id data = [request.responseObject valueForKeyPath:@"data"];
        success(data);
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"requestLiveVideoList_response -- %@" , request.error) ;
        failure(request.error);
    }];
}

+ (void)requestEquipmentTypeList:(NSDictionary *)parameters success:(successData)success failure:(failedData)failure   {
    [MRKBaseRequest mrkGetRequestUrl:@"/equip/equipment/equipmentTypeController/getAssociateList"
                             andParm:parameters
            completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"requestEquipmentTypeList_response -- %@" , request.responseObject);
        id data = [request.responseObject valueForKeyPath:@"data"];
        success(data);
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"requestEquipmentTypeList_response -- %@" , request.error) ;
        failure(request.error);
    }];
}

+ (void)uploadLocation:(NSDictionary *)parameters success:(successData)success failure:(failedData)failure   {
    [MRKBaseRequest mrkSilencePostRequestUrl:@"/user/signIn/v1/log"
                             andParm:parameters
            completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"uploadLocation_response -- %@" , request.responseObject);
        id data = [request.responseObject valueForKeyPath:@"data"];
        success(data);
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"uploadLocation_response -- %@" , request.error) ;
        failure(request.error);
    }];
}


+ (void)unReadMessage:(NSDictionary *)parameters success:(successData)success failure:(failedData)failure   {
    [MRKBaseRequest mrkGetRequestUrl:@"/message/message/userMessageAssociated/getUnReadNum"
                             andParm:parameters
            completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"unReadMessage_response -- %@" , request.responseObject);
        id data = [request.responseObject valueForKeyPath:@"data"];
        success(data);
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"unReadMessage_response -- %@" , request.error) ;
        failure(request.error);
    }];
}

+ (void)getUserInfo:(NSDictionary *)parameters success:(successData)success failure:(failedData)failure   {
    MRKUserInfoRequest *infoRequest = [[MRKUserInfoRequest alloc] init];
    [infoRequest startWithCompletionBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        id data = [request.responseObject valueForKeyPath:@"data"];
        success(data);
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSError *error = request.error;
        failure(error);
    }];
}


+ (void)deleteTrainRecord:(NSDictionary *)parameters success:(successData)success failure:(failedData)failure   {
    [MRKBaseRequest mrkRequestType:YTKRequestMethodDELETE
                               url:@"/user/train"
                           andParm:parameters
          completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"deleteTrainRecord -- %@" , request.responseObject);
        id data = [request.responseObject valueForKeyPath:@"data"];
        success(data);
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"deleteTrainRecord -- %@" , request.error);
        failure( request.error);
    }];
}

+ (void)trainRecord:(NSDictionary *)parameters success:(successData)success failure:(failedData)failure   {
    [MRKBaseRequest mrkGetRequestUrl:@"/user/train"
                             andParm:parameters
            completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"heartTrainRecord -- %@" , request.responseObject);
        id data = [request.responseObject valueForKeyPath:@"data"];
        success(data);
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"heartTrainRecord -- %@" , request.error) ;
        failure(request.error);
    }];
}


+ (void)requestMyHeartDevice:(NSDictionary *)parameters success:(successData)success failure:(failedData)failure   {
    [MRKBaseRequest mrkGetRequestUrl:@"/equip/equipment/equipmentInfoController/getDeviceDetail"
                             andParm:parameters
            completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"MyHeartDevice -- %@" , request.responseObject);
        id data = [request.responseObject valueForKeyPath:@"data"];
        [MRKNetworkCache setHttpCache:data URL:HeartDeviceInfoKey parameters:nil];
        success(data);
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"MyHeartDevice -- %@" , request.error) ;
        failure(request.error);
    }];
}


+ (void)searchHeartDevice:(NSDictionary *)parameters success:(successData)success failure:(failedData)failure   {
    [MRKBaseRequest mrkPostRequestUrl:@"/equip/equipment-bind/query"
                             andParm:parameters
            completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"searchHeartDevice -- %@" , request.responseObject);
        id data = [request.responseObject valueForKeyPath:@"data"];
        success(data);
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"searchHeartDevice -- %@" , request.error) ;
        failure(request.error);
    }];
}

+ (void)bindOrUnbindHeartDevice:(NSDictionary *)parameters success:(successData)success failure:(failedData)failure   {
    [MRKBaseRequest mrkPostRequestUrl:@"/equip/equipment-bind"
                             andParm:parameters
            completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"bindOrUnbindHeartDevice -- %@" , request.responseObject);
        id data = [request.responseObject valueForKeyPath:@"data"];
        success(data);
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"bindOrUnbindHeartDevice -- %@" , request.error) ;
        failure(request.error);
    }];
}

+ (void)heartDeviceTrainDetial:(NSDictionary *)parameters success:(successData)success failure:(failedData)failure   {
    [MRKBaseRequest mrkGetRequestUrl:@"/user/train-rate"
                             andParm:parameters
            completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"heartDeviceTrainDetial -- %@" , request.responseObject);
        id data = [request.responseObject valueForKeyPath:@"data"];
        success(data);
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"heartDeviceTrainDetial -- %@" , request.error) ;
        failure(request.error);
    }];
}

+ (void)addTrainData:(NSDictionary *)parameters success:(successData)success failure:(failedData)failure {
    [MRKBaseRequest mrkPostRequestUrl:@"/user/train"
                             andParm:parameters
            completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"addTrainData -- %@" , request.responseObject);
        id data = [request.responseObject valueForKeyPath:@"data"];
        success(data);
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"addTrainData -- %@" , request.error) ;
        failure(request.error);
    }];
}

///根据名称和类型id获取设备型号信息
+ (void)getEquipmentInfo:(NSDictionary *)dic success:(successData)success failure:(nonnull failedData)failure{
    [MRKBaseRequest mrkGetRequestUrl:@"/equip/equipment/equipmentInfoController/getEquipTypeInfoByName/v2"
                             andParm:dic
            completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"EquipTypeInfoByName -- %@" , request.responseObject);
        id data = [request.responseObject valueForKeyPath:@"data"];
        success(data);
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"EquipTypeInfoByName error-- %@" , request.error) ;
        failure(request.error);
    }];
}

//网页后台埋点
+ (void)logHtml:(NSDictionary *)dic success:(successData)success failure:(nonnull failedData)failure{
    [MRKBaseRequest mrkSilencePostRequestUrl:@"/app/log-html/logHtml"
                                     andParm:dic
                    completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"logHtml -- %@" , request.responseObject);
        id data = [request.responseObject valueForKeyPath:@"data"];
        success(data);
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"logHtml error -- %@" , request.error) ;
        failure(request.error);
    }];
}

+ (RACSignal *)uploadDeviceInfo:(NSDictionary *)parameters  {
    return [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        [MRKBaseRequest mrkRequestType:YTKRequestMethodPUT
                                   url:@"/equip/equipment/equipmentInfoController/equipment"
                               andParm:parameters
              completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
            NSLog(@"uploadDeviceInfo -- %@" , request.responseObject);
            id data = [request.responseObject valueForKeyPath:@"data"];
            [subscriber sendNext:data];
        } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
            NSLog(@"uploadDeviceInfo_error -- %@" , request.error);
          
        }];
        return nil;
    }];
}

+(void)getSupportEquipmentPerfix:(NSDictionary *)dic success:(successData)success failure:(nonnull failedData)failure {
    [MRKBaseRequest mrkSilenceGetRequestUrl:@"/equip/equipDictController/getEquipDictList"
                                    andParm:dic
                   completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"getSupportEquipmentPerfix -- %@" , request.responseObject);
        id data = [request.responseObject valueForKeyPath:@"data"];
        success(data);
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"getSupportEquipmentPerfix error-- %@" , request.error) ;
        failure(request.error);
    }];
}


+(void)searchDevice:(NSDictionary *)parameters success:(successData)success failure:(failedData)failure {
    [MRKBaseRequest mrkGetRequestUrl:@"/equip/equipment/equipmentInfoController/getBinding"
                             andParm:parameters
            completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"searchDevice -- %@" , request.responseObject);
        id data = [request.responseObject valueForKeyPath:@"data"];
        success(data);
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"searchDevice error-- %@" , request.error) ;
        failure(request.error);
    }];
}

+(void)chooseTypeList:(NSDictionary *)parameters success:(successData)success failure:(failedData)failure {
    [MRKBaseRequest mrkGetRequestUrl:@"/equip/equipment/equipmentTypeController/equipSearchList"
                             andParm:parameters
            completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"chooseTypeList -- %@" , request.responseObject);
        id data = [request.responseObject valueForKeyPath:@"data"];
        success(data);
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"chooseTypeList error-- %@" , request.error) ;
        failure(request.error);
    }];
}

///心率带上传数据前 获取签名
+(void)getSocketSign:(NSDictionary *)parameters success:(successData)success failure:(failedData)failure {
    MRKConfigRequest *api = [[MRKConfigRequest alloc] init];
    api.type = YTKRequestMethodGET;
    api.url = @"/equip/equipment/equipmentInfoController/createHeratRateWS";
    api.notTipError = YES;
    api.requestAutoRetry = YES;
    api.requestData = parameters.mutableCopy;
    [api startWithCompletionBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        id data = [request.responseObject valueForKeyPath:@"data"];
        success(data);
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        failure(nil);
    }];
}

////心率预警详情
+(void)getHeartRateWarnDetial:(NSDictionary *)parameters success:(successData)success  failure:(failedData)failure {
    [MRKBaseRequest mrkSilenceGetRequestUrl:@"/equip/equipment/heart-rate-warning/getHeartRateWarning"
                                    andParm:parameters
                   completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"getHeartRateWarnDetial -- %@" , request.responseObject);
        id data = [request.responseObject valueForKeyPath:@"data"];
        [MRKNetworkCache setHttpCache:data URL:HeartWarnInfoKey parameters:nil];
        success(data);
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"getHeartRateWarnDetial error-- %@" , request.error) ;
        failure(request.error);
    }];
}

///设置心率预警值/开关
+(void)setHeartRateWarnValueStatus:(NSDictionary *)parameters success:(successData)success  failure:(failedData)failure {
    [MRKBaseRequest mrkPostRequestUrl:@"/equip/equipment/heart-rate-warning/adjustHeartRateWarning"
                              andParm:parameters
             completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"setHeartRateWarnValueStatus -- %@" , request.responseObject);
        id data = [request.responseObject valueForKeyPath:@"data"];
        success(data);
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"setHeartRateWarnValueStatus error-- %@" , request.error) ;
        failure(request.error);
    }];
}

///回调心率弹窗显示
+(void)setHeartRateWarnPopup:(NSDictionary *)parameters success:(successData)success  failure:(failedData)failure {
    [MRKBaseRequest mrkRequestType:YTKRequestMethodPUT
                               url:@"/equip/equipment/heart-rate-warning/heartRateWarningPopup"
                           andParm:parameters
          completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"setHeartRateWarnPopup -- %@" , request.responseObject);
        id data = [request.responseObject valueForKeyPath:@"data"];
        success(data);
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"setHeartRateWarnPopup error-- %@" , request.error) ;
        failure(request.error);
    }];
}

@end




