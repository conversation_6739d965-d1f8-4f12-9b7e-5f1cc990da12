//
//  MRKAIPlanAPIClient.m
//  Student_IOS
//
//  Created by <PERSON><PERSON> on 2025/7/2.
//

#import "MRKAIPlanAPIClient.h"
#import "MRKPlanOverViewModel.h"

@implementation MRKAIPlanAPIClient

///AIplan 升级
+ (void)aiplanUpgradeSuccess:(successData)success failure:(failedData)failure{
    [MRKBaseRequest mrkRequestType:YTKRequestMethodPOST
                               url:@"/course/training-plan/v2/upgrade"
                           andParm:nil
          completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        id data = [request.responseObject valueForKeyPath:@"data"];
        success(data);
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        failure(request.error);
    }];
}

///AIplan 数据概况
+ (void)aiplanOverviewSuccess:(successData)success failure:(failedData)failure{
    [MRKBaseRequest mrkRequestType:YTKRequestMethodGET
                               url:@"/course/training-plan/v2/overview"
                           andParm:nil
          completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        id data = [request.responseObject valueForKeyPath:@"data"];
        NSLog(@"aiplanOverviewSuccess == %@", data);
        MRKPlanOverViewModel *model = [MRKPlanOverViewModel modelWithJSON:data];
        success(model);
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        failure(request.error);
    }];
}


///AIplan 销假
+ (void)aiplanCancelLeaveWithId:(NSString *)planId version:(NSInteger)version Success:(successData)success failure:(failedData)failure{
    NSString *url = version == 1 ? @"/course/training-plan/cancel-leave": @"/course/training-plan/v2/cancel-leave";
    [MRKBaseRequest mrkRequestType:YTKRequestMethodPOST
                               url:url
                           andParm:@{@"planId":planId?:@""}
          completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        id data = [request.responseObject valueForKeyPath:@"data"]; ///data    boolean
        success(data);
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        failure(request.error);
    }];
}


///AIplan 退出
+ (void)aiplanQuitWithId:(NSString *)planId Success:(successData)success failure:(failedData)failure{
    [MRKBaseRequest mrkRequestType:YTKRequestMethodPOST
                               url:@"/course/training-plan/v2/exit"
                           andParm:@{@"planId":planId?:@""}
          completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        id data = [request.responseObject valueForKeyPath:@"data"]; ///data    boolean
        success(data);
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        failure(request.error);
    }];
}


///AIplan 修改动作目标数量
+ (void)aiplanUpdateMotionTaskTargetValueWithParms:(NSDictionary *)parms Success:(successData)success failure:(failedData)failure{
    [MRKBaseRequest mrkRequestType:YTKRequestMethodPUT
                               url:@"/course/training-plan/update-motion-task-target"
                           andParm:parms
                      notShowError:YES
          completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        id data = [request.responseObject valueForKeyPath:@"data"];
        success(data);
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        failure(request.error);
    }];
}

@end
