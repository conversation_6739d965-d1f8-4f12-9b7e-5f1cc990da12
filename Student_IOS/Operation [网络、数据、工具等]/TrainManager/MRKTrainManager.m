//
//  MRKTrainManager.m
//  Student_IOS
//
//  Created by merit on 2024/11/21.
//

#import "MRKTrainManager.h"
#import "MRKSportDataModel.h"
#import "MRKAutoConnectManager.h"
#import "MRKSportDataCalculateManager.h"
#import "MRKSportDataSQLManager.h"
#import "MRKPrepareTrainManager.h"
#import "ExerciseReportWebController.h"
#import "MRKDeviceConnectAlertView.h"
#import "MrkHeartRateManager.h"

@interface MRKTrainManager()<MRKAutoConnectManagerDelegate>{
    CFAbsoluteTime _lastUpdateTime;
}

@property (nonatomic, assign) NSInteger connectCount;                   ///重连次数
///
@property (nonatomic, strong) NewConnectManager *connectManager;
@property (nonatomic, strong) MRKAutoConnectManager *autoConnect;
@property (nonatomic, assign) DEVICE_CONNECT_STATUS connectStatus;
@property (nonatomic, strong) NSNumber *treamillStatus;

/// 训练内容，训练类型、训练模式、训练目标
@property (nonatomic, strong, nullable) MRKTrainingInfo *trainInfo;
@property (nonatomic, strong, nullable) MRKDeviceModel *deviceModel;
@property (nonatomic, assign) BOOL isDealUntrackedTraining;              ///是否已经处理空练数据
@property (nonatomic, assign) BOOL hasStartTraining;
@property (nonatomic, strong, nullable) NSString *trainingRecordId;      ///训练记录id
///
@property (nonatomic, assign) BOOL trainingHasSettled;                   ///训练是否已结算
@end

@implementation MRKTrainManager

- (void)dealloc {
    NSLog(@"😊😊😊-----dealloc-----😊😊😊%@" , NSStringFromClass([self class]));
}

- (instancetype)init {
    self = [super init];
    if (self) {
        self.isTargetCompletedContinue = YES;
        self.connectedAutoGetData = YES;
        
        [self deviceModelListener];
        
        _lastUpdateTime = 0;
        
        ///视频不支持连接课程消耗数据，
        [[NSNotificationCenter defaultCenter] addObserver:self
                                                 selector:@selector(getBlueDeviceData:)
                                                     name:@"VideoNotSupportConnectData"
                                                   object:nil];
    }
    return self;
}


- (void)getBlueDeviceData:(NSNotification *)sender {
    BaseEquipDataModel *model = sender.object;
    if (model == nil) {
        return;
    }
    
    if (CFAbsoluteTimeGetCurrent() - _lastUpdateTime < 1.0){
        return;
    }
    _lastUpdateTime = CFAbsoluteTimeGetCurrent();
    [self handleBlueData:model];
    return;
}

- (void)deviceModelListener{
    @weakify(self);
    RACSignal *signal = [RACObserve(self, deviceModel) takeUntil:[self rac_willDeallocSignal]];
    [[signal skip:1] subscribeNext:^(id  _Nullable x) {
        @strongify(self);
        NSLog(@"当前训练设备 deviceModel ==== %@",x);
        if (x == nil){
            self.equipmentInfo = nil;
            return;
        }
        
        [self configEquipmentInfo];
    }];
}

#pragma mark - init
- (NewConnectManager *)connectManager {
    if(!_connectManager) {
        _connectManager = [[NewConnectManager alloc]init];
    }
    return _connectManager;
}

- (MRKAutoConnectManager *)autoConnect {
    if (!_autoConnect){
        _autoConnect = [MRKAutoConnectManager sharedInstance];
    }
    return _autoConnect;
}

- (MRKSportDataCalculateManager *)dataManager{
    if (!_dataManager){
        _dataManager = [[MRKSportDataCalculateManager alloc] init];
    }
    return _dataManager;
}

- (void)setEquipmentInfo:(MrkEquipmentInfo *)equipmentInfo {
    _equipmentInfo = equipmentInfo;
}

#pragma mark ================  configEquipmentInfo  ===============
- (void)configEquipmentInfo{
    if (self.equipmentInfo != nil) return;
    
    @weakify(self);
    self.equipmentInfo = [[MrkEquipmentInfo alloc] init];
    self.equipmentInfo.model = self.deviceModel;
    self.equipmentInfo.equipmentInfoDataBlock = ^(BaseEquipDataModel * _Nonnull model) {
        @strongify(self);
        NSLog(@"equipmentInfoDataBlock===============%@", model.description);
        [self handleBlueData:model];
    };
    
    [[[RACObserve(self.equipmentInfo, connectStatus) filter:^BOOL(NSNumber *status) {
        return status.intValue > 0; /// 过滤
    }] distinctUntilChanged] subscribeNext:^(NSNumber * x) {
        @strongify(self);
        dispatch_async(dispatch_get_main_queue(), ^{
            switch (x.intValue) {
                case DeviceConnecting:
                { ///连接中
                    
                } break;
                    
                case DeviceDisconnect:
                {///未连接
                    
                    ///如果是开启了 自动重连 && 第一次断开连接，则不通知更新状态，直接开启重连
                    if (self.isOpenAutoConnect && self.connectCount == 0) {
                        [MRKAutoConnectManager sharedInstance].autoMode = SportAutoConnectMode;
                        return;
                    }
                } break;
                    
                case DeviceConnected:
                {///已连接
                    
                    ///连接成功以后在此重置重连次数
                    self.connectCount = 0;
                    
                    ///
                    if (self.hasStartTraining && self.connectedAutoGetData){
                        [self startGetData];
                    }
                } break;
                    
                case DeviceAutoConnecting:
                {///自动重连中
                    
                } break;
                    
                case DeviceScaning:
                {///设备搜索中
                    
                } break;
                default: break;
            }
        });
        
        self.connectStatus = x.intValue;
    }];
    
    [[RACObserve(self.equipmentInfo, treamillStatus) distinctUntilChanged] subscribeNext:^(NSNumber * x) {
        @strongify(self);
        if (x == nil) return;
        self.treamillStatus = x;
    }];
    
}

#pragma mark ================  startTrain  ===============
/// 开始训练   进入课程&连接设备
- (void)startTrain:(NSString *)productId trainingInfo:(MRKTrainingInfo * _Nullable)info{
    /// 开启获取数据通道
    self.hasStartTraining = YES;
    self.trainInfo = info;
    
    ///监听当前设备大类下连接的设备
    @weakify(self);
    RACSignal *signal = [[MRKConnectStatusManager sharedInstance] connectDeviceModelWithProductID:productId];
    [[[signal distinctUntilChanged] takeUntil:[self rac_willDeallocSignal]] subscribeNext:^(id x) {
        @strongify(self);
        if (x == nil) return;
        if ([x isKindOfClass:[BluetoothModel class]]){
            BluetoothModel *model = (BluetoothModel *)x;
            ///训练过程中，设备连接后就不在重新赋值【断连，连接其他设备不考虑】
            if (self.deviceModel == nil){
                self.deviceModel = model.equipmentModel;
            }
        }
    }];
    
    
    {///自动连接类处理
        self.connectCount = 0;
        [self.autoConnect cancelAllAutoConnect];
        self.autoConnect.autoMode = SportAutoConnectMode;
        self.autoConnect.delegate = self;
    }
}

#pragma mark ================  handleBlueData  ===============
///处理蓝牙数据
- (void)handleBlueData:(BaseEquipDataModel * _Nonnull )model {
    /// 训练已结算
    if (self.trainingHasSettled) {
        return;
    }
    
    /// 已完成目标，完成不继续
    if (self.hasFinishTarget && !self.isTargetCompletedContinue) {
        return;
    }
    
    TrainingTrackData *item = [TrainingTrackData initWithEquipModel:model];
    /// 心率单独处理
    [MrkHeartRateManager shareManager].deviceHeartRate = model.deviceRate;
    
    /// 处理第一条数据
    BOOL isDealFirst = self.isDealUntrackedTraining;
    if (!isDealFirst) {
        ///有训练id ，取缓存数据
        if ([MRKSportDataSQLManager shared].trainID != nil) {
            self.trainingRecordId = [MRKSportDataSQLManager shared].trainID;
            self.dataManager.lastTrackData = [MRKSportDataSQLManager shared].trainData;
            self.dataManager.incrementData = [MRKSportDataSQLManager shared].trainIncrementData;
            isDealFirst = YES; // 继续运动时需要处理第一条数据
        } else {
            @weakify(self);
            /// 是否需要处理空练数据
            if ([MRKPrepareTrainManager shared].saveCacheData) {
                [self requestUntrackedTrainingData:item completion:^{ // 空练
                    @strongify(self);
                    [self afterHandleUntrackedTraining];
                }];
            } else { // 不需要处理空练数据
                [self afterHandleUntrackedTraining];
            }
        }
        self.isDealUntrackedTraining = YES;
    }
    
    /// 设备支持清零，第一条不上报，作为空练，清零后0为底, 不清零设备，第一条数据就是底
    if ((self.equipmentInfo.detailModel.isClean && isDealFirst) || !self.equipmentInfo.detailModel.isClean) {
        [self.dataManager calculateIncrementData:item productId:self.equipmentInfo.model.productId]; // 直接处理数据就行
        [self uploadTrainingData];
    }
}

/// 处理空练数据后
- (void)afterHandleUntrackedTraining {
    if (self.equipmentInfo) {
        ///发送清零指令
        @weakify(self);
        [self.equipmentInfo equipmentClean:^(BOOL success, NSError * error) {
            @strongify(self);
            //        if(self.equipmentInfo.detailModel.isClean) {
            //            [self.dataManager startTrainClear];
            //        }
            
            /// 请求预生成接口
            [self requestPreGenerateTrainId:^{
                /// 创建sql缓存
                [MRKSportDataSQLManager shared].trainID = self.trainingRecordId;
                /// 再次打开获取数据通道
                [self.equipmentInfo startGetData];
            }];
        }];
    } else { // 徒手直接预生成
        /// 请求预生成接口
        [self requestPreGenerateTrainId:^{
            /// 创建sql缓存
            [MRKSportDataSQLManager shared].trainID = self.trainingRecordId;
        }];
    }
}

/// 连接设备
- (void)connectEquipmentModel:(MRKDeviceModel *)model{
    UIViewController *currentVC = [UIViewController currentViewController];
    [NewConnectManager showConnectingLoading:currentVC.view];
    
    ///第一次自动连接时设置
    self.connectedAutoGetData = YES;
    
    ///设备连接方式为手动连接，连接失败会有弹窗提示连接失败原因
    self.connectManager.connectMode = ManualDeviceConnectMode;///
    @weakify(currentVC);
    self.connectManager.connectStatusBlock = ^(id data) {
        @strongify(currentVC);
//        if (![data boolValue]){
//            [MBProgressHUD showMessage:@"连接设备失败" toView:currentVC.view];
//        }
        [NewConnectManager dismssConnectingLoading:currentVC.view];
    };

    [self.connectManager connectDeviceModel:self.deviceModel?:model];
}

///开始获取数据
- (void)startGetData {
    if (self.equipmentInfo != nil){
        ///开始运动指令, 打开数据通道
        [self.equipmentInfo startGetData];
    }
}

///停止获取数据
- (void)endGetData{
    if (self.equipmentInfo != nil){
        ///关闭数据通道
        [self.equipmentInfo endGetData];
        
        {///自动连接类处理
            [self.autoConnect cancelAllAutoConnect];
            self.autoConnect.autoMode = NormalAutoConnectMode;
            self.autoConnect.delegate = nil;
        }
    }
}


/// 结束训练
- (void)stopTrain{
    self.hasStartTraining = NO;
    
    // 关闭数据获取通道
    [self.equipmentInfo endGetData];
    
    {///自动连接类处理
        [self.autoConnect cancelAllAutoConnect];
        self.autoConnect.autoMode = NormalAutoConnectMode;
        self.autoConnect.delegate = nil;
    }
    
    self.equipmentInfo = nil;
    self.deviceModel = nil;
}

/// 继续训练
- (void)continueTrain{
    ///发送继续运动指令
    [self.equipmentInfo startGetData];
}

/// 设备参数
- (NSDictionary *)deviceParms{
    if (self.equipmentInfo == nil){
        return @{};
    }
    return @{
        @"productId": self.equipmentInfo.model.productId?:@"",
        @"productModelId": self.equipmentInfo.model.modelId?:@"",
        @"equipmentInfoId": self.equipmentInfo.model.deviceUserRelId?:@"",
    };
}

/// 训练的参数
- (NSDictionary *)trainParms{
    return @{
        @"trainingType": @(self.trainInfo.trainingType),
        @"trainingMode": self.trainInfo.trainingMode?:@"",
        @"trainingTarget": self.trainInfo.trainingTarget?:@"",
    };
}


#pragma mark ————————————  pre-generate  ————————————
/// 请求预生产训练id接口
- (void)requestPreGenerateTrainId:(void(^)(void))completion{
    NSMutableDictionary *parms = self.deviceParms.mutableCopy;
    [parms addEntriesFromDictionary: self.trainParms.mutableCopy];
    ///
    if (self.trainInfo.trainingType == MRKTrainingTypeCourse ||
        self.trainInfo.trainingType == MRKTrainingTypeRealVideo) {
        parms[@"courseId"] = self.trainInfo.courseId;
    }
    
    [MRKBaseRequest mrkPostRequestUrl:@"/sport/training-record/pre-generate"
                              andParm:parms
             completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        id data = [request.responseObject valueForKeyPath:@"data"];
        self.trainingRecordId = data;///训练记录id
        completion();
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        /// 报错返回上一个页面
        [[MRKPrepareTrainManager shared] backVC:nil];
    }];
}

#pragma mark ————————————  untrackedTraining  ————————————
/// 请求空练数据接口
- (void)requestUntrackedTrainingData:(TrainingTrackData *)item completion:(void(^)(void))completion{
    NSMutableDictionary *parms = self.deviceParms.mutableCopy;
    parms[@"trainingTrackData"] = [item modelToJSONObject];
    [MRKBaseRequest mrkSilencePostRequestUrl:@"/sport/training-record/settlement/untracked-training"
                                     andParm:parms
                    completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        completion();
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        completion();
    }];
}



#pragma mark ————————————  uploadData  ————————————
- (void)uploadTrainingData{
    if (![self.trainingRecordId isNotBlank]) {
        return;
    }
    
    TrainingTrackData *lastTrackData = self.dataManager.lastTrackData;
    TrainingIncrementData *incrementData = self.dataManager.incrementData;
    
    ///数据里塞入小节id和播放时长
    if (self.trainInfo.trainingType == MRKTrainingTypeCourse 
        || self.trainInfo.trainingType == MRKTrainingTypeRealVideo) {
        lastTrackData.linkId = self.linkId;
        lastTrackData.playTime = self.playTime;
        lastTrackData.meritRate = self.preMeritRate;
        
        incrementData.totalMeritBurning = self.totalMeritRate;///总超燃脂率
    }
    
    NSMutableDictionary *parms = self.deviceParms.mutableCopy;
    parms[@"trainingRecordId"] = self.trainingRecordId?:@"";
    parms[@"trainingType"] = @(self.trainInfo.trainingType);
    parms[@"trainingTrackData"] = lastTrackData ? @[[lastTrackData modelToJSONObject]] : nil;
    parms[@"trainingIncrementData"] = [incrementData modelToJSONObject];
    
    [MRKBaseRequest mrkSilencePostRequestUrl:@"/sport/training-record/detail-upload"
                                     andParm:parms
                    completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSInteger statusCode = [[request.responseObject valueForKeyPath:@"status"] integerValue];
        if (statusCode == 500010000)
        {
            /// 训练记录已结算
            if (!self.trainingHasSettled){
                 self.trainingHasSettled = YES;
                MRKUnsettleSportDataModel *it = [self createUnsettleSportDataModel];
                [[MRKPrepareTrainManager shared] showAutoTrainCompleteAlert:it];
            }
        }
    }];
}





#pragma mark ————————————  正常settlement  ————————————
- (void)requestSettlement{
    if (self.trainingHasSettled) {
        return;
    }
    self.trainingHasSettled = YES;
    
    ///
    if (![self.trainingRecordId isNotBlank]) {
        [[MRKPrepareTrainManager shared] backVC:nil];
        return;
    }
    
    MRKUnsettleSportDataModel *it = [self createUnsettleSportDataModel];
    [[MRKPrepareTrainManager shared] requestSettlement:it trainingSettlementScene:nil success:^(id data) {
        ///跳转训练报告
        [self skipReportVC];
        
    } failure:^(NSInteger code) {
        if (code == 500010000) {
            /// 训练记录已结算
            [[MRKPrepareTrainManager shared] showAutoTrainCompleteAlert:it];
        } else {
            /// 其余情况直接返回
            [[MRKPrepareTrainManager shared] backVC:nil];
        }
    }];
}

- (void)requestSettlementSuccess:(void(^)(id __nullable))success failure:(void(^)(NSInteger))failure{
    [self requestSettlementSuccess:success failure:failure needMapping:YES];
}

- (void)requestSettlementSuccess:(void(^)(id __nullable))success failure:(void(^)(NSInteger))failure needMapping:(BOOL)needMapping{
    if (self.trainingHasSettled) {
        return;
    }
    self.trainingHasSettled = YES;
    
    ///
    if (![self.trainingRecordId isNotBlank]) {
        [[MRKPrepareTrainManager shared] backVC:nil];
        return;
    }
    
    @weakify(self);
    void(^calorieMappingBlock)(id parms) = ^(id parms){
        @strongify(self);
        [self requestCalorieMapping:^(id data) {
            if (success){
                success(data);
            }
        } failure:^{
            if (success){
                success(nil);
            }
        } withParms:parms];
    };
    
    MRKUnsettleSportDataModel *it = [self createUnsettleSportDataModel];
    [[MRKPrepareTrainManager shared] requestSettlement:it trainingSettlementScene:nil success:^(id _Nonnull data) {
        if (needMapping && data != nil){
            calorieMappingBlock(data);
        } else {
            if (success){
                success(nil);
            }
        }
    } failure:^(NSInteger code) {
        if (code == 500010000) {
            /// 训练记录已结算
            [[MRKPrepareTrainManager shared] showAutoTrainCompleteAlert:it];
        } else {
            if (failure){
                failure(code);
            }
        }
    }];
}




#pragma mark ————————————  用户结算数据映射公里食物 ————————————
- (void)requestCalorieMapping:(void(^)(id))success failure:(void(^)(void))failure withParms:(id)parms{
    if (![parms isKindOfClass:[NSDictionary class]]){
        failure();
        return;
    }
    
    NSMutableDictionary *par = [(NSDictionary *)parms mutableCopy];
    par[@"userId"] = UserInfo.userId;
    /**
     (lldb) po par
     {
         distance = 0;
         duration = 155;
         isSuccess = 1;
         kcal = "5.466666691005228";
         productId = 32;
         trainingRecordId = 1952488222720709;
         userId = 1415291775388712961;
     }
     */
    [MRKBaseRequest mrkSilenceGetRequestUrl:@"/user/training/get-settlement-summary-data"
                                    andParm:par
                   completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        id data = [request.responseObject valueForKeyPath:@"data"];
        success(data);
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        failure();
    }];
}


- (void)skipReportVC:(UIViewController *)controller{
    self.trainingHasSettled = YES;
    
    jxt_getSafeMainQueue(^{
        ExerciseReportWebController *vc = [[ExerciseReportWebController alloc] init];
        vc.courseType = [NSString stringWithFormat:@"%ld", (long)self.trainInfo.trainingType];
        vc.exerciseID = self.trainingRecordId;
        vc.equipmentId = self.equipmentInfo.model.deviceUserRelId;
        vc.fromTrainingView = YES;
        vc.firstInto = self.firstInto;
        
        vc.liveCourseModel = self.courseModel;
        vc.playTime = self.playTime ?:@(0);
        vc.quitMidway = self.exitOnPlaying; ///课程是否中途结束训练
        @weakify(self);
        [controller.navigationController pushViewController:vc animated:YES completion:^{
            @strongify(self);
            [self stopTrain];
            ///更新课程详情弹窗
            [[NSNotificationCenter defaultCenter] postNotificationName:@"UpdateReportComplete" object:nil];
        }];
    });
}

///跳转训练报告
- (void)skipReportVC{
    UIViewController *controller = [UIViewController currentViewController];
    [self skipReportVC:controller];
}



#pragma mark ————————————  prepareForTrain  ————————————

- (NSString *)equipmentName{
    return self.equipmentInfo.model.bluetoothName;
}

- (NSString *)productId{
    return self.equipmentInfo.model.productId;
}

- (UIView *)view{
    UIViewController *vc = [UIViewController currentViewController];
    return vc.view;
}

#pragma mark - MRKAutoConnectManagerDelegate

///如果当前是运动模式，断开连接后的代理方法
- (void)disconnectBModel:(BluetoothModel *)bModel{
    NSLog(@"%@____disconnectBModel:%@", self, bModel.debugDescription);
    if (!self.isOpenAutoConnect) {
        MLog(@"MRKTrainManager=====未开启自动重连功能");
        return;
    }
    
    if ([self.equipmentName isEqualToString:bModel.localName])
    {
        if (self.connectCount == 0) {
            MLog(@"MRKTrainManager=====当前设备自动连接");
            self.connectCount = 1;
            self.connectStatus = DeviceAutoConnecting;
            if (self.autoConnectNeedLoading) {
                [NewConnectManager showConnectingLoading:self.view];
            }
            self.autoConnect.autoMode = SportAutoConnectMode;
            [self.autoConnect startAutoConnetDeviceModel:bModel.equipmentModel];
        } else {
            MLog(@"MRKTrainManager=====当前设备不是第一次中断");
        }
    } else {
        MLog(@"断开的不是当前设备，不做处理");
    }
}

- (void)autoConnectFailure{
    [NewConnectManager dismssConnectingLoading:self.view];
    if (self.connectCount == 1) {
        self.connectStatus = DeviceDisconnect;
        MLog(@"MRKTrainManager=====自动重连失败");
        [self connectFailAlert];
        
        ///
        if (self.autoConnectFailureBlock){
            self.autoConnectFailureBlock();
        }
    }
}

- (void)autoConnectSuccess{
    MLog(@"MRKTrainManager=====自动重连成功");
    [NewConnectManager dismssConnectingLoading:self.view];
    
    ///标识重连成功
    self.isReconnectSuccess = YES;
    [self continueTrain];
}

- (MRKUnsettleSportDataModel *)createUnsettleSportDataModel {
    MRKUnsettleSportDataModel *it = [[MRKUnsettleSportDataModel alloc] init];
    it.productId = self.equipmentInfo.model.productId;
    it.productImg = self.equipmentInfo.model.cover;
    it.productModelId = self.equipmentInfo.model.modelId;
    it.equipmentInfoId = self.equipmentInfo.model.deviceUserRelId;
    it.bluetoothName = self.equipmentInfo.model.bluetoothName;
    it.communicationProtocol = self.equipmentInfo.model.communicationProtocol.stringValue;
    
    it.trainingRecordId = self.trainingRecordId;
    it.trainingType = self.trainInfo.trainingType;
    it.trainingMode = self.trainInfo.trainingMode;
    it.trainingTarget = self.trainInfo.trainingTarget;
    it.trainingTrackData = self.dataManager.lastTrackData.copy;
    it.trainingIncrementData = self.dataManager.incrementData.copy;
    it.trainingIncrementData.totalMeritBurning = self.totalMeritRate;
    /// 重连成功次数 可添加
    it.isReconnect = self.isReconnectSuccess ? @"1" : @"0";
    it.clearableEquipment = self.equipmentInfo.detailModel.isClean ?  @"1" : @"0";
    it.firstInto = self.firstInto;
    it.rank = self.rank ?: @"";
    it.isCountRank = self.isCountRank;
    it.matchDuration = self.matchDuration;
    it.scene = 1;///钉死这个
    
    it.courseModel = self.courseModel;
    it.playTime = self.playTime;
    return it;
}

- (void)setPlayTime:(NSNumber *)playTime{
    if (playTime.doubleValue > self.playTime.doubleValue) {
        _playTime = playTime;
    }
}

/// 连接失败弹窗
- (void)connectFailAlert {
    /// 屏蔽掉视频弹窗
    if (self.trainInfo.trainingType == MRKTrainingTypeCourse
        || self.trainInfo.trainingType == MRKTrainingTypeRealVideo
        || self.trainInfo.trainingType == MRKTrainingTypeLandPath) {
       
        return;
    }
    
    MRKUnsettleSportDataModel *it = [self createUnsettleSportDataModel];
    MRKDeviceConnectFailView *view = [[MRKDeviceConnectFailView alloc] init];
    view.ensureAction = ^{
        [[MRKPrepareTrainManager shared] showSettlementOrContinueTrainAlert:it];
    };
    view.helpAction = ^{
        ///跳转帮助与建议
        UIViewController *controller = [UIViewController currentViewController];
        if (controller) {
            WebViewViewController *vc = [[WebViewViewController alloc] init];
            [vc.mrkDeallocSignal subscribeNext:^(id x) {
                [[MRKPrepareTrainManager shared] showSettlementOrContinueTrainAlert:it];
            }];
            vc.isHiddenNav = YES;
            vc.htmlURL = MRKAppH5LinkCombine(MRKUserHelpAndFeedback);
            [controller.navigationController pushViewController:vc animated:YES];
        }
    };
    [view showIn:[UIViewController currentViewController].view];
}

@end
