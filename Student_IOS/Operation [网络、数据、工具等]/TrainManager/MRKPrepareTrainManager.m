//
//  MRKPrepareTrainManager.m
//  Student_IOS
//
//  Created by merit on 2024/11/27.
//

#import "MRKPrepareTrainManager.h"
#import "MRKRealmSportData.h"
#import "MRKToolKit.h"
#import "MRKSportDataSQLManager.h"
#import "MRKContinueTrainManager.h"
#import "ExerciseReportWebController.h"
#import "MrkGeneralAlertView.h"
#import "MRKFreeTrainingController.h"

@interface MRKPrepareTrainManager()

@end

@implementation MRKPrepareTrainManager

static MRKPrepareTrainManager *_instance = nil;
+ (instancetype)shared
{
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        _instance = [[MRKPrepareTrainManager alloc] init];
    });
    return _instance;
}

#pragma mark ————————————  checkUnClosedTraining  ————————————
/// 训练前准备工作
/// 查询用户是否存在未结算训练记录，
/// 存在未结算的数据，判断该训练数据是否支持继续训练，支持，让用户选择继续训练还是手动结算
/// 不支持，自动调用结算
- (void)checkUnClosedTraining:(void(^)(void))completion{
    [self checkTrainingWithType:TrainCheckTypeNormal complete:completion];
}

- (void)checkTrainingWithType:(TrainCheckType)type complete:(void(^)(void))completion{
    @weakify(self);
    [self requestQueryUnsettleRecord:^(MRKUnsettleSportDataModel *item) {
        if (item == nil) {
            [[MRKSportDataSQLManager shared] clearSQL]; /// 没有结算数据，直接清空
            completion();
            return;
        }
        @strongify(self);
        [self handleLocalAndRequestUnsettleRecord:item
                                        sceneType:type
                                       completion:completion];
    } failure:^{ }];
}


#pragma mark ————————————  处理本地缓存数据和未结算的数据，取两者最大值 ————————————
- (void)handleLocalAndRequestUnsettleRecord:(MRKUnsettleSportDataModel *)item
                                  sceneType:(TrainCheckType)type
                                 completion:(void(^)(void))completion{
    
    MLog(@"MRKPrepareTrainManager 有未结算数据");
    MRKRealmSportData *cacheData = [[MRKSportDataSQLManager shared] cacheWithTrainID: item.trainingRecordId]; // 取缓存，比较两者最大值
    if (cacheData.trainIncrementData != nil) {
        TrainingTrackData *traData = [TrainingTrackData modelWithJSON:cacheData.trainData];
        TrainingIncrementData *increData = [TrainingIncrementData modelWithJSON:cacheData.trainIncrementData];
        if (increData.totalTime > item.trainingIncrementData.totalTime) {// 缓存比接口数据大，覆盖接口返回的数据
            item.trainingTrackData = traData;
            item.trainingIncrementData = increData;
        }
        MLog(@"MRKPrepareTrainManager 缓存数据trainData:%@ == trainIncrementData:%@",cacheData.trainData, cacheData.trainIncrementData);
    }
    if (type == TrainCheckTypeNormal || type == TrainCheckTypeFreedom){ // 首页、自由训练，如果是自由训练，弹继续运动，其余结算
        if (item.trainingType == MRKTrainingTypeFreedom) {
            NSString *settlementScene = [self isAutoSettlement:item];/// 判断九十分钟的逻辑
            if (settlementScene) { /// 自动结算
                [self requestSettlement:item trainingSettlementScene:settlementScene];
                MLog(@"自动结算");
            } else { /// 选择 继续训练 还是结算
                [self showSettlementOrContinueTrainAlert:item];
                MLog(@"弹窗选择 继续训练 还是结算");
            }
        } else{/// 不是自由训练，直接结算
            [self endTrainWithItem:item trainSceneType:type completion:completion];
            MLog(@"不是自由训练，直接结算");
        }
    } else{///视频入口不区分是否是自由训练还是课程，统一弹结算
        [self endTrainWithItem:item trainSceneType:type completion:completion];
        MLog(@"视频入口不区分是否是自由训练还是课程，统一弹结算");
    }
}

/// 结束训练（首页-结束同个手机终端（非自由训练）、课程-结束上一次的训练）
- (void)endTrainWithItem:(MRKUnsettleSportDataModel *)item trainSceneType:(TrainCheckType)type completion:(void(^)(void))completion{
    if (type == TrainCheckTypeNormal){ // 首页
        if (item.platformType.intValue == kDeviceType){ // 同个设备
            [self requestSettlement:item trainingSettlementScene:nil success:^(id data){
                /// tabbar 添加提示窗
                [[NSNotificationCenter defaultCenter] postNotificationName:@"MeritRepairTrainingSuc" object:nil];
            } failure:^(NSInteger code) {}];
        }
        return;
    } /// 结算上一次
    [MrkAlertManager showAlert:@"上次的运动报告未生成"
                       message:@"开始新运动前需保存上一次的运动报告，生成后可前往[我的运动记录]查看。"
                        cancel:@"暂不处理"
                        ensure:@"生成报告"
                   handleIndex:^(NSInteger index) {
        if (index == 1) {/// 生成报告
            [MBProgressHUD showLodingWithMessage:@"运动报告生成中" view:nil];
            [self requestSettlement:item trainingSettlementScene:nil hiddenError:YES success:^(id data){
                [MBProgressHUD hideHUDForView:nil];
                [MBProgressHUD showMessage:@"运动报告已生成"];
                /// 2s 后自动跳转
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    if (completion){
                        completion();
                    }
                });
            } failure:^(NSInteger code) {
                [MBProgressHUD hideHUDForView:nil];
                [MBProgressHUD showMessage:@"运动报告生成失败"];
            }];
        }
    }];
}

#pragma mark ————————————  展示自动结束训练弹窗 ————————————
- (void)showAutoTrainCompleteAlert:(MRKUnsettleSportDataModel *)item{
    [self showAutoTrainCompleteAlert:item alertScene:@""];
}

- (void)showAutoTrainCompleteAlert:(MRKUnsettleSportDataModel *)item alertScene:(NSString *)scene{
    [self removeContinueTrainAlert:YES];
    
    NSString *tipStr = @"";
    if ([scene isEqualToString:@"102"]) {
        tipStr = @"运动暂停超过90分钟，已自动生成报告";
    } else if ([scene isEqualToString:@"设备已解绑"]) {
        tipStr = @"设备已解绑，已自动生成报告";
    } else {
        tipStr = @"运动已结束，已自动生成报告";
    }
    
    MLog(@"showAutoTrainCompleteAlert=====%@", tipStr);
    
    @weakify(self);
    NSMutableAttributedString *message = [[NSMutableAttributedString alloc] initWithString:tipStr];
    message.color = UIColorHex(#363A44) ;
    message.font = [UIFont fontWithName:fontNameMeDium size:16];
    message.alignment = NSTextAlignmentCenter;
    
    MrkGeneralAlertView *alert = [MrkGeneralAlertView build];
    alert.messageObject = MakeAttributAlertViewMessageObject(message);
    alert.maskViewTapHidden = YES;
    [alert addBtnWithTitle:@"知道了" btnStyle:kAlertViewButtonCancelStyle clicked:^(MrkGeneralAlertView *alertView) {
        [alertView hide];
        @strongify(self);
        [self backVC: item];
    }];
    [alert addBtnWithTitle:@"查看报告" btnStyle:kAlertViewButtonEnsureStyle clicked:^(MrkGeneralAlertView *alertView) {
        [alertView hide];
        @strongify(self);
        [self skipReportVC: item];
    }];
    [alert showInKeyWindow];
}


- (BOOL)isNeedAutoBack:(UIViewController *)controller {
    if ([controller isKindOfClass:NSClassFromString(@"MRKFreeTrainingController")]           ///自由训练
        || [controller isKindOfClass:NSClassFromString(@"MRKRouteTrainController")]           ///自由训练
        || [controller isKindOfClass:NSClassFromString(@"MRKVerticalVideoController")]       ///竖屏普通课程、Merit课程
        || [controller isKindOfClass:NSClassFromString(@"MRKVideoController")]               ///普通课程、Merit课程
        || [controller isKindOfClass:NSClassFromString(@"MRKGameVideoController")]           ///竞赛课程
        || [controller isKindOfClass:NSClassFromString(@"MrkPlotVideoController")]           ///新竞赛课程
        || [controller isKindOfClass:NSClassFromString(@"MRKVideoExperienceController")]     ///新用户测评-入门指导课
        ) {
        return YES;
    }
    return NO;
}

/// 在训练页面，哪里来的（进训练的入口）返回哪里去
- (void)backVC:(MRKUnsettleSportDataModel * _Nullable )item{
    @weakify(self);
    jxt_getSafeMainQueue(^{
        @strongify(self);
        [self removeContinueTrainAlert:YES];
        
        UIViewController *vc = [UIViewController currentViewController];
        if ([self isNeedAutoBack: vc]) {
            if (item.firstInto) { /// 新手链路
                [self skipReportVC:item];
            } else { /// 返回上一个页面
//                [vc.navigationController popViewControllerAnimated:YES];
                
                UINavigationController *nav = vc.navigationController;
                if (nav) {
                    if (nav.viewControllers.count > 1) {
                        if ([nav.viewControllers.lastObject isEqual:vc]) {
                            [nav popViewControllerAnimated:YES];
                        }
                    } else {
                        [nav dismissViewControllerAnimated:YES completion:nil];
                    }
                } else {
                    [vc dismissViewControllerAnimated:YES completion:nil];
                }
            }
        }
    });
}

///跳转训练报告
- (void)skipReportVC:(MRKUnsettleSportDataModel *)item{
    @weakify(self);
    jxt_getSafeMainQueue(^{
        @strongify(self);
        [self removeContinueTrainAlert:YES];
        UIViewController *top = [UIViewController currentViewController];
        ExerciseReportWebController *vc = [[ExerciseReportWebController alloc] init];
        vc.courseType = [NSString stringWithFormat:@"%ld", (long)item.trainingType];
        vc.exerciseID = item.trainingRecordId;
        vc.equipmentId = item.equipmentInfoId;
        vc.fromTrainingView = YES;
        vc.firstInto = item.firstInto;
        vc.liveCourseModel = item.courseModel;
        vc.playTime = item.playTime ?:@(0);
        vc.quitMidway = item.exitOnPlaying; //课程是否中途结束训练
        [top.navigationController pushViewController:vc animated:YES];
    });
}


#pragma mark ————————————  移除继续训练弹窗————————————
- (void)removeContinueTrainAlert:(BOOL)animation {
    if (self.continueTrainAlert) {
        if (animation){
            [self.continueTrainAlert hide];
            return;
        }
        [self.continueTrainAlert removeFromSuperview];
        self.continueTrainAlert = nil;
    }
}

#pragma mark ————————————  选择 继续训练 还是结束运动 弹窗————————————
/// 1 运动中断开连接  0 不在运动中
- (void)showSettlementOrContinueTrainAlert:(MRKUnsettleSportDataModel *)item {
    [self removeContinueTrainAlert:NO];
    
    self.continueTrainAlert = [[MRKContinueTrainAlert alloc] init];
    self.continueTrainAlert.hiddenCloseBtn = item.scene == 1;
    self.continueTrainAlert.item = item;
    @weakify(self);
    self.continueTrainAlert.endTrainingBlock = ^{
        @strongify(self);
        [self dealCommon:item type:1];
    };
    self.continueTrainAlert.continueTrainingBlock = ^(ContinueConnectView * connectView) {
        @strongify(self);
        [self dealCommon:item type:2];
    };
    [self.continueTrainAlert showIn:UIViewController.tabbarView];
}

/// type 1 结束 2 继续
- (void)dealCommon:(MRKUnsettleSportDataModel *)item type:(NSInteger)type {
    @weakify(self);
    [self requestQueryUnsettleRecord:^(MRKUnsettleSportDataModel *it) {
        @strongify(self);
        if (it == nil || ![it.trainingRecordId isEqualToString:item.trainingRecordId]) {
            [self showAutoTrainCompleteAlert: item];
            return;
        }
        if (it.trainingIncrementData.totalTime > item.trainingIncrementData.totalTime) {/// 覆盖新值
            item.trainingTrackData = it.trainingTrackData;
            item.trainingIncrementData = it.trainingIncrementData;
        }
        /// 再次走判断九十分钟的逻辑
        NSString *settlementScene = [self isAutoSettlement:item];
        if (type == 1 || settlementScene) { /// 结束运动 - 结算
            [self requestSettlement:item trainingSettlementScene:settlementScene];
        } else { /// 继续运动
            [[MRKContinueTrainManager shared] continueTrain:item];
        }
    } failure:^{
        @strongify(self);
        /// 再次走判断九十分钟的逻辑
        NSString *settlementScene = [self isAutoSettlement:item];
        if (type == 1) { /// 结束运动 - 结算
            [self requestSettlement:item trainingSettlementScene:settlementScene];
        }
    }];
}

- (void)requestSettlement:(MRKUnsettleSportDataModel *)item trainingSettlementScene:(NSString * _Nullable)trainingSettlementScene{
    @weakify(self);
    [self requestSettlement:item trainingSettlementScene:trainingSettlementScene success:^(id data){
        @strongify(self);
        if (trainingSettlementScene){ /// 结算成功后的弹窗
            [self showAutoTrainCompleteAlert:item alertScene:trainingSettlementScene];
        }else{ /// 结算后直接跳转到报告页面
            [self skipReportVC:item];
        }
    } failure:^(NSInteger code) {
        @strongify(self);
        [self backVC:item];
    }];
}

#pragma mark ————————————  走自动结算的场景 ————————————
- (NSString *)isAutoSettlement:(MRKUnsettleSportDataModel *)item{
    if (item.trainingTrackData == nil) {
        return nil;
    }
    /// 大于90分钟自动结算
    if (([MRKToolKit current13TimeInterval] - item.trainingTrackData.timestamp.longValue) > 90 * 60 * 1000) {
        return @"102";
    }
    /// 小于1分钟自动结算，说明用户改时间了，直接结算
    if (([MRKToolKit current13TimeInterval] - item.trainingTrackData.timestamp.longValue) < -1 * 60 * 1000) {
        return @"";
    }
    return nil;
}

#pragma mark ————————————  查询用户是否存在未结算训练记录 ————————————
- (void)requestQueryUnsettleRecord:(void(^)(MRKUnsettleSportDataModel *))success failure:(void(^)(void))failure {
    [MRKBaseRequest mrkGetRequestUrl:@"/sport/training-record/dispatch-unsettled-record"
                             andParm:nil
            completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        id data = [request.responseObject valueForKeyPath:@"data"];
        MRKUnsettleSportDataModel *item = [MRKUnsettleSportDataModel modelWithDictionary:data];
        success(item);
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        failure();
    }];
}

/** trainingSettlementScene
 正常结算
 0/nil
 
 首页结算
 101

 断联结算
 102
 蓝牙断联超过 90 分钟 app 自动发起结算
 
 阈值结算（超过运动限时范围）
 110
 训练记录超8小时自动结算
 
 空练结算
 100
 未进入任何训练页面开始运动
 
 能力测评
 201
 完成“能力测评”
 
 游戏
 300
 监听游戏数据同步
 
 智能控阻
 210
 完成“自由训练-智控练”
 */
#pragma mark ————————————  补偿结算/正常结算接口 tykdh  ———————————
- (void)requestSettlement:(MRKUnsettleSportDataModel *)item
  trainingSettlementScene:(NSString * _Nullable)trainingSettlementScene
                  success:(void(^)(id))success
                  failure:(void(^)(NSInteger))failure{
    
    [self requestSettlement:item
    trainingSettlementScene:trainingSettlementScene
                hiddenError:NO
                    success:success
                    failure:failure];
}

- (void)requestSettlement:(MRKUnsettleSportDataModel *)item
  trainingSettlementScene:(NSString * _Nullable)trainingSettlementScene
              hiddenError:(BOOL)hiddenError
                  success:(void(^)(id))success
                  failure:(void(^)(NSInteger))failure{
    
    NSMutableDictionary *parms = @{
        @"trainingRecordId":item.trainingRecordId?:@"",
        @"trainingSettlementScene":trainingSettlementScene?:@"",///结算场景 [只要102]
        @"trainingType":@(item.trainingType)
    }.mutableCopy;
    parms[@"isReconnect"] = item.isReconnect;
    parms[@"clearableEquipment"] = item.clearableEquipment;
    parms[@"trainingTrackData"] = [item.trainingTrackData modelToJSONObject];
    parms[@"trainingIncrementData"] = [item.trainingIncrementData modelToJSONObject];
    ///实景训练
    if (item.trainingType == MRKTrainingTypeRealVideo) {
        parms[@"rank"] = item.rank?:@"";
        parms[@"isCountLiveVideoRank"] = item.isCountRank?@1:@0;
        parms[@"liveVideoMatchDuration"] = item.matchDuration?:@"";
    }
    
    [MRKBaseRequest mrkSilencePostRequestUrl:@"/user/training/normal-settlement"
                                     andParm:parms
                    completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        [[MRKSportDataSQLManager shared] clearSQL];
        id data = [request.responseObject valueForKeyPath:@"data"];
        ///健康数据写入
        [[MRKHealthManager shared] saveSportJsonData:data];
        success(data);
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        [[MRKSportDataSQLManager shared] saveTrainData];
        if (request.responseStatusCode != 500010000 && !hiddenError) {
            dispatch_async(dispatch_get_main_queue(), ^{
                NSString *tipStr = [request.responseObject valueForKeyPath:@"message"];
                [AppDelegate errorView:tipStr];
            });
        }
        NSInteger statusCode = [[request.responseObject valueForKeyPath:@"status"] integerValue];
        failure(statusCode);
    }];
}

#pragma mark ————————————  请求空练开关 设为全局存储 ————————————
- (void)requestUntrackedTrainingSwitch {
    [MRKBaseRequest mrkGetRequestUrl:@"/user/user-setting/get"
                             andParm:@{ @"type": @1 }
            completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        id data = [request.responseObject valueForKeyPath:@"data"];
        self.saveCacheData = [data boolValue];
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {}];
}

@end
