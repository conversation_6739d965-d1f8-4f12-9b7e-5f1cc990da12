//
//  BaseChannelNames.swift
//  Student_IOS
//
//  Created by merit on 2025/4/11.
//

import UIKit

class BaseChannelNames {
    static let  BASE_CHANNEL_NAME = "base_channel";
    static let  BASE_METHOD_POP = "base_channel_method_pop";
    static let  BASE_METHOD_OPEN_NATIVE_PAGE = "base_channel_method_open_native_page";
    static let  BASE_METHOD_FINISH_NATIVEPAGE = "base_channel_method_finish_nativepage";
    static let  BASE_METHOD_TRACK_CLICK_NATIVE = "base_channel_method_track_click_native";
    static let  BASE_METHOD_TRACK_EXPOSE_NATIVE = "base_channel_method_track_expose_native";
    static let  BASE_METHOD_LIFECYCLE = "base_channel_method_lifecycle";
}

class BaseMethodName{
    static let FINISH_PAGE = "finish_page";
    static let SEND_PARAM = "send_param";
    
    static let ON_NEXT_PAGE = "on_next_page";
    static let GET_PAGE_PARAM = "get_page_param";
    
    static let GET_VOICE_TEXT = "get_voice_text";
    static let FINISH_ARRIVED_60 = "finish_arrived_60";
    static let MOVE = "move";
    static let MOVE_CANCEL = "move_cancel";
    
    static let AI_DIET_PLAN_OPEN_PERION_AND_EXPAND = "ai_diet_plan_open_perion_and_expand";  ///开启本周并动态延长
    static let AI_DIET_PLAN_OPEN_PERION = "ai_diet_plan_open_perion"; ///AI动态开启本周计划
    static let CLOSE_AI_DIET_PLAN_DETAIL_PAGE = "close_ai_diet_plan_detail_page"; ///关闭计划详情页面
    static let AI_DIET_PLAN_TODAY_TASK_COMPLETED = "ai_diet_plan_today_task_completed"; ///AI计划今日完成

}
