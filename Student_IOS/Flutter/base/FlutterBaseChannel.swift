//
//  FlutterBaseChannel.swift
//  Student_IOS
//
//  Created by merit on 2025/4/7.
//

import UIKit
/// flutter交互通道
class FlutterBaseChannel: NSObject {
    deinit {
        print("😊😊😊-----dealloc-----😊😊😊\(type(of: self))")
    }
    
    override init() {}
    var baseChannel : FlutterMethodChannel?  // 基础channel
    var handleBlock : FlutterMethodCallHandler? // 抛给业务的回调
    var _title : String?
    
    func initChannel(engine: FlutterEngine, title: String?) -> FlutterBaseChannel {
        _title = title
        baseChannel = FlutterMethodChannel(name: BaseChannelNames.BASE_CHANNEL_NAME, binaryMessenger: engine.binaryMessenger)
        baseChannel?.setMethodCallHandler({[weak self] call, reslt in
            self?.handlePageChannel(call, reslt)
        })
        return self
    }
    
    func handlePageChannel(_ call: FlutterMethodCall, _ result: @escaping FlutterResult) {
        // 通用channel 回调实现
        FlutterBaseChannelHandle.shared.handleMethodCallWithToken(_title ?? "" ,call, result, nil)
        // 特殊回调，抛给业务实现
        if let block = handleBlock {
            block(call, result);
        }
    }
    
    func invokeMethod(method: String, arguments: Any?, result: ((Any) -> Void)? = nil) {
        SerialAsyncQueue.queue.addTask { [weak self] completion in
//            self?.baseChannel?.invokeMethod(method, arguments: arguments)
            self?.baseChannel?.invokeMethod(method, arguments: arguments, result: result)
            DispatchQueue.global().asyncAfter(deadline: .now() + 0.4) {
                completion()
            }
        }
    }
    
    class func invokeMethod(channle:FlutterMethodChannel, method: String, arguments: Any) {
        SerialAsyncQueue.queue.addTask { completion in
            channle.invokeMethod(method, arguments: arguments)
            DispatchQueue.global().asyncAfter(deadline: .now() + 0.4) {
                completion()
            }
        }
    }
    
}

/// base channel 交互方法的实现
class FlutterBaseChannelHandle: NSObject {
    
    static let shared = FlutterBaseChannelHandle()
    func handleMethodCallWithToken(_ title: String, _ call: FlutterMethodCall, _ result: @escaping FlutterResult, _ handler: ((FlutterMethodCall, @escaping FlutterResult) -> Void)?) {
        // 打开页面
        if call.method == BaseChannelNames.BASE_METHOD_OPEN_NATIVE_PAGE {
            let dic = call.arguments as? [String:Any]
            guard let name = dic?["name"] as? String else {
                return
            }
            
            ///< VIP会员
            if name == NativePageNames.VIP {
                RouteManager.sharedInstance().skipVIP()
            }
            ///< 意见反馈
            else if name == NativePageNames.FEEDBACK {
                RouteManager.sharedInstance().skipServiceWeb()
            }
            /// 饮食记录
            else if name == NativePageNames.FOOD_RECORD_LIST {
                var dateString = ""
                var type = 0
                if let data = dic?["data"] as? [String: Any]{
                    dateString = data["date"] as? String ?? ""
                    type = data["type"] as? Int ?? 0
                }
                MRKDailyLogic.shared()?.jump(toDietRecord: dateString, index: type)
            }
            /// 健康类型
            else if name == NativePageNames.AI_FOOD_TRAIN_TYPE {
                MRKDailyLogic.shared()?.jumpSelectActivityTypeCodePage()
            }
            /// 图片识别
            else if name == NativePageNames.FOOD_SCAN_CAMERA {
                var dateString = ""
                if let data = dic?["data"] as? [String: String],
                   let date = data["date"] {
                    dateString = date
                }
                MRKDailyLogic.shared()?.jump(toImageRecognition: dateString)
            }
            /// 文字识别
            else if name == NativePageNames.FOOD_SCAN_TEXT {
                var dateString = ""
                if let data = dic?["data"] as? [String: String],
                   let date = data["date"] {
                    dateString = date
                }
                MRKDailyLogic.shared()?.jump(toTextRecognition: dateString)
            }
            /// 饮食确认页
            else if name == NativePageNames.FOOD_CONFIRM {
                let vc = MRKFlutterFoodConfirmViewController()
                var recognitionType = 1;
                if let data = dic?["data"] as? [String: Any] {
                    vc.data = data
                    recognitionType = (data["recognitionType"] as? Int) ?? 1;
                }
                
                if recognitionType == 2 {
                    UIViewController.current()?.dismiss(animated: true) {
                        UIViewController.current()?.navigationController?.pushViewController(vc, animated: true)
                    }
                }else {
                    UIViewController.current()?.navigationController?.pushViewController(vc, animated: true)
                }
            }
            else if name == NativePageNames.AI_PLAN_TASK_VOICE_ADJUST {
                if let data = dic?["data"] as? [String: Any], let type = data["type"] as? Int{
                    MRKAIPlanLogic.shared()?.jump(toAIPlanAdjustPage:Int32(type))
                }
            }
            // 动作搜索
            else if name == NativePageNames.MOTION_SEARCH {
                let vc = MRKFlutterMotionSearchController()
                vc.hidesBottomBarWhenPushed = true
                UIViewController.current()?.navigationController?.pushViewController(vc, animated: true)
            }
            /// 动作详情
            else if name == NativePageNames.MOTION_DETAIL {
                if let data = dic?["data"] as? [String: String],
                   let motionId = data["id"] {
                    let trainTargetValue = data["trainTargetValue"] ?? ""
                    let vc = FlutterManager.motionDetail(motionId, trainTargetValue: trainTargetValue)
                    vc.hidesBottomBarWhenPushed = true
                    UIViewController.current()?.navigationController?.pushViewController(vc, animated: true)
                }
            }
            /// 体重历史记录
            else if name == NativePageNames.WEIGHT_RECORDS {
                RouteManager.sharedInstance().skipWeightHistoryVC()
            }
            /// 体重管理，体脂秤banner点击
            else if name == NativePageNames.BANNER_ITEM_CLICK {
                if let data = dic?["data"] as? [String: Any], let jsonStr = data["data"] as? String {
                    print(jsonStr)
                    RouteManager.sharedInstance().skipAdvertModel(withJsonStr: jsonStr)
                }
            }
            /// AIDietPlan分享
            else if name == NativePageNames.AIPLAN_DETAIL_SHARE {
                if let data = dic?["data"] as? [String: Any],
                   let description = data["description"] as? String {
                    ///
                    let version = data["version"] as? Int ?? 2
                    let dict: [String: Any] = [
                        "description": description,
                        "version": version
                    ]
                    FlutterManager.shareAiPlanDetailVC(dic: dict)
                } else {
                    print("AIPLAN_DETAIL_SHARE 参数缺失或格式错误")
                }
            }
            //. AIDietPlan 指南页面
            else if name == NativePageNames.AI_RULE {
                // 打开AI规则说明页面，支持不同版本
                 var versionType = 1
                 if let data = dic?["data"] as? [String: Any],
                    let version = data["version"] as? Int {
                     versionType = version
                 }
                 // 选择不同的H5页面
                 let guidePath = (versionType == 2) ? MRKAIUsageGuideV2 : MRKAIUsageGuide
                 let uri = MRKSwiftUseOCHelp.mrkAppH5AILinkCombineSwift(guidePath)
                 print("AI规则说明页面地址: \(uri)")
                 // 跳转到web页面，隐藏导航栏
                 RouteManager.sharedInstance().skipWeb(uri, hiddenNav: true)
            }
            /// 课程详情
            else if name == NativePageNames.COURSE_DETAIL {
                if let data = dic?["data"] as? [String: String],
                   let courseId = data["courseId"] {
                    let trainingTargetValue = data["trainTargetValue"] ?? ""
                    if trainingTargetValue == "1" {
                        RouteManager.sharedInstance().jumpToPlanCourseDetail(withId: courseId)
                    } else {
                        RouteManager.sharedInstance().jumpToCourseDetail(withId: courseId)
                    }
                } else {
                    print("COURSE_DETAIL 参数缺失或格式错误")
                }
            }
            /// AIDietPlan换一换
            else if name == NativePageNames.AI_PLAN_TASK_ADJUST {
                if let data = dic?["data"] as? [String: Any]{
                    let vc = MRKFlutterAIPlanAdjustAlertController()
                    vc.dict = data
                    vc.modalPresentationStyle = .overFullScreen
                    vc.modalTransitionStyle = .crossDissolve
                    UIViewController.current()?.present(vc, animated: true)
                } else {
                    print("AI_PLAN_TASK_ADJUST 参数缺失或格式错误")
                }
            }
            /// AIDietPlan 饮食记录弹窗
            else if name == NativePageNames.AI_DIET_PLAN_RECORD_ALERT {
                if let data = dic?["data"] as? [String: Any],
                   let date = data["date"] as? String {
                    MRKAIPlanLogic.shared()?.recordDiet(date, fromMainPage: false)
                }
            }
            /// AIDietPlan 饮食分析
            else if name == NativePageNames.AI_DIET_PLAN_ANALYSIC_PAGE {
                if let data = dic?["data"] as? [String: Any],
                   let date = data["date"] as? String {
                  
                    MRKDailyLogic.shared()?.jump(toDietSportAnalysis: date)
                } else {
                    print("AI_DIET_PLAN_ANALYSIC_PAGE 参数缺失或格式错误")
                }
            }
            /// AIDietPlan 详情页面
            else if name == NativePageNames.AI_DIET_PLAN_DETAIL_PAGE {
                if let data = dic?["data"] as? [String: Any],
                   let planId = data["planId"] as? String {
                  
                    MRKAIPlanLogic.shared()?.jump(toAIPlanDetailPage: planId)
                } else {
                    print("AI_DIET_PLAN_DETAIL_PAGE 参数缺失或格式错误")
                }
            }
            /// AIDietPlan 重新制定
            else if name == NativePageNames.AI_DIET_PLAN_REGENERATE {
                
//                ///进入之前关闭之前的详情页
//                if let navigationController = UIViewController.current()?.navigationController {
//                    var viewControllers = navigationController.viewControllers
//                    if let index = viewControllers.firstIndex(where: { $0 is MRKFlutterAIDietPlanDetailController}) {
//                        viewControllers.remove(at: index)
//                        navigationController.viewControllers = viewControllers
//                    }
//                }
//                
                MRKAIPlanLogic.shared()?.jumpToAIPlanStartPage()
            }
            /// AIDietPlan 重置问卷
            else if name == NativePageNames.AI_DIET_PLAN_RESET_QUESTIONNAIRE_PAGE {
                if let data = dic?["data"] as? [String: Any],
                   let planId = data["planId"] as? String {
                
                    MRKAIPlanLogic.shared()?.jump(toAIPlanResetPage: planId)
                } else {
                    print("AI_DIET_PLAN_RESET_QUESTIONNAIRE_PAGE 参数缺失或格式错误")
                }
            }
        }
        /// 埋点点击
        else if call.method == BaseChannelNames.BASE_METHOD_TRACK_CLICK_NATIVE {
            guard let dic = call.arguments as? [String:Any] else {
                return
            }
            
            /// 触发埋点
            guard let model = FlutterTrackModel.model(withJSON:dictionaryToJSON(dic)) else {
                return
            }
            
            MRKTraceManager.sharedInstance().manualUploadTraceType(2, pageTitle: title, pageId: model.pageId, eventId: model.eventCode, route: nil, duration: 0, extendPara: model.data)
        }
        /// 埋点曝光
        else if call.method == BaseChannelNames.BASE_METHOD_TRACK_EXPOSE_NATIVE {
            guard let dic = call.arguments as? [String:Any] else {
                return
            }
            
            /// 触发埋点
            guard let model = FlutterTrackModel.model(withJSON:dictionaryToJSON(dic)) else {
                return
            }
            
            MRKTraceManager.sharedInstance().manualUploadTraceType(1, pageTitle: title, pageId: model.pageId, eventId: model.eventCode, route: nil, duration: model.duration, extendPara: model.data)
        }
    }
}

