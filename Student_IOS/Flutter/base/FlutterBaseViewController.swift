//
//  FlutterBaseViewController.swift
//  Student_IOS
//
//  Created by merit on 2025/4/7.
//

import UIKit

class FlutterBaseViewController: MRKFlutterViewController {
    
    var channel : FlutterBaseChannel?   //基本的公用channel
    private var engineStr : String?     //引擎名称
    
    deinit {
        // 删除引擎
        if let str = engineStr{
            FlutterBaseEntry.entryEngine.removeValue(forKey: str)
//            FlutterEntry.removeEntryEngine(entryPoint: str)
        }
        print("😊😊😊-----dealloc-----😊😊😊\(type(of: self))")
    }
    required init(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    init(withEntrypoint entryPoint: String, router: String = "/") {
        engineStr = entryPoint
        let engine = FlutterBaseEntry.getEntryEngine(entryPoint: entryPoint, router)
        super.init(engine: engine, nibName: nil, bundle: nil)
        // 初始化公用的channel
        if let engin = self.engine {
            channel = FlutterBaseChannel().initChannel(engine: engin, title: self.title)
            channel?.handleBlock = ({[weak self] call, reslt in
                self?.handleChannel(call, reslt)
            })
        }
    }
    
    func setupChannelViewWillAppear() {
        channel?.invokeMethod(method: BaseChannelNames.BASE_METHOD_LIFECYCLE, arguments: ["lifecycleType":"onResume"])
    }
    
    func setupChannelViewWillDisappear() {
        channel?.invokeMethod(method: BaseChannelNames.BASE_METHOD_LIFECYCLE, arguments: ["lifecycleType":"onPause"])
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()

        // Do any additional setup after loading the view.
        view.backgroundColor = UIColor(hexString: "#F8F8FA")
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
      
    }
    
    // flutter 所需要的参数，子类实现
    func pageParam() -> Any{
        return [:]
    }
    
    func finsh(_ params: [String: Any]? = nil) {
        DispatchQueue.main.async {
            if let params = params, let isPresented = params["isPresented"] as? Bool, isPresented {
                self.dismiss(animated: true)
            } else {
                self.navigationController?.popViewController(animated: true)
            }
            print("😊😊😊-----页面关闭-----😊😊😊\(type(of: self))")
        }
    }
    
    /// 子类实现自己业务
    func handleChannel(_ call: FlutterMethodCall, _ result: @escaping FlutterResult) {
        // 获取当前flutter所需要的 参数
        if call.method == BaseMethodName.GET_PAGE_PARAM {
            let param = pageParam()
            result(param)
        }
        // 关闭页面
        else if call.method == BaseChannelNames.BASE_METHOD_FINISH_NATIVEPAGE {
            let dic = call.arguments as? [String:Any]
            finsh(dic)
        }
    }

}




