//
//  NativePageNames.swift
//  Student_IOS
//
//  Created by merit on 2024/1/16.
//
class NativePageNames {
    static let VIP = "vip"
    static let FEEDBACK = "feedback"
    static let BODYFATHISTORY = "bodyfathistory"
    static let BODYFATREPORT = "bodyfatreport"
    static let BODYFATDETAIL = "bodyfatdetail"
    static let SPORT_REPORT = "sportreport"
    static let COACH_PAGE = "coach_page"
    static let MOTION_SEARCH = "motion_search"
    static let MOTION_DETAIL = "motion_detail"
    static let MOTION_PLAY = "motion_play"
    static let AIPLAN_TRAIN_DONE = "aiplan_train_done"
    static let AI_PLAN_LIST = "ai_plan_list"
    static let COURSE_DETAIL = "course_detail"
    static let AI_RULE = "ai_rule"
    static let FOOD_RECORD_LIST = "food_record_list"
    static let FOOD_SCAN_CAMERA = "food_scan_camera"
    static let FOOD_SCAN_TEXT = "food_scan_text"
    static let AI_FOOD_TRAIN_TYPE = "ai_food_train_type"
    static let FOOD_CONFIRM = "food_confirm"
    static let AI_PLAN_TASK_ADJUST = "ai_plan_task_adjust"
    static let AI_PLAN_TASK_VOICE_ADJUST = "voice_identify"
    static let WEIGHT_RECORDS = "weight_records" ///< 体重历史记录
    static let BANNER_ITEM_CLICK = "banner_item_click" ///< 体重管理banner点击
    static let AIPLAN_DETAIL_SHARE = "aiplan_channel_share"
    static let AI_DIET_PLAN_RECORD_ALERT = "ai_diet_plan_record_alert" ///<
    static let AI_DIET_PLAN_ANALYSIC_PAGE = "ai_diet_plan_analysic_page" ///<
    static let AI_DIET_PLAN_DETAIL_PAGE = "ai_diet_plan_detail_page"
    static let AI_DIET_PLAN_RESET_QUESTIONNAIRE_PAGE = "ai_diet_plan_reset_questionnaire_page" ///问卷调查
    static let AI_DIET_PLAN_REGENERATE = "ai_diet_plan_regenrate" ///重新制定 【网页】
    static let AI_DIET_PLAN_VIP_OPEN_PAGE = "ai_diet_plan_vip_open_page" ///AI计划开通vip中间页
    static let AI_DIET_VIP_OPEN_PAGE = "ai_diet_vip_open_page" ///AI计划开通vip中间页
    static let NATIVE_MAIN_PAGE = "native_main_page" ///返回native的主页面
    static let AI_DIET_PLAN_RECIPE_PAGE = "ai_diet_plan_recipe_page" ///饮食食谱页面
}


//enum NativePageNames: String {
//    case DEFAULT = ""
//    case VIP = "vip"
//    case FEEDBACK = "feedback"
//    case BODYFATHISTORY = "bodyfathistory"
//    case BODYFATREPORT = "bodyfatreport"
//    case BODYFATDETAIL = "bodyfatdetail"
//    case SPORT_REPORT = "sportreport"
//    case COACH_PAGE = "coach_page"
//    case MOTION_SEARCH = "motion_search"
//    case MOTION_DETAIL = "motion_detail"
//    case MOTION_PLAY = "motion_play"
//    case AIPLAN_TRAIN_DONE = "aiplan_train_done"
//
//    ///允许不列举完所有值
//    init(rawValue: String) {
//        self = NativePageNames(rawValue: rawValue) ?? .DEFAULT
//    }
//}
