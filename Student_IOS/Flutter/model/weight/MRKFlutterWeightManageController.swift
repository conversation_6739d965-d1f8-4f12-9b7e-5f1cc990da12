//
//  MRKFlutterWeightManageController.swift
//  Student_IOS
//
//  Created by <PERSON><PERSON> on 2025/7/1.
//

import UIKit

class MRKFlutterWeightManageController: FlutterBaseViewController {
    var data :[String: Any]?
    
    required init(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    @objc init() {
        super.init(withEntrypoint: EntryNames.ENTRY_WEIGHT_MANAGE_MAIN, router: "/")
    }
    
    override func finsh(_ params: [String: Any]? = nil) {
        self.navigationController?.popViewController(animated: true)
    }
    
    override func pageParam() -> Any {
        guard let d = data else { return [:] }
        return ["data":d]
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        self.fd_interactivePopDisabled = true
    }
    
    override func viewWillDisappear(_ animated: <PERSON><PERSON>) {
        super.viewWillDisappear(animated)
        self.fd_interactivePopDisabled = false
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
    }
}
