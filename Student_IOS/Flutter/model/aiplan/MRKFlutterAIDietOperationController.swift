//
//  MRKFlutterAIDietOperationController.swift
//  Student_IOS
//
//  Created by <PERSON><PERSON> on 2025/7/9.
//

import UIKit

///< 这个页面是个dialog，用来操作V2版本饮食计划的，周计划的开启和延长
class MRKFlutterAIDietOperationController: FlutterBaseViewController {
    @objc var operatePlanType: NSNumber?   ///< 0开启计划，1是延长计划
    @objc var startMode: NSNumber?   ///< 1立即开始，2是从明天开始
    
    required init(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    @objc init() {
        super.init(withEntrypoint: EntryNames.ENTRY_AI_DIET_PLAN_OPERATION_MAIN, router: "/")
    }
    
    override func finsh(_ params: [String: Any]? = nil) {
        if let dict = params?["finish"] as? [String: Any], let str = dict["operator"] as? String, str == "openNextPeriodSuccess" {
            NotificationCenter.default.post(name: NSNotification.Name("kAIPlanOpenNextPeriodSuccessNotification"), object: nil, userInfo: dict);
        }
        self.dismiss(animated: false)
    }
    
    
    override func pageParam() -> Any? {
        guard let operatePlanType = operatePlanType else { return nil}
        return [
            "operatePlanType":operatePlanType,
            "startMode":startMode ?? NSNumber(value: 1)
        ]
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
    }
    
    deinit {
       
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = .clear
    }

    func handlePageChannel(_ call: FlutterMethodCall, _ result: @escaping FlutterResult) {
        
    }
}
