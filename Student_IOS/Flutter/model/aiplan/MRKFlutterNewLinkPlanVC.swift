//
//  MRKFlutterNewLinkPlanVC.swift
//  Student_IOS
//
//  Created by merit on 2025/8/5.
//

import Foundation
import UIKit

class MRKFlutterNewLinkPlanVC: FlutterBaseViewController {
    @objc var productId: NSNumber?   //类型ID
    @objc var type: NSNumber? // 四大件 1 其他 0
    
    required init(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    @objc init() {
        super.init(withEntrypoint: EntryNames.ENTRY_NEW_PERSON_AI_PLAN_MAIN, router: "/")
    }
    
    override func finsh(_ params: [String: Any]? = nil) {
//        self.navigationController?.popViewController(animated: true)
        self.navigationController?.popViewController(animated: true, removeControllerClasses: [MRKFlutterNewLinkPlanVC.self], removePolicy: .newest)
    }
    
    override func pageParam() -> Any {
        guard let productId = productId else { return [:]}
        return ["productId":productId, "type": type ?? NSNumber(value: 0) ]
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        self.fd_interactivePopDisabled = true
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        self.fd_interactivePopDisabled = false
    }
    
    deinit {
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
    }
    
   
    override func handleChannel(_ call: FlutterMethodCall, _ result: @escaping FlutterResult) {
        super.handleChannel(call, result)
        
    }
}
