//
//  MRKFlutterAIPlanAdjustAlertController.swift
//  Student_IOS
//
//  Created by merit on 2025/5/14.
//

import UIKit

class MRKFlutterAIPlanAdjustAlertController: FlutterBaseViewController {
    @objc var dict:[String: Any] = [:]
    
    required init(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    @objc init() {
        super.init(withEntrypoint: EntryNames.ENTRY_AI_PLAN_ADJUST_MAIN, router: "/ai-plan/ai-plan-adjust-dialog")
    }
    
    override func finsh(_ params: [String: Any]? = nil){
        if let dict = params?["finish"] as? [String: Any], let str = dict["operator"] as? String, str == "adjustTaskSuccess" {
            NotificationCenter.default.post(name: NSNotification.Name("kAIPlanAdjustSuccessNotification"), object: nil, userInfo: dict);
        }
        self.dismiss(animated: true)
    }
    
    override func pageParam() -> Any {
        return dict
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = .clear

        // Do any additional setup after loading the view.
    }

    deinit {
        
    }
}
