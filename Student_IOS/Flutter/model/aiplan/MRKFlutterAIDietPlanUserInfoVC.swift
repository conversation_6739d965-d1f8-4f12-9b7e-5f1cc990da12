//
//  MRKFlutterAIDietPlanUserInfoVC.swift
//  Student_IOS
//
//  Created by merit on 2025/9/10.
//

import UIKit

class MRKFlutterAIDietPlanUserInfoVC: FlutterBaseViewController {

    @objc var fromSource = ""
    @objc var planId = ""
    
    override func viewDidLoad() {
        super.viewDidLoad()
    }
    
    // flutter 所需要的参数，子类实现
    override func pageParam() -> Any{
        return ["fromSource": fromSource, "planId": planId]
    }
    
    @objc init() {
        super.init(withEntrypoint: EntryNames.ENTRY_USER_INFO_V3_ENTER_MAIN, router: "")
    }
    
    override func finsh(_ params: [String: Any]? = nil) {
        self.navigationController?.popViewController(animated: true, removeControllerClasses: [MRKFlutterAIDietPlanUserInfoVC.self], removePolicy: .newest)
        ///更新用户信息
        UserInfo.update()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        self.fd_interactivePopDisabled = true
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        self.fd_interactivePopDisabled = false
    }
    
    required init(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func handleChannel(_ call: FlutterMethodCall, _ result: @escaping FlutterResult) {
        super.handleChannel(call, result)
        if call.method == BaseMethodName.CLOSE_AI_DIET_PLAN_DETAIL_PAGE  {
            ///避免和finsh竞争设置navigationController.viewControllers
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                /// 进入start页面。 移除之前的入口网页和详情页
                if let navigationController = self.navigationController {
                    var viewControllers = navigationController.viewControllers
                    if let index = viewControllers.firstIndex(where: { $0 is MRKFlutterAIDietPlanDetailController}) {
                        viewControllers.remove(at: index)
                    }
                    ///
                    if let index = viewControllers.firstIndex(where: { $0 is FlutterAIPlanDetailViewController}) {
                        viewControllers.remove(at: index)
                    }
                    navigationController.viewControllers = viewControllers
                }
            }
        }
    }

}
