//
//  MRKFlutterAIDietPlanQuitController.swift
//  Student_IOS
//
//  Created by <PERSON><PERSON> on 2025/7/8.
//

import UIKit

class MRKFlutterAIDietPlanQuitController: FlutterBaseViewController {
    @objc var planId: String?   //计划ID
    
    required init(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    @objc init() {
        super.init(withEntrypoint: EntryNames.ENTRY_AI_DIET_PLAN_RESET_QUESTIONNAIRE_MAIN, router: "/")
    }
    
    override func finsh(_ params: [String: Any]? = nil) {
        
//        if let viewControllers = self.navigationController?.viewControllers {
//            for (index, vc) in viewControllers.enumerated() {
//                print("finsh前[\(index)]: \(NSStringFromClass(type(of: vc)))")
//            }
//        } else {
//            print("⚠️ 当前没有 navigationController 或 viewControllers")
//        }
        self.navigationController?.popViewController(animated: true, removeControllerClasses: [MRKFlutterAIDietPlanQuitController.self], removePolicy: .newest)
  
    }
    
    override func pageParam() -> Any {
        guard let planId = planId else { return [:] }
        return ["planId":planId]
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
    }
    
    deinit {
       
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
    }

    override func handleChannel(_ call: FlutterMethodCall, _ result: @escaping FlutterResult) {
        super.handleChannel(call, result)
    }
}
