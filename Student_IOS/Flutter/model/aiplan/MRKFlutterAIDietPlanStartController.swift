//
//  MRKFlutterAIDietPlanStartController.swift
//  Student_IOS
//
//  Created by <PERSON><PERSON> on 2025/7/7.
//

import UIKit

class MRKFlutterAIDietPlanStartController: FlutterBaseViewController {
    
    required init(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    @objc init() {
        super.init(withEntrypoint: EntryNames.ENTRY_AI_PLAN, router: "/")
    }
    
    override func finsh(_ params: [String: Any]? = nil) {
//        self.navigationController?.popViewController(animated: true)
        self.navigationController?.popViewController(animated: true, removeControllerClasses: [MRKFlutterAIDietPlanStartController.self], removePolicy: .newest)
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        
        ///避免和finsh竞争设置navigationController.viewControllers
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            /// 进入start页面。 移除之前的入口网页和详情页
            if let navigationController = self.navigationController {
                var viewControllers = navigationController.viewControllers
                if let webViewControllerClass = NSClassFromString("WebViewViewController"),
                   let index = viewControllers.firstIndex(where: { $0.isKind(of: webViewControllerClass) }) {
                    viewControllers.remove(at: index)
                }
                
                ///
                if let index = viewControllers.firstIndex(where: { $0 is MRKFlutterAIDietPlanDetailController}) {
                    viewControllers.remove(at: index)
                }
                
                ///
                if let index = viewControllers.firstIndex(where: { $0 is FlutterAIPlanDetailViewController}) {
                    viewControllers.remove(at: index)
                }
                
                navigationController.viewControllers = viewControllers
            }
        }
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        self.fd_interactivePopDisabled = true
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        self.fd_interactivePopDisabled = false
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = UIColor(hexString: "05182C")
    }
   
    override func handleChannel(_ call: FlutterMethodCall, _ result: @escaping FlutterResult) {
        super.handleChannel(call, result)
        if (call.method == FlutterChannel.finsh(channelName: ChannelNames.AI_CHANNEL)) {
            ///移除掉当前控制器
            self.navigationController?.popViewController(animated: true)
        }
    }
}
