//
//  MRKFlutterAIDietPlanDetailController.swift
//  Student_IOS
//
//  Created by <PERSON><PERSON> on 2025/7/6.
//

import UIKit

class MRKFlutterAIDietPlanDetailController: FlutterBaseViewController {
    @objc var planId: String?   //计划ID
    @objc var isNewUserLink: Bool = false
    
    required init(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    @objc init() {
        super.init(withEntrypoint: EntryNames.ENTRY_AI_DIET_PLAN_DETAIL_MAIN, router: "/")
    }
    
    override func finsh(_ params: [String: Any]? = nil) {
        self.navigationController?.popViewController(animated: true)
    }
    
    override func pageParam() -> Any {
        guard let planId = planId else { return [:]}
        return ["planId":planId, "isNewUserLink": isNewUserLink]
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        self.fd_interactivePopDisabled = true
        setupChannelViewWillAppear()
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        self.fd_interactivePopDisabled = false
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = UIColor.black // 必须加，引导页底色
        NotificationCenter.default.addObserver(self, selector: #selector(adjustSuccess(_:)), name:NSNotification.Name("kAIPlanAdjustSuccessNotification"), object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(adjustSuccess(_:)), name:NSNotification.Name("kRefreshDailyDietNotification"), object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(adjustSuccess(_:)), name:NSNotification.Name("kAIPlanOpenNextPeriodSuccessNotification"), object: nil)
    }
    
    @objc func adjustSuccess(_ noti: Notification){
        channel?.invokeMethod(method: BaseMethodName.SEND_PARAM, arguments: noti.userInfo)
    }
    
    @objc func refreshDailyDiet(_ noti: Notification){
        setupChannelViewWillAppear()
    }
   
    override func handleChannel(_ call: FlutterMethodCall, _ result: @escaping FlutterResult) {
        super.handleChannel(call, result)
        if call.method == BaseMethodName.AI_DIET_PLAN_OPEN_PERION_AND_EXPAND  {
            ///开启本周并动态延长
            MRKAIPlanLogic.shared()?.startAndExtendCurrentWeek()
        }
        else if call.method == BaseMethodName.AI_DIET_PLAN_OPEN_PERION  {
            ///AI动态开启本周计划
            MRKAIPlanLogic.shared()?.dynamicStartCurrentWeek()
        }
    }
}
