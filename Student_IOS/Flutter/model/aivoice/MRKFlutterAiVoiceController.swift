//
//  MRKFlutterAiVoiceController.swift
//  Student_IOS
//
//  Created by merit on 2025/5/16.
//  首页 AI 语音

import UIKit

class MRKFlutterAiVoiceController: FlutterBaseViewController {
    
    var finshVoice = false
    @objc var voiceText: ((String) -> Void)?
    required init(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    @objc init() {
        super.init(withEntrypoint: EntryNames.ENTRY_AI_VOICE_MAIN, router: "")
    }
    
    override func finsh(_ params: [String: Any]? = nil){
        
    }
    
    override func pageParam() -> Any {
        let bottom = Int(tabBarHeight() + 14)
        return ["bottom": bottom, "right": 16 + 52]
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
//        self.fd_interactivePopDisabled = true
    }
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
//        self.fd_interactivePopDisabled = false
        self.cancelVoice()
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        self.tracePageId = "page_home_voice_input"
        
        view.backgroundColor = .clear
        transitioningDelegate = self
        // Do any additional setup after loading the view.
    }
    
    
    override func handleChannel(_ call: FlutterMethodCall, _ result: @escaping FlutterResult) {
        super.handleChannel(call, result)
        if call.method == BaseMethodName.FINISH_ARRIVED_60 {
            print("FINISH_ARRIVED_60-123 ：\(String(describing: call.arguments))")
            finshVoice = true
            guard let dic = call.arguments as? [String:Any] else {
                voiceText?("")
                return
            }
            if let content = dic["text"] as? String {
                voiceText?(content)
            }
        }
    }
    
    // 取消语音
    @objc func cancelVoice() {
        channel?.invokeMethod(method: BaseMethodName.MOVE_CANCEL, arguments: nil)
    }
    
    //获取语音转文字
    @objc func getVoiceToText() {
        if finshVoice {
            return
        }
        channel?.invokeMethod(method: BaseMethodName.GET_VOICE_TEXT, arguments: nil) {[weak self] str in
            print("getVoiceToText ：\(str)")
            if let dic = str as?[String:String], let content = dic["text"] {
                self?.voiceText?(content)
            }else {
                self?.voiceText?("")
            }
        }
    }
    
    // 上滑
    @objc func moveVoice(up: Bool) {
        print("moveVoicemoveVoice ：\(up)")
        channel?.invokeMethod(method: BaseMethodName.MOVE, arguments: ["moveStatus": up ? "up" : "down"] )
        if up {
            ReportMrkLogParms(2, "滑动取消语音输入", "page_home_voice_input", "swipe_cancel_voice", nil, 0, nil)
        }
    }
    
    
}

extension MRKFlutterAiVoiceController : UIViewControllerTransitioningDelegate{
//    // 返回Present动画控制器
//    func animationController(forPresented presented: UIViewController,
//                             presenting: UIViewController,
//                             source: UIViewController) -> UIViewControllerAnimatedTransitioning? {
//        return FadeTransitionAnimator(transitionType: .present)
//    }
//    
    // 返回Dismiss动画控制器
    func animationController(forDismissed dismissed: UIViewController) -> UIViewControllerAnimatedTransitioning? {
        return FadeTransitionAnimator(transitionType: .dismiss)
    }
}


// 渐变转场动画类
class FadeTransitionAnimator: NSObject, UIViewControllerAnimatedTransitioning {
    // 控制是Present还是Dismiss动画
    enum TransitionType {
        case present
        case dismiss
    }
    
    let transitionType: TransitionType
    
    init(transitionType: TransitionType) {
        self.transitionType = transitionType
        super.init()
    }
    
    // 动画时长
    func transitionDuration(using transitionContext: UIViewControllerContextTransitioning?) -> TimeInterval {
        return 0.3
    }
    
    // 执行动画
    func animateTransition(using transitionContext: UIViewControllerContextTransitioning) {
        guard let fromVC = transitionContext.viewController(forKey: .from),
              let toVC = transitionContext.viewController(forKey: .to)
        else { return }
        
        let containerView = transitionContext.containerView
        let duration = transitionDuration(using: transitionContext)
        
        if transitionType == .present {
            // 呈现逻辑
            
            toVC.view.alpha = 0.0
            
            UIView.animate(withDuration: duration, animations: {
                containerView.addSubview(toVC.view)
                toVC.view.alpha = 1.0
            }) { completed in
                transitionContext.completeTransition(completed)
            }
        } else {
            UIView.animate(withDuration: duration, animations: {
                fromVC.view.alpha = 0.0
            }) { completed in
                transitionContext.completeTransition(!transitionContext.transitionWasCancelled)
            }
        }
    }
}
