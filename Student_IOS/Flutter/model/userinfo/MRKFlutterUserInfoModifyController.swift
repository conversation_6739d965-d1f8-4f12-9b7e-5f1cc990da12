//
//  MRKFlutterUserInfoModifyController.swift
//  Student_IOS
//
//  Created by merit on 2025/4/28.
//

import UIKit

class MRKFlutterUserInfoModifyController: FlutterBaseViewController {

    override func viewDidLoad() {
        super.viewDidLoad()
    }
    
    // flutter 所需要的参数，子类实现
    override func pageParam() -> Any{
        return ["fromPage": "PlanFood"]
    }
    
    @objc init() {
        super.init(withEntrypoint: EntryNames.ENTRY_AI_USER_INFO_MAIN, router: "/ai-plan/ai-user-info-new")
    }
    
    override func finsh(_ params: [String: Any]? = nil) {
        self.navigationController?.popViewController(animated: true)
        ///更新用户信息
        UserInfo.update()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        self.fd_interactivePopDisabled = true
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        self.fd_interactivePopDisabled = false
    }
    
    required init(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    

}
