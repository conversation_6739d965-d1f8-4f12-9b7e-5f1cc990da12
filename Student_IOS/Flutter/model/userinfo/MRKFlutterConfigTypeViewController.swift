//
//  MRKFlutterConfigTypeViewController.swift
//  Student_IOS
//
//  Created by merit on 2025/5/15.
//

import UIKit

class MRKFlutterConfigTypeViewController: FlutterBaseViewController {
    
    @objc var refreshBlock: ((String) -> Void)?

    required init(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    @objc init() {
        super.init(withEntrypoint: EntryNames.ENTRY_FOOD_SPORT_TYPE_MAIN, router: "/ai-config-type/injured-part")
    }
    
    override func finsh(_ params: [String: Any]? = nil){
        if let finishDict = params?["finish"] as? [String: Any],
           let injuredPart = finishDict["injuredPart"] as? String {
            refreshBlock?(injuredPart)
        }
        
        DispatchQueue.main.async {
            self.navigationController?.popViewController(animated: true)
        }
    }

    override func viewDidLoad() {
        super.viewDidLoad()

        // Do any additional setup after loading the view.
    }

    deinit {
        
    }

}
