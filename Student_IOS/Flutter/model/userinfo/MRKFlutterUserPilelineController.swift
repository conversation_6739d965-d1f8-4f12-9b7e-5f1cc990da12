//
//  MRKFlutterUserPilelineController.swift
//  Student_IOS
//
//  Created by merit on 2025/5/13.
//  新手链路

import UIKit

@objc(MRKFlutterUserPilelineController)
class MRKFlutterUserPilelineController: FlutterBaseViewController {
    
    private var user_channel : FlutterMethodChannel?  //业务通道
    
    required init(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    @objc init() {
        let status = MRKHealthManager.shared().healthKitAllNoAutho()
        if (status){
            super.init(withEntrypoint: EntryNames.ENTRY_APPLE_HEALTH_AUTHORIZATION_MAIN, router: "/")
        } else {
            super.init(withEntrypoint: EntryNames.ENTRY_AI_USERPIPELINE_MAIN, router: "/")
        }
        // 初始化业务通道
        user_channel = FlutterMethodChannel(name: ChannelNames.USERPIPELINE_CHANNEL_NAME, binaryMessenger: self.engine!.binaryMessenger)
        user_channel?.setMethodCallHandler({ [weak self] call, reslt in
            self?.handlePageChannel(call, reslt)
        })
    }
    
    override func finsh(_ params: [String: Any]? = nil) {
        let dic = self.pageRouterData as? [String: [String:Any]]
        if let resourceUrl = dic?["SUBMIT_SUCCESS"]?["resourceUrl"] {
            MRKLinkRouterManager.sharedInstance().routerResourcePath(resourceUrl as! String, handleParameters: nil)
        }else {
        }
        ///移除掉当前控制器
        self.navigationController?.navigationRemoveContrller(self)
    }
    
    override func pageParam() -> Any {
        let status = MRKHealthManager.shared().healthKitAllNoAutho()
        return ["isFirstAppleHealth":status]
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        self.fd_interactivePopDisabled = true
    }
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        self.fd_interactivePopDisabled = false
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = UIColor(hexString: "#05182C")
    }
    
    func appleHealthAuthorization() {
        MRKHealthManager.shared().authorizateHealthKit {[weak self] success in
            self?.appleHealthRead()
        }
    }
    
    func appleHealthRead() {
        print("wwwkk appleHealthRead")
        MRKHealthManager.shared().readSportData(-100, duration: 100) {[weak self] model in
            print("wk\(String(describing: model.modelToJSONString()))")
            self?.invokeMethod(model: model)
        }
    }
    
    func invokeMethod(model: MRKHealthKitReadModel) {
        print("wwwkk invokeMethod")
        
        // 回显到flutter
        FlutterBaseChannel.invokeMethod(channle: self.user_channel!, method: ChannelNames.USERPIPELINE_METHOD_APPLE_HEALTH_DATA, arguments: [
            "height":model.height,
            "weight":model.weight,
            "birthday":model.birthday,
            "restRate":model.restRate,
            "heartRate":model.heartRate,
            "bodyFatRate":model.bodyFatRate
        ])
    }
    
    
    func handlePageChannel(_ call: FlutterMethodCall, _ result: @escaping FlutterResult) {
        if call.method == ChannelNames.USERPIPELINE_METHOD_GET_APPLE_HEALTH_AUTHORIZATION  {
            appleHealthAuthorization()
        }
    }

}


extension MRKFlutterUserPilelineController : SJRouteHandler {
    static func routePath() -> String {
        return "router_perfect_information"
    }
    static func handle(_ request: SJRouteRequest, topViewController: UIViewController, completionHandler: SJCompletionHandler? = nil) {
        let vc = MRKFlutterUserPilelineController()
        topViewController.navigationController?.pushViewController(vc, animated: true)
    }
}
