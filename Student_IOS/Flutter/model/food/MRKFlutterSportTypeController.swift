//
//  MRKFlutterSportTypeController.swift
//  Student_IOS
//
//  Created by merit on 2025/4/24.
//  活动类型选择

import UIKit

extension Notification.Name {
    static let refreshDailyTarget = Notification.Name("kRefreshDailyTargetNotification")
}

class MRKFlutterSportTypeController: FlutterBaseViewController {

    required init(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    @objc init() {
        super.init(withEntrypoint: EntryNames.ENTRY_FOOD_SPORT_TYPE_MAIN, router: "/")
    }
    
    override func finsh(_ params: [String: Any]? = nil){

        /// finish 是字典
        if let finishDict = params?["finish"] as? [String: Any],
           let _ = finishDict["calorieCalcCode"] as? String {
            NotificationCenter.default.post(name: .refreshDailyTarget, object: nil)
        }
        
        ///
        weak var weakSelf = self
        DispatchQueue.main.async {
            weakSelf?.navigationController?.popViewController(animated: true)
        }
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()

        // Do any additional setup after loading the view.
    }

    deinit {
        
    }
}
