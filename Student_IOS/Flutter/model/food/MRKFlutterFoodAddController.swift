//
//  MRKFlutterFoodAddController.swift
//  Student_IOS
//
//  Created by merit on 2025/4/24.
//  食物记录模块

import UIKit

@objc enum FoodRecordMode: Int {
    case text = 0  //文本记录
    case image     //图片记录
}

class MRKFlutterFoodAddController: FlutterBaseViewController {
    var smode : FoodRecordMode = .text
    @objc var date: String?
    @objc var type: Int = 0;
    
    required init(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    @objc init(_ mode: FoodRecordMode) {
        var entry = EntryNames.ENTRY_FOOD_SCAN_TEXT_MAIN
        switch mode {
        case .text:
            entry = EntryNames.ENTRY_FOOD_SCAN_TEXT_MAIN
            super.init(withEntrypoint: entry, router: "/identify-dialog")
            break
        case .image:
            entry = EntryNames.ENTRY_FOOD_SCAN_CAMERA_MAIN
            super.init(withEntrypoint: entry, router: "/")
            break
        }
        smode = mode
    }
    
    override func finsh(_ params: [String: Any]? = nil) {
        switch smode {
        case .text:
            if let dict = params?["finish"] as? [String: Any], let str = dict["operator"] as? String, str == "adjustTaskSuccess" {
                NotificationCenter.default.post(name: NSNotification.Name("kAIPlanAdjustSuccessNotification"), object: nil, userInfo: dict);
            }
            self.dismiss(animated: true)
        case .image:
            self.navigationController?.popViewController(animated: true, removeControllerClasses: [MRKFlutterFoodAddController.self], removePolicy: .newest)
        }
    }
    
    override func pageParam() -> Any {
        guard let d = date else { return ["type": type]}
        return ["recordDate":d, "type": type]
    }
    
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        self.fd_interactivePopDisabled = true
    }
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        self.fd_interactivePopDisabled = false
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        if (smode == .text) {
            view.backgroundColor = UIColor.clear;
        }
    }
    
}
