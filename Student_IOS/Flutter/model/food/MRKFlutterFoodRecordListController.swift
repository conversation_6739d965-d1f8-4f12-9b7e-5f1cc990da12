//
//  MRKFlutterFoodRecordListController.swift
//  Student_IOS
//
//  Created by merit on 2025/4/24.
//  饮食记录列表页

import UIKit

class MRKFlutterFoodRecordListController: FlutterBaseViewController {
    
    @objc var date: String?  ///默认日期 yyyy-MM-dd
    @objc var type: Int = 0; ///饮食0 饮水1
    
    required init(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    @objc init() {
        super.init(withEntrypoint: EntryNames.ENTRY_FOOD_RECORD_LIST_MAIN, router: "/")
    }
    
    override func finsh(_ params: [String: Any]? = nil){
//        if self.navigationController?.viewControllers.count ?? 0 <= 1 {
//            self.navigationController?.dismiss(animated: false)
//        }
//        self.navigationController?.popViewController(animated: true)
//        self.navigationController?.popToRootViewController(animated: true)

        if let viewControllers = self.navigationController?.viewControllers {
            ///viewControllers包含详情，优先返回详情页
            if let detailVC = viewControllers.first(where: { $0 is MRKFlutterAIDietPlanDetailController }) {
                   self.navigationController?.popToViewController(detailVC, animated: true)
            } else {
                let foodRecordControllers = viewControllers.filter { $0 is MRKFlutterFoodRecordListController }
                if foodRecordControllers.count >= 2 {
                    self.navigationController?.popToRootViewController(animated: true)
                } else {
                    let confirmViewControllers = viewControllers.filter { $0 is MRKFlutterFoodConfirmViewController }
                    if confirmViewControllers.count >= 1 {
                        self.navigationController?.popToRootViewController(animated: true)
                    }else {
                        self.navigationController?.popViewController(animated: true)
                    }
                }
            }
        }
    }
    
    override func pageParam() -> Any {
        guard let d = date else {
            return ["index": type]
        }
        return ["date":d, "index": type]
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        self.fd_interactivePopDisabled = true
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        self.fd_interactivePopDisabled = false
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
//        if let nav = self.navigationController {
//            var vcs = nav.viewControllers
//            if let index = vcs.firstIndex(where: { $0 is MRKFlutterFoodAddController }) {
//                vcs.remove(at: index)
//                nav.setViewControllers(vcs, animated: false)
//            }
//        }
        ///刷新flutter 
        setupChannelViewWillAppear()
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()

        // Do any additional setup after loading the view.
    }

}
