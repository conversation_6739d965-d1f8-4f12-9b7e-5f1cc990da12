//
//  MRKFlutterFoodConfirmViewController.swift
//  Student_IOS
//
//  Created by merit on 2025/4/27.
//

import UIKit

class MRKFlutterFoodConfirmViewController: FlutterBaseViewController {
    var data :[String: Any]?
    
    required init(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    @objc init() {
        super.init(withEntrypoint: EntryNames.ENTRY_FOOD_CONFIRM_MAIN, router: "/")
    }
    
    override func finsh(_ params: [String: Any]? = nil) {
        self.navigationController?.popViewController(animated: true)
    }
    
    override func pageParam() -> Any {
        guard let d = data else { return [:] }
        return ["data":d]
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
//        if let nav = self.navigationController {
//            var vcs = nav.viewControllers
//            if let index = vcs.firstIndex(where: { $0 is MRKFlutterFoodAddController }),let vc = vcs[index] as? MRKFlutterFoodAddController {
//                if vc.smode == .text  {
//                    vcs.remove(at: index)
//                    nav.setViewControllers(vcs, animated: false)
//                }
//            }
//        }
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        self.fd_interactivePopDisabled = true
    }
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        self.fd_interactivePopDisabled = false
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
    }
}
