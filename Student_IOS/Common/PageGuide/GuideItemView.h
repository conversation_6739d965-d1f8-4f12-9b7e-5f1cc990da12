//
//  GuideItemView.h
//  GuideDemo
//
//  Created by MacPro on 2023/2/16.
//  Copyright © 2023 sunli. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSUInteger, IntroGuideShape) {
    IntroGuideShape_Square,
    IntroGuideShape_Circle,
    IntroGuideShape_Other
};


typedef void(^dismissBlock)(__nonnull id);

@interface GuideItemView : UIView

@property (nonatomic, strong) UIImageView *btnMaskView;
- (void)showInView:(UIView *)view maskBtn:(UIView *)btn;
- (void)dismiss ;

@property (nonatomic, strong) UIView *target;
@property (nonatomic, copy) dismissBlock dismissBlock;
@property (nonatomic, weak) UIView *maskBtn;
@property (nonatomic, assign) BOOL useDismissTimers;
@end




@interface GuideItemViewControl : UIView

@property (nonatomic, strong) NSMutableArray *itemsArray;
@property (nonatomic, assign) NSInteger step;
@property (nonatomic, strong) UIView *target;
@property (nonatomic, assign) CGFloat cutoutRadius;
@property (nonatomic, assign) BOOL maskUserInteractionEnabled;

@property (nonatomic , copy) dismissBlock finishBlock;

- (void)start;
- (void)dismissGuide:(NSInteger)step;
- (void)dismiss;

@end


NS_ASSUME_NONNULL_END
