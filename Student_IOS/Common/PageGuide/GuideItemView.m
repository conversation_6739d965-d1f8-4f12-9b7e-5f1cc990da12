//
//  GuideItemView.m
//  GuideDemo
//
//  Created by MacPro on 2023/2/16.
//  Copyright © 2023 sunli. All rights reserved.
//

#import "GuideItemView.h"
#import "UIImage+Mask.h"
#import "NSTimer+SJAssetAdd.h"


@interface GuideItemView (){
    // waitTimer
    NSTimer *_Nullable _timer;
    NSTimeInterval _countDownNum;
}
@property (nonatomic, weak) UIView *parentView;
@property (nonatomic, strong) UIView *maskBg;
@end

@implementation GuideItemView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self addSubview:self.btnMaskView];
        _countDownNum = 5.0;
    }
    return self;
}

- (void)layoutSubviews {
    [super layoutSubviews];
    self.frame = _parentView.bounds;
    _maskBg.frame = self.bounds;
    _btnMaskView.center = [_maskBtn.superview convertPoint:_maskBtn.center toView:self.target ? : [UIViewController keywindow]];
    
    CGRect btnMaskRect = _btnMaskView.frame;
    btnMaskRect.size = CGSizeMake(floor(btnMaskRect.size.width), floor(btnMaskRect.size.height));
    btnMaskRect.origin = CGPointMake(floor(btnMaskRect.origin.x), floor(btnMaskRect.origin.y));
    _btnMaskView.frame = btnMaskRect;
    _btnMaskView.layer.cornerRadius = btnMaskRect.size.height / 2.0;
}

- (void)dealloc {
    if ([_timer isValid]) {
        [_timer invalidate];
        _timer = nil;
    }
}

- (void)addWaitingTimers {
    if (!_useDismissTimers) return;
    if ( _timer != nil ) return;
    
    __weak typeof(self) _self = self;
    _timer = [NSTimer sj_timerWithTimeInterval:1.0 repeats:YES usingBlock:^(NSTimer * _Nonnull timer) {
        __strong typeof(_self) self = _self;
        if ( !self ) {
            [timer invalidate];
            return ;
        }
        
        self->_countDownNum --;
        if (self->_countDownNum <= 0){
            [self dismiss];
        }
    }];
    
    [NSRunLoop.mainRunLoop addTimer:_timer forMode:NSRunLoopCommonModes];
    [_timer fire];
}

- (void)showInView:(UIView *)view maskBtn:(UIView *)btn {
    self.parentView = view;
    self.maskBtn = btn;
    
    self.alpha = 0;
    [view addSubview:self];
    [UIView animateWithDuration:0.2 animations:^{
        self.alpha = 1;
    } completion:nil];
    
    [self addWaitingTimers];
}

- (void)dismiss {
    
    if ([_timer isValid]) {
        [_timer invalidate];
        _timer = nil;
    }
    
    [UIView animateWithDuration:0.2 animations:^{
        self.alpha = 0;
    } completion:^(BOOL finished) {
        [self removeFromSuperview];
        
        if(self.dismissBlock) {
            self.dismissBlock(@1);
        }
    }];
}


#pragma mark - getter and setter

- (UIView *)maskBg {
    if (!_maskBg) {
        _maskBg = [[UIView alloc] init];
    }
    return _maskBg;
}

- (UIImageView *)btnMaskView {
    if (!_btnMaskView) {
        UIImage *image = [UIImage imageNamed:@""];//whiteMask
        image = [image maskImage:[[UIColor whiteColor] colorWithAlphaComponent:1.0]];
        UIImageView *imageView = [[UIImageView alloc] initWithImage:image];
        _btnMaskView = imageView;
    }
    return _btnMaskView;
}

- (void)setMaskBtn:(UIView *)maskBtn {
    _maskBtn = maskBtn;
}

@end





@interface GuideItemViewControl ()
/**  遮盖层 */
@property (nonatomic, strong) CAShapeLayer *mask;
@end

@implementation GuideItemViewControl

- (CAShapeLayer *)mask {
    if (!_mask) {
        _mask = [CAShapeLayer layer];
        [_mask setFillRule:kCAFillRuleEvenOdd];
        [_mask setFillColor:[[UIColor colorWithHue:0.0f saturation:0.0f brightness:0.0f alpha:0.8f] CGColor]];
        _mask.path = [UIBezierPath bezierPathWithRect:self.bounds].CGPath;
    }
    return _mask;
}

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if(self ) {
        
        self.itemsArray = [NSMutableArray array];
    }
    return self;
}

- (void)start {
    [self.layer addSublayer:self.mask];
    [self.target addSubview:self];
    
    self.alpha = 1.0;
    self.step = 0;
    
    if (self.itemsArray.count) {
        [self showNextGuide:self.step];
    }
}

- (void)showNextGuide:(NSInteger)step {
    
    if(step < self.itemsArray.count) {
        GuideItemView *v = self.itemsArray[step];
        @weakify(self);
        v.dismissBlock = ^(id _Nonnull sender) {
            [self_weak_ showNextGuide:step + 1];
        };
        self.step = step;
        [v showInView:self maskBtn:v.maskBtn];
        
        CGRect frame = [v.maskBtn.superview convertRect:v.maskBtn.frame toView:self.target];
        CGPoint center = [v.maskBtn.superview convertPoint:v.maskBtn.center toView:self.target];
        [self animateCutoutToRect:CGRectMake(center.x - frame.size.width / 2.0, center.y - frame.size.width / 2.0, frame.size.width, frame.size.width) shape:IntroGuideShape_Circle];
        
    } else {
        [self dismiss];
    }
}

- (void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
    if (self.maskUserInteractionEnabled) {
        [self dismissGuide:self.step];
    }
}

- (void)dismissGuide:(NSInteger)step {
    if(step < self.itemsArray.count) {
        if(step == self.itemsArray.count -1) {
            [self dismiss];
        } else {
            GuideItemView *v = self.itemsArray[step];
            [v dismiss];
        }
    }
}

- (void)dismiss{
    [UIView animateWithDuration:0.2 animations:^{
        self.alpha = 0;
    } completion:^(BOOL finished) {
        
        [self.itemsArray makeObjectsPerformSelector:@selector(removeFromSuperview)];
        [self.itemsArray removeAllObjects];
        
        [self.mask removeFromSuperlayer];
        [self removeFromSuperview];
        
        if(self.finishBlock) {
            self.finishBlock(@1);
        }
    }];
}

- (void)animateCutoutToRect:(CGRect)rect shape:(IntroGuideShape)shape {
    // Define shape
    UIBezierPath *maskPath = [UIBezierPath bezierPathWithRect:self.bounds];
    UIBezierPath *cutoutPath;
    //    cutoutPath = [UIBezierPath bezierPathWithOvalInRect:rect];
    
    switch (shape) {
        case IntroGuideShape_Square: {
            cutoutPath = [UIBezierPath bezierPathWithRect:rect];
            break;
        }
        case IntroGuideShape_Circle: {
            cutoutPath = [UIBezierPath bezierPathWithOvalInRect:rect];
            break;
        }
        case IntroGuideShape_Other: {
            cutoutPath = [UIBezierPath bezierPathWithRoundedRect:rect cornerRadius:self.cutoutRadius];
            break;
        }
    }
    
    [maskPath appendPath:cutoutPath];
    
    // Animate it
    CABasicAnimation *anim = [CABasicAnimation animationWithKeyPath:@"path"];
    //    anim.delegate = self;
    anim.timingFunction = [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionEaseOut];
    anim.duration = 0.1;
    anim.removedOnCompletion = YES;
    anim.fillMode = kCAFillModeForwards;
    anim.fromValue = (__bridge id)(self.mask.path);
    anim.toValue = (__bridge id)(maskPath.CGPath);
    [self.mask addAnimation:anim forKey:@"path"];
    self.mask.path = maskPath.CGPath;
}



@end
