//
//  PopView.h
//  GuideDemo
//
//  Created by MacPro on 2023/2/21.
//  Copyright © 2023 sunli. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface PopView : UIView
@property (nonatomic, assign) CGFloat arrowL;//箭头位置（居左间隔）
@property (nonatomic, assign) CGFloat arrowW;//箭头宽 默认20
@property (nonatomic, assign) CGFloat arrowH;//箭头高 10

@property (nonatomic, assign) CGFloat corRadius; ///圆角 默认10
@property (nonatomic, assign) BOOL isTranslute; ///是否开启毛玻璃效果
@property (nonatomic, assign) BOOL isBlack;     ///是否黑色毛玻璃效果
///是否显示背景遮罩 默认不显示
@property (nonatomic, assign) BOOL isMaskShow;
@property (nonatomic, strong) UIColor *maskColor;//背景色

@property (nonatomic, assign) BOOL isUpward; ///< 箭头指向, YES为向上, 反之为向下, 默认为YES.
@property (nonatomic, strong) UIView *contentView;

- (void)updateUI;
- (void)showOnView:(UIView *)onView;
- (void)dismiss;

@end


@interface PopTitleView : PopView
@property (nonatomic, strong) NSString *tipString;
@property (nonatomic, strong) UIFont *font;
@property (nonatomic, strong) UIColor *textColor;

- (void)dismissAfter:(NSTimeInterval)time;
@end

NS_ASSUME_NONNULL_END
