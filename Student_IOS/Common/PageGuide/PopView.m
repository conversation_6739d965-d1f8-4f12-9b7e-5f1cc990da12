//
//  PopView.m
//  GuideDemo
//
//  Created by MacPro on 2023/2/21.
//  Copyright © 2023 sunli. All rights reserved.
//

#import "PopView.h"

#define kContentViewEdge 10

@interface PopView ()

/**  遮盖层 */
@property (nonatomic, strong) CAShapeLayer *mask;

@end



@implementation PopView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if(self) {
        self.backgroundColor = [UIColor colorWithWhite:1.0 alpha:0.2];
        
        self.arrowL = frame.size.width / 2.0;
        self.arrowW = 10;
        self.arrowH = 5;
        self.corRadius = 10;
        
        self.maskColor = [UIColor colorWithHue:0.0f saturation:0.0f brightness:0.0f alpha:0.6f];
    }
    return self;
}

- (void)layoutSubviews {
    [super layoutSubviews];
}

- (void)updateUI {
    
    if(self.subviews.count) {
        [self.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
    }
    
    if(self.layer.sublayers.count) {
        [self.layer.sublayers makeObjectsPerformSelector:@selector(removeFromSuperlayer)];
    }
    
    CGFloat width = self.bounds.size.width;
    CGFloat height = self.bounds.size.height;
    UIBezierPath *maskPath = [UIBezierPath bezierPath];
    
    CGFloat arrowLeft = self.arrowL; // width / 2.0 + 30;
    CGFloat arrowW = self.arrowW;
    CGFloat arrowH = self.arrowH;
    CGFloat r = self.corRadius;
    
    BOOL isUp = self.isUpward;
    if(isUp) {
        ///箭头向上
        [maskPath moveToPoint:CGPointMake(arrowLeft, 0)];
        [maskPath addLineToPoint:CGPointMake(arrowLeft - arrowW / 2.0, arrowH)];
        [maskPath addLineToPoint:CGPointMake(r, arrowH)];
        //左上
        [maskPath addQuadCurveToPoint:CGPointMake(0,  arrowH + r) controlPoint:CGPointMake(0, arrowH)];
        [maskPath addLineToPoint:CGPointMake(0, height - r)];
        //左下
        [maskPath addQuadCurveToPoint:CGPointMake(r, height) controlPoint:CGPointMake(0, height)];
        [maskPath addLineToPoint:CGPointMake(width - r, height )];
        //右下
        [maskPath addQuadCurveToPoint:CGPointMake(width, height - r) controlPoint:CGPointMake(width, height)];
        [maskPath addLineToPoint:CGPointMake(width, r + arrowH)];
        //右上
        [maskPath addQuadCurveToPoint:CGPointMake(width - r,  arrowH) controlPoint:CGPointMake(width,  arrowH)];
        [maskPath addLineToPoint:CGPointMake(arrowLeft + arrowW / 2.0, arrowH)];
        
    } else {
        
        ///箭头向下
        [maskPath moveToPoint:CGPointMake(arrowLeft, height)];
        [maskPath addLineToPoint:CGPointMake(arrowLeft - arrowW / 2.0, height - arrowH)];
        [maskPath addLineToPoint:CGPointMake(arrowH, height - arrowH)];
        //左下
        [maskPath addQuadCurveToPoint:CGPointMake(0, height - arrowH - r) controlPoint:CGPointMake(0, height - arrowH)];
        [maskPath addLineToPoint:CGPointMake(0, r)];
        //左上
        [maskPath addQuadCurveToPoint:CGPointMake(r, 0) controlPoint:CGPointMake(0, 0)];
        [maskPath addLineToPoint:CGPointMake(width - r, 0)];
        //右上
        [maskPath addQuadCurveToPoint:CGPointMake(width, r) controlPoint:CGPointMake(width, 0)];
        [maskPath addLineToPoint:CGPointMake(width, height - r - arrowH)];
        //右下
        [maskPath addQuadCurveToPoint:CGPointMake(width - r, height - arrowH) controlPoint:CGPointMake(width, height - arrowH)];
        [maskPath addLineToPoint:CGPointMake(arrowLeft + arrowW / 2.0, height - arrowH)];
    }
    
    [maskPath closePath];

    
    CAShapeLayer *maskLayer = [CAShapeLayer layer];
    maskLayer.frame = self.bounds;
    maskLayer.path = maskPath.CGPath;
    self.layer.mask = maskLayer;
    
//    if (self.isTranslute) {
//
//        UIBlurEffectStyle style = self.isBlack ? UIBlurEffectStyleDark: UIBlurEffectStyleLight;
//        UIBlurEffect *blurEffect = [UIBlurEffect effectWithStyle:style];
//        UIVisualEffectView *blurEffectView = [[UIVisualEffectView alloc] initWithEffect:blurEffect];
//        blurEffectView.frame = self.bounds;
//        blurEffectView.alpha = 0.4;
//        [self addSubview:blurEffectView];
//    }
    
    [self addContentView];
}

-(void)showOnView:(UIView *)onView {
    
    [self updateUI];
    
    if(self.isMaskShow) {
        self.mask.path = [UIBezierPath bezierPathWithRect:onView.bounds].CGPath;
        [onView.layer addSublayer:self.mask];
    }
    [onView addSubview:self];
    
    self.transform = CGAffineTransformMakeScale(0.01f, 0.01f);
    [UIView animateWithDuration:0.0f animations:^{
        self.transform = CGAffineTransformIdentity;
        self.maskView.alpha = 1.f;
        self.alpha = 1.f;
    }];
}

-(void)dismiss {
    
    [UIView animateWithDuration:0.25f animations:^{
        self.alpha = 0.f;
//        self.transform = CGAffineTransformMakeScale(0.01f, 0.01f);
        self.maskView.alpha = 0.f;
    } completion:^(BOOL finished) {
        if(self.subviews.count) {
            [self.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
        }
        if(self.layer.sublayers.count) {
            [self.layer.sublayers makeObjectsPerformSelector:@selector(removeFromSuperlayer)];
        }
        [self.mask removeFromSuperlayer];
        [self removeFromSuperview];
    }];
}

- (CAShapeLayer *)mask {
    if (!_mask) {
        _mask = [CAShapeLayer layer];
        [_mask setFillRule:kCAFillRuleEvenOdd];
        [_mask setFillColor:[self.maskColor CGColor]];
    }
    return _mask;
}

- (void)setContentView:(UIView *)contentView {
    _contentView = contentView;
}

- (void)addContentView {
    if(!self.contentView) {
        return;
    }
    CGFloat space = kContentViewEdge;
    self.contentView.frame = CGRectMake(space, (self.isUpward ? self.arrowH : 0) + space, self.frame.size.width - space * 2, self.frame.size.height - 2 *space - (self.isUpward ? self.arrowH : 0));
    [self addSubview:self.contentView];
}
@end




@interface PopTitleView ()
@property (nonatomic, strong) UILabel *label;
@end

@implementation PopTitleView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if(self) {
        
    }
    return self;
}

- (void)updateUI {
    
    CGFloat w =  self.bounds.size.width;
    NSString *tip = self.tipString;
    
    UILabel *l = [UILabel new];
    l.font = self.font ? : [UIFont systemFontOfSize:15.0];
    l.text = tip ;
    l.textColor = self.textColor ? : [UIColor blackColor];
    l.numberOfLines = 0;
    
    CGFloat height = [tip heightForFont:l.font width:w];
    CGFloat h = height + self.arrowH + 2 * kContentViewEdge;
    CGRect rect = self.frame;
    rect.size.height = h;
    self.frame = rect;
    self.contentView = l;
    
    [super updateUI];
}

- (void)dismissAfter:(NSTimeInterval)time {
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(time * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self dismiss];
    });
}

@end
