//
//  MRKDatePickerManager.m
//  Student_IOS
//
//  Created by merit on 2022/6/16.
//

#import "MRKDatePickerManager.h"
#import "NSBundle+Helper.h"
#import "MRKDailyLogic.h"


@interface MRKDatePickerManager ()
@property (nonatomic, strong) BRPickerStyle *pickerStyle;
@end

@implementation MRKDatePickerManager

- (instancetype)init
{
    self = [super init];
    if (self) {
        
    }
    return self;
}

- (BRPickerStyle *)pickerStyle{
    if (!_pickerStyle) {
        BRPickerStyle *style = [[BRPickerStyle alloc] init];
        style.pickerColor = BR_RGB_HEX(0xFFFFFF, 1.0f);
        style.separatorColor = [UIColor clearColor];
        style.topCornerRadius = 12;
        
        /** 设置 titleBarView*/
        style.hiddenTitleLine = NO;
        style.titleLineColor = UIColorHex(#F6F6F8);
        style.titleBarHeight = 60;
        
        /** 设置 titleLabel */
        style.titleTextColor = UIColorHex(#333333);
        style.titleTextFont = [UIFont systemFontOfSize:17 weight:UIFontWeightMedium];
        style.titleLabelFrame = CGRectMake(100, 0, kScreenWidth- 200, 60);
        
        /** 设置 doneBtn */
        style.doneTextColor = UIColorHex(#16D2E3);
        style.doneTextFont = [UIFont systemFontOfSize:15 weight:UIFontWeightMedium];
        style.doneBtnFrame = CGRectMake(kScreenWidth- 80, 0, 80, 60);
        
        /** 设置 cancelBtn */
        style.cancelTextColor = UIColorHex(#666666);
        style.cancelTextFont = [UIFont systemFontOfSize:15 weight:UIFontWeightMedium];
        style.cancelBtnFrame = CGRectMake(0, 0, 80, 60);
        
        style.paddingBottom = kSafeArea_Bottom;
        style.rowHeight = 60;
        style.pickerHeight = 286;
        
        style.selectRowColor = UIColorHex(#F8F9FC);
        _pickerStyle = style;
    }
    return _pickerStyle;
}

- (void)drinkPickerViewWithIndex:(double)value resultBlock:(BRRulerResultBlock)resultBlock{
    WeightPickerView *stringPickerView = [[WeightPickerView alloc] initWithType:DrinkRulerViewType];
    stringPickerView.title = @"左右滑动调整每日喝水目标";
    stringPickerView.describeTitle = [NSString stringWithFormat:@"建议每日喝水目标范围控制在%d～%d毫升以内", [[MRKDailyLogic shared] drinkSelectLeast], [[MRKDailyLogic shared] drinkSelectMost]];
    stringPickerView.value = value;
    stringPickerView.isAutoSelect = NO;
    stringPickerView.resultModelBlock = resultBlock;
    self.pickerStyle.hiddenTitleLine = YES;
    self.pickerStyle.hiddenCancelBtn = YES;
    self.pickerStyle.titleBarHeight = 60;
    self.pickerStyle.titleTextColor = UIColorHex(#333333);
    self.pickerStyle.titleTextFont = [UIFont systemFontOfSize:20 weight:UIFontWeightMedium];
    self.pickerStyle.titleLabelFrame = CGRectMake(10, 16, kScreenWidth- 20, 44);
    self.pickerStyle.describeLabelColor = [UIColor whiteColor];
    self.pickerStyle.describeTextColor = UIColorHex(#848A9B);
    self.pickerStyle.describeTextFont = [UIFont systemFontOfSize:12 weight:UIFontWeightRegular];
    self.pickerStyle.describeLabelFrame = CGRectMake(10, 60, kScreenWidth - 20, 16);
    self.pickerStyle.showDescribeLabel = YES;
    self.pickerStyle.doneColor = UIColorHex(#17D2E3);
    self.pickerStyle.doneBtnTitle = @"确认";
    self.pickerStyle.doneTextColor = [UIColor whiteColor];
    self.pickerStyle.doneTextFont = [UIFont systemFontOfSize:18 weight:UIFontWeightMedium];
    self.pickerStyle.doneBtnFrame = CGRectMake(47, 280, kScreenWidth - 47*2, 48);
    self.pickerStyle.doneCornerRadius = 24;
    self.pickerStyle.doneBorderStyle = BRBorderStyleFill;
    self.pickerStyle.paddingBottom = kSafeArea_Bottom;
    self.pickerStyle.rowHeight = 60;
    self.pickerStyle.selectRowColor = UIColorHex(#F8F9FC);
    self.pickerStyle.pickerHeight = DHPX(280);
    self.pickerStyle.unitText = @"毫升";
    self.pickerStyle.dateUnitTextFont = [UIFont systemFontOfSize:16 weight:UIFontWeightMedium];
    self.pickerStyle.dateUnitTextColor = UIColorHex(#17D2E3);
    self.pickerStyle.dateUnitOffsetX = 19;
    self.pickerStyle.dateUnitOffsetY = 5;
    stringPickerView.pickerStyle = self.pickerStyle;
    [stringPickerView show];
}

- (void)weightPickerViewWithIndex:(double)value resultBlock:(BRRulerResultBlock)resultBlock cancelBlock:(BRCancelBlock)cancelBlock{
    WeightPickerView *stringPickerView = [[WeightPickerView alloc] initWithType:WeightRulerViewType];
    value = (value < 30.0 || value > 200.0 ) ? 60.0 : value;
    stringPickerView.title = @"选择体重";
    stringPickerView.value = value;
    stringPickerView.isAutoSelect = NO;
    stringPickerView.pickerStyle = self.pickerStyle;
    stringPickerView.resultModelBlock = resultBlock;
    stringPickerView.cancelBlock = cancelBlock;
    [stringPickerView show];
}

- (void)heightPickerViewWithIndex:(double)value resultBlock:(BRRulerResultBlock)resultBlock cancelBlock:(BRCancelBlock)cancelBlock{
    WeightPickerView *stringPickerView = [[WeightPickerView alloc] initWithType:HeightRulerViewType];
    value = (value < 50.0 || value > 300.0 ) ? 170.0 : value;
    stringPickerView.title = @"选择身高";
    stringPickerView.value = value;
    stringPickerView.isAutoSelect = NO;
    stringPickerView.pickerStyle = self.pickerStyle;
    stringPickerView.resultModelBlock = resultBlock;
    stringPickerView.cancelBlock = cancelBlock;
    [stringPickerView show];
}

+ (void)countPickerViewTitle:(NSString *)title withIndex:(double)value resultBlock:(BRRulerResultBlock)resultBlock cancelBlock:(BRCancelBlock)cancelBlock{
    WeightPickerView *stringPickerView = [[WeightPickerView alloc] initWithType:CountRulerViewType];
    value = (value < 0 || value > 999.0 ) ? 100 : value;
    stringPickerView.title = title ?:@"选择个数";
    stringPickerView.value = value;
    stringPickerView.isAutoSelect = NO;
    MRKDatePickerManager *share = [[MRKDatePickerManager alloc] init];
    stringPickerView.pickerStyle = share.pickerStyle;
    stringPickerView.resultModelBlock = resultBlock;
    stringPickerView.cancelBlock = cancelBlock;
    [stringPickerView show];
}


- (void)sexPickerViewWithIndex:(NSInteger)index resultBlock:(BRStringResultModelBlock)resultBlock cancelBlock:(BRCancelBlock)cancelBlock{
    BRStringPickerView *stringPickerView = [[BRStringPickerView alloc] init];
    stringPickerView.pickerMode = BRStringPickerComponentSingle;
    stringPickerView.title = @"选择性别";
    stringPickerView.selectIndex = index;
    stringPickerView.dataSourceArr = @[@"男", @"女"];
    stringPickerView.resultModelBlock = resultBlock;
    stringPickerView.isAutoSelect = NO;
    self.pickerStyle.pickerTextColor = UIColorHex(#4C5362);
    self.pickerStyle.pickerTextFont = [UIFont systemFontOfSize:18 weight:UIFontWeightMedium];
    //防止覆盖有历史数据
    self.pickerStyle.unitText = @"";
    stringPickerView.pickerStyle = self.pickerStyle;
    stringPickerView.cancelBlock = cancelBlock;
    [stringPickerView show];
}



- (void)heightPickerViewWithIndex:(NSInteger)index dataSource:(NSArray <NSString*>*)sources resultBlock:(BRStringResultModelBlock)resultBlock cancelBlock:(BRCancelBlock)cancelBlock{
    if (sources.count <= 0) {
        return;
    }
    index = (index > 0 && index < sources.count ) ? index : 70;
    
    BRStringPickerView *stringPickerView = [[BRStringPickerView alloc] init];
    stringPickerView.pickerMode = BRStringPickerComponentSingle;
    stringPickerView.title = @"选择身高";
    stringPickerView.selectIndex = index;
    stringPickerView.dataSourceArr = sources;
    stringPickerView.resultModelBlock = resultBlock;
    stringPickerView.isAutoSelect = NO;
    self.pickerStyle.pickerTextColor = UIColorHex(#4C5362);
    self.pickerStyle.pickerTextFont = [UIFont fontWithName:Bebas_Font size:30];
    self.pickerStyle.unitText = @"厘米";
    self.pickerStyle.dateUnitTextFont = [UIFont systemFontOfSize:16];
    self.pickerStyle.dateUnitTextColor = UIColorHex(#4C5362);
    self.pickerStyle.dateUnitOffsetX = 10;
    self.pickerStyle.dateUnitOffsetY = 5;
    stringPickerView.pickerStyle = self.pickerStyle;
    stringPickerView.cancelBlock = cancelBlock;
    [stringPickerView show];
}


- (void)addressPickerViewWithValues:(NSArray <NSString*>*)selectValues resultBlock:(BRAddressResultBlock)resultBlock cancelBlock:(BRCancelBlock)cancelBlock {
    BRAddressPickerView *addressPickerView = [[BRAddressPickerView alloc]init];
    addressPickerView.pickerMode = BRAddressPickerModeCity;
    addressPickerView.title = @"选择地区";
    addressPickerView.selectValues = selectValues;
    addressPickerView.dataSourceArr = [NSBundle getRegion];
    addressPickerView.isAutoSelect = NO;
    addressPickerView.resultBlock = resultBlock;
    addressPickerView.cancelBlock = cancelBlock;
    self.pickerStyle.pickerTextColor = UIColorHex(#4C5362);
    self.pickerStyle.pickerTextFont = [UIFont systemFontOfSize:18 weight:UIFontWeightMedium];
    addressPickerView.pickerStyle = self.pickerStyle;
    [addressPickerView show];
}


- (void)datePickerViewWithTime:(NSString *)timeStr resultBlock:(BRDateResultBlock)resultBlock cancelBlock:(BRCancelBlock)cancelBlock{
    BRDatePickerView *datePickerView = [[BRDatePickerView alloc]init];
    datePickerView.pickerMode = BRDatePickerModeYMD;
    datePickerView.title = @"选择生日日期";
    datePickerView.selectValue = timeStr; /* 2018-03-15 格式字符*/
    NSArray *values = [timeStr componentsSeparatedByString:@"-"];
    if (values.count == 3) {
        datePickerView.selectDate = [NSDate br_setYear:[values[0] integerValue]
                                                 month:[values[1] integerValue]
                                                   day:[values[2] integerValue]];
    }
    datePickerView.minDate = [NSDate br_setYear:1940 month:01 day:01];
    datePickerView.maxDate = [NSDate date];
    datePickerView.isAutoSelect = NO;
    datePickerView.showUnitType = BRShowUnitTypeOnlyCenter;
    datePickerView.resultBlock = resultBlock;
    datePickerView.cancelBlock = cancelBlock;
    
    self.pickerStyle.pickerTextColor = UIColorHex(#4C5362);
    self.pickerStyle.pickerTextFont = [UIFont fontWithName:Bebas_Font size:22];
    self.pickerStyle.dateUnitTextFont = [UIFont systemFontOfSize:14];
    self.pickerStyle.dateUnitTextColor = UIColorHex(#4C5362);
    ///防止覆盖有历史数据
    self.pickerStyle.dateUnitOffsetX = 0;
    self.pickerStyle.dateUnitOffsetY = 0;
    datePickerView.pickerStyle = self.pickerStyle;
    [datePickerView show];
}


- (void)dateHHMMPickerViewWithTime:(NSString *)timeStr resultBlock:(BRDateResultBlock)resultBlock cancelBlock:(BRCancelBlock)cancelBlock{
    BRDatePickerView *datePickerView = [[BRDatePickerView alloc]init];
    datePickerView.pickerMode = BRDatePickerModeHM;
    datePickerView.title = @"设置时间提醒";
    datePickerView.selectValue = timeStr; /* 14:55 格式字符*/
    NSArray *values = [timeStr componentsSeparatedByString:@":"];
    if (values.count == 2) {
        datePickerView.selectDate = [NSDate br_setHour:[values[0] integerValue]
                                                minute:[values[1] integerValue]];
    }
    datePickerView.isAutoSelect = NO;
    datePickerView.showUnitType = BRShowUnitTypeOnlyCenter;
    datePickerView.resultBlock = resultBlock;
    datePickerView.cancelBlock = cancelBlock;
    
    self.pickerStyle.pickerTextColor = UIColorHex(#4C5362);
    self.pickerStyle.pickerTextFont = [UIFont fontWithName:Bebas_Font size:22];
    self.pickerStyle.dateUnitTextFont = [UIFont systemFontOfSize:14];
    self.pickerStyle.dateUnitTextColor = UIColorHex(#4C5362);
    ///防止覆盖有历史数据
    self.pickerStyle.dateUnitOffsetX = 0;
    self.pickerStyle.dateUnitOffsetY = 0;
    datePickerView.pickerStyle = self.pickerStyle;
    [datePickerView show];
}


+ (void)dateMMSSPickerViewWithTime:(NSString *)timeStr resultBlock:(BRDateResultBlock)resultBlock cancelBlock:(BRCancelBlock)cancelBlock{
    BRDatePickerView *datePickerView = [[BRDatePickerView alloc]init];
    datePickerView.pickerMode = BRDatePickerModeMS;
    datePickerView.title = @"选择时间";
    datePickerView.selectValue = timeStr; /* 14:55 格式字符*/
    NSArray *values = [timeStr componentsSeparatedByString:@":"];
    if (values.count == 2) {
        datePickerView.selectDate = [NSDate br_setMinute:[values[0] integerValue]
                                                  second:[values[1] integerValue]];
    }
    datePickerView.isAutoSelect = NO;
    datePickerView.showUnitType = BRShowUnitTypeOnlyCenter;
    datePickerView.resultBlock = resultBlock;
    datePickerView.cancelBlock = cancelBlock;
    
    MRKDatePickerManager *share = [[MRKDatePickerManager alloc] init];
    share.pickerStyle.pickerTextColor = UIColorHex(#4C5362);
    share.pickerStyle.pickerTextFont = [UIFont fontWithName:Bebas_Font size:22];
    share.pickerStyle.dateUnitTextFont = [UIFont systemFontOfSize:14];
    share.pickerStyle.dateUnitTextColor = UIColorHex(#4C5362);
    ///防止覆盖有历史数据
    share.pickerStyle.dateUnitOffsetX = 0;
    share.pickerStyle.dateUnitOffsetY = 0;
    datePickerView.pickerStyle = share.pickerStyle;
    [datePickerView show];
}

@end
