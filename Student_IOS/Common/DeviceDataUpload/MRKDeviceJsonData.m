//
//  MRKDeviceJsonData.m
//  Student_IOS
//
//  Created by merit on 2022/12/1.
//

#import "MRKDeviceJsonData.h"

@implementation MRKDeviceJsonData

+ (id)itemWithModel:(BaseEquipDataModel *)model andDeviceId:(NSString *)deviceId{
    return [MRKDeviceJsonData itemWithModel:model andDeviceId:deviceId andSportId:@""];
}


///设备数据转换接口
+ (id)itemWithModel:(BaseEquipDataModel *)model andDeviceId:(NSString *)deviceId andSportId:(NSString *)sportId{
    
    NSMutableDictionary *parm    = [NSMutableDictionary dictionary];
    parm[@"originDeviceRelId"]   = deviceId ?:@"";                    /// 设备Id
    parm[@"userId"]              = UserInfo.userId;                   /// 用户Id
    parm[@"totalDistance"]       = model.totalDistance?:@0;           /// 总距离
    parm[@"resistanceLevel"]     = model.drag?:@0;                    /// 阻力位
    parm[@"instantaneousPower"]  = model.power?:@0;                   /// 瞬时功率
    parm[@"averagePower"]        = model.avgPower?:@0;                /// 平均功率
    parm[@"totalEnergy"]         = model.energy?:@0;                  /// 总能量
    parm[@"heartRate"]           = model.deviceRate?:@0;              /// 设备心率
    parm[@"rateKcal"]            = model.rateKcal?:@0;                /// 🔥🔥🔥 心率消耗, 跟着活动上
    parm[@"sportId"]             = sportId ?:@"";                     /// 训练id
    ///
    parm[@"elapsedTime"]         = model.totalTimeSecond?:@0;         /// 运行时间
    parm[@"remainingTime"]       = model.remainingTimeSecond?:@0;     /// 剩余时间

    int type = model.type.intValue;
    switch (type) {
        case BicycleEquipment:{ //动感单车
            
            parm[@"instantaneousCadence"] = model.spm?:@0;           /// 瞬时节奏
            parm[@"averageCadence"]       = model.avgSpm?:@0;        /// 平均节奏
            parm[@"instantaneousSpeed"]   = model.speed?:@0;         /// 瞬时速度
            parm[@"averageSpeed"]         = model.avgSpeed?:@0;      /// 平均速度
            
        } break;
        case TreadmillEquipment:{ //跑步机
            
            parm[@"instantaneousSpeed"]  = model.speed?:@0;         /// 瞬时速度
            parm[@"averageSpeed"]        = model.avgSpeed?:@0;      /// 平均速度
            parm[@"strideCount"]         = model.count?:@0;         /// 步计数
            parm[@"rampAngleSetting"]    = model.gradient?:@0;      /// 坡度角度设置
            
        } break;
        case BoatEquipment:{ //划船机
            
            parm[@"strokeRate"]         = model.spm?:@0;           /// 划桨频率
            parm[@"strokeCount"]        = model.count?:@0;         /// 划桨次数
            parm[@"averageStrokeRate"]  = model.avgSpm?:@0;        /// 平均划桨速率
            parm[@"instantaneousPace"]  = model.speed?:@0;         /// 瞬时速度
            parm[@"averagePace"]        = model.avgSpeed?:@0;      /// 平均速度
            
        } break;
        case EllipticalEquipment: { //椭圆机
            
            parm[@"instantaneousSpeed"] = model.speed?:@0;         /// 瞬时速度
            parm[@"averageSpeed"]       = model.avgSpeed?:@0;      /// 平均速度
            parm[@"stepPerMinute"]      = model.spm?:@0;           /// 踏频
            parm[@"averageStepRate"]    = model.avgSpm?:@0;        /// 平均踏频
            parm[@"strideCount"]        = model.count?:@0;         /// 步计数
            parm[@"rampAngleSetting"]   = model.gradient?:@0;      /// 坡度角度设置
            
        } break;
        default:
            break;
    }
    
    return parm;
}

@end




/*
 
 + (id)itemWithModel:(BaseEquipDataModel *)model andDeviceId:(NSString *)deviceId{
     
     NSMutableDictionary *parm    = [NSMutableDictionary dictionary];
     parm[@"deviceId"]            = deviceId?:@"";                     /// 设备Id
     parm[@"userId"]              = [User getUserID];                  /// 用户Id
     parm[@"totalDistance"]       = model.totalDistance?:@0;           /// 总距离
     parm[@"resistanceLevel"]     = model.drag?:@0;                    /// 阻力位
     parm[@"instantaneousPower"]  = model.power?:@0;                   /// 瞬时功率
     parm[@"averagePower"]        = model.avgPower?:@0;                /// 平均功率
     parm[@"totalEnergy"]         = model.energy?:@0;                  /// 总能量
     parm[@"heartRate"]           = model.deviceRate?:@0;              /// 设备心率
     parm[@"elapsedTime"]         = model.totalTimeSecond?:@0;         /// 运行时间
     parm[@"remainingTime"]       = model.remainingTimeSecond?:@0;     /// 剩余时间
 //    parm[@"metabolicEquivalent"] = model.modelId;                    /// 代谢当量
     
     int type = model.type.intValue;
     switch (type) {
         case BicycleEquipment:{ //动感单车
             
             parm[@"instantaneousCadence"] = model.spm?:@0;           /// 瞬时节奏
             parm[@"averageCadence"]       = model.avgSpm?:@0;        /// 平均节奏
             parm[@"instantaneousSpeed"]   = model.speed?:@0;         /// 瞬时速度
             parm[@"averageSpeed"]         = model.avgSpeed?:@0;      /// 平均速度
             
         } break;
         case TreadmillEquipment:{ //跑步机
             
             parm[@"instantaneousSpeed"]  = model.speed?:@0;         /// 瞬时速度
             parm[@"averageSpeed"]        = model.avgSpeed?:@0;      /// 平均速度
             parm[@"strideCount"]         = model.count?:@0;         /// 步计数
             parm[@"rampAngleSetting"]    = model.gradient?:@0;      /// 坡度角度设置
             
 //            parm[@"positiveElevationGain"] = model.gradient; // 正仰角增益
 //            parm[@"negativeElevationGain"] = model.gradient; // 负仰角增益
 //            parm[@"inclination"] = model.modelId;            // 倾角
 //            parm[@"stepPerMinute"] = model.modelId;          // 每分钟步数
 //            parm[@"averageStepRate"] = model.modelId;        // 平均步速
             
         } break;
         case BoatEquipment:{ //划船机
             
             parm[@"strokeRate"]         = model.spm?:@0;           /// 划桨频率
             parm[@"strokeCount"]        = model.count?:@0;         /// 划桨次数
             parm[@"averageStrokeRate"]  = model.avgSpm?:@0;        /// 平均划桨速率
             parm[@"instantaneousPace"]  = model.speed?:@0;         /// 瞬时速度
             parm[@"averagePace"]        = model.avgSpeed?:@0;      /// 平均速度
             
         } break;
         case EllipticalEquipment: { //椭圆机
             
             parm[@"instantaneousSpeed"] = model.speed?:@0;         /// 瞬时速度
             parm[@"averageSpeed"]       = model.avgSpeed?:@0;      /// 平均速度
             parm[@"stepPerMinute"]      = model.spm?:@0;           /// 踏频
             parm[@"averageStepRate"]    = model.avgSpm?:@0;        /// 平均踏频
             parm[@"strideCount"]        = model.count?:@0;         /// 步计数
             parm[@"rampAngleSetting"]   = model.gradient?:@0;      /// 坡度角度设置
             
 //            parm[@"stepPerMinute"] = model.modelId;          // 每分钟步数
 //            parm[@"averageStepRate"] = model.modelId;        // 平均步速
 //            parm[@"inclination"] = model.modelId;            // 倾角
 //            parm[@"positiveElevationGain"] = model.gradient; // 正仰角增益
 //            parm[@"negativeElevationGain"] = model.gradient; // 负仰角增益
             
         } break;
         default:
             break;
     }
     
     return parm;
 }
 
 */

