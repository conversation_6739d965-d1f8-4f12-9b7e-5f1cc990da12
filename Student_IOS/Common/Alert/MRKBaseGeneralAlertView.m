//
//  MRKBaseGeneralAlertView.m
//  Student_IOS
//
//  Created by Junq on 2023/12/22.
//

#import "MRKBaseGeneralAlertView.h"
#import "UIView+AZGradient.h"
#import "POP.h"

@interface MRKBaseGeneralAlertView ()

@end

@implementation MRKBaseGeneralAlertView

- (void)show {
    if (self.contentView) {
        [self.contentView addSubview:self];
        
        [self createBlackView];
        [self createMessageView];
    }
}

- (void)hide {
    [super hide];
    
    if (self.contentView) {
        [self removeViews];
    }
}

- (void)removeViews {
    if (self.willDisappearBlock) {
        self.willDisappearBlock(self);
    }
    [UIView animateWithDuration:0.2f animations:^{
        self.containerView.alpha = 0.f;
        self.containerView.transform = CGAffineTransformMakeScale(0.9f, 0.9f);
    } completion:^(BOOL finished) {
        [self removeFromSuperview];
        if (self.didDisappearBlock){
            self.didDisappearBlock(self);
        }
    }];
}

- (void)mrk_interfaceOrientationDidChange {
    [self.containerView mas_updateConstraints:^(MASConstraintMaker *make) {
        if (DeviceMetrics.isLandscape) {
            make.size.mas_equalTo([self reloadContainerViewLandscapeSize]);
        } else {
            make.size.mas_equalTo([self reloadContainerViewPortraitSize]);
        }
    }];
    [self.containerView updateConstraintsIfNeeded];
    [self.containerView setNeedsUpdateConstraints];
}

- (void)createBlackView {
    self.blackView = [[UIView alloc] init];
    self.blackView.backgroundColor = [UIColor colorWithWhite:0 alpha:0.4];
    [self addSubview:self.blackView];
    [self.blackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsMake(0, 0, 0, 0));
    }];
}

- (CGSize)reloadContainerViewPortraitSize{
    [NSException raise:NSStringFromClass([self class]) format:@"Use show method from subclass."];
    return CGSizeZero;
}

- (CGSize)reloadContainerViewLandscapeSize{
    [NSException raise:NSStringFromClass([self class]) format:@"Use show method from subclass."];
    return CGSizeZero;
}

- (void)createMessageView {
    // 创建信息窗体view
    self.containerView = [[UIView alloc] init];
    self.containerView.backgroundColor = [UIColor whiteColor];
    self.containerView.alpha = 0.f;
    self.containerView.layer.cornerRadius = 8.0;
    [self addSubview:self.containerView];
    [self.containerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.mas_equalTo(self.contentView);
        if (DeviceMetrics.isLandscape) {
            make.size.mas_equalTo([self reloadContainerViewLandscapeSize]);
        } else {
            make.size.mas_equalTo([self reloadContainerViewPortraitSize]);
        }
    }];
    
    
    [self setupContainerSubViews];
    
    
    // 执行动画
    POPBasicAnimation  *alpha = [POPBasicAnimation animationWithPropertyNamed:kPOPViewAlpha];
    alpha.toValue             = @(1.f);
    alpha.duration            = 0.3f;
    [self.containerView pop_addAnimation:alpha forKey:nil];
    
    POPSpringAnimation *scale = [POPSpringAnimation animationWithPropertyNamed:kPOPLayerScaleXY];
    scale.fromValue           = [NSValue valueWithCGSize:CGSizeMake(1.1f, 1.1f)];
    scale.toValue             = [NSValue valueWithCGSize:CGSizeMake(1.f, 1.f)];
    scale.dynamicsTension     = 200;
    scale.dynamicsMass        = 1.3;
    scale.dynamicsFriction    = 10.3;
    scale.springSpeed         = 10;
    scale.springBounciness    = 10;
    [self.containerView.layer pop_addAnimation:scale forKey:nil];
    
    @weakify(self);
    scale.completionBlock = ^(POPAnimation *anim, BOOL finished) {
        @strongify(self);
        [self.containerView.subviews enumerateObjectsUsingBlock:^(__kindof UIView *obj, NSUInteger idx, BOOL * _Nonnull stop) {
            if ([obj isKindOfClass:[UIButton class]]) {
                UIButton *btn = obj;
                btn.userInteractionEnabled = YES;
            }
        }];
    };
}

- (void)setupContainerSubViews{
    [NSException raise:NSStringFromClass([self class]) format:@"Use show method from subclass."];
}

- (void)layoutContainerViewSubViews{
    [self layoutIfNeeded];
}

/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/

@end

