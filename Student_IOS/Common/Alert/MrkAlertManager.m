//
//  MrkAlertManager.m
//  Student_IOS
//
//  Created by merit on 2022/12/7.
//

#import "MrkAlertManager.h"
#import "MrkGeneralAlertView.h"

@interface MrkAlertManager ()

@end

@implementation MrkAlertManager

+ (void)showAlert:(NSString *)title
          message:(NSString *)message
           cancel:(NSString *)cancel
           ensure:(NSString *)ensure
      handleIndex:(void (^)(NSInteger index))handle{
    
    MrkGeneralAlertView *alert = [MrkGeneralAlertView build];
    alert.messageObject = MakeAlertViewTitleMessageObject(title, message);
    alert.maskViewTapHidden = YES;
    [alert addBtnWithTitle:cancel btnStyle:kAlertViewButtonCancelStyle clicked:^(MrkGeneralAlertView *alertView) {
        [alertView hide];
        
        if (handle) {
            handle(0);
        }
    }];
    [alert addBtnWithTitle:ensure btnStyle:kAlertViewButtonEnsureStyle clicked:^(MrkGeneralAlertView *alertView) {
        [alertView hide];
        
        if (handle) {
            handle(1);
        }
    }];
    [alert showInKeyWindow];
}

/// 头部带图 弹窗
+ (void)showTopImageAlert:(NSString *)image
                    title:(NSString *)title
                  message:(NSString *)message
                   cancel:(NSString *)cancel
                   ensure:(NSString *)ensure
              handleIndex:(void (^)(NSInteger index))handle {
    
    MrkGeneralAlertView *alert = [MrkGeneralAlertView build];
    alert.messageObject = MakeAlertViewTitleMessageObject(title, message);
    alert.maskViewTapHidden = YES;
    [alert addImage:[UIImage imageNamed:image] imageStyle:kAlertViewImageOutsetTopStyle];
    if ([cancel isNotEmpty]) {
        [alert addBtnWithTitle:cancel btnStyle:kAlertViewButtonCancelStyle clicked:^(MrkGeneralAlertView *alertView) {
            [alertView hide];
            
            if (handle) {
                handle(0);
            }
        }];
    }
    if ([ensure isNotEmpty]) {
        [alert addBtnWithTitle:ensure btnStyle:kAlertViewButtonEnsureStyle clicked:^(MrkGeneralAlertView *alertView) {
            [alertView hide];
            
            if (handle) {
                handle(1);
            }
        }];
    }
    
    [alert showInKeyWindow];
}

/// 头部带图 弹窗
+ (void)showTopGIFImageAlert:(NSString *)image
                       title:(NSString *)title
                     message:(NSString *)message
                      cancel:(NSString *)cancel
                      ensure:(NSString *)ensure
                 handleIndex:(void (^)(NSInteger index))handle {
    
    MrkGeneralAlertView *alert = [MrkGeneralAlertView build];
    alert.messageObject = MakeAlertViewTitleMessageObject(title, message);
    alert.maskViewTapHidden = YES;
    [alert addImage:[YYImage imageNamed:image] imageStyle:kAlertViewImageOutsetTopStyle];
    if ([cancel isNotEmpty]) {
        [alert addBtnWithTitle:cancel btnStyle:kAlertViewButtonCancelStyle clicked:^(MrkGeneralAlertView *alertView) {
            [alertView hide];
            
            if (handle) {
                handle(0);
            }
        }];
    }
    if ([ensure isNotEmpty]) {
        [alert addBtnWithTitle:ensure btnStyle:kAlertViewButtonEnsureStyle clicked:^(MrkGeneralAlertView *alertView) {
            [alertView hide];
            
            if (handle) {
                handle(1);
            }
        }];
    }
    
    [alert showInKeyWindow];
    
}

///keyWindow上加弹窗
+ (void)showAlert:(NSString *)title
          message:(NSString *)message
           ensure:(NSString *)ensure
      handleIndex:(void (^)(NSInteger index))handle {
    
    AlertViewMessageObject *messageObject = MakeAlertViewTitleMessageObject(title, message);
    MrkGeneralAlertView *alert = [MrkGeneralAlertView build];
    alert.messageObject = messageObject;
    [alert addBtnWithTitle:ensure btnStyle:kAlertViewButtonEnsureStyle clicked:^(MrkGeneralAlertView *alertView) {
        [alertView hide];
        
        if (handle) {
            handle(0);
        }
    }];
    [alert showInKeyWindow];
}

+ (void)showAlertInView:(UIView *)view
                message:(NSString *)message
                 cancel:(NSString *)cancel
                 ensure:(NSString *)ensure
            handleIndex:(void (^)(NSInteger index))handle{
    
    AlertViewMessageObject *messageObject = MakeAlertViewTitleMessageObject(@"", message);
    MrkGeneralAlertView *alert = [MrkGeneralAlertView build];
    alert.messageObject = messageObject;
    [alert addBtnWithTitle:cancel btnStyle:kAlertViewButtonCancelStyle clicked:^(MrkGeneralAlertView *alertView) {
        [alertView hide];
        
        if (handle) {
            handle(0);
        }
    }];
    [alert addBtnWithTitle:ensure btnStyle:kAlertViewButtonEnsureStyle clicked:^(MrkGeneralAlertView *alertView) {
        [alertView hide];
        
        if (handle) {
            handle(1);
        }
    }];
    [alert showIn:view];
}

+ (void)showAttriMessage:(NSMutableAttributedString *)attriMessage
                  cancel:(NSString *)cancel
                  ensure:(NSString *)ensure
             handleIndex:(void (^)(NSInteger index))handle{
    AlertViewMessageObject *messageObject = MakeAttributAlertViewTitleMessageObject([[NSMutableAttributedString alloc] initWithString:@""], attriMessage);
    MrkGeneralAlertView *alert = [MrkGeneralAlertView build];
    alert.messageObject = messageObject;
    [alert addBtnWithTitle:cancel btnStyle:kAlertViewButtonCancelStyle clicked:^(MrkGeneralAlertView *alertView) {
        [alertView hide];
        
        if (handle) {
            handle(0);
        }
    }];
    [alert addBtnWithTitle:ensure btnStyle:kAlertViewButtonEnsureStyle clicked:^(MrkGeneralAlertView *alertView) {
        [alertView hide];
        
        if (handle) {
            handle(1);
        }
    }];
    [alert showInKeyWindow];
}

+ (void)showAlertInView:(UIView *)view
                  title:(NSString *)title
                 cancel:(NSString *)cancel
                 ensure:(NSString *)ensure
            handleIndex:(void (^)(NSInteger index))handle {
    
    AlertViewMessageObject *messageObject = MakeAlertViewTitleMessageObject(title, @"");
    MrkGeneralAlertView *alert = [MrkGeneralAlertView build];
    alert.messageObject = messageObject;
    [alert addBtnWithTitle:cancel btnStyle:kAlertViewButtonCancelStyle clicked:^(MrkGeneralAlertView *alertView) {
        [alertView hide];
        
        if (handle) {
            handle(0);
        }
    }];
    [alert addBtnWithTitle:ensure btnStyle:kAlertViewButtonEnsureStyle clicked:^(MrkGeneralAlertView *alertView) {
        [alertView hide];
        
        if (handle) {
            handle(1);
        }
    }];
    
    if (view == nil) {
        [alert showInKeyWindow];
        return;
    }
    [alert showIn:view];
}



+ (void)showAlertTitle:(NSString *)title
                message:(NSString *)message
                 cancel:(NSString *)cancel
                 ensure:(NSString *)ensure
            handleIndex:(void (^)(NSInteger index))handle{
    
    AlertViewMessageObject *messageObject = MakeAlertViewTitleMessageObject(title, message);
    MrkGeneralAlertView *alert = [MrkGeneralAlertView build];
    alert.messageObject = messageObject;
    [alert addBtnWithTitle:cancel btnStyle:kAlertViewButtonCancelStyle clicked:^(MrkGeneralAlertView *alertView) {
        [alertView hide];
        
        if (handle) {
            handle(0);
        }
    }];
    [alert addBtnWithTitle:ensure btnStyle:kAlertViewButtonEnsureStyle clicked:^(MrkGeneralAlertView *alertView) {
        [alertView hide];
        
        if (handle) {
            handle(1);
        }
    }];
    [alert showInKeyWindow];
}


+ (void)showPlayerAlert:(UIView *)view andEquipmentName:(NSString *)name handle:(void (^)(NSInteger index))handle{
    
    NSMutableAttributedString *title = [[NSMutableAttributedString alloc] initWithString:@"温馨提示"];
    title.color = UIColorHex(#1C1A1A) ;
    title.font = [UIFont fontWithName:fontNameMeDium size:DHPX(14.0)];
    title.alignment = NSTextAlignmentCenter;
    
    NSString *tipStr = [NSString stringWithFormat:@"当前课程为超燃脂课程，上课期间会根据课程设置自动调节%@的参数，如有任何不适，请及时调整或暂停训练",name];
    NSMutableAttributedString *message = [[NSMutableAttributedString alloc] initWithString:tipStr];
    message.color = UIColorHex(#666666);
    message.font = [UIFont fontWithName:fontNamePing size:DHPX(12.0)];
    
    AlertViewMessageObject *messageObject = MakeAttributAlertViewTitleMessageObject(title, message);
    MrkGeneralAlertView *alert = [MrkGeneralAlertView build];
    alert.messageObject = messageObject;
    [alert addBtnWithTitle:@"暂不开始" btnStyle:kAlertViewButtonCancelStyle clicked:^(MrkGeneralAlertView *alertView) {
        [alertView hide];
        
        if (handle) {
            handle(0);
        }
    }];
    [alert addBtnWithTitle:@"开始上课" btnStyle:kAlertViewButtonEnsureStyle clicked:^(MrkGeneralAlertView *alertView) {
        [alertView hide];
        
        if (handle) {
            handle(1);
        }
    }];
    [alert showIn:view];
}


+ (void)connectFailureAlert {
    NSMutableAttributedString *title = [[NSMutableAttributedString alloc] initWithString:@"连接失败"];
    title.color = UIColorHex(#1C1A1A) ;
    title.font = [UIFont fontWithName:fontNameMeDium size:DHPX(15.0)];
    title.alignment = NSTextAlignmentCenter;
    
    NSString *tipStr = [NSString stringWithFormat:@"请尝试以下几种方法：\n\n"];
    NSMutableAttributedString *message = [[NSMutableAttributedString alloc] initWithString:tipStr];
    message.color = UIColorHex(#333333);
    message.font = [UIFont fontWithName:fontNamePing size:DHPX(12.0)];
    
    NSMutableAttributedString *about = [[NSMutableAttributedString alloc] initWithString:@"1、请确认设备已开机并在蓝牙连接范围内；\n2、请确认设备未被他人连接；\n3、若仍无法连接，建议断电重启或在【设备管理】中解绑重连；"];
    about.color = UIColorHex(#666666);
    about.font = [UIFont fontWithName:fontNamePing size:DHPX(12.0)];
    
    [message appendAttributedString:about];
    message.alignment = NSTextAlignmentLeft;
    
    AlertViewMessageObject *messageObject = MakeAttributAlertViewTitleMessageObject(title, message);
    MrkGeneralAlertView *alert = [MrkGeneralAlertView build];
    alert.messageObject = messageObject;
    [alert addBtnWithTitle:@"我知道了" btnStyle:kAlertViewButtonEnsureStyle clicked:^(MrkGeneralAlertView *alertView) {
        [alertView hide];
    }];
    [alert showInKeyWindow];
}

///心率预警授权降速降阻弹窗
+ (void)showHeartRateWarnAlertInView:(UIView * __nullable)view
                         detailModek:(EquipmentDetialModel * __nullable)eqModel
                         handleIndex:(void (^)(NSInteger index))handle {
   
    ReportMrkLogParms(1, @"课程-心率预警弹窗", @"hr_alert_popup", @"", nil, 0, nil);
    
    NSString *type = eqModel.productID.intValue == TreadmillEquipment ? @"速度" : @"阻力";
    NSString *tipStr = [NSString stringWithFormat:@"心率监测达到心率预警值\n是否同意系统%@降低%@？", eqModel.isElectromagneticControl ? @"为你"  :@"提示", type];
    
    NSMutableAttributedString *title = [[NSMutableAttributedString alloc] initWithString:tipStr];
    title.color = UIColorHex(#1C1A1A);
    title.font = [UIFont systemFontOfSize:DHPX(15.0) weight:UIFontWeightMedium];
    title.alignment = NSTextAlignmentCenter;
    
    NSDictionary *attributes = @{NSFontAttributeName: [UIFont systemFontOfSize:DHPX(12.0) weight:UIFontWeightRegular],
                                 NSForegroundColorAttributeName: UIColorHex(#999999)};
    NSMutableAttributedString *text = [[NSMutableAttributedString alloc] initWithString:@"可在「心率设备详情-心率预警」中设置心率预警值" attributes:attributes];
    [text setTextHighlightRange:[[text string] rangeOfString:@"「心率设备详情-心率预警」"]
                          color:[UIColor colorWithHexString:@"#333333"]
                backgroundColor:[UIColor clearColor]
                      tapAction:^(UIView * _Nonnull containerView, NSAttributedString * _Nonnull text, NSRange range, CGRect rect) {
    }];
 
    MrkGeneralAlertView *alert = [MrkGeneralAlertView build];
    alert.messageObject = ({
        AlertViewMessageObject *object = [AlertViewMessageObject new];
        object.title                   = title.string;
        object.attributTitle           = title;
        object.bottomTips              = text.string;
        object.bottomAttributTips      = text;
        object.type                    = kAlertViewMessageAttribType;
        object;
    });
    alert.maskViewTapHidden = YES;
    [alert addImage:[UIImage imageNamed:@"hrtrl_warn_alert"] imageStyle:kAlertViewImageInsetTopStyle];
    [alert addBtnWithTitle:@"否" btnStyle:kAlertViewButtonCancelStyle clicked:^(MrkGeneralAlertView *alertView) {
        [alertView hide];
        ReportMrkLogParms(2, @"课程-心率预警弹窗", @"hr_alert_popup", @"hr_alert_popup_cancel", nil, 0, nil);
        if (handle) {
            handle(0);
        }
    }];
    [alert addBtnWithStyle:({
        AlertViewButtonStyle *item = [AlertViewButtonStyle new];
        item.title = @"是";
        item.bgColors = @[UIColorHex(#17D2E3),
                          UIColorHex(#AA69FF),
                          UIColorHex(#FF8FB4)];
        item.style = kAlertViewButtonEnsureStyle;
        item;
    }) clicked:^(MrkGeneralAlertView *alertView) {
        [alertView hide];
        ReportMrkLogParms(2, @"课程-心率预警弹窗", @"hr_alert_popup", @"hr_alert_popup_confirm", nil, 0, nil);
        if (handle) {
            handle(1);
        }
    }];
    
    if (view == nil) {
        view = [UIViewController currentViewController].view;
    }
    [alert showIn:view];
}


///心率预警绑定设备弹窗
+ (void)showHeartRateBuildDeviceAlertInView:(UIView * __nullable)view
                                handleIndex:(void (^)(NSInteger index))handle {
    ReportMrkLogParms(1, @"智控模式提示绑定设备弹窗", @"smart_mode_bind_alert", @"", nil, 0, nil);
    
    
    NSMutableAttributedString *title = [[NSMutableAttributedString alloc] initWithString:@"欢迎开启心率智控模式"];
    title.color = UIColorHex(#1C1A1A);
    title.font = [UIFont systemFontOfSize:DHPX(15.0) weight:UIFontWeightMedium];
    title.alignment = NSTextAlignmentCenter;
    
    NSMutableAttributedString *content = [[NSMutableAttributedString alloc] initWithString:@"MIA将结合您的心率数据,智能调控训练强度,帮助您在短时间内达到最佳燃脂效果"];
    content.color = UIColorHex(#666666);
    content.font = [UIFont systemFontOfSize:DHPX(13.0)];
    content.alignment = NSTextAlignmentCenter;
    
    NSMutableAttributedString *tip = [[NSMutableAttributedString alloc] initWithString:@"请先绑定心率设备,以便同步心率数据"];
    tip.color = UIColorHex(#999999);
    tip.font = [UIFont systemFontOfSize:DHPX(12.0)];
    tip.alignment = NSTextAlignmentCenter;
    
    MrkGeneralAlertView *alert = [MrkGeneralAlertView build];
    alert.messageObject = ({
        AlertViewMessageObject *object = [AlertViewMessageObject new];
        object.title                   = title.string;
        object.attributTitle           = title;
        object.content                 = content.string;
        object.attributContent         = content;
        object.bottomTips              = tip.string;
        object.bottomAttributTips      = tip;
        object.type                    = kAlertViewMessageAttribType;
        object;
    });
    alert.maskViewTapHidden = YES;
    [alert addImage:[UIImage imageNamed:@"hrtrl_warn_alert"] imageStyle:kAlertViewImageInsetTopStyle];
    [alert addBtnWithTitle:@"取消" btnStyle:kAlertViewButtonCancelStyle clicked:^(MrkGeneralAlertView *alertView) {
        [alertView hide];
        ReportMrkLogParms(2, @"智控模式提示绑定设备弹窗", @"smart_mode_bind_alert", @"smart_mode_cancel_btn", nil, 0, nil);
        if (handle) {
            handle(0);
        }
    }];
    [alert addBtnWithStyle:({
        AlertViewButtonStyle *item = [AlertViewButtonStyle new];
        item.title = @"绑定设备";
        item.bgColors = @[UIColorHex(#17D2E3),
                          UIColorHex(#AA69FF),
                          UIColorHex(#FF8FB4)];
        item.style = kAlertViewButtonEnsureStyle;
        item;
    }) clicked:^(MrkGeneralAlertView *alertView) {
        [alertView hide];
        ReportMrkLogParms(2, @"智控模式提示绑定设备弹窗", @"smart_mode_bind_alert", @"smart_mode_bind_btn", nil, 0, nil);
        if (handle) {
            handle(1);
        }
    }];
    
    if (view == nil) {
        view = [UIViewController currentViewController].view;
    }
    [alert showIn:view];
}


+ (void)freeVipForNewUser:(int)days completion:(void(^)(void))completion {
    NSString *title = [NSString stringWithFormat:@"%d天会员已到账", days];
    MrkGeneralAlertView *alert = [MrkGeneralAlertView build];
    alert.messageObject = MakeAlertViewTitleMessageObject(title, @"即刻体验5000+课程，探索AI玩法！");
    alert.maskViewTapHidden = YES;
    [alert addImage:[UIImage imageNamed:@"pic_vip_gif"] imageStyle:kAlertViewImageInsetTopStyle];
    [alert addBtnWithTitle:@"好的" btnStyle:kAlertViewButtonEnsureStyle clicked:^(MrkGeneralAlertView *alertView) {
        [alertView hide];
        
        if (completion) {
            completion();
        }
    }];
    [alert showInKeyWindow];
}



+ (void)closeAccountAlertHandle:(void (^)(NSInteger index))handle{
    NSMutableAttributedString *title = [[NSMutableAttributedString alloc] initWithString:@"谨慎操作"];
    title.color = UIColorHex(#1C1A1A) ;
    title.font = [UIFont fontWithName:fontNameMeDium size:DHPX(15.0)];
    title.alignment = NSTextAlignmentCenter;
    
    NSString *tipStr = [NSString stringWithFormat:@"请阅读以下内容：\n\n"];
    NSMutableAttributedString *message = [[NSMutableAttributedString alloc] initWithString:tipStr];
    message.color = UIColorHex(#333333);
    message.font = [UIFont fontWithName:fontNamePing size:DHPX(12.0)];
    
    NSMutableAttributedString *about = [[NSMutableAttributedString alloc] initWithString:@"1、注销后该账号的全部个人资料和历史信息将无法找回\n2、该账号下的观看、运动记录都将被清空，且无法恢复\n3、该账号如有订阅自动续费服务，为避免继续扣费，请操作前先关闭会员订阅服务"];
    about.color = UIColorHex(#666666);
    about.font = [UIFont fontWithName:fontNamePing size:DHPX(12.0)];
    
    [message appendAttributedString:about];
    message.alignment = NSTextAlignmentLeft;
    
    AlertViewMessageObject *messageObject = MakeAttributAlertViewTitleMessageObject(title, message);
    MrkGeneralAlertView *alert = [MrkGeneralAlertView build];
    alert.messageObject = messageObject;
    [alert addBtnWithTitle:@"我再想想" btnStyle:kAlertViewButtonCancelStyle clicked:^(MrkGeneralAlertView *alertView) {
        [alertView hide];
        
        if (handle) {
            handle(0);
        }
    }];
    [alert addBtnWithTitle:@"确认注销" btnStyle:kAlertViewButtonEnsureStyle clicked:^(MrkGeneralAlertView *alertView) {
        [alertView hide];
        
        if (handle) {
            handle(1);
        }
    }];
    [alert showInKeyWindow];
}


@end
