//
//  MrkAlertManager.h
//  Student_IOS
//
//  Created by merit on 2022/12/7.
//

#import <Foundation/Foundation.h>


NS_ASSUME_NONNULL_BEGIN

@interface MrkAlertManager : NSObject

///keyWindow上加弹窗
+ (void)showAlert:(NSString *)title
          message:(NSString *)message
           cancel:(NSString *)cancel
           ensure:(NSString *)ensure
      handleIndex:(void (^)(NSInteger index))handle;

/// 头部带图 弹窗
+ (void)showTopImageAlert:(NSString *)image
                    title:(NSString *)title
                  message:(NSString *)message
                   cancel:(NSString *)cancel
                   ensure:(NSString *)ensure
              handleIndex:(void (^)(NSInteger index))handle;

/// 头部带图 弹窗
+ (void)showTopGIFImageAlert:(NSString *)image
                    title:(NSString *)title
                  message:(NSString *)message
                   cancel:(NSString *)cancel
                   ensure:(NSString *)ensure
              handleIndex:(void (^)(NSInteger index))handle;

///keyWindow上加弹窗
+ (void)showAlert:(NSString *)title
          message:(NSString *)message
           ensure:(NSString *)ensure
      handleIndex:(void (^)(NSInteger index))handle;

///只有message的弹窗
+ (void)showAlertInView:(UIView *)view
                message:(NSString *)message
                 cancel:(NSString *)cancel
                 ensure:(NSString *)ensure
            handleIndex:(void (^)(NSInteger index))handle;

+ (void)showAttriMessage:(NSMutableAttributedString *)attriMessage
                 cancel:(NSString *)cancel
                 ensure:(NSString *)ensure
            handleIndex:(void (^)(NSInteger index))handle;

///只有title的弹窗
+ (void)showAlertInView:(UIView * __nullable)view
                  title:(NSString *)title
                 cancel:(NSString *)cancel
                 ensure:(NSString *)ensure
            handleIndex:(void (^)(NSInteger index))handle;

+ (void)showAlertTitle:(NSString *)title
                message:(NSString *)message
                 cancel:(NSString *)cancel
                 ensure:(NSString *)ensure
            handleIndex:(void (^)(NSInteger index))handle;





///视频温馨弹窗
+ (void)showPlayerAlert:(UIView *)view
       andEquipmentName:(NSString *)name
                 handle:(void (^)(NSInteger index))handle;

///设备连接失败弹窗
+ (void)connectFailureAlert;


///心率预警授权降速降阻弹窗
+ (void)showHeartRateWarnAlertInView:(UIView * __nullable)view
                         detailModek:(EquipmentDetialModel * __nullable)eqModel
                         handleIndex:(void (^)(NSInteger index))handle;

///心率预警绑定设备弹窗
+ (void)showHeartRateBuildDeviceAlertInView:(UIView * __nullable)view
                                handleIndex:(void (^)(NSInteger index))handle;


+ (void)freeVipForNewUser:(int)days completion:(void(^)(void))completion;

///注销账户
+ (void)closeAccountAlertHandle:(void (^)(NSInteger index))handle;

@end

NS_ASSUME_NONNULL_END
