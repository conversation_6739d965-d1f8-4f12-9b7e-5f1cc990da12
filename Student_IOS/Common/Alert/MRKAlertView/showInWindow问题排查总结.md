# showInWindow 方法 containerView 显示问题排查总结

## 🔍 问题描述

在 `MRKSwiftAlertView` 的 `showInWindow` 方法中，`containerView` 无法正常显示，用户看不到弹窗内容。

## 🕵️ 问题排查过程

### 1. 问题定位

通过分析 `showInWindow` 的执行流程：

```swift
func showInWindow() {
    showBackgroundWindow()     // 显示背景窗口
    createAlertWindow()        // 创建弹窗窗口
    performShowAnimation()     // 执行显示动画
}
```

发现问题可能出现在以下几个环节：
- `containerView` 的布局设置
- 视图层次结构
- 动画初始状态设置

### 2. 发现的关键问题

#### 问题 1：`layoutContainerView` 设置了错误的 frame
```swift
// 🚫 原始代码（有问题）
@objc open func layoutContainerView() {
    containerView.frame = CGRectZero  // ❌ 设置为零尺寸！
}
```

**影响**：`containerView` 的尺寸为零，导致完全看不到内容。

#### 问题 2：缺少默认的布局约束
原始代码没有提供合理的默认布局，子类如果不重写 `layoutContainerView` 方法，容器就无法正确显示。

#### 问题 3：动画初始状态使用了错误的坐标系
```swift
// 🚫 原始代码（有问题）
containerView.transform = CGAffineTransform(translationX: 0, y: bounds.height)
```

**影响**：在 Window 模式下，`self.bounds` 可能还没有正确设置，导致动画位置计算错误。

## 🔧 修复方案

### 修复 1：提供合理的默认布局

```swift
/// 布局容器视图的位置
@objc open func layoutContainerView() {
    // 默认实现：居中显示，使用 Auto Layout
    containerView.translatesAutoresizingMaskIntoConstraints = false
    NSLayoutConstraint.activate([
        containerView.centerXAnchor.constraint(equalTo: centerXAnchor),
        containerView.centerYAnchor.constraint(equalTo: centerYAnchor),
        containerView.widthAnchor.constraint(lessThanOrEqualTo: widthAnchor, constant: -32),
        containerView.heightAnchor.constraint(lessThanOrEqualTo: heightAnchor, constant: -64)
    ])
}
```

**优势**：
- 提供合理的默认布局
- 使用 Auto Layout 确保在不同屏幕尺寸下都能正确显示
- 子类仍可以重写实现自定义布局

### 修复 2：完善默认样式

```swift
@objc open func setupContainerViewAttributes() {
    // 默认样式：白色背景，圆角，阴影
    containerView.backgroundColor = .white
    containerView.layer.cornerRadius = 12
    containerView.layer.masksToBounds = false
    containerView.layer.shadowColor = UIColor.black.cgColor
    containerView.layer.shadowOpacity = 0.15
    containerView.layer.shadowOffset = CGSize(width: 0, height: 4)
    containerView.layer.shadowRadius = 12
}
```

### 修复 3：提供默认内容

```swift
@objc open func setupContainerSubViews() {
    // 默认实现：添加一个测试标签（如果容器为空）
    if containerView.subviews.isEmpty {
        let testLabel = UILabel()
        testLabel.text = "MRKSwiftAlertView\n默认内容"
        testLabel.textAlignment = .center
        testLabel.numberOfLines = 0
        // ... 设置约束
    }
}
```

**优势**：即使子类没有重写，也能看到基本内容，便于调试。

### 修复 4：修复动画坐标系问题

```swift
func setupInitialAnimationState() {
    // 确保布局已完成
    layoutIfNeeded()
    
    let screenHeight = UIScreen.main.bounds.height  // 使用屏幕高度
    
    switch transitionStyle {
    case .slideFromBottom:
        containerView.transform = CGAffineTransform(translationX: 0, y: screenHeight)
    // ...
    }
}
```

### 修复 5：添加布局检查

```swift
public override func layoutSubviews() {
    super.layoutSubviews()
    
    if isLayoutDirty {
        // 确保容器视图的约束已激活
        if containerView.translatesAutoresizingMaskIntoConstraints {
            layoutContainerView()
        }
        isLayoutDirty = false
    }
}
```

## 🧪 验证方案

### 1. 基础显示测试
```swift
let alert = MRKSwiftAlertView(animationStyle: .slideFromBottom)
alert.show()  // 应该能看到默认内容
```

### 2. 自定义内容测试
```swift
class CustomAlert: MRKSwiftAlertView {
    override func setupContainerSubViews() {
        // 添加自定义内容
    }
}
```

### 3. 不同动画效果测试
测试各种 `transitionStyle` 是否都能正常显示。

## 📋 调试信息

添加了详细的调试日志：

```swift
print("MRKSwiftAlertView: showInWindow called")
print("MRKSwiftAlertView: viewDidLoad called")
print("MRKSwiftAlertView: setupContainerView called")
print("MRKSwiftAlertView: containerView.frame = \(containerView.frame)")
```

## 🎯 修复效果

### 修复前
- ❌ `containerView` 尺寸为零，完全看不到
- ❌ 没有默认内容，空白弹窗
- ❌ 动画位置计算错误
- ❌ 缺少调试信息，难以排查

### 修复后
- ✅ `containerView` 有合理的默认尺寸和位置
- ✅ 提供默认内容，即使不自定义也能看到
- ✅ 动画使用正确的坐标系
- ✅ 丰富的调试信息，便于问题排查
- ✅ 保持向后兼容，子类仍可自定义

## 🔄 测试流程

1. **基础测试**：直接调用 `show()` 方法，验证是否能看到默认内容
2. **自定义测试**：创建自定义子类，验证自定义内容是否正常显示
3. **动画测试**：测试不同的 `transitionStyle` 是否都能正常工作
4. **背景测试**：验证背景点击是否正常响应
5. **内存测试**：确保弹窗能正确释放，没有内存泄漏

## 📝 注意事项

1. **向后兼容**：所有修改都保持了向后兼容性，现有的自定义子类不需要修改
2. **性能考虑**：使用 Auto Layout 而不是手动计算 frame，确保在不同设备上都能正确显示
3. **调试友好**：添加了详细的日志输出，便于后续问题排查
4. **默认体验**：即使开发者忘记自定义内容，也能看到基本的弹窗效果

## 🚀 后续优化建议

1. **单元测试**：为关键方法添加单元测试，确保修复的稳定性
2. **文档完善**：更新使用文档，说明如何正确自定义弹窗内容
3. **示例代码**：提供更多的使用示例，帮助开发者快速上手
4. **性能监控**：添加性能监控，确保弹窗显示的流畅性
