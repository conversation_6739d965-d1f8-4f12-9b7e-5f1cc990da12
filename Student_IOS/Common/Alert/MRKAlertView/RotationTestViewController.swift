//
//  RotationTestViewController.swift
//  Student_IOS
//
//  Created by merit on 2025/8/7.
//  测试 MRKSwiftAlertView 在 iPad 上的旋转支持
//

import UIKit

class RotationTestViewController: UIViewController {
    
    private let scrollView = UIScrollView()
    private let stackView = UIStackView()
    private let statusLabel = UILabel()
    private let logTextView = UITextView()
    
    private var logMessages: [String] = []
    private var currentAlert: MRKSwiftAlertView?
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        startOrientationMonitoring()
    }
    
    deinit {
        stopOrientationMonitoring()
    }
    
    private func setupUI() {
        title = "iPad 旋转测试"
        view.backgroundColor = .systemBackground
        
        // 状态标签
        statusLabel.text = getCurrentOrientationStatus()
        statusLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        statusLabel.textAlignment = .center
        statusLabel.backgroundColor = UIColor.systemBlue.withAlphaComponent(0.1)
        statusLabel.layer.cornerRadius = 8
        statusLabel.clipsToBounds = true
        statusLabel.numberOfLines = 0
        
        // 滚动视图
        scrollView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(scrollView)
        
        // 堆栈视图
        stackView.axis = .vertical
        stackView.spacing = 16
        stackView.translatesAutoresizingMaskIntoConstraints = false
        scrollView.addSubview(stackView)
        
        // 日志文本视图
        logTextView.font = UIFont.monospacedSystemFont(ofSize: 12, weight: .regular)
        logTextView.backgroundColor = UIColor.systemGray6
        logTextView.layer.cornerRadius = 8
        logTextView.isEditable = false
        logTextView.text = "📋 旋转测试日志:\n"
        
        // 添加控件到堆栈视图
        stackView.addArrangedSubview(statusLabel)
        
        // 测试按钮
        let buttons = [
            ("显示 Window 模式弹窗", #selector(showWindowAlert)),
            ("显示嵌入模式弹窗", #selector(showEmbeddedAlert)),
            ("显示自定义内容弹窗", #selector(showCustomAlert)),
            ("隐藏当前弹窗", #selector(hideCurrentAlert)),
            ("清空日志", #selector(clearLog))
        ]
        
        for (title, action) in buttons {
            let button = createButton(title: title, action: action)
            stackView.addArrangedSubview(button)
        }
        
        stackView.addArrangedSubview(logTextView)
        
        // 设置约束
        setupConstraints()
    }
    
    private func setupConstraints() {
        NSLayoutConstraint.activate([
            scrollView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            scrollView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            scrollView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            scrollView.bottomAnchor.constraint(equalTo: view.bottomAnchor),
            
            stackView.topAnchor.constraint(equalTo: scrollView.topAnchor, constant: 20),
            stackView.leadingAnchor.constraint(equalTo: scrollView.leadingAnchor, constant: 20),
            stackView.trailingAnchor.constraint(equalTo: scrollView.trailingAnchor, constant: -20),
            stackView.bottomAnchor.constraint(equalTo: scrollView.bottomAnchor, constant: -20),
            stackView.widthAnchor.constraint(equalTo: scrollView.widthAnchor, constant: -40),
            
            statusLabel.heightAnchor.constraint(greaterThanOrEqualToConstant: 60),
            logTextView.heightAnchor.constraint(equalToConstant: 300)
        ])
    }
    
    private func createButton(title: String, action: Selector) -> UIButton {
        let button = UIButton(type: .system)
        button.setTitle(title, for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 16)
        button.backgroundColor = .systemBlue
        button.setTitleColor(.white, for: .normal)
        button.layer.cornerRadius = 8
        button.addTarget(self, action: action, for: .touchUpInside)
        
        button.translatesAutoresizingMaskIntoConstraints = false
        button.heightAnchor.constraint(equalToConstant: 50).isActive = true
        
        return button
    }
    
    // MARK: - 方向监控
    
    private func startOrientationMonitoring() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(orientationDidChange),
            name: UIDevice.orientationDidChangeNotification,
            object: nil
        )
    }
    
    private func stopOrientationMonitoring() {
        NotificationCenter.default.removeObserver(self, name: UIDevice.orientationDidChangeNotification, object: nil)
    }
    
    @objc private func orientationDidChange() {
        DispatchQueue.main.async {
            self.statusLabel.text = self.getCurrentOrientationStatus()
            self.addLog("📱 设备方向发生变化: \(self.getCurrentOrientationString())")
        }
    }
    
    private func getCurrentOrientationStatus() -> String {
        let orientation = getCurrentOrientationString()
        let size = view.bounds.size
        return "当前方向: \(orientation)\n屏幕尺寸: \(Int(size.width)) × \(Int(size.height))"
    }
    
    private func getCurrentOrientationString() -> String {
        let orientation = UIDevice.current.orientation
        switch orientation {
        case .portrait:
            return "竖屏"
        case .portraitUpsideDown:
            return "倒竖屏"
        case .landscapeLeft:
            return "左横屏"
        case .landscapeRight:
            return "右横屏"
        case .faceUp:
            return "平放向上"
        case .faceDown:
            return "平放向下"
        default:
            return "未知方向"
        }
    }
    
    // MARK: - 测试方法
    
    @objc private func showWindowAlert() {
        addLog("🪟 显示 Window 模式弹窗")
        
        let alert = RotationTestAlert(
            title: "Window 模式测试",
            message: "这是一个 Window 模式的弹窗\n请旋转 iPad 测试背景是否跟随旋转",
            mode: .window
        )
        
        currentAlert = alert
        alert.show()
        
        addLog("✅ Window 模式弹窗已显示")
    }
    
    @objc private func showEmbeddedAlert() {
        addLog("📱 显示嵌入模式弹窗")
        
        let alert = RotationTestAlert(
            title: "嵌入模式测试",
            message: "这是一个嵌入模式的弹窗\n请旋转 iPad 测试背景是否跟随旋转",
            mode: .embedded
        )
        
        currentAlert = alert
        alert.show(in: view)
        
        addLog("✅ 嵌入模式弹窗已显示")
    }
    
    @objc private func showCustomAlert() {
        addLog("🎨 显示自定义内容弹窗")
        
        let alert = RotationTestAlert(
            title: "自定义内容测试",
            message: "这是一个自定义内容的弹窗\n背景使用渐变效果\n请旋转 iPad 测试效果",
            mode: .window
        )
        
        alert.backgroundStyle = .gradient
        alert.isAutoHidden = true
        
        currentAlert = alert
        alert.show()
        
        addLog("✅ 自定义内容弹窗已显示")
    }
    
    @objc private func hideCurrentAlert() {
        if let alert = currentAlert {
            addLog("❌ 隐藏当前弹窗")
            alert.dismiss(animated: true)
            currentAlert = nil
        } else {
            addLog("⚠️ 没有当前弹窗需要隐藏")
        }
    }
    
    @objc private func clearLog() {
        logMessages.removeAll()
        logTextView.text = "📋 旋转测试日志:\n"
    }
    
    private func addLog(_ message: String) {
        let timestamp = DateFormatter.timeFormatter.string(from: Date())
        let logMessage = "[\(timestamp)] \(message)"
        logMessages.append(logMessage)
        
        let allLogs = "📋 旋转测试日志:\n" + logMessages.joined(separator: "\n")
        logTextView.text = allLogs
        
        // 滚动到底部
        DispatchQueue.main.async {
            if self.logTextView.text.count > 0 {
                let bottom = NSMakeRange(self.logTextView.text.count - 1, 1)
                self.logTextView.scrollRangeToVisible(bottom)
            }
        }
        
        print(logMessage)
    }
    
    // MARK: - 旋转支持
    
    override var shouldAutorotate: Bool {
        return UIDevice.current.userInterfaceIdiom == .pad
    }
    
    override var supportedInterfaceOrientations: UIInterfaceOrientationMask {
        return .all
    }
    
    override func viewWillTransition(to size: CGSize, with coordinator: UIViewControllerTransitionCoordinator) {
        super.viewWillTransition(to: size, with: coordinator)
        
        addLog("🔄 视图即将旋转到尺寸: \(Int(size.width)) × \(Int(size.height))")
        
        coordinator.animate(alongsideTransition: { _ in
            self.statusLabel.text = self.getCurrentOrientationStatus()
        }, completion: { _ in
            self.addLog("✅ 视图旋转完成")
        })
    }
}

// MARK: - 测试弹窗类

class RotationTestAlert: MRKSwiftAlertView {
    
    private let titleLabel = UILabel()
    private let messageLabel = UILabel()
    private let confirmButton = UIButton()
    
    private let alertTitle: String
    private let alertMessage: String
    private let displayMode: MRKAlertViewPresentationMode
    
    init(title: String, message: String, mode: MRKAlertViewPresentationMode) {
        self.alertTitle = title
        self.alertMessage = message
        self.displayMode = mode
        
        super.init(animationStyle: .slideFromBottom)
        
        self.isAutoHidden = false // 不自动隐藏，方便测试
        self.shouldAutorotate = UIDevice.current.userInterfaceIdiom == .pad
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func setupContainerViewAttributes() {
        super.setupContainerViewAttributes()
        // 使用父类的默认样式
    }
    
    override func setupContainerSubViews() {
        // 清空默认内容
        containerView.subviews.forEach { $0.removeFromSuperview() }
        
        // 配置标题
        titleLabel.text = alertTitle
        titleLabel.font = UIFont.boldSystemFont(ofSize: 18)
        titleLabel.textColor = .black
        titleLabel.textAlignment = .center
        containerView.addSubview(titleLabel)
        
        // 配置消息
        messageLabel.text = alertMessage
        messageLabel.font = UIFont.systemFont(ofSize: 16)
        messageLabel.textColor = .darkGray
        messageLabel.textAlignment = .center
        messageLabel.numberOfLines = 0
        containerView.addSubview(messageLabel)
        
        // 配置确认按钮
        confirmButton.setTitle("确定", for: .normal)
        confirmButton.setTitleColor(.white, for: .normal)
        confirmButton.backgroundColor = .systemBlue
        confirmButton.layer.cornerRadius = 8
        confirmButton.addTarget(self, action: #selector(confirmTapped), for: .touchUpInside)
        containerView.addSubview(confirmButton)
    }
    
    override func layoutContainerViewSubViews() {
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        messageLabel.translatesAutoresizingMaskIntoConstraints = false
        confirmButton.translatesAutoresizingMaskIntoConstraints = false
        
        NSLayoutConstraint.activate([
            // 标题约束
            titleLabel.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 20),
            titleLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 20),
            titleLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -20),
            
            // 消息约束
            messageLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 16),
            messageLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 20),
            messageLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -20),
            
            // 按钮约束
            confirmButton.topAnchor.constraint(equalTo: messageLabel.bottomAnchor, constant: 20),
            confirmButton.centerXAnchor.constraint(equalTo: containerView.centerXAnchor),
            confirmButton.widthAnchor.constraint(equalToConstant: 120),
            confirmButton.heightAnchor.constraint(equalToConstant: 44),
            confirmButton.bottomAnchor.constraint(equalTo: containerView.bottomAnchor, constant: -20)
        ])
    }
    
    @objc private func confirmTapped() {
        dismiss(animated: true)
    }
}

// MARK: - 扩展

private extension DateFormatter {
    static let timeFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm:ss"
        return formatter
    }()
}
