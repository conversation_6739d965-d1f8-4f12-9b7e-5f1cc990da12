//
//  MRKAlertView.swift
//  Student_IOS
//
//  Created by merit on 2025/8/7.
//  Swift重构版本，支持Objective-C使用
//

import UIKit

// MARK: - 枚举定义

extension UIWindow.Level {
    static let evaluation = UIWindow.Level(rawValue: 900)
    static let evaluationBackground = UIWindow.Level(rawValue: 899)
}

/// 背景效果类型
@objc public enum MRKAlertViewBackgroundStyle: Int {
    case solid = 0              // 纯色背景
    case gradient               // 渐变背景
    case gradientHeavy          // 重渐变背景
    case clear                  // 透明背景
}

/// 动画效果类型
@objc public enum MRKAlertViewTransitionStyle: Int {
    case slideFromBottom = 0    // 从底部滑入
    case fade                   // 淡入淡出
    case bounce                 // 弹跳效果
    case dropDown               // 下拉效果
    case slideFromTop           // 从顶部滑入
}

/// 展示模式
@objc public enum MRKAlertViewPresentationMode: Int {
    case window                // 以Window形式展示（默认）
    case embedded              // 嵌入到指定View中展示
}

// MARK: - 代理协议

@objc public protocol MRKActionAlertViewDelegate: AnyObject {
    @objc optional func actionAlertViewWillShow()                  // 即将出现
    @objc optional func actionAlertViewDidShow()                   // 已经出现
    @objc optional func actionAlertViewWillDismiss()               // 即将消失
    @objc optional func actionAlertViewDidDismiss()                // 已经消失
    @objc optional func actionAlertViewDidSelectBackGroundView()   // 点击背景
}

// MARK: - 主类

@objc @objcMembers
public class MRKSwiftAlertView: UIView {
    
    // MARK: - 公共属性
    
    /// 代理
    @objc public weak var delegate: MRKActionAlertViewDelegate?
    
    /// 背景效果
    @objc public var backgroundStyle: MRKAlertViewBackgroundStyle = .solid
    
    /// 动画效果
    @objc public var transitionStyle: MRKAlertViewTransitionStyle = .slideFromBottom
    
    /// 是否正在显示
    @objc public private(set) var isVisible: Bool = false
    
    /// 容器视图
    @objc public private(set) var containerView: UIView = UIView()
    
    /// 是否点击背景隐藏
    @objc public var isAutoHidden: Bool = false
    
    /// 背景不透明度
    @objc public var opaqueness: Float = 0.4
    
    /// 展示模式
    @objc public var presentationMode: MRKAlertViewPresentationMode = .window
    
    /// 父视图（当使用embedded模式时）
    @objc public weak var parentView: UIView?
    
    /// 是否支持旋转（iPad默认支持，iPhone默认不支持）
    @objc public var shouldAutorotate: Bool = {
        return UIDevice.current.userInterfaceIdiom == .pad
    }()
    
    // MARK: - 私有属性
    
    private var alertWindow: UIWindow?
    private var backgroundView: UIView?
    private var alertViewController: MRKAlertViewController?
    private var isLayoutDirty: Bool = true
    private var oldKeyWindow: UIWindow?
    
    // MARK: - 静态属性
    
    private static var sharedQueue: [MRKSwiftAlertView] = []
    private static var isAnimating: Bool = false
    private static var currentAlertView: MRKSwiftAlertView?
    private static var backgroundWindow: MRKAlertViewBackgroundWindow?
    
    // MARK: - 初始化方法
    
    /// 便利构造方法
    @objc public class func actionAlertView(withAnimationStyle style: MRKAlertViewTransitionStyle) -> Self {
        return self.init(animationStyle: style)
    }
    
    /// 指定初始化方法
    @objc public required init(animationStyle style: MRKAlertViewTransitionStyle) {
        super.init(frame: .zero)
        self.transitionStyle = style
        setupInitialState()
    }
    
    public override init(frame: CGRect) {
        super.init(frame: frame)
        setupInitialState()
    }
    
    required public init?(coder: NSCoder) {
        super.init(coder: coder)
        setupInitialState()
    }
    
    private func setupInitialState() {
        self.isAutoHidden = false
        self.backgroundStyle = .solid
        self.opaqueness = 0.4
        addSubview(containerView)
        
        // 设置设备相关的默认值
        self.shouldAutorotate = UIDevice.current.userInterfaceIdiom == .pad
    }
    
    // MARK: - 公共方法
    
    /// 显示弹窗（默认以Window模式）
    @objc public func show() {
        showWithMode(.window, in: nil)
    }

    /// 在指定视图中显示弹窗
    @objc public func show(in parentView: UIView) {
        showWithMode(.embedded, in: parentView)
    }

    /// 指定模式显示弹窗
    @objc public func showWithMode(_ mode: MRKAlertViewPresentationMode, in parentView: UIView?) {
        if isVisible {
            return
        }

        // 如果当前有弹窗正在显示或正在执行动画，则加入队列
        if Self.currentAlertView != nil || Self.isAnimating {
            Self.addToQueue(self)
            return
        }

        // 设置当前弹窗
        Self.currentAlertView = self
        Self.isAnimating = true

        self.presentationMode = mode
        self.parentView = parentView

        switch mode {
        case .window:
            showInWindow()
        case .embedded:
            guard let parentView = parentView else {
                print("MRKAlertView: parentView is required for embedded mode")
                Self.isAnimating = false
                Self.currentAlertView = nil
                return
            }
            showInView(parentView)
        }
    }
    
    /// 隐藏弹窗
    @objc public func dismiss(animated: Bool) {
        dismiss(animated: animated, completion: nil)
    }
    
    /// 隐藏弹窗（带完成回调）
    @objc public func dismiss(animated: Bool, completion: (() -> Void)?) {
        if !isVisible {
            completion?()
            return
        }

        delegate?.actionAlertViewWillDismiss?()

        let dismissBlock = { [weak self] in
            guard let self = self else { return }

            switch self.presentationMode {
            case .window:
                self.dismissFromWindow(animated: animated) { [weak self] in
                    completion?()
                    self?.handleDismissComplete()
                }
            case .embedded:
                self.dismissFromView(animated: animated) { [weak self] in
                    completion?()
                    self?.handleDismissComplete()
                }
            }
        }

        if animated {
            performDismissAnimation(completion: dismissBlock)
        } else {
            dismissBlock()
        }
    }

    /// 处理隐藏完成后的逻辑
    private func handleDismissComplete() {
        // 清除当前弹窗引用
        if Self.currentAlertView === self {
            Self.currentAlertView = nil
        }

        // 重置动画状态
        Self.isAnimating = false

        // 显示队列中的下一个弹窗
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            Self.showNext()
        }
    }
    
    // MARK: - 子类需要重写的方法
    
    /// 布局容器视图的位置
    @objc open func layoutContainerView() {
        // 子类重写实现具体布局
        containerView.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            containerView.centerXAnchor.constraint(equalTo: centerXAnchor),
            containerView.centerYAnchor.constraint(equalTo: centerYAnchor),
            containerView.widthAnchor.constraint(lessThanOrEqualTo: widthAnchor, constant: -32),
            containerView.heightAnchor.constraint(lessThanOrEqualTo: heightAnchor, constant: -64)
        ])
    }
    
    /// 设置容器视图的属性
    
    /// 初始化容器视图及其子视图（供子类重写）
    @objc open func setupContainerView() {
        setupContainerViewAttributes()
        setupContainerSubViews()
        
        layoutContainerView()
        layoutContainerViewSubViews()
    }

    @objc open func setupContainerViewAttributes() {
        // 子类重写实现具体属性设置
        containerView.backgroundColor = .white
        containerView.layer.cornerRadius = 12
        containerView.layer.masksToBounds = true
    }
    
    /// 给容器视图添加子视图
    @objc open func setupContainerSubViews() {
        // 子类重写实现具体子视图添加
    }
    
    /// 设置子视图的约束
    @objc open func layoutContainerViewSubViews() {
        // 子类重写实现具体子视图布局
    }
    
    // MARK: - 生命周期方法（供控制器调用）
    
    @objc public func beforeSuperViewDidLoad() {
        // 在控制器viewDidLoad之前调用
    }
    
    @objc public func viewDidLoad() {
        setupContainerView()
        invalidateLayout()
    }
    
    @objc public func resetTransition() {
        // 重置动画状态
    }
    
    @objc public func invalidateLayout() {
        isLayoutDirty = true
        setNeedsLayout()
    }
    
    @objc public func reloadBackGroundLayout(size: CGSize) {
        // 重新布局背景
        if presentationMode == .window {
            MRKSwiftAlertView.backgroundWindow?.frame = CGRect(origin: .zero, size: size)
        } else {
            backgroundView?.frame = CGRect(origin: .zero, size: size)
        }
    }
}

// MARK: - 私有实现

extension UIApplication {
    var currentKeyWindow: UIWindow? {
        if #available(iOS 13.0, *) {
            return self
                .connectedScenes
                .compactMap { $0 as? UIWindowScene }
                .flatMap { $0.windows }
                .first { $0.isKeyWindow }
        } else {
            return self.keyWindow
        }
    }
}

private extension MRKSwiftAlertView {

    /// 在Window中显示
    func showInWindow() {
        // 保存当前的keyWindow
        oldKeyWindow = UIApplication.shared.currentKeyWindow

        // 创建背景Window
        showBackgroundWindow()

        // 创建Alert Window
        createAlertWindow()

        // 显示动画
        performShowAnimation()
    }

    /// 在指定View中显示
    func showInView(_ parentView: UIView) {
        // 创建背景视图
        createBackgroundView(in: parentView)

        // 添加到父视图
        parentView.addSubview(self)
        setupEmbeddedConstraints(in: parentView)

        // 调用生命周期方法
        viewDidLoad()

        // 显示动画
        performShowAnimation()
    }

    /// 创建Alert Window
    func createAlertWindow() {
        let windowScene = UIApplication.shared.connectedScenes
            .compactMap { $0 as? UIWindowScene }
            .first

        if let windowScene = windowScene {
            alertWindow = UIWindow(windowScene: windowScene)
        } else {
            alertWindow = UIWindow(frame: UIScreen.main.bounds)
        }

        alertWindow?.windowLevel = .evaluation
        alertWindow?.backgroundColor = .clear

        // 创建控制器
        alertViewController = MRKAlertViewController()
        alertViewController?.alertView = self
        alertWindow?.rootViewController = alertViewController

        alertWindow?.makeKeyAndVisible()
    }

    /// 显示背景Window
    func showBackgroundWindow() {
        guard Self.backgroundWindow == nil else { return }

        let frame = UIScreen.main.bounds
        Self.backgroundWindow = MRKAlertViewBackgroundWindow(
            frame: frame,
            style: backgroundStyle,
            opaqueness: opaqueness
        )

        Self.backgroundWindow?.makeKeyAndVisible()
        Self.backgroundWindow?.alpha = 0

        UIView.animate(withDuration: 0.3) {
            Self.backgroundWindow?.alpha = 1
        }
    }

    /// 创建背景视图（嵌入模式）
    func createBackgroundView(in parentView: UIView) {
        backgroundView = UIView(frame: parentView.bounds)
        backgroundView?.autoresizingMask = [.flexibleWidth, .flexibleHeight]

        // 设置背景样式
        setupBackgroundStyle(for: backgroundView!)

        // 添加点击手势
        if isAutoHidden {
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(backgroundTapped))
            backgroundView?.addGestureRecognizer(tapGesture)
        }

        parentView.addSubview(backgroundView!)
        backgroundView?.alpha = 0
    }

    /// 设置嵌入模式的约束
    func setupEmbeddedConstraints(in parentView: UIView) {
        translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            topAnchor.constraint(equalTo: parentView.topAnchor),
            leadingAnchor.constraint(equalTo: parentView.leadingAnchor),
            trailingAnchor.constraint(equalTo: parentView.trailingAnchor),
            bottomAnchor.constraint(equalTo: parentView.bottomAnchor)
        ])
    }

    /// 设置背景样式
    func setupBackgroundStyle(for view: UIView) {
        switch backgroundStyle {
        case .solid:
            view.backgroundColor = UIColor.black.withAlphaComponent(CGFloat(opaqueness))
        case .clear:
            view.backgroundColor = .clear
        case .gradient, .gradientHeavy:
            // 创建渐变背景
            let gradientLayer = CAGradientLayer()
            gradientLayer.frame = view.bounds

            let alpha: CGFloat = backgroundStyle == .gradient ? 0.3 : 0.5
            gradientLayer.colors = [
                UIColor.black.withAlphaComponent(0).cgColor,
                UIColor.black.withAlphaComponent(alpha).cgColor
            ]
            gradientLayer.type = .radial
            gradientLayer.startPoint = CGPoint(x: 0.5, y: 0.5)
            gradientLayer.endPoint = CGPoint(x: 1.0, y: 1.0)

            view.layer.insertSublayer(gradientLayer, at: 0)
        }
    }

    /// 执行显示动画
    func performShowAnimation() {
        isVisible = true
        delegate?.actionAlertViewWillShow?()

        // 设置初始状态
        setupInitialAnimationState()

        // 执行动画
        UIView.animate(withDuration: 0.3, delay: 0, usingSpringWithDamping: 0.8, initialSpringVelocity: 0, options: .curveEaseInOut) {
            self.setupFinalAnimationState()
            self.backgroundView?.alpha = 1
        } completion: { _ in
            Self.isAnimating = false
            self.delegate?.actionAlertViewDidShow?()
        }
    }

    /// 设置动画初始状态
    func setupInitialAnimationState() {
        switch transitionStyle {
        case .slideFromBottom:
            containerView.transform = CGAffineTransform(translationX: 0, y: bounds.height)
        case .slideFromTop:
            containerView.transform = CGAffineTransform(translationX: 0, y: -bounds.height)
        case .fade:
            containerView.alpha = 0
        case .bounce, .dropDown:
            containerView.transform = CGAffineTransform(scaleX: 0.1, y: 0.1)
            containerView.alpha = 0
        }
    }

    /// 设置动画最终状态
    func setupFinalAnimationState() {
        containerView.transform = .identity
        containerView.alpha = 1
    }

    /// 执行隐藏动画
    func performDismissAnimation(completion: @escaping () -> Void) {
        UIView.animate(withDuration: 0.25, animations: {
            self.setupDismissAnimationState()
            self.backgroundView?.alpha = 0
        }) { _ in
            completion()
        }
    }

    /// 设置隐藏动画状态
    func setupDismissAnimationState() {
        switch transitionStyle {
        case .slideFromBottom:
            containerView.transform = CGAffineTransform(translationX: 0, y: bounds.height)
        case .slideFromTop:
            containerView.transform = CGAffineTransform(translationX: 0, y: -bounds.height)
        case .fade:
            containerView.alpha = 0
        case .bounce, .dropDown:
            containerView.transform = CGAffineTransform(scaleX: 0.1, y: 0.1)
            containerView.alpha = 0
        }
    }

    /// 从Window中隐藏
    func dismissFromWindow(animated: Bool, completion: (() -> Void)?) {
        isVisible = false

        // 隐藏背景Window
        hideBackgroundWindow(animated: animated)

        // 隐藏Alert Window
        alertWindow?.isHidden = true
        alertWindow = nil

        // 恢复之前的keyWindow
        oldKeyWindow?.makeKeyAndVisible()
        oldKeyWindow = nil

        delegate?.actionAlertViewDidDismiss?()
        completion?()
    }

    /// 从View中隐藏
    func dismissFromView(animated: Bool, completion: (() -> Void)?) {
        isVisible = false

        backgroundView?.removeFromSuperview()
        backgroundView = nil

        removeFromSuperview()

        delegate?.actionAlertViewDidDismiss?()
        completion?()
    }

    /// 隐藏背景Window
    func hideBackgroundWindow(animated: Bool) {
        guard let backgroundWindow = Self.backgroundWindow else { return }

        if animated {
            UIView.animate(withDuration: 0.3, animations: {
                backgroundWindow.alpha = 0
            }) { _ in
                backgroundWindow.isHidden = true
                Self.backgroundWindow = nil
            }
        } else {
            backgroundWindow.isHidden = true
            Self.backgroundWindow = nil
        }
    }

    /// 背景点击事件
    @objc func backgroundTapped() {
        delegate?.actionAlertViewDidSelectBackGroundView?()

        if isAutoHidden {
            dismiss(animated: true)
        }
    }
}

// MARK: - 背景Window类

private class MRKAlertViewBackgroundWindow: UIWindow {

    private let backgroundStyle: MRKAlertViewBackgroundStyle
    private let opaqueness: Float

    init(frame: CGRect, style: MRKAlertViewBackgroundStyle, opaqueness: Float) {
        self.backgroundStyle = style
        self.opaqueness = opaqueness

        super.init(frame: frame)

        self.windowLevel = .evaluationBackground
        self.backgroundColor = .clear
        self.isOpaque = false

        // 创建根控制器
        let rootVC = UIViewController()
        rootVC.view.backgroundColor = .clear
        self.rootViewController = rootVC
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    override func draw(_ rect: CGRect) {
        super.draw(rect)

        guard let context = UIGraphicsGetCurrentContext() else { return }

        switch backgroundStyle {
        case .solid:
            let alpha = opaqueness > 0 ? CGFloat(opaqueness) : 0.4
            UIColor.black.withAlphaComponent(alpha).setFill()
            context.fill(rect)

        case .clear:
            UIColor.clear.setFill()
            context.fill(rect)

        case .gradient:
            drawGradientBackground(in: context, rect: rect, alpha: 0.3)

        case .gradientHeavy:
            drawGradientBackground(in: context, rect: rect, alpha: 0.5)
        }
    }

    private func drawGradientBackground(in context: CGContext, rect: CGRect, alpha: CGFloat) {
        let colorSpace = CGColorSpaceCreateDeviceRGB()
        let colors = [
            UIColor.black.withAlphaComponent(0).cgColor,
            UIColor.black.withAlphaComponent(alpha).cgColor
        ]

        guard let gradient = CGGradient(colorsSpace: colorSpace, colors: colors as CFArray, locations: [0.0, 1.0]) else {
            return
        }

        let center = CGPoint(x: rect.width / 2, y: rect.height / 2)
        let radius = min(rect.width, rect.height)

        context.drawRadialGradient(gradient, startCenter: center, startRadius: 0, endCenter: center, endRadius: radius, options: .drawsAfterEndLocation)
    }
}

// MARK: - 控制器类

@objc @objcMembers
public class MRKAlertViewController: UIViewController {
    weak var alertView: MRKSwiftAlertView?

    public override func loadView() {
        guard let alertView = alertView else {
            super.loadView()
            return
        }
        self.view = alertView
    }

    public override func viewDidLoad() {
        super.viewDidLoad()
        alertView?.beforeSuperViewDidLoad()
        alertView?.viewDidLoad()
    }

    public override var shouldAutorotate: Bool {
        // iPad支持旋转，iPhone不支持
        if let alertView = alertView {
            return alertView.shouldAutorotate
        }
        return UIDevice.current.userInterfaceIdiom == .pad
    }

    public override var supportedInterfaceOrientations: UIInterfaceOrientationMask {
        if shouldAutorotate {
            return .all
        } else {
            return .portrait
        }
    }

    public override var preferredInterfaceOrientationForPresentation: UIInterfaceOrientation {
        if shouldAutorotate {
            return view.window?.windowScene?.interfaceOrientation ?? .portrait
        } else {
            return .portrait
        }
    }

    public override func viewWillTransition(to size: CGSize, with coordinator: UIViewControllerTransitionCoordinator) {
        super.viewWillTransition(to: size, with: coordinator)
        coordinator.animate(alongsideTransition: { _ in
            // 更新背景布局
            self.alertView?.reloadBackGroundLayout(size: size)

            // 重新布局容器视图
            self.alertView?.invalidateLayout()
        }, completion: nil)
    }
}

// MARK: - Objective-C 兼容性扩展

@objc public extension MRKSwiftAlertView {

    /// Objective-C 兼容的显示方法
    @objc(showInView:)
    func objc_show(in parentView: UIView) {
        show(in: parentView)
    }

    /// Objective-C 兼容的隐藏方法
    @objc(dismissAnimated:)
    func objc_dismiss(animated: Bool) {
        dismiss(animated: animated)
    }

    /// Objective-C 兼容的隐藏方法（带回调）
    @objc(dismissAnimated:complete:)
    func objc_dismiss(animated: Bool, complete: (() -> Void)?) {
        dismiss(animated: animated, completion: complete)
    }

    /// 设置是否可见（只读属性的setter，用于兼容）
    @objc func setVisible(_ visible: Bool) {
        // 这个方法主要用于兼容，实际的可见性由show/dismiss方法控制
        if visible && !isVisible {
            show()
        } else if !visible && isVisible {
            dismiss(animated: true)
        }
    }
}

// MARK: - 静态队列管理

public extension MRKSwiftAlertView {
    
    /// 获取队列中的弹窗数量
    @objc class func queueCount() -> Int {
        return sharedQueue.count
    }

    /// 强制关闭当前弹窗并清空队列
    @objc class func dismissAll() {
        // 强制关闭当前弹窗
        if let current = currentAlertView {
            current.dismiss(animated: false) // 使用无动画关闭，避免队列处理
            Self.currentAlertView = nil
        }

        // 清空队列
        clearQueue()

        // 重置动画状态
        Self.isAnimating = false

        print("MRKSwiftAlertView: 已强制关闭所有弹窗并清空队列")
    }

    /// 强制关闭当前弹窗但不清空队列（会继续显示下一个）
    @objc class func dismissCurrent() {
        currentAlertView?.dismiss(animated: true)
    }

    /// 添加到队列
    class func addToQueue(_ alertView: MRKSwiftAlertView) {
        // 避免重复添加
        guard !sharedQueue.contains(where: { $0 === alertView }) else {
            return
        }
        sharedQueue.append(alertView)
        print("MRKSwiftAlertView: 弹窗已加入队列，当前队列长度: \(sharedQueue.count)")
    }

    /// 从队列中移除
    class func removeFromQueue(_ alertView: MRKSwiftAlertView) {
        let beforeCount = sharedQueue.count
        sharedQueue.removeAll { $0 === alertView }
        let afterCount = sharedQueue.count

        if beforeCount != afterCount {
            print("MRKSwiftAlertView: 弹窗已从队列移除，当前队列长度: \(afterCount)")
        }
    }

    /// 显示队列中的下一个
    class func showNext() {
        // 如果正在动画或已有弹窗显示，则不处理
        guard !isAnimating, currentAlertView == nil, let nextAlert = sharedQueue.first else {
            return
        }

        print("MRKSwiftAlertView: 开始显示队列中的下一个弹窗")
        removeFromQueue(nextAlert)

        // 直接调用内部显示方法，避免重新加入队列
        Self.currentAlertView = nextAlert
        Self.isAnimating = true

        switch nextAlert.presentationMode {
        case .window:
            nextAlert.showInWindow()
        case .embedded:
            if let parentView = nextAlert.parentView {
                nextAlert.showInView(parentView)
            } else {
                print("MRKSwiftAlertView: 嵌入模式缺少父视图，跳过显示")
                Self.currentAlertView = nil
                Self.isAnimating = false
                showNext() // 尝试显示下一个
            }
        }
    }

    /// 清空队列
    class func clearQueue() {
        let count = sharedQueue.count
        sharedQueue.removeAll()
        if count > 0 {
            print("MRKSwiftAlertView: 已清空队列，移除了 \(count) 个弹窗")
        }
    }

    /// 将弹窗插入到队列头部（优先显示）
    @objc class func insertToQueueHead(_ alertView: MRKSwiftAlertView) {
        // 避免重复添加
        guard !sharedQueue.contains(where: { $0 === alertView }) else {
            return
        }
        sharedQueue.insert(alertView, at: 0)
        print("MRKSwiftAlertView: 弹窗已插入队列头部，当前队列长度: \(sharedQueue.count)")
    }
}


 



