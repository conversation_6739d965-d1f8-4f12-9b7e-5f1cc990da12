//
//  MRKAlertView.swift
//  Student_IOS
//
//  Created by merit on 2025/8/7.
//
//

import UIKit

// MARK: - 枚举定义

extension UIWindow.Level {
    static let evaluation = UIWindow.Level(rawValue: 900)
    static let evaluationBackground = UIWindow.Level(rawValue: 899)
}

/// 背景效果类型
@objc public enum MRKAlertViewBackgroundStyle: Int {
    case solid = 0              // 纯色背景
    case gradient               // 渐变背景
    case gradientHeavy          // 重渐变背景
    case clear                  // 透明背景
}

/// 动画效果类型
@objc public enum MRKAlertViewTransitionStyle: Int {
    case slideFromBottom = 0    // 从底部滑入
    case fade                   // 淡入淡出
    case bounce                 // 弹跳效果
    case dropDown               // 下拉效果
    case slideFromTop           // 从顶部滑入
}

/// 展示模式
@objc public enum MRKAlertViewPresentationMode: Int {
    case window                // 以Window形式展示（默认）
    case embedded              // 嵌入到指定View中展示
}

// MARK: - 代理协议

@objc public protocol MRKAlertViewDelegate: AnyObject {
    @objc optional func alertViewWillShow()                  // 即将出现
    @objc optional func alertViewDidShow()                   // 已经出现
    @objc optional func alertViewWillDismiss()               // 即将消失
    @objc optional func alertViewDidDismiss()                // 已经消失
    @objc optional func alertViewDidSelectBackGroundView()   // 点击背景
}

// MARK: - 主类

@objc @objcMembers
public class MRKSwiftAlertView: UIView {
    
    // MARK: - 公共属性
    
    /// 代理
    @objc public weak var delegate: MRKAlertViewDelegate?
    
    /// 背景效果
    @objc public var backgroundStyle: MRKAlertViewBackgroundStyle = .solid
    
    /// 动画效果
    @objc public var transitionStyle: MRKAlertViewTransitionStyle = .slideFromBottom
    
    /// 是否正在显示
    @objc public private(set) var isVisible: Bool = false
    
    /// 容器视图
    @objc public private(set) var containerView: UIView = UIView()
    
    /// 是否点击背景隐藏
    @objc public var isAutoHidden: Bool = false
    
    /// 背景不透明度
    @objc public var opaqueness: Float = 0.4
    
    /// 展示模式
    @objc public var presentationMode: MRKAlertViewPresentationMode = .window
    
    /// 父视图（当使用embedded模式时）
    @objc public weak var parentView: UIView?
    
    /// 是否支持旋转（iPad默认支持，iPhone默认不支持）
    @objc public var shouldAutorotate: Bool = {
        return UIDevice.current.userInterfaceIdiom == .pad
    }()
    
    // MARK: - 私有属性
    
    private var alertWindow: UIWindow?
    private var backgroundView: UIView?
    private var alertViewController: MRKAlertViewController?
    private var isLayoutDirty: Bool = true
    private var oldKeyWindow: UIWindow?
    
    // MARK: - iPhone 竖屏锁定相关（embedded 模式）
    private var orientationObserverAdded: Bool = false
    private func addOrientationObserverIfNeeded() {
        guard !orientationObserverAdded else { return }
        orientationObserverAdded = true
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(deviceOrientationDidChange(_:)),
            name: UIDevice.orientationDidChangeNotification,
            object: nil
        )
    }
    private func removeOrientationObserverIfNeeded() {
        if orientationObserverAdded {
            NotificationCenter.default.removeObserver(
                self,
                name: UIDevice.orientationDidChangeNotification,
                object: nil
            )
            orientationObserverAdded = false
        }
    }
    @objc private func deviceOrientationDidChange(_ n: Notification) {
        updateFixedOrientationTransform()
    }
    /// 更新 containerView 的逆向旋转，使弹窗在 iPhone 上视觉保持竖屏
    private func updateFixedOrientationTransform() {
        guard !shouldAutorotate else {
            containerView.transform = .identity
            return
        }
        let orientation = UIApplication.shared.currentKeyWindow?.windowScene?.interfaceOrientation ?? .portrait
        let angle: CGFloat
        switch orientation {
        case .portrait: angle = 0
        case .landscapeLeft: angle = .pi / 2
        case .landscapeRight: angle = -.pi / 2
        case .portraitUpsideDown: angle = .pi
        default: angle = 0
        }
        containerView.transform = CGAffineTransform(rotationAngle: -angle)
    }
    
    // MARK: - 静态属性
    
    private static var sharedQueue: [MRKSwiftAlertView] = []
    private static var isAnimating: Bool = false
    private static var currentAlertView: MRKSwiftAlertView?
    private static var backgroundWindow: MRKAlertViewBackgroundWindow?
    
    // MARK: - 初始化方法
    
    /// 便利构造方法
    @objc public class func actionAlertView(withAnimationStyle style: MRKAlertViewTransitionStyle) -> Self {
        return self.init(animationStyle: style)
    }
    
    /// 指定初始化方法
    @objc public required init(animationStyle style: MRKAlertViewTransitionStyle) {
        super.init(frame: .zero)
        self.transitionStyle = style
        setupInitialState()
    }
    
    public override init(frame: CGRect) {
        super.init(frame: frame)
        setupInitialState()
    }
    
    required public init?(coder: NSCoder) {
        super.init(coder: coder)
        setupInitialState()
    }
    
    private func setupInitialState() {
        self.isAutoHidden = false
        self.backgroundStyle = .solid
        self.opaqueness = 0.4
        addSubview(containerView)

        // 设置设备相关的默认值
        self.shouldAutorotate = UIDevice.current.userInterfaceIdiom == .pad
    }

    deinit {
        print("MRKSwiftAlertView ======= deinit")
    }
    
    // MARK: - 公共显示方法

    /// 显示弹窗（默认以 Window 模式）
    @objc public func showWindow() {
        showWithMode(.window, in: nil)
    }

    /// 在指定视图中显示弹窗（嵌入模式）
    @objc public func showEmbedded(in parentView: UIView) {
        showWithMode(.embedded, in: parentView)
    }

    /// 指定模式显示弹窗
    /// - Parameters:
    ///   - mode: 显示模式（Window 或嵌入）
    ///   - parentView: 父视图（嵌入模式时必需）
    @objc public func showWithMode(_ mode: MRKAlertViewPresentationMode, in parentView: UIView?) {
        // 防止重复显示
        if isVisible {
            return
        }
        
        if Self.currentAlertView != nil || Self.isAnimating {
            Self.addToQueue(self)
            return
        }
        
        Self.currentAlertView = self
        Self.isAnimating = true
        
        self.presentationMode = mode
        self.parentView = parentView
        
        switch mode {
        case .window:
            showInWindow()
        case .embedded:
            guard let parentView = parentView else {
                print("MRKSwiftAlertView Error: parentView is required for embedded mode")
                Self.isAnimating = false
                Self.currentAlertView = nil
                return
            }
            showInView(parentView)
        }
    }
    
    // MARK: - 公共隐藏方法

    /// 隐藏弹窗
    /// - Parameter animated: 是否使用动画
    @objc public func dismiss(animated: Bool) {
        dismiss(animated: animated, completion: nil)
    }

    /// 隐藏弹窗（带完成回调）
    /// - Parameters:
    ///   - animated: 是否使用动画
    ///   - completion: 隐藏完成后的回调
    @objc public func dismiss(animated: Bool, completion: (() -> Void)?) {
        if !isVisible {
            completion?()
            return
        }
        
        delegate?.alertViewWillDismiss?()
        
        let dismissBlock = { [weak self] in
            guard let self = self else { return }

            switch self.presentationMode {
            case .window:
                self.dismissFromWindow(animated: animated) { [weak self] in
                    completion?()
                    self?.handleDismissComplete()
                }
            case .embedded:
                self.dismissFromView(animated: animated) { [weak self] in
                    completion?()
                    self?.handleDismissComplete()
                }
            }
        }
        
        if animated {
            performDismissAnimation(completion: dismissBlock)
        } else {
            dismissBlock()
        }
    }

    /// 处理隐藏完成后的逻辑
    private func handleDismissComplete() {
        if Self.currentAlertView === self {
            Self.currentAlertView = nil
        }
        
        Self.isAnimating = false

        // 显示队列中的下一个弹窗
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            Self.showNext()
        }
    }
    
    // MARK: - 子类需要重写的方法
    
    /// 布局容器视图的位置
    @objc open func layoutContainerView() {
        // 子类重写实现具体布局
        containerView.frame = CGRectZero
    }
    
    /// 设置容器视图的属性
    
    /// 初始化容器视图及其子视图（供子类重写）
    @objc open func setupContainerView() {
        setupContainerViewAttributes()
        setupContainerSubViews()
        
        layoutContainerView()
        layoutContainerViewSubViews()
    }

    @objc open func setupContainerViewAttributes() {
        // 子类重写实现具体属性设置
        containerView.backgroundColor = .white
    }
    
    /// 给容器视图添加子视图
    @objc open func setupContainerSubViews() {
        // 子类重写实现具体子视图添加
    }
    
    /// 设置子视图的约束
    @objc open func layoutContainerViewSubViews() {
        // 子类重写实现具体子视图布局
    }
    
    // MARK: - 生命周期方法（供控制器调用）
    
    @objc public func beforeSuperViewDidLoad() {
        // 在控制器viewDidLoad之前调用
    }
    
    @objc public func viewDidLoad() {
        setupContainerView()
        invalidateLayout()
    }
    
    @objc public func resetTransition() {
        // 重置动画状态
    }
    
    @objc public func invalidateLayout() {
        isLayoutDirty = true
        setNeedsLayout()
    }
    
    @objc public func reloadBackGroundLayout(size: CGSize) {
        // 重新布局背景
        if presentationMode == .window {
            MRKSwiftAlertView.backgroundWindow?.frame = CGRect(origin: .zero, size: size)
        } else {
            backgroundView?.frame = CGRect(origin: .zero, size: size)
        }
    }
}

// MARK: - 私有实现

private extension UIApplication {
    var currentKeyWindow: UIWindow? {
        if #available(iOS 13.0, *) {
            return self
                .connectedScenes
                .compactMap { $0 as? UIWindowScene }
                .flatMap { $0.windows }
                .first { $0.isKeyWindow }
        } else {
            return self.keyWindow
        }
    }
}

private extension MRKSwiftAlertView {
    
    /// 在Window中显示
    func showInWindow() {
        oldKeyWindow = UIApplication.shared.currentKeyWindow
        
//        // iPhone 且锁竖屏 → fallback 为 embedded 避免系统旋转
//        if UIDevice.current.userInterfaceIdiom == .phone && !self.shouldAutorotate {
//            if let keyWin = UIApplication.shared.currentKeyWindow {
//                self.presentationMode = .embedded
//                self.parentView = keyWin
//                showInView(keyWin)
//                return
//            }
//        }
        
        showBackgroundWindow()
        createAlertWindow()
        performShowAnimation()
    }
    
    /// 在指定View中显示
    func showInView(_ parentView: UIView) {
        createBackgroundView(in: parentView)
        parentView.addSubview(self)
        setupEmbeddedConstraints(in: parentView)
        viewDidLoad()
        
        // iPhone 且锁竖屏 → 注册方向观察 & 更新 transform
        if UIDevice.current.userInterfaceIdiom == .phone && !self.shouldAutorotate {
            addOrientationObserverIfNeeded()
            updateFixedOrientationTransform()
        }
        
        performShowAnimation()
    }
    
    /// 创建Alert Window
    func createAlertWindow() {
        let windowScene = UIApplication.shared.connectedScenes
            .compactMap { $0 as? UIWindowScene }
            .first
        
        if let windowScene = windowScene {
            alertWindow = UIWindow(windowScene: windowScene)
        } else {
            alertWindow = UIWindow(frame: UIScreen.main.bounds)
        }
        
        alertWindow?.windowLevel = .evaluation
        alertWindow?.backgroundColor = .clear
        
        // 创建控制器
        alertViewController = MRKAlertViewController()
        alertViewController?.alertView = self
        alertWindow?.rootViewController = alertViewController
        
        alertWindow?.makeKeyAndVisible()
    }
    
    /// 显示背景Window（用于 Window 模式的全局遮罩）
    func showBackgroundWindow() {
        guard Self.backgroundWindow == nil else { return }
        
        let frame = UIScreen.main.bounds
        let backgroundWindow = MRKAlertViewBackgroundWindow(
            frame: frame,
            style: backgroundStyle,
            opaqueness: opaqueness
        )
        
        // 设置当前 AlertView 引用，用于处理背景点击
        backgroundWindow.currentAlertView = self
        
        backgroundWindow.makeKeyAndVisible()
        Self.backgroundWindow = backgroundWindow
        
        // 背景窗口淡入动画，提供平滑的视觉过渡
        backgroundWindow.alpha = 0
        UIView.animate(withDuration: 0.25, delay: 0, options: .curveEaseOut) {
            backgroundWindow.alpha = 1.0
        }
    }
    
    /// 创建背景视图（嵌入模式）
    func createBackgroundView(in parentView: UIView) {
        let bgView = UIView(frame: parentView.bounds)
        bgView.autoresizingMask = [.flexibleWidth, .flexibleHeight]
        
        // 设置背景样式
        setupBackgroundStyle(for: bgView)
        
        // 添加背景视图到父视图
        parentView.addSubview(bgView)
        backgroundView = bgView
        
        // 背景淡入动画
        bgView.alpha = 0
        UIView.animate(withDuration: 0.25, delay: 0, options: .curveEaseOut) {
            bgView.alpha = 1.0
        }
    }
    
    /// 设置嵌入模式的约束
    func setupEmbeddedConstraints(in parentView: UIView) {
        translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            topAnchor.constraint(equalTo: parentView.topAnchor),
            leadingAnchor.constraint(equalTo: parentView.leadingAnchor),
            trailingAnchor.constraint(equalTo: parentView.trailingAnchor),
            bottomAnchor.constraint(equalTo: parentView.bottomAnchor)
        ])
    }
    
    /// 设置背景样式
    func setupBackgroundStyle(for view: UIView) {
        switch backgroundStyle {
        case .solid:
            view.backgroundColor = UIColor.black.withAlphaComponent(CGFloat(opaqueness))
        case .clear:
            view.backgroundColor = .clear
        case .gradient, .gradientHeavy:
            // 创建渐变背景
            let gradientLayer = CAGradientLayer()
            gradientLayer.frame = view.bounds
            
            let alpha: CGFloat = backgroundStyle == .gradient ? 0.3 : 0.5
            gradientLayer.colors = [
                UIColor.black.withAlphaComponent(0).cgColor,
                UIColor.black.withAlphaComponent(alpha).cgColor
            ]
            gradientLayer.type = .radial
            gradientLayer.startPoint = CGPoint(x: 0.5, y: 0.5)
            gradientLayer.endPoint = CGPoint(x: 1.0, y: 1.0)
            
            view.layer.insertSublayer(gradientLayer, at: 0)
        }
    }
    
    /// 执行显示动画
    func performShowAnimation() {
        isVisible = true
        delegate?.alertViewWillShow?()
        
        // 设置初始状态
        setupInitialAnimationState()
        
        // 执行动画
        UIView.animate(withDuration: 0.3,
                       delay: 0,
                       usingSpringWithDamping: 0.8,
                       initialSpringVelocity: 0,
                       options: .curveEaseInOut) {
            self.setupFinalAnimationState()
            self.backgroundView?.alpha = 1
        } completion: { _ in
            Self.isAnimating = false
            self.delegate?.alertViewDidShow?()
        }
    }
    
    /// 设置动画初始状态
    func setupInitialAnimationState() {
        switch transitionStyle {
        case .slideFromBottom:
            containerView.transform = CGAffineTransform(translationX: 0, y: bounds.height)
        case .slideFromTop:
            containerView.transform = CGAffineTransform(translationX: 0, y: -bounds.height)
        case .fade:
            containerView.alpha = 0
        case .bounce, .dropDown:
            containerView.transform = CGAffineTransform(scaleX: 0.1, y: 0.1)
            containerView.alpha = 0
        }
    }
    
    /// 设置动画最终状态
    func setupFinalAnimationState() {
        containerView.transform = .identity
        containerView.alpha = 1
    }
    
    /// 执行隐藏动画
    func performDismissAnimation(completion: @escaping () -> Void) {
        UIView.animate(withDuration: 0.25, animations: {
            self.setupDismissAnimationState()
            self.backgroundView?.alpha = 0
        }) { _ in
            completion()
        }
    }
    
    /// 设置隐藏动画状态
    func setupDismissAnimationState() {
        switch transitionStyle {
        case .slideFromBottom:
            containerView.transform = CGAffineTransform(translationX: 0, y: bounds.height)
        case .slideFromTop:
            containerView.transform = CGAffineTransform(translationX: 0, y: -bounds.height)
        case .fade:
            containerView.alpha = 0
        case .bounce, .dropDown:
            containerView.transform = CGAffineTransform(scaleX: 0.1, y: 0.1)
            containerView.alpha = 0
        }
    }
    
    /// 从Window中隐藏
    func dismissFromWindow(animated: Bool, completion: (() -> Void)?) {
        isVisible = false
        
        // 隐藏背景Window
        hideBackgroundWindow(animated: animated)
        
        // 隐藏Alert Window
        alertWindow?.isHidden = true
        alertWindow = nil
        
        // 恢复之前的keyWindow
        oldKeyWindow?.makeKeyAndVisible()
        oldKeyWindow = nil
        
        delegate?.alertViewDidDismiss?()
        completion?()
    }
    
    /// 从View中隐藏
    func dismissFromView(animated: Bool, completion: (() -> Void)?) {
        isVisible = false
        
        if animated && backgroundView != nil {
            // 带动画的隐藏背景
            UIView.animate(withDuration: 0.25, delay: 0, options: .curveEaseIn, animations: {
                self.backgroundView?.alpha = 0
            }) { _ in
                self.backgroundView?.removeFromSuperview()
                self.backgroundView = nil
                self.removeFromSuperview()
                self.removeOrientationObserverIfNeeded()
                self.delegate?.alertViewDidDismiss?()
                completion?()
            }
        } else {
            // 立即隐藏
            backgroundView?.removeFromSuperview()
            backgroundView = nil
            removeFromSuperview()
            removeOrientationObserverIfNeeded()
            delegate?.alertViewDidDismiss?()
            completion?()
        }
    }
    
    /// 隐藏背景Window
    /// - Parameter animated: 是否使用淡出动画
    func hideBackgroundWindow(animated: Bool) {
        guard let backgroundWindow = Self.backgroundWindow else { return }
        
        // 清除当前 AlertView 引用，避免内存泄漏
        backgroundWindow.currentAlertView = nil
        
        if animated {
            // 带动画的隐藏：淡出效果提供平滑过渡
            UIView.animate(withDuration: 0.25, delay: 0, options: .curveEaseIn, animations: {
                backgroundWindow.alpha = 0
            }) { _ in
                backgroundWindow.isHidden = true
                Self.backgroundWindow = nil
            }
        } else {
            // 立即隐藏：用于强制关闭等场景
            backgroundWindow.isHidden = true
            Self.backgroundWindow = nil
        }
    }
}

// MARK: - 背景触摸处理

extension MRKSwiftAlertView {
    
    /// 重写触摸事件处理（参考原始 OC 版本的精确实现）
    /// 这个方法会在 Window 模式和嵌入模式下都生效，提供精确的触摸判断
    public override func touchesEnded(_ touches: Set<UITouch>, with event: UIEvent?) {
        super.touchesEnded(touches, with: event)

        guard let touch = touches.first else { return }

        // 获取触摸点在当前视图中的位置
        let touchLocation = touch.location(in: self)

        // 检查触摸点是否在容器视图内部
        let containerFrame = containerView.frame
        let isInsideContainer = containerFrame.contains(touchLocation)

        // 只有点击在容器外部（背景区域）才触发背景点击逻辑
        if !isInsideContainer{
            handleBackgroundTouch()
        }
    }

    /// 统一的背景触摸处理逻辑
    /// 处理背景点击事件，包括代理方法触发和自动隐藏逻辑
    func handleBackgroundTouch() {
        // 先触发代理方法，通知外部背景被点击
        delegate?.alertViewDidSelectBackGroundView?()

        // 如果设置了自动隐藏，则隐藏弹窗
        if isAutoHidden {
            dismiss(animated: true)
        }
    }
}



// MARK: - 背景Window类

/// 背景窗口类，用于 Window 模式下的全局遮罩效果
/// 支持多种背景样式和触摸事件处理
private class MRKAlertViewBackgroundWindow: UIWindow {
    private let backgroundStyle: MRKAlertViewBackgroundStyle
    private let opaqueness: Float

    /// 弱引用当前的 AlertView，用于处理背景点击事件
    weak var currentAlertView: MRKSwiftAlertView?

    init(frame: CGRect, style: MRKAlertViewBackgroundStyle, opaqueness: Float) {
        self.backgroundStyle = style
        self.opaqueness = opaqueness

        super.init(frame: frame)

        self.windowLevel = .evaluationBackground
        self.backgroundColor = .clear
        self.isOpaque = false
    }

    /// 处理背景点击事件
    func handleBackgroundTouch() {
        currentAlertView?.handleBackgroundTouch()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    override func draw(_ rect: CGRect) {
        super.draw(rect)

        guard let context = UIGraphicsGetCurrentContext() else { return }

        switch backgroundStyle {
        case .solid:
            let alpha = opaqueness > 0 ? CGFloat(opaqueness) : 0.4
            UIColor.black.withAlphaComponent(alpha).setFill()
            context.fill(rect)

        case .clear:
            UIColor.clear.setFill()
            context.fill(rect)

        case .gradient:
            drawGradientBackground(in: context, rect: rect, alpha: 0.3)

        case .gradientHeavy:
            drawGradientBackground(in: context, rect: rect, alpha: 0.5)
        }
    }

    private func drawGradientBackground(in context: CGContext, rect: CGRect, alpha: CGFloat) {
        let colorSpace = CGColorSpaceCreateDeviceRGB()
        let colors = [
            UIColor.black.withAlphaComponent(0).cgColor,
            UIColor.black.withAlphaComponent(alpha).cgColor
        ]

        guard let gradient = CGGradient(colorsSpace: colorSpace, colors: colors as CFArray, locations: [0.0, 1.0]) else {
            return
        }

        let center = CGPoint(x: rect.width / 2, y: rect.height / 2)
        let radius = min(rect.width, rect.height)

        context.drawRadialGradient(gradient, startCenter: center, startRadius: 0, endCenter: center, endRadius: radius, options: .drawsAfterEndLocation)
    }
}

// MARK: - 控制器类

@objc @objcMembers
public class MRKAlertViewController: UIViewController {
    weak var alertView: MRKSwiftAlertView?

    public override func loadView() {
        guard let alertView = alertView else {
            super.loadView()
            return
        }
        self.view = alertView
    }

    public override func viewDidLoad() {
        super.viewDidLoad()
        alertView?.beforeSuperViewDidLoad()
        alertView?.viewDidLoad()
    }

    public override var shouldAutorotate: Bool {
        // iPad支持旋转，iPhone不支持
        if let alertView = alertView {
            return alertView.shouldAutorotate
        }
        return UIDevice.current.userInterfaceIdiom == .pad
    }

    public override var supportedInterfaceOrientations: UIInterfaceOrientationMask {
        if shouldAutorotate {
            return .all
        } else {
            return .portrait
        }
    }

    public override var preferredInterfaceOrientationForPresentation: UIInterfaceOrientation {
        if shouldAutorotate {
            return view.window?.windowScene?.interfaceOrientation ?? .portrait
        } else {
            return .portrait
        }
    }

    public override func viewWillTransition(to size: CGSize, with coordinator: UIViewControllerTransitionCoordinator) {
        super.viewWillTransition(to: size, with: coordinator)
        coordinator.animate(alongsideTransition: { _ in
            // 更新背景布局
            self.alertView?.reloadBackGroundLayout(size: size)

            // 重新布局容器视图
            self.alertView?.invalidateLayout()
        }, completion: nil)
    }
}

// MARK: - 静态队列管理

public extension MRKSwiftAlertView {
    
    /// 获取队列中的弹窗数量
    @objc class func queueCount() -> Int {
        return sharedQueue.count
    }

    /// 强制关闭当前弹窗并清空队列
    @objc class func dismissAll() {
        // 强制关闭当前弹窗
        if let current = currentAlertView {
            current.dismiss(animated: false) // 使用无动画关闭，避免队列处理
            Self.currentAlertView = nil
        }

        // 清空队列
        clearQueue()

        // 重置动画状态
        Self.isAnimating = false

        debugPrint("MRKSwiftAlertView: 已强制关闭所有弹窗并清空队列")
    }

    /// 强制关闭当前弹窗但不清空队列（会继续显示下一个）
    @objc class func dismissCurrent() {
        currentAlertView?.dismiss(animated: true)
    }

    /// 添加到队列
    class func addToQueue(_ alertView: MRKSwiftAlertView) {
        // 避免重复添加
        guard !sharedQueue.contains(where: { $0 === alertView }) else {
            return
        }
        sharedQueue.append(alertView)
        debugPrint("MRKSwiftAlertView: 弹窗已加入队列，当前队列长度: \(sharedQueue.count)")
    }

    /// 从队列中移除
    class func removeFromQueue(_ alertView: MRKSwiftAlertView) {
        let beforeCount = sharedQueue.count
        sharedQueue.removeAll { $0 === alertView }
        let afterCount = sharedQueue.count

        if beforeCount != afterCount {
            debugPrint("MRKSwiftAlertView: 弹窗已从队列移除，当前队列长度: \(afterCount)")
        }
    }

    /// 显示队列中的下一个
    class func showNext() {
        // 如果正在动画或已有弹窗显示，则不处理
        guard !isAnimating, currentAlertView == nil, let nextAlert = sharedQueue.first else {
            return
        }

        debugPrint("MRKSwiftAlertView: 开始显示队列中的下一个弹窗")
        removeFromQueue(nextAlert)

        // 直接调用内部显示方法，避免重新加入队列
        Self.currentAlertView = nextAlert
        Self.isAnimating = true

        switch nextAlert.presentationMode {
        case .window:
            nextAlert.showInWindow()
        case .embedded:
            if let parentView = nextAlert.parentView {
                nextAlert.showInView(parentView)
            } else {
                debugPrint("MRKSwiftAlertView: 嵌入模式缺少父视图，跳过显示")
                Self.currentAlertView = nil
                Self.isAnimating = false
                showNext() // 尝试显示下一个
            }
        }
    }

    /// 清空队列
    class func clearQueue() {
        let count = sharedQueue.count
        sharedQueue.removeAll()
        if count > 0 {
            debugPrint("MRKSwiftAlertView: 已清空队列，移除了 \(count) 个弹窗")
        }
    }

    /// 将弹窗插入到队列头部（优先显示）
    @objc class func insertToQueueHead(_ alertView: MRKSwiftAlertView) {
        // 避免重复添加
        guard !sharedQueue.contains(where: { $0 === alertView }) else {
            return
        }
        sharedQueue.insert(alertView, at: 0)
        debugPrint("MRKSwiftAlertView: 弹窗已插入队列头部，当前队列长度: \(sharedQueue.count)")
    }
}
