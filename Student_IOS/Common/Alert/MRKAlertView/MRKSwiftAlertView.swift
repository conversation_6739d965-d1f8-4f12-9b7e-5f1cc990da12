//
//  MRKAlertView.swift
//  Student_IOS
//
//  Created by merit on 2025/8/7.
//  Swift 重构版本，支持 Objective-C 使用
//

import UIKit

// MARK: - 枚举定义

extension UIWindow.Level {
    static let evaluation = UIWindow.Level(rawValue: 900)
    static let evaluationBackground = UIWindow.Level(rawValue: 899)
}

/// 背景效果类型
@objc public enum MRKAlertViewBackgroundStyle: Int {
    case solid = 0
    case gradient
    case gradientHeavy
    case clear
}

/// 动画效果类型
@objc public enum MRKAlertViewTransitionStyle: Int {
    case slideFromBottom = 0
    case fade
    case bounce
    case dropDown
    case slideFromTop
}

/// 展示模式
@objc public enum MRKAlertViewPresentationMode: Int {
    case window
    case embedded
}

// MARK: - Delegate

@objc public protocol MRKActionAlertViewDelegate: AnyObject {
    @objc optional func actionAlertViewWillShow()
    @objc optional func actionAlertViewDidShow()
    @objc optional func actionAlertViewWillDismiss()
    @objc optional func actionAlertViewDidDismiss()
    @objc optional func actionAlertViewDidSelectBackGroundView()
}

// MARK: - MRKSwiftAlertView (基础父类)

@objc @objcMembers
public class MRKSwiftAlertView: UIView {

    // MARK: - 公共属性

    /// Delegate
    @objc public weak var delegate: MRKActionAlertViewDelegate?

    /// 背景样式
    @objc public var backgroundStyle: MRKAlertViewBackgroundStyle = .solid

    /// 动画样式
    @objc public var transitionStyle: MRKAlertViewTransitionStyle = .slideFromBottom

    /// 是否正在显示
    @objc public private(set) var isVisible: Bool = false

    /// 容器视图（子类应把具体 UI 加到 containerView）
    @objc public private(set) var containerView: UIView = UIView()

    /// 点击背景是否自动隐藏
    @objc public var isAutoHidden: Bool = false

    /// 背景不透明度（仅对 solid 有效）
    @objc public var opaqueness: Float = 0.4

    /// 展示模式：window / embedded
    @objc public var presentationMode: MRKAlertViewPresentationMode = .window

    /// 嵌入模式的父视图（embedded 模式需传）
    @objc public weak var parentView: UIView?

    /// 是否支持自动旋转（iPad 默认支持，iPhone 默认不支持）
    @objc public var shouldAutorotate: Bool = {
        return UIDevice.current.userInterfaceIdiom == .pad
    }()

    // MARK: - 私有属性

    private var alertWindow: UIWindow?
    private var backgroundView: UIView?
    private var alertViewController: MRKAlertViewController?
    private var isLayoutDirty: Bool = true
    private var oldKeyWindow: UIWindow?

    // MARK: - iPhone 竖屏锁定相关（embedded 模式）
    // 这些方法为最小侵入性增加：仅用于 embedded 模式下，视觉上让弹窗保持竖屏（不影响整个 Scene）
    private var orientationObserverAdded: Bool = false
    private func addOrientationObserverIfNeeded() {
        guard !orientationObserverAdded else { return }
        orientationObserverAdded = true
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(deviceOrientationDidChange(_:)),
            name: UIDevice.orientationDidChangeNotification,
            object: nil
        )
    }
    private func removeOrientationObserverIfNeeded() {
        if orientationObserverAdded {
            NotificationCenter.default.removeObserver(
                self,
                name: UIDevice.orientationDidChangeNotification,
                object: nil
            )
            orientationObserverAdded = false
        }
    }
    @objc private func deviceOrientationDidChange(_ n: Notification) {
        updateFixedOrientationTransform()
    }
    /// 更新 containerView 的逆向旋转，使弹窗在 iPhone 上视觉保持竖屏
    private func updateFixedOrientationTransform() {
        guard !shouldAutorotate else {
            containerView.transform = .identity
            return
        }
        // 优先使用 keyWindow 的 windowScene 的 interfaceOrientation
        let orientation = UIApplication.shared.currentKeyWindow?.windowScene?.interfaceOrientation ?? .portrait
        let angle: CGFloat
        switch orientation {
        case .portrait: angle = 0
        case .landscapeLeft: angle = .pi / 2
        case .landscapeRight: angle = -.pi / 2
        case .portraitUpsideDown: angle = .pi
        default: angle = 0
        }
        containerView.transform = CGAffineTransform(rotationAngle: -angle)
    }

    // MARK: - 静态队列与背景 Window

    private static var sharedQueue: [MRKSwiftAlertView] = []
    private static var isAnimating: Bool = false
    private static var currentAlertView: MRKSwiftAlertView?
    private static var backgroundWindow: MRKAlertViewBackgroundWindow?

    // MARK: - 初始化

    @objc public class func actionAlertView(withAnimationStyle style: MRKAlertViewTransitionStyle) -> Self {
        return self.init(animationStyle: style)
    }

    @objc public required init(animationStyle style: MRKAlertViewTransitionStyle) {
        super.init(frame: .zero)
        self.transitionStyle = style
        setupInitialState()
    }

    public override init(frame: CGRect) {
        super.init(frame: frame)
        setupInitialState()
    }

    required public init?(coder: NSCoder) {
        super.init(coder: coder)
        setupInitialState()
    }

    private func setupInitialState() {
        self.isAutoHidden = false
        self.backgroundStyle = .solid
        self.opaqueness = 0.4
        addSubview(containerView)
        self.shouldAutorotate = UIDevice.current.userInterfaceIdiom == .pad
    }

    // MARK: - Subclass hooks (重要：这些为父类提供给子类重写的点)

    /// 子类重写实现具体布局（containerView 的约束）
    @objc open func layoutContainerView() {
        // 子类可重写。默认居中并限制最大宽高
        containerView.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            containerView.centerXAnchor.constraint(equalTo: centerXAnchor),
            containerView.centerYAnchor.constraint(equalTo: centerYAnchor),
            containerView.widthAnchor.constraint(lessThanOrEqualTo: widthAnchor, constant: -32),
            containerView.heightAnchor.constraint(lessThanOrEqualTo: heightAnchor, constant: -64)
        ])
    }

    /// 初始化容器视图（由子类重写：设置属性/子视图/布局等）
    @objc open func setupContainerView() {
      
        setupContainerViewAttributes()
        layoutContainerView()
   
        setupContainerSubViews()
        layoutContainerViewSubViews()
    }

    /// 子类覆盖以设置 containerView 的样式（背景色、圆角等）
    @objc open func setupContainerViewAttributes() {
        containerView.backgroundColor = .white
        containerView.layer.cornerRadius = 12
        containerView.layer.masksToBounds = true
    }

    /// 子类覆盖用于向 containerView 添加子视图
    @objc open func setupContainerSubViews() {
        // 子类实现
    }

    /// 子类覆盖用于布局 containerView 的子视图
    @objc open func layoutContainerViewSubViews() {
        // 子类实现
    }

    // MARK: - 生命周期方法（供控制器调用）

    /// 在 view controller 的 viewDidLoad 时由外部调用（父类会执行 setupContainerView）
    @objc public func viewDidLoad() {
        setupContainerView()
        invalidateLayout()
    }

    @objc public func resetTransition() {
        // 子类可重写以在显示前重置状态
    }

    @objc public func invalidateLayout() {
        isLayoutDirty = true
        setNeedsLayout()
    }

    // MARK: - 显示 / 隐藏 (外部接口)

    @objc public func show() {
        showWithMode(.window, in: nil)
    }

    @objc public func show(in parentView: UIView) {
        showWithMode(.embedded, in: parentView)
    }

    @objc public func showWithMode(_ mode: MRKAlertViewPresentationMode, in parentView: UIView?) {
        guard !isVisible else { return }

        // 如果已有弹窗或动画中，加入队列
        if Self.currentAlertView != nil || Self.isAnimating {
            Self.addToQueue(self)
            return
        }

        Self.currentAlertView = self
        Self.isAnimating = true

        self.presentationMode = mode
        self.parentView = parentView

        switch mode {
        case .window:
            showInWindow()
        case .embedded:
            guard let parentView = parentView else {
                print("MRKAlertView: parentView is required for embedded mode")
                Self.isAnimating = false
                Self.currentAlertView = nil
                return
            }
            showInView(parentView)
        }
    }

    @objc public func dismiss(animated: Bool) {
        dismiss(animated: animated, completion: nil)
    }

    @objc public func dismiss(animated: Bool, completion: (() -> Void)?) {
        guard isVisible else {
            completion?()
            return
        }

        delegate?.actionAlertViewWillDismiss?()

        let dismissBlock = { [weak self] in
            guard let self = self else { return }
            switch self.presentationMode {
            case .window:
                self.dismissFromWindow(animated: animated) { [weak self] in
                    completion?()
                    self?.handleDismissComplete()
                }
            case .embedded:
                self.dismissFromView(animated: animated) { [weak self] in
                    completion?()
                    self?.handleDismissComplete()
                }
            }
        }

        animated ? performDismissAnimation(completion: dismissBlock) : dismissBlock()
    }

    private func handleDismissComplete() {
        if Self.currentAlertView === self { Self.currentAlertView = nil }
        Self.isAnimating = false
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) { Self.showNext() }
    }

    // MARK: - 内部展示流程（最小侵入改动）

    /// 在 Window 中显示（注意：对 iPhone 且 shouldAutorotate == false 时，fallback 为 embedded 加入 keyWindow）
    func showInWindow() {
        // 保存当前 key window，供恢复使用
        oldKeyWindow = UIApplication.shared.currentKeyWindow

        // 若为 iPhone 且不允许自动旋转，为避免强制旋转整个 scene（影响 app UI），采用 embedded fallback 到 keyWindow
        if UIDevice.current.userInterfaceIdiom == .phone && !self.shouldAutorotate {
            if let keyWin = UIApplication.shared.currentKeyWindow {
                self.presentationMode = .embedded
                self.parentView = keyWin
                showInView(keyWin)
                return
            }
        }

        // 显示背景 window（全局遮罩）
        showBackgroundWindow()

        // 创建 alert window（优先使用 oldKeyWindow 的 windowScene）
        createAlertWindow()

        // 执行显示动画
        performShowAnimation()
    }

    /// 嵌入到指定父视图显示（embedded）
    func showInView(_ parentView: UIView) {
        // 创建背景视图（覆盖父视图）
        createBackgroundView(in: parentView)

        // 把自己加入父视图
        parentView.addSubview(self)
        setupEmbeddedConstraints(in: parentView)

        // 子类生命周期钩子
        viewDidLoad()

        // 如果在 iPhone 且需要固定竖屏，注册方向观察并更新 transform
        if UIDevice.current.userInterfaceIdiom == .phone && !self.shouldAutorotate {
            addOrientationObserverIfNeeded()
            updateFixedOrientationTransform()
        }

        // 执行动画
        performShowAnimation()
    }

    /// 创建 alert window（优先使用 oldKeyWindow 的 windowScene，兼容多 scene）
    func createAlertWindow() {
        let windowScene = oldKeyWindow?.windowScene ??
            UIApplication.shared.connectedScenes.compactMap { $0 as? UIWindowScene }.first

        if let windowScene = windowScene {
            alertWindow = UIWindow(windowScene: windowScene)
        } else {
            alertWindow = UIWindow(frame: UIScreen.main.bounds)
        }

        alertWindow?.windowLevel = .evaluation
        alertWindow?.backgroundColor = .clear

        alertViewController = MRKAlertViewController()
        alertViewController?.alertView = self
        alertWindow?.rootViewController = alertViewController

        alertWindow?.makeKeyAndVisible()
    }

    // MARK: - 动画与布局

    func performShowAnimation() {
        isVisible = true
        delegate?.actionAlertViewWillShow?()

        // 确保布局已完成，避免 bounds/约束尚未准备好时做动画
        self.setNeedsLayout()
        self.layoutIfNeeded()
        containerView.setNeedsLayout()
        containerView.layoutIfNeeded()

        // 初始状态
        switch transitionStyle {
        case .fade:
            containerView.alpha = 0
        case .slideFromBottom:
            containerView.transform = CGAffineTransform(translationX: 0, y: bounds.height)
        case .slideFromTop:
            containerView.transform = CGAffineTransform(translationX: 0, y: -bounds.height)
        case .bounce:
            containerView.transform = CGAffineTransform(scaleX: 0.7, y: 0.7)
            containerView.alpha = 0
        case .dropDown:
            containerView.transform = CGAffineTransform(translationX: 0, y: -bounds.height)
        }

        UIView.animate(withDuration: 0.3, delay: 0, options: .curveEaseOut) {
            self.containerView.alpha = 1
            self.containerView.transform = .identity
        } completion: { _ in
            self.delegate?.actionAlertViewDidShow?()
            Self.isAnimating = false
        }
    }

    func performDismissAnimation(completion: @escaping () -> Void) {
        UIView.animate(withDuration: 0.25, animations: {
            switch self.transitionStyle {
            case .fade:
                self.containerView.alpha = 0
            case .slideFromBottom:
                self.containerView.transform = CGAffineTransform(translationX: 0, y: self.bounds.height)
            case .slideFromTop:
                self.containerView.transform = CGAffineTransform(translationX: 0, y: -self.bounds.height)
            case .bounce:
                self.containerView.transform = CGAffineTransform(scaleX: 0.7, y: 0.7)
                self.containerView.alpha = 0
            case .dropDown:
                self.containerView.transform = CGAffineTransform(translationX: 0, y: -self.bounds.height)
            }
        }, completion: { _ in
            completion()
        })
    }

    // MARK: - Dismiss 处理

    func dismissFromWindow(animated: Bool, completion: (() -> Void)?) {
        isVisible = false
        alertWindow?.isHidden = true
        alertWindow = nil
        Self.hideBackgroundWindow()
        // 恢复老 key window（如果存在）
        oldKeyWindow?.makeKeyAndVisible()
        completion?()
    }

    func dismissFromView(animated: Bool, completion: (() -> Void)?) {
        isVisible = false
        backgroundView?.removeFromSuperview()
        backgroundView = nil
        
        removeFromSuperview()
        removeOrientationObserverIfNeeded()
        delegate?.actionAlertViewDidDismiss?()
        completion?()
    }

    // MARK: - 背景视图（embedded 模式）

    func createBackgroundView(in parentView: UIView) {
        let bg = UIView(frame: parentView.bounds)
        bg.autoresizingMask = [.flexibleWidth, .flexibleHeight]

        switch backgroundStyle {
        case .solid:
            bg.backgroundColor = UIColor.black.withAlphaComponent(CGFloat(opaqueness))
        case .clear:
            bg.backgroundColor = .clear
        case .gradient, .gradientHeavy:
            // embedded 下若需更复杂渐变可在子类/使用方自定义
            bg.backgroundColor = UIColor.black.withAlphaComponent(CGFloat(opaqueness))
        }

        if isAutoHidden {
            let tap = UITapGestureRecognizer(target: self, action: #selector(backgroundTapped))
            bg.addGestureRecognizer(tap)
        }

        parentView.addSubview(bg)
        backgroundView = bg
    }

    @objc private func backgroundTapped() {
        delegate?.actionAlertViewDidSelectBackGroundView?()
        if isAutoHidden {
            dismiss(animated: true)
        }
    }

    // MARK: - 背景 Window（全局遮罩）

    class func showBackgroundWindow() {
        guard backgroundWindow == nil else { return }
        let frame = UIScreen.main.bounds
        let win = MRKAlertViewBackgroundWindow(frame: frame)
        win.windowLevel = .evaluationBackground
        // 将 backgroundStyle / opaqueness 暴露给 background window，如需可在这里传递
        win.backgroundStyle = .solid
        win.opaqueness = 0.4
        win.isHidden = false
        backgroundWindow = win
    }

    class func hideBackgroundWindow() {
        backgroundWindow?.isHidden = true
        backgroundWindow = nil
    }

    func showBackgroundWindow() {
        Self.showBackgroundWindow()
    }

    // MARK: - 约束（embedded）

    func setupEmbeddedConstraints(in parentView: UIView) {
        translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            topAnchor.constraint(equalTo: parentView.topAnchor),
            bottomAnchor.constraint(equalTo: parentView.bottomAnchor),
            leadingAnchor.constraint(equalTo: parentView.leadingAnchor),
            trailingAnchor.constraint(equalTo: parentView.trailingAnchor)
        ])
    }

    // MARK: - 队列管理（简洁实现）

    private static func addToQueue(_ alert: MRKSwiftAlertView) {
        sharedQueue.append(alert)
    }

    private static func showNext() {
        guard !isAnimating, currentAlertView == nil, let next = sharedQueue.first else { return }
        sharedQueue.removeFirst()
        next.showWithMode(next.presentationMode, in: next.parentView)
    }
}

// MARK: - 背景 Window 实现（使用 CAGradientLayer）

class MRKAlertViewBackgroundWindow: UIWindow {
    var backgroundStyle: MRKAlertViewBackgroundStyle = .solid {
        didSet { setNeedsLayout() }
    }
    var opaqueness: Float = 0.4 {
        didSet { setNeedsLayout() }
    }

    private let gradientLayer = CAGradientLayer()

    override init(frame: CGRect) {
        super.init(frame: frame)
        commonInit()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        commonInit()
    }

    private func commonInit() {
        isUserInteractionEnabled = true
        backgroundColor = .clear
        rootViewController = UIViewController()
    }

    override func layoutSubviews() {
        super.layoutSubviews()
        switch backgroundStyle {
        case .solid:
            rootViewController?.view.backgroundColor = UIColor.black.withAlphaComponent(CGFloat(opaqueness))
            gradientLayer.removeFromSuperlayer()
        case .clear:
            rootViewController?.view.backgroundColor = .clear
            gradientLayer.removeFromSuperlayer()
        case .gradient, .gradientHeavy:
            rootViewController?.view.backgroundColor = .clear
            if gradientLayer.superlayer == nil {
                rootViewController?.view.layer.insertSublayer(gradientLayer, at: 0)
            }
            gradientLayer.frame = bounds
            gradientLayer.colors = [
                UIColor.black.withAlphaComponent(0).cgColor,
                UIColor.black.withAlphaComponent(backgroundStyle == .gradient ? 0.3 : 0.5).cgColor
            ]
            gradientLayer.locations = [0.0, 1.0]
            gradientLayer.startPoint = CGPoint(x: 0.5, y: 0.0)
            gradientLayer.endPoint = CGPoint(x: 0.5, y: 1.0)
        }
    }
}

// MARK: - Alert VC（用于 Window 模式）

class MRKAlertViewController: UIViewController {
    weak var alertView: MRKSwiftAlertView?

    public override var shouldAutorotate: Bool {
        // 由 alertView 控制是否支持旋转（iPad 支持、iPhone 通常不）
        return alertView?.shouldAutorotate ?? true
    }

    public override var supportedInterfaceOrientations: UIInterfaceOrientationMask {
        return (alertView?.shouldAutorotate ?? true) ? .all : .portrait
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = .clear
        if let alertView = alertView {
            view.addSubview(alertView)
            alertView.translatesAutoresizingMaskIntoConstraints = false
            NSLayoutConstraint.activate([
                alertView.topAnchor.constraint(equalTo: view.topAnchor),
                alertView.bottomAnchor.constraint(equalTo: view.bottomAnchor),
                alertView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
                alertView.trailingAnchor.constraint(equalTo: view.trailingAnchor)
            ])
        }
    }
}

// MARK: - UIApplication 扩展（安全获取 keyWindow）

extension UIApplication {
    var currentKeyWindow: UIWindow? {
        if #available(iOS 13.0, *) {
            return connectedScenes
                .filter { $0.activationState == .foregroundActive }
                .compactMap { $0 as? UIWindowScene }
                .flatMap { $0.windows }
                .first { $0.isKeyWindow }
        } else {
            return keyWindow
        }
    }
}

