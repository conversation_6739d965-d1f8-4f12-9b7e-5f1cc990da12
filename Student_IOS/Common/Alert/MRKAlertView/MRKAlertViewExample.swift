////
////  MRKAlertViewExample.swift
////  Student_IOS
////
////  Created by merit on 2025/8/7.
////  MRKAlertView 使用示例
////
//
//import UIKit
//
//// MARK: - 自定义弹窗示例
//
//class ExampleAlertView: MRKAlertView {
//    
//    private let titleLabel = UILabel()
//    private let messageLabel = UILabel()
//    private let confirmButton = UIButton()
//    private let cancelButton = UIButton()
//    
//    var confirmAction: (() -> Void)?
//    var cancelAction: (() -> Void)?
//    
//    convenience init(title: String, message: String, animationStyle: MRKActionAlertViewTransitionStyle = .slideFromBottom) {
//        self.init(animationStyle: animationStyle)
//        titleLabel.text = title
//        messageLabel.text = message
//    }
//    
//    override func setupContainerViewAttributes() {
//        super.setupContainerViewAttributes()
//        
//        containerView.backgroundColor = .white
//        containerView.layer.cornerRadius = 16
//        containerView.layer.shadowColor = UIColor.black.cgColor
//        containerView.layer.shadowOpacity = 0.15
//        containerView.layer.shadowOffset = CGSize(width: 0, height: 4)
//        containerView.layer.shadowRadius = 12
//    }
//    
//    override func setupContainerSubViews() {
//        super.setupContainerSubViews()
//        
//        // 配置标题
//        titleLabel.font = UIFont.boldSystemFont(ofSize: 18)
//        titleLabel.textColor = .black
//        titleLabel.textAlignment = .center
//        containerView.addSubview(titleLabel)
//        
//        // 配置消息
//        messageLabel.font = UIFont.systemFont(ofSize: 16)
//        messageLabel.textColor = .darkGray
//        messageLabel.textAlignment = .center
//        messageLabel.numberOfLines = 0
//        containerView.addSubview(messageLabel)
//        
//        // 配置确认按钮
//        confirmButton.setTitle("确定", for: .normal)
//        confirmButton.setTitleColor(.white, for: .normal)
//        confirmButton.backgroundColor = .systemBlue
//        confirmButton.layer.cornerRadius = 8
//        confirmButton.addTarget(self, action: #selector(confirmTapped), for: .touchUpInside)
//        containerView.addSubview(confirmButton)
//        
//        // 配置取消按钮
//        cancelButton.setTitle("取消", for: .normal)
//        cancelButton.setTitleColor(.systemBlue, for: .normal)
//        cancelButton.backgroundColor = .clear
//        cancelButton.layer.borderWidth = 1
//        cancelButton.layer.borderColor = UIColor.systemBlue.cgColor
//        cancelButton.layer.cornerRadius = 8
//        cancelButton.addTarget(self, action: #selector(cancelTapped), for: .touchUpInside)
//        containerView.addSubview(cancelButton)
//    }
//    
//    override func layoutContainerView() {
//        containerView.translatesAutoresizingMaskIntoConstraints = false
//        NSLayoutConstraint.activate([
//            containerView.centerXAnchor.constraint(equalTo: centerXAnchor),
//            containerView.centerYAnchor.constraint(equalTo: centerYAnchor),
//            containerView.widthAnchor.constraint(equalToConstant: 280),
//            containerView.heightAnchor.constraint(greaterThanOrEqualToConstant: 160)
//        ])
//    }
//    
//    override func layoutContainerViewSubViews() {
//        super.layoutContainerViewSubViews()
//        
//        titleLabel.translatesAutoresizingMaskIntoConstraints = false
//        messageLabel.translatesAutoresizingMaskIntoConstraints = false
//        confirmButton.translatesAutoresizingMaskIntoConstraints = false
//        cancelButton.translatesAutoresizingMaskIntoConstraints = false
//        
//        NSLayoutConstraint.activate([
//            // 标题约束
//            titleLabel.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 24),
//            titleLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 20),
//            titleLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -20),
//            
//            // 消息约束
//            messageLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 16),
//            messageLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 20),
//            messageLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -20),
//            
//            // 按钮约束
//            cancelButton.topAnchor.constraint(equalTo: messageLabel.bottomAnchor, constant: 24),
//            cancelButton.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 20),
//            cancelButton.widthAnchor.constraint(equalToConstant: 110),
//            cancelButton.heightAnchor.constraint(equalToConstant: 44),
//            cancelButton.bottomAnchor.constraint(equalTo: containerView.bottomAnchor, constant: -20),
//            
//            confirmButton.topAnchor.constraint(equalTo: messageLabel.bottomAnchor, constant: 24),
//            confirmButton.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -20),
//            confirmButton.widthAnchor.constraint(equalToConstant: 110),
//            confirmButton.heightAnchor.constraint(equalToConstant: 44),
//            confirmButton.bottomAnchor.constraint(equalTo: containerView.bottomAnchor, constant: -20)
//        ])
//    }
//    
//    @objc private func confirmTapped() {
//        confirmAction?()
//        dismiss(animated: true)
//    }
//    
//    @objc private func cancelTapped() {
//        cancelAction?()
//        dismiss(animated: true)
//    }
//}
//
//// MARK: - 使用示例控制器
//
//class MRKAlertViewExampleViewController: UIViewController {
//    
//    @IBOutlet weak var stackView: UIStackView!
//    
//    override func viewDidLoad() {
//        super.viewDidLoad()
//        setupUI()
//    }
//    
//    private func setupUI() {
//        title = "MRKAlertView 示例"
//        view.backgroundColor = .systemBackground
//        
//        // 创建按钮
//        let buttons = [
//            ("Window 模式 - 滑入", #selector(showWindowSlideAlert)),
//            ("Window 模式 - 淡入", #selector(showWindowFadeAlert)),
//            ("Window 模式 - 弹跳", #selector(showWindowBounceAlert)),
//            ("嵌入模式 - 滑入", #selector(showEmbeddedSlideAlert)),
//            ("嵌入模式 - 淡入", #selector(showEmbeddedFadeAlert)),
//            ("测试旋转支持", #selector(showRotationAlert))
//        ]
//        
//        for (title, action) in buttons {
//            let button = UIButton(type: .system)
//            button.setTitle(title, for: .normal)
//            button.titleLabel?.font = UIFont.systemFont(ofSize: 16)
//            button.backgroundColor = .systemBlue
//            button.setTitleColor(.white, for: .normal)
//            button.layer.cornerRadius = 8
//            button.addTarget(self, action: action, for: .touchUpInside)
//            
//            button.translatesAutoresizingMaskIntoConstraints = false
//            button.heightAnchor.constraint(equalToConstant: 50).isActive = true
//            
//            stackView.addArrangedSubview(button)
//        }
//    }
//    
//    // MARK: - Window 模式示例
//    
//    @objc private func showWindowSlideAlert() {
//        let alert = ExampleAlertView(
//            title: "Window 模式",
//            message: "这是一个从底部滑入的弹窗，以独立 Window 形式展示",
//            animationStyle: .slideFromBottom
//        )
//        
//        alert.delegate = self
//        alert.isAutoHidden = true
//        alert.backgroundStyle = .solid
//        
//        alert.confirmAction = {
//            print("用户点击了确定")
//        }
//        
//        alert.cancelAction = {
//            print("用户点击了取消")
//        }
//        
//        alert.show()
//    }
//    
//    @objc private func showWindowFadeAlert() {
//        let alert = ExampleAlertView(
//            title: "淡入效果",
//            message: "这是一个淡入淡出的弹窗效果",
//            animationStyle: .fade
//        )
//        
//        alert.delegate = self
//        alert.backgroundStyle = .gradient
//        alert.show()
//    }
//    
//    @objc private func showWindowBounceAlert() {
//        let alert = ExampleAlertView(
//            title: "弹跳效果",
//            message: "这是一个弹跳效果的弹窗",
//            animationStyle: .bounce
//        )
//        
//        alert.delegate = self
//        alert.backgroundStyle = .gradientHeavy
//        alert.show()
//    }
//    
//    // MARK: - 嵌入模式示例
//    
//    @objc private func showEmbeddedSlideAlert() {
//        let alert = ExampleAlertView(
//            title: "嵌入模式",
//            message: "这是一个嵌入到当前视图中的弹窗",
//            animationStyle: .slideFromBottom
//        )
//        
//        alert.delegate = self
//        alert.isAutoHidden = true
//        alert.show(in: view)
//    }
//    
//    @objc private func showEmbeddedFadeAlert() {
//        let alert = ExampleAlertView(
//            title: "嵌入淡入",
//            message: "这是一个嵌入模式的淡入弹窗",
//            animationStyle: .fade
//        )
//        
//        alert.delegate = self
//        alert.show(in: view)
//    }
//    
//    @objc private func showRotationAlert() {
//        let alert = ExampleAlertView(
//            title: "旋转测试",
//            message: "在 iPad 上旋转屏幕测试弹窗的适配效果",
//            animationStyle: .bounce
//        )
//        
//        alert.delegate = self
//        alert.shouldAutorotate = true // 强制支持旋转
//        alert.show()
//    }
//}
//
//// MARK: - 代理实现
//
//extension MRKAlertViewExampleViewController: MRKActionAlertViewDelegate {
//    
//    func actionAlertViewWillShow() {
//        print("弹窗即将显示")
//    }
//    
//    func actionAlertViewDidShow() {
//        print("弹窗已经显示")
//    }
//    
//    func actionAlertViewWillDismiss() {
//        print("弹窗即将隐藏")
//    }
//    
//    func actionAlertViewDidDismiss() {
//        print("弹窗已经隐藏")
//    }
//    
//    func actionAlertViewDidSelectBackGroundView() {
//        print("用户点击了背景")
//    }
//}
