//
//  WindowShowTestViewController.swift
//  Student_IOS
//
//  Created by merit on 2025/8/7.
//  测试 showInWindow 方法中 containerView 显示问题
//

import UIKit

class WindowShowTestViewController: UIViewController {
    
    private let scrollView = UIScrollView()
    private let stackView = UIStackView()
    private let logTextView = UITextView()
    
    private var logMessages: [String] = []
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
    }
    
    private func setupUI() {
        title = "Window 显示测试"
        view.backgroundColor = .systemBackground
        
        // 设置滚动视图
        scrollView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(scrollView)
        
        // 设置堆栈视图
        stackView.axis = .vertical
        stackView.spacing = 16
        stackView.translatesAutoresizingMaskIntoConstraints = false
        scrollView.addSubview(stackView)
        
        // 日志文本视图
        logTextView.font = UIFont.monospacedSystemFont(ofSize: 12, weight: .regular)
        logTextView.backgroundColor = UIColor.systemGray6
        logTextView.layer.cornerRadius = 8
        logTextView.isEditable = false
        logTextView.text = "📋 测试日志:\n"
        
        // 添加按钮
        let buttons = [
            ("测试基础 Window 显示", #selector(testBasicWindowShow)),
            ("测试自定义内容 Window", #selector(testCustomContentWindow)),
            ("测试不同动画效果", #selector(testDifferentAnimations)),
            ("测试背景点击", #selector(testBackgroundTap)),
            ("清空日志", #selector(clearLog))
        ]
        
        stackView.addArrangedSubview(logTextView)
        
        for (title, action) in buttons {
            let button = createButton(title: title, action: action)
            stackView.addArrangedSubview(button)
        }
        
        // 设置约束
        NSLayoutConstraint.activate([
            scrollView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            scrollView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            scrollView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            scrollView.bottomAnchor.constraint(equalTo: view.bottomAnchor),
            
            stackView.topAnchor.constraint(equalTo: scrollView.topAnchor, constant: 20),
            stackView.leadingAnchor.constraint(equalTo: scrollView.leadingAnchor, constant: 20),
            stackView.trailingAnchor.constraint(equalTo: scrollView.trailingAnchor, constant: -20),
            stackView.bottomAnchor.constraint(equalTo: scrollView.bottomAnchor, constant: -20),
            stackView.widthAnchor.constraint(equalTo: scrollView.widthAnchor, constant: -40),
            
            logTextView.heightAnchor.constraint(equalToConstant: 200)
        ])
    }
    
    private func createButton(title: String, action: Selector) -> UIButton {
        let button = UIButton(type: .system)
        button.setTitle(title, for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 16)
        button.backgroundColor = .systemBlue
        button.setTitleColor(.white, for: .normal)
        button.layer.cornerRadius = 8
        button.addTarget(self, action: action, for: .touchUpInside)
        
        button.translatesAutoresizingMaskIntoConstraints = false
        button.heightAnchor.constraint(equalToConstant: 50).isActive = true
        
        return button
    }
    
    // MARK: - 测试方法
    
    @objc private func testBasicWindowShow() {
        addLog("开始测试基础 Window 显示")
        
        let alert = MRKSwiftAlertView(animationStyle: .slideFromBottom)
        alert.isAutoHidden = true
        alert.delegate = self
        
        addLog("创建 MRKSwiftAlertView 实例")
        addLog("调用 show() 方法...")
        
        alert.show()
        
        addLog("show() 方法调用完成")
    }
    
    @objc private func testCustomContentWindow() {
        addLog("开始测试自定义内容 Window")
        
        let alert = CustomContentAlert(animationStyle: .slideFromBottom)
        alert.isAutoHidden = true
        alert.delegate = self
        
        addLog("创建自定义内容弹窗")
        alert.show()
        addLog("自定义内容弹窗显示完成")
    }
    
    @objc private func testDifferentAnimations() {
        addLog("开始测试不同动画效果")
        
        let animations: [MRKAlertViewTransitionStyle] = [.fade, .bounce, .slideFromTop, .dropDown]
        let animationNames = ["淡入", "弹跳", "从顶部滑入", "下拉"]
        
        for (index, animation) in animations.enumerated() {
            DispatchQueue.main.asyncAfter(deadline: .now() + Double(index) * 2.0) {
                let alert = MRKSwiftAlertView(animationStyle: animation)
                alert.isAutoHidden = true
                alert.delegate = self
                
                self.addLog("显示 \(animationNames[index]) 动画弹窗")
                alert.show()
            }
        }
    }
    
    @objc private func testBackgroundTap() {
        addLog("开始测试背景点击")
        
        let alert = MRKSwiftAlertView(animationStyle: .slideFromBottom)
        alert.isAutoHidden = true
        alert.delegate = self
        
        addLog("显示弹窗，请点击背景测试")
        alert.show()
    }
    
    @objc private func clearLog() {
        logMessages.removeAll()
        logTextView.text = "📋 测试日志:\n"
    }
    
    private func addLog(_ message: String) {
        let timestamp = DateFormatter.timeFormatter.string(from: Date())
        let logMessage = "[\(timestamp)] \(message)"
        logMessages.append(logMessage)
        
        let allLogs = "📋 测试日志:\n" + logMessages.joined(separator: "\n")
        logTextView.text = allLogs
        
        // 滚动到底部
        let bottom = NSMakeRange(logTextView.text.count - 1, 1)
        logTextView.scrollRangeToVisible(bottom)
        
        print(logMessage)
    }
}

// MARK: - 自定义内容弹窗

class CustomContentAlert: MRKSwiftAlertView {
    
    private let titleLabel = UILabel()
    private let messageLabel = UILabel()
    private let confirmButton = UIButton()
    
    override func setupContainerViewAttributes() {
        super.setupContainerViewAttributes()
        // 使用父类的默认样式
    }
    
    override func setupContainerSubViews() {
        // 清空默认内容
        containerView.subviews.forEach { $0.removeFromSuperview() }
        
        // 配置标题
        titleLabel.text = "自定义内容测试"
        titleLabel.font = UIFont.boldSystemFont(ofSize: 18)
        titleLabel.textColor = .black
        titleLabel.textAlignment = .center
        containerView.addSubview(titleLabel)
        
        // 配置消息
        messageLabel.text = "这是一个自定义内容的弹窗\n用于测试 containerView 是否正常显示"
        messageLabel.font = UIFont.systemFont(ofSize: 16)
        messageLabel.textColor = .darkGray
        messageLabel.textAlignment = .center
        messageLabel.numberOfLines = 0
        containerView.addSubview(messageLabel)
        
        // 配置确认按钮
        confirmButton.setTitle("确定", for: .normal)
        confirmButton.setTitleColor(.white, for: .normal)
        confirmButton.backgroundColor = .systemBlue
        confirmButton.layer.cornerRadius = 8
        confirmButton.addTarget(self, action: #selector(confirmTapped), for: .touchUpInside)
        containerView.addSubview(confirmButton)
    }
    
    override func layoutContainerViewSubViews() {
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        messageLabel.translatesAutoresizingMaskIntoConstraints = false
        confirmButton.translatesAutoresizingMaskIntoConstraints = false
        
        NSLayoutConstraint.activate([
            // 标题约束
            titleLabel.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 20),
            titleLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 20),
            titleLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -20),
            
            // 消息约束
            messageLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 16),
            messageLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 20),
            messageLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -20),
            
            // 按钮约束
            confirmButton.topAnchor.constraint(equalTo: messageLabel.bottomAnchor, constant: 20),
            confirmButton.centerXAnchor.constraint(equalTo: containerView.centerXAnchor),
            confirmButton.widthAnchor.constraint(equalToConstant: 120),
            confirmButton.heightAnchor.constraint(equalToConstant: 44),
            confirmButton.bottomAnchor.constraint(equalTo: containerView.bottomAnchor, constant: -20)
        ])
    }
    
    @objc private func confirmTapped() {
        dismiss(animated: true)
    }
}

// MARK: - 代理实现

extension WindowShowTestViewController: MRKActionAlertViewDelegate {
    
    func actionAlertViewWillShow() {
        addLog("🎬 弹窗即将显示")
    }
    
    func actionAlertViewDidShow() {
        addLog("✅ 弹窗已显示")
    }
    
    func actionAlertViewWillDismiss() {
        addLog("🎬 弹窗即将隐藏")
    }
    
    func actionAlertViewDidDismiss() {
        addLog("✅ 弹窗已隐藏")
    }
    
    func actionAlertViewDidSelectBackGroundView() {
        addLog("🎯 用户点击了背景区域！")
    }
}

// MARK: - 扩展

private extension DateFormatter {
    static let timeFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm:ss.SSS"
        return formatter
    }()
}
