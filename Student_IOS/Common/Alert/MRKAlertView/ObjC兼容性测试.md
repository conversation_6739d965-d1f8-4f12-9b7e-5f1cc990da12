# MRKSwiftAlertView Objective-C 兼容性测试

## 概述

`MRKSwiftAlertView` 完全支持 Objective-C 调用，以下是兼容性测试和使用示例。

## API 对照表

### 静态方法

| Swift 方法 | Objective-C 方法 | 说明 |
|-----------|------------------|------|
| `MRKSwiftAlertView.getCurrentAlertView()` | `[MRKSwiftAlertView getCurrentAlertView]` | 获取当前弹窗 |
| `MRKSwiftAlertView.isAnimating()` | `[MRKSwiftAlertView isAnimating]` | 是否正在动画 |
| `MRKSwiftAlertView.queueCount()` | `[MRKSwiftAlertView queueCount]` | 队列长度 |
| `MRKSwiftAlertView.dismissAll()` | `[MRKSwiftAlertView dismissAll]` | 强制关闭所有 |
| `MRKSwiftAlertView.dismissCurrent()` | `[MRKSwiftAlertView dismissCurrent]` | 关闭当前弹窗 |
| `MRKSwiftAlertView.clearQueue()` | `[MRKSwiftAlertView clearQueue]` | 清空队列 |
| `MRKSwiftAlertView.insertToQueueHead(_:)` | `[MRKSwiftAlertView insertToQueueHead:]` | 插入队列头部 |

### 实例方法

| Swift 方法 | Objective-C 方法 | 说明 |
|-----------|------------------|------|
| `alert.show()` | `[alert show]` | 显示弹窗 |
| `alert.show(in: view)` | `[alert showInView:view]` | 嵌入显示 |
| `alert.dismiss(animated: true)` | `[alert dismissAnimated:YES]` | 隐藏弹窗 |

## Objective-C 使用示例

### 1. 基本使用

```objc
#import "Student_IOS-Swift.h" // 导入 Swift 生成的头文件

@interface TestViewController : UIViewController <MRKActionAlertViewDelegate>
@end

@implementation TestViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    [self testBasicAlert];
}

- (void)testBasicAlert {
    // 创建弹窗
    MRKSwiftAlertView *alert = [[MRKSwiftAlertView alloc] initWithAnimationStyle:MRKAlertViewTransitionStyleSlideFromBottom];
    
    // 设置属性
    alert.delegate = self;
    alert.isAutoHidden = YES;
    alert.backgroundStyle = MRKAlertViewBackgroundStyleSolid;
    alert.opaqueness = 0.5f;
    
    // 显示弹窗
    [alert show];
}

#pragma mark - MRKActionAlertViewDelegate

- (void)actionAlertViewDidShow {
    NSLog(@"弹窗已显示");
}

- (void)actionAlertViewDidDismiss {
    NSLog(@"弹窗已隐藏");
}

- (void)actionAlertViewDidSelectBackGroundView {
    NSLog(@"用户点击了背景");
}

@end
```

### 2. 队列管理测试

```objc
@implementation QueueTestViewController

- (void)testMultipleAlerts {
    // 创建多个弹窗，测试队列功能
    for (int i = 1; i <= 3; i++) {
        MRKSwiftAlertView *alert = [[MRKSwiftAlertView alloc] initWithAnimationStyle:MRKAlertViewTransitionStyleSlideFromBottom];
        alert.delegate = self;
        [alert show]; // 会自动加入队列
    }
    
    NSLog(@"已创建 3 个弹窗，队列长度: %ld", (long)[MRKSwiftAlertView queueCount]);
}

- (void)testPriorityAlert {
    // 先创建普通弹窗
    [self testMultipleAlerts];
    
    // 创建优先弹窗
    MRKSwiftAlertView *priorityAlert = [[MRKSwiftAlertView alloc] initWithAnimationStyle:MRKAlertViewTransitionStyleBounce];
    priorityAlert.delegate = self;
    
    // 插入到队列头部
    [MRKSwiftAlertView insertToQueueHead:priorityAlert];
    
    NSLog(@"已插入优先弹窗，队列长度: %ld", (long)[MRKSwiftAlertView queueCount]);
}

- (void)testQueueControl {
    // 查看队列状态
    MRKSwiftAlertView *current = [MRKSwiftAlertView getCurrentAlertView];
    NSInteger queueCount = [MRKSwiftAlertView queueCount];
    BOOL isAnimating = [MRKSwiftAlertView isAnimating];
    
    NSLog(@"队列状态 - 当前: %@, 队列: %ld, 动画: %@", 
          current ? @"有" : @"无", 
          (long)queueCount, 
          isAnimating ? @"是" : @"否");
    
    // 强制关闭所有弹窗
    [MRKSwiftAlertView dismissAll];
    NSLog(@"已强制关闭所有弹窗");
}

- (void)testEmbeddedMode {
    // 嵌入模式测试
    MRKSwiftAlertView *alert = [[MRKSwiftAlertView alloc] initWithAnimationStyle:MRKAlertViewTransitionStyleFade];
    alert.delegate = self;
    [alert showInView:self.view];
    
    NSLog(@"已显示嵌入模式弹窗");
}

@end
```

### 3. 自定义弹窗（Objective-C）

```objc
@interface CustomObjCAlert : MRKSwiftAlertView
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *messageLabel;
@property (nonatomic, strong) UIButton *confirmButton;
@property (nonatomic, copy) void(^confirmAction)(void);
@end

@implementation CustomObjCAlert

- (instancetype)initWithTitle:(NSString *)title message:(NSString *)message {
    if (self = [super initWithAnimationStyle:MRKAlertViewTransitionStyleSlideFromBottom]) {
        [self setupWithTitle:title message:message];
    }
    return self;
}

- (void)setupWithTitle:(NSString *)title message:(NSString *)message {
    self.titleLabel = [[UILabel alloc] init];
    self.titleLabel.text = title;
    self.titleLabel.font = [UIFont boldSystemFontOfSize:18];
    self.titleLabel.textAlignment = NSTextAlignmentCenter;
    
    self.messageLabel = [[UILabel alloc] init];
    self.messageLabel.text = message;
    self.messageLabel.font = [UIFont systemFontOfSize:16];
    self.messageLabel.textAlignment = NSTextAlignmentCenter;
    self.messageLabel.numberOfLines = 0;
    
    self.confirmButton = [UIButton buttonWithType:UIButtonTypeSystem];
    [self.confirmButton setTitle:@"确定" forState:UIControlStateNormal];
    [self.confirmButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    self.confirmButton.backgroundColor = [UIColor systemBlueColor];
    self.confirmButton.layer.cornerRadius = 8;
    [self.confirmButton addTarget:self action:@selector(confirmTapped) forControlEvents:UIControlEventTouchUpInside];
}

- (void)setupContainerViewAttributes {
    [super setupContainerViewAttributes];
    
    self.containerView.backgroundColor = [UIColor whiteColor];
    self.containerView.layer.cornerRadius = 12;
    self.containerView.layer.shadowColor = [UIColor blackColor].CGColor;
    self.containerView.layer.shadowOpacity = 0.2f;
    self.containerView.layer.shadowOffset = CGSizeMake(0, 2);
    self.containerView.layer.shadowRadius = 8;
}

- (void)setupContainerSubViews {
    [super setupContainerSubViews];
    
    [self.containerView addSubview:self.titleLabel];
    [self.containerView addSubview:self.messageLabel];
    [self.containerView addSubview:self.confirmButton];
}

- (void)layoutContainerView {
    self.containerView.translatesAutoresizingMaskIntoConstraints = NO;
    [NSLayoutConstraint activateConstraints:@[
        [self.containerView.centerXAnchor constraintEqualToAnchor:self.centerXAnchor],
        [self.containerView.centerYAnchor constraintEqualToAnchor:self.centerYAnchor],
        [self.containerView.widthAnchor constraintEqualToConstant:280],
        [self.containerView.heightAnchor constraintGreaterThanOrEqualToConstant:160]
    ]];
}

- (void)layoutContainerViewSubViews {
    [super layoutContainerViewSubViews];
    
    self.titleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    self.messageLabel.translatesAutoresizingMaskIntoConstraints = NO;
    self.confirmButton.translatesAutoresizingMaskIntoConstraints = NO;
    
    [NSLayoutConstraint activateConstraints:@[
        // 标题约束
        [self.titleLabel.topAnchor constraintEqualToAnchor:self.containerView.topAnchor constant:20],
        [self.titleLabel.leadingAnchor constraintEqualToAnchor:self.containerView.leadingAnchor constant:20],
        [self.titleLabel.trailingAnchor constraintEqualToAnchor:self.containerView.trailingAnchor constant:-20],
        
        // 消息约束
        [self.messageLabel.topAnchor constraintEqualToAnchor:self.titleLabel.bottomAnchor constant:16],
        [self.messageLabel.leadingAnchor constraintEqualToAnchor:self.containerView.leadingAnchor constant:20],
        [self.messageLabel.trailingAnchor constraintEqualToAnchor:self.containerView.trailingAnchor constant:-20],
        
        // 按钮约束
        [self.confirmButton.topAnchor constraintEqualToAnchor:self.messageLabel.bottomAnchor constant:20],
        [self.confirmButton.centerXAnchor constraintEqualToAnchor:self.containerView.centerXAnchor],
        [self.confirmButton.widthAnchor constraintEqualToConstant:120],
        [self.confirmButton.heightAnchor constraintEqualToConstant:44],
        [self.confirmButton.bottomAnchor constraintEqualToAnchor:self.containerView.bottomAnchor constant:-20]
    ]];
}

- (void)confirmTapped {
    if (self.confirmAction) {
        self.confirmAction();
    }
    [self dismissAnimated:YES];
}

@end

// 使用示例
- (void)showCustomAlert {
    CustomObjCAlert *alert = [[CustomObjCAlert alloc] initWithTitle:@"自定义弹窗" message:@"这是用 Objective-C 创建的自定义弹窗"];
    
    alert.confirmAction = ^{
        NSLog(@"用户点击了确定按钮");
    };
    
    [alert show];
}
```

## 兼容性验证清单

### ✅ 基本功能
- [x] 弹窗创建和显示
- [x] 不同动画效果
- [x] 背景样式设置
- [x] 代理方法回调

### ✅ 队列管理
- [x] 自动队列管理
- [x] 优先级插入
- [x] 强制关闭功能
- [x] 状态查询方法

### ✅ 展示模式
- [x] Window 模式
- [x] 嵌入模式
- [x] 设备旋转支持

### ✅ 内存管理
- [x] 自动释放
- [x] 代理弱引用
- [x] 循环引用避免

## 注意事项

1. **导入头文件**：确保导入 `Student_IOS-Swift.h`
2. **枚举值**：使用完整的枚举名称，如 `MRKAlertViewTransitionStyleSlideFromBottom`
3. **代理方法**：所有代理方法都是可选的，使用 `@optional` 标记
4. **内存管理**：遵循 ARC 规则，避免强引用循环
5. **线程安全**：UI 操作确保在主线程执行

## 总结

`MRKSwiftAlertView` 提供了完整的 Objective-C 兼容性，包括：

- ✅ 所有公共 API 都可以在 OC 中调用
- ✅ 完整的队列管理功能
- ✅ 自定义弹窗支持
- ✅ 代理模式完全兼容
- ✅ 内存管理安全可靠

这确保了现有的 Objective-C 项目可以无缝使用这个 Swift 重构的弹窗组件。
