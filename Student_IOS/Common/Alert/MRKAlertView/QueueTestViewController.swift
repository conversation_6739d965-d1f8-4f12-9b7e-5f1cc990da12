//
//  QueueTestViewController.swift
//  Student_IOS
//
//  Created by merit on 2025/8/7.
//  队列管理测试控制器
//

import UIKit

// MARK: - 简单的测试弹窗

class SimpleTestAlert: MRKSwiftAlertView {
    
    private let titleLabel = UILabel()
    private let messageLabel = UILabel()
    private let confirmButton = UIButton()
    
    var confirmAction: (() -> Void)?
    
    convenience init(title: String, message: String, animationStyle: MRKAlertViewTransitionStyle = .slideFromBottom) {
        self.init(animationStyle: animationStyle)
        titleLabel.text = title
        messageLabel.text = message
    }
    
    override func setupContainerViewAttributes() {
        super.setupContainerViewAttributes()
        
        containerView.backgroundColor = .white
        containerView.layer.cornerRadius = 12
        containerView.layer.shadowColor = UIColor.black.cgColor
        containerView.layer.shadowOpacity = 0.2
        containerView.layer.shadowOffset = CGSize(width: 0, height: 2)
        containerView.layer.shadowRadius = 8
    }
    
    override func setupContainerSubViews() {
        super.setupContainerSubViews()
        
        // 配置标题
        titleLabel.font = UIFont.boldSystemFont(ofSize: 18)
        titleLabel.textColor = .black
        titleLabel.textAlignment = .center
        containerView.addSubview(titleLabel)
        
        // 配置消息
        messageLabel.font = UIFont.systemFont(ofSize: 16)
        messageLabel.textColor = .darkGray
        messageLabel.textAlignment = .center
        messageLabel.numberOfLines = 0
        containerView.addSubview(messageLabel)
        
        // 配置确认按钮
        confirmButton.setTitle("确定", for: .normal)
        confirmButton.setTitleColor(.white, for: .normal)
        confirmButton.backgroundColor = .systemBlue
        confirmButton.layer.cornerRadius = 8
        confirmButton.addTarget(self, action: #selector(confirmTapped), for: .touchUpInside)
        containerView.addSubview(confirmButton)
    }
    
    override func layoutContainerView() {
        containerView.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            containerView.centerXAnchor.constraint(equalTo: centerXAnchor),
            containerView.centerYAnchor.constraint(equalTo: centerYAnchor),
            containerView.widthAnchor.constraint(equalToConstant: 280),
            containerView.heightAnchor.constraint(greaterThanOrEqualToConstant: 160)
        ])
    }
    
    override func layoutContainerViewSubViews() {
        super.layoutContainerViewSubViews()
        
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        messageLabel.translatesAutoresizingMaskIntoConstraints = false
        confirmButton.translatesAutoresizingMaskIntoConstraints = false
        
        NSLayoutConstraint.activate([
            // 标题约束
            titleLabel.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 20),
            titleLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 20),
            titleLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -20),
            
            // 消息约束
            messageLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 16),
            messageLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 20),
            messageLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -20),
            
            // 按钮约束
            confirmButton.topAnchor.constraint(equalTo: messageLabel.bottomAnchor, constant: 20),
            confirmButton.centerXAnchor.constraint(equalTo: containerView.centerXAnchor),
            confirmButton.widthAnchor.constraint(equalToConstant: 120),
            confirmButton.heightAnchor.constraint(equalToConstant: 44),
            confirmButton.bottomAnchor.constraint(equalTo: containerView.bottomAnchor, constant: -20)
        ])
    }
    
    @objc private func confirmTapped() {
        confirmAction?()
        dismiss(animated: true)
    }
}

// MARK: - 队列测试控制器

class QueueTestViewController: UIViewController {
    
    private let scrollView = UIScrollView()
    private let stackView = UIStackView()
    private let statusLabel = UILabel()
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        startStatusMonitoring()
    }
    
    private func setupUI() {
        title = "队列管理测试"
        view.backgroundColor = .systemBackground
        
        // 设置滚动视图
        scrollView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(scrollView)
        
        // 设置堆栈视图
        stackView.axis = .vertical
        stackView.spacing = 16
        stackView.translatesAutoresizingMaskIntoConstraints = false
        scrollView.addSubview(stackView)
        
        // 状态标签
        statusLabel.font = UIFont.monospacedSystemFont(ofSize: 14, weight: .regular)
        statusLabel.textColor = .systemBlue
        statusLabel.numberOfLines = 0
        statusLabel.backgroundColor = UIColor.systemGray6
        statusLabel.layer.cornerRadius = 8
        statusLabel.layer.masksToBounds = true
        statusLabel.textAlignment = .left
        
        // 添加按钮
        let buttons = [
            ("连续显示 3 个弹窗", #selector(testMultipleAlerts)),
            ("连续显示 5 个弹窗", #selector(testFiveAlerts)),
            ("优先显示弹窗", #selector(testPriorityAlert)),
            ("不同动画效果测试", #selector(testDifferentAnimations)),
            ("嵌入模式队列测试", #selector(testEmbeddedQueue)),
            ("强制关闭所有弹窗", #selector(testDismissAll)),
            ("关闭当前弹窗", #selector(testDismissCurrent)),
            ("清空队列", #selector(testClearQueue)),
            ("查看队列状态", #selector(showQueueStatus))
        ]
        
        stackView.addArrangedSubview(statusLabel)
        
        for (title, action) in buttons {
            let button = createButton(title: title, action: action)
            stackView.addArrangedSubview(button)
        }
        
        // 设置约束
        NSLayoutConstraint.activate([
            scrollView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            scrollView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            scrollView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            scrollView.bottomAnchor.constraint(equalTo: view.bottomAnchor),
            
            stackView.topAnchor.constraint(equalTo: scrollView.topAnchor, constant: 20),
            stackView.leadingAnchor.constraint(equalTo: scrollView.leadingAnchor, constant: 20),
            stackView.trailingAnchor.constraint(equalTo: scrollView.trailingAnchor, constant: -20),
            stackView.bottomAnchor.constraint(equalTo: scrollView.bottomAnchor, constant: -20),
            stackView.widthAnchor.constraint(equalTo: scrollView.widthAnchor, constant: -40)
        ])
        
        // 初始状态更新
        updateStatusLabel()
    }
    
    private func createButton(title: String, action: Selector) -> UIButton {
        let button = UIButton(type: .system)
        button.setTitle(title, for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 16)
        button.backgroundColor = .systemBlue
        button.setTitleColor(.white, for: .normal)
        button.layer.cornerRadius = 8
        button.addTarget(self, action: action, for: .touchUpInside)
        
        button.translatesAutoresizingMaskIntoConstraints = false
        button.heightAnchor.constraint(equalToConstant: 50).isActive = true
        
        return button
    }
    
    // MARK: - 测试方法
    
    @objc private func testMultipleAlerts() {
        for i in 1...3 {
            let alert = SimpleTestAlert(
                title: "弹窗 \(i)",
                message: "这是第 \(i) 个弹窗，会按顺序显示",
                animationStyle: .slideFromBottom
            )
            
            alert.confirmAction = {
                print("用户确认了弹窗 \(i)")
            }
            
            alert.show()
        }
        
        print("已创建 3 个弹窗，将按顺序显示")
    }
    
    @objc private func testFiveAlerts() {
        let animations: [MRKAlertViewTransitionStyle] = [.slideFromBottom, .fade, .bounce, .dropDown, .slideFromTop]
        
        for i in 1...5 {
            let alert = SimpleTestAlert(
                title: "弹窗 \(i)",
                message: "动画效果: \(animations[i-1])",
                animationStyle: animations[i-1]
            )
            
            alert.confirmAction = {
                print("用户确认了弹窗 \(i)")
            }
            
            alert.show()
        }
        
        print("已创建 5 个不同动画效果的弹窗")
    }
    
    @objc private func testPriorityAlert() {
        // 先创建几个普通弹窗
        for i in 1...3 {
            let alert = SimpleTestAlert(
                title: "普通弹窗 \(i)",
                message: "这是普通优先级的弹窗",
                animationStyle: .slideFromBottom
            )
            alert.show()
        }
        
        // 创建优先弹窗
        let priorityAlert = SimpleTestAlert(
            title: "优先弹窗",
            message: "这个弹窗会优先显示！",
            animationStyle: .bounce
        )
        
        priorityAlert.confirmAction = {
            print("用户确认了优先弹窗")
        }
        
        MRKSwiftAlertView.insertToQueueHead(priorityAlert)
        
        print("已插入优先弹窗到队列头部")
    }
    
    @objc private func testDifferentAnimations() {
        let animations: [(MRKAlertViewTransitionStyle, String)] = [
            (.slideFromBottom, "从底部滑入"),
            (.slideFromTop, "从顶部滑入"),
            (.fade, "淡入淡出"),
            (.bounce, "弹跳效果"),
            (.dropDown, "下拉效果")
        ]
        
        for (animation, description) in animations {
            let alert = SimpleTestAlert(
                title: "动画测试",
                message: description,
                animationStyle: animation
            )
            alert.show()
        }
    }
    
    @objc private func testEmbeddedQueue() {
        for i in 1...3 {
            let alert = SimpleTestAlert(
                title: "嵌入弹窗 \(i)",
                message: "这是嵌入到当前视图的弹窗",
                animationStyle: .fade
            )
            
            alert.confirmAction = {
                print("用户确认了嵌入弹窗 \(i)")
            }
            
            alert.show(in: view)
        }
        
        print("已创建 3 个嵌入模式弹窗")
    }
    
    @objc private func testDismissAll() {
        MRKSwiftAlertView.dismissAll()
        print("已强制关闭所有弹窗并清空队列")
    }
    
    @objc private func testDismissCurrent() {
        MRKSwiftAlertView.dismissCurrent()
        print("已关闭当前弹窗")
    }
    
    @objc private func testClearQueue() {
        MRKSwiftAlertView.clearQueue()
        print("已清空队列")
    }
    
    @objc private func showQueueStatus() {
        updateStatusLabel()
        
        let current = MRKSwiftAlertView.currentAlertView()
        let queueCount = MRKSwiftAlertView.queueCount()
        let isAnimating = MRKSwiftAlertView.isAnimating()
        
        let alert = SimpleTestAlert(
            title: "队列状态",
            message: """
            当前显示: \(current != nil ? "是" : "否")
            队列长度: \(queueCount)
            正在动画: \(isAnimating ? "是" : "否")
            """,
            animationStyle: .fade
        )
        
        alert.show()
    }
    
    // MARK: - 状态监控
    
    private func startStatusMonitoring() {
        Timer.scheduledTimer(withTimeInterval: 0.5, repeats: true) { [weak self] _ in
            self?.updateStatusLabel()
        }
    }
    
    private func updateStatusLabel() {
        let current = MRKSwiftAlertView.currentAlertView()
        let queueCount = MRKSwiftAlertView.queueCount()
        let isAnimating = MRKSwiftAlertView.isAnimating()
        
        let statusText = """
        📊 队列状态实时监控
        
        当前显示: \(current != nil ? "✅ 有弹窗" : "❌ 无弹窗")
        队列长度: \(queueCount) 个
        动画状态: \(isAnimating ? "🔄 执行中" : "⏸️ 空闲")
        
        更新时间: \(DateFormatter.timeFormatter.string(from: Date()))
        """
        
        statusLabel.text = statusText
    }
}

// MARK: - 扩展

private extension DateFormatter {
    static let timeFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm:ss"
        return formatter
    }()
}
