//
//  BackgroundTouchTestViewController.swift
//  Student_IOS
//
//  Created by merit on 2025/8/7.
//  背景点击功能测试控制器
//

import UIKit

// MARK: - 测试弹窗

class BackgroundTouchTestAlert: MRKSwiftAlertView {
    
    private let titleLabel = UILabel()
    private let messageLabel = UILabel()
    private let confirmButton = UIButton()
    private let toggleButton = UIButton()
    
    var confirmAction: (() -> Void)?
    
    convenience init(title: String, message: String, autoHidden: Bool = true, animationStyle: MRKAlertViewTransitionStyle = .slideFromBottom) {
        self.init(animationStyle: animationStyle)
        titleLabel.text = title
        messageLabel.text = message
        self.isAutoHidden = autoHidden
    }
    
    override func setupContainerViewAttributes() {
        super.setupContainerViewAttributes()
        
        containerView.backgroundColor = .white
        containerView.layer.cornerRadius = 12
        containerView.layer.shadowColor = UIColor.black.cgColor
        containerView.layer.shadowOpacity = 0.2
        containerView.layer.shadowOffset = CGSize(width: 0, height: 2)
        containerView.layer.shadowRadius = 8
    }
    
    override func setupContainerSubViews() {
        super.setupContainerSubViews()
        
        // 配置标题
        titleLabel.font = UIFont.boldSystemFont(ofSize: 18)
        titleLabel.textColor = .black
        titleLabel.textAlignment = .center
        containerView.addSubview(titleLabel)
        
        // 配置消息
        messageLabel.font = UIFont.systemFont(ofSize: 16)
        messageLabel.textColor = .darkGray
        messageLabel.textAlignment = .center
        messageLabel.numberOfLines = 0
        containerView.addSubview(messageLabel)
        
        // 配置确认按钮
        confirmButton.setTitle("确定", for: .normal)
        confirmButton.setTitleColor(.white, for: .normal)
        confirmButton.backgroundColor = .systemBlue
        confirmButton.layer.cornerRadius = 8
        confirmButton.addTarget(self, action: #selector(confirmTapped), for: .touchUpInside)
        containerView.addSubview(confirmButton)
        
        // 配置切换按钮
        updateToggleButton()
        toggleButton.setTitleColor(.white, for: .normal)
        toggleButton.backgroundColor = .systemOrange
        toggleButton.layer.cornerRadius = 8
        toggleButton.addTarget(self, action: #selector(toggleAutoHidden), for: .touchUpInside)
        containerView.addSubview(toggleButton)
    }
    
    override func layoutContainerView() {
        containerView.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            containerView.centerXAnchor.constraint(equalTo: centerXAnchor),
            containerView.centerYAnchor.constraint(equalTo: centerYAnchor),
            containerView.widthAnchor.constraint(equalToConstant: 300),
            containerView.heightAnchor.constraint(greaterThanOrEqualToConstant: 200)
        ])
    }
    
    override func layoutContainerViewSubViews() {
        super.layoutContainerViewSubViews()
        
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        messageLabel.translatesAutoresizingMaskIntoConstraints = false
        confirmButton.translatesAutoresizingMaskIntoConstraints = false
        toggleButton.translatesAutoresizingMaskIntoConstraints = false
        
        NSLayoutConstraint.activate([
            // 标题约束
            titleLabel.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 20),
            titleLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 20),
            titleLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -20),
            
            // 消息约束
            messageLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 16),
            messageLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 20),
            messageLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -20),
            
            // 切换按钮约束
            toggleButton.topAnchor.constraint(equalTo: messageLabel.bottomAnchor, constant: 20),
            toggleButton.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 20),
            toggleButton.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -20),
            toggleButton.heightAnchor.constraint(equalToConstant: 44),
            
            // 确认按钮约束
            confirmButton.topAnchor.constraint(equalTo: toggleButton.bottomAnchor, constant: 16),
            confirmButton.centerXAnchor.constraint(equalTo: containerView.centerXAnchor),
            confirmButton.widthAnchor.constraint(equalToConstant: 120),
            confirmButton.heightAnchor.constraint(equalToConstant: 44),
            confirmButton.bottomAnchor.constraint(equalTo: containerView.bottomAnchor, constant: -20)
        ])
    }
    
    @objc private func confirmTapped() {
        confirmAction?()
        dismiss(animated: true)
    }
    
    @objc private func toggleAutoHidden() {
        isAutoHidden.toggle()
        updateToggleButton()
        print("背景点击自动隐藏: \(isAutoHidden ? "开启" : "关闭")")
    }
    
    private func updateToggleButton() {
        let title = isAutoHidden ? "关闭背景点击隐藏" : "开启背景点击隐藏"
        toggleButton.setTitle(title, for: .normal)
    }
}

// MARK: - 测试控制器

class BackgroundTouchTestViewController: UIViewController {
    
    private let scrollView = UIScrollView()
    private let stackView = UIStackView()
    private let logTextView = UITextView()
    
    private var logMessages: [String] = []
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
    }
    
    private func setupUI() {
        title = "背景点击测试"
        view.backgroundColor = .systemBackground
        
        // 设置滚动视图
        scrollView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(scrollView)
        
        // 设置堆栈视图
        stackView.axis = .vertical
        stackView.spacing = 16
        stackView.translatesAutoresizingMaskIntoConstraints = false
        scrollView.addSubview(stackView)
        
        // 日志文本视图
        logTextView.font = UIFont.monospacedSystemFont(ofSize: 12, weight: .regular)
        logTextView.backgroundColor = UIColor.systemGray6
        logTextView.layer.cornerRadius = 8
        logTextView.isEditable = false
        logTextView.text = "📋 测试日志:\n"
        
        // 添加按钮
        let buttons = [
            ("Window 模式 - 背景点击隐藏", #selector(testWindowAutoHidden)),
            ("Window 模式 - 背景点击不隐藏", #selector(testWindowNoAutoHidden)),
            ("嵌入模式 - 背景点击隐藏", #selector(testEmbeddedAutoHidden)),
            ("嵌入模式 - 背景点击不隐藏", #selector(testEmbeddedNoAutoHidden)),
            ("测试不同背景样式", #selector(testDifferentBackgrounds)),
            ("测试容器内点击", #selector(testContainerClick)),
            ("清空日志", #selector(clearLog))
        ]
        
        stackView.addArrangedSubview(logTextView)
        
        for (title, action) in buttons {
            let button = createButton(title: title, action: action)
            stackView.addArrangedSubview(button)
        }
        
        // 设置约束
        NSLayoutConstraint.activate([
            scrollView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            scrollView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            scrollView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            scrollView.bottomAnchor.constraint(equalTo: view.bottomAnchor),
            
            stackView.topAnchor.constraint(equalTo: scrollView.topAnchor, constant: 20),
            stackView.leadingAnchor.constraint(equalTo: scrollView.leadingAnchor, constant: 20),
            stackView.trailingAnchor.constraint(equalTo: scrollView.trailingAnchor, constant: -20),
            stackView.bottomAnchor.constraint(equalTo: scrollView.bottomAnchor, constant: -20),
            stackView.widthAnchor.constraint(equalTo: scrollView.widthAnchor, constant: -40),
            
            logTextView.heightAnchor.constraint(equalToConstant: 200)
        ])
    }
    
    private func createButton(title: String, action: Selector) -> UIButton {
        let button = UIButton(type: .system)
        button.setTitle(title, for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 16)
        button.backgroundColor = .systemBlue
        button.setTitleColor(.white, for: .normal)
        button.layer.cornerRadius = 8
        button.addTarget(self, action: action, for: .touchUpInside)
        
        button.translatesAutoresizingMaskIntoConstraints = false
        button.heightAnchor.constraint(equalToConstant: 50).isActive = true
        
        return button
    }
    
    // MARK: - 测试方法
    
    @objc private func testWindowAutoHidden() {
        let alert = createTestAlert(
            title: "Window 模式测试",
            message: "点击背景区域应该会自动隐藏弹窗",
            autoHidden: true
        )
        alert.show()
        addLog("显示 Window 模式弹窗 - 背景点击隐藏: 开启")
    }
    
    @objc private func testWindowNoAutoHidden() {
        let alert = createTestAlert(
            title: "Window 模式测试",
            message: "点击背景区域不会隐藏弹窗，但会触发代理方法",
            autoHidden: false
        )
        alert.show()
        addLog("显示 Window 模式弹窗 - 背景点击隐藏: 关闭")
    }
    
    @objc private func testEmbeddedAutoHidden() {
        let alert = createTestAlert(
            title: "嵌入模式测试",
            message: "点击背景区域应该会自动隐藏弹窗",
            autoHidden: true
        )
        alert.show(in: view)
        addLog("显示嵌入模式弹窗 - 背景点击隐藏: 开启")
    }
    
    @objc private func testEmbeddedNoAutoHidden() {
        let alert = createTestAlert(
            title: "嵌入模式测试",
            message: "点击背景区域不会隐藏弹窗，但会触发代理方法",
            autoHidden: false
        )
        alert.show(in: view)
        addLog("显示嵌入模式弹窗 - 背景点击隐藏: 关闭")
    }
    
    @objc private func testDifferentBackgrounds() {
        let styles: [(MRKAlertViewBackgroundStyle, String)] = [
            (.solid, "纯色背景"),
            (.gradient, "渐变背景"),
            (.gradientHeavy, "重渐变背景"),
            (.clear, "透明背景")
        ]
        
        for (style, description) in styles {
            let alert = createTestAlert(
                title: "背景样式测试",
                message: "当前背景样式: \(description)",
                autoHidden: true
            )
            alert.backgroundStyle = style
            alert.show()
        }
        
        addLog("显示不同背景样式的弹窗")
    }
    
    @objc private func testContainerClick() {
        let alert = createTestAlert(
            title: "容器点击测试",
            message: "点击这个弹窗内容区域不应该触发背景点击事件",
            autoHidden: true
        )
        alert.show()
        addLog("显示容器点击测试弹窗")
    }
    
    @objc private func clearLog() {
        logMessages.removeAll()
        logTextView.text = "📋 测试日志:\n"
    }
    
    private func createTestAlert(title: String, message: String, autoHidden: Bool) -> BackgroundTouchTestAlert {
        let alert = BackgroundTouchTestAlert(
            title: title,
            message: message,
            autoHidden: autoHidden,
            animationStyle: .slideFromBottom
        )
        
        alert.delegate = self
        
        alert.confirmAction = {
            self.addLog("用户点击了确定按钮")
        }
        
        return alert
    }
    
    private func addLog(_ message: String) {
        let timestamp = DateFormatter.timeFormatter.string(from: Date())
        let logMessage = "[\(timestamp)] \(message)"
        logMessages.append(logMessage)
        
        let allLogs = "📋 测试日志:\n" + logMessages.joined(separator: "\n")
        logTextView.text = allLogs
        
        // 滚动到底部
        let bottom = NSMakeRange(logTextView.text.count - 1, 1)
        logTextView.scrollRangeToVisible(bottom)
        
        print(logMessage)
    }
}

// MARK: - 代理实现

extension BackgroundTouchTestViewController: MRKActionAlertViewDelegate {
    
    func actionAlertViewWillShow() {
        addLog("弹窗即将显示")
    }
    
    func actionAlertViewDidShow() {
        addLog("弹窗已显示")
    }
    
    func actionAlertViewWillDismiss() {
        addLog("弹窗即将隐藏")
    }
    
    func actionAlertViewDidDismiss() {
        addLog("弹窗已隐藏")
    }
    
    func actionAlertViewDidSelectBackGroundView() {
        addLog("🎯 用户点击了背景区域！")
    }
}

// MARK: - 扩展

private extension DateFormatter {
    static let timeFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm:ss"
        return formatter
    }()
}
