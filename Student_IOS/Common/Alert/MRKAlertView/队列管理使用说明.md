# MRKSwiftAlertView 队列管理使用说明

## 概述

`MRKSwiftAlertView` 提供了完整的队列管理功能，确保多个弹窗按顺序显示，避免同时显示多个弹窗造成的界面混乱。

## 核心特性

### 🎯 自动队列管理

当有弹窗正在显示时，新的弹窗会自动加入队列等待显示：

```swift
// 第一个弹窗会立即显示
let alert1 = MRKSwiftAlertView(animationStyle: .slideFromBottom)
alert1.show() // 立即显示

// 第二个弹窗会自动加入队列
let alert2 = MRKSwiftAlertView(animationStyle: .fade)
alert2.show() // 加入队列，等待 alert1 关闭后显示

// 第三个弹窗也会加入队列
let alert3 = MRKSwiftAlertView(animationStyle: .bounce)
alert3.show() // 加入队列，等待前面的弹窗关闭
```

### 📊 队列状态查询

```swift
// 获取当前显示的弹窗
if let current = MRKSwiftAlertView.currentAlertView() {
    print("当前显示的弹窗: \(current)")
}

// 检查是否正在执行动画
if MRKSwiftAlertView.isAnimating() {
    print("正在执行动画")
}

// 获取队列中等待的弹窗数量
let queueCount = MRKSwiftAlertView.queueCount()
print("队列中有 \(queueCount) 个弹窗等待显示")
```

### 🔧 队列控制方法

#### 1. 强制关闭所有弹窗
```swift
// 立即关闭当前弹窗并清空队列
MRKSwiftAlertView.dismissAll()
```

#### 2. 关闭当前弹窗（继续显示下一个）
```swift
// 关闭当前弹窗，队列中的下一个会自动显示
MRKSwiftAlertView.dismissCurrent()
```

#### 3. 优先显示（插入队列头部）
```swift
let urgentAlert = MRKSwiftAlertView(animationStyle: .bounce)
// 插入到队列头部，优先显示
MRKSwiftAlertView.insertToQueueHead(urgentAlert)
```

#### 4. 清空队列
```swift
// 只清空队列，不影响当前显示的弹窗
MRKSwiftAlertView.clearQueue()
```

## 使用场景示例

### 场景1：网络请求错误处理

```swift
class NetworkManager {
    
    func handleMultipleErrors() {
        // 模拟多个网络请求同时失败
        showError("网络连接失败")
        showError("服务器响应超时")
        showError("数据解析错误")
        
        // 这些错误弹窗会按顺序显示，不会重叠
    }
    
    private func showError(_ message: String) {
        let alert = createErrorAlert(message: message)
        alert.show() // 自动队列管理
    }
    
    private func createErrorAlert(message: String) -> MRKSwiftAlertView {
        let alert = CustomAlertView(
            title: "错误",
            message: message,
            animationStyle: .slideFromBottom
        )
        
        alert.confirmAction = {
            print("用户确认错误: \(message)")
        }
        
        return alert
    }
}
```

### 场景2：应用启动时的多个提示

```swift
class AppLaunchManager {
    
    func showLaunchAlerts() {
        // 检查更新提示
        if hasUpdate {
            let updateAlert = createUpdateAlert()
            updateAlert.show()
        }
        
        // 权限请求提示
        if needsPermission {
            let permissionAlert = createPermissionAlert()
            permissionAlert.show()
        }
        
        // 新功能介绍
        if hasNewFeature {
            let featureAlert = createFeatureAlert()
            featureAlert.show()
        }
        
        // 这些弹窗会按顺序显示
    }
    
    // 紧急公告（优先显示）
    func showUrgentAnnouncement() {
        let urgentAlert = createAnnouncementAlert()
        MRKSwiftAlertView.insertToQueueHead(urgentAlert)
    }
}
```

### 场景3：用户操作确认

```swift
class UserActionManager {
    
    func handleBatchOperations() {
        // 用户执行批量操作，可能触发多个确认弹窗
        
        if needsDeleteConfirmation {
            showDeleteConfirmation()
        }
        
        if needsPaymentConfirmation {
            showPaymentConfirmation()
        }
        
        if needsLogoutConfirmation {
            showLogoutConfirmation()
        }
    }
    
    private func showDeleteConfirmation() {
        let alert = CustomAlertView(
            title: "确认删除",
            message: "此操作不可撤销，确定要删除吗？",
            animationStyle: .bounce
        )
        
        alert.confirmAction = { [weak self] in
            self?.performDelete()
        }
        
        alert.show()
    }
    
    // 紧急情况：强制清空所有弹窗
    func handleEmergencyExit() {
        MRKSwiftAlertView.dismissAll()
        // 执行紧急退出逻辑
    }
}
```

## 队列管理原理

### 显示流程

```
用户调用 show()
       ↓
检查是否有弹窗正在显示
       ↓
有 → 加入队列等待    无 → 立即显示
       ↓                    ↓
等待当前弹窗关闭      设置为当前弹窗
       ↓                    ↓
自动显示队列中的下一个   执行显示动画
```

### 隐藏流程

```
用户调用 dismiss()
       ↓
执行隐藏动画
       ↓
清除当前弹窗引用
       ↓
重置动画状态
       ↓
延迟 0.1 秒后显示队列中的下一个
```

## 最佳实践

### 1. 合理使用队列

```swift
// ✅ 推荐：让系统自动管理队列
alert1.show()
alert2.show()
alert3.show()

// ❌ 不推荐：手动管理显示时机
alert1.show()
DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
    alert2.show()
}
```

### 2. 紧急情况处理

```swift
// 在应用进入后台或遇到严重错误时
func applicationDidEnterBackground() {
    // 清空所有弹窗，避免后台状态下的问题
    MRKSwiftAlertView.dismissAll()
}

func handleCriticalError() {
    // 清空队列，显示关键错误信息
    MRKSwiftAlertView.dismissAll()
    
    let criticalAlert = createCriticalErrorAlert()
    criticalAlert.show()
}
```

### 3. 队列状态监控

```swift
class AlertMonitor {
    
    func logQueueStatus() {
        let current = MRKSwiftAlertView.currentAlertView()
        let queueCount = MRKSwiftAlertView.queueCount()
        let isAnimating = MRKSwiftAlertView.isAnimating()
        
        print("""
        弹窗状态:
        - 当前显示: \(current != nil ? "是" : "否")
        - 队列长度: \(queueCount)
        - 正在动画: \(isAnimating ? "是" : "否")
        """)
    }
    
    // 定期检查队列状态
    func startMonitoring() {
        Timer.scheduledTimer(withTimeInterval: 5.0, repeats: true) { _ in
            self.logQueueStatus()
        }
    }
}
```

### 4. 内存管理

```swift
// 确保弹窗对象不会造成内存泄漏
class ViewController: UIViewController {
    
    // ❌ 不要强引用弹窗对象
    // var currentAlert: MRKSwiftAlertView?
    
    func showAlert() {
        // ✅ 推荐：创建后直接显示，让队列管理生命周期
        let alert = CustomAlertView(title: "提示", message: "消息")
        alert.show()
        
        // 弹窗会在显示完成后自动从队列中移除
    }
}
```

## 注意事项

1. **避免循环引用**：弹窗的回调闭包中要使用 `[weak self]`
2. **合理的显示时机**：避免在视图控制器转场过程中显示弹窗
3. **队列长度控制**：如果队列过长，考虑合并或简化提示信息
4. **测试覆盖**：确保队列管理在各种场景下都能正常工作

## 总结

通过完善的队列管理系统，`MRKSwiftAlertView` 能够：

- ✅ 自动管理多个弹窗的显示顺序
- ✅ 避免弹窗重叠和界面混乱
- ✅ 提供灵活的队列控制方法
- ✅ 支持优先级显示和紧急处理
- ✅ 完整的状态查询和监控功能

这使得在复杂的应用场景中，开发者可以专注于业务逻辑，而不用担心弹窗管理的复杂性。
