//
//  MRKAlertView+ObjC.h
//  Student_IOS
//
//  Created by merit on 2025/8/7.
//  Objective-C 桥接头文件
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

// 前向声明
@class MRKSwiftAlertView;
@class MRKAlertViewController;

// 背景效果类型
typedef NS_ENUM(NSInteger, MRKAlertViewBackgroundStyle) {
    MRKAlertViewBackgroundStyleSolid = 0,
    MRKAlertViewBackgroundStyleGradient,
    MRKAlertViewBackgroundStyleGradientHeavy,
    MRKAlertViewBackgroundStyleClear,
};

// 动画效果类型
typedef NS_ENUM(NSInteger, MRKAlertViewTransitionStyle) {
    MRKAlertViewTransitionStyleSlideFromBottom = 0,
    MRKAlertViewTransitionStyleFade,
    MRKAlertViewTransitionStyleBounce,
    MRKAlertViewTransitionStyleDropDown,
    MRKAlertViewTransitionStyleSlideFromTop,
};

// 展示模式
typedef NS_ENUM(NSInteger, MRKAlertViewPresentationMode) {
    MRKAlertViewPresentationModeWindow = 0,
    MRKAlertViewPresentationModeEmbedded,
};

// 代理协议
@protocol MRKActionAlertViewDelegate <NSObject>
@optional
- (void)actionAlertViewWillShow;
- (void)actionAlertViewDidShow;
- (void)actionAlertViewWillDismiss;
- (void)actionAlertViewDidDismiss;
- (void)actionAlertViewDidSelectBackGroundView;
@end

// 主类接口（用于 Objective-C）
@interface MRKSwiftAlertView : UIView

// 属性
@property (nonatomic, weak, nullable) id<MRKActionAlertViewDelegate> delegate;
@property (nonatomic, assign) MRKAlertViewBackgroundStyle backgroundStyle;
@property (nonatomic, assign) MRKAlertViewTransitionStyle transitionStyle;
@property (nonatomic, assign, readonly, getter=isVisible) BOOL visible;
@property (nonatomic, strong, readonly) UIView *containerView;
@property (nonatomic, assign) BOOL isAutoHidden;
@property (nonatomic, assign) float opaqueness;
@property (nonatomic, assign) MRKAlertViewPresentationMode presentationMode;
@property (nonatomic, weak, nullable) UIView *parentView;
@property (nonatomic, assign) BOOL shouldAutorotate;

// 初始化方法
+ (instancetype)actionAlertViewWithAnimationStyle:(MRKAlertViewTransitionStyle)style;
- (instancetype)initWithAnimationStyle:(MRKAlertViewTransitionStyle)style;

// 显示和隐藏方法
- (void)show;
- (void)showInView:(UIView *)parentView;
- (void)showWithMode:(MRKAlertViewPresentationMode)mode inView:(nullable UIView *)parentView;
- (void)dismissAnimated:(BOOL)animated;
- (void)dismissAnimated:(BOOL)animated complete:(nullable void(^)(void))block;

// 子类需要重写的方法
- (void)layoutContainerView;
- (void)setupContainerViewAttributes;
- (void)setupContainerSubViews;
- (void)layoutContainerViewSubViews;

// 生命周期方法
- (void)beforeSuperViewDidLoad;
- (void)viewDidLoad;
- (void)resetTransition;
- (void)invalidateLayout;
- (void)reloadBackGroundLayoutWithSize:(CGSize)size;

// 静态方法
+ (nullable MRKSwiftAlertView *)currentAlertView;
+ (BOOL)isAnimating;

@end

// 控制器类接口
@interface MRKAlertViewController : UIViewController
@property (nonatomic, weak, nullable) MRKSwiftAlertView *alertView;
@end

NS_ASSUME_NONNULL_END
