//
//  MRKAlertView.m
//  Student_IOS
//
//  Created by merit on 2021/8/11.
//

#import "MRKAlertView.h"
#import "UIWindow+SIUtils.h"

@class MRKActionAlertViewBackgroundWindow;

const UIWindowLevel windowLevelEvaluation          = 900;  // don't overlap system's alert
const UIWindowLevel windwLevelEvaluationBackground = 899;  // below the alert window


static NSMutableArray *__si_queue;
static BOOL __si_animating;
static MRKActionAlertViewBackgroundWindow *__si_background_window;
static MRKAlertView *__si_current_view;


@interface MRKActionAlertViewBackgroundWindow : UIWindow
@property (nonatomic, assign) MRKActionAlertViewBackgroundStyle style;
@property (nonatomic, assign) float opaquess;
@end


@implementation MRKActionAlertViewBackgroundWindow

- (id)initWithFrame:(CGRect)frame
           andStyle:(MRKActionAlertViewBackgroundStyle)style
             opaque:(float)opaques{
    
    self = [super initWithFrame:frame];
    if (self) {
        self.style = style;
        self.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
        self.opaque = NO;
        
        self.opaquess = opaques;
        self.windowLevel = windwLevelEvaluationBackground;
        self.rootViewController = ({
            UIViewController *vc = [[UIViewController alloc] init];
            vc;
        });
    }
    return self;
}

- (void)drawRect:(CGRect)rect {
    
    CGContextRef context = UIGraphicsGetCurrentContext();
    switch (self.style) {
        case MRKActionAlertViewBackgroundStyleGradient:{
            [[UIColor colorWithWhite:0 alpha:0.3] set];

            CGContextFillRect(context, self.bounds);
            size_t locationsCount = 2;
            CGFloat locations[2] = {0.0f, 1.0f};
            CGFloat colors[8] = {0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.75f};
            CGColorSpaceRef colorSpace = CGColorSpaceCreateDeviceRGB();
            CGGradientRef gradient = CGGradientCreateWithColorComponents(colorSpace, colors, locations, locationsCount);
            CGColorSpaceRelease(colorSpace);
            CGPoint center = CGPointMake(self.bounds.size.width / 2, self.bounds.size.height / 2);
            CGFloat radius = MIN(self.bounds.size.width, self.bounds.size.height) ;
            CGContextDrawRadialGradient (context, gradient, center, 0, center, radius, kCGGradientDrawsAfterEndLocation);
            CGGradientRelease(gradient);
        } break;
            
        case MRKActionAlertViewBackgroundStyleGradientHeavy:{
            [[UIColor colorWithWhite:0 alpha:0.5] set];
            
            CGContextFillRect(context, self.bounds);
            size_t locationsCount = 2;
            CGFloat locations[2] = {0.0f, 1.0f};
            CGFloat colors[8] = {0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.75f};
            CGColorSpaceRef colorSpace = CGColorSpaceCreateDeviceRGB();
            CGGradientRef gradient = CGGradientCreateWithColorComponents(colorSpace, colors, locations, locationsCount);
            CGColorSpaceRelease(colorSpace);
            CGPoint center = CGPointMake(self.bounds.size.width / 2, self.bounds.size.height / 2);
            CGFloat radius = MIN(self.bounds.size.width, self.bounds.size.height) ;
            CGContextDrawRadialGradient (context, gradient, center, 0, center, radius, kCGGradientDrawsAfterEndLocation);
            CGGradientRelease(gradient);
        } break;
            
        case MRKActionAlertViewBackgroundStyleSolid:{
            NSLog(@"MRKActionAlertViewBackgroundWindow.self.bounds === %@",NSStringFromCGRect(self.bounds));
            if (self.opaquess != 0) {
                [[UIColor colorWithWhite:0 alpha:self.opaquess] set];
            }else {
                [[UIColor colorWithWhite:0 alpha:0.4] set];
            }
            CGContextFillRect(context, self.bounds);
        } break;
            
        case MRKActionAlertViewBackgroundStyleClear:{
            [[UIColor clearColor] set];
            CGContextFillRect(context, self.bounds);
        } break;
            
        default:
            break;
    }
}

@end




#pragma mark -  MRKActionAlertViewController 声明一个controller来做actionview的容器,并加载到window上
@interface MRKActionAlertViewController()
@property (nonatomic, strong) MRKAlertView *alertView;
@end

@implementation MRKActionAlertViewController
#pragma mark View life cycle
- (void)loadView {
    self.view = self.alertView;
}

- (void)viewDidLoad {
    [self.alertView beforeSuperViewDidLoad];
    [super viewDidLoad];

    [self.alertView viewDidLoad];
}


- (BOOL)shouldAutorotate {
    if ([UIDevice currentDevice].isPad){
        return YES;
    }
    return NO;
}

/// 当前控制器支持的屏幕方向
- (UIInterfaceOrientationMask)supportedInterfaceOrientations {
    if ([UIDevice currentDevice].isPad){
        return UIInterfaceOrientationMaskAll;
    }
    return UIInterfaceOrientationMaskPortrait;
}

/// 优先的屏幕方向 - 只会在 presentViewController:animated:completion时被调用
- (UIInterfaceOrientation)preferredInterfaceOrientationForPresentation {
    return UIInterfaceOrientationPortrait;
}

- (void)viewWillTransitionToSize:(CGSize)size withTransitionCoordinator:(id<UIViewControllerTransitionCoordinator>)coordinator {
    [super viewWillTransitionToSize:size withTransitionCoordinator:coordinator];
    NSLog(@"viewWillTransitionToSize ======== %@", NSStringFromCGSize(size));
}


//
//- (BOOL)shouldAutorotate {
//    UIViewController *controller = [self.alertView.oldKeyWindow currentViewController];
//    if (controller) {
//        BOOL shouldAutorotate = [controller shouldAutorotate];
//        return shouldAutorotate;
//    }
//    return YES;
//}
//
//- (UIInterfaceOrientationMask)supportedInterfaceOrientations {
//    UIViewController *controller = [self.alertView.oldKeyWindow currentViewController];
//    if (controller) {
//        UIInterfaceOrientationMask mask = [controller supportedInterfaceOrientations];
//        return mask;
//    }
//    return UIInterfaceOrientationMaskAll;
//}
//
//- (UIInterfaceOrientation)preferredInterfaceOrientationForPresentation {
//    return UIInterfaceOrientationPortrait;
//}
//
//- (void)viewWillTransitionToSize:(CGSize)size withTransitionCoordinator:(id<UIViewControllerTransitionCoordinator>)coordinator{
//    [super viewWillTransitionToSize:size withTransitionCoordinator:coordinator];
//    NSLog(@"%f  %f", size.width, size.height);
//    NSLog(@"willRotateToInterfaceOrientation");
//    
//    [self.alertView resetTransition];
//    [self.alertView invalidateLayout];
//    [self.alertView reloadBackGroundLayout:size];
//}
@end


#pragma mark - MRKAlertView

@interface MRKAlertView()<CAAnimationDelegate>
@property (nonatomic, strong) UIWindow *alertWindow;
@property (nonatomic, assign, getter = isLayoutDirty) BOOL layoutDirty;
+ (NSMutableArray *)sharedQueue;
@end


@implementation MRKAlertView

+ (instancetype)actionAlertViewWithAnimationStyle:(MRKActionAlertViewTransitionStyle)style{
    return [[self alloc] initWithAnimationStyle:style];
}

- (instancetype)initWithAnimationStyle:(MRKActionAlertViewTransitionStyle)style{
    if (self = [super init]) {
        self.transitionStyle = style;
        _isAutoHidden = NO;
    }
    return self;
}

+ (void)initialize{
    if (self != [MRKAlertView class])
        return;
}

- (void)beforeSuperViewDidLoad{
    
}


#pragma mark Class methods

+ (NSMutableArray *)sharedQueue{
    if (!__si_queue) {
        __si_queue = [NSMutableArray array];
    }
    return __si_queue;
}

+ (MRKAlertView *)currentAlertView{
    return __si_current_view;
}

+ (void)setCurrentAlertView:(MRKAlertView *)alertView{
    __si_current_view = alertView;
}

+ (BOOL)isAnimating{
    return __si_animating;
}

+ (void)setAnimating:(BOOL)animating{
    __si_animating = animating;
}

+ (void)showBackground{
    if (!__si_background_window) {
        CGRect frame = [[UIScreen mainScreen] bounds];
        
        NSLog(@"MRKActionAlertViewBackgroundWindow.frame === %@",NSStringFromCGRect(frame));
        if ([[UIScreen mainScreen] respondsToSelector:@selector(fixedCoordinateSpace)]) {
            frame = [[[UIScreen mainScreen] fixedCoordinateSpace] convertRect:frame fromCoordinateSpace:[[UIScreen mainScreen] coordinateSpace]];
        }

        NSLog(@"MRKActionAlertViewBackgroundWindow.fixedCoordinateSpace.frame === %@",NSStringFromCGRect(frame));
        __si_background_window = [[MRKActionAlertViewBackgroundWindow alloc] initWithFrame:frame
                                                                                  andStyle:[MRKAlertView currentAlertView].backgroundStyle
                                                                                    opaque:[MRKAlertView currentAlertView].opaquess];
        [__si_background_window makeKeyAndVisible];
        __si_background_window.alpha = 0;
        [UIView animateWithDuration:0.3 animations:^{
            __si_background_window.alpha = 1;
        }];
    }
}

+ (void)hideBackgroundAnimated:(BOOL)animated{
    if (!animated) {
        [__si_background_window removeFromSuperview];
        __si_background_window = nil;
        return;
    }
    
    [UIView animateWithDuration:0.3
                     animations:^{
        __si_background_window.alpha = 0;
    } completion:^(BOOL finished) {
        [__si_background_window removeFromSuperview];
        __si_background_window = nil;
    }];
}

#pragma mark  Public

- (void)showInView:(UIView *)view{
    
}



- (void)show {
    NSLog(@"MRKAlertView======== show");
    if (self.isVisible) {
        return;
    }
    
    self.oldKeyWindow = [[UIApplication sharedApplication] keyWindow];
    if (![[MRKAlertView sharedQueue] containsObject:self]) {
        [[MRKAlertView sharedQueue] addObject:self];
    }
    
    if ([MRKAlertView isAnimating]) {
        return;
    }
    
    if ([MRKAlertView currentAlertView].isVisible) {
        MRKAlertView *alert = [MRKAlertView currentAlertView];
        [alert dismissAnimated:YES cleanup:NO complete:nil];
        return;
    }
    
    self.visible = YES;
    [MRKAlertView setAnimating:YES];
    [MRKAlertView setCurrentAlertView:self];
    [MRKAlertView showBackground];
    
    if (!self.alertWindow) {
        UIWindow *window = [[UIWindow alloc] initWithFrame:[UIScreen mainScreen].bounds];
        window.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
        window.opaque = NO;
        window.windowLevel = windowLevelEvaluation;
        window.rootViewController = ({
            MRKActionAlertViewController *vc = [[MRKActionAlertViewController alloc] init];
            vc.alertView = self;
            self.alertViewController = vc;
            vc;
        });
        self.alertWindow = window;
    }
    [self.alertWindow makeKeyAndVisible];
    
    if (self.delegate && [self.delegate respondsToSelector:@selector(actionAlertViewWillShow)]) {
        [self.delegate actionAlertViewWillShow];
    }
    
    [self validateLayout];
    [self transitionInCompletion:^{
        if (self.delegate && [self.delegate respondsToSelector:@selector(actionAlertViewDidShow)]) {
            [self.delegate actionAlertViewDidShow];
        }
        
        [MRKAlertView setAnimating:NO];
        NSInteger index = [[MRKAlertView sharedQueue] indexOfObject:self];
        if (index < [MRKAlertView sharedQueue].count - 1) {
            [self dismissAnimated:YES cleanup:NO complete:nil]; // dismiss to show next alert view
        }
    }];
}

- (void)dismissAnimated:(BOOL)animated{
    [self endEditing:YES];
    [self dismissAnimated:animated cleanup:YES complete:nil];
}

- (void)dismissAnimated:(BOOL)animated complete:(void(^)(void))block{
    [self endEditing:YES];
    [self dismissAnimated:animated cleanup:YES complete:block];
}

- (void)dismissAnimated:(BOOL)animated cleanup:(BOOL)cleanup complete:(void(^)(void))block{
    BOOL isVisible = self.isVisible;
    
    if (isVisible) {
        if (self.delegate && [self.delegate respondsToSelector:@selector(actionAlertViewWillDismiss)]) {
            [self.delegate actionAlertViewWillDismiss];
        }
    }
    
    void (^dismissComplete)(void) = ^{
        self.visible = NO;
        
        [self teardown];
        
        [MRKAlertView setCurrentAlertView:nil];
        
        MRKAlertView *nextAlertView;
        NSInteger index = [[MRKAlertView sharedQueue] indexOfObject:self];
        if (index != NSNotFound && index < [MRKAlertView sharedQueue].count - 1) {
            nextAlertView = [MRKAlertView sharedQueue][index + 1];
        }
        
        if (cleanup) {
            [[MRKAlertView sharedQueue] removeObject:self];
        }
        
        [MRKAlertView setAnimating:NO];
        
        if (isVisible) {
            if (self.delegate && [self.delegate respondsToSelector:@selector(actionAlertViewDidDismiss)]) {
                [self.delegate actionAlertViewDidDismiss];
            }
            
            if (block) {
                block();
            }
        }
        
        // check if we should show next alert
        if (!isVisible) {
            return;
        }
        
        if (nextAlertView) {
            [nextAlertView show];
        } else {
            // show last alert view
            if ([MRKAlertView sharedQueue].count > 0) {
                MRKAlertView *alert = [[MRKAlertView sharedQueue] lastObject];
                [alert show];
            }
        }
    };
    
    if (animated && isVisible) {
        [MRKAlertView setAnimating:YES];
        [self transitionOutCompletion:dismissComplete];
        
        if ([MRKAlertView sharedQueue].count == 1) {
            [MRKAlertView hideBackgroundAnimated:YES];
        }
    } else {
        dismissComplete();
        
        if ([MRKAlertView sharedQueue].count == 0) {
            [MRKAlertView hideBackgroundAnimated:YES];
        }
    }
    
    UIWindow *window = self.oldKeyWindow;
    if (!window) {
        window = [UIApplication sharedApplication].windows[0];
    }
    [window makeKeyWindow];
    window.hidden = NO;
}





#pragma mark Transitions

- (void)transitionInCompletion:(void(^)(void))completion {
    
    switch (self.transitionStyle) {
        case MRKActionAlertViewTransitionStyleSlideFromBottom:{
            CGRect rect = self.containerView.frame;
            CGRect originalRect = rect;
            
            rect.origin.y = self.bounds.size.height;
            self.containerView.frame = rect;
            [UIView animateWithDuration:0.3
                             animations:^{
                self.containerView.frame = originalRect;
            } completion:^(BOOL finished) {
                if (completion) {
                    completion();
                }
            }];
            
        } break;
            
        case MRKActionAlertViewTransitionStyleSlideFromTop:{
            CGRect rect = self.containerView.frame;
            CGRect originalRect = rect;
            
            rect.origin.y = -rect.size.height;
            self.containerView.frame = rect;
            [UIView animateWithDuration:0.3
                             animations:^{
                self.containerView.frame = originalRect;
            } completion:^(BOOL finished) {
                if (completion) {
                    completion();
                }
            }];
        } break;
            
        case MRKActionAlertViewTransitionStyleFade:{
            self.containerView.alpha = 0;
            [UIView animateWithDuration:0.3
                             animations:^{
                self.containerView.alpha = 1;
            } completion:^(BOOL finished) {
                if (completion) {
                    completion();
                }
            }];
        } break;
            
        case MRKActionAlertViewTransitionStyleBounce:{
            
            CAKeyframeAnimation *animation = [CAKeyframeAnimation animationWithKeyPath:@"transform.scale"];
            animation.values = @[@(0.01), @(1.2), @(0.9), @(1)];
            animation.keyTimes = @[@(0), @(0.4), @(0.6), @(1)];
            animation.timingFunctions = @[[CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionLinear],
                                          [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionLinear],
                                          [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionEaseOut]];
            animation.duration = 0.5;
            animation.delegate = self;
            [animation setValue:completion forKey:@"handler"];
            [self.containerView.layer addAnimation:animation forKey:@"bouce"];
        } break;
            
        case MRKActionAlertViewTransitionStyleDropDown:{
            
            CGFloat y = self.containerView.center.y;
            CAKeyframeAnimation *animation = [CAKeyframeAnimation animationWithKeyPath:@"position.y"];
            animation.values = @[@(y - self.bounds.size.height), @(y + 20), @(y - 10), @(y)];
            animation.keyTimes = @[@(0), @(0.5), @(0.75), @(1)];
            animation.timingFunctions = @[[CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionEaseOut],
                                          [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionLinear],
                                          [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionEaseOut]];
            animation.duration = 0.4;
            animation.delegate = self;
            [animation setValue:completion forKey:@"handler"];
            [self.containerView.layer addAnimation:animation forKey:@"dropdown"];
        } break;
            
        default:  break;
    }
}

- (void)transitionOutCompletion:(void(^)(void))completion {
    switch (self.transitionStyle) {
        case MRKActionAlertViewTransitionStyleSlideFromBottom:{
            CGRect rect = self.containerView.frame;
            rect.origin.y = self.bounds.size.height;
            [UIView animateWithDuration:0.3
                                  delay:0
                                options:UIViewAnimationOptionCurveEaseIn
                             animations:^{
                self.containerView.frame = rect;
            } completion:^(BOOL finished) {
                if (completion) {
                    completion();
                }
            }];
        }
            break;
        case MRKActionAlertViewTransitionStyleSlideFromTop:{
            CGRect rect = self.containerView.frame;
            rect.origin.y = -rect.size.height;
            [UIView animateWithDuration:0.3
                                  delay:0
                                options:UIViewAnimationOptionCurveEaseIn
                             animations:^{
                self.containerView.frame = rect;
            } completion:^(BOOL finished) {
                if (completion) {
                    completion();
                }
            }];
        }
            break;
        case MRKActionAlertViewTransitionStyleFade:{
            [UIView animateWithDuration:0.25
                             animations:^{
                self.containerView.alpha = 0;
            } completion:^(BOOL finished) {
                if (completion) {
                    completion();
                }
            }];
        }
            break;
        case MRKActionAlertViewTransitionStyleBounce:{
            CAKeyframeAnimation *animation = [CAKeyframeAnimation animationWithKeyPath:@"transform.scale"];
            animation.values = @[@(1), @(1.2), @(0.01)];
            animation.keyTimes = @[@(0), @(0.4), @(1)];
            animation.timingFunctions = @[[CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionEaseInEaseOut],
                                          [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionEaseOut]];
            animation.duration = 0.35;
            animation.delegate = self;
            [animation setValue:completion forKey:@"handler"];
            [self.containerView.layer addAnimation:animation forKey:@"bounce"];
            self.containerView.transform = CGAffineTransformMakeScale(0.01, 0.01);
        }
            break;
        case MRKActionAlertViewTransitionStyleDropDown:{
            CGPoint point = self.containerView.center;
            point.y += self.bounds.size.height;
            [UIView animateWithDuration:0.3
                                  delay:0
                                options:UIViewAnimationOptionCurveEaseIn
                             animations:^{
                self.containerView.center = point;
                CGFloat angle = ((CGFloat)arc4random_uniform(100) - 50.f) / 100.f;
                self.containerView.transform = CGAffineTransformMakeRotation(angle);
            } completion:^(BOOL finished) {
                if (completion) {
                    completion();
                }
            }];
        }
            break;
        default:
            break;
    }
}

#pragma mark CAAnimation delegate
- (void)animationDidStop:(CAAnimation *)anim finished:(BOOL)flag {
    void(^completion)(void) = [anim valueForKey:@"handler"];
    if (completion) {
        completion();
    }
}




- (void)resetTransition {
    [self.containerView.layer removeAllAnimations];
}

#pragma mark Layout

- (void)layoutSubviews {
    [super layoutSubviews];
    
    [self validateLayout];
}

- (void)invalidateLayout {
    self.layoutDirty = YES;
    [self setNeedsLayout];
}

- (void)reloadBackGroundLayout:(CGSize)size{
    if (__si_background_window != nil){
        __si_background_window.size = size;
        [__si_background_window setNeedsDisplay];
    }
}

- (void)validateLayout {
    if (!self.isLayoutDirty) {
        return;
    }
    
    self.layoutDirty = NO;
    self.containerView.transform = CGAffineTransformIdentity;
    [self layoutContainerView];
    
    self.containerView.layer.shadowPath = [UIBezierPath bezierPathWithRoundedRect:self.containerView.bounds
                                                                     cornerRadius:self.containerView.layer.cornerRadius].CGPath;
    [self layoutContainerViewSubViews];
}

- (void)teardown {
    [self.containerView removeFromSuperview];
    self.containerView = nil;
    
    [self.alertWindow removeFromSuperview];
    self.alertWindow = nil;
    self.layoutDirty = NO;
}



#pragma mark Setup
- (void)viewDidLoad {
    [self setupContainerView];
    [self setupContainerSubViews];
    [self invalidateLayout];
}

- (void)setupContainerView {
    self.containerView = [[UIView alloc] initWithFrame:self.bounds];
    self.containerView.backgroundColor = [UIColor whiteColor];
    self.containerView.clipsToBounds = YES;
    [self addSubview:self.containerView];
    
    [self setupContainerViewAttributes];
}

- (void)setupContainerViewAttributes {
    //给容器视图加属性
    
}

- (void)setupContainerSubViews {
    //设置容器视图
    
}

- (void)layoutContainerViewSubViews {
    //布局容器子视图
    
}


- (void)layoutContainerView {
    //布局容器视图
    self.containerView.frame = CGRectMake(0, 0, 0, 0);
}

- (void)touchesEnded:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
    UITouch *touch = [touches.allObjects lastObject];
    BOOL result = [touch.view isDescendantOfView:self.containerView];
    if (!result) {
        if (_isAutoHidden) {
            [self dismissAnimated:YES];
        }
        
        if(self.delegate && [self.delegate respondsToSelector:@selector(actionAlertViewDidSelectBackGroundView)]){
            [self.delegate actionAlertViewDidSelectBackGroundView];
        }
    }
}

#pragma mark Lazy
- (void)dealloc {
    NSLog(@"😊😊😊-----dealloc-----😊😊😊%@" , NSStringFromClass([self class]));
}

@end


