/*
 * @Author: Do not edit
 * @Date: 2025-08-04 13:54:15
 * @LastEditors: li<PERSON><PERSON>
 * @LastEditTime: 2025-08-07 16:06:15
 * @FilePath: /Merit_iOS/Student_IOS/Common/Alert/MRKAlertView/MRKAlertView.h
 * @Description: 
 * Copyright (c) 2025 by MERIT, All Rights Reserved.
 */
//
//  MRKAlertView.h
//  Student_IOS
//
//  Created by merit on 2021/8/11.
//

#import <UIKit/UIKit.h>

@class MRKAlertView;

//背景效果 0是渐变 1是不渐变
typedef NS_ENUM(NSInteger, MRKActionAlertViewBackgroundStyle) {
    MRKActionAlertViewBackgroundStyleSolid = 0,
    MRKActionAlertViewBackgroundStyleGradient,
    MRKActionAlertViewBackgroundStyleGradientHeavy,
    MRKActionAlertViewBackgroundStyleClear, ///透明
};

//动画效果
typedef NS_ENUM(NSInteger, MRKActionAlertViewTransitionStyle) {
    MRKActionAlertViewTransitionStyleSlideFromBottom = 0,
    MRKActionAlertViewTransitionStyleFade,
    MRKActionAlertViewTransitionStyleBounce,
    MRKActionAlertViewTransitionStyleDropDown,
    MRKActionAlertViewTransitionStyleSlideFromTop,
};


@protocol MRKActionAlertViewDelegate <NSObject>
@optional
- (void)actionAlertViewWillShow;                ///< 即将出现
- (void)actionAlertViewDidShow;                 ///< 已经出现
- (void)actionAlertViewWillDismiss;             ///< 即将消失
- (void)actionAlertViewDidDismiss;              ///< 已经消失
- (void)actionAlertViewDidSelectBackGroundView; ///< 点击背景
@end

@class MRKActionAlertViewController;
@interface MRKAlertView : UIView
@property (nonatomic, weak) id<MRKActionAlertViewDelegate> delegate;
@property (nonatomic, assign) MRKActionAlertViewBackgroundStyle backgroundStyle;   ///< 背景效果
@property (nonatomic, assign) MRKActionAlertViewTransitionStyle transitionStyle;
@property (nonatomic, assign, getter = isVisible) BOOL visible;                    ///< 是否正在显示
@property (nonatomic, strong) UIView *containerView;                               ///< 容器视图
@property (nonatomic, weak) UIWindow *oldKeyWindow;
@property (nonatomic, assign) BOOL isAutoHidden;                                   ///< 是否点击背景隐藏
@property (nonatomic, assign) float opaquess;                                      ///< 不透明
@property (nonatomic, weak) MRKActionAlertViewController *alertViewController;     /// 当前页面
/**
 初始化方法,传入一个动画类型
 @param style 动画类型
 @return 初始化的对象
 */
+ (instancetype)actionAlertViewWithAnimationStyle:(MRKActionAlertViewTransitionStyle)style;
- (instancetype)initWithAnimationStyle:(MRKActionAlertViewTransitionStyle)style;

/**
 展示和消失
 */
- (void)dismissAnimated:(BOOL)animated;
- (void)dismissAnimated:(BOOL)animated complete:(void(^)(void))block;
- (void)show;

///
- (void)showInView:(UIView *)view;

/**
 继承者需要实现的
 */
- (void)layoutContainerView;               ///< 布局containerview的位置,就是那个看得到的视图
- (void)setupContainerViewAttributes;      ///< 设置containerview的属性,比如切边啥的
- (void)setupContainerSubViews;            ///< 给containerview添加子视图
- (void)layoutContainerViewSubViews;       ///< 设置子视图的frame

/**
 给控制器调用的, 不用管
 */
- (void)beforeSuperViewDidLoad;
- (void)viewDidLoad;
- (void)resetTransition;
- (void)invalidateLayout;
- (void)reloadBackGroundLayout:(CGSize)size;
@end

@interface MRKActionAlertViewController : UIViewController
@end
