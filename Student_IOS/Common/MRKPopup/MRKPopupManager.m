//
//  MRKPopupManager.m
//  Student_IOS
//
//  Created by merit on 2021/11/13.
//

#import "MRKPopupManager.h"
#import "MRKAlertView.h"
#import "MRKHrCtrlPopView.h"
#import "MRKAlertController.h"
#import "MRKMainPageController.h"
#import "MRKHomeGuideViewController.h"
#import "MrkGeneralAlertView.h"

#define kDismissAlertPageTime (10 * 60)

@interface MRKPopupManager()
@property (nonatomic) id showingAlert; //当前正在显示弹窗
@property (nonatomic, strong) NSMutableArray *alertArray;  //未弹出的弹框
@property (nonatomic, assign) BOOL isAlert;                //弹框是否正在弹出
@end

@implementation MRKPopupManager

///单例
static MRKPopupManager *_popManager = nil;
+ (instancetype)sharedInstance {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        _popManager = [[MRKPopupManager alloc] init];
    });
    return _popManager;
}

///弹出弹窗
- (void)showAlertView:(id)popupView
                level:(MRKPopupViewLevel)popupLevel
             callback:(void(^ __nullable)(void))callback {
    
    NSLog(@"showAlertView level======== %@", popupView);
    
    if (_isAlert) {
        //如果正在显示弹框，就把弹框先添加进数组
        MRKPopupViewModel *alertModel = [[MRKPopupViewModel alloc] init];
        alertModel.popupView = popupView;
        alertModel.popupLevel = popupLevel;
        alertModel.callBack = callback;
        [self.alertArray addObject:alertModel];
        
        //根据数组中弹窗的优先级排序
        // 排序key, 某个对象的属性名称，是否升序, YES-升序, NO-降序
        NSSortDescriptor *tempDes = [NSSortDescriptor sortDescriptorWithKey:@"popupLevel" ascending:YES];
        // 排序结果
        self.alertArray = [NSMutableArray arrayWithArray:[self.alertArray sortedArrayUsingDescriptors:@[tempDes]]];
        return;
    }
    
    //无弹窗，弹出弹窗
    [self addAlertViewToRootView:popupView callback:callback];
}


///消失弹窗
- (void)dismissAlertView:(id)popupView callback:(void(^ __nullable)(void))callback {
    
    NSLog(@"dismissAlertView ======== %@", popupView);
    self.showingAlert = nil;
    
    //如果是自定义弹窗，自己消失，在callback函数中
    if (![popupView isKindOfClass:[MRKAlertView class]] &&
        ![popupView isKindOfClass:[MRKAlertController class]]
        && ![popupView isKindOfClass:[MRKHomeGuideViewController class]]
        ) {
        
        //把弹窗从父试图删除
        [popupView removeFromSuperview];
    }
    
    if (callback) {
        callback();
    }
                                                                                                            
    _isAlert = NO;
    //判断消失的弹窗存不存在弹窗数组中，如果存在就删除
    NSMutableArray *tmpArr = [[NSMutableArray alloc] init];
    for (MRKPopupViewModel *tmpAlertModel in self.alertArray) {
        if (![tmpAlertModel.popupView isEqual:popupView]) {
            [tmpArr addObject:tmpAlertModel];
        }
    }
    self.alertArray = tmpArr;
     
    //然后在弹出弹框数组中的第一个弹窗
    if(self.alertArray.count)
    {
        MRKPopupViewModel *alertModel = self.alertArray.firstObject;
        [self showAlertView:alertModel.popupView level:alertModel.popupLevel callback:alertModel.callBack];
    } else {
        NSLog(@"all_alert_close");
        ///所有弹窗都关闭了 22-12-08 zqp
        if(self.closeAllAlert) {
            self.closeAllAlert(@1);
        }
    }
}



//用于做弹窗弹出的具体操作
- (void)addAlertViewToRootView:(id)alertView callback:(void(^)(void))callback {
    self.showingAlert = alertView;
    //判断弹窗类型，根据不同类型的弹窗，做弹出
    if ([alertView isKindOfClass:[MRKAlertView class]]) {
        _isAlert = YES;
        dispatch_async(dispatch_get_main_queue(), ^{
            MRKAlertView *vc = (MRKAlertView *)alertView;
            [vc show];
        });
        NSLog(@"addAlertViewToRootView ====== %@", alertView);
    }else if ([alertView isKindOfClass:[MRKHrCtrlPopView class]]) {
        _isAlert = YES;
        dispatch_async(dispatch_get_main_queue(), ^{
            MRKHrCtrlPopView *popView = (MRKHrCtrlPopView *)alertView;
            UIViewController *current = [UIViewController currentViewController];
            if ([current isKindOfClass:[MRKMainPageController class]]) {
                [popView showInKeyWindow];
            }
        });
    }else if ([alertView isKindOfClass:[MRKAlertController class]]) {
        _isAlert = YES;
        MRKAlertController *pageVC = (MRKAlertController *)alertView;
        dispatch_async(dispatch_get_main_queue(), ^{
//            UIViewController *vc = [UIApplication sharedApplication].delegate.window.rootViewController;
//            [vc.view addSubview:pageVC.view];
//            [vc.view bringSubviewToFront:pageVC.view];
//            [vc addChildViewController:pageVC];
            UITabBarController *tabbar = [UIViewController currentTabBarController];
            if (tabbar) {
                [tabbar.view addSubview:pageVC.view];
            }
        });
    }
    else if ([alertView isKindOfClass:[MRKHomeGuideViewController class]]) {
        _isAlert = YES;
        MRKHomeGuideViewController *pageVC = (MRKHomeGuideViewController *)alertView;
        dispatch_async(dispatch_get_main_queue(), ^{
            UITabBarController *tabbar = [UIViewController currentTabBarController];
            if (tabbar) {
                [tabbar.view addSubview:pageVC.view];
            }
        });
    } else if ([alertView isKindOfClass:[MrkGeneralAlertView class]]) {
        _isAlert = YES;
        dispatch_async(dispatch_get_main_queue(), ^{
            MrkGeneralAlertView *vc = (MrkGeneralAlertView *)alertView;
            [vc showInKeyWindow];
        });
        NSLog(@"addAlertViewToRootView ====== %@", alertView);
    }
    
    if (callback) {
        callback();
    }
}

#pragma mark 懒加载
- (NSMutableArray *)alertArray {
    if (!_alertArray) {
        _alertArray = [[NSMutableArray alloc] init];
    }
    return _alertArray;
}

@end


#pragma mark MRKPopupViewModel
@implementation MRKPopupViewModel

@end

