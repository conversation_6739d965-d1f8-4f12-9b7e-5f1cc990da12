//
//  MRKCustomShareContentView.m
//  Student_IOS
//
//  Created by merit on 2023/10/7.
//

#import "MRKCustomShareContentView.h"
#import "WKCircleProgress.h"
#import "UIView+AZGradient.h"


/// 分享用户信息
@implementation MRKShareUserView
- (UIImageView *)avatarImgV {
    if (!_avatarImgV) {
        _avatarImgV = [[UIImageView alloc] init];
        _avatarImgV.cornerRadius = WKDHPX(19);
    }
    return _avatarImgV;
}
- (UIImageView *)vipImgV {
    if (!_vipImgV) {
        _vipImgV = [[UIImageView alloc] init];
    }
    return _vipImgV;
}
- (UILabel *)nameLabel {
    if (!_nameLabel) {
        UILabel *label = [UILabel new];
        label.textColor = [UIColor colorWithHexString:@"#FFFFFF"];
        label.font = kMedium_Font_NoDHPX(WKDHPX(15));
        _nameLabel = label;
    }
    return _nameLabel;
}
- (UILabel *)timeLabel {
    if (!_timeLabel) {
        UILabel *label = [UILabel new];
        label.textColor = [UIColor colorWithHexString:@"#FFFFFF" alpha:.8];
        label.font = kSystem_Font_NoDHPX(WKDHPX(11));
        _timeLabel = label;
    }
    return _timeLabel;
}

- (instancetype)init {
    if (self = [super init]) {
        [self initUI];
    }
    return self;
}

- (void)initUI {
    [self addSubview:self.avatarImgV];
    [self addSubview:self.vipImgV];
    [self addSubview:self.nameLabel];
    [self addSubview:self.timeLabel];
    
    [self.avatarImgV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.bottom.mas_equalTo(0);
        make.size.mas_equalTo(CGSizeMake(WKDHPX(38), WKDHPX(38)));
    }];
    [self.nameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.avatarImgV.mas_right).offset(WKDHPX(13));
        make.top.mas_equalTo(0);
        make.width.mas_lessThanOrEqualTo(70);
    }];
    [self.vipImgV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.nameLabel.mas_right).offset(WKDHPX(4));
        make.centerY.equalTo(self.nameLabel.mas_centerY);
    }];
    [self.timeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.nameLabel.mas_left);
        make.bottom.mas_equalTo(0);
    }];
    
    self.nameLabel.text = UserInfo.nickName;
    
    NSString *url = [UserInfo.avatar imageUrlAdaptReSize:CGSizeMake(50, 50)];
    [self.avatarImgV setImageWithURL:[NSURL URLWithString:url]
                         placeholder:UserInfo.avatarHoldingImage
                             options:YYWebImageOptionProgressiveBlur|YYWebImageOptionSetImageWithFadeAnimation
                          completion:nil];
    self.vipImgV.image = UserInfo.userVipIcon;
}

@end



/// 分享 运动记录item
@implementation MRKShareSportItem
- (instancetype)init {
    if (self = [super init]) {
        [self addSubview:self.titleLabel];
        [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.left.right.mas_equalTo(0);
        }];
        
        [self addSubview:self.detailLabel];
        [self.detailLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.bottom.right.mas_equalTo(0);
            make.top.equalTo(self.titleLabel.mas_bottom).offset(WKDHPX(2));
        }];
    }
    return self;
}

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        UILabel *label = [UILabel new];
        label.textColor = [UIColor colorWithHexString:@"#FFFFFF" alpha:.8];
        label.font = kSystem_Font_NoDHPX(WKDHPX(10));
        _titleLabel = label;
    }
    return _titleLabel;
}
- (UILabel *)detailLabel {
    if (!_detailLabel) {
        UILabel *label = [UILabel new];
        label.textColor = [UIColor colorWithHexString:@"#FFFFFF"];
        label.font = BebasFont_Bold_NoDHPX(WKDHPX(20));
        _detailLabel = label;
    }
    return _detailLabel;
}
- (void)setForShare:(BOOL)forShare {
    if (forShare) {
        self.titleLabel.font = kSystem_Font_NoDHPX(WKDHPX(13));
        self.detailLabel.font = BebasFont_Bold_NoDHPX(WKDHPX(26));
    }else {
        self.titleLabel.font = kSystem_Font_NoDHPX(WKDHPX(10));
        self.detailLabel.font = BebasFont_Bold_NoDHPX(WKDHPX(20));
    }
}
@end










@interface MRKShareSportContentView ()
@property (nonatomic, strong) WKCircleProgress *progress;
@end
/// 分享运动记录
@implementation MRKShareSportContentView
- (UIImageView *)backContentImgV {
    if (!_backContentImgV) {
        _backContentImgV = [[UIImageView alloc] init];
    }
    return _backContentImgV;
}
- (UILabel *)titleLabel {
    if (!_titleLabel) {
        UILabel *label = [UILabel new];
        label.textColor = [UIColor colorWithHexString:@"#FFFFFF"];
        label.font = kSystem_Font_NoDHPX(WKDHPX(13));
        label.text = @"";
        _titleLabel = label;
    }
    return _titleLabel;
}
- (UILabel *)numLabel {
    if (!_numLabel) {
        UILabel *label = [UILabel new];
        label.textColor = [UIColor colorWithHexString:@"#FFFFFF"];
        label.font = BebasFont_Bold_NoDHPX(WKDHPX(20));
        label.text = @"";
        _numLabel = label;
    }
    return _numLabel;
}
- (UILabel *)unitLabel {
    if (!_unitLabel) {
        UILabel *label = [UILabel new];
        label.textColor = [[UIColor colorWithHexString:@"#FFFFFF"] colorWithAlphaComponent:0.6];
        label.font = kSystem_Font_NoDHPX(WKDHPX(8));
        label.text = @"消耗·千卡";
        _unitLabel = label;
    }
    return _unitLabel;
}
- (UIImageView *)kcalImgV {
    if (!_kcalImgV) {
        _kcalImgV = [[UIImageView alloc] init];
        _kcalImgV.image = [UIImage imageNamed:@"fire_kacl"];
    }
    return _kcalImgV;
}
- (WKCircleProgress *)progress {
    if (!_progress) {
        _progress = [[WKCircleProgress alloc] initWithFrame:CGRectMake(0, 0, WKDHPX(80), WKDHPX(80))];
        _progress.bgColor = [UIColor colorWithHexString:@"#FFFFFF" alpha:.2];
        _progress.lineWidth = WKDHPX(8);
        [_progress setStart:M_PI*(150)/180.0 end:M_PI*(30)/180.0 radius:(WKDHPX(80) / 2.0 - WKDHPX(8))];
        [_progress setProgressColor:@[[UIColor whiteColor], [UIColor whiteColor]]];
        [_progress setProgressRate:.6 duration:.3];
    }
    return _progress;
}
- (UIView *)dataView {
    if (!_dataView) {
        _dataView = [[UIView alloc] init];
    }
    return _dataView;
}

- (instancetype)init {
    if (self = [super init]) {
        [self initUI];
    }
    return self;
}

- (void)initUI {
    [self addSubview:self.backContentImgV];
    [self.backContentImgV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsMake(0, 0, 0, 0));
    }];
    
    [self addSubview:self.titleLabel];
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.mas_equalTo(0);
    }];
    
    
    [self addSubview:self.progress];
    [self.progress addSubview:self.numLabel];
    [self.progress addSubview:self.kcalImgV];
    [self.progress addSubview:self.unitLabel];
    [self.progress mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.titleLabel.mas_bottom).offset(WKDHPX(8));
        make.left.mas_equalTo(0);
//        make.width.equalTo(self.mas_width).multipliedBy(0.26);
//        make.height.equalTo(self.progress.mas_width);
        make.size.mas_equalTo(CGSizeMake(WKDHPX(80), WKDHPX(80)));
    }];
    [self.numLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.progress.mas_centerX);
        make.top.equalTo(self.progress.mas_top).offset(WKDHPX(16));
        make.height.mas_equalTo(WKDHPX(30));
    }];
    [self.kcalImgV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.progress.mas_centerX);
        make.top.equalTo(self.numLabel.mas_bottom);
        make.size.mas_equalTo(CGSizeMake(WKDHPX(15), WKDHPX(15)));
    }];
    [self.unitLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.progress.mas_centerX);
        make.top.equalTo(self.kcalImgV.mas_bottom);
    }];
    
    
    [self addSubview:self.dataView];
    [self.dataView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.progress.mas_centerY);
        make.right.mas_equalTo(0);
        make.left.equalTo(self.progress.mas_right).offset(24);
    }];
}


- (void)setModel:(MRKTrainingDetailModel *)model{
    _model = model;

    self.titleLabel.text = model.title;
    self.numLabel.text = model.kcal ? [NSString convertDecimalNumber:model.kcal num:1] : @"--"; // 小数点后一位
    
    float press = 1;
    if (model.courseShare) {
        press = model.kcal.doubleValue/model.courseShare.kcal.doubleValue;
    }
    [self.progress setProgressRate:press duration:0.3];
    
    [self.dataView removeAllSubviews];
    NSMutableArray *arrry = [NSMutableArray array];
    
    int equipmentId = model.equipmentId.intValue;
    switch (equipmentId) {
        case BoatEquipment: case BicycleEquipment: case EllipticalEquipment: {
           
            MRKShareSportItem *timeItem = [MRKShareSportItem new];
            timeItem.titleLabel.text = @"训练时长·分钟";
            timeItem.detailLabel.text = [MRKTimeManager getHHMMSSFromSS:model.takeTime];
            [self.dataView addSubview:timeItem];
            [arrry addObject:timeItem];

            MRKShareSportItem *distanceItem = [MRKShareSportItem new];
            distanceItem.titleLabel.text = @"里程·公里";
            distanceItem.detailLabel.text = model.distanceStr;
            [self.dataView addSubview:distanceItem];
            [arrry addObject:distanceItem];
            
            ///超燃脂训练总踏频有可能统计不到
            if (model.num.intValue > 0) {
                MRKShareSportItem *numItem = [MRKShareSportItem new];
                numItem.titleLabel.text = equipmentId == BoatEquipment ? @"总桨数":@"总踏数";
                numItem.detailLabel.text = model.num;
                [self.dataView addSubview:numItem];
                [arrry addObject:numItem];
            }
            
            if (model.avgData.intValue > 0) {
                MRKShareSportItem *speedItem = [MRKShareSportItem new];
                speedItem.titleLabel.text = equipmentId == BoatEquipment ? @"平均桨频·次/分钟":@"平均踏频·次/分钟";
                speedItem.detailLabel.text = model.avgData;
                [self.dataView addSubview:speedItem];
                [arrry addObject:speedItem];
            }
        }break;
        case TreadmillEquipment: {
            
            MRKShareSportItem *timeItem = [MRKShareSportItem new];
            timeItem.titleLabel.text = @"训练时长·分钟";
            timeItem.detailLabel.text = [MRKTimeManager getHHMMSSFromSS:model.takeTime];
            [self.dataView addSubview:timeItem];
            [arrry addObject:timeItem];

            MRKShareSportItem *distanceItem = [MRKShareSportItem new];
            distanceItem.titleLabel.text = @"里程·公里";
            distanceItem.detailLabel.text = model.distanceStr;
            [self.dataView addSubview:distanceItem];
            [arrry addObject:distanceItem];
            
            if (model.avgData.floatValue > 0) {
                MRKShareSportItem *speedItem = [MRKShareSportItem new];
                speedItem.titleLabel.text = @"平均速度·公里/时";
                speedItem.detailLabel.text = model.avgData;
                [self.dataView addSubview:speedItem];
                [arrry addObject:speedItem];
            }
        }break;
        case SkipRopeEquipment: case PowerEquipment: {
           
            MRKShareSportItem *timeItem = [MRKShareSportItem new];
            timeItem.titleLabel.text = @"训练时长·分钟";
            timeItem.detailLabel.text = [MRKTimeManager getHHMMSSFromSS:model.takeTime];
            [self.dataView addSubview:timeItem];
            [arrry addObject:timeItem];

            
            MRKShareSportItem *numItem = [MRKShareSportItem new];
            numItem.titleLabel.text = @"个数";
            numItem.detailLabel.text = model.num?:@"0";
            [self.dataView addSubview:numItem];
            [arrry addObject:numItem];
        }break;
        case JinMoQiangEquipment: {
           
            self.progress.alpha = 0;
            [self.dataView mas_updateConstraints:^(MASConstraintMaker *make) {
                make.left.equalTo(self.mas_left).offset(0);
            }];
            
            MRKShareSportItem *timeItem = [MRKShareSportItem new];
            timeItem.titleLabel.text = @"训练时长·分钟";
            timeItem.detailLabel.text = [MRKTimeManager getHHMMSSFromSS:model.takeTime];
            [self.dataView addSubview:timeItem];
            [arrry addObject:timeItem];
           
        }break;
        default: {
            
            MRKShareSportItem *timeItem = [MRKShareSportItem new];
            timeItem.titleLabel.text = @"训练时长·分钟";
            timeItem.detailLabel.text = [MRKTimeManager getHHMMSSFromSS:model.takeTime];
            [self.dataView addSubview:timeItem];
            [arrry addObject:timeItem];
        } break;
    }
    if (arrry.count > 0) {
        [arrry mas_distributeSudokuViewsWithFixedLineSpacing:8
                                       fixedInteritemSpacing:8
                                                   warpCount:2
                                                  topSpacing:0
                                               bottomSpacing:0
                                                 leadSpacing:0
                                                 tailSpacing:0];
    }
}
@end














@interface MRKCustomShareCourseView()
@property (nonatomic, assign) BOOL forShare;
@property (nonatomic, strong) UIImageView *codeImgV;
@end
/// 课程分享
@implementation MRKCustomShareCourseView
- (instancetype)init {
    if (self = [super init]) {
        [self initUI];
    }
    return self;
}
- (instancetype)initForShare:(BOOL)share{
    if (self = [super init]) {
        _forShare = share;
        [self initUI];
    }
    return self;
}

- (void)initUI {
    [self addSubview:self.backImgV];
    [self.backImgV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsMake(0, 0, 0, 0));
    }];

    [self addSubview:self.maskTopLayerView];
    [self.maskTopLayerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.right.mas_equalTo(0);
        make.height.mas_equalTo(self.forShare ? WKDHPX(99) : WKDHPX(62));
    }];
    
    [self addSubview:self.maskBottomLayerView];
    [self.maskBottomLayerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.bottom.mas_equalTo(0);
        make.height.mas_equalTo(self.forShare ? WKDHPX(236) : WKDHPX(147));
    }];
    
    
    
    if (self.forShare){
        [self addSubview:self.userView];
        [self.userView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(WKDHPX(20));
            make.left.mas_equalTo(WKDHPX(16));
            make.right.mas_equalTo(-WKDHPX(16));
        }];
    }else {
        [self addSubview:self.userView];
        [self.userView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(WKDHPX(20));
            make.left.mas_equalTo(WKDHPX(16));
            make.right.mas_equalTo(-WKDHPX(116));
        }];
        
        [self addSubview:self.changeButton];
        [self.changeButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(self.mas_right).offset(-WKDHPX(8));
            make.centerY.equalTo(self.userView.mas_centerY);
            make.size.mas_equalTo(CGSizeMake(WKDHPX(82), WKDHPX(33)));
        }];
    }

    
   
    
    [self addSubview:self.sloganLabel];
    [self.sloganLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(WKDHPX(16));
        make.right.mas_equalTo(-WKDHPX(16));
        if (self.forShare){
            make.bottom.mas_equalTo(-WKDHPX(32));
        }else {
            make.bottom.mas_equalTo(-WKDHPX(16));
        }
    }];
    
    [self addSubview:self.bottomView];
    [self.bottomView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(WKDHPX(16));
        make.right.mas_equalTo(-WKDHPX(16));
        make.bottom.equalTo(self.sloganLabel.mas_top).offset(-WKDHPX(8));
        make.height.mas_equalTo(WKDHPX(124));
    }];
    
    UIView *line = [UIView new];
    line.backgroundColor = [UIColor colorWithHexString:@"#FFFFFF" alpha:.2];
    [self addSubview:line];
    [line mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(WKDHPX(16));
        make.right.mas_equalTo(-WKDHPX(16));
        make.top.equalTo(self.bottomView.mas_bottom);
        make.height.mas_equalTo(CGFloatFromPixel(1.0));
    }];
    
    if (self.forShare){
        [self addSubview:self.codeImgV];
        [self.codeImgV mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.mas_equalTo(self.mas_right).offset(-WKDHPX(16));
            make.centerY.equalTo(self.userView.mas_centerY);
            make.height.mas_equalTo(WKDHPX(60));
            make.width.mas_equalTo(WKDHPX(60));
        }];
    }
}

- (void)setModel:(MRKTrainingDetailModel *)model{
    _model = model;
    self.sloganLabel.text = model.slogan;
    self.userView.timeLabel.text = model.createTime;
    self.bottomView.model = model;
    if (self.forShare) {
        self.bottomView.titleLabel.font = kMedium_Font_NoDHPX(15);
    }else {
        self.bottomView.titleLabel.font = kSystem_Font_NoDHPX(13);
    }
}

- (UIImageView *)backImgV {
    if (!_backImgV) {
        _backImgV = [[UIImageView alloc] init];
        _backImgV.backgroundColor = [UIColor randomColor];
        _backImgV.contentMode = UIViewContentModeScaleAspectFill;
    }
    return _backImgV;
}
- (MRKShareUserView *)userView {
    if (!_userView) {
        _userView = [[MRKShareUserView alloc] init];
    }
    return _userView;
}
- (MRKShareSportContentView *)bottomView {
    if (!_bottomView) {
        _bottomView = [[MRKShareSportContentView alloc] init];
    }
    return _bottomView;
}
- (UILabel *)sloganLabel {
    if (!_sloganLabel) {
        UILabel *label = [UILabel new];
        label.textColor = [UIColor colorWithHexString:@"#FEFEFE"];
        label.font = kMedium_Font_NoDHPX(WKDHPX(18));
        label.textAlignment = NSTextAlignmentCenter;
        label.numberOfLines = 0;
        _sloganLabel = label;
    }
    return _sloganLabel;
}
- (UIImageView *)codeImgV {
    if (!_codeImgV) {
        _codeImgV = [[UIImageView alloc] init];
        _codeImgV.contentMode = UIViewContentModeScaleAspectFill;
        _codeImgV.image = [UIImage imageNamed:@"merit_code"];
    }
    return _codeImgV;
}
- (UIImageView *)maskTopLayerView {
    if (!_maskTopLayerView) {
        _maskTopLayerView = [[UIImageView alloc] init];
        _maskTopLayerView.image = [UIImage imageNamed:@"course_share_top"];
    }
    return _maskTopLayerView;
}
- (UIImageView *)maskBottomLayerView {
    if (!_maskBottomLayerView) {
        _maskBottomLayerView = [[UIImageView alloc] init];
        _maskBottomLayerView.image = [UIImage imageNamed:@"course_share_bottom"];
    }
    return _maskBottomLayerView;
}

- (UIButton *)changeButton {
    if (!_changeButton) {
        UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
        btn.traceEventId = @"btn_share_custom_change";
        [btn setTitle:@"更换背景" forState:UIControlStateNormal];
        UIImage *image = [UIImage imageNamed:@"data_edit"];
        [btn setImage:[image imageByResizeToSize:CGSizeMake(16, 16)] forState:UIControlStateNormal];
        btn.imageView.contentMode = UIViewContentModeScaleAspectFit;
        [btn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        [btn setBackgroundColor:[UIColor colorWithHexString:@"#848A9A" alpha:.6]];
        btn.titleLabel.font = kSystem_Font_NoDHPX(WKDHPX(12));
        btn.cornerRadius = WKDHPX(33)/2;
        [btn addTarget:self action:@selector(changeAction:) forControlEvents:UIControlEventTouchUpInside];
        _changeButton = btn;
    }
    return _changeButton;
}
- (void)changeAction:(id)sender {
    [[NSNotificationCenter defaultCenter] postNotificationName:@"changeMainViewImage" object:nil];
}


- (void)setShowShaowLayer:(BOOL)showShaowLayer{
    _showShaowLayer = showShaowLayer;
    
    self.maskTopLayerView.hidden = !showShaowLayer;
    self.maskBottomLayerView.hidden = !showShaowLayer;
}
@end



@interface MRKCustomShareFoodView()
@property (nonatomic, assign) BOOL forShare;
@property (nonatomic, strong) MRKShareBottomView *bottomView;
@end
/// 食物分享
@implementation MRKCustomShareFoodView
- (instancetype)init {
    if (self = [super init]) {
        [self initUI];
    }
    return self;
}
- (instancetype)initForShare:(BOOL)share{
    if (self = [super init]) {
        _forShare = share;
        [self initUI];
    }
    return self;
}

- (void)initUI {
    [self az_setGradientBackgroundWithColors:@[[UIColor colorWithHexString:@"#F0F8FF"],
                                               [UIColor colorWithHexString:@"#E0F2FC"]]
                                   locations:@[@0,@1]
                                  startPoint:CGPointMake(0, 0)
                                    endPoint:CGPointMake(0, 1)];
    
    [self addSubview:self.userView];
    [self addSubview:self.dataView];
    [self.userView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(WKDHPX(20));
        make.left.mas_equalTo(WKDHPX(16));
        make.right.mas_equalTo(-WKDHPX(16));
    }];
    [self.dataView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(WKDHPX(30));
        make.right.mas_equalTo(-WKDHPX(30));
        make.top.equalTo(self.userView.mas_bottom).offset(WKDHPX(36));
    }];

    if (self.forShare){
        [self addSubview:self.bottomView];
        [self.bottomView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.mas_equalTo(self.mas_left);
            make.right.mas_equalTo(self.mas_right);
            make.bottom.mas_equalTo(self.mas_bottom);
            make.height.mas_equalTo(WKDHPX(92));
        }];
        
        [self addSubview:self.detailLabel];
        [self.detailLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.bottomView.mas_top).offset(-WKDHPX(45));
            make.left.mas_equalTo(WKDHPX(16));
            make.right.mas_equalTo(-WKDHPX(16));
        }];
        
        [self addSubview:self.foodImgV];
        [self.foodImgV mas_makeConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(self.detailLabel.mas_top).offset(-WKDHPX(50));
            make.centerX.equalTo(self.mas_centerX);
            make.size.mas_equalTo(CGSizeMake(WKDHPX(200), WKDHPX(200)));
        }];
        
    } else {
        
        [self addSubview:self.detailLabel];
        [self.detailLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.bottom.mas_equalTo(-WKDHPX(32));
            make.left.mas_equalTo(WKDHPX(16));
            make.right.mas_equalTo(-WKDHPX(16));
        }];
        
        [self addSubview:self.foodImgV];
        [self.foodImgV mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(self.mas_centerX);
            make.bottom.equalTo(self.detailLabel.mas_top).offset(-WKDHPX(24));
            make.size.mas_equalTo(CGSizeMake(WKDHPX(140), WKDHPX(140)));
        }];
    }
}

- (void)setModel:(MRKTrainingDetailModel *)model{
    _model = model;
    
    self.userView.timeLabel.textColor = UIColorHex(#363A44);
    self.userView.timeLabel.text = model.createTime;
    
    NSMutableArray *arrry = [NSMutableArray array];
    MRKShareSportItem *timeItem = [MRKShareSportItem new];
    timeItem.titleLabel.textColor = UIColorHex(#363A44);
    timeItem.titleLabel.text = [NSString stringWithFormat:@"%ld月总训练时长·分钟", model.month];
    timeItem.detailLabel.textColor = UIColorHex(#363A44);
    timeItem.detailLabel.text = [MRKTimeManager getHHMMSSFromSS:model.takeTime];
    timeItem.forShare = self.forShare;
    [self.dataView addSubview:timeItem];
    [arrry addObject:timeItem];
    
    MRKShareSportItem *distanceItem = [MRKShareSportItem new];
    distanceItem.titleLabel.textColor = UIColorHex(#363A44);
    distanceItem.titleLabel.text = [NSString stringWithFormat:@"%ld月累计消耗·千卡", model.month];
    distanceItem.detailLabel.textColor = UIColorHex(#363A44);
    distanceItem.detailLabel.text = [NSString stringWithFormat:@"%@", model.kcal];
    distanceItem.forShare = self.forShare;
    [self.dataView addSubview:distanceItem];
    [arrry addObject:distanceItem];
    [arrry mas_distributeSudokuViewsWithFixedLineSpacing:8
                                   fixedInteritemSpacing:8
                                               warpCount:2
                                              topSpacing:0
                                           bottomSpacing:0
                                             leadSpacing:0
                                             tailSpacing:0];

    
    if (!self.foodImage) {
        NSString *url = [model.img imageUrlAdaptReSize:CGSizeMake(200, 200)];
//        [self.foodImgV setImageWithURL:[NSURL URLWithString:url] options:YYWebImageOptionProgressiveBlur|YYWebImageOptionSetImageWithFadeAnimation];
        @weakify(self);
        [self.foodImgV setImageWithURL:[NSURL URLWithString:url]
                           placeholder:nil
                               options:YYWebImageOptionProgressiveBlur|YYWebImageOptionSetImageWithFadeAnimation
                            completion:^(UIImage * _Nullable image, NSURL * _Nonnull url, YYWebImageFromType from, YYWebImageStage stage, NSError * _Nullable error) {
            @strongify(self);
            self.foodImage = image;
        }];
    }else {
        self.foodImgV.image = self.foodImage;
    }
    self.detailLabel.text = [NSString stringWithFormat:@"累计消耗了「%@%@%@」", model.num, model.identification, model.name];
}

- (UIImageView *)foodImgV {
    if (!_foodImgV) {
        _foodImgV = [[UIImageView alloc] init];
        _foodImgV.contentMode = UIViewContentModeScaleAspectFill;
    }
    return _foodImgV;
}
- (MRKShareUserView *)userView {
    if (!_userView) {
        _userView = [[MRKShareUserView alloc] init];
        _userView.nameLabel.textColor = UIColorHex(#363A44);
        _userView.timeLabel.textColor = UIColorHex(#363A44);
    }
    return _userView;
}
- (UILabel *)detailLabel {
    if (!_detailLabel) {
        UILabel *label = [UILabel new];
        label.textColor = [UIColor colorWithHexString:@"#363A44"];
        label.font = kMedium_Font_NoDHPX(WKDHPX(15));
        label.textAlignment = NSTextAlignmentCenter;
        label.numberOfLines = 0;
        _detailLabel = label;
    }
    return _detailLabel;
}
- (UIView *)dataView {
    if (!_dataView) {
        _dataView = [[UIView alloc] init];
    }
    return _dataView;
}
- (MRKShareBottomView *)bottomView {
    if (!_bottomView) {
        _bottomView = [[MRKShareBottomView alloc] init];
        _bottomView.isDark = YES;
    }
    return _bottomView;
}
@end







@interface MRKCustomShareLocalView()
@property (nonatomic, assign) BOOL forShare;
@property (nonatomic, strong) MRKShareBottomView *bottomView;
@end
/// 地标分享
@implementation MRKCustomShareLocalView
- (instancetype)init {
    if (self = [super init]) {
        [self initUI];
    }
    return self;
}
- (instancetype)initForShare:(BOOL)share{
    if (self = [super init]) {
        _forShare = share;
        [self initUI];
    }
    return self;
}

- (void)initUI {
    self.backgroundColor = UIColor.whiteColor;
    
    [self addSubview:self.localImgV];
    [self.localImgV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsMake(0, 0, 0, 0));
    }];
    [self addSubview:self.maskTopLayerView];
    [self.maskTopLayerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.right.mas_equalTo(0);
        make.height.mas_equalTo(self.forShare ? WKDHPX(99) : WKDHPX(62));
    }];
    [self addSubview:self.maskBottomLayerView];
    [self.maskBottomLayerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.bottom.mas_equalTo(0);
        make.height.mas_equalTo(self.forShare ? WKDHPX(236) : WKDHPX(147));
    }];
    
    
    [self addSubview:self.userView];
    [self addSubview:self.dataView];
    [self.userView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(WKDHPX(20));
        make.left.mas_equalTo(WKDHPX(16));
        make.right.mas_equalTo(-WKDHPX(16));
    }];
    [self.dataView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(WKDHPX(30));
        make.right.mas_equalTo(-WKDHPX(30));
        make.top.equalTo(self.userView.mas_bottom).offset(WKDHPX(36));
    }];
 
    if (self.forShare){
        [self addSubview:self.bottomView];
        [self.bottomView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.mas_equalTo(self.mas_left);
            make.right.mas_equalTo(self.mas_right);
            make.bottom.mas_equalTo(self.mas_bottom);
            make.height.mas_equalTo(WKDHPX(92));
        }];
        
        [self addSubview:self.detailLabel];
        [self.detailLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.bottomView.mas_top).offset(-WKDHPX(45));
            make.left.mas_equalTo(WKDHPX(16));
            make.right.mas_equalTo(-WKDHPX(16));
        }];
    } else {
        [self addSubview:self.detailLabel];
        [self.detailLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.bottom.mas_equalTo(-WKDHPX(32));
            make.left.mas_equalTo(WKDHPX(16));
            make.right.mas_equalTo(-WKDHPX(16));
        }];
    }
}

- (void)setModel:(MRKTrainingDetailModel *)model{
    _model = model;
    self.userView.timeLabel.text = model.createTime;
    
    NSMutableArray *arrry = [NSMutableArray array];
    MRKShareSportItem *timeItem = [MRKShareSportItem new];
    timeItem.titleLabel.text = [NSString stringWithFormat:@"%ld月总训练时长·分钟", model.month];
    timeItem.detailLabel.text = [MRKTimeManager getHHMMSSFromSS:model.takeTime];
    timeItem.forShare = self.forShare;
    [self.dataView addSubview:timeItem];
    [arrry addObject:timeItem];
    
    MRKShareSportItem *distanceItem = [MRKShareSportItem new];
    distanceItem.titleLabel.text = [NSString stringWithFormat:@"%ld月累计里程·公里", model.month];;
    distanceItem.detailLabel.text = ({
        NSString *distanceStr = @"";
        if ([model.distance  isNotBlank]) {
            distanceStr = [NSString convertDecimalNumber:[NSString stringWithFormat:@"%lf", model.distance.doubleValue] num:2 dividing:@"1000"];
        }else{
            distanceStr = @"0.00";
        }
        distanceStr;
    });
    distanceItem.forShare = self.forShare;
    [self.dataView addSubview:distanceItem];
    [arrry addObject:distanceItem];
    [arrry mas_distributeSudokuViewsWithFixedLineSpacing:8
                                   fixedInteritemSpacing:8
                                               warpCount:2
                                              topSpacing:0
                                           bottomSpacing:0
                                             leadSpacing:0
                                             tailSpacing:0];
    
    self.detailLabel.text = ({
        NSString *detailStr = @"";
        double kmDistance = model.distance.doubleValue/1000;
        double count = kmDistance/model.num.doubleValue;
        detailStr = [NSString stringWithFormat:@"相当于绕「%@」%.1f圈", model.identification, count];
        detailStr;
    });
    
    
//    NSString *url = [model.img imageUrlAdaptReSize:CGSizeMake(375, 630)];
//    [self.localImgV setImageWithURL:[NSURL URLWithString:url] options:YYWebImageOptionProgressiveBlur|YYWebImageOptionSetImageWithFadeAnimation];
    
    if (!self.localImage) {
        NSString *url = [model.img imageUrlAdaptReSize:CGSizeMake(375, 630)];
        @weakify(self);
        [self.localImgV setImageWithURL:[NSURL URLWithString:url]
                           placeholder:nil
                               options:YYWebImageOptionProgressiveBlur|YYWebImageOptionSetImageWithFadeAnimation
                            completion:^(UIImage * _Nullable image, NSURL * _Nonnull url, YYWebImageFromType from, YYWebImageStage stage, NSError * _Nullable error) {
            @strongify(self);
            self.localImage = image;
        }];
    }else {
        self.localImgV.image = self.localImage;
    }
    
}

- (UIImageView *)maskTopLayerView {
    if (!_maskTopLayerView) {
        _maskTopLayerView = [[UIImageView alloc] init];
        _maskTopLayerView.image = [UIImage imageNamed:@"course_share_top"];
    }
    return _maskTopLayerView;
}
- (UIImageView *)maskBottomLayerView {
    if (!_maskBottomLayerView) {
        _maskBottomLayerView = [[UIImageView alloc] init];
        _maskBottomLayerView.image = [UIImage imageNamed:@"course_share_bottom"];
    }
    return _maskBottomLayerView;
}

- (MRKShareUserView *)userView {
    if (!_userView) {
        _userView = [[MRKShareUserView alloc] init];
    }
    return _userView;
}
- (UIImageView *)localImgV {
    if (!_localImgV) {
        _localImgV = [[UIImageView alloc] init];
        _localImgV.backgroundColor = [UIColor lightGrayColor];
        _localImgV.contentMode = UIViewContentModeScaleAspectFill;
    }
    return _localImgV;
}
- (UILabel *)detailLabel {
    if (!_detailLabel) {
        UILabel *label = [UILabel new];
        label.textColor = [UIColor colorWithHexString:@"#FFFFFF"];
        label.font = kMedium_Font_NoDHPX(WKDHPX(15));
        label.textAlignment = NSTextAlignmentCenter;
        label.numberOfLines = 0;
        _detailLabel = label;
    }
    return _detailLabel;
}
- (UIView *)dataView {
    if (!_dataView) {
        _dataView = [[UIView alloc] init];
    }
    return _dataView;
}

- (MRKShareBottomView *)bottomView {
    if (!_bottomView) {
        _bottomView = [[MRKShareBottomView alloc] init];
        _bottomView.isDark = NO;
    }
    return _bottomView;
}
@end










/// 地标底部图
@implementation MRKShareBottomView

- (instancetype)init {
    if (self = [super init]) {
        [self initUI];
    }
    return self;
}

- (UIImageView *)sloganImgV {
    if (!_sloganImgV) {
        _sloganImgV = [[UIImageView alloc] init];
        _sloganImgV.contentMode = UIViewContentModeScaleAspectFill;
        _sloganImgV.image = [[UIImage imageNamed:@"img_slogan"] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
    }
    return _sloganImgV;
}

- (UIImageView *)codeImgV {
    if (!_codeImgV) {
        _codeImgV = [[UIImageView alloc] init];
        _codeImgV.contentMode = UIViewContentModeScaleAspectFill;
        _codeImgV.image = [UIImage imageNamed:@"merit_code"];
    }
    return _codeImgV;
}

- (UIView *)lineView {
    if (!_lineView) {
        _lineView = [[UIView alloc] init];
        _lineView.backgroundColor = [UIColorHex(#000000) colorWithAlphaComponent:0.6];
    }
    return _lineView;
}

- (void)initUI {
    [self addSubview:self.lineView];
    [self addSubview:self.sloganImgV];
    [self addSubview:self.codeImgV];

    [self.lineView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.mas_top);
        make.left.mas_equalTo(WKDHPX(20));
        make.right.mas_equalTo(-WKDHPX(20));
        make.height.mas_equalTo(CGFloatFromPixel(1.0));
    }];
    [self.sloganImgV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(self.lineView.mas_left);
        make.top.equalTo(self.lineView.mas_bottom).offset(WKDHPX(20));
        make.height.mas_equalTo(WKDHPX(46));
    }];
    [self.codeImgV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_equalTo(self.lineView.mas_right);
        make.centerY.equalTo(self.sloganImgV.mas_centerY);
        make.height.mas_equalTo(WKDHPX(60));
        make.width.mas_equalTo(WKDHPX(60));
    }];
}

- (void)setIsDark:(BOOL)isDark{
    _isDark = isDark;
    self.sloganImgV.tintColor = isDark ? [UIColor blackColor] :[UIColor whiteColor];
    if (isDark) {
        self.lineView.backgroundColor = [UIColorHex(#000000) colorWithAlphaComponent:0.2];
    }else {
        self.lineView.backgroundColor = [UIColorHex(#FFFFFF) colorWithAlphaComponent:0.2];
    }
}
@end

