//
//  MRKNewShareController.m
//  Student_IOS
//
//  Created by <PERSON><PERSON> on 2023/9/26.
//

#import "MRKNewShareController.h"
#import "JXCategoryView.h" //标签栏
#import "JXCategoryListContainerView.h" //展示区
#import "MRKLongPictureViewController.h"
#import "MRKUserDefineViewController.h"
#import "MRKShareImageController.h"
#import "UIView+AZGradient.h"



@interface MRKNewShareController ()<JXCategoryViewDelegate , JXCategoryListContainerViewDelegate>
@property (nonatomic, strong) JXCategoryTitleView *categoryView;  //标签view
@property (nonatomic, strong) JXCategoryListContainerView *listContainerView;  //展示区容器view
@property (nonatomic, strong) MRKShareImageController *shareItemVC;  // 分享按钮
@property (nonatomic, strong) UIImageView *backImageView;   //模糊背景
@property (nonatomic, strong) MRKUserDefineViewController *userDefineVC;

@property (nonatomic, assign) NSInteger pageIndex;
@property (nonatomic, strong) UIButton *openVipBtn;
@property (nonatomic, strong) UIView *shareView;

@property (nonatomic, strong) UIImageView *tagImageView;
@property (nonatomic, assign) BOOL isMember;
@end

@implementation MRKNewShareController

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)viewDidLoad {
    [super viewDidLoad];
    _pageIndex = 0;
    _isMember = UserInfo.isMember;
    self.statusBarStyle = UIStatusBarStyleLightContent;
    
    ///会员开通通知
    [[NSNotificationCenter defaultCenter] addObserver:self 
                                             selector:@selector(refreshData)
                                                 name:@"MeritVipCreateSuc"
                                               object:nil];
    
    
    self.view.backgroundColor = [UIColor whiteColor];
    self.backImageView.backgroundColor = [UIColor blackColor];
    [self.mrkContentView addSubview:self.backImageView];
    [self.backImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsMake(0, 0, 0, 0));
    }];
    
    //添加头部导航等view
    [self.mrkContentView addSubview:[self headerViewWith]];
    
    //添加标签，容器view
    [self initCategoryView];
    // Do any additional setup after loading the view.
    
    @weakify(self);
    [[RACObserve(self, pageIndex) distinctUntilChanged] subscribeNext:^(id x) {
        @strongify(self)
        NSLog(@"RACObserve(self, index) ===== %@",x);
        dispatch_async(dispatch_get_main_queue(), ^{
            [self reloadPageStatus:[x integerValue]];
        });
    }];
}

- (void)backButtonClick {
    [self dismissViewControllerAnimated:YES completion:nil];
}

#pragma mark - 会员购买后更新
- (void)refreshData {
    _isMember = YES;
    [self reloadPageStatus:self.pageIndex];
}


#pragma mark - 初始化试图
- (UIView *)headerViewWith{
    UIView *topView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, RealScreenWidth, kNavBarHeight)];
    UIButton *backBtn = [[UIButton alloc]initWithFrame:CGRectMake(0, kStatusBarHeight, 44, 44)];
    [backBtn setImage:[UIImage imageNamed:@"icon_back-4"] forState:UIControlStateNormal];
    [backBtn addTarget:self action:@selector(backButtonClick) forControlEvents:UIControlEventTouchUpInside];
    [topView addSubview:backBtn];
    return topView;
}

- (void)initCategoryView {
    self.categoryView.delegate = self;
    [self.mrkContentView addSubview:self.categoryView];
    [self.mrkContentView addSubview:self.listContainerView];
    self.categoryView.listContainer = self.listContainerView;
    
    [self.categoryView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.mas_equalTo(self.mrkContentView.mas_centerX);
        make.top.mas_equalTo(kStatusBarHeight);
        make.height.mas_equalTo(44);
        make.width.mas_equalTo(200);
    }];
    [self.listContainerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(kNavBarHeight);
        make.left.bottom.right.mas_equalTo(0);
    }];
    
    [self.mrkContentView addSubview:self.shareView];
    [self.shareView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.mas_equalTo(self.mrkContentView.mas_centerX);
        make.width.mas_equalTo(RealScreenWidth);
        make.height.mas_equalTo(WKDHPX(220));
        make.bottom.mas_equalTo(self.mrkContentView.mas_bottom).offset(0);
    }];
    
    [self.mrkContentView addSubview:self.openVipBtn];
    [self.openVipBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_equalTo(self.mrkContentView.mas_bottom).offset(WKDHPX(48));
        make.centerX.mas_equalTo(self.mrkContentView.mas_centerX);
        make.size.mas_equalTo(CGSizeMake(DHPX(270), DHPX(48)));
    }];
    
    [self.mrkContentView addSubview:self.tagImageView];
    [self.tagImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_equalTo(self.categoryView.mas_top).offset(15);
        make.left.mas_equalTo(self.categoryView.mas_right).offset(-10);
        make.size.mas_equalTo(CGSizeMake(WKDHPX(47), WKDHPX(18)));
    }];
   
    [self.categoryView reloadData];
}


- (void)reloadPageStatus:(NSInteger)pageIndex {
    
    self.tagImageView.hidden = _isMember;
    self.tagImageView.image = [UIImage imageNamed:({
        NSString *imageName = @"";
        int viptype = UserInfo.vipType;
        switch (viptype) {
            case 10: case 20:
                imageName = @"courseTag_vip";
                break;
            case 30:
                imageName = @"courseTag_enjoyvip";
                break;
            default:
                break;
        }
        imageName;
    })];
    
        
    if (pageIndex == 0 || _isMember) {
        [UIView animateWithDuration:0.3 animations:^{
            dispatch_async(dispatch_get_main_queue(), ^{
                [self.shareView mas_updateConstraints:^(MASConstraintMaker *make) {
                    make.bottom.mas_equalTo(self.view.mas_bottom).offset(0);
                }];
                [self.openVipBtn mas_updateConstraints:^(MASConstraintMaker *make) {
                    make.bottom.mas_equalTo(self.view.mas_bottom).offset(DHPX(48));
                }];
                [self.view setNeedsUpdateConstraints];
                [self.view updateConstraintsIfNeeded];
            });
        }];
        return;
    }
    
    
    if (!_isMember) {
        [UIView animateWithDuration:0.3 animations:^{
            dispatch_async(dispatch_get_main_queue(), ^{
                [self.shareView mas_updateConstraints:^(MASConstraintMaker *make) {
                    make.bottom.mas_equalTo(self.view.mas_bottom).offset(WKDHPX(220));
                }];
                [self.openVipBtn mas_updateConstraints:^(MASConstraintMaker *make) {
                    make.bottom.mas_equalTo(self.view.mas_bottom).offset(-DHPX(70));
                }];
                
                [self.view setNeedsUpdateConstraints];
                [self.view updateConstraintsIfNeeded];
            });
        }];
        return;
    }
}










- (UIView *)shareView {
    if (!_shareView) {
        _shareView = self.shareItemVC.view;
    }
    return _shareView;
}

/// <#Description#>
/// - Parameter courseImage: <#courseImage description#>
- (void)setCourseImage:(UIImage *)courseImage {
    _courseImage = courseImage;
    if (courseImage) {
        self.userDefineVC.courseImage = courseImage;
        self.backImageView.image = ({
            UIImage *img = [courseImage imageByBlurRadius:5 tintColor:[[UIColor blackColor] colorWithAlphaComponent:0.6] tintMode:0 saturation:1 maskImage:nil];
            img;
        });
    }
}

/// 要分享出去的图片
- (void)setShareImage:(UIImage *)shareImage {
    _shareImage = shareImage;
    self.shareItemVC.shareImage = shareImage;
}

/// 分享按钮点击
- (void)shareClick {
    if (self.pageIndex == 0) {
        /// 直接分享，就是 _shareImage 这张图片
        self.shareItemVC.shareImage = self.shareImage;
    }else {
        /// 去截图，再分享
        self.shareItemVC.shareImage = [self.userDefineVC screenshot];
    }
}

#pragma mark - JXCategoryViewDelegate

///点击选中或者滚动选中都会调用该方法。适用于只关心选中事件，不关心具体是点击还是滚动选中的。
- (void)categoryView:(JXCategoryBaseView *)categoryView didSelectedItemAtIndex:(NSInteger)index {
    NSLog(@"%@ ===== %ld", NSStringFromSelector(_cmd), index);
    self.pageIndex = index;
}

///滚动选中的情况才会调用该方法
- (void)categoryView:(JXCategoryBaseView *)categoryView didScrollSelectedItemAtIndex:(NSInteger)index {
    NSLog(@"%@ ===== %ld", NSStringFromSelector(_cmd), index);
}





#pragma mark - JXCategoryListContainerViewDelegate
///返回列表的数量
- (NSInteger)numberOfListsInlistContainerView:(JXCategoryListContainerView *)listContainerView {
    return 2;
}

///返回各个列表菜单下的实例，该实例需要遵守并实现 <JXCategoryListContentViewDelegate> 协议
- (id<JXCategoryListContentViewDelegate>)listContainerView:(JXCategoryListContainerView *)listContainerView initListForIndex:(NSInteger)index {
    NSLog(@"JXCategoryListContentViewDelegate_initListForIndex---%ld",(long)index);
    if (index == 0) {
        MRKLongPictureViewController *vc = [[MRKLongPictureViewController alloc] init];
        vc.shareImage = self.shareImage;
        return vc;
    }
    self.userDefineVC.shareItemVC = self.shareItemVC;
    self.userDefineVC.exerciseID = self.exerciseID;
    self.userDefineVC.equipmentId = self.equipmentId;
    return self.userDefineVC;
}






#pragma mark - lazy

- (MRKUserDefineViewController *)userDefineVC {
    if (!_userDefineVC){
        _userDefineVC = [[MRKUserDefineViewController alloc] init];
    }
    return _userDefineVC;
}

- (UIImageView *)backImageView {
    if (!_backImageView) {
        _backImageView = [[UIImageView alloc] init];
        _backImageView.contentMode = UIViewContentModeScaleAspectFill;
        _backImageView.image = ({
            UIImage *image = [UIImage imageNamed:@"course_share_0"];
            UIImage *img = [[image imageByResizeToSize:kScreenSize] imageByBlurRadius:5 tintColor:[[UIColor blackColor] colorWithAlphaComponent:0.6] tintMode:0 saturation:1 maskImage:nil];
            img;
        });
    }
    return _backImageView;
}

- (JXCategoryTitleView *)categoryView {
    if (!_categoryView) {
        _categoryView = [[JXCategoryTitleView alloc] initWithFrame:CGRectMake(0, 0, 200, 44)];
        _categoryView.backgroundColor = [UIColor clearColor];
        _categoryView.contentScrollViewClickTransitionAnimationEnabled = NO;
        _categoryView.titleFont = kSystem_Font_NoDHPX(15);
        _categoryView.titleSelectedFont = kMedium_Font_NoDHPX(16);
        _categoryView.titleColor = [UIColor colorWithHexString:@"#ffffff"];
        _categoryView.titleSelectedColor = [UIColor colorWithHexString:@"#ffffff"];
        _categoryView.titleColorGradientEnabled = YES;
        _categoryView.titleLabelZoomEnabled = YES;
        _categoryView.titleLabelZoomScale = 1.14;
        _categoryView.titleLabelStrokeWidthEnabled = YES;
        _categoryView.defaultSelectedIndex = 0;
        _categoryView.titles = @[@"长图报告", @"自定义分享"];
        _categoryView.averageCellSpacingEnabled = YES; ///均分
        _categoryView.cellSpacing = 0;
        _categoryView.contentEdgeInsetLeft = 0;
        _categoryView.contentEdgeInsetRight = 0;
        _categoryView.averageCellSpacingEnabled = NO;
        _categoryView.cellWidth = 100;
        
        JXCategoryIndicatorLineView *lineView = [[JXCategoryIndicatorLineView alloc] init];
        lineView.indicatorColor = [UIColor colorWithHexString:@"#ffffff"];
        lineView.indicatorWidth = 20;
        lineView.indicatorHeight = 4;
        lineView.scrollStyle = JXCategoryIndicatorScrollStyleSameAsUserScroll;
        lineView.verticalMargin = 4;
        _categoryView.indicators = @[lineView];
    }
    return _categoryView;
}

- (JXCategoryListContainerView *)listContainerView {
    if (!_listContainerView) {
        _listContainerView = [[JXCategoryListContainerView alloc] initWithType:JXCategoryListContainerType_ScrollView delegate:self];
    }
    return _listContainerView;
}

- (MRKShareImageController *)shareItemVC {
    if (!_shareItemVC) {
        _shareItemVC = [[MRKShareImageController alloc] init];
        @weakify(self);
        _shareItemVC.closeBlock = ^{
            [self_weak_ backButtonClick];
        };
        // 分享
        _shareItemVC.shareClick = ^(NSInteger index) {
            [self_weak_ shareClick];
        };
    }
    return _shareItemVC;
}

- (UIButton *)openVipBtn{
    if (!_openVipBtn) {
        _openVipBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        _openVipBtn.traceEventId = @"btn_member_open";
        [_openVipBtn setTitle:@"开通会员 解锁个性化分享" forState:UIControlStateNormal];
        [_openVipBtn setTitleColor:UIColorHex(#644821) forState:UIControlStateNormal];
        _openVipBtn.titleLabel.font = [UIFont systemFontOfSize:17 weight:UIFontWeightMedium];
        [_openVipBtn addTarget:self action:@selector(moreBtnClicked:) forControlEvents:UIControlEventTouchUpInside];
        _openVipBtn.layer.cornerRadius = WKDHPX(50)/2;
        _openVipBtn.layer.masksToBounds = YES;
        [_openVipBtn  az_setGradientBackgroundWithColors:@[[UIColor colorWithHexString:@"#FFECDF"],
                                                           [UIColor colorWithHexString:@"#F0BA8A"]]
                                              locations:nil
                                             startPoint:CGPointMake(0, 0)
                                               endPoint:CGPointMake(1, 0)];
    }
    return _openVipBtn;
}

- (void)moreBtnClicked:(id)sender {
    [[RouteManager sharedInstance] skipVIP];
}

- (UIImageView *)tagImageView {
    if (!_tagImageView) {
        _tagImageView = [[UIImageView alloc] init];
        _tagImageView.image = [UIImage imageNamed:@"courseTag_vip"];
        _tagImageView.hidden = YES;
    }
    return _tagImageView;
}
@end


