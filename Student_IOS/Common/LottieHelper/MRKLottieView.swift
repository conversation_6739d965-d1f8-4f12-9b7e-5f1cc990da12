//
//  FELottieView.swift
//  Student_IOS
//
//  Created by Jun<PERSON> on 2025/3/4.
//

import UIKit
import Foundation
import Lottie

@objc
public enum MRKLottieBackgroundBehavior: Int {
    case mrk_default = 0
    case mrk_stop = 1
    case mrk_pause = 2
    case mrk_pauseAndRestore = 3
    case mrk_forceFinish = 4
    case mrk_continuePlaying = 5
}

@objc
open class MRKLottieView: UIView {
    /// 动画
    @objc public var animationView = LottieAnimationView()
    
    /// 是否动画中
    @objc public var isAnimationPlaying: Bool {
        return animationView.isAnimationPlaying
    }

    /// 次数
    @objc public var loopAnimationCount: CGFloat = 0 {
        didSet {
            animationView.loopMode = loopAnimationCount == -1 ? .loop : .repeat(Float(loopAnimationCount))
        }
    }
    
    /// 动画名称
    @objc public var animationName: String? {
        didSet {
            if let animationName {
                if let animation = LottieAnimation.named(animationName) {
                    animationView.animation = animation
                } else {
                    print("Error: Could not load animation named '\(animationName)'")
                }
            }
        }
    }

    /// 本地动画文件路径[
    @objc public var filePath: String? {
        didSet {
            guard let filePath = filePath, !filePath.isEmpty else { return }
            if let animation = LottieAnimation.filepath(filePath) {
                animationView.animation = animation
            } else {
                print("Error: Could not load animation at filePath '\(filePath)'")
            }
        }
    }
    
    /// 速度
    @objc public var speed: CGFloat = 1 {
        didSet {
            animationView.animationSpeed = speed
        }
    }
    
    /// 进度属性 (0.0 - 1.0)
    @objc public var progress: CGFloat {
        get {
            return animationView.currentProgress // 获取当前进度
        }
        set {
            animationView.currentProgress = newValue // 设置动画进度
        }
    }
    
    /// 程序到后台动画的行为
    @objc public var backgroundBehavior: MRKLottieBackgroundBehavior = .mrk_default {
        didSet {
            switch backgroundBehavior {
            case .mrk_stop:
                animationView.backgroundBehavior = .stop
            case .mrk_pause:
                animationView.backgroundBehavior = .pause
            case .mrk_pauseAndRestore:
                animationView.backgroundBehavior = .pauseAndRestore
            case .mrk_continuePlaying:
                animationView.backgroundBehavior = .continuePlaying
            case .mrk_forceFinish:
                animationView.backgroundBehavior = .forceFinish
            case .mrk_default:
                break
            }
        }
    }
    
    /// 播放完成的回调
    @objc public var onCompletion: (() -> Void)?
    
    /// 进度更新的回调
    @objc public var onProgressUpdate: ((CGFloat) -> Void)?
    
    /// 替换动画中的文本内容
    /// - Parameters:
    ///   - textReplacements: 文本替换字典，key 是要替换的原文本，value 是新文本
    @objc public func replaceText(with textReplacements: [String: String]) {
        guard animationView.animation != nil else { return }
        animationView.textProvider = TextProvider(textReplacement: textReplacements)
    }
    
    ///
    public init() {
        self.speed = 1
        self.loopAnimationCount = 0
        self.backgroundBehavior = .mrk_default
        super.init(frame: .zero)
        
        animationView.contentMode = .scaleAspectFit
        self.addSubview(animationView)
        animationView.mas_makeConstraints { make in
            make?.edges.equalTo()(self)
        }
        
        animationView.logHierarchyKeypaths()
    }
    
    open override func layoutSubviews() {
        super.layoutSubviews()
        
    }
    
    deinit {
        // 清理资源
        animationView.stop()
        animationView.removeFromSuperview()
        print("MRKLottieView deinitialized")
        
    }
    
    ///名称--创建动画
    @objc public convenience init(name: String) {
        self.init()
        
        guard !name.isEmpty else {
            print("Error: Animation name cannot be empty.")
            return
        }
        
        animationView.animation = LottieAnimation.named(name)
        animationView.play()
    }
    
    ///远层--创建动画
    @objc public convenience init(remoteUrl:String) {
        self.init()
        weak var weakSelf = self
        if let url = URL(string: remoteUrl) {
            animationView = LottieAnimationView(url:url, closure: { (error) in
                if let _ = error {
                    DispatchQueue.main.async {
                        weakSelf?.remove()
                    }
                } else {
                    DispatchQueue.main.async {
                        weakSelf?.showRemoteLottieWhenSuccess()
                    }
                }
            })
        }
    }
    
    ///远层动画成功
    fileprivate func showRemoteLottieWhenSuccess() {
        animationView.contentMode = .scaleAspectFit
        animationView.play()
    }
    
    required public init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    ///播放动画
    @objc open func play(completion:@escaping() -> ()) {
        animationView.play { (isu) in
            if Thread.isMainThread {
                completion()
            } else {
                DispatchQueue.main.async {
                    completion()
                }
            }
        }
    }
    
    ///播放动画
    @objc public func play() -> Void {
        animationView.play { [weak self] _ in
            self?.onCompletion?() // 调用完成回调
        }
    }
    
    ///暂停动画
    @objc public func pause() -> Void {
        animationView.pause();
    }
    
    ///停止动画
    @objc public func stop() -> Void {
        animationView.stop();
    }
    
    ///删除动画
    @objc public func remove() -> Void {
        animationView.removeFromSuperview()
        self.removeFromSuperview()
    }
    
    ///设置动画并自动播放
    @objc public func setAnimation(name: String, autoPlay: Bool = true) {
        guard !name.isEmpty else {
            print("Error: Animation name cannot be empty.")
            return
        }
        
        if let animation = LottieAnimation.named(name) {
            animationView.animation = animation
            if autoPlay {
                animationView.play()
            }
        } else {
            print("Error: Could not load animation named '\(name)'")
        }
    }
    
    /// 更新进度
    @objc public func updateProgress() {
        let currentProgress = animationView.currentProgress
        onProgressUpdate?(currentProgress) // 调用进度更新回调
    }
}

/// 用于在 Objective-C 中使用的扩展
extension MRKLottieView {
    @objc public func replaceTexts(_ replacements: NSDictionary) {
        let swiftDict: [String : String] = replacements as? [String: String] ?? [:]
        replaceText(with: swiftDict)
    }
}
