//
//  PrefixHeader.pch
//  Student_IOS
//
//  Created by merit on 2021/10/14.
//

#ifndef PrefixHeader_pch
#define PrefixHeader_pch

// Include any system framework and library headers here that should be included in all compilation units.
// You will also need to set the Prefix Header build setting of one or more of your targets to reference this file.

//#ifdef __OBJC__

#import <UIKit/UIKit.h>
#import "Student_IOS-Swift.h"

typedef void (^successData)(id data);
typedef void (^failedData)(id data);
typedef void (^netStatus)(NSString * data);
typedef void (^sendPictureProgree)(id data);


#import "AppDelegate.h"
#import "Student_IOS-Swift.h"


#import "ThemeConstant.h"
#import "LEETheme.h"
#import <ReactiveCocoa.h>
#import "SJRouter.h"
#import "MRKLinkRouterManager.h"

#import "MRKBaseController.h"
#import "MRKNavigationController.h"
#import "UINavigationController+FDFullscreenPopGesture.h"
#import "BaseTableViewCell.h"

#import "AlivcImage.h"
#import "UIColor+AlivcHelper.h"

//网络基础
#import "AFNetworking.h"
#import "YTKNetwork.h"

//网络配置
#import "TGApiManager.h"
#import "TGRequest.h"
#import "MRKBaseRequest.h"
#import "DYFStoreManager.h"

#import "MRKPositionCode.h"
#import "MRKAdvertManager.h"

/////这两个define 用于不用考虑父控件
/////1,对于约束参数可以省略"mas_"
//#define MAS_SHORTHAND
/////对于默认的约束参数自动装箱
//#define MAS_SHORTHAND_GLOBALS
#import "Masonry.h"
#import "YYKit.h"
#import "MJRefresh.h"
#import "MJDIYHeader.h"
#import "MJDIYAutoFooter.h"

#import "loadingView.h"
#import "MBProgressHUD.h"
#import "MRKToolKit.h"
#import "UIImageView+WebCache.h"
#import "Header.h"

#import "SimpleLable.h"
#import "SimpleImageView.h"

#import "MRKRequestData.h"
#import "MRKResponse.h"
#import "MRKNetworkCache.h"
#import "MRKCGUtilities.h"


#import "MRKPushManager.h"
#import "JXTAlertManagerHeader.h"
#import "NSString+helper.h"
#import "UIView+Effects.h"
#import "UIView+Frame.h"
#import "Login.h"
#import "User.h"
#import "UserInfo.h"
#import "MRKGrowAlertManager.h"

#import "MRKHealthDataModel.h"
#import "MRKAddHealthPersonVC.h"
#import "MRKTimeManager.h"
#import "FLAnimatedImageView.h"
#import "NSMutableAttributedString+Helper.h"
#import "MRKStandardMacros.h"

///埋点
#import "UIViewController+Trace.h"
#import "UIView+Trace.h"
#import "UIView+Animation.h"
#import "UIColor+mrk.h"
#import "SJCornerMask.h"
#import "MRKTraceManager.h"
#import "MrkLinkUrl.h"
#import "UIView+WebCache.h"
#import "UIView+AZGradient.h"

//@import Lottie;
#import "Lottie/lottie-ios-umbrella.h"




#define kLoginStatus                    @"login_status"   //保存用户登录状态  BOOL类型
#define kLogin_Notification             @"user_login"
#define kLogout_Notification            @"user_logout"
#define kLoginUserDict                  @"user_dict"      //保存用户信息



#if TARGET_IPHONE_SIMULATOR
//模拟器
#elif TARGET_OS_IPHONE
//真机
#endif



#if __has_feature(objc_arc)
//如果是ARC，那么就执行这里的代码1
#else
//如果不是ARC，那么就执行代理的代码2
#endif


/////测试
#if(1)
        #define isDistribute      NO
        #define Base_URL          getServiceUrl(@"http")
        #define AIBase_URL        getServiceUrl(@"aiHttp")
        #define AIVoice_URL       getServiceUrl(@"aiVoiceUri")
        #define WebBase_URL       getServiceUrl(@"web")
        #define WebBasePath_URL   getServiceUrl(@"webPath")
        #define WebSocketURL      getServiceUrl(@"socket")
        #define AIWebBasePathURL  getServiceUrl(@"aiWeb")
        #define AIConfigURL       getServiceUrl(@"aiConfig")
        #define IsTra             ([getServiceUrl(@"isTra") intValue])
        #define mrk_env           getServiceUrl(@"env")
        #define mrk_envCode       getServiceUrl(@"envCode")
#else
        #define isDistribute      YES
        #define Base_URL          @"https://api.merach.com"
        #define AIBase_URL        @"https://mia.merach.com/api"
        #define AIVoice_URL       @"wss://wss-ai.merach.com"
        #define WebBase_URL       @"https://console.merach.com/webview/"
        #define WebBasePath_URL   @"https://h5.merach.com/"
        #define WebSocketURL      @"wss://api.merach.com"
        #define AIWebBasePathURL  @"https://mia.merach.com/"
        #define AIConfigURL       @"https://static.merach.com/ai/ai_config.json"
        #define IsTra             0
        #define mrk_env           @"301"  //101-测试环境 201-预发环境 301-生产环境
        #define mrk_envCode       @"301"
#endif



static inline NSString *MRKAppH5LinkCombine(NSString *remainingAbsoluteString) {
    if (isDistribute) {
        return [NSString stringWithFormat:@"%@%@", WebBasePath_URL, remainingAbsoluteString];
    }

    if ([remainingAbsoluteString containsString:@"?"]) {
        return [NSString stringWithFormat:@"%@%@&server=%@", WebBasePath_URL, remainingAbsoluteString, Base_URL];
    }
    return [NSString stringWithFormat:@"%@%@?server=%@", WebBasePath_URL, remainingAbsoluteString, Base_URL];
}

static inline NSString *MRKAppAIH5LinkCombine(NSString *remainingAbsoluteString) {
    if (isDistribute) {
        return [NSString stringWithFormat:@"%@%@", AIWebBasePathURL, remainingAbsoluteString];
    }

    if ([remainingAbsoluteString containsString:@"?"]) {
        return [NSString stringWithFormat:@"%@%@&server=%@", AIWebBasePathURL, remainingAbsoluteString, AIWebBasePathURL];
    }
    return [NSString stringWithFormat:@"%@%@?server=%@", AIWebBasePathURL, remainingAbsoluteString, AIWebBasePathURL];
}


#define JMessageAppKey                    @"0c18e22f62bc1e908fb1011e"
#define JmessageName                      @""

//#define WechatUniversalLink                                      @"https://console.merach.com/webview/app-link/index.html" //我们的链接
#define WechatUniversalLink               @"https://ce3f47de1580da84a5a31f8ff2f23cc3.share2dlink.com/"//shareSDK地址

#define AppId                             @"1547744135"

#define WechatAppId                       @"wx5268e9b7f55c368b"
#define WechatAppSecret                   @"549a141ead26000bfd93f8ead3fbf2f8"

#define WeiboKey                          @"982091331"
#define WeiboSecret                       @"80c75f5b3286f3d633c6defcd64c9802"
#define WeiBo_RedirectUri                 @"https://api.weibo.com/oauth2/default.html"

#define QQAppId                           @"1111447758"
#define QQAppKey                          @"qY9tLENGItuZ5H7Z"
#define QQURLlink                         @"https://console.merach.com/qq_conn/1111447758"

///判断时候竖屏
#define MRKInterfaceOrientationIsPortrait UIInterfaceOrientationIsPortrait([UIApplication sharedApplication].statusBarOrientation)

///判断时候横屏
#define MRKInterfaceOrientationIsLandscape UIInterfaceOrientationIsLandscape([UIApplication sharedApplication].statusBarOrientation)


#pragma mark   -------- Frame -----------
#define FONTAUTO(SIZE)                  ceilf((SIZE) * (kScreenWidth/375.0f))

#define placeImage                      [UIImage imageNamed:@"course_cell_placeholder"]

//宏定义不同尺寸屏幕大小尺寸以及字体大小
#define MainWidth                       [UIScreen mainScreen].bounds.size.width
#define MainHeight                      [UIScreen mainScreen].bounds.size.height
#define MaxMainWidth                    MAX(MainWidth, MainHeight)
#define MinMainWidth                    MIN(MainWidth, MainHeight)

#define statusLength                    (IS_IPHONEX_SURE ? 0:24)
#define tabbarLength                    (IS_IPHONEX_SURE ? 34:0)


#pragma mark   -------- Color -----------
#define RGBA(r, g, b, a)                [UIColor colorWithRed:(r)/255.0f green:(g)/255.0f blue:(b)/255.0f alpha:a]
#define RGB(r, g, b)                    [UIColor colorWithRed:(r)/255.0f green:(g)/255.0f blue:(b)/255.0f alpha:1.0]
#define UIColorRGB_BGColor              [UIColor colorWithHexString:@"#F5F5F5"]

#pragma mark   -------- PATH -----------


#pragma mark   -------- 字体色-----------
#define MainTextColor                   UIColorHex(#363A44)
#define DescribeTextColor               UIColorHex(#C9CBCF)

//
#define MainAppColor                    UIColorHex(#16D2E3)

#define LineColor                       [UIColor colorWithHexString:@"#4C5362" alpha:0.1]
#define titleNormalColor1               @"#363A44"
#define titleNormalColor2               @"#6C7B8A"
#define titleNormalColor3               @"#848A9B"


// Include any system framework and library headers here that should be included in all compilation units.
// You will also need to set the Prefix Header build setting of one or more of your targets to reference this file.
#define kAppDelegate                    ((AppDelegate *)[UIApplication sharedApplication].delegate)


#pragma mark ===================== 个人页面的配置 ======================
//评价我们
#define kEvaluatePassStar                4 // 四星、五星及格

#pragma mark ======================== 其他 ===========================
//测试代码执行时长
#define TICK    CFAbsoluteTime start = CFAbsoluteTimeGetCurrent()
#define TOCK    NSLog(@"代码执行时长 \n Time: %f", CFAbsoluteTimeGetCurrent() - start)



#ifdef DEBUG

#define SJNSLog(formater,...) NSLog((@"\n======SJNSLog Start======\n >>> class: %s\n >>> method: %s\n" " >>> code line: %d 行\n >>> message: "  formater @"\n======SJNSLog======"),__FILE__,__FUNCTION__,__LINE__,##__VA_ARGS__)

//A better version of NSLog
//#define NSLog(format, ...) do { \
//fprintf(stderr, "▧▧▧▧▧▧▧▧▧▧▧▧▧▧▧▧▧▧▧▧▧▧▧▧▧▧▧▧▧▧▧▧▧▧▧▧▧▧▧▧▧▧▧▧▧▧▧▧▧▧▧▧▧▧▧▧▧\n"); \
//fprintf(stderr, "<%s : %d> %s\n", \
//[[[NSString stringWithUTF8String:__FILE__] lastPathComponent] UTF8String], \
//__LINE__, __func__); \
//(NSLog)((format), ##__VA_ARGS__); \
//fprintf(stderr, "--------------------------------------------------------\n"); \
//} while (0)

#else

#define SJNSLog(...) /* */
#define NSLog(...)

#endif



//蓝牙log
#if(0)
#define BlueNSLog(formater,...) NSLog((@"\n======BlueNSLog Start======\n >>> class: %s\n >>> method: %s\n" " >>> code line: %d 行\n >>> message: "  formater @"\n======SJNSLog Start======"),__FILE__,__FUNCTION__,__LINE__,##__VA_ARGS__)
#else
#define BlueNSLog(...) /* */
#endif


//蓝牙服务log
#if(0)
#define BlueServiceNSLog(formater,...) NSLog((@"\n======BlueServiceNSLog Start======\n >>> class: %s\n >>> method: %s\n" " >>> code line: %d 行\n >>> message: "  formater @"\n======BlueServiceNSLog======"),__FILE__,__FUNCTION__,__LINE__,##__VA_ARGS__)
#else
#define BlueServiceNSLog(...) /* */
#endif


#endif /* PrefixHeader_pch */



#ifdef __OBJC__
#import <UIKit/UIKit.h>
#else
#ifndef FOUNDATION_EXPORT
#if defined(__cplusplus)
#define FOUNDATION_EXPORT extern "C"
#else
#define FOUNDATION_EXPORT extern
#endif
#endif
#endif


