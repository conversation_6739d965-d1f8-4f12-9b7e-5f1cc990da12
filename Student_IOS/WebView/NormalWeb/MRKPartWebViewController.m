//
//  MRKPartWebViewController.m
//  Student_IOS
//
//  Created by merit on 2021/10/21.
//

#import "MRKPartWebViewController.h"
#import "MRKConvenientFunc.h"
#import "MRKTraceManager.h"
#import "MRKWebJSHandler.h"

@interface MRKPartWebViewController ()
@property (nonatomic, strong) NSString *realTrueUrlString;
@end

@implementation MRKPartWebViewController
- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self.webView mas_updateConstraints:^(MASConstraintMaker *make) {
        float paddingTop = kNavBarHeight;
        make.top.mas_equalTo(self.mrkContentView.mas_top).offset(paddingTop);
    }];

    [self registerJSHandler];
    [self loadWebMainPage:self.httpUrl];
}

- (void)setHttpUrl:(NSString *)httpUrl {
    _httpUrl = httpUrl;
}

- (void)loadWebMainPage:(NSString *)httpStr{
    NSString *url = httpStr;
    NSMutableURLRequest *requset = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:url]
                                                           cachePolicy:NSURLRequestUseProtocolCachePolicy
                                                       timeoutInterval:20];
    [self.webView loadRequest:requset];
}

- (NSMutableDictionary *)callBackParms {
    NSDictionary *parms = @{
        @"token":UserInfo.token,
        @"type":@"iOS",
        @"headerHeight":@(kNavBarHeight),
        @"bottomType":IS_IPHONEX_SURE ? @"1":@"0",
        @"version" : [MRKConvenientFunc getAppShortVersion],///app版本
        @"User-Agent" : [NSString stringWithFormat:@"%@,H5",[TGApiManager userAgentString]],
        @"traceDeviceId": [MRKConvenientFunc getDeviceUUID],
        @"traceTerminal": @([MRKConvenientFunc getCurrentTerminalIndex]),
        @"traceRoute": [MRKTraceManager sharedInstance].traceRoute
    };
    return parms.mutableCopy;
}

- (void)registerJSHandler{
    [[MRKWebJSHandler shared] registerJSMethodHandler:self.bridge base:self];
}


- (UIImage *)mrkNavigationBarLeftButtonImage:(UIButton *)leftButton navigationBar:(MRKNavigationBar *)navigationBar {
    return [UIImage imageNamed:@"icon_back"];
}

/** 背景色 */
- (UIColor *)mrkNavigationBarBackgroundColor:(MRKNavigationBar *)navigationBar{
    return [UIColor whiteColor];
}

- (UIStatusBarStyle)navControllerStatusBarStyle:(MRKBaseController *)viewController{
    return UIStatusBarStyleDefault;
}

- (void)dealloc{
    
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

@end
