//
//  WebViewViewController.m
//  Student_IOS
//
//  Created by MacPro on 2021/6/10.
//

#import "WebViewViewController.h"
#import "UIView+LXShadowPath.h"
#import <WebKit/WebKit.h>
#import "WKWebViewJavascriptBridge.h"
#import "YYCGUtilities.h"
#import "MRKShareView.h"
#import "MRKChallengeDetailVC.h"
#import "MRKChallengeModel.h"
#import "MRKCustomShareViewController.h"  //分享图片
#import <Photos/Photos.h>
#import "TGPermissionSetting.h"
#import "DeviceSearchViewController.h"

#import "MRKShareImageController.h"
#import "UIViewController+ShareImage.h"
#import "MRKHUAWEIHealth.h"
#import "MRKConvenientFunc.h"
#import "MRKShareButtonsView.h"
#import "MRKTraceManager.h"
#import "TelePhoneLoginViewController.h"
#import "MRKWebJSHandler.h"

@interface WebViewViewController () <MrkWebViewDelegate>
@property (nonatomic, strong) RACSubject *mrkDeallocSignal;
@end

@implementation WebViewViewController

- (instancetype)init {
    self = [super init];
    if (self) {
        self.mrkDeallocSignal = [[RACSubject subject] setNameWithFormat:@"WebViewViewController"];
    }
    return self;
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    
    //如果是活动，获取一下有无获得活动勋章 2022-07-14 --wk
    if ([self.activityId isNotBlank]) {
        [MRKGrowAlertManager postLevelAndMedalRequest];
    }
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self setUI];
    [self registerJSHandler];
    
    if (self.isAddControlDeviceObs) {
        [[NSNotificationCenter defaultCenter] addObserver:self
                                                 selector:@selector(setWeightNotification:)
                                                     name:@"MRKSetWeightNotification"
                                                   object:nil];
    }
}

- (void)setWeightNotification:(NSNotification *)notification{
    MLog(@"标定配重块设置结果：%@", notification.object);
    [self.bridge callHandler:[MRKWebJSHandler operatorStr: MRKWebJSCallBackMethodDeviceControl] 
                        data:@{@"productId": [NSString stringWithFormat:@"%@",@(PowerEquipment)],
                               @"operator": @"calibrationPowerWeight",
                               @"status": [NSString stringWithFormat:@"%@",notification.object]}
            responseCallback:^(id responseData) {
        
    }];
}

- (void)setUI {
    self.navTitle = self.titleString;
    self.webView.backgroundColor = UIColor.whiteColor;
    self.webView.delegate = self;
    [self.webView mas_updateConstraints:^(MASConstraintMaker *make) {
        float paddingTop = self.isHiddenNav ? 0 : kNavBarHeight;
        make.top.mas_equalTo(self.mrkContentView.mas_top).offset(paddingTop);
    }];
    
    NSURL *url = [NSURL URLWithString:self.htmlURL];
    NSURLRequest *request = [NSURLRequest requestWithURL:url];
    [self.webView loadRequest:request];
    
    NSLog(@"self.pageRouterData======== %@", self.pageRouterData);
    
    self.mrk_navgationBar.rightView.hidden = YES;
    ///修复activityId 没有传进来的情况, 从Url里面取
    if ([self.htmlURL isNotBlank] && !self.activityId) {
        self.activityId = [NSString getIdFromUrlParams:self.htmlURL andCode:@"id"];
    }
    
    
    ///新用户链路指引页面
    self.isNewUserPath = [url.path isEqualToString:@"/connect-link"];
    
    
    // 自己处理侧滑返回手势 wk
    @weakify(self);
    self.fd_interactivePopBlock = ^BOOL(UIPanGestureRecognizer * pan) {
        @strongify(self);
        if ([self.methods containsObject: [MRKWebJSHandler operatorStr: MRKWebJSCallBackMethodOnBack]]) {
            [self.bridge callHandler: [MRKWebJSHandler operatorStr: MRKWebJSCallBackMethodOnBack]];
            return NO;
        } else {
            return YES;
        }
    };
    
    __block RACDisposable *dispose = [[[NSNotificationCenter defaultCenter] rac_addObserverForName:@"ShowH5Alert" object:nil] subscribeNext:^(id x) {
        @strongify(self);
        NSLog(@"ShowH5Alert");
        [self.bridge callHandler:[MRKWebJSHandler operatorStr: MRKWebJSCallBackMethodShowModal] data:nil responseCallback:^(id responseData) {
            
        }];
       [dispose dispose];
    }];
    
    
    ///设备解绑，绑定，连接设备通知
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(switchShowDevice:)
                                                 name:@"kUpdateHomeShowDeviceNotification"
                                               object:nil];
    
    //会员开通成功后通知
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(refreshWebPage:)
                                                 name:@"MeritVipCreateSuc"
                                               object:nil];
}

///设备绑定
- (void)switchShowDevice:(NSNotification *)sender {
    if (sender.object != nil) {
        BOOL isbind = [[sender.object objectForKey:@"bind"] boolValue]; ///判断是否设备绑定操作，其他过滤
        if (isbind) {
            [self.bridge callHandler:[MRKWebJSHandler operatorStr: MRKWebJSCallBackMethodWebRefresh] data:nil responseCallback:^(id responseData) {
                
            }];
        }
    }
}

///会员购买成功
- (void)refreshWebPage:(NSNotification *)sender {
    [self.bridge callHandler:[MRKWebJSHandler operatorStr: MRKWebJSCallBackMethodWebRefresh] data:nil responseCallback:^(id responseData) {
    }];
    NSDictionary *dict = @{@"payDown": @1};
    NSString *str = [dict modelToJSONString];
    [self.bridge callHandler:[MRKWebJSHandler operatorStr: MRKWebJSCallBackAppMessage] data:str responseCallback:^(id responseData) {
    }];
}


- (NSMutableDictionary *)callBackParms {
    NSString *appTheme = @"light";
    NSString *themeTag = [LEETheme currentThemeTag];
    if ([themeTag isNotBlank]) {
        if ([themeTag isEqualToString:THEME_XENJOY]) {
            appTheme = @"dark";
        }
    }
    
    NSMutableDictionary *parms = [[NSMutableDictionary alloc] initWithDictionary:@{
        @"token"        : UserInfo.token,
        @"terminal"     : @(kTerminal),
        @"version"      : [MRKConvenientFunc getAppShortVersion],///app版本
        @"User-Agent"   : [NSString stringWithFormat:@"%@,H5", [TGApiManager userAgentString]],
        @"bottomType"   : IS_IPHONEX_SURE ? @"1":@"0",
        @"navHeight"    : @(kNavBarHeight),
        @"channel"      : self.activitySource ?:@"", // channel 渠道：1-首页弹窗，2-APP广告页，3-banner
        @"id"           : self.activityId ?:@"",
        @"newStyle"     : @(1), //2.7.2 新增字段，传入后 直接隐藏该页面导航条 22-05-25
        @"traceDeviceId": [MRKConvenientFunc getDeviceUUID],
        @"traceTerminal": @([MRKConvenientFunc getCurrentTerminalIndex]),
        @"traceRoute"   : [MRKTraceManager sharedInstance].traceRoute?:@"",
        @"appTheme"     : appTheme,
        @"systemVersion": [MRKConvenientFunc getSystemVersion],
        @"globalSoundFlag":@([[[NSUserDefaults standardUserDefaults] valueForKey:@"SaveH5AIGlobalSoundFlag"] intValue]),
        @"isNewUser"    : @(self.isNewUser)
    }];
    
    ///新链路添加
    if (self.firstInto){
        [parms setObject:@"1" forKey:@"firstInto"];
    }
    
    if (self.date.length > 0) {
        [parms setObject:self.date forKey:@"date"];
    }
    if (self.planId.length > 0) {
        [parms setObject:self.planId forKey:@"planId"];
    }
    //ai语音对话，默认文字
    if (self.miaVoiceText.isNotBlank) {
        [parms setObject:self.miaVoiceText forKey:@"text"];
    }
    return parms.mutableCopy;
}


- (void)registerJSHandler {
    [[MRKWebJSHandler shared] registerJSMethodHandler:self.bridge base:self];
}

///自定义动画
- (CATransition *)popTransition {
    CATransition *transition = [[CATransition alloc] init];
    transition.duration = 0.25;
    transition.type = kCATransitionPush;
    transition.subtype = kCATransitionFromLeft;
    return transition;
}

- (void)backButtonClick {
    if ([self.methods containsObject:[MRKWebJSHandler operatorStr: MRKWebJSCallBackMethodOnBack]]) {
        [self.bridge callHandler:[MRKWebJSHandler operatorStr: MRKWebJSCallBackMethodOnBack] data:nil responseCallback:^(id responseData) {}];
    } else {
        [self goback];
    }
}
/*
- (void)back {
    
    if (self.navigationController.childViewControllers.count > 1) {
        [self.navigationController popViewControllerAnimated:YES];
    } else {
        [self dismissViewControllerAnimated:YES completion:nil];
    }
}
 */

//分享
- (void)shareButtonClick {
    [self.bridge callHandler:[MRKWebJSHandler operatorStr:MRKWebJSCallBackMethodShareBody] data:@"" responseCallback:^(id responseData) {
        NSLog(@"shareBody -- %@" , responseData);
        //        MRKShareView *view = [MRKShareView shareViewWithType:ShareActionViewReport andViewController:self];
        //        view.shareModel = model;
        //        [view showInView:self.view];
        MRKShareModel *model = [MRKShareModel modelWithJSON:responseData];
        [FlutterManager shareCommonVCWithTitle:model.shareTitle text:model.shareDescrip link:model.shareUrl thumbImage:model.shareImageUrl];
    }];
}

- (void)webView:(WKWebView *)webView decidePolicyForNavigationAction:(WKNavigationAction *)navigationAction decisionHandler:(void (^)(WKNavigationActionPolicy))decisionHandler {
    NSString *url = navigationAction.request.URL.absoluteString;
    if ([navigationAction.request.URL.scheme isEqualToString:@"yudongapp"]) {
        [[UIApplication sharedApplication] openMrkUrl:url];
    }
    
    if (navigationAction.targetFrame == nil) {
        [webView loadRequest:navigationAction.request];
    }
    decisionHandler(WKNavigationActionPolicyAllow);

    NSLog(@"decidePolicyForNavigationAction===%@" , navigationAction.request.URL.host);
}
#pragma mark - delegate

- (void)webView:(MrkWebView *)webView didLoadWithTitle:(NSString *)title {
    self.navTitle = title;
    self.navTitleColor = MainTextColor;
}



- (BOOL)viewControllerIsNeedNavBar:(MRKBaseController *)viewController {
    return !self.isHiddenNav;
}

- (void)leftButtonEvent:(UIButton *)sender navigationBar:(MRKNavigationBar *)navigationBar {
    [self backButtonClick];
}

- (UIImage *)mrkNavigationBarLeftButtonImage:(UIButton *)leftButton navigationBar:(MRKNavigationBar *)navigationBar {
    return [UIImage imageNamed:@"icon_back"];
}

- (UIImage *)mrkNavigationBarRightButtonImage:(UIButton *)rightButton navigationBar:(MRKNavigationBar *)navigationBar {
    rightButton.contentHorizontalAlignment = UIControlContentHorizontalAlignmentRight;
    return [UIImage imageNamed:@"icon_share_B"];;
}

- (void)rightButtonEvent:(UIButton *)sender navigationBar:(MRKNavigationBar *)navigationBar {
    sender.traceEventId = @"btn_all_share";
    [self shareButtonClick];
}

- (UIColor *)mrkNavigationBarBackgroundColor:(MRKNavigationBar *)navigationBar {
    return [UIColor whiteColor];
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
    [(RACSubject *)self.mrkDeallocSignal sendNext:nil];
    NSLog(@"%@___dealloc" , NSStringFromClass([self class]));
}

- (UIRectEdge)preferredScreenEdgesDeferringSystemGestures{
    NSString *aiChatUrl = MRKAppAIH5LinkCombine(MRKAIChat);
    if ([self.htmlURL isEqualToString:aiChatUrl]) {
        return UIRectEdgeBottom;
    }
    return UIRectEdgeNone;
}

@end
