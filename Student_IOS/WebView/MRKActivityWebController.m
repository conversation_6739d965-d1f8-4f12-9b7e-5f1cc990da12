//
//  MRKActivityWebController.m
//  Student_IOS
//
//  Created by merit on 2021/9/27.
//

#import "MRKActivityWebController.h"
#import "MRKShareView.h"
#import "MRKChallengeModel.h"
#import "MRKChallengeEntranceVC.h"
#import "MRKTimerManager.h"
#import "MRKTimeManager.h"

#import "MRKActionProgressVC.h"
#import "MRKActionSignUpAlert.h"
#import "MRKCoursePlanController.h"
#import "MRKChallengeEntranceVC.h"
#import "MRKActivityRankAlert.h"
#import "MRKActivityPrizeAlert.h"
#import "MRKChallengeDetailVC.h"
#import "PlanExerciseReportWebController.h"
#import "MRKNewChallengeEntranceVC.h"
#import "MRKAddressViewController.h"
#import "MRKConvenientFunc.h"
#import "MRKTraceManager.h"
#import "MRKWebJSHandler.h"

@interface MRKActivityWebController ()<MRKWebBottomBarDelegate>
@property (nonatomic, strong) NSString *realTrueUrlString;
@property (nonatomic, strong) MRKWebBottomBar *barView;
@property (nonatomic, strong) MRKChallengeModel *model;
@property (nonatomic, strong) UIView *adressView;
@end

@implementation MRKActivityWebController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    //计划创建成功后 刷新页面
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(requestWebData) name:@"MeritVipCreateSuc" object:nil];

    self.barView = [[MRKWebBottomBar alloc] init];
    self.barView.delegate = self;
    [self.view addSubview:self.barView];
    [self.barView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_equalTo(self.view.mas_bottom);
        make.centerX.mas_equalTo(self.view.mas_centerX);
        make.size.mas_equalTo(CGSizeMake(RealScreenWidth, DHPX(60) +kSafeArea_Bottom));
    }];
    
    [self.webView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.mrkContentView.mas_top).offset(kNavBarHeight);
        make.bottom.mas_equalTo(self.barView.mas_top);
    }];
    
    
    [self registerJSHandler];
    [self requestWebData];
}

- (void)requestWebData{
    ChallengeEntranceRequest *t_api = [[ChallengeEntranceRequest alloc] init];
    t_api.animatingView = self.view;
    t_api.requestData = @{@"activityId":self.activityId?:@""}.mutableCopy;
    [t_api startWithCompletionBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"dic======%@",request.responseObject);
        
        MRKChallengeModel *model = [MRKChallengeModel modelWithJSON:[request.responseObject valueForKeyPath:@"data"]];
        self.model = model;
        
        self.navTitle = model.activityInfo.title;
        self.navTitleColor = [UIColor whiteColor];
      
        self.barView.model = model;
        
        NSInteger onlineStatus = model.activityInfo.onlineStatus;
        switch (onlineStatus) {
            case 1:{
                NSMutableString *appleString = [[NSMutableString alloc]init];
                [appleString appendString:WebBase_URL];
                [appleString appendString:@"banner/activity-one-info/index.html"];
                self.httpUrl = appleString;
                
                [self loadWebMainPage:self.httpUrl];
            }break;
            case 2:{
                NSMutableString *appleString = [[NSMutableString alloc]init];
                [appleString appendString:WebBase_URL];
                [appleString appendString:@"banner/activity-one-start-info/index.html"];
                self.httpUrl = appleString;
                
                [self loadWebMainPage:self.httpUrl];
            }break;
            case 3:{
                NSMutableString *appleString = [[NSMutableString alloc]init];
                [appleString appendString:WebBase_URL];
                [appleString appendString:@"banner/activity-end-info/index.html"];
                self.httpUrl = appleString;
                
                [self loadWebMainPage:self.httpUrl];
                
                self.mrk_navgationBar.rightView.hidden = YES;
                
                ///判断有无排名信息
                if (self.model.myRank.rank <= 0) {
                    self.webView.height = kScreenHeight - kNavBarHeight;
                    self.webView.bottom = kScreenHeight;
                    self.barView.hidden = YES;
                    
                    self.webProgressView.top = 0;
                }
                
                ///判断有无添加过地址
                if (self.model.isFillInAddress) {
                    [self addAdressPart];
                }else{
                    self.adressView.hidden = YES;
                }
                
            }break;
            default:
                break;
        }
        
        [self registerJSHandler];
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        
    }];
}


- (void)loadWebMainPage:(NSString *)httpStr {
    NSLog(@"httpStr======%@",httpStr);
    NSURLRequest *request = [NSURLRequest requestWithURL:[NSURL URLWithString:httpStr]];
    [self.webView loadRequest:request];
}

- (NSMutableDictionary *)callBackParms {
    NSDictionary *parms = @{
        @"token":UserInfo.token,
        @"id":self.activityId?:@"",
        @"type":@"iOS",
        @"headerHeight":@(kNavBarHeight),
        @"bottomType":IS_IPHONEX_SURE ? @"1":@"0",
        @"channel" : self.activitySource ?: @"",
        @"version" : [MRKConvenientFunc getAppShortVersion],///app版本
        @"User-Agent" : [NSString stringWithFormat:@"%@,H5",[TGApiManager userAgentString]],
        @"traceDeviceId": [MRKConvenientFunc getDeviceUUID],
        @"traceTerminal": @([MRKConvenientFunc getCurrentTerminalIndex]),
        @"traceRoute": [MRKTraceManager sharedInstance].traceRoute
    };
    return parms.mutableCopy;
}

- (void)registerJSHandler {
    [[MRKWebJSHandler shared] registerJSMethodHandler:self.bridge base:self];
}

- (void)shareBody {
    [MBProgressHUD showLodingWithMessage:@"" view:self.view];

    NSDictionary *parms= @{
        @"id":self.activityId?:@"",
        @"url":self.httpUrl?:@""
    };
    [MRKBaseRequest mrkPostRequestUrl:@"/course/activityShareController/share"
                              andParm:parms
             completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        [MBProgressHUD hideHUDForView:self.view];
        NSString *shareUrl = [request.responseObject valueForKeyPath:@"data.share"]; //分享链接
        NSArray *shareDetail = [request.responseObject valueForKeyPath:@"data.activityShareList"];  //分享的内容
        //点击分享按钮时，才会确定分享的类型，是朋友圈还是好友
        MRKShareView *view = [MRKShareView shareViewWithType:ShareActionViewReport andViewController:self];
        view.shareUrl = shareUrl;
        @weakify(self);
        view.tapShareButtonBlock = ^(SSDKPlatformType type, void (^shareBlock)(NSString * _Nonnull, NSString * _Nonnull, id _Nonnull)) {
            @strongify(self);
            [self shareType:type shareDetail:shareDetail shareBlock:shareBlock];
        };
        [view showInView:self.view];
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        [MBProgressHUD hideHUDForView:self.view];
    }];
}

///获取活动页面分享的副标题 2021-11-16 --wk
- (void)shareType:(SSDKPlatformType)type shareDetail:(NSArray *)detail shareBlock:(void(^)(NSString *, NSString *, id))shareBlock {
    // 1.分享至好友,2.分享至朋友圈图 只有微信朋友圈是分享至朋友圈
    NSDictionary *shareDic = [NSDictionary dictionary];
    NSString *shareType = @"1";
    switch (type) {
        case SSDKPlatformSubTypeWechatTimeline:
            shareType = @"2";
            break;
        default:
            break;
    }
    //筛选分享的数据
    for (NSDictionary * dic in detail) {
        if ([[NSString stringWithFormat:@"%@",[dic objectForKey:@"type"]] isEqualToString:shareType]) {
            shareDic = dic;
            break;
        }
    }
    /// 执行分享操作
    if (shareBlock) {
        shareBlock([shareDic objectForKey:@"title"],[shareDic objectForKey:@"describeInfo"],[shareDic objectForKey:@"image"]);
    }
}

#pragma mark - WebViewDelegate
#pragma mark - WebViewDelegate

- (void)webView:(MrkWebView * _Nonnull)webView didFailLoadWithError:(NSError * _Nonnull)error {
    [self.webView.scrollView.mj_header endRefreshing];
}

- (void)webView:(MrkWebView * _Nonnull)webView didLoadWithTitle:(NSString * _Nonnull)title {
    self.navTitle = title;
    self.navTitleColor = [UIColor whiteColor];
}

- (BOOL)webView:(MrkWebView * _Nonnull)webView shouldStartLoadWith:(NSURLRequest * _Nonnull)request navigationType:(WKNavigationType)navigationType {
    NSLog(@"request.URL.absoluteString =========%@", request.URL.absoluteString);
    return YES;
}

- (void)webViewDidFinishLoad:(MrkWebView * _Nonnull)webView {
    [self.webView.scrollView.mj_header endRefreshing];
    
    if (self.webView.title.length > 0) {
        self.navTitle = self.webView.title;
    }
}

- (void)webViewDidStartLoad:(MrkWebView * _Nonnull)webView {
    
}

- (void)dealloc{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

#pragma mark - MRKWebBottomBarDelegate -
/*
 * 功能 ： 刷新网页
 */
- (void)onRefreshWithWEB:(MRKWebBottomBar *)controlView{
    [self requestWebData];
}
/*
 * 功能 ： 我的奖品
 */
- (void)onActivityThePrize:(MRKWebBottomBar *)controlView{
    
    BOOL hasPrize = controlView.model.activityPrizeList.count != 0;
    if (hasPrize) {
        
        //有奖品弹窗奖品列表
        MRKActivityPrizeAlert *alert = [[MRKActivityPrizeAlert alloc] initWithAnimationStyle:MRKActionAlertViewTransitionStyleFade];
        alert.model = self.model;
        alert.closeBlock = ^{
            [self onReportWithTheChallenge:controlView];
        };
        [alert show];
        
    }else{
        //无奖品,弹窗排名
        MRKActivityRankAlert *alert = [[MRKActivityRankAlert alloc] initWithAnimationStyle:MRKActionAlertViewTransitionStyleFade];
        alert.model = self.model;
        alert.closeBlock = ^{
            [self onReportWithTheChallenge:controlView];
        };
        [alert show];
    }
}

/*
 * 功能 ： 挑战赛报告
 */
- (void)onReportWithTheChallenge:(MRKWebBottomBar*)controlView{
    NSLog(@"挑战赛报告");
    MRKChallengeModel *ml = controlView.model;
    
    PlanExerciseReportWebController *vc = [[PlanExerciseReportWebController alloc] init];
    vc.httpUrl = MRKAppH5LinkCombine(MRKLinkActivityReport);
    vc.planUserId = controlView.model.activityInfo.planUserId;
    vc.activityId = self.activityId;
    vc.titleStr = [NSString stringWithFormat:@"%@的运动报告", UserInfo.nickName];
    vc.model = ({
        MRKPlanInfoModel *model = [[MRKPlanInfoModel alloc] init];
        model.cid = ml.activityInfo.planId;              ///计划id
        model.associatedId = ml.activityInfo.planUserId; ///计划关联id
        model.activityId = self.activityId;              ///活动id
        model.title = ml.activityInfo.title;             ///标题
        model.mainFigure = ml.activityInfo.mainFigure;   ///主图
        model;
    });
    [self.navigationController pushViewController:vc animated:YES];
}

/*
 * 功能 ： 活动进度
 */
- (void)onProgressWithChallenge:(MRKWebBottomBar*)controlView{
    MRKChallengeModel *ml = controlView.model;
    MRKActionProgressVC *vc = [[MRKActionProgressVC alloc] init];
    vc.model = ({
        MRKPlanInfoModel *model = [[MRKPlanInfoModel alloc] init];
        model.cid = ml.activityInfo.planId;              ///计划id
        model.associatedId = ml.activityInfo.planUserId; ///计划关联id
        model.activityId = self.activityId;              ///活动id
        model.title = ml.activityInfo.title;             ///标题
        model.mainFigure = ml.activityInfo.mainFigure;   ///主图
        model;
    });
    [self.navigationController pushViewController:vc animated:YES];
}

/*
 * 功能 ： 开始挑战,继续挑战
 */
- (void)enterIntoWithChallenge:(MRKWebBottomBar*)controlView{
    [self.navigationController popToRootViewControllerAnimated:YES];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [[NSNotificationCenter defaultCenter] postNotificationName:@"SetTabBarItemIndex" object:@1];
    });
}

/*
 * 功能 ： 报名挑战赛
 */
- (void)toSignUpWithChallenge:(MRKWebBottomBar*)controlView{
    
    MRKChallengeModel *ml = controlView.model;
    
    if (ml.activityInfo.type == 2) {
        MRKNewChallengeEntranceVC *vc= [MRKNewChallengeEntranceVC new];
        vc.activityId = self.activityId;
        vc.model = self.model;
        [self.navigationController pushViewController:vc animated:YES];
    }else{
        
        MRKChallengeEntranceVC *vc= [MRKChallengeEntranceVC new];
        vc.activityId = self.activityId;
        vc.model = self.model;
        [self.navigationController pushViewController:vc animated:YES];
    }
}

/*
 * 功能 ： 报名成功后弹窗
 */
- (void)signUpChallengeSuccessAlert:(MRKWebBottomBar*)controlView{
    
    NSString *stratTime = controlView.model.activityInfo.startTime;
    
    MRKActionSignUpAlert *alert = [[MRKActionSignUpAlert alloc] initWithAnimationStyle:MRKActionAlertViewTransitionStyleFade];
    alert.startTime = [MRKTimeManager stringDatesToMMdd:stratTime];
    alert.closeBlock = ^{
        
    };
    alert.openPlanCOurseBlock = ^{
        MRKCoursePlanController *vc = [[MRKCoursePlanController alloc] init];
        vc.planType = CoursePlanTypeActivity;
        vc.model = ({
            MRKCoursePlanModel *model = [MRKCoursePlanModel new];
            model.cid = controlView.model.activityInfo.planId;
            model.title = controlView.model.activityInfo.title;
            model;
        });
        [self.navigationController pushViewController:vc animated:NO];
    };
    [alert show];
}

/*
 * 功能 ：报名截止
 */
- (void)toSignUpWithChallengeEnd:(MRKWebBottomBar*)controlView{
    [MBProgressHUD showMessage:@"活动报名已截止~" toView:self.view];
}

/*
 * 功能 ：开通会员
 */
- (void)toOpenVipPermissions:(MRKWebBottomBar*)controlView{
    
    MRKChallengeModel *ml = controlView.model;
    if (ml.activityInfo.type == 2) {
        MRKNewChallengeEntranceVC *vc= [MRKNewChallengeEntranceVC new];
        vc.activityId = self.activityId;
        vc.model = self.model;
        [self.navigationController pushViewController:vc animated:YES];
    }else{
        
        MRKChallengeEntranceVC *vc= [MRKChallengeEntranceVC new];
        vc.activityId = self.activityId;
        vc.model = self.model;
        [self.navigationController pushViewController:vc animated:YES];
    }
}


#pragma mark - Action
- (void)leftBtnClick:(UIButton *)sender{
    sender.traceEventId = @"btn_all_return";
    NSLog(@"canGoBack=========%@",self.webView.canGoBack ? @"yes":@"no");
    // 调用父类中的方法进行返回的判断操作
    if (self.webView.canGoBack) {
        [self.webView goBack];
    } else {
        [self.navigationController popViewControllerAnimated:YES];
    }
}



- (void)addAdressPart{
    UIView *v = [[UIView alloc] init];
    v.size = CGSizeMake(kScreenWidth, DHPX(35));
    v.backgroundColor = [UIColor colorWithWhite:0 alpha:0.75];
    v.bottom = self.webView.bottom;
    [self.view addSubview:v];
    
    UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
    btn.backgroundColor = UIColorHex(#62DCFF);
    btn.frame = CGRectMake(kScreenWidth -65 -14, (DHPX(35) - 24)/2, 65, 24);
    [btn setTitle:@"去填写" forState:UIControlStateNormal];
    [btn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    btn.titleLabel.font = [UIFont systemFontOfSize:12 weight:UIFontWeightMedium];
    btn.layer.cornerRadius = 12.0f;
    [[btn rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(id x) {
        MRKAddressViewController *vc = [MRKAddressViewController controllerWithType:MRKAddressTypeActivity];
        vc.activityId = self.activityId;
        vc.commitCallBcak  = ^{
            [self requestWebData];
        };
        [self.navigationController pushViewController:vc animated:YES];
    }];
    [v addSubview:btn];
    
    UILabel *l = [[UILabel alloc] init];
    l.text = @"还未填写收货信息";
    l.textColor = [UIColor whiteColor];
    l.font = [UIFont systemFontOfSize:14];
    l.frame = CGRectMake(14, 0, kScreenWidth -65 -14*3, DHPX(35));
    l.textAlignment = 2;
    [v addSubview:l];
    
    self.adressView = v;
}


/** 背景色 */
- (UIColor *)mrkNavigationBarBackgroundColor:(MRKNavigationBar *)navigationBar{
    return UIColorHex(#384364);
}

/** 导航条右边的按钮 */
- (UIImage *)mrkNavigationBarRightButtonImage:(UIButton *)rightButton navigationBar:(MRKNavigationBar *)navigationBar{
    rightButton.contentHorizontalAlignment = UIControlContentHorizontalAlignmentRight;
    return [UIImage imageNamed:@"icon_share_W"];
}

/** 右边的按钮的点击 */
- (void)rightButtonEvent:(UIButton *)sender navigationBar:(MRKNavigationBar *)navigationBar{
    [self shareBody];
}


- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

@end








#import "UIView+AZGradient.h"

@interface MRKWebBottomBar()
@property (strong, nonatomic) UIButton *actionBtn1;
@property (strong, nonatomic) UIButton *actionBtn2;
@property (strong, nonatomic) UIButton *actionBtn3;
@end

@implementation MRKWebBottomBar

- (instancetype)init{
    self = [super init];
    if (self) {
        self.backgroundColor = [UIColor colorWithHexString:@"#384364"];
        
        [self addSubview:self.actionBtn1];
        [self addSubview:self.actionBtn2];
        [self addSubview:self.actionBtn3];
    }
    return self;
}

- (void)layoutSubviews{
    [super layoutSubviews];
}

- (void)dealloc {
    [MRKTimerManager cancelTimer];
}

- (void)setModel:(MRKChallengeModel *)model{
    _model = model;
    
    MRKActivityInfoModel *activityInfo = model.activityInfo;
    /*未上线，2进行中，3已结束*/
    switch (activityInfo.onlineStatus) {
        case 1:{
            
            if (model.isSignUp) {///已报名
                
                ///按钮状态改为 报名成功,X天后活动正式开始 , 一天时间内按照[报名成功,23:59:34后活动正式开始
                if (model.seconds > 24*60*60) {
                    
                    NSInteger dayTime = 24*60*60;
                    CGFloat days = (CGFloat)model.seconds / dayTime;
                    int day = floor(days);
                    [self setActionBtnStatus:[NSString stringWithFormat:@"报名成功, %d天后挑战赛正式开始",day]];
                    [self.actionBtn1 addTarget:self action:@selector(signUpChallengeSuccessAlert) forControlEvents:UIControlEventTouchUpInside];
                }else{
                    
                    if (model.seconds <= 0) {
                        [self setActionBtnStatus:@"报名成功, 等待挑战赛开始"];
                        [self.actionBtn1 addTarget:self action:@selector(signUpChallengeSuccessAlert) forControlEvents:UIControlEventTouchUpInside];
                        return;
                    }
                    
                    @weakify(self);
                    [MRKTimerManager initGCDWithSeconds:model.seconds ongoingBlock:^(NSInteger seconds) {
                        @strongify(self);
                        NSString *str_hour = [NSString stringWithFormat:@"%02ld",seconds/3600];
                        NSString *str_minute = [NSString stringWithFormat:@"%02ld",(seconds%3600)/60];
                        NSString *str_second = [NSString stringWithFormat:@"%02ld",seconds%60];
                        NSString *format_time = [NSString stringWithFormat:@"%@:%@:%@",str_hour,str_minute,str_second];
                        [self setActionBtnStatus:[NSString stringWithFormat:@"报名成功, %@后挑战赛正式开始",format_time]];
                    } endBlock:^{
                        @strongify(self);
                        //刷新网页
                        if (self.delegate && [self.delegate respondsToSelector:@selector(onRefreshWithWEB:)]) {
                            [self.delegate onRefreshWithWEB:self];
                        }
                    }];
                    [self.actionBtn1 addTarget:self action:@selector(signUpChallengeSuccessAlert) forControlEvents:UIControlEventTouchUpInside];
                }
                
            } else { ///未报名
                
                ///活动已结束 或者  报名库存为0
                if (model.isSignUpEnd || model.remaining == 0) {
                    
                    ///按钮状态为报名已截止
                    [self setActionBtnStatus:@"报名已截止"];
                    [self.actionBtn1 addTarget:self action:@selector(toSignUpWithChallengeEnd) forControlEvents:UIControlEventTouchUpInside];
                    
                } else {
                    
                    ///判断活动需要VIP权限和不是会员
                    if (activityInfo.isVip && !model.isMember) {
                        
                        [self setActionBtnStatus:@"开通会员后即可报名挑战赛"];
                        [self.actionBtn1 addTarget:self action:@selector(toOpenVipPermissions) forControlEvents:UIControlEventTouchUpInside];
                    }else{
                        
                        [self setActionBtnStatus:@"马上报名"];
                        [self.actionBtn1 addTarget:self action:@selector(toSignUpWithChallenge) forControlEvents:UIControlEventTouchUpInside];
                    }
                }
            }
            
        }break;
        case 2:{
            
            if (!model.isSignUp) { ///如果没有报名
                [self setActionBtnStatus:@"报名已截止"];
                [self.actionBtn1 addTarget:self action:@selector(toSignUpWithChallengeEnd) forControlEvents:UIControlEventTouchUpInside];
                return;
            }
            
            NSString *activityKey = [NSString stringWithFormat:@"begainActivity_%@_%@",model.activityInfo.cid,UserInfo.userId];
            NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
            if([defaults boolForKey:activityKey]) {
                
                ///判断用户是否获得奖品
                [self setBottomStatusWithAwards:model.activityPrizeList.count != 0];
            } else {
                [defaults setBool:YES forKey:activityKey];
                [defaults synchronize];
                
                ///第一次进入 显示开始挑战
                [self setActionBtnStatus:@"开始挑战"];
                [self.actionBtn1 addTarget:self action:@selector(enterIntoWithChallenge) forControlEvents:UIControlEventTouchUpInside];
            }
            
        }break;
        case 3:{
            
            ///首次进入弹窗告诉用户是否获奖
            if (model.myRank.rank > 0) {
                NSString *hasPrizeKey = [NSString stringWithFormat:@"hasPrize_%@_%@",model.activityInfo.cid,UserInfo.userId];
                NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
                if(![defaults boolForKey:hasPrizeKey]) {
                    [defaults setBool:YES forKey:hasPrizeKey];
                    [defaults synchronize];
                    
                    if (self.delegate && [self.delegate respondsToSelector:@selector(onActivityThePrize:)]) {
                        [self.delegate onActivityThePrize:self];
                    }
                }
            }
            
            
            ///判断用户是否获得奖品
            if (model.activityPrizeList.count == 0) {
                ///展示挑战赛报告
                [self setActionBtnStatus:@"挑战赛报告"];
                [self.actionBtn1 addTarget:self action:@selector(onReportWithTheChallenge) forControlEvents:UIControlEventTouchUpInside];
            } else {
                ///展示我的奖品和挑战赛报告
                [self  setBottomStatusActivityEnd];
            }
            
        }break;
        default:
            break;
    }
}


- (void)setActionBtnStatus:(NSString *)title {
    
    CGFloat padding = (DHPX(60)-DHPX(44))/2;
    [self.actionBtn1 mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.mas_top).offset(padding);
        make.height.mas_equalTo(DHPX(44));
        make.width.mas_equalTo(kScreenWidth - DHPX(45)*2);
        make.centerX.mas_equalTo(self.mas_centerX);
    }];
    
    [self.actionBtn1 az_setGradientBackgroundWithColors:@[[UIColor colorWithHexString:@"#FE6040"],[UIColor colorWithHexString:@"#FF7F65"]]
                                              locations:nil
                                             startPoint:CGPointMake(1, 0)
                                               endPoint:CGPointMake(0, 0)];
    [self.actionBtn1 setTitle:title forState:UIControlStateNormal];
    [self.actionBtn1 setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
}

- (void)setBottomStatusActivityEnd {
    
    CGFloat btnWidth = (kScreenWidth -DHPX(20) -DHPX(20) -DHPX(10))/2;
    CGFloat padding = (DHPX(60)-DHPX(44))/2;
    [self.actionBtn1 mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(DHPX(20));
        make.top.mas_equalTo(self.mas_top).offset(padding);
        make.height.mas_equalTo(DHPX(44));
        make.width.mas_equalTo(btnWidth);
    }];
    [self.actionBtn1 az_setGradientBackgroundWithColors:@[[UIColor colorWithHexString:@"#FE6040"],[UIColor colorWithHexString:@"#FF7F65"]]
                                              locations:nil
                                             startPoint:CGPointMake(1, 0)
                                               endPoint:CGPointMake(0, 0)];
    [self.actionBtn1 setTitle:@"我的奖品" forState:UIControlStateNormal];
    [self.actionBtn1 addTarget:self action:@selector(onActivityThePrize) forControlEvents:UIControlEventTouchUpInside];
    
    [self.actionBtn2 mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(self.actionBtn1.mas_right).offset(DHPX(10));
        make.centerY.mas_equalTo(self.actionBtn1.mas_centerY);
        make.height.mas_equalTo(DHPX(44));
        make.width.mas_equalTo(btnWidth);
    }];
    self.actionBtn2.backgroundColor = [UIColor clearColor];
    self.actionBtn2.layer.borderWidth = 2.0f;
    self.actionBtn2.layer.borderColor = UIColorHex(#E99F8F).CGColor;
    [self.actionBtn2 setTitle:@"挑战赛报告" forState:UIControlStateNormal];
    [self.actionBtn2 setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    [self.actionBtn2 addTarget:self action:@selector(onReportWithTheChallenge) forControlEvents:UIControlEventTouchUpInside];
}


- (void)setBottomStatusWithAwards:(BOOL)hasAwards {
    
    if (hasAwards) {
        
        CGFloat btnWidth = (kScreenWidth -DHPX(20) -DHPX(20) -DHPX(10) - DHPX(10))/3;
        CGFloat padding = (DHPX(60)-DHPX(44))/2;
        
        [self.actionBtn1 mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.mas_equalTo(DHPX(20));
            make.top.mas_equalTo(self.mas_top).offset(padding);
            make.height.mas_equalTo(DHPX(44));
            make.width.mas_equalTo(btnWidth);
        }];
        [self.actionBtn1 az_setGradientBackgroundWithColors:@[[UIColor colorWithHexString:@"#FE6040"],[UIColor colorWithHexString:@"#FF7F65"]]
                                                  locations:nil
                                                 startPoint:CGPointMake(1, 0)
                                                   endPoint:CGPointMake(0, 0)];
        [self.actionBtn1 setTitle:@"我的奖品" forState:UIControlStateNormal];
        [self.actionBtn1 addTarget:self action:@selector(onActivityThePrize) forControlEvents:UIControlEventTouchUpInside];
        
        
        [self.actionBtn2 mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.mas_equalTo(self.actionBtn1.mas_right).offset(DHPX(10));
            make.centerY.mas_equalTo(self.actionBtn1.mas_centerY);
            make.height.mas_equalTo(DHPX(44));
            make.width.mas_equalTo(btnWidth);
        }];
        [self.actionBtn2 az_setGradientBackgroundWithColors:@[[UIColor colorWithHexString:@"#FE6040"],[UIColor colorWithHexString:@"#FF7F65"]]
                                                  locations:nil
                                                 startPoint:CGPointMake(1, 0)
                                                   endPoint:CGPointMake(0, 0)];
        [self.actionBtn2 setTitle:@"继续挑战" forState:UIControlStateNormal];
        [self.actionBtn2 addTarget:self action:@selector(enterIntoWithChallenge) forControlEvents:UIControlEventTouchUpInside];
        
        
        [self.actionBtn3 mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.mas_equalTo(self.actionBtn2.mas_right).offset(DHPX(10));
            make.centerY.mas_equalTo(self.actionBtn1.mas_centerY);
            make.height.mas_equalTo(DHPX(44));
            make.width.mas_equalTo(btnWidth);
        }];
        self.actionBtn3.backgroundColor = [UIColor clearColor];
        self.actionBtn3.layer.borderWidth = 2.0f;
        self.actionBtn3.layer.borderColor = UIColorHex(#E99F8F).CGColor;
        [self.actionBtn3 setTitle:@"活动进度" forState:UIControlStateNormal];
        [self.actionBtn3 setTitleColor:UIColorHex(#E99F8F) forState:UIControlStateNormal];
        [self.actionBtn3 addTarget:self action:@selector(onProgressWithChallenge) forControlEvents:UIControlEventTouchUpInside];
        
    }else{
        
        CGFloat btnWidth = (kScreenWidth -DHPX(20) -DHPX(20) -DHPX(10))/2;
        CGFloat padding = (DHPX(60)-DHPX(44))/2;
        [self.actionBtn1 mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.mas_equalTo(DHPX(20));
            make.top.mas_equalTo(self.mas_top).offset(padding);
            make.height.mas_equalTo(DHPX(44));
            make.width.mas_equalTo(btnWidth);
        }];
        [self.actionBtn1 az_setGradientBackgroundWithColors:@[[UIColor colorWithHexString:@"#FE6040"],[UIColor colorWithHexString:@"#FF7F65"]]
                                                  locations:nil
                                                 startPoint:CGPointMake(1, 0)
                                                   endPoint:CGPointMake(0, 0)];
        [self.actionBtn1 setTitle:@"继续挑战" forState:UIControlStateNormal];
        [self.actionBtn1 addTarget:self action:@selector(enterIntoWithChallenge) forControlEvents:UIControlEventTouchUpInside];
        
        [self.actionBtn2 mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.mas_equalTo(self.actionBtn1.mas_right).offset(DHPX(10));
            make.centerY.mas_equalTo(self.actionBtn1.mas_centerY);
            make.height.mas_equalTo(DHPX(44));
            make.width.mas_equalTo(btnWidth);
        }];
        self.actionBtn2.backgroundColor = [UIColor clearColor];
        self.actionBtn2.layer.borderWidth = 2.0f;
        self.actionBtn2.layer.borderColor = UIColorHex(#E99F8F).CGColor;
        [self.actionBtn2 setTitle:@"活动进度" forState:UIControlStateNormal];
        [self.actionBtn2 setTitleColor:UIColorHex(#E99F8F) forState:UIControlStateNormal];
        [self.actionBtn2 addTarget:self action:@selector(onProgressWithChallenge) forControlEvents:UIControlEventTouchUpInside];
    }
    
}


/*
 * 功能 ： 刷新网页
 */
- (void)onRefreshWithWEB{
    if (self.delegate && [self.delegate respondsToSelector:@selector(onRefreshWithWEB:)]) {
        [self.delegate onRefreshWithWEB:self];
    }
}
/*
 * 功能 ： 我的奖品
 */
- (void)onActivityThePrize{
    if (self.delegate && [self.delegate respondsToSelector:@selector(onActivityThePrize:)]) {
        [self.delegate onActivityThePrize:self];
    }
}

/*
 * 功能 ： 挑战赛报告
 */
- (void)onReportWithTheChallenge{
    if (self.delegate && [self.delegate respondsToSelector:@selector(onReportWithTheChallenge:)]) {
        [self.delegate onReportWithTheChallenge:self];
    }
}

/*
 * 功能 ： 活动进度
 */
- (void)onProgressWithChallenge{
    if (self.delegate && [self.delegate respondsToSelector:@selector(onProgressWithChallenge:)]) {
        [self.delegate onProgressWithChallenge:self];
    }
}

/*
 * 功能 ： 开始挑战,继续挑战
 */
- (void)enterIntoWithChallenge{
    if (self.delegate && [self.delegate respondsToSelector:@selector(enterIntoWithChallenge:)]) {
        [self.delegate enterIntoWithChallenge:self];
    }
}

/*
 * 功能 ： 报名挑战赛
 */
- (void)toSignUpWithChallenge{
    if (self.delegate && [self.delegate respondsToSelector:@selector(toSignUpWithChallenge:)]) {
        [self.delegate toSignUpWithChallenge:self];
    }
}

/*
 * 功能 ： 报名成功后弹窗
 */
- (void)signUpChallengeSuccessAlert{
    if (self.delegate && [self.delegate respondsToSelector:@selector(signUpChallengeSuccessAlert:)]) {
        [self.delegate signUpChallengeSuccessAlert:self];
    }
}

/*
 * 功能 ：报名截止
 */
- (void)toSignUpWithChallengeEnd{
    if (self.delegate && [self.delegate respondsToSelector:@selector(toSignUpWithChallengeEnd:)]) {
        [self.delegate toSignUpWithChallengeEnd:self];
    }
}

/*
 * 功能 ：开通会员
 */
- (void)toOpenVipPermissions{
    if (self.delegate && [self.delegate respondsToSelector:@selector(toOpenVipPermissions:)]) {
        [self.delegate toOpenVipPermissions:self];
    }
}



#pragma mark - Lazy -

- (UIButton *)actionBtn1{
    if (!_actionBtn1) {
        UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
        button.titleLabel.font = [UIFont systemFontOfSize:15 weight:UIFontWeightMedium];
        [button setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        button.tag = 1000;
        button.layer.cornerRadius = DHPX(44)/2;
        _actionBtn1 = button;
    }
    return _actionBtn1;
}

- (UIButton *)actionBtn2{
    if (!_actionBtn2) {
        UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
        button.titleLabel.font = [UIFont systemFontOfSize:15 weight:UIFontWeightMedium];
        [button setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        button.tag = 2000;
        button.layer.cornerRadius = DHPX(44)/2;
        _actionBtn2 = button;
    }
    return _actionBtn2;
}

- (UIButton *)actionBtn3{
    if (!_actionBtn3) {
        UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
        button.titleLabel.font = [UIFont systemFontOfSize:15 weight:UIFontWeightMedium];
        [button setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        button.tag = 2000;
        button.layer.cornerRadius = DHPX(44)/2;
        _actionBtn3 = button;
    }
    return _actionBtn3;
}

@end
