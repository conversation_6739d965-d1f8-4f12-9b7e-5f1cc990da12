//
//  IntroducePeopleWebView.m
//  Student_IOS
//
//  Created by MacPro on 2021/5/2.
//
#import "IntroducePeopleWebView.h"
#import "MRKTraceManager.h"
#import "MRKConvenientFunc.h"
#import "MRKWebJSHandler.h"

@interface IntroducePeopleWebView ()
@end

@implementation IntroducePeopleWebView

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self.webView mas_updateConstraints:^(MASConstraintMaker *make) {
        float paddingTop = kNavBarHeight;
        make.top.mas_equalTo(self.mrkContentView.mas_top).offset(paddingTop);
    }];

    [self registerJSHandler];
    
    NSURL *url = [NSURL URLWithString:MRKAppH5LinkCombine(MRKLinkGuideNewUser)];
    NSURLRequest *request = [NSURLRequest requestWithURL:url];
    [self.webView loadRequest:request];
}

- (NSString *)idString{
    NSString *strId = @"";
    if ([self.showInsideType isEqualToString:@"fromExercise"]) {
        strId = [self.H5Dict objectForKey:@"productId"];
    }else{
        strId = [self.H5Dict objectForKey:@"equipTypeId"];
    }
    return strId;
}


- (void)registerJSHandler {
    [[MRKWebJSHandler shared] registerJSMethodHandler:self.bridge base:self];
}

- (NSMutableDictionary *)callBackParms {
    NSDictionary *parms = @{
        @"token"        : UserInfo.token ?: @"",
        @"id"           : self.idString ?: @"",
        @"bottomType"   : IS_IPHONEX_SURE ? @"1" : @"0" ,
        @"terminal"     : @(kTerminal) ,
        @"version"      : [MRKConvenientFunc getAppShortVersion],
        @"traceDeviceId": [MRKConvenientFunc getDeviceUUID],
        @"traceTerminal": @([MRKConvenientFunc getCurrentTerminalIndex]),
        @"traceRoute"   : [MRKTraceManager sharedInstance].traceRoute,
        @"globalSoundFlag":@([[[NSUserDefaults standardUserDefaults] valueForKey:@"SaveH5AIGlobalSoundFlag"] intValue]),
    };
    return parms.mutableCopy;
}

- (NSString *)activityId {
    return self.idString;
}

- (void)webView:(MrkWebView *)webView didLoadWithTitle:(NSString *)title {
    self.navTitle = title;
    self.navTitleColor = MainTextColor;
}








#pragma mark ---------Delegate -----------
- (BOOL)viewControllerIsNeedNavBar:(MRKBaseController *)viewController {
    return YES;
}
/** 背景色 */
- (UIColor *)mrkNavigationBarBackgroundColor:(MRKNavigationBar *)navigationBar{
    return UIColorHex(#ffffff);
}

- (UIImage *)mrkNavigationBarLeftButtonImage:(UIButton *)leftButton navigationBar:(MRKNavigationBar *)navigationBar {
    return [UIImage imageNamed:@"icon_back"];
}

- (void)dealloc {
    
}

@end

