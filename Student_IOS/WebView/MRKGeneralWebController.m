//
//  MRKGeneralWebController.m
//  Student_IOS
//
//  Created by merit on 2022/9/7.
//

#import "MRKGeneralWebController.h"
#import "MRKShareView.h"
#import "MRKWebJSHandler.h"

@interface MRKGeneralWebController ()
@property (nonatomic, strong) NSString *realTrueUrlString;
@end

@implementation MRKGeneralWebController

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self.webView.realWebView.configuration.userContentController removeAllUserScripts];
    self.webView.scalesPageToFit = YES;
    [self.webView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.mrkContentView.mas_top).offset(kNavBarHeight);
    }];

    [self loadWebMainPage:self.httpUrl];
    
    //侧滑返回事件
    @weakify(self)
    self.fd_interactivePopBlock = ^BOOL(UIPanGestureRecognizer *pan) {
        [self_weak_ backAction];
        return NO;
    };
}

- (void)loadWebMainPage:(NSString *)httpStr{
    NSMutableURLRequest *requset = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:httpStr]
                                                           cachePolicy:NSURLRequestUseProtocolCachePolicy
                                                       timeoutInterval:20];
    [self.webView loadRequest:requset];
}

- (void)backButtonClick {
    // xr
}

- (void)backAction {
    [self.bridge callHandler:[MRKWebJSHandler operatorStr: MRKWebJSCallBackMethodOnBack] data:nil responseCallback:^(id responseData) {}];
}

- (void)back {
    if (self.navigationController) {
        [self.navigationController popViewControllerAnimated:YES];
    } else {
        [self dismissViewControllerAnimated:YES completion:nil];
    }
}

///自定义动画
- (CATransition *)popTransition {
    CATransition *transition = [[CATransition alloc] init];
    transition.duration = 0.25;
    transition.type = kCATransitionPush;
    transition.subtype = kCATransitionFromLeft;
    return transition;
}

#pragma mark - WebViewDelegate
- (void)webView:(MrkWebView *)webView didLoadWithTitle:(NSString *)title{
    self.navTitle = title;
}





#pragma mark - Delegate
- (BOOL)viewControllerIsNeedNavBar:(MRKBaseController *)viewController {
    return YES;
}

- (UIStatusBarStyle)navControllerStatusBarStyle:(MRKBaseController *)viewController{
    return UIStatusBarStyleDefault;
}

- (UIView *)mrkNavigationBarLeftView:(MRKNavigationBar *)navigationBar{
    UIView *v = [[UIView alloc] init];
    v.frame = CGRectMake(0, 0, 60, 44);
    
    UIButton *backBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    backBtn.frame = CGRectMake( 0, 0, 30, 44);
    [backBtn setImage:[UIImage imageNamed:@"icon_back"] forState:UIControlStateNormal];
    [backBtn addTarget:self action:@selector(btnClick:) forControlEvents:UIControlEventTouchUpInside];
    backBtn.tag = 2000;
    [v addSubview:backBtn];
    
    UIButton *closeBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    closeBtn.frame = CGRectMake( 30, 0, 30, 44);
    [closeBtn setImage:[UIImage imageNamed:@"icon_back-3"] forState:UIControlStateNormal];
    [closeBtn addTarget:self action:@selector(btnClick:) forControlEvents:UIControlEventTouchUpInside];
    closeBtn.tag = 3000;
    [v addSubview:closeBtn];
    
    return v;
}

- (void)btnClick:(UIButton *)sender{
    NSInteger tag = sender.tag;
    if (tag == 2000) {
        if (self.webView.canGoBack) {
            [self.webView goBack];
            return;
        }
    }
    
    [self.navigationController popViewControllerAnimated:YES];
}

- (UIColor *)mrkNavigationBarBackgroundColor:(MRKNavigationBar *)navigationBar{
    return [UIColor colorWithWhite:1 alpha:1.0];
}

/** 导航条右边的按钮 */
- (UIImage *)mrkNavigationBarRightButtonImage:(UIButton *)rightButton navigationBar:(MRKNavigationBar *)navigationBar{
    rightButton.contentHorizontalAlignment = UIControlContentHorizontalAlignmentRight;
    return [UIImage imageNamed:@"icon_share_B"];
}

/** 右边的按钮的点击 */
- (void)rightButtonEvent:(UIButton *)sender navigationBar:(MRKNavigationBar *)navigationBar{
    sender.traceEventId = @"btn_all_share";
    [self shareBody];
}

///获去分享数据
- (void)shareBody {
//    MRKShareView *view = [MRKShareView shareViewWithType:ShareActionViewReport andViewController:self];
//    view.shareUrl = self.realTrueUrlString?:self.httpUrl;
//    view.shareTitle = self.navTitle;
//    view.shareDescrip = self.navTitle;;
//    view.images = [UIImage imageNamed:@"merach_shop_logo"];
//    [view showInView:self.view];
    [FlutterManager shareCommonVCWithTitle:self.navTitle 
                                      text:self.navTitle
                                      link:self.realTrueUrlString?:self.httpUrl
                                thumbImage:@"merach_shop_logo"];
}

- (void)dealloc{
    
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

@end
