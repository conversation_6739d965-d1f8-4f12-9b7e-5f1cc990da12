//
//  ExerciseReportWebController.m
//  Student_IOS
//
//  Created by merit on 2021/10/12.
//

#import "ExerciseReportWebController.h"
#import "MRKCourseModel.h"
#import "UIScrollView+FDFullscreenPopGesture.h"
#import "UIImage+Helper.h"
#import "MRKExerciseFeelView.h"
#import "MRKShareImageController.h"
#import "UIViewController+ShareImage.h"
#import "MRKConvenientFunc.h"
#import "MRKShareButtonsView.h"
#import "MRKTraceManager.h"
#import "MRKToolKit.h"
#import "MRKNewShareController.h"
#import "UIDevice+YYAdd.h"
#import "MRKWebJSHandler.h"

@interface ExerciseReportWebController ()
@property (nonatomic, strong) NSString *realTrueUrlString;
@property (nonatomic, assign) BOOL showFell;  //是否弹出过训练感受
@end

@implementation ExerciseReportWebController

- (NSMutableArray<ExerciseReportShareSnippetModel *> *)shareJsons {
    if (!_shareJsons) {
        _shareJsons = [NSMutableArray array];
    }
    return _shareJsons;
}

- (BOOL)shouldAutorotate {
    if ([UIDevice currentDevice].isPad){
        return YES;
    }
    if (@available(iOS 16.0, *)) {
    } else {
        return YES;
    }
    return NO;
}

/// 当前控制器支持的屏幕方向
- (UIInterfaceOrientationMask)supportedInterfaceOrientations {
    if ([UIDevice currentDevice].isPad){
        return UIInterfaceOrientationMaskAll;
    }
    return UIInterfaceOrientationMaskPortrait;
}

- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    
    //关闭手势
    self.fd_interactivePopDisabled = YES;
    MLog(@"ExerciseReportWebController__viewWillAppear");
    
    //发送关闭设备蓝牙交互的通知
    [[NSNotificationCenter defaultCenter] postNotificationName:@"DeviceEndConnect" object:nil];
    
    //移除弹窗
    if ([[UIViewController keywindow] viewWithTag:0x888888]) {
        UIView *quiteView = [[UIViewController keywindow] viewWithTag:0x888888];
        [quiteView removeFromSuperview];
    }
    
    ///iphone 强制竖屏
    if (UI_USER_INTERFACE_IDIOM() == UIUserInterfaceIdiomPhone){
        [self forceOrientationPortrait];
    }
}

/// 监听到截屏调起h5的方法
- (void)didTakeScreenshot:(NSNotification *)notification {
    ReportMrkLogParms(2, @"截屏", @"page_exercise_report", @"btn_execise_report_screen_capture", nil, 0, nil);
    if (self.showShare) {
        return;
    }
    [self.bridge callHandler:[MRKWebJSHandler operatorStr: MRKWebJSCallBackMethodOnTrainingReportShare] data:nil];
}

- (void)viewWillDisappear:(BOOL)animated{
    [super viewWillDisappear:animated];
    //开启手势
    self.fd_interactivePopDisabled = NO;
}

- (void)viewDidAppear:(BOOL)animated{
    [super viewDidAppear:animated];
  
    ///移除测评页面
    NSMutableArray *tempArr = [[NSMutableArray alloc] initWithArray:self.navigationController.viewControllers];
    NSMutableArray *array = tempArr.mutableCopy;
    [array enumerateObjectsUsingBlock:^(id  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        UIViewController *controller = (UIViewController *)obj;
        if ([controller isKindOfClass:NSClassFromString(@"MRKFreeTrainingController")] ||
            [controller isKindOfClass:NSClassFromString(@"MRKRouteTrainController")] ||
            [controller isKindOfClass:NSClassFromString(@"MRKVideoController")] ||
            [controller isKindOfClass:NSClassFromString(@"MRKVerticalVideoController")] ||
            [controller isKindOfClass:NSClassFromString(@"MRKGameVideoController")] ||
            [controller isKindOfClass:NSClassFromString(@"MrkPlotVideoController")] ||
            [controller isKindOfClass:NSClassFromString(@"MRKVideoExperienceController")] ||
            [controller isKindOfClass:NSClassFromString(@"MRKHrCtrlSportsController")] ||
            [controller isKindOfClass:NSClassFromString(@"MRKUltraTrainController")] ||
            [controller isKindOfClass:NSClassFromString([FlutterMotionPlayController sc_className])])
        {
            [tempArr removeObject:controller];
            *stop = YES;
        }
    }];
    [self.navigationController setViewControllers:tempArr animated:YES];
    
    
    /// 训练感受
    [self trainFeel];
    
    
    ///新手链路中屏蔽掉弹窗
    if (!self.firstInto){
        // 请求用户等级是否升级 2022-7-13 --wk
        [MRKGrowAlertManager postLevelAndMedalRequest];
    }
}

///训练感受
- (void)trainFeel {
    if (_showFell) {
        return;
    }
    
    if (self.courseType.intValue == 1 && self.fromTrainingView && !self.quitMidway) {
        ///训练感受
        if (self.liveCourseModel.isRealVideoCourse) {
            return;
        }
        
        ///比赛不弹训练感受
        if (self.liveCourseModel.type.integerValue == 3 
            || self.liveCourseModel.type.integerValue == 4) {
            return;
        }
        
        MRKExerciseFeelView *vc = [MRKExerciseFeelView exerciseFeelWithController:self];
        vc.courseId = self.liveCourseModel.courseId;
        [vc showInView:self.mrkContentView];
        _showFell = YES;
    }
}

- (void)viewDidLoad {
    self.tracePageId = @"page_exercise_report";
    [super viewDidLoad];
    
    ///截屏交互
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(didTakeScreenshot:) name:UIApplicationUserDidTakeScreenshotNotification object:nil];
    
    self.navTitle = @"";
    self.navTitleColor = [UIColor whiteColor];
    
    ///训练报告过来带的数据
    if (self.trainModel != nil) {
        self.courseType = self.trainModel.type;
        self.exerciseID = self.trainModel.idd;
        self.fromTrainingView = NO;
        self.courseName = self.trainModel.title;
        self.courseImage = self.trainModel.courseImageUrl;
        self.equipmentId = self.trainModel.equipmentId;
    }
    
    if (![self.courseName isNotBlank]) {
        self.courseName = self.liveCourseModel.name;
    }
    
    [self registerJSHandler];
    
    ///加载
    [self webLoadRequest];
    
    // 预加载flutter 分享引擎
//    [FlutterManager sharePreloadEngine];
//    [FlutterManager shareDeleteEngine];
}

- (void)webLoadRequest{
    NSString *url = self.reportUrl ?: MRKAppH5LinkCombine(MRKLinkTrainingReport);
    NSLog(@"ExerciseReportWebController webLoadRequest url====== %@", url);
    NSMutableURLRequest *requset = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:url]
                                                           cachePolicy:NSURLRequestUseProtocolCachePolicy
                                                       timeoutInterval:15];
    [self.webView loadRequest:requset];
}

- (NSMutableDictionary *)callBackParms {
    NSMutableDictionary *parms = @{
        @"token"        : UserInfo.token,
        @"id"           : self.exerciseID ?:@"",
        @"bottomType"   : IS_IPHONEX_SURE ? @"1":@"0",
        @"version"      : [MRKConvenientFunc getAppShortVersion],///app版本
        @"User-Agent"   : [NSString stringWithFormat:@"%@,H5", [TGApiManager userAgentString]],
        @"traceDeviceId": [MRKConvenientFunc getDeviceUUID],
        @"traceTerminal": @([MRKConvenientFunc getCurrentTerminalIndex]),
        @"traceRoute"   : [MRKTraceManager sharedInstance].traceRoute,
        @"globalSoundFlag":@([[[NSUserDefaults standardUserDefaults] valueForKey:@"SaveH5AIGlobalSoundFlag"] intValue]),
    }.mutableCopy;
    
    ///是否训练页面过来
    if (self.fromTrainingView){
        [parms setObject:@"1" forKey:@"isNew"];
    }
    
    ///新链路添加
    if (self.firstInto){
        [parms setObject:@"1" forKey:@"firstInto"];
    }
    
    if (self.date.length > 0) {
        [parms setObject:self.date forKey:@"date"];
    }
    
    if ([self.liveCourseModel.type isNotBlank]) {
        [parms setValue:self.liveCourseModel.type forKey:@"courseType"];
    }
    return parms.mutableCopy;
}

- (void)registerJSHandler{
    [[MRKWebJSHandler shared] registerJSMethodHandler:self.bridge base:self];
}

- (NSString *)activityId {
    return self.exerciseID;
}
///
- (void)closeWebPage{
//    [self.navigationController popViewControllerAnimated:YES];
    
    UINavigationController *nav = self.navigationController;
    if (nav) {
        if (nav.viewControllers.count > 1) {
            if ([nav.viewControllers.lastObject isEqual:self]) {
                [nav popViewControllerAnimated:YES];
            }
        } else {
            [nav dismissViewControllerAnimated:YES completion:nil];
        }
    } else {
        [self dismissViewControllerAnimated:YES completion:nil];
    }
    
    [[NSNotificationCenter defaultCenter] postNotificationName:@"popToBeforePage" object:nil];
}


///分享长图
///分享长图/短图
- (void)shareImage:(UIImage *)shareImage simple:(UIImage *)simpleImage {
    if ([self.liveCourseModel.cover isNotBlank]) {
        @weakify(self);
        [MRKToolKit imageDownLoad:self.liveCourseModel.cover andSucBlock:^(UIImage *image) {
            @strongify(self);
            [self openShareController:shareImage andCourseImage:image andSimpleImage:simpleImage];
        }];
    } else if ([self.courseImage isNotBlank]) {
        @weakify(self);
        [MRKToolKit imageDownLoad:self.courseImage andSucBlock:^(UIImage *image) {
            @strongify(self);
            [self openShareController:shareImage andCourseImage:image andSimpleImage:simpleImage];
        }];
    } else { ///自由训练
        [self openShareController:shareImage andCourseImage:nil andSimpleImage:simpleImage];
    }
}

/// 分享
- (void)share:(id)data {
    // 接收json，在比对json是否接收完
    ExerciseReportShareSnippetModel *model = [ExerciseReportShareSnippetModel modelWithDictionary:data];
    [self.shareJsons addObject:model];
    if (self.shareJsons.count < model.totalPieces) {
        return;
    }
    
    // 排序
    self.shareJsons = [self.shareJsons sortedArrayUsingComparator:^NSComparisonResult(ExerciseReportShareSnippetModel*  _Nonnull obj1, ExerciseReportShareSnippetModel*  _Nonnull obj2) {
        return obj1.pieceNumber > obj2.pieceNumber;
    }].mutableCopy;
    
    // 拼接json，分享
    NSMutableString *jsonString = [NSMutableString string];
    for (ExerciseReportShareSnippetModel *m in self.shareJsons) {
        [jsonString appendString:m.dataPiece];
    }
    NSLog(@"整理好的分享json %@",jsonString);
    [self.shareJsons removeAllObjects];
    NSDictionary *dic = [NSDictionary dictionaryWithJsonString:jsonString];
    NSString *simpleImageStr = [dic valueForKeyPath:@"simple"]; // 简略图片
    NSString *detailImageStr = [dic valueForKeyPath:@"detail"]; // 详细图片
    dispatch_async(dispatch_get_main_queue(), ^{
        [self flutterShare:detailImageStr simle:simpleImageStr];
    });
}

/// Flutter 分享
/// - Parameters:
///   - detailString: 大图
///   - simleString: 简图
- (void)flutterShare:(NSString *)detailString simle:(NSString *)simleString {
    if (self.showShare) {
        return;
    }
    NSString *url;
    if ([self.liveCourseModel.cover isNotBlank]) {
        url = self.liveCourseModel.cover;
    } else if ([self.courseImage isNotBlank]) {
        url = self.courseImage;
    }
    FlutterShareController *vc = [FlutterManager shareCustom];
    [vc receiveH5CodeWithLong:detailString sheet:simleString courseUrl:url trainId:self.exerciseID];
    @weakify(self);
    vc.dismissBlock = ^{
        self_weak_.showShare = NO;
    };
    
    MRKNavigationController *nav = [[MRKNavigationController alloc] initWithRootViewController:vc];
    nav.modalPresentationStyle = UIModalPresentationFullScreen;
    [self presentViewController:nav animated:YES completion:nil];
    self.showShare = YES;
}

- (void)openShareController:(UIImage *)shareImage andCourseImage:(UIImage *)courseImage andSimpleImage:(UIImage *)simpleImage{
    NSLog(@"courseImage ===== %@", courseImage);
    jxt_getSafeMainQueue(^{
        MRKNewShareController *vc = [[MRKNewShareController alloc] init];
        vc.modalPresentationStyle =  UIModalPresentationFullScreen;
        vc.shareImage = shareImage;
        vc.courseImage = courseImage;
        vc.exerciseID = self.exerciseID;
        vc.equipmentId = self.equipmentId;
        
        MRKNavigationController *nav = [[MRKNavigationController alloc] initWithRootViewController:vc];
        nav.modalPresentationStyle = UIModalPresentationFullScreen;
        [self presentViewController:nav animated:YES completion:nil];
    });
}


#pragma mark - MrkWebViewDelegate
- (void)webView:(MrkWebView *)webView didLoadWithTitle:(NSString *)title {
    self.navTitle = title;
}





- (BOOL)viewControllerIsNeedNavBar:(MRKBaseController *)viewController {
    return NO;
}


- (void)dealloc{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

@end








@implementation ExerciseReportShareSnippetModel
@end
