//
//  WebViewViewController.h
//  Student_IOS
//
//  Created by MacPro on 2021/6/10.
//

#import "BaseWKWebController.h"
#import <ReactiveCocoa.h>

NS_ASSUME_NONNULL_BEGIN

@interface WebViewViewController : BaseWKWebController
@property (nonatomic, copy) NSString *titleString;
@property (nonatomic, copy) NSString *htmlURL;
@property (nonatomic, assign) BOOL isHiddenNav;
@property (nonatomic, strong) NSNumber *activitySource;
@property (nonatomic, strong) NSArray *methods;
@property (nonatomic, assign) BOOL isNewUserPath;
@property (nonatomic, assign) BOOL isAddControlDeviceObs;
@property (nonatomic, readonly) RACSignal *mrkDeallocSignal;

@property (nonatomic, assign) BOOL firstInto;           ///是否第一次进入[新链路添加]
@property (nonatomic, copy) NSString *date;                ///日期
@property (nonatomic, copy) NSString *planId;                ///计划id
@property (nonatomic, copy) NSString *miaVoiceText;        ///语音文字
@end

NS_ASSUME_NONNULL_END
