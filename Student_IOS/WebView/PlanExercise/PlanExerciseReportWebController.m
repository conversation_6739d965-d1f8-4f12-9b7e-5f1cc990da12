//
//  PlanExerciseReportWebController.m
//  Student_IOS
//
//  Created by merit on 2021/8/31.
//

#import "PlanExerciseReportWebController.h"
#import "MRKTrainingProgressVC.h"
#import "MRKCoursePlanController.h"
#import "MRKCustomShareViewController.h"  //分享控制器
#import "MRKConvenientFunc.h"
#import "MRKTraceManager.h"
#import "MRKWebJSHandler.h"

@interface PlanExerciseReportWebController ()
@property (nonatomic, strong) NSString *realTrueUrlString;
@end

@implementation PlanExerciseReportWebController
- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.navTitle = self.titleStr;
    self.navTitleColor = [UIColor whiteColor];
    
    [self registerJSHandler];
    [self loadWebMainPage:self.httpUrl];
}

- (void)setHttpUrl:(NSString *)httpUrl {
    _httpUrl = httpUrl;
}

- (void)loadWebMainPage:(NSString *)httpStr{
    NSString *url = httpStr;
    NSMutableURLRequest *requset = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:url]
                                                           cachePolicy:NSURLRequestUseProtocolCachePolicy
                                                       timeoutInterval:20];
    [self.webView loadRequest:requset];
}

#pragma mark - Action
- (void)closeWebPage:(UIButton *)sender{
    [self.navigationController popViewControllerAnimated:YES];
}

- (void)leftBtnClick:(UIButton *)sender{
    sender.traceEventId = @"btn_all_return";
    NSLog(@"canGoBack=========%@",self.webView.canGoBack ? @"yes":@"no");
    // 调用父类中的方法进行返回的判断操作
    if (self.webView.canGoBack) {
        [self.webView goBack];
    } else {
        [self closeWebPage:nil];
    }
}

- (NSMutableDictionary *)callBackParms {
    NSDictionary *address = @{};
    NSString *province = @"";
    if([[address allKeys] containsObject:@"province"]){
        province = [address valueForKey:@"province"];
    }
    if ([province isEmpty]) {
        province = @"浙江省";
    }
    NSDictionary *parms = @{
        @"token":UserInfo.token,
        @"id":self.planUserId?:@"",
        @"type":@"iOS",
        @"city":province,
        @"isActivity":[self.activityId isNotBlank] ? @(1):@(0),
        @"avatar":UserInfo.avatar,
        @"headerHeight":@(kNavBarHeight),
        @"bottomType":IS_IPHONEX_SURE ? @"1":@"0",
        @"version" : [MRKConvenientFunc getAppShortVersion],///app版本
        @"User-Agent" : [NSString stringWithFormat:@"%@,H5",[TGApiManager userAgentString]],
        @"traceDeviceId": [MRKConvenientFunc getDeviceUUID],
        @"traceTerminal": @([MRKConvenientFunc getCurrentTerminalIndex]),
        @"traceRoute": [MRKTraceManager sharedInstance].traceRoute
    };
    return parms.mutableCopy;
}

- (void)registerJSHandler{
    [[MRKWebJSHandler shared] registerJSMethodHandler:self.bridge base:self];
}

- (void)shareBody{
    // 调用h5中的方法，h5中会回调 oc 中注册的 shareImage 方法  2021-11-05 --wk
    [self.bridge callHandler:[MRKWebJSHandler operatorStr: MRKWebJSCallBackMethodShareImage]];
}

#pragma mark - MrkWebViewDelegate
- (void)webView:(MrkWebView *)webView didLoadWithTitle:(NSString *)title {
    self.navTitle = title;
}



/** 导航条右边的按钮 */
- (UIImage *)mrkNavigationBarRightButtonImage:(UIButton *)rightButton navigationBar:(MRKNavigationBar *)navigationBar{
    rightButton.contentHorizontalAlignment = UIControlContentHorizontalAlignmentRight;
    return [UIImage imageNamed:@"icon_share_W"];
}

/** 右边的按钮的点击 */
- (void)rightButtonEvent:(UIButton *)sender navigationBar:(MRKNavigationBar *)navigationBar{
    [self shareBody];
}

- (void)dealloc{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

@end
