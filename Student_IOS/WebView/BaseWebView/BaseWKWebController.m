//
//  BaseWKWebController.m
//  Student_IOS
//
//  Created by merit on 2021/8/31.
//

#import "BaseWKWebController.h"

@interface BaseWKWebController ()<UIScrollViewDelegate, MrkWebViewDelegate>
@property (nonatomic, strong) NSString *realTrueUrlString;
@end

@implementation BaseWKWebController

- (void)viewWillDisappear:(BOOL)animated{
     [super viewWillDisappear:YES];
     [self.webProgressView removeFromSuperview];
}

- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:YES];
}

- (void)resetWebViewWidth:(CGFloat)width{
    [self.webView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.centerY.centerX.mas_equalTo(0);
        make.width.mas_equalTo(MIN(width, RealScreenWidth));
        make.top.bottom.mas_equalTo(0);
    }];
}

- (MrkWebView *)webView {
    if (!_webView) {
        _webView = [[MrkWebView alloc] initWithFrame:CGRectZero controlMediaPlayback:self.controlMediaPlayback];
    }
    return _webView;
}

- (MrkWebProgressView *)webProgressView {
    if (!_webProgressView) {
        _webProgressView = [[MrkWebProgressView alloc] init];
        _webProgressView.backgroundColor = UIColor.clearColor;
        _webProgressView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleTopMargin;
    }
    return _webProgressView;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.mrkContentView.backgroundColor = UIColor.clearColor;
    
   
    self.webView.scalesPageToFit = YES;
    self.webView.delegate = self;
    [self.mrkContentView addSubview:self.webView];
    [self.webView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsMake(0, 0, 0, 0));
    }];
    if (@available(iOS 11.0, *)) {
        self.webView.realWebView.scrollView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    } else {
        self.automaticallyAdjustsScrollViewInsets = NO;
    }
    
    @weakify(self);
    /// 2024.2.4 因为h5的七鱼识别出来的userAgent有问题，客户端进行强制修正
    [self.webView.realWebView evaluateJavaScript:@"navigator.userAgent" completionHandler:^(id result, NSError *error) {
        NSLog(@"userAgent :%@", result);
        self_weak_.webView.realWebView.customUserAgent = result;
     }];
    

    [self.webView addSubview:self.webProgressView];
    [self.webProgressView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.equalTo(self.webView);
        make.width.mas_equalTo(self.webView.mas_width);
        make.height.mas_equalTo(2.5);
    }];

    
#if DEBUG
    [WKWebViewJavascriptBridge enableLogging];
#endif
    self.bridge = [WKWebViewJavascriptBridge bridgeForWebView:self.webView.realWebView];
    [self.bridge setWebViewDelegate:self.webView];
    
    
    self.webProgressView.progress = 0;
    [[RACObserve(self.webView, estimatedProgress) takeUntil:[self rac_willDeallocSignal]]  subscribeNext:^(id x) {
        dispatch_async(dispatch_get_main_queue(), ^{
            double progress = [x doubleValue];
            [self_weak_.webProgressView setProgress:progress animated:YES];
        });
    }];
}

#pragma mark - WebViewDelegate
- (void)webView:(MrkWebView * _Nonnull)webView didFailLoadWithError:(NSError * _Nonnull)error {
    
}

- (void)webView:(MrkWebView * _Nonnull)webView didLoadWithTitle:(NSString * _Nonnull)title {
    self.navTitle = title;
    self.navTitleColor = [UIColor whiteColor];
}

- (BOOL)webView:(MrkWebView * _Nonnull)webView shouldStartLoadWith:(NSURLRequest * _Nonnull)request navigationType:(WKNavigationType)navigationType {
    NSLog(@"request.URL.absoluteString =========%@", request.URL.absoluteString);
    return YES;
}

- (void)webViewDidFinishLoad:(MrkWebView * _Nonnull)webView {
    
}

- (void)webViewDidStartLoad:(MrkWebView * _Nonnull)webView {
    
}


#pragma mark ---------Delegate -----------

- (BOOL)viewControllerIsNeedNavBar:(MRKBaseController *)viewController {
    return YES;
}

- (BOOL)mrkNavigationBarIsHideBottomLine:(MRKNavigationBar *)navigationBar {
    return YES;
}

- (UIStatusBarStyle)navControllerStatusBarStyle:(MRKBaseController *)viewController{
    return UIStatusBarStyleLightContent;
}

- (UIImage *)mrkNavigationBarLeftButtonImage:(UIButton *)leftButton navigationBar:(MRKNavigationBar *)navigationBar {
    return [UIImage imageNamed:@"icon_back-4"];
}

- (UIColor *)mrkNavigationBarBackgroundColor:(MRKNavigationBar *)navigationBar{
    return [UIColor colorWithWhite:1 alpha:0.0];
}

- (void)leftButtonEvent:(UIButton *)sender navigationBar:(MRKNavigationBar *)navigationBar {
    [self goback];
}

#pragma mark - webView中返回按钮的点击方法
- (void)goback {
    if (self.webView.canGoBack) {
        [self.webView goBack];
    }
   
    if ([self.navigationController canPerformAction:@selector(popViewControllerAnimated:) withSender:nil]){
        NSArray *viewcontrollers = self.navigationController.viewControllers;
        if (viewcontrollers.count > 1) {
            if ([viewcontrollers objectAtIndex:viewcontrollers.count - 1] == self) {
                [self.navigationController popViewControllerAnimated:YES];
            }
        } else {
            [self.navigationController dismissViewControllerAnimated:YES completion:nil];
        }
    } else {
        [self dismissViewControllerAnimated:YES completion:nil];
    }
}

- (void)dealloc {
   
}

@end
