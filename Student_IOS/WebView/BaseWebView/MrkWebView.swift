//
//  MrkWebView.swift
//  Student_IOS
//
//  Created by <PERSON><PERSON> on 2025/3/13.
//

import UIKit
import WebKit
import AVFoundation

// MARK: - MrkWebViewDelegate Protocol
@objc protocol MrkWebViewDelegate: AnyObject {
    @objc optional func webViewDidStartLoad(_ webView: MrkWebView)
    @objc optional func webViewDidFinishLoad(_ webView: MrkWebView)
    @objc optional func webView(_ webView: MrkWebView, didFailLoadWithError error: Error)
    @objc func webView(_ webView: MrkWebView, shouldStartLoadWith request: URLRequest, navigationType: WKNavigationType) -> Bool
    @objc optional func webView(_ webView: MrkWebView, didLoadWithTitle title: String)
}

// MARK: - 设置可选方法
extension MrkWebViewDelegate {
    func webViewDidStartLoad(_ webView: MrkWebView) {}
    func webViewDidFinishLoad(_ webView: MrkWebView) {}
    func webView(_ webView: MrkWebView, didFailLoadWithError error: Error) {}
    func webView(_ webView: MrkWebView, shouldStartLoadWith request: URLRequest, navigationType: WKNavigationType) -> Bool { return true }
    func webView(_ webView: MrkWebView, didLoadWithTitle title: String) {}
}

@objcMembers
class MrkWebView: UIView {
    
    // MARK: - Properties
    weak var delegate: MrkWebViewDelegate?
    public var realWebView: WKWebView!
    public private(set) dynamic var title: String = ""
    public private(set) dynamic var estimatedProgress: Double = 0
    private(set) var originRequest: URLRequest?
    private(set) var currentRequest: URLRequest?
    
    var scalesPageToFit: Bool = true {
        didSet {
            configureScalesPageToFit()
        }
    }
    
    var scrollView: UIScrollView {
        return realWebView.scrollView
    }
    
    var url: URL? {
        return realWebView.url
    }
    
    var isLoading: Bool {
        return realWebView.isLoading
    }
    
    var canGoBack: Bool {
        return realWebView.canGoBack
    }
    
    var canGoForward: Bool {
        return realWebView.canGoForward
    }
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        initMyself(controlMediaPlayback: false)
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        initMyself(controlMediaPlayback: false)
    }
    
    convenience init(frame: CGRect, controlMediaPlayback: Bool) {
        self.init(frame: frame)
        initMyself(controlMediaPlayback: controlMediaPlayback)
    }
    
    private func initMyself(controlMediaPlayback: Bool) {
        initWKWebView(controlMediaPlayback: controlMediaPlayback)
        scalesPageToFit = true
        realWebView.autoresizingMask = [.flexibleWidth, .flexibleHeight]
        addSubview(realWebView)
        realWebView.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            realWebView.topAnchor.constraint(equalTo: topAnchor),
            realWebView.leadingAnchor.constraint(equalTo: leadingAnchor),
            realWebView.trailingAnchor.constraint(equalTo: trailingAnchor),
            realWebView.bottomAnchor.constraint(equalTo: bottomAnchor)
        ])
    }
    
    private func initWKWebView(controlMediaPlayback: Bool) {
        let configuration = WKWebViewConfiguration()
        if controlMediaPlayback {
            configuration.allowsInlineMediaPlayback = true
            configuration.mediaTypesRequiringUserActionForPlayback = .all
        }
        
        let preferences = WKPreferences()
        preferences.javaScriptEnabled = true
        preferences.javaScriptCanOpenWindowsAutomatically = true
//        preferences.minimumFontSize = 11
        configuration.preferences = preferences
        
        // 共用cookie
        configuration.processPool = WKProcessPool.shared
        
        let userContentController = WKUserContentController()
        let script = """
            document.documentElement.style.webkitTouchCallout='none';
            document.documentElement.style.webkitUserSelect='none';
        """
        let userScript = WKUserScript(source: script, injectionTime: .atDocumentEnd, forMainFrameOnly: false)
        userContentController.addUserScript(userScript)
        configuration.userContentController = userContentController
        
        let webView = WKWebView(frame: .zero, configuration: configuration)
        webView.scrollView.showsVerticalScrollIndicator = false
        webView.scrollView.delegate = self
        webView.navigationDelegate = self
        webView.uiDelegate = self
        webView.isOpaque = false
        webView.scrollView.isScrollEnabled = true
        webView.scrollView.bounces = false
        
        webView.addObserver(self, forKeyPath: "estimatedProgress", options: .new, context: nil)
        webView.addObserver(self, forKeyPath: "title", options: .new, context: nil)
        
        realWebView = webView
    }
    
    // MARK: - Public Methods
    func loadUrl(_ url: URL) {
        let request: URLRequest = URLRequest(url: url)
        self.loadRequest(request)
    }
    
    func loadRequest(_ request: URLRequest) {
        originRequest = request
        currentRequest = request
        let navigation:WKNavigation? = realWebView.load(request)
        if let navigation = navigation {
            print("navigation === \(navigation)")
        }
    }
    
    func loadHTMLString(_ string: String, baseURL: URL?) -> WKNavigation? {
        return realWebView.loadHTMLString(string, baseURL: baseURL)
    }
    
    func goBack() {
        let _ = realWebView.goBack()
    }
    
    func goForward() {
        let _ = realWebView.goForward()
    }
    
    func reload() {
        let _ = realWebView.reload()
    }
    
    func reloadFromOrigin() {
        let _ = realWebView.reloadFromOrigin()
    }
    
    func stopLoading() {
        realWebView.stopLoading()
    }
    
    func evaluateJavaScript(_ javaScriptString: String, completionHandler: ((Any?, Error?) -> Void)? = nil) {
        realWebView.evaluateJavaScript(javaScriptString, completionHandler: completionHandler)
    }
    
    // MARK: - JavaScript Message Handling 添加js回调oc通知方式
    func addScriptMessageHandler(_ scriptMessageHandler: WKScriptMessageHandler, name: String) {
        realWebView.configuration.userContentController.add(scriptMessageHandler, name: name)
    }
    
    // MARK: - 注销 注册过的js回调oc通知方式
    func removeScriptMessageHandler(forName name: String) {
        realWebView.configuration.userContentController.removeScriptMessageHandler(forName: name)
    }
    
    // MARK: - Navigation History
    var countOfHistory: Int {
        return realWebView.backForwardList.backList.count
    }
    
    func goback(withStep step: Int) {
        guard canGoBack else { return }
        
        if step > 0 {
            let historyCount = countOfHistory
            let targetStep = min(step, historyCount - 1)
            if let backItem = realWebView.backForwardList.backList[safe: targetStep] {
                realWebView.go(to: backItem)
            }
        } else {
            goBack()
        }
    }
    
    // MARK: - KVO
    override func observeValue(forKeyPath keyPath: String?, of object: Any?, change: [NSKeyValueChangeKey : Any]?, context: UnsafeMutableRawPointer?) {
        if keyPath == "estimatedProgress" {
            estimatedProgress = realWebView.estimatedProgress
        } else if keyPath == "title" {
            title = realWebView.title ?? ""
            delegate?.webView(self, didLoadWithTitle: title)
        }
    }
    
    // MARK: - Cleanup
    deinit {
        realWebView.uiDelegate = nil
        realWebView.navigationDelegate = nil
        realWebView.scrollView.delegate = nil
        realWebView.removeObserver(self, forKeyPath: "estimatedProgress")
        realWebView.removeObserver(self, forKeyPath: "title")
        realWebView.configuration.userContentController.removeAllUserScripts()
        realWebView.stopLoading()
        realWebView.removeFromSuperview()
        
        print("MrkWebView deinit")
    }
}

// MARK: - WKNavigationDelegate
extension MrkWebView: WKNavigationDelegate {
    func webView(_ webView: WKWebView, decidePolicyFor navigationAction: WKNavigationAction, decisionHandler: @escaping (WKNavigationActionPolicy) -> Void) {
        let result = delegate?.webView(self, shouldStartLoadWith: navigationAction.request, navigationType: navigationAction.navigationType) ?? true
        
        if result {
            currentRequest = navigationAction.request
            if navigationAction.targetFrame == nil {
                DispatchQueue.main.async {
                    webView.load(navigationAction.request)
                }
            }
            decisionHandler(.allow)
        } else {
            decisionHandler(.cancel)
        }
    }
    
    /// Page started loading
    func webView(_ webView: WKWebView, didStartProvisionalNavigation navigation: WKNavigation!) {
        if let url = webView.url?.absoluteString {
            print("Started loading page: \(url)")
        } else {
            print("Started loading page, but URL could not be retrieved")
        }
        delegate?.webViewDidStartLoad(self)
    }
    
    /// Page finished loading
    func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
        if let url = webView.url?.absoluteString {
            print("Page finished loading: \(url)")
        } else {
            print("Page finished loading, but URL could not be retrieved")
        }
        delegate?.webViewDidFinishLoad(self)
    }
    
    func webView(_ webView: WKWebView, didFail navigation: WKNavigation!, withError error: Error) {
        print("Page loading failed: \(error.localizedDescription)")
        delegate?.webView(self, didFailLoadWithError: error)
    }
    
    /// Page loading failed
    func webView(_ webView: WKWebView, didFailProvisionalNavigation navigation: WKNavigation!, withError error: Error) {
        print("Page loading failed: \(error.localizedDescription)")
        delegate?.webView(self, didFailLoadWithError: error)
    }
    
    func webViewWebContentProcessDidTerminate(_ webView: WKWebView) {
        print("Web content process has terminated, reloading...")
        webView.reload()
    }
}

// MARK: - WKUIDelegate
extension MrkWebView: WKUIDelegate {
    func webView(_ webView: WKWebView, runJavaScriptAlertPanelWithMessage message: String, initiatedByFrame frame: WKFrameInfo, completionHandler: @escaping () -> Void) {
        print("message ===== \(message)")
        completionHandler()
    }
    
    func webView(_ webView: WKWebView, runJavaScriptConfirmPanelWithMessage message: String, initiatedByFrame frame: WKFrameInfo, completionHandler: @escaping (Bool) -> Void) {
        print("message ===== \(message)")
        completionHandler(true)
    }
    
    func webView(_ webView: WKWebView, runJavaScriptTextInputPanelWithPrompt prompt: String, defaultText: String?, initiatedByFrame frame: WKFrameInfo, completionHandler: @escaping (String?) -> Void) {
        print("prompt ====== \(prompt)")
        completionHandler(prompt)
    }
    
    @available(iOS 15.0, *)
    func webView(_ webView: WKWebView, requestMediaCapturePermissionFor origin: WKSecurityOrigin, initiatedByFrame frame: WKFrameInfo, type: WKMediaCaptureType, decisionHandler: @escaping @MainActor @Sendable (WKPermissionDecision) -> Void) {
        // 检查麦克风权限
        let authStatus = AVCaptureDevice.authorizationStatus(for: .audio)
        switch authStatus {
        case .authorized:
            // 已授权，允许H5访问
            decisionHandler(.grant)
        case .denied, .restricted:
            // 未授权，拒绝H5访问，并提示
            decisionHandler(.deny)
            showPermissionAlert()
        case .notDetermined:
            // 未决定，请求原生权限
            AVCaptureDevice.requestAccess(for: .audio) { granted in
                DispatchQueue.main.async {
                    decisionHandler(granted ? .grant : .deny)
                    if !granted {
                        self.showPermissionAlert()
                    }
                }
            }
        @unknown default:
            decisionHandler(.deny)
        }
    }
    
    func webViewDidClose(_ webView: WKWebView) {
        
    }
}

// MARK: - UIScrollViewDelegate
extension MrkWebView: UIScrollViewDelegate {
    func viewForZooming(in scrollView: UIScrollView) -> UIView? {
        return nil // 禁止网页手势缩放
    }
}

// MARK: - Private Helper Methods
private extension MrkWebView {
    func configureScalesPageToFit() {
        let script = """
            var meta = document.createElement('meta');
            meta.name = 'viewport';
            meta.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';
            var head = document.getElementsByTagName('head')[0];
            if (head.hasChildNodes('viewport')) return;
            head.appendChild(meta);
        """
        
        if scalesPageToFit {
            let userScript = WKUserScript(source: script, injectionTime: .atDocumentEnd, forMainFrameOnly: false)
            realWebView.configuration.userContentController.addUserScript(userScript)
        } else {
            let scripts = realWebView.configuration.userContentController.userScripts
            realWebView.configuration.userContentController.removeAllUserScripts()
            scripts.forEach { userScript in
                if userScript.source != script {
                    realWebView.configuration.userContentController.addUserScript(userScript)
                }
            }
        }
    }
}

// MARK: - WKProcessPool Extension
extension WKProcessPool {
    private static var sharedProcessPool: WKProcessPool = {
        return WKProcessPool()
    }()
    
    static var shared: WKProcessPool {
        return sharedProcessPool
    }
}

// MARK: - Array Extension
private extension Array {
    subscript(safe index: Int) -> Element? {
        return indices.contains(index) ? self[index] : nil
    }
}

// MARK: - 权限弹窗
private extension MrkWebView {
    func showPermissionAlert() {
        // 这里请根据你的实际弹窗实现替换
        // 示例：MrkAlertManager.showTopImageAlert
        MrkAlertManager.showTopImageAlert(
            "",
            title: "授权Merit获取麦克风权限",
            message: "语音对话功能需要获取麦克风权限",
            cancel: "暂不授权",
            ensure: "同意授权"
        ) { index in
            if index == 1 {
                UIApplication.displayAppPrivacySettings()
            }
        }
    }
}
