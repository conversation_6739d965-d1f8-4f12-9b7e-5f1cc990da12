//
//  BaseWKWebController.h
//  Student_IOS
//
//  Created by merit on 2021/8/31.
//

#import "MRKBaseController.h"
#import "WKWebViewJavascriptBridge.h"

@class MrkWebView;
@class MrkWebProgressView;
@interface BaseWKWebController : MRKBaseController
@property (nonatomic, strong) MrkWebProgressView *webProgressView;
@property (nonatomic, strong) MrkWebView *webView;

@property (nonatomic, strong) WKWebViewJavascriptBridge *bridge;
@property (nonatomic, strong) NSString *activityId; // 传入id
@property (nonatomic, assign) BOOL controlMediaPlayback; // 控制视频在页面播放
@property (nonatomic, assign) BOOL controlIpadWebWidth; // 控制web宽度
@property (nonatomic, copy) NSMutableDictionary *callBackParms; //交互返回
- (void)resetWebViewWidth:(CGFloat)width;
- (void)goback;
@end
