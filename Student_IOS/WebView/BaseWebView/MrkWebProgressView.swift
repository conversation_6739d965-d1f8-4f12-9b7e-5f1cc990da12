//
//  IMYWebProgressView.swift
//  Student_IOS
//
//  Created by merit on 2024/1/10
//

import UIKit

@objcMembers
class MrkWebProgressView: UIView {
    // MARK: - Properties
    private(set) lazy var progressBarView: UIView = {
        let view = UIView()
        view.frame.origin = .zero
        view.backgroundColor = UIColor(hex: 0x5791FE)
        return view
    }()
    
    var progress: Float = 0.0 {
        didSet {
            setProgress(progress, animated: false)
        }
    }
    
    var barAnimationDuration: TimeInterval = 0.27
    var fadeAnimationDuration: TimeInterval = 0.27
    var fadeOutDelay: TimeInterval = 0.1
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        configureViews()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        configureViews()
    }
    
    // MARK: - Configuration
    private func configureViews() {
        isUserInteractionEnabled = false
        autoresizingMask = .flexibleWidth
        addSubview(progressBarView)
    }
    
    // MARK: - Layout
    override func layoutSubviews() {
        super.layoutSubviews()
        progressBarView.frame.size.height = bounds.height
    }
    
    // MARK: - Progress Update
    func setProgress(_ progress: Float, animated: Bool) {
        
        let isGrowing = progress > 0.0
        UIView.animate(withDuration: (isGrowing && animated) ? barAnimationDuration : 0.0,
                       delay: 0,
                       options: .curveEaseInOut) {
            let width = CGFloat(progress) * self.bounds.width
            self.progressBarView.frame.size.width = width
        }
        
        if progress >= 1.0 {
            UIView.animate(withDuration: animated ? fadeAnimationDuration : 0.0,
                           delay: fadeOutDelay,
                           options: .curveEaseInOut) {
                self.progressBarView.alpha = 0.0
            } completion: { _ in
                self.progressBarView.frame.size.width = 0
            }
        } else {
            UIView.animate(withDuration: animated ? fadeAnimationDuration : 0.0,
                           delay: 0.0,
                           options: .curveEaseInOut) {
                self.progressBarView.alpha = 1.0
            }
        }
    }
}

// MARK: - UIColor Extension
extension UIColor {
    convenience init(hex: Int) {
        self.init(
            red: CGFloat((hex >> 16) & 0xFF) / 255.0,
            green: CGFloat((hex >> 8) & 0xFF) / 255.0,
            blue: CGFloat(hex & 0xFF) / 255.0,
            alpha: 1.0
        )
    }
}
