//
//  MRKWebJSHandler.h
//  Student_IOS
//
//  Created by merit on 2024/2/4.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, MRKWebJSCallBackMethod) {
    MRKWebJSCallBackMethodOnBack                    =  1,/// 返回退出  原生左上角返回点击调用h5的返回
    MRKWebJSCallBackMethodShareBody,                     /// 分享，右上角分享点击
    MRKWebJSCallBackMethodSourceData,                    /// 设备数据、控制档位、刷新设备等
    MRKWebJSCallBackMethodShareImage,                    /// 分享，右上角分享点击 PlanExerciseReportWebController
    MRKWebJSCallBackMethodOnTrainingReportShare,         /// 监听到截屏调起h5的分享方法
    MRKWebJSCallBackMethodShowModal,                     /// 显示勋章弹窗
    MRKWebJSCallBackMethodShareType,                     /// 分享时h5获取分享参数
    MRKWebJSCallBackMethodWebRefresh,                    /// H5页面刷新
    /// productId: 设备大类
    /// operator: calibrationPowerWeight 标定力量站配重块
    /// status 1 成功 2 失败
    MRKWebJSCallBackMethodDeviceControl,                /// 发送设备指令回调    handleDeviceControl
    MRKWebJSCallBackMethodPermissionControl,              /// 获取权限状态
    MRKWebJSCallBackAppMessage
}; // app调用h5

typedef NS_ENUM(NSInteger, MRKWebJSMethod) {
    MRKWebJSMethodInitData                          =  1,/// MRKAbilityTestWebView——数据交互
    MRKWebJSMethodExercisData,                           /// BaseWKWebController——数据交互
    MRKWebJSMethodToTrainingData,                        /// PlanExerciseReportWebController 的查看训练数据
    MRKWebJSMethodToRetraining,                          /// PlanExerciseReportWebController 重新训练
    MRKWebJSMethodShareImage,                            /// PlanExerciseReportWebController 的分享图片 WebViewViewController的分享图片
    MRKWebJSMethodShareTrainReportImage,                 /// 分享运动数据的新方法
    MRKWebJSMethodShareTrainReportImageByPiece,          /// 使用flutter 分享，更改base64 传输方式
    MRKWebJSMethodToHomeIndex,                           /// 返回并跳转指定tab
    MRKWebJSMethodBackFinish,                            /// 返回
    MRKWebJSMethodActivityShareBtn,                      /// 活动分享
    MRKWebJSMethodEscalationData,                        /// h5进入原生页面的时候调用该方法，更新页面路径
    MRKWebJSMethodUploadBuryData,                        /// h5已进入页面的时候调用该方法， 替换当前页面的tracePageRoute和，tracePageId为的是像分享之类在当前页进行交互的事件
    MRKWebJSMethodUploadTrainId,                         /// 上传运动数据到第三方
    MRKWebJSMethodLinkDataCenter,                        /// h5进入数据中心
    MRKWebJSMethodToExternalLink,                        /// 外链
    MRKWebJSMethodToChallengeDetail,                     /// 跳转挑战赛明细
    MRKWebJSMethodSaveImage,                             /// 2.7.2 新增保存图片
    MRKWebJSMethodWebDataShare,                          /// 2.7.0 产品百科 分享
    MRKWebJSMethodToBindDevice,                          /// 绑定设备
    MRKWebJSMethodToAddressOperation,                    /// 去编辑地址页
    MRKWebJSMethodShareModal,                            /// 22-06-14 修改分享方法，前端把分享参数传过来，我们不用调用接口
    MRKWebJSMethodSmartControl,                          /// 智能调阻 引导购买
    MRKWebJSMethodVipRedemptionSuccess,                  /// 会员兑换成功 [剔除前面的VIp页面]
    MRKWebJSMethodHuaweiAuthorization,                   /// 三方数据对接，授权成功
    MRKWebJSMethodChangeAppTheme,                        /// 改变主题
    MRKWebJSMethodLinkToSearchEquipment,                 /// 进设备搜索
    //    MRKWebJSMethodShareBtn,  h5已经去掉了
    //    MRKWebJSMethodSendToAppData,  /// 没有操作
    MRKWebJSMethodApplePay,                              /// 拉起苹果支付
    MRKWebJSMethodShareHealthReport,                     /// 八极电子秤点击分享
    MRKWebJSMethodHealthReportDetail,                    /// 八极体脂称跳转到健康报告详情
    MRKWebJSMethodJumpRouter,                            /// 新用户活动页面-页面跳转（新人分享页面 /good-gift-v2, /equity-details这两个页面）
    MRKWebJSMethodScan,                                  /// 扫一扫
    MRKWebJSMethodNewUserAcceptSuc,
    
    MRKWebJSMethodSubscribeDeviceData,                  /// 订阅/取消订阅设备数据   productId    subscribeStatus 1 订阅 0 取消订阅
    MRKWebJSMethodControlDevice,                        /// 发送设备指令   type:productId  PowerTotalNumber: 数值（1～15 30是标定结束）,
    MRKWebJSMethodLinkTrainPlanRecord,                  /// 回顾计划详情
    MRKWebJSMethodNewUserLinkTrainPlan,                 /// 新手链路打开aiPlan
    MRKWebJSMethodSetScreenWidth,                       /// 重新设定屏幕宽度
    MRKWebJSMethodControlPermission,                    /// 开权限
    MRKWebJSMethodReanalysisFood,                       /// 饮食-重新分析
    MRKWebJSMethodMotionDetail,                         /// 动作详情
    MRKWebJSMethodFoodRecord,                           /// 饮食记录
    MRKWebJSMethodDietTakePhoto,                        /// AI拍照识食
    MRKWebJSMethodChangeGlobalSoundFlag,                /// AI播放大喇叭处理
    MRKWebJSMethodLaunchNewAiPlan,                      /// 开启新计划
    MRKWebJSMethodHandleDesignatedPlan,                 /// 新手链路，暂无设备，跳转到ai计划生成
    MRKWebJSMethodHandleSubscribe,                      /// ai对话，ai订阅弹窗
}; // h5调用app

@interface MRKWebJSHandler : NSObject

+ (instancetype)shared; // 单例

- (void)registerJSMethodHandler:(WKWebViewJavascriptBridge *)bridge base:(__weak id)base;

+ (NSString *)methodStr:(MRKWebJSMethod)type;

+ (NSString *)operatorStr:(MRKWebJSCallBackMethod)type;

/// 苹果支付
- (void)fetchesPaymentCode:(NSString *)productId;

- (void)handleJumpToTabIndex:(NSInteger)index;

@end

NS_ASSUME_NONNULL_END
