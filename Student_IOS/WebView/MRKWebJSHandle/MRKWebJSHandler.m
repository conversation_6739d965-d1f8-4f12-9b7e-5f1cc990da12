//
//  MRKWebJSHandler.m
//  Student_IOS
//
//  Created by merit on 2024/2/4.
//

#import "MRKWebJSHandler.h"
#import "MRKChallengeDetailVC.h"
#import "MRKMineDataViewController.h"
#import "MRKAddressViewController.h"      //添加地址页面
#import "MRKAddressListModel.h"           //地址模型
#import "DeviceSearchViewController.h"
#import "MRKChallengeModel.h"
#import "MRKDeviceURLRequest.h"
#import <Photos/Photos.h>
#import "TGPermissionSetting.h"
#import "MRKAbilityTestWebView.h"
#import "MRKTrainingProgressVC.h"
#import "PlanExerciseReportWebController.h"
#import "MRKCoursePlanController.h"
#import "MRKCustomShareViewController.h"  //分享控制器
#import "MRKShareImageController.h"
#import "MRKShareButtonsView.h"
#import "UIViewController+ShareImage.h"
#import "ExerciseReportWebController.h"
#import "DYFStoreManager.h"
#import "DeviceFilterViewController.h"
#import "HearlthReportDetailVC.h"
#import "TZLocationManager.h"
#import "MRKDailyLogic.h"

@implementation MRKWebJSHandler

static dispatch_once_t onceToken;
static MRKWebJSHandler *_share = nil;

+ (instancetype)shared {
    dispatch_once(&onceToken, ^{
        _share = [[self alloc] init];
    });
    return _share;
}

// MARK: —— 注册js方法
- (void)registerJSMethodHandler:(WKWebViewJavascriptBridge *)bridge base:(__weak id)base {
    NSArray *arr = @[@(MRKWebJSMethodInitData),
                     @(MRKWebJSMethodExercisData),
                     @(MRKWebJSMethodToTrainingData),
                     @(MRKWebJSMethodToRetraining),
                     @(MRKWebJSMethodShareImage),
                     @(MRKWebJSMethodShareTrainReportImage),
                     @(MRKWebJSMethodShareTrainReportImageByPiece),
                     @(MRKWebJSMethodToHomeIndex),
                     @(MRKWebJSMethodBackFinish),
                     @(MRKWebJSMethodActivityShareBtn),
                     @(MRKWebJSMethodEscalationData),
                     @(MRKWebJSMethodUploadBuryData),
                     @(MRKWebJSMethodUploadTrainId),
                     @(MRKWebJSMethodLinkDataCenter),
                     @(MRKWebJSMethodToExternalLink),
                     @(MRKWebJSMethodToChallengeDetail),
                     @(MRKWebJSMethodSaveImage),
                     @(MRKWebJSMethodWebDataShare),
                     @(MRKWebJSMethodToBindDevice),
                     @(MRKWebJSMethodToAddressOperation),
                     @(MRKWebJSMethodShareModal),
                     @(MRKWebJSMethodSmartControl),
                     @(MRKWebJSMethodVipRedemptionSuccess),
                     @(MRKWebJSMethodHuaweiAuthorization),
                     @(MRKWebJSMethodChangeAppTheme),
                     @(MRKWebJSMethodLinkToSearchEquipment),
                     @(MRKWebJSMethodApplePay),
                     @(MRKWebJSMethodShareHealthReport),
                     @(MRKWebJSMethodHealthReportDetail),
                     @(MRKWebJSMethodJumpRouter),
                     @(MRKWebJSMethodScan),
                     @(MRKWebJSMethodNewUserAcceptSuc),
                     @(MRKWebJSMethodSubscribeDeviceData),
                     @(MRKWebJSMethodControlDevice),
                     @(MRKWebJSMethodLinkTrainPlanRecord),
                     @(MRKWebJSMethodNewUserLinkTrainPlan),
                     @(MRKWebJSMethodSetScreenWidth),
                     @(MRKWebJSMethodControlPermission),
                     @(MRKWebJSMethodReanalysisFood),
                     @(MRKWebJSMethodMotionDetail),
                     @(MRKWebJSMethodFoodRecord),
                     @(MRKWebJSMethodDietTakePhoto),
                     @(MRKWebJSMethodChangeGlobalSoundFlag),
                     @(MRKWebJSMethodLaunchNewAiPlan),
                     @(MRKWebJSMethodHandleDesignatedPlan),
                     @(MRKWebJSMethodHandleSubscribe),
    ];
    for (int i = 0; i < arr.count; i ++) {
        MRKWebJSMethod type = [arr[i] intValue];
        WVJBHandler handler = [self handlerForMethod:type bridge:bridge base:base];
        if (handler != nil) {
            [bridge registerHandler:[MRKWebJSHandler methodStr:type] handler:handler];
        }
    }
}

// MARK: —— 处理js方法对应的操作
- (nullable WVJBHandler)handlerForMethod:(MRKWebJSMethod)type bridge:(WKWebViewJavascriptBridge *)bridge base:(__weak id)base {
    @weakify(self);
    return ^(id data, WVJBResponseCallback responseCallback) {
        NSLog(@"from js: %@", data);
        switch (type) {
            case MRKWebJSMethodInitData: /// 数据交互
                [self_weak_ testData:data base:base back:responseCallback];
                break;
            case MRKWebJSMethodExercisData: /// 数据交互
                [self_weak_ exercisData:data base:base back:responseCallback];
                break;
            case MRKWebJSMethodToTrainingData: /// PlanExerciseReportWebController 的查看训练数据 ///跳转计划进度
                [self_weak_ toTrainingProgress:data base:base];
                break;
            case MRKWebJSMethodToRetraining:/// PlanExerciseReportWebController 重新训练 ///跳转计划详情
                [self_weak_ toCoursePlan:data base:base];
                break;
            case MRKWebJSMethodShareImage:/// PlanExerciseReportWebController 的分享图片 WebViewViewController的分享图片
                [self_weak_ shareImage:data base:base];
                break;
            case MRKWebJSMethodShareTrainReportImage:///分享运动数据的新方法
                [self_weak_ flutterShare:data base:base];
                break;
            case MRKWebJSMethodShareTrainReportImageByPiece:/// 接收json，在比对json是否接收完
                [self_weak_ flutterShareByPiece:data base:base];
                break;
            case MRKWebJSMethodToHomeIndex:///返回并跳转指定tab
                [self_weak_ toHomeIndex:data base:base];
                break;
            case MRKWebJSMethodBackFinish: /// 返回
                [self_weak_ back:data base:base];
                break;
            case MRKWebJSMethodActivityShareBtn: /// 活动分享
                [self_weak_ shareBody:data base:base];
                break;
            case MRKWebJSMethodEscalationData:/// h5进入原生页面的时候调用该方法，更新页面路径
                [self_weak_ escalationData:data];
                break;
            case MRKWebJSMethodUploadBuryData:///h5已进入页面的时候调用该方法，替换当前页面的tracePageRoute和，tracePageId为的是像分享之类在当前页进行交互的事件
                [self_weak_ uploadBuryData:data];
                break;
            case MRKWebJSMethodToChallengeDetail:///跳转挑战赛明细
                [self_weak_ toChallengeDetail:data base:base];
                break;
            case MRKWebJSMethodUploadTrainId:/// 上传数据到三方
                [self_weak_ uploadSportDataToThird:data];
                break;
            case MRKWebJSMethodLinkDataCenter:/// h5进入数据中心
                [self_weak_ toDataCenter:data];
                break;
            case MRKWebJSMethodToAddressOperation: ///去编辑地址页
                [self_weak_ toAddressOperation:data];
                break;
            case MRKWebJSMethodLinkToSearchEquipment:/// h5进设备搜索
                [self_weak_ toDeviceSearch:data];
                break;
            case MRKWebJSMethodHuaweiAuthorization: /// 三方数据对接，授权成功
                [self_weak_ thirdDataAuthSuccess:data base:base];
                break;
            case MRKWebJSMethodChangeAppTheme: /// h5改变主题
                [self_weak_ changeTheme:data];
                break;
            case MRKWebJSMethodWebDataShare: /// 2.7.0 产品百科 分享
                [self_weak_ webDataShare:data];
                break;
            case MRKWebJSMethodToBindDevice: ///绑定设备
                [self_weak_ toBindDevice:data];
                break;
            case MRKWebJSMethodToExternalLink: /// 外链
                [self_weak_ toExternalLink:data];
                break;
            case MRKWebJSMethodSmartControl: /// 智能调阻 引导购买
                [self_weak_ toGuideBuy:data];
                break;
            case MRKWebJSMethodVipRedemptionSuccess: case MRKWebJSMethodNewUserAcceptSuc: /// 会员兑换成功 [剔除前面的VIp页面]
                [self_weak_ removeVipController:data];
                break;
            case MRKWebJSMethodSaveImage: ///2.7.2 新增保存图片
                [self_weak_ saveImageToLocal:data];
                break;
            case MRKWebJSMethodShareModal:///22-06-14 修改分享方法，前端把分享参数传过来，我们不用调用接口
                [self_weak_ shareModal:data bridge:bridge];
                break;
            case MRKWebJSMethodApplePay: /// 调用苹果支付
                [self_weak_ applePay:data bridge:bridge];
                break;
            case MRKWebJSMethodShareHealthReport: /// 八极电子秤点击分享
                [self_weak_ shareHealthReport:data bridge:bridge];
                break;
            case MRKWebJSMethodHealthReportDetail: /// 八极体脂称跳转到健康报告详情
                [self_weak_ jumpHealthReportDetail:data bridge:bridge];
                break;
            case MRKWebJSMethodJumpRouter: /// 新用户活动页面-页面跳转（新人分享页面 /good-gift-v2, /equity-details这两个页面）
                [self_weak_ jumpAppRouter:data bridge:bridge];
                break;
            case MRKWebJSMethodScan:
                [self_weak_ jumpScanQrcode:data];
                break;
            case MRKWebJSMethodSubscribeDeviceData:
                [self_weak_ subscribeDeviceData:data];
                break;
            case MRKWebJSMethodControlDevice:
                [self_weak_ toControlDevice:data];
                break;
            case MRKWebJSMethodLinkTrainPlanRecord:
                [self_weak_ toTrainPlanRecord:data];
                break;
            case MRKWebJSMethodNewUserLinkTrainPlan:
                [self_weak_ handleJumpToTabAIPlan];
                break;
            case MRKWebJSMethodSetScreenWidth:
                [self_weak_ toChangeScreenWidth:data base:base];
                break;
            case MRKWebJSMethodControlPermission:
                [self_weak_ toControlPermission:data base:base bridge:bridge];
                break;
            case MRKWebJSMethodReanalysisFood:
                [self_weak_ toReanalysisFood:data base:base bridge:bridge];
                break;
            case MRKWebJSMethodMotionDetail:
                [self_weak_ toMotionDetail:data base:base bridge:bridge];
                break;
            case MRKWebJSMethodFoodRecord:
                [self_weak_ toFoodRecord:data base:base bridge:bridge];
                break;
            case MRKWebJSMethodDietTakePhoto:
                [self_weak_ toDietTakePhoto:data base:base bridge:bridge];
                break;
            case MRKWebJSMethodChangeGlobalSoundFlag:
                [self_weak_ changeGlobalSoundFlag:data base:base bridge:bridge];
                break;
            case MRKWebJSMethodLaunchNewAiPlan:
                [self_weak_ launchNewAiPlan];
                break;
            case MRKWebJSMethodHandleDesignatedPlan:
                [self_weak_ handleDesignatedPlan];
                break;
            case MRKWebJSMethodHandleSubscribe:
                [self_weak_ handleSubscribe:data base:base bridge:bridge];
                break;
            default: break;
        }
    };
}

///< 会员弹窗
- (void)handleSubscribe:(id)data base:(__weak id)base bridge:(WKWebViewJavascriptBridge *)bridge {
    NSString *type = [NSString stringWithFormat:@"%@", [data valueForKeyPath:@"type"]];
    
}


///< 定制计划 flutter
- (void)handleDesignatedPlan {
    [MRKNewLinkLogic jumpNoConnect];
}

// MARK: —— AI播放大喇叭处理
- (void)changeGlobalSoundFlag:(id)data base:(__weak id)base bridge:(WKWebViewJavascriptBridge *)bridge {
    NSString *globalSoundFlag = [NSString stringWithFormat:@"%@", data];
    [[NSUserDefaults standardUserDefaults] setObject:globalSoundFlag forKey:@"SaveH5AIGlobalSoundFlag"];
}

// MARK: —— 点击AI拍照识别
- (void)toDietTakePhoto:(id)data base:(__weak id)base bridge:(WKWebViewJavascriptBridge *)bridge {
    NSString *date = [NSString stringWithFormat:@"%@", [data valueForKeyPath:@"date"]];
    [[MRKDailyLogic shared] jumpToImageRecognition:date];
}

// MARK: —— 点击进行重新食物分析的方法
- (void)toReanalysisFood:(id)data base:(__weak id)base bridge:(WKWebViewJavascriptBridge *)bridge {
    NSString *date = [NSString stringWithFormat:@"%@", [data valueForKeyPath:@"date"]];
    [[MRKDailyLogic shared] jumpToDietSportReAnalysis:date];
}
// MARK: —— 点击进入动作详情
- (void)toMotionDetail:(id)data base:(__weak id)base bridge:(WKWebViewJavascriptBridge *)bridge {
    NSString *motionId = [NSString stringWithFormat:@"%@", [data valueForKeyPath:@"id"]];
    [[RouteManager sharedInstance] jumpToMotionDetailWithId:motionId trainTarget:@""];
}

// MARK: —— 点击进入食物列表
- (void)toFoodRecord:(id)data base:(__weak id)base bridge:(WKWebViewJavascriptBridge *)bridge {
    NSString *date = [NSString stringWithFormat:@"%@", [data valueForKeyPath:@"date"]];
    [[MRKDailyLogic shared] jumpToDietRecord:date index:0];
}


// MARK: —— 去控制权限
- (void)toControlPermission:(id)data base:(__weak id)base bridge:(WKWebViewJavascriptBridge *)bridge {
    NSString *operator = [NSString stringWithFormat:@"%@", [data valueForKeyPath:@"operator"]];
    @weakify(self);
    if ([operator isEqualToString:@"camera"]) {
        //检查相机状态
        
        [[SystemPermissionManager shared] requestCameraPermissionOnGranted:^{
            ///
            [self_weak_ toBackControlPermission:operator bridge:bridge status:@"success" data:@""];
        } onDenied:^{
            [self_weak_ toBackControlPermission:operator bridge:bridge status:@"failure" data:@""];
        }];

    } else if ([operator isEqualToString:@"photo"]) {
        
        [[SystemPermissionManager shared] requestPhotoLibraryPermissionOnGranted:^{
            ///
            [self_weak_ toBackControlPermission:operator bridge:bridge status:@"success" data:@""];
        } onDenied:^{
            [self_weak_ toBackControlPermission:operator bridge:bridge status:@"failure" data:@""];
        }];
 
    } else if ([operator isEqualToString:@"audio"]) {
        
        [[SystemPermissionManager shared] requestMicrophonePermissionWithUndeterminedControl:true onGranted:^{
            ///
            [self_weak_ toBackControlPermission:operator bridge:bridge status:@"success" data:@""];
        } onDenied:^{
            [self_weak_ toBackControlPermission:operator bridge:bridge status:@"failure" data:@""];
        }];

    } else if ([operator isEqualToString:@"location"]) {
        
        [[TZLocationManager manager] startLocationWithSuccessBlock:^(NSArray<CLLocation *> *locations) {
            CLLocation *location = [locations firstObject];
            // 经度
            NSString *longitude = [NSString stringWithFormat:@"%lf", location.coordinate.longitude];
            // 纬度
            NSString *latitude = [NSString stringWithFormat:@"%lf", location.coordinate.latitude];
            // 获取当前所在的城市名
            CLGeocoder *geocoder = [[CLGeocoder alloc] init];
            //根据经纬度反向地理编译出地址信息
            [geocoder reverseGeocodeLocation:location completionHandler:^(NSArray *array, NSError *error){
                if (error) {
                    NSLog(@"Error: %@", error);
                    return;
                }
                CLPlacemark *placemark = [array lastObject];
                NSMutableDictionary *dict = [[NSMutableDictionary alloc] initWithDictionary:placemark.addressDictionary];
                dict[@"longitude"] = longitude;
                dict[@"latitude"] = latitude;
                [self_weak_ toBackControlPermission:operator bridge:bridge status:@"success" data:dict.modelToJSONString];
             }];
            
        } failureBlock:^(NSError *error) {
            [self_weak_ toBackControlPermission:operator bridge:bridge status:@"failure" data:@""];
        }];
    }
}


- (void)toBackControlPermission:(NSString *)operator bridge:(WKWebViewJavascriptBridge *)bridge status:(NSString *)status data:(NSString *)data{
    NSDictionary *dict = @{@"operator": operator,
                           @"status": status,
                           @"data": data};
    [bridge callHandler:[MRKWebJSHandler operatorStr: MRKWebJSCallBackMethodPermissionControl]
                   data:dict.modelToJSONString
       responseCallback:^(id responseData) {}];
}

// 跳进app设置
- (void)openAPPSetting {
    [UIApplication displayAppPrivacySettings];
}


// MARK: —— 回顾计划详情
- (void)toChangeScreenWidth:(id)data base:(__weak id)base {
    CGFloat width = [[NSString stringWithFormat:@"%@", [data valueForKeyPath:@"width"]] doubleValue];
    if ([base isKindOfClass:[BaseWKWebController class]]){
        BaseWKWebController *web = (BaseWKWebController *)base;
        [web resetWebViewWidth: width];
    }
}

// MARK: —— 回顾计划详情
- (void)toTrainPlanRecord:(id)data{
    NSString *planId = [NSString stringWithFormat:@"%@", [data valueForKeyPath:@"planId"]];
    NSString *version = [NSString stringWithFormat:@"%@", [data valueForKeyPath:@"edition"]];
    if (planId.isNotEmpty) {
        [[MRKAIPlanLogic shared] jumpToAIPlanDetailPage:planId version:version ? version.integerValue : 1];
    }
}

// MARK: —— 新手链路打开aiPlan 入口
- (void)handleJumpToTabAIPlan{
    UIWindow *window = [UIApplication sharedApplication].delegate.window;
    if ([window.rootViewController isKindOfClass:[UITabBarController class]]) {
        [[MRKAIPlanLogic shared] jumpToAIPlan];
        return;
    }
    /// 链路中的话，没有初始化tabbarcontroller
    if ([Login isLogin]){
        [[NSNotificationCenter defaultCenter] postNotificationName:kLogin_Notification object:UserInfo.userId];
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [[MRKAIPlanLogic shared] jumpToAIPlan];
        });
    }
}

///< 开启新计划 []
- (void)launchNewAiPlan {
    [[MRKAIPlanLogic shared] jumpToAIPlan];
}

// MARK: —— 订阅/取消订阅蓝牙数据
- (void)subscribeDeviceData:(id)data{
    NSString *productId = [NSString stringWithFormat:@"%@", [data valueForKeyPath:@"productId"]];
    BOOL isSubscribe = [[data valueForKeyPath:@"status"] intValue] == 1;
    if(![BlueDataStorageManager isConnectDeviceWithProductID:productId]) {
        [AppDelegate errorView:@"设备已断连，请返回设备详情连接后再次尝试"];
        return;
    }
    ///订阅/取消订阅 数据服务-
    if ([productId isNotEmpty]) {
        [BlueCommunicationProtocol subscribeCharacteristicFromType:productId isSubscribe:isSubscribe];
    }
}

// MARK: —— 对设备发送指令
- (void)toControlDevice:(id)data{
    NSString *productId = [NSString stringWithFormat:@"%@", [data valueForKeyPath:@"type"]];
    if(![BlueDataStorageManager isConnectDeviceWithProductID:productId]) {
        [AppDelegate errorView:@"设备已断连，请返回设备详情连接后再次尝试"];
        return;
    }
    [[NSNotificationCenter defaultCenter] postNotificationName:SetResistanceSlopeSpeedNotification object:data];
}

// MARK: —— 跳转到扫一扫页面
- (void)jumpScanQrcode:(id)data{
    NSString *scene = [data valueForKeyPath:@"scene"];
    [[RouteManager sharedInstance] jumpToScanQrcodeVC: scene];
}

// MARK: —— 八极电子秤点击分享
- (void)shareHealthReport:(id)data bridge:(WKWebViewJavascriptBridge *)bridge {
    if (![data isKindOfClass:[NSDictionary class]]) {
        return;
    }
    NSString *imageStr = [data valueForKeyPath:@"simplePng"]; // 简略图片
    NSString *a4ImageStr = [data valueForKeyPath:@"figureA4Png"]; // 详细图片
    dispatch_async(dispatch_get_main_queue(), ^{
        [FlutterManager shareScaleReportVCWithImage:imageStr a4Image:a4ImageStr];
    });
}

// MARK: —— 八极体脂称跳转到健康报告详情
- (void)jumpHealthReportDetail:(id)data bridge:(WKWebViewJavascriptBridge *)bridge {
    NSString *itemId = [data valueForKeyPath:@"id"];
    NSString *healthReportId = [data valueForKeyPath:@"healthReportId"];
    HearlthReportDetailVC *vc = [[HearlthReportDetailVC alloc] init];
    vc.itemId = itemId;
    vc.bodyFatScaleId = healthReportId;
    vc.electrodeType = 2;
    [[UIViewController currentViewController].navigationController pushViewController:vc animated:YES];
}

// MARK: —— 新用户活动页面-页面跳转（新人分享页面 /good-gift-v2, /equity-details这两个页面）
- (void)jumpAppRouter:(id)data bridge:(WKWebViewJavascriptBridge *)bridge {
    NSString *content = [data valueForKeyPath:@"content"];
    [[RouteManager sharedInstance] applicationOpenURL:[NSURL URLWithString:content]];
}

// MARK: —— 苹果支付
- (void)applePay:(id)data bridge:(WKWebViewJavascriptBridge *)bridge {
    NSString *appStoreCode = [data valueForKeyPath:@"appStoreCode"];
    [self fetchesPaymentCode:appStoreCode];
}

/// Strategy 1:
///  - Step 1: Requests localized information about a product from the Apple App Store.
///  - Step 2: Adds payment of the product with the given product identifier.
- (void)fetchesPaymentCode:(NSString *)productId {
    // You need to check whether the device is not able or allowed to make payments before requesting product.
    if (![DYFStore canMakePayments]) {
        [self showTipsMessage:@"当前手机设备不支持或者未授权购买"];
        return;
    }
    
    MLog(@"_开始内购 ====productId:%@ ", productId);
    [self showLoading:@"加载中..."];
    [DYFStore.defaultStore requestProductWithIdentifier:productId success:^(NSArray *products, NSArray *invalidIdentifiers) {
        [self hideLoading];
        if (products.count == 1) {
            NSString *productId = ((SKProduct *)products[0]).productIdentifier;
            [self addPayment:productId];
            
            MLog(@"_开始内购 block ====productId:%@ ",  productId);
        } else {
            [self showTipsMessage:@"暂无此项购买销售"];
            MLog(@"_内购失败 ==== 暂无此项购买销售 ");
        }
    } failure:^(NSError *error) {
        
        [self hideLoading];
        
        NSString *value = error.userInfo[NSLocalizedDescriptionKey];
        NSString *msg = value ?: error.localizedDescription;
        // This indicates that the product cannot be fetched, because an error was reported.
        [self sendNotice:[NSString stringWithFormat:@"出现错误, %zi, %@", error.code, msg]];
        MLog(@"_内购失败 ==== 出现错误, 错误码:%zi , %@ ", error.code, msg);
    }];
}

- (void)addPayment:(NSString *)productId{
    
    // Get account name from your own user system.
    NSString *userIdentifier = UserInfo.userId;
    
    // This algorithm is negotiated with server developer.
    //    NSString *userIdentifier = DYF_SHA256_HashValue(accountName);
    DYFStoreLog(@"userIdentifier: %@", userIdentifier);
    [DYFStoreManager.shared addPayment:productId userIdentifier:userIdentifier];
}

- (void)sendNotice:(NSString *)message {
    [self showAlertWithTitle:@"提示"
                     message:message
           cancelButtonTitle:nil
                      cancel:NULL
          confirmButtonTitle:@"知道了"
                     execute:^(UIAlertAction *action) {
        DYFStoreLog(@"Alert action title: %@", action.title);
    }];
}

// MARK: —— 初始化数据交互-回调数据给h5
- (void)testData:(id)data base:(__weak id)base back:(WVJBResponseCallback)responseCallback {
    if ([base isKindOfClass:[MRKAbilityTestWebView class]]){
        MRKAbilityTestWebView *web = (MRKAbilityTestWebView *)base;
        NSDictionary *dic = web.callBackParms;
        if (dic.count > 0) {
            responseCallback([dic modelToJSONString]);
        }
    }
}

- (void)exercisData:(id)data base:(__weak id)base back:(WVJBResponseCallback)responseCallback {
    if ([base isKindOfClass:[BaseWKWebController class]]){
        BaseWKWebController *web = (BaseWKWebController *)base;
        NSDictionary *dic = web.callBackParms;
        if (dic.count > 0) {
            responseCallback([dic modelToJSONString]);
        }
    }
    if ([data isNotEmpty] && [base isKindOfClass:[WebViewViewController class]]) {
        WebViewViewController*web = (WebViewViewController *)base;
        NSArray *methods = [data componentsSeparatedByString:@","];
        web.methods = methods;
        if ([web.methods containsObject:@"share"]) {
            web.mrk_navgationBar.rightView.hidden = NO;
        }
    }
}

// MARK: —— 埋点相关
/// h5进入原生页面的时候调用该方法，更新页面路径
- (void)escalationData:(id)data {
    NSString *traceRoute = [data valueForKeyPath:@"traceRoute"];
    if (traceRoute.length > 0) {
        [MRKTraceManager sharedInstance].traceRoute = traceRoute;
    }
}

///h5已进入页面的时候调用该方法，替换当前页面的tracePageRoute和，tracePageId为的是像分享之类在当前页进行交互的事件
- (void)uploadBuryData:(id)data {
    UIViewController *controller = [UIViewController currentViewController];
    NSString *traceRoute = [data valueForKeyPath:@"traceRoute"];
    NSString *tracePageId = [data valueForKeyPath:@"tracePageId"];
    if (traceRoute.length > 0 ) {
        controller.tracePageRoute = traceRoute;
        [MRKTraceManager sharedInstance].traceRoute = traceRoute;
    }
    if (tracePageId.length > 0 ) {
        controller.tracePageId = tracePageId;
    }
}

// MARK: —— 跳转相关
///添加/编辑地址 2021-12-14 --wk
- (void)toAddressOperation:(id)data {
    //跳转到地址页面
    MRKAddressViewController *vc = [MRKAddressViewController controllerWithType:MRKAddressTypeShipping];
    //模型有数据为编辑，没有数据为添加
    if (data) {
        MRKAddressListModel *addressModel = [MRKAddressListModel modelWithJSON:data];
        vc.addressModel = addressModel;
    }
    vc.commitCallBcak  = ^{
        //地址编辑/添加成功之后的回调
        NSLog(@"活动报名地址回调");
    };
    [[UIViewController currentViewController].navigationController pushViewController:vc animated:YES];
}

///跳转挑战赛明细
- (void)toChallengeDetail:(id)data base:(__weak id)base {
    if ([base isKindOfClass:[BaseWKWebController class]]){
        BaseWKWebController *web = (BaseWKWebController *)base;
        if (web.activityId.length > 0) {
            MRKChallengeDetailVC *vc = [[MRKChallengeDetailVC alloc] init];
            vc.activityId = web.activityId;
            [[UIViewController currentViewController].navigationController pushViewController:vc animated:YES];
        }
    }
}

/// 进入数据中心
- (void)toDataCenter:(id)data {
    [self handleJumpToTabIndex:2];
    //    MRKMineDataViewController *vc = [[MRKMineDataViewController alloc] init];
    //    vc.hidesBottomBarWhenPushed = YES;
    //    [[UIViewController currentViewController].navigationController pushViewController:vc animated:YES];
}

/// 统一处理跳转到tab页
- (void)handleJumpToTabIndex:(NSInteger)index {
    UIWindow *window = [UIApplication sharedApplication].delegate.window;
    if ([window.rootViewController isKindOfClass:[UITabBarController class]]) {
        UIViewController *controller = [UIViewController currentViewController];
        NSUInteger count = controller.navigationController.childViewControllers.count;
        if (count != 0){
            [controller.navigationController popToRootViewControllerAnimated:YES];
        }
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [[NSNotificationCenter defaultCenter] postNotificationName:@"SetTabBarItemIndex" object:@(index)];
        });
        return;
    }
    /// 链路中的话，没有初始化tabbarcontroller。直接发登录成功通知初始化
    if ([Login isLogin]){
        [[NSNotificationCenter defaultCenter] postNotificationName:kLogin_Notification object:UserInfo.userId];
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [[NSNotificationCenter defaultCenter] postNotificationName:@"SetTabBarItemIndex" object:@(index)];
        });
    }
}

/// PlanExerciseReportWebController 的查看训练数据   跳转计划进度
- (void)toTrainingProgress:(id)data base:(__weak id)base{
    if ([base isKindOfClass:[PlanExerciseReportWebController class]]){
        PlanExerciseReportWebController *web = (PlanExerciseReportWebController *)base;
        MRKTrainingProgressVC *vc = [[MRKTrainingProgressVC alloc] init];
        vc.type = [web.activityId isNotBlank] ? TrainingDataTypeActivity : TrainingDataTypePlan;
        vc.model = web.model;
        [[UIViewController currentViewController].navigationController pushViewController:vc animated:YES];
    }
}

/// PlanExerciseReportWebController 重新训练 ///跳转计划详情
- (void)toCoursePlan:(id)data base:(__weak id)base{
    if ([base isKindOfClass:[PlanExerciseReportWebController class]]){
        PlanExerciseReportWebController *web = (PlanExerciseReportWebController *)base;
        MRKCoursePlanController *vc = [[MRKCoursePlanController alloc] init];
        vc.planType = CoursePlanTypeDetail;
        vc.model = ({
            MRKCoursePlanModel *model = [MRKCoursePlanModel new];
            model.cid = web.model.cid;
            model;
        });
        [[UIViewController currentViewController].navigationController pushViewController:vc animated:NO];
    }
}

// MARK: —— 返回
- (void)toHomeIndex:(id)data base:(__weak id)base {
    ///检查window根视图
    NSInteger index = [[data valueForKeyPath:@"index"] integerValue];
    [self handleJumpToTabIndex:index];
}

- (void)back:(id)data base:(__weak id)base {
    if ([base isKindOfClass:[WebViewViewController class]]){
        WebViewViewController *web = (WebViewViewController *)base;
        if (web.isNewUserPath){
            UIWindow *window = [UIApplication sharedApplication].delegate.window;
            if ([window.rootViewController isKindOfClass:[UITabBarController class]]){
                [web goback];
                return;
            }
            if ([Login isLogin]){
                [[NSNotificationCenter defaultCenter] postNotificationName:kLogin_Notification object:UserInfo.userId];
            }
            return;
        }
        
        [web goback];
    } else if ([base isKindOfClass:[ExerciseReportWebController class]]){
        ExerciseReportWebController *web = (ExerciseReportWebController *)base;
        [web closeWebPage];
    }
}

// MARK: —— 设备相关
/// 进设备搜索
- (void)toDeviceSearch:(id)data {
    NSString *productID = (NSString *)data;
    if(productID.intValue == HeartEquipment || productID.intValue == FatScaleEquipment ) {
        ///心率带/体脂秤
        ///跳转到搜索列表 选择型号 22-09-06
        ///新手链路是走到最底WeightDeviceBindSuccessViewController 返回判断
        DeviceFilterViewController *vc = [DeviceFilterViewController new];
        vc.equipmentType = productID;
        [[UIViewController currentViewController].navigationController pushViewController:vc animated:YES];
        return;
    }
    

    DeviceSearchViewController *vc = [DeviceSearchViewController new];
    vc.pageRelationPath = [UIViewController currentViewController].pageRelationPath;
    if ([productID isNotBlank]){
        vc.productID = productID;
    }else{
        vc.type = @(4); ///过滤四大件
    }
    [[UIViewController currentViewController].navigationController pushViewController:vc animated:YES];
}
///绑定设备
- (void)toBindDevice:(id)data {
    MRKChallengeEquipmentModel *model = [MRKChallengeEquipmentModel modelWithJSON:data];
    if (model.bindStatus) {
        return;
    }
    DeviceSearchViewController *vc = [DeviceSearchViewController new];
    vc.productID = model.typeId;
    vc.modelIdList = model.modelIdList;
    [[UIViewController currentViewController].navigationController pushViewController:vc animated:YES];
}
///智能调阻 引导购买
- (void)toGuideBuy:(id)data {
    // 判断有没有四大件设备
    [MBProgressHUD showLodingWithMessage:@"" view:[UIViewController currentViewController].view];
    [MRKDeviceURLRequest requestMyBigDeviceSuccess:^(id data) {
        [MBProgressHUD hideHUDForView:[UIViewController currentViewController].view];
        NSArray *array = (NSArray *)data;
        if (array.count > 0) {
            MRKDeviceModel *model = array.firstObject;
            [[RouteManager sharedInstance] jumpToUltraHomeVC:model.productId];
        }else {
            [[RouteManager sharedInstance] guideBuyDevice];
        }
    } fail:^(id data) {
        [MBProgressHUD hideHUDForView:[UIViewController currentViewController].view];
    }];
}




// MARK: —— 外链
- (void)toExternalLink:(id)data {
    NSString *linkUrl = [data valueForKeyPath:@"url"];
    NSString *tip = [data valueForKeyPath:@"tip"];
    
    UIApplication *application = [UIApplication sharedApplication];
    NSURL *url = [NSURL URLWithString:linkUrl];
    if ([application canOpenURL:url]) {
        if (@available(iOS 10.0, *)) {
            [application openURL:url
                         options:@{}
               completionHandler:^(BOOL success) {
                if (!success) {
                    [AppDelegate errorView:@"跳转失败,请重试"];
                }
            }];
        } else {
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"
            [application openURL:url];
#pragma clang diagnostic pop
        }
    } else {
        [AppDelegate errorView:tip];
    }
}

// MARK: —— 会员相关
///移除VIP页面
- (void)removeVipController:(id)data {
    NSLog(@"webjs:vipRedemptionSuccess ===============");
    ///通知购买成功
    [[NSNotificationCenter defaultCenter] postNotificationName:@"UpdateUserInfo" object:nil];
    [[NSNotificationCenter defaultCenter] postNotificationName:@"MeritVipCreateSuc" object:nil];
}

// MARK: —— 主题
- (void)changeTheme:(id)data {
    NSString *themeType = [data valueForKeyPath:@"theme"];
    if ([themeType isNotBlank]) {
        NSString *loacalTheme = [LEETheme currentThemeTag];
        if (![themeType isNotBlank]) return;
        BOOL isXenjoyType = [loacalTheme isEqualToString:THEME_XENJOY];
        
        if ([themeType isEqualToString:@"dark"]) {
            if (isXenjoyType) return;
            [LEETheme startTheme:THEME_XENJOY];
            
            ///缓存
            NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
            [userDefaults setObject:@"dark" forKey:[NSString stringWithFormat:@"UserAppTheme_%@", UserInfo.userId]];
            [userDefaults synchronize];
        }
        
        if ([themeType isEqualToString:@"light"]) {
            if (!isXenjoyType) return;
            [LEETheme startTheme:THEME_NORMAL];
            
            ///缓存
            NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
            [userDefaults setObject:@"light" forKey:[NSString stringWithFormat:@"UserAppTheme_%@", UserInfo.userId]];
            [userDefaults synchronize];
        }
    }
}

// MARK: —— 三方数据相关
/// 上传运动数据到第三方 2023-07-04 --wk
- (void)uploadSportDataToThird:(id)data {
    id trainId = [data objectForKey:@"trainId"];
    if ([trainId isNotEmpty]) {
        // 上传数据
        [MRKHealthManager writeSportData:[NSString stringWithFormat:@"%@", trainId]];
    }
}

/// 三方数据对接，授权成功
- (void)thirdDataAuthSuccess:(id)data base:(__weak id)base {
    if ([base isKindOfClass:[BaseWKWebController class]]){
        [(BaseWKWebController *)base goback];
    }
    NSString *string = [NSString stringWithFormat:@"%@",[data objectForKey:@"isAuthorization"]];
    [[NSNotificationCenter defaultCenter] postNotificationName:HUAWEI_Authorization_Success object:string];
    NSLog(@"%@",string);
}

// MARK: —— 分享
///2.7.0 产品百科 分享
- (void)webDataShare:(id)data {
    MRKShareModel *model = [MRKShareModel modelWithJSON:data];
    //    MRKShareView *view = [MRKShareView shareViewWithType:ShareActionViewReport andViewController:[UIViewController currentViewController]];
    //    view.shareModel = model;
    //    [view showInView:[UIViewController currentViewController].view];
    [FlutterManager shareCommonVCWithTitle:model.shareTitle text:model.shareDescrip link:model.shareUrl thumbImage:model.shareImageUrl];
}

// MARK: —— 分享图片
- (void)shareImage:(id)data base:(__weak id)base {
    dispatch_async(dispatch_get_main_queue(), ^{
        if ([base isKindOfClass:[PlanExerciseReportWebController class]]){
            //            PlanExerciseReportWebController *web = (PlanExerciseReportWebController *)base;
            //            //分享图片控制器
            //            [MRKCustomShareViewController showShareView:web shareType:MRKShareExerciseReport shareData:@{
            //                @"title":@"分享你的运动报告",
            //                @"contentImage":[UIImage imageWithBase64Data:data],
            //                @"shareTitle":web.titleStr
            //            }];
            
            NSString *imagebyte = [NSString stringWithFormat:@"%@",data];
            jxt_getSafeMainQueue(^{
                [FlutterManager shareImageVCWithImage:imagebyte];
            });
        }
        if ([base isKindOfClass:[ExerciseReportWebController class]]){
            NSString *imagebyte = [NSString stringWithFormat:@"%@",data];
            jxt_getSafeMainQueue(^{
                ExerciseReportWebController *vc = (ExerciseReportWebController *)base;
                if ([vc.reportUrl isEqualToString:MRKAppAIH5LinkCombine(MRKAIPlanReport)]) {
                    [FlutterManager shareImageVCFromImage:imagebyte from:1];
                } else {
                    [FlutterManager shareImageVCWithImage:imagebyte];
                }
            });
        }
        if ([base isKindOfClass:[WebViewViewController class]]){
            //            WebViewViewController*web = (WebViewViewController *)base;
            //            UIImage *image = [UIImage imageWithBase64Data:data];
            //            jxt_getSafeMainQueue(^{
            //                MRKShareImageController *vc = [[MRKShareImageController alloc] init];
            //                vc.closeBtn.traceEventId = @"btn_all_share_close";
            //                vc.shareClick = ^(NSInteger index) {
            //                    [[MRKTraceManager sharedInstance] manualUploadTraceType:2
            //                                                                  pageTitle:web.navTitle
            //                                                                     pageId:web.tracePageId
            //                                                                    eventId:@"btn_all_share_click"
            //                                                                      route:web.tracePageRoute
            //                                                                   duration:0
            //                                                                 extendPara:@{@"control_result": [MRKShareButtonsView userClickName: index]}];
            //                };
            //                vc.shareImage = image;
            //                NSDictionary *parms = @{
            //                    KNSemiModalOptionKeys.pushParentBack : @(YES),
            //                    KNSemiModalOptionKeys.transitionStyle : @(KNSemiModalTransitionStyleShareImage),
            //                    KNSemiModalOptionKeys.disableCancel : @(YES),
            //                    KNSemiModalOptionKeys.shareImage : image,
            //                    KNSemiModalOptionKeys.parentScale : @(0.8),
            //                    KNSemiModalOptionKeys.animationDuration : @(0.5),
            //                    KNSemiModalOptionKeys.parentAlpha : @(1.0),
            //                    KNSemiModalOptionKeys.shadowOpacity : @(0.3)
            //                };
            //                [web presentSemiViewController:vc withOptions:parms];
            //            });
            
            NSString *imagebyte = [NSString stringWithFormat:@"%@",data];
            jxt_getSafeMainQueue(^{
                [FlutterManager shareImageVCWithImage:imagebyte];
            });
        }
    });
}

// MARK: —— 22-06-14 修改分享方法，前端把分享参数传过来，我们不用调用接口
- (void)shareModal:(id)data bridge:(WKWebViewJavascriptBridge *)bridge {
    //    __block MRKShareView *view = [MRKShareView shareViewWithType:ShareActionViewReport andViewController:[UIViewController currentViewController]];
    //    [view showInView:[UIViewController currentViewController].view];
    //    //点击分享按钮时，才会确定分享的类型，是朋友圈还是好友
    //    @weakify(view);
    //    view.tapShareButtonBlock = ^(SSDKPlatformType type, void (^shareBlock)(NSString * _Nonnull, NSString * _Nonnull, id _Nonnull)) {
    //        NSNumber *shareType;
    //        //1:微信分享，2:微信朋友圈，3：qq,4:微博
    //        switch (type) {
    //            case SSDKPlatformSubTypeWechatSession:
    //                shareType = @1;
    //                break;
    //            case SSDKPlatformSubTypeWechatTimeline:
    //                shareType = @2;
    //                break;
    //            case SSDKPlatformTypeQQ:
    //                shareType = @3;
    //                break;
    //            case SSDKPlatformTypeSinaWeibo:
    //                shareType = @4;
    //                break;
    //
    //            default:
    //                break;
    //        }
    //
    //        [bridge callHandler:[MRKWebJSHandler operatorStr: MRKWebJSCallBackMethodShareType] data:shareType responseCallback:^(id responseData) {
    //            @strongify(view);
    //            NSLog(@"shareModal==%@" , responseData);
    //            view.shareUrl = [responseData objectForKey:@"shareUrl"];
    //            if (shareBlock) {
    //                shareBlock([responseData objectForKey:@"shareTitle"],[responseData objectForKey:@"shareDescrip"],[responseData objectForKey:@"shareImageUrl"]);
    //            }
    //        }];
    //    };
    [bridge callHandler:[MRKWebJSHandler operatorStr: MRKWebJSCallBackMethodShareType] data:@1 responseCallback:^(id responseData) {
        jxt_getSafeMainQueue(^{
            [FlutterManager shareCommonVCWithTitle:[responseData objectForKey:@"shareTitle"]
                                              text:[responseData objectForKey:@"shareDescrip"]
                                              link:[responseData objectForKey:@"shareUrl"]
                                        thumbImage:[responseData objectForKey:@"shareImageUrl"]];
        });
    }];
}

// MARK: —— 保存图片
- (void)saveImageToLocal:(id)data {
    [PHPhotoLibrary requestAuthorization:^(PHAuthorizationStatus status) {
        if (status == PHAuthorizationStatusRestricted || status == PHAuthorizationStatusDenied) {
            dispatch_async(dispatch_get_main_queue(), ^{
                //不是第一次请求权限，那么可以弹出权限提示，用户选择设置，即跳转到设置界面，设置权限
                [TGPermissionSetting showAlertToDislayPrivacySettingWithTitle:@"提示"
                                                                          msg:@"无访问相册权限，是否去打开权限"
                                                                       cancel:@"取消"
                                                                      setting:@"设置"];
            });
        } else {
            //要插入相册的图片
            UIImage *tempImage = [UIImage imageWithBase64Data:data];
            UIImageWriteToSavedPhotosAlbum(tempImage, self, @selector((image:didFinishSavingWithError:contextInfo:)), (__bridge void *)self);
        }
    }];
}

- (void)image:(UIImage *)image didFinishSavingWithError:(NSError *)error contextInfo:(void *)contextInfo {
    if (error) {
        [MBProgressHUD showMessage:@"图片保存失败"];
    } else {
        [MBProgressHUD showMessage:@"图片保存成功"];
    }
}

// MARK: —— 活动分享
- (void)shareBody:(id)data base:(__weak id)base {
    if ([base isKindOfClass:[WebViewViewController class]]){
        WebViewViewController *web = (WebViewViewController *)base;
        [MBProgressHUD showLodingWithMessage:@"" view:web.view];
        NSDictionary *parms= @{
            @"id":web.activityId?:@"",
            @"url":web.htmlURL?:@""
        };
        [MRKBaseRequest mrkPostRequestUrl:@"/course/activityShareController/share"
                                  andParm:parms
                 completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
            [MBProgressHUD hideHUDForView:web.view];
            NSString *shareUrl = [request.responseObject valueForKeyPath:@"data.share"]; //分享链接
            NSArray *shareDetail = [request.responseObject valueForKeyPath:@"data.activityShareList"];  //分享的内容
            //点击分享按钮时，才会确定分享的类型，是朋友圈还是好友
            MRKShareView *view = [MRKShareView shareViewWithType:ShareActionViewReport andViewController:web];
            view.shareUrl = shareUrl;
            @weakify(self);
            view.tapShareButtonBlock = ^(SSDKPlatformType type, void (^shareBlock)(NSString * _Nonnull, NSString * _Nonnull, id _Nonnull)) {
                @strongify(self);
                [self shareType:type shareDetail:shareDetail shareBlock:shareBlock];
            };
            [view showInView:web.view];
        } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
            [MBProgressHUD hideHUDForView:web.view];
        }];
    }
}

///获取活动页面分享的副标题
- (void)shareType:(SSDKPlatformType)type shareDetail:(NSArray *)detail shareBlock:(void(^)(NSString *, NSString *, id))shareBlock {
    // 1.分享至好友,2.分享至朋友圈图 只有微信朋友圈是分享至朋友圈
    NSDictionary *shareDic = [NSDictionary dictionary];
    NSString *shareType = @"1";
    switch (type) {
        case SSDKPlatformSubTypeWechatTimeline:
            shareType = @"2";
            break;
        default:
            break;
    }
    //筛选分享的数据
    for (NSDictionary * dic in detail) {
        if ([[NSString stringWithFormat:@"%@",[dic objectForKey:@"type"]] isEqualToString:shareType]) {
            shareDic = dic;
            break;
        }
    }
    /// 执行分享操作
    if (shareBlock) {
        shareBlock([shareDic objectForKey:@"title"],[shareDic objectForKey:@"describeInfo"],[shareDic objectForKey:@"image"]);
    }
}

// MARK: —— Flutter 分享
/// - Parameters:
///   - detailString: 大图
///   - simleString: 简图
- (void)flutterShare:(NSString *)detailString simle:(NSString *)simleString web:(ExerciseReportWebController *)web {
    if (web.showShare) {
        return;
    }
    NSString *url;
    if ([web.liveCourseModel.cover isNotBlank]) {
        url = web.liveCourseModel.cover;
    } else if ([web.courseImage isNotBlank]) {
        url = web.courseImage;
    }
    FlutterShareController *vc = [FlutterManager shareCustom];
    [vc receiveH5CodeWithLong:detailString sheet:simleString courseUrl:url trainId:web.exerciseID];
    vc.dismissBlock = ^{
        web.showShare = NO;
    };
    
    MRKNavigationController *nav = [[MRKNavigationController alloc] initWithRootViewController:vc];
    nav.modalPresentationStyle = UIModalPresentationFullScreen;
    [web presentViewController:nav animated:YES completion:nil];
    web.showShare = YES;
}

///分享运动数据的新方法
- (void)flutterShare:(id)data base:(__weak id)base {
    if ([base isKindOfClass:[ExerciseReportWebController class]]){
        @weakify(self);
        ExerciseReportWebController *web = (ExerciseReportWebController *)base;
        NSString *simpleImageStr = [data valueForKeyPath:@"simple"]; // 简略图片
        NSString *detailImageStr = [data valueForKeyPath:@"detail"]; // 详细图片
        dispatch_async(dispatch_get_main_queue(), ^{
            [self_weak_ flutterShare:detailImageStr simle:simpleImageStr web:web];
        });
    }
}

///使用flutter 分享，更改base64 传输方式
- (void)flutterShareByPiece:(id)data base:(__weak id)base {
    if ([base isKindOfClass:[ExerciseReportWebController class]]){
        ExerciseReportWebController *web = (ExerciseReportWebController *)base;
        // 接收json，在比对json是否接收完
        ExerciseReportShareSnippetModel *model = [ExerciseReportShareSnippetModel modelWithDictionary:data];
        [web.shareJsons addObject:model];
        if (web.shareJsons.count < model.totalPieces) {
            return;
        }
        // 排序
        web.shareJsons = [web.shareJsons sortedArrayUsingComparator:^NSComparisonResult(ExerciseReportShareSnippetModel*  _Nonnull obj1, ExerciseReportShareSnippetModel*  _Nonnull obj2) {
            return obj1.pieceNumber > obj2.pieceNumber;
        }].mutableCopy;
        
        // 拼接json，分享
        NSMutableString *jsonString = [NSMutableString string];
        for (ExerciseReportShareSnippetModel *m in web.shareJsons) {
            [jsonString appendString:m.dataPiece];
        }
        NSLog(@"整理好的分享json %@",jsonString);
        [web.shareJsons removeAllObjects];
        NSDictionary *dic = [NSDictionary dictionaryWithJsonString:jsonString];
        NSString *simpleImageStr = [dic valueForKeyPath:@"simple"]; // 简略图片
        NSString *detailImageStr = [dic valueForKeyPath:@"detail"]; // 详细图片
        @weakify(self);
        dispatch_async(dispatch_get_main_queue(), ^{
            [self_weak_ flutterShare:detailImageStr simle:simpleImageStr web:web];
        });
    }
}

// MARK: —— h5调用app
+ (NSString *)methodStr:(MRKWebJSMethod)type {
    switch (type) {
        case MRKWebJSMethodInitData:
            return @"initData";                         ///initData
            break;
        case MRKWebJSMethodExercisData:
            return @"exercisData";                      ///exercisData
            break;
        case MRKWebJSMethodEscalationData:
            return @"escalationData";                   ///escalationData
            break;
        case MRKWebJSMethodUploadBuryData:
            return @"uploadBuryData";                   ///uploadBuryData
            break;
            //        case MRKWebJSMethodShareBtn:
            //            return @"shareBtn";                 ///shareBtn
            //            break;
        case MRKWebJSMethodToTrainingData:
            return @"toTrainingData";                   ///toTrainingData
            break;
        case MRKWebJSMethodToRetraining:
            return @"toRetraining";                     ///toRetraining
            break;
        case MRKWebJSMethodShareImage:
            return @"shareImage";                       ///shareImage
            break;
        case MRKWebJSMethodShareTrainReportImage:
            return @"shareTrainReportImage";            ///shareTrainReportImage
            break;
        case MRKWebJSMethodShareTrainReportImageByPiece:
            return @"shareTrainReportImageByPiece";     ///shareTrainReportImageByPiece
            break;
        case MRKWebJSMethodUploadTrainId:
            return @"uploadTrainId";                    ///uploadTrainId
            break;
        case MRKWebJSMethodLinkDataCenter:
            return @"linkDataCenter";                   ///linkDataCenter
            break;
        case MRKWebJSMethodToHomeIndex:
            return @"toHomeIndex";                      ///toHomeIndex
            break;
        case MRKWebJSMethodBackFinish:
            return @"backFinish";                       ///backFinish
            break;
        case MRKWebJSMethodToExternalLink:
            return @"toExternalLink";                   ///toExternalLink
            break;
            //        case MRKWebJSMethodSendToAppData:
            //            return @"sendToAppData";            ///sendToAppData
            //            break;
        case MRKWebJSMethodToChallengeDetail:
            return @"toChallengeDetail";                ///toChallengeDetail
            break;
        case MRKWebJSMethodSaveImage:
            return @"saveImage";                        ///saveImage
            break;
        case MRKWebJSMethodWebDataShare:
            return @"webDataShare";                     ///webDataShare
            break;
        case MRKWebJSMethodActivityShareBtn:
            return @"activityShareBtn";                 ///activityShareBtn
            break;
        case MRKWebJSMethodToBindDevice:
            return @"toBindDevice";                     ///toBindDevice
            break;
        case MRKWebJSMethodToAddressOperation:
            return @"toAddressOperation";               ///toAddressOperation
            break;
        case MRKWebJSMethodShareModal:
            return @"shareModal";                       ///shareModal
            break;
        case MRKWebJSMethodSmartControl:
            return @"smartControl";                     ///smartControl
            break;
        case MRKWebJSMethodVipRedemptionSuccess:
            return @"vipRedemptionSuccess";             ///vipRedemptionSuccess
            break;
        case MRKWebJSMethodHuaweiAuthorization:
            return @"huaweiAuthorization";              ///huaweiAuthorization
            break;
        case MRKWebJSMethodChangeAppTheme:
            return @"changeAppTheme";                   ///changeAppTheme
            break;
        case MRKWebJSMethodLinkToSearchEquipment:
            return @"linkToSearchEquipment";            ///linkToSearchEquipment
            break;
        case MRKWebJSMethodApplePay:
            return @"linkPay";                          ///linkPay
            break;
        case MRKWebJSMethodShareHealthReport:
            return @"shareHealthReport";                ///shareHealthReport
            break;
        case MRKWebJSMethodHealthReportDetail:
            return @"linkHealthReportDetail";           ///linkHealthReportDetail
            break;
        case MRKWebJSMethodJumpRouter:
            return @"jumpRouter";                       ///linkHealthReportDetail
            break;
        case MRKWebJSMethodScan:
            return @"linkToEquipmentScan";              ///linkToEquipmentScan
        case MRKWebJSMethodNewUserAcceptSuc:
            return @"newUserVipAcceptSuc";              ///
            break;
        case MRKWebJSMethodSubscribeDeviceData:
            return @"toSubscribeDeviceData";            ///toSubscribeDeviceData
            break;
        case MRKWebJSMethodControlDevice:
            return @"toControlDevice";                  ///toControlDevice
            break;
        case MRKWebJSMethodLinkTrainPlanRecord:
            return @"linkTrainPlanRecord";
            break;
        case MRKWebJSMethodNewUserLinkTrainPlan:
            return @"newUserlinkTrainPlan";
            break;
        case MRKWebJSMethodSetScreenWidth:
            return @"setScreenWidth";
            break;
        case MRKWebJSMethodControlPermission:
            return @"controlPermission";
            break;
        case MRKWebJSMethodReanalysisFood:
            return @"reanalysisFood";
            break;
        case MRKWebJSMethodMotionDetail:
            return @"linkMotionDetail";
            break;
        case MRKWebJSMethodFoodRecord:
            return @"linkFoodRecord";
            break;
        case MRKWebJSMethodDietTakePhoto:
            return @"dietTakePhoto";
            break;
        case MRKWebJSMethodChangeGlobalSoundFlag:
            return @"changeGlobalSoundFlag";
            break;
        case MRKWebJSMethodLaunchNewAiPlan:
            return @"launchNewAiPlan";
            break;
        case MRKWebJSMethodHandleDesignatedPlan:
            return @"handleDesignatedPlan";
            break;
        case MRKWebJSMethodHandleSubscribe:
            return @"handleSubscribe";
            break;
        default:
            return @"";
            break;
    }
}

// MARK: —— app调用h5
+ (NSString *)operatorStr:(MRKWebJSCallBackMethod)type {
    switch (type) {
        case MRKWebJSCallBackMethodOnBack:
            return @"onBack";                   /// 返回退出  原生左上角返回点击调用h5的返回
            break;
        case MRKWebJSCallBackMethodShareBody:
            return @"shareBody";                /// 分享，右上角分享点击
            break;
        case MRKWebJSCallBackMethodSourceData:
            return @"sourceData";               /// 设备数据、控制档位、刷新设备等
            break;
        case MRKWebJSCallBackMethodShareImage:
            return @"shareImage";               /// 分享，右上角分享点击 PlanExerciseReportWebController
            break;
        case MRKWebJSCallBackMethodOnTrainingReportShare:
            return @"onTrainingReportShare";    /// 监听到截屏调起h5的分享方法
            break;
        case MRKWebJSCallBackMethodShowModal:
            return @"showModal";                /// 展示h5的提示框
            break;
        case MRKWebJSCallBackMethodShareType:
            return @"shareType";                /// 分享时h5获取分享参数
            break;
        case MRKWebJSCallBackMethodWebRefresh:
            return @"webPageRefresh";           /// H5页面刷新
            break;
        case MRKWebJSCallBackMethodDeviceControl: /// 发送设备指令回调
            return @"handleDeviceControl";
            break;
        case MRKWebJSCallBackMethodPermissionControl: /// 发送设备指令回调
            return @"handlePermissionControl";
            break;
        case MRKWebJSCallBackAppMessage:
            return @"callbackAppMessage";
            break;
        default:
            return @"";
            break;
    }
}

@end
