//
//  DYFStoreManager.m
//
//  Created by dyf on 2014/11/4. ( https://github.com/dgynfi/DYFStoreKit )
//  Copyright © 2014 dyf. All rights reserved.
//
// Permission is hereby granted, free of charge, to any person obtaining a copy
// of this software and associated documentation files (the "Software"), to deal
// in the Software without restriction, including without limitation the rights
// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
// copies of the Software, and to permit persons to whom the Software is
// furnished to do so, subject to the following conditions:
//
// The above copyright notice and this permission notice shall be included in
// all copies or substantial portions of the Software.
//
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
// THE SOFTWARE.
//

#import "DYFStoreManager.h"

@interface DYFStoreManager () <DYFServeReceiptVerifierDelegate>
@property (nonatomic, strong) DYFStoreNotificationInfo *purchaseInfo;
@property (nonatomic, strong) DYFStoreNotificationInfo *downloadInfo;
@property (nonatomic, strong) DYFServeReceiptVerifier *receiptVerifier;
@end

@implementation DYFStoreManager

// Provides a global static variable.
static DYFStoreManager *_instance = nil;

+ (instancetype)shared {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        _instance = [[self alloc] init];
    });
    return _instance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        [self addStoreObserver];
    }
    return self;
}

- (void)dealloc {
     NSLog(@"😊😊😊-----dealloc-----😊😊😊%@" , NSStringFromClass([self class]));
    [NSNotificationCenter.defaultCenter removeObserver:self];
}

- (void)addStoreObserver {
    [NSNotificationCenter.defaultCenter addObserver:self
                                           selector:@selector(processPurchaseNotification:)
                                               name:DYFStorePurchasedNotification
                                             object:nil];
    
    [NSNotificationCenter.defaultCenter addObserver:self
                                           selector:@selector(processDownloadNotification:)
                                               name:DYFStoreDownloadedNotification
                                             object:nil];
}


- (void)addPayment:(NSString *)productIdentifier {
    [self addPayment:productIdentifier userIdentifier:nil];
}

- (void)addPayment:(NSString *)productIdentifier userIdentifier:(NSString *)userIdentifier {
    [self showLoading:@"等待中..."]; // Initiate purchase request.
    [DYFStore.defaultStore purchaseProduct:productIdentifier userIdentifier:userIdentifier];
}



/**
 内购商品类型 : 消耗型项目,  消耗项目,  自动续期订阅, 非续期自动订阅
 其中"非消耗型"和"自动续期订阅"需要提供恢
 
 */
- (void)restorePurchases {
    [self restorePurchases:nil];
}

- (void)restorePurchases:(NSString *)userIdentifier {
    /// finishTransaction
    NSArray *transactions = [SKPaymentQueue defaultQueue].transactions;
    DYFStoreLog(@"transactions ==== %@",transactions);
    
    DYFStoreLog(@"restorePurchases userIdentifier: %@", userIdentifier);
    [self showLoading:@"恢复中..."];
    [DYFStore.defaultStore restoreTransactions:userIdentifier];
}






- (void)processPurchaseNotification:(NSNotification *)notification {
    [self hideLoading];
    self.purchaseInfo = notification.object;
    
    switch (self.purchaseInfo.state) {
        case DYFStorePurchaseStatePurchasing:
            [self showLoading:@"购买中..."];
            break;
        case DYFStorePurchaseStateCancelled:
            [self sendNotice:@"取消购买"];
            break;
        case DYFStorePurchaseStateFailed:{
            DYFStoreNotificationInfo *info = self.purchaseInfo;
            
            if (info.error.userInfo[NSUnderlyingErrorKey] != NULL) {
                NSError *error = info.error.userInfo[NSUnderlyingErrorKey];
                if (error.code == 3532) {
                    [self sendNotice:@"您目前已订阅此项目"];
                    return;
                }
            }

            [self sendNotice:[NSString stringWithFormat:@"购买出现错误, %zi", self.purchaseInfo.error.code]];
        } break;
        case DYFStorePurchaseStateSucceeded:
        case DYFStorePurchaseStateRestored:
            [self completePayment];
            break;
        case DYFStorePurchaseStateNoneRestore:
            [self sendNotice:@"暂无可恢复订单"];
            break;
        case DYFStorePurchaseStateRestoreFailed:
            [self sendNotice:[NSString stringWithFormat:@"购买出现错误, %zi", self.purchaseInfo.error.code]];
            break;
        case DYFStorePurchaseStateDeferred:
            DYFStoreLog(@"延迟");
            break;
        default:
            break;
    }
}

- (void)processDownloadNotification:(NSNotification *)notification {
    self.downloadInfo = notification.object;
    
    switch (self.downloadInfo.downloadState) {
        case DYFStoreDownloadStateStarted:
            DYFStoreLog(@"开始下载");
            break;
        case DYFStoreDownloadStateInProgress:
            DYFStoreLog(@"下载进度: %.2f%%", self.downloadInfo.downloadProgress);
            break;
        case DYFStoreDownloadStateCancelled:
            DYFStoreLog(@"下载取消");
            break;
        case DYFStoreDownloadStateFailed:
            DYFStoreLog(@"下载失败");
            break;
        case DYFStoreDownloadStateSucceeded:
            DYFStoreLog(@"下载完成: 100%%");
            break;
        default:
            break;
    }
}


- (void)completePayment {
    DYFStoreNotificationInfo *info = self.purchaseInfo;
    
    ///苹果返回购买成功的回调后 本地存储信息
    DYFStoreUserDefaultsPersistence *persister = [[DYFStoreUserDefaultsPersistence alloc] init];
    NSString *identifier = info.transactionIdentifier;
    if (![persister containsTransaction:identifier]) {
        [self storeReceipt];
        return;
    }
    
    DYFStoreTransaction *tx = [persister retrieveTransaction:identifier];
    NSData *receiptData = tx.transactionReceipt.base64DecodedData;
    
    DYFStoreLog(@"transaction.state: %zi", tx.state);
    DYFStoreLog(@"transaction.productIdentifier: %@", tx.productIdentifier);
    DYFStoreLog(@"transaction.userIdentifier: %@", tx.userIdentifier);
    DYFStoreLog(@"transaction.transactionIdentifier: %@", tx.transactionIdentifier);
    DYFStoreLog(@"transaction.transactionTimestamp: %@", tx.transactionTimestamp);
    DYFStoreLog(@"transaction.transactionReceipt: %@", receiptData);
    
    DYFStoreLog(@"transaction.originalTransactionIdentifier: %@", tx.originalTransactionIdentifier);
    DYFStoreLog(@"transaction.originalTransactionTimestamp: %@", tx.originalTransactionTimestamp);
   
    [self verifyReceipt:receiptData andTransaction:tx];
}

- (void)storeReceipt {
    DYFStoreLog();
    
    NSURL *receiptURL = DYFStore.receiptURL;
    NSData *data = [NSData dataWithContentsOfURL:receiptURL];
    if (!data || data.length == 0) {
        [self refreshReceipt];
        return;
    }
    
    DYFStoreNotificationInfo *info = self.purchaseInfo;
    DYFStoreTransaction *transaction = [[DYFStoreTransaction alloc] init];
    if (info.state == DYFStorePurchaseStateSucceeded) {
        transaction.state = DYFStoreTransactionStatePurchased;
    } else if (info.state == DYFStorePurchaseStateRestored) {
        transaction.state = DYFStoreTransactionStateRestored;
    }
    transaction.productIdentifier = info.productIdentifier;
    transaction.userIdentifier = info.userIdentifier;
    transaction.transactionIdentifier = info.transactionIdentifier;
    transaction.transactionTimestamp = info.transactionDate.timestamp;
    transaction.originalTransactionTimestamp = info.originalTransactionDate.timestamp;
    transaction.originalTransactionIdentifier = info.originalTransactionIdentifier;
    transaction.transactionReceipt = data.base64EncodedString;
    
    DYFStoreLog(@"transaction.state: %zi", transaction.state);
    DYFStoreLog(@"transaction.productIdentifier: %@", transaction.productIdentifier);
    DYFStoreLog(@"transaction.userIdentifier: %@", transaction.userIdentifier);
    DYFStoreLog(@"transaction.transactionIdentifier: %@", transaction.transactionIdentifier);
    DYFStoreLog(@"transaction.transactionTimestamp: %@", transaction.transactionTimestamp);
    DYFStoreLog(@"transaction.originalTransactionIdentifier: %@", transaction.originalTransactionIdentifier);
    DYFStoreLog(@"transaction.originalTransactionTimestamp: %@", transaction.originalTransactionTimestamp);
    
    ///存储中
    DYFStoreUserDefaultsPersistence *persister = [[DYFStoreUserDefaultsPersistence alloc] init];
    [persister storeTransaction:transaction];
    
    DYFStoreLog(@"transaction.transactionReceipt: %@", data);
    [self verifyReceipt:data andTransaction:transaction];
}

- (void)refreshReceipt {
    DYFStoreLog();
    [self showLoading:@"刷新购买数据..."];
    
    [DYFStore.defaultStore refreshReceiptOnSuccess:^{
        [self storeReceipt];
    } failure:^(NSError *error) {
        [self failToRefreshReceipt];
    }];
}

- (void)failToRefreshReceipt {
    DYFStoreLog();
    [self hideLoading];
    
    [self showAlertWithTitle:@"提示"
                     message:@"确认购买失败! 请检查设备网络是否正常。"
           cancelButtonTitle:@"取消"
                      cancel:^(UIAlertAction *action) {}
          confirmButtonTitle:@"重试"
                     execute:^(UIAlertAction *action) {
        [self refreshReceipt];
    }];
}

// It is better to use your own server to obtain the parameters uploaded from the client to verify the receipt from the app store server (C -> Uploaded Parameters -> S -> App Store S -> S -> Receive And Parse Data -> C).
// If the receipts are verified by your own server, the client needs to upload these parameters, such as: "transaction identifier, bundle identifier, product identifier, user identifier, shared sceret(Subscription), receipt(Safe URL Base64), original transaction identifier(Optional), original transaction time(Optional) and the device information, etc.".
//如果收据是由你自己的服务器验证的，客户端需要上传这些参数，例如:“事务标识符、包标识符、产品标识符、用户标识符、共享的页面(订阅)、收据(安全URL Base64)、原始事务标识符(可选)、原始事务时间(可选)和设备信息等”。
- (void)verifyReceipt:(NSData *)receiptData andTransaction:(DYFStoreTransaction *)transaction{
    DYFStoreLog();
    [self hideLoading];
    [self showLoading:@"确认购买..."];
    
    if (!_receiptVerifier) {
        _receiptVerifier = [[DYFServeReceiptVerifier alloc] init];
        _receiptVerifier.delegate = self;
    }
    ///服务器校验
    NSData *data = receiptData ?: [NSData dataWithContentsOfURL:DYFStore.receiptURL];
    DYFStoreLog(@"data: %@", data);
    [_receiptVerifier verifyReceipt:data andTransaction:transaction];
    
    // Only used for receipts that contain auto-renewable subscriptions.
    // 仅用于包含自动更新订阅的收据。
    //[_receiptVerifier verifyReceipt:data sharedSecret:@"A43512564ACBEF687924646CAFEFBDCAEDF4155125657"];
}



- (void)retryToVerifyReceipt {
    DYFStoreNotificationInfo *info = self.purchaseInfo;
    DYFStoreUserDefaultsPersistence *persister = [[DYFStoreUserDefaultsPersistence alloc] init];
    
    NSString *identifier = info.transactionIdentifier;
    DYFStoreTransaction *transaction = [persister retrieveTransaction:identifier];
    NSData *receiptData = transaction.transactionReceipt.base64DecodedData;
    [self verifyReceipt:receiptData andTransaction:transaction];
}


- (void)verifyReceiptDidFinish:(nonnull DYFServeReceiptVerifier *)verifier didReceiveData:(nullable id)data {
    DYFStoreLog(@"data: %@", data);
    @weakify(self);
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        @strongify(self);
        [[NSNotificationCenter defaultCenter] postNotificationName:@"UpdateUserInfo" object:nil];
        [[NSNotificationCenter defaultCenter] postNotificationName:@"MeritVipCreateSuc" object:nil];
        
        [self hideLoading];
        [self finishTransaction];
        
        NSInteger status = [data integerValue];
        if (status == 20230109) { ///消单子
            
        }else{
            [self showTipsMessage:@"购买成功"];
        }
    });
}

- (void)verifyReceipt:(nonnull DYFServeReceiptVerifier *)verifier didFailWithError:(nonnull NSError *)error {
    // Prints the reason of the error.
    DYFStoreLog(@"error: %zi, %@", error.code, error.localizedDescription);
    [self hideLoading];
    
    // An error occurs that has nothing to do with in-app purchase. Maybe it's the internet.
    // 发生与应用内购买无关的错误。也许是因为互联网。
    if (error.code < 21000) {
        // After several attempts, you can cancel refreshing receipt.
        [self showAlertWithTitle:@"提示"
                         message:@"未能核实购买! 请检查设备网络是否正常。"
               cancelButtonTitle:@"取消"
                          cancel:NULL
              confirmButtonTitle:@"重试"
                         execute:^(UIAlertAction *action) {
            [self verifyReceipt:nil andTransaction:verifier.transaction];
        }];
        return;
    }
    
    [self showTipsMessage:@"未能购买商品!"];
    
    [self finishTransaction];
}

- (void)finishTransaction{
    dispatch_time_t time = dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC));
    dispatch_after(time, dispatch_get_main_queue(), ^{
     
        DYFStore *store = DYFStore.defaultStore;
        DYFStoreNotificationInfo *info = self.purchaseInfo;
        if (info.state == DYFStorePurchaseStateRestored) {
            SKPaymentTransaction *transaction = [store extractRestoredTransaction:info.transactionIdentifier];
            [store finishTransaction:transaction];
        } else {
            SKPaymentTransaction *transaction = [store extractPurchasedTransaction:info.transactionIdentifier];
            // The transaction can be finished only after the client and server adopt secure communication and data encryption and the receipt verification is passed. In this way, we can avoid refreshing orders and cracking in-app purchase. If we were unable to complete the verification, we want `StoreKit` to keep reminding us that there are still outstanding transactions.
            [store finishTransaction:transaction];
        }
        
        DYFStoreUserDefaultsPersistence *persister = [[DYFStoreUserDefaultsPersistence alloc] init];
        [persister removeTransaction:info.transactionIdentifier];
        if (info.originalTransactionIdentifier) {
            [persister removeTransaction:info.originalTransactionIdentifier];
        }
    });
}

- (void)sendNotice:(NSString *)message {
    [self showAlertWithTitle:@"提示"
                     message:message
           cancelButtonTitle:nil
                      cancel:NULL
          confirmButtonTitle:@"知道了"
                     execute:^(UIAlertAction *action) {
        DYFStoreLog(@"alert action title: %@", action.title);
    }];
}


@end
