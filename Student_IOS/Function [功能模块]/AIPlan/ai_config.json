{"aiPrologue": {"manHint": ["戳我！戳我！我是Mia！\n和我说说话！", "点点我！\n和我聊！我就是你的Mia！", "我是你的Mia！\n想了解健身就戳我哦！"], "womanHint": ["戳我！戳我！我是Mia！\n和我说说话！", "点点我！\n和我聊！我就是你的Mia！", "我是你的Mia！\n想了解健身就戳我哦！"], "otherHint": ["戳我！戳我！我是Mia！\n和我说说话！", "点点我！\n和我聊！我就是你的Mia！", "我是你的Mia！\n想了解健身就戳我哦！"]}, "consecutiveMissedDays": 5, "aiPlanNeedCount": 2, "oldPlanCount": 3, "aiPlanStartTxt": "通过您的行为数据和身体数据，为您生成私人定制化训练教程！请跟上Mia的步伐 我们一起乘风破浪！", "aiPlanDeviceMenu": [{"key": "无器械", "value": ""}, {"key": "动感单车", "value": "1"}, {"key": "椭圆机", "value": "6"}, {"key": "划船机", "value": "5"}, {"key": "跑步机", "value": "2"}], "aiPlanDayMenu": [{"key": "7天焕新活力", "value": "7"}, {"key": "14天进阶飞跃", "value": "14"}, {"key": "28天极致蜕变", "value": "28"}], "aiPlanDayMenuAdvisNum": "14", "aiModelName": "deepseek", "aiModelNameM": "DeepSeek", "aiModelIcon": "https://static.merach.com/ai/icon_aiplan_deepseek.png", "aiPlanAskForRest": {"men": ["加班加班加班！", "是个病号", "Di！摸鱼卡！", "兄弟开黑日，健身下一次！"], "women": ["老板画饼我摆烂，明日再卷", "病号请签收：躺着就是胜利", "Di！摸鱼卡！", "姨妈来了，申请休息！", "干饭人干饭魂！"]}, "aiPlanAskForLeave": {"men": ["加班加班加班！", "是个病号", "Di！摸鱼卡！", "兄弟开黑日，健身下一次！"], "women": ["老板画饼我摆烂，明日再卷", "病号请签收：躺着就是胜利", "Di！摸鱼卡！", "姨妈来了，申请休息！", "干饭人干饭魂！"]}, "dietSportAnalysisCount": 1, "dietImageRecognitionCount": 1, "dietTextRecognitionCount": 1, "leastEatCalorieSepYellow": 0, "leastEatCalorieSepRed": -800, "drinkSelectLeast": 100, "drinkSelectMost": 4000, "voiceCourseCount": 0, "voiceMotionCount": 0, "identifyFoodHint": "发送你想记录的食物和餐别，例如：\n· 早餐吃了一个包子、一杯牛奶、一个鸡蛋\n· 午餐吃了青椒肉丝盖饭\n· 晚餐吃了鸭血粉丝汤、2个生煎包", "identifyCourseHint": "一句话调整课程难度、时长、训练器械，例如：\n· “降低课程难度”或“将课程难度降到M1”\n· 更换为15分钟的课程\n· 更换为椭圆机的课程，保持难度和时长", "identifyMotionHint": "一句话调整训练动作难度、锻炼部位，例如：\n· 更换锻炼部位为腿部\n· 希望将训练时长缩短到15秒\n· 更换训练部位为手臂并降低动作难度", "userpipelineStartTxt": "HI,我是Mia\n\n我将问你一些问题，帮助我了解你以便为你制定专属个性化计划\n\n让我们开始吧！", "healthType": {"title": "请选择日常活动水平\nMia为你计算热量缺口", "items": [{"id": "1", "key": "SEDENTARY", "value": "不怎么活动（久坐）"}, {"id": "2", "key": "LIGHT_ACTIVITY", "value": "轻度活动（无规律的轻量级活动）"}, {"id": "3", "key": "MODERATE_ACTIVITY", "value": "适度运动（每周规律锻炼1~2天）"}, {"id": "4", "key": "HIGHLY_ACTIVE", "value": "高活动水平（每周规律锻炼3～5天）"}, {"id": "5", "key": "PROFESSIONAL_ATHLETE", "value": "职业级训练（每周6~7天高强度训练）"}]}, "injuredPart": {"title": "哪些部位受过伤(多选)", "items": [{"id": "0", "value": "无"}, {"id": "1", "value": "脚踝"}, {"id": "2", "value": "膝盖"}, {"id": "3", "value": "手腕"}, {"id": "4", "value": "腿部"}, {"id": "5", "value": "背/腰部"}, {"id": "6", "value": "颈部"}]}, "dietHabits": {"title": "饮食偏好或忌口（多选）", "items": [{"id": "0", "value": "无"}, {"id": "1", "value": "乳糖不耐受"}, {"id": "2", "value": "蛋白质过敏"}, {"id": "3", "value": "海鲜过敏"}, {"id": "4", "value": "孕期哺乳期饮食"}, {"id": "5", "value": "素食主义者"}, {"id": "6", "value": "其他"}]}, "deviceSelect": {"title": "选择常用运动器械(多选)", "items": [{"id": "0", "value": "徒手训练"}, {"id": "1", "value": "动感单车"}, {"id": "6", "value": "椭圆机"}, {"id": "5", "value": "划船机"}, {"id": "2", "value": "跑步机"}]}, "resetQuestionnaire": {"title": "为什么要重新生成计划呢？", "items": [{"id": "0", "value": "当前进度与目标严重偏离"}, {"id": "1", "value": "不喜欢推荐的内容"}, {"id": "2", "value": "受伤、疾病、妊娠"}, {"id": "3", "value": "更换运动器械"}, {"id": "4", "value": "健康目标调整"}]}, "voiceTip": "Mia在听，你说吧～\n\n你可以说：\n1. 帮我记录早餐，2个包子，1个鸡蛋/晚餐，1碗重庆小面。（消耗2次）\n2. 帮我找一节10分钟动感单车的课程。（消耗1次）\n3. 帮我找一节训练腿部的动作。（消耗1次）\n\n来和我聊聊吧！", "fatSpeedTip": ["缓慢减重，身体适应更轻松呢～", "推荐速度，稳扎稳打，说！你能做到", "较快减重，需要更强的意志力哦~"]}