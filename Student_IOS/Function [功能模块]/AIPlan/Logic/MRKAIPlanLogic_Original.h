//
//  MRKAIPlanLogic.h
//  Student_IOS
//
//  Created by merit on 2025/3/3.
//

#import <Foundation/Foundation.h>
#import "MRKDailyScheduleModel.h"
#import "MRKAIPlanModel.h"
#import "MRKAIPlanCheckModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface MRKAIPlanLogic : NSObject

+ (nullable instancetype)shared;

@property (nonatomic, strong) MRKAIConfigModel *config;

@property (nonatomic, copy, nonnull, readonly) NSString *config_tip;

@property (nonatomic, strong) NSDictionary *configDict;

/// mia入口
- (void)jumpToAIChat;
- (void)jumpToAIChatWithVoiceText:(NSString *)text;
- (void)jumpToAIChatFirstTip;

/// firstIntoPlanReport 完成任务页面写死yes, 训练记录列表写死no
- (void)jumpToAIReport:(BOOL)firstIntoPlanReport
                planId:(NSString *)planId
               version:(NSInteger)version;

/// 计划入口
- (void)jumpToAIPlan;

/// 重新生成计划+二次确认弹窗
- (void)regenerateAIPlanAndConfirm;

/// 重新生成，直接走判断次数
//- (void)regenerateAIPlan;

/// 调整今天的运动项
- (void)changeAIPlanTodayContent:(NSString *)planId currentDay:(NSString *)currentDay;

/// 调整计划
- (void)changeAIPlan:(NSString *)planId currentDay:(NSString *)currentDay;

/// 判断计划是否过期
//- (void)judgeAIPlanExpire:(void(^)(MRKPlanOverViewModel *))unExpireBlock;

/// 进入AI计划入口页
- (void)jumpToAIPlanEntrancePage;

/// 进入AI计划启动页
- (void)jumpToAIPlanStartPage;

/// 进入AI计划生成页
- (void)jumpToAIPlanGeneratePage:(NSDictionary *)param;
- (void)jumpToAIPlanDetailPage:(NSString *)planId;


/// 进入AI重置原因页面
- (void)jumpToAIPlanResetPage:(NSString *)planId;

/// 进入更换课程/更换动作弹窗
- (void)jumpToAIPlanAdjustPage:(int)type;

/// 进入AI计划详情页面
- (void)jumpToAIPlanDetailPage:(NSString *)planId version:(NSInteger)version;

///// 进入AI计划详情页面 判断下是否过期
//- (void)jumpToAIPlanDetailPageAndJudgeExpire:(NSString *)planId;

/// 进入AI计划完成页
- (void)jumpToAIPlanDone:(MRKAIPlanCheckModel *)model;

/// 点击AI计划任务训练
- (void)jumpToAIPlanTaskTrain:(MRKDailyScheduleItemModel *)model andTimeReference:(NSString *)timeReference;
- (void)jumpToTaskTrain:(MRKDailyScheduleItemModel *)model;

/// 请求权益次数
- (void)requestAIBenefit:(void(^)(MRKBenefitModel *))success failure:(void(^)(void))failure;

/// 展示vip弹窗，开通会员/次数不足
- (void)showVipAlert:(int)source;

/// Mia会员引导弹窗
- (void)showVipAlert:(int)source normalPageId:(NSString *)normalPageId vipPageId:(NSString *)vipPageId;



/// 开启本周并动态延长
- (void)startAndExtendCurrentWeek;

/// AI动态开启本周计划
- (void)dynamicStartCurrentWeek;

/// 销假
- (void)cancelLeave;

/// 去记录 [默认今日]
- (void)recordDiet:(BOOL)fromMainPage;

/// 去记录 [默认date 结构"yyyy-MM-dd"]
- (void)recordDiet:(NSString *)date fromMainPage:(BOOL)fromMainPage;

/// 去运动 [筛选今日任务->跳详情]
- (void)goToTaskTrain;

/// V2计划查看报告
- (void)dietAIPlanReport:(NSString *)planId;
- (void)AIPlanReport;



///日程页课程/动作换一换
- (void)planTaskExchange:(MRKDailyScheduleItemModel *)model;

///日程页动作换时长/次水
- (void)planMotionTaskDurtionOrCountExchange:(MRKDailyScheduleItemModel *)model;


- (void)updateDailySchedulePage;

// MARK: ———————————— 弹窗统一样式 ————————————
- (void)showGeneralAlertWithTitle:(NSString *)title
                          message:(NSString *)message
                cancelButtonTitle:(NSString *)cancelButtonTitle
               confirmButtonTitle:(NSString *)confirmButtonTitle
                           cancel:(dispatch_block_t)cancelHandler
                          confirm:(dispatch_block_t)executableHandler;

- (void)showGeneralAlertWithTitle:(NSString *)title
                          message:(NSString *)message
                cancelButtonTitle:(NSString *)cancelButtonTitle
     confirmButtonAttributedTitle:(NSAttributedString *)confirmButtonTitle
                           cancel:(dispatch_block_t)cancelHandler
                          confirm:(dispatch_block_t)executableHandler;
@end

NS_ASSUME_NONNULL_END
