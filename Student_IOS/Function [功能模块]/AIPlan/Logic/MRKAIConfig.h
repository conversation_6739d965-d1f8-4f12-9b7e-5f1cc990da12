//
//  MRKAIConfig.h
//  Student_IOS
//
//  Created by <PERSON><PERSON> on 2025/3/17.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@class MRKAIConfigModel;
@interface MRKAIConfig : NSObject
//@property (nonatomic, strong, readonly) MRKAIConfigModel *currentConfig;

+ (instancetype)shared;

/**
  请求AI配置
  1，优先读取网络缓存，不存在本地缓存读取
  2，服务器获取后再缓存本地
 */
- (void)requestAIConfig;

@end

NS_ASSUME_NONNULL_END
