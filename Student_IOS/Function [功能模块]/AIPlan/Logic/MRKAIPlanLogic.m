//
//  MRKAIPlanLogic.m
//  Student_IOS
//
//  Created by merit on 2025/3/3.
//

#import "MRKAIPlanLogic.h"
#import "MrkGeneralAlertView.h"
#import "ExerciseReportWebController.h"
#import "MRKAIPlanDoneController.h"
#import "MRKMainPageController.h"
#import "MRKDailyScheduleController.h"
#import "MRKAiPlanEditAlert.h"

#import "MRKAIPlanAPIClient.h"
#import "MRKDailyScheduleDietAlert.h"
#import "MRKPopupManager.h"
#import "MRKRenewWeightAlert.h"
#import "MRKUpdateWeightManager.h"
#import "MRKDatePickerManager.h"



@interface MRKAIPlanLogic()
@property (nonatomic, strong) MRKUpdateWeightManager *updateWeightManager;
@end

@implementation MRKAIPlanLogic

- (MRKUpdateWeightManager *)updateWeightManager{
    if (!_updateWeightManager) {
        _updateWeightManager = [[MRKUpdateWeightManager alloc] init];
    }
    return _updateWeightManager;
}

static MRKAIPlanLogic *_instance = nil;
+ (instancetype)shared
{
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        _instance = [[MRKAIPlanLogic alloc] init];
    });
    return _instance;
}

- (NSString *)config_tip {
    MRKAIConfigModel *item = [MRKAIPlanLogic shared].config;
    if (!item) return @"Hi~ 我是你的健康伙伴\n24小时在线待命！";
    ///性别：0-未知，1-男，2-女
    NSArray *array = @[];
    NSInteger sex = UserInfo.sex.integerValue;
    switch (sex) {
        case 0:
            array = item.aiPrologue.otherHint;
            break;
        case 1:
            array = item.aiPrologue.manHint;
            break;
        case 2:
            array = item.aiPrologue.womanHint;
            break;
        default:
            break;
    }
    
    if (array.count == 0) {
        return @"Hi~ 我是你的健康伙伴\n24小时在线待命！";
    }
    
    NSInteger randomIndex = arc4random_uniform((uint32_t)array.count);
    return array[randomIndex];
}




// MARK: ———————————— AI入口逻辑 ————————————

/// mia chart 入口
- (void)jumpToAIChat{
    NSString *aiChatUrl = MRKAppAIH5LinkCombine(MRKAIChat);
    [[RouteManager sharedInstance] openAppJumpH5Url:aiChatUrl];
    ReportMrkLogParms(2, @"点击Mia图标", @"page_home", @"button_mia_icon", nil, 0, nil);
}

/// mia chart 语音
- (void)jumpToAIChatWithVoiceText:(NSString *)text {
    NSString *aiChatUrl = MRKAppAIH5LinkCombine(MRKAIChat);
    [[RouteManager sharedInstance] skipToMia:aiChatUrl text:text];
}

/// 全部课程进mia入口先弹窗
- (void)jumpToAIChatFirstTip{
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    BOOL RegulateTip = [userDefaults boolForKey:@"AIPlanRegulateTip"];
    if (!RegulateTip){
        MRKAiPlanEditAlert *alert = [[MRKAiPlanEditAlert alloc] initWithAnimationStyle:MRKActionAlertViewTransitionStyleFade];
        alert.editBlock = ^(NSInteger index) {
            NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
            [userDefaults setBool:YES forKey:@"AIPlanRegulateTip"];
            [userDefaults synchronize];
            [MRKAIPlanLogic.shared jumpToAIChat];
        };
        [alert show];
    } else {
        [MRKAIPlanLogic.shared jumpToAIChat];
    }
}







// MARK: ———————————— AIPlan 训练报告入口 ————————————
/// 训练报告入口
/// 完成任务页面写死yes, 列表写死no
- (void)jumpToAIReport:(BOOL)firstIntoPlanReport
                planId:(NSString *)planId
               version:(NSInteger)version {
    
    NSString *aiReportUrl = version == 2 ? MRKAppAIH5LinkCombine(MRKAIPlanReportV2): MRKAppAIH5LinkCombine(MRKAIPlanReport);
    ExerciseReportWebController *vc = [[ExerciseReportWebController alloc] init];
    vc.reportUrl = aiReportUrl;
    vc.exerciseID = planId;
    vc.firstInto = firstIntoPlanReport;
    vc.hidesBottomBarWhenPushed = YES;
    [[UIViewController currentViewController].navigationController pushViewController:vc animated:YES];
}

// MARK: ———————————— AIPlan入口逻辑 ————————————
/// AI计划入口
- (void)jumpToAIPlan{
    @weakify(self);
    [self requestPlanOverview:^(MRKPlanOverViewModel *item) {
        @strongify(self);
        /// AI计划入口根据当前用户AI计划制定情况处理逻辑
        [self handleLogicWithJumpToAIPlan:item];
    } failure:^{
        
    }];
}

/// AI计划入口根据当前用户AI计划制定情况处理逻辑
- (void)handleLogicWithJumpToAIPlan:(MRKPlanOverViewModel *)item{
    /// 进入计划详情
    if (item.currentPlan) {
        [self jumpToAIPlanDetailPage:item.currentPlan.planId version:item.currentPlan.version];
        return;
    }
    
    /// 判断计划次数逻辑
    NSString *aiPlanEntranceUrl = MRKAppAIH5LinkCombine(MRKAIPlanEntrance);
    if ([[UIViewController currentViewController] isKindOfClass:[WebViewViewController class]]) {
        WebViewViewController *web = (WebViewViewController *)[UIViewController currentViewController];
        /// 如果目前已经在h5计划入口页面
        if ([web.htmlURL isEqualToString:aiPlanEntranceUrl]){ /// 继续往下走
            NSLog(@"判断计划次数逻辑");
            [self judgeHaveRedeSignPlanCountLogic:item showConfirmAlert:NO];
            return;
        }
    }
    
    /// 未制定过计划进入h5页面 计划入口
    NSLog(@"未制定过计划进入h5页面 计划入口");
    WebViewViewController *vc = [[WebViewViewController alloc] init];
    vc.controlMediaPlayback = YES;
    vc.isHiddenNav = YES;
    vc.htmlURL = aiPlanEntranceUrl;
    vc.hidesBottomBarWhenPushed = YES;
    [[UIViewController currentViewController].navigationController pushViewController:vc animated:YES];
}


// MARK: ———————————— 调整今天的运动项 ————————————
/// 调整今天的运动项
- (void)changeAIPlanTodayContent:(NSString *)planId currentDay:(NSString *)currentDay{
    @weakify(self);
    [self requestPlanOverview:^(MRKPlanOverViewModel *item) {
        @strongify(self);
        [self changeAIPlanTodayLogic:item planId:planId currentDay: currentDay];/// 判断今天的运动调整次数
    } failure:^{
        
    }];
}

/// 今天的运动调整逻辑
- (void)changeAIPlanTodayLogic:(MRKPlanOverViewModel *)item planId:(NSString *)planId currentDay:(NSString *)currentDay{
    if (item.currentPlan == nil || item.currentPlan.remainAdjustCount == 0) {
        [MBProgressHUD showMessage:@"今天的运动调整次数已用完" toView:nil];
        return;
    }
    /// 今日调整运动项弹窗
    FlutterAlertViewController *vc = [FlutterManager adjustPlanTodayAlert:planId currentDay:currentDay];
    vc.modalPresentationStyle = UIModalPresentationOverFullScreen;
    vc.modalTransitionStyle = UIModalTransitionStyleCrossDissolve;
    [[UIViewController currentViewController] presentViewController:vc animated:NO completion:nil];
}

// MARK: ———————————— 调整计划 ————————————
/// 调整计划弹窗
- (void)changeAIPlan:(NSString *)planId currentDay:(NSString *)currentDay{
    FlutterAlertViewController *vc = [FlutterManager adjustPlanSheet:planId currentDay:currentDay];
    vc.modalPresentationStyle = UIModalPresentationOverFullScreen;
    vc.modalTransitionStyle = UIModalTransitionStyleCrossDissolve;
    [[UIViewController currentViewController] presentViewController:vc animated:NO completion:nil];
}

// MARK: ———————————— 点击重新制定计划 二次确认弹窗 ————————————
/// 点击重新制定计划 二次确认弹窗
- (void)regenerateAIPlanAndConfirm{
    @weakify(self);
    [self requestPlanOverview:^(MRKPlanOverViewModel *item) {
        @strongify(self);
        [self judgeHaveRedeSignPlanCountLogic:item showConfirmAlert:YES];
    } failure:^{}];
}

// MARK: ———————————— 点击重新制定 ————————————
/// 点击重新制定
- (void)regenerateAIPlan{
    @weakify(self);
    [self requestPlanOverview:^(MRKPlanOverViewModel *item) {
        @strongify(self);
        [self judgeHaveRedeSignPlanCountLogic:item showConfirmAlert:NO];
    } failure:^{}];
}

/// 进入更换课程/更换动作弹窗
- (void)jumpToAIPlanAdjustPage:(int)type{
    [MBProgressHUD showLodingWithMessage:@"" view:nil];
    /// 权益次数判断
    @weakify(self);
    [[MRKAIPlanLogic shared] requestAIBenefit:^(MRKBenefitModel * it) {
        [MBProgressHUD hideHUD];
        @strongify(self);
        [self handleAIPlanAdjustAIBenefitLogic:type item:it];
    } failure:^{
        [MBProgressHUD hideHUD];
    }];
}

/// 处理文本识别权益逻辑
- (void)handleAIPlanAdjustAIBenefitLogic:(int)type item:(MRKBenefitModel *)item{
    NSString *needCount = type == 1 ? [MRKAIPlanLogic shared].config.voiceCourseCount?:@"0" : [MRKAIPlanLogic shared].config.voiceMotionCount?:@"0";
    /// 需要次数大于0，剩余次数小于需要次数，此时提示vip
    if (needCount.intValue > 0 && (item.remainCount.intValue != -1 && item.remainCount.intValue < needCount.intValue)) {
        [[MRKAIPlanLogic shared] showVipAlert:TraceVipOpenSourceTypeAIPlan];
        return;
    }
    
    /// 跳转到文本识别的flutter页面
    [self jumpAIPlanAdjustFlutter:type];
}

/// 跳转到文本识别flutter页面
- (void)jumpAIPlanAdjustFlutter:(int)type {
    UIViewController *vc = [UIViewController currentViewController];
    if ([vc isKindOfClass:[MRKFlutterAIPlanAdjustAlertController class]]) {
        @weakify(self);
        [vc dismissViewControllerAnimated:NO completion:^{
            @strongify(self);
            [self jumpAIPlanAdjustFlutterPage: type];
        }];
    } else {
        [self jumpAIPlanAdjustFlutterPage: type];
    }
}

- (void)jumpAIPlanAdjustFlutterPage:(int)type {
    MRKFlutterFoodAddController *vc = [[MRKFlutterFoodAddController alloc] init:FoodRecordModeText];
    vc.type = type;
    vc.modalPresentationStyle = UIModalPresentationOverFullScreen;
    vc.modalTransitionStyle = UIModalTransitionStyleCrossDissolve;
    [[UIViewController currentViewController] presentViewController:vc animated:NO completion:nil];
}


- (void)judgeHaveRedeSignPlanCountLogic:(MRKPlanOverViewModel *)item showConfirmAlert:(BOOL)isShow {
    if (!isShow) {
        [self judgeOldPlanCountLogic:item];
        return;
    }
    
    // 未开始计划或计划为空
    if (item.currentPlan == nil || item.currentPlan.status == 1) {
        @weakify(self);
        ReportMrkLogParms(1, @"重新制定计划次数提醒", @"page_ai_plan_regenerate_count_alert", @"", nil, 0, @{@"source": [self getTraceSource]});
        [self showGeneralAlertWithTitle:@"请确认是否重新制定计划？"
                                message:@""
                      cancelButtonTitle:@"暂不生成"
                     confirmButtonTitle:@"确定重新制定"
                                 cancel:^{ }
                                confirm:^{
            @strongify(self);
            [self judgeOldPlanCountLogic:item];// 有次数再点击让用户确认
            ReportMrkLogParms(2, @"重新制定计划次数提醒", @"page_ai_plan_regenerate_count_alert", @"btn_regenerate_count_in_popup", nil, 0, @{@"source": [self getTraceSource]});
        }];
    } else {
        @weakify(self);
        ReportMrkLogParms(1, @"重新制定计划,原计划结束提醒", @"page_ai_plan_regenerate_old_alert", @"", nil, 0, @{@"source": [self getTraceSource]});
        NSMutableAttributedString *str = [[NSMutableAttributedString alloc] initWithString:@"重新制定"];
        [self showGeneralAlertWithTitle:@"重新制定计划后，原计划将自动结束"
                                message:@""
                      cancelButtonTitle:@"暂不生成"
           confirmButtonAttributedTitle:str
                                 cancel:^{ }
                                confirm:^{
            @strongify(self);
            [self judgeOldPlanCountLogic:item];// 有次数再点击让用户确认
            ReportMrkLogParms(2, @"重新制定计划,原计划结束提醒", @"page_ai_plan_regenerate_old_alert", @"btn_regenerate_old_in_popup", nil, 0, @{@"source": [self getTraceSource]});
        }];
    }
}

- (void)showVipAlert:(int)source{
    [self showVipAlert:source normalPageId:@"page_ai_plan_vip_alert" vipPageId:@"page_ai_plan_count_alert"];
}

- (void)showVipAlert:(int)source normalPageId:(NSString *)normalPageId vipPageId:(NSString *)vipPageId{
    /// 判断少于两次
    if (!UserInfo.isMember){
        /// 不是会员提示开通会员
        ReportMrkLogParms(1, @"开通会员享受更多AI次数", normalPageId, @"", nil, 0, @{@"source": [self getTraceSource]});
        @weakify(self);
        [self showGeneralAlertWithTitle:@"开通会员享受更多AI次数"
                                message:@"今日AI次数消耗不足，开通会员享更多次数。"
                      cancelButtonTitle:@"暂不开通"
                     confirmButtonTitle:@"开通会员"
                                 cancel:^{ }
                                confirm:^{
            @strongify(self);
            [DYFStoreManager shared].appPurchaseSource = source;
            [[RouteManager sharedInstance] skipVIP];
            ReportMrkLogParms(2, @"开通会员享受更多AI次数", normalPageId, @"btn_open_vip_in_popup", nil, 0, @{@"source": [self getTraceSource]});
        }];
    } else {
        ///会员的也到上限
        ReportMrkLogParms(1, @"今日AI次数消耗不足，明日再来~", vipPageId, @"", nil, 0, @{@"source": [self getTraceSource]});
        @weakify(self);
        [self showGeneralAlertWithTitle:@"Mia提醒你"
                                message:@"今日AI次数消耗不足，明日再来~"
                      cancelButtonTitle:@""
                     confirmButtonTitle:@"我知道了"
                                 cancel:^{ }
                                confirm:^{
            @strongify(self);
            ReportMrkLogParms(2, @"今日AI次数消耗不足，明日再来~", vipPageId, @"btn_know_in_popup", nil, 0, @{@"source": [self getTraceSource]});
        }];
    }
}

// MARK: ———————————— 判断老计划个数逻辑 ————————————
- (void)judgeOldPlanCountLogic:(MRKPlanOverViewModel *)item{
    if (item.oldPlanCount >= [MRKAIPlanLogic shared].config.oldPlanCount.intValue) { /// 判断老的计划
        ReportMrkLogParms(1, @"计划数已达上限提醒", @"page_ai_plan_old_alert", @"", nil, 0, @{@"source": [self getTraceSource]});
        @weakify(self);
        [self showGeneralAlertWithTitle:@"进行中的计划数已达上限"
                                message:@"继续制定AI计划，最早的计划将自动结束"
                      cancelButtonTitle:@"暂不制定"
                     confirmButtonTitle:@"制定AI计划"
                                 cancel:^{ }
                                confirm:^{
            @strongify(self);
            ReportMrkLogParms(2, @"计划数已达上限提醒", @"page_ai_plan_old_alert", @"btn_continue_in_popup", nil, 0, @{@"source": [self getTraceSource]});
            /// 进入AI计划启动页
            [self jumpToAIPlanStartPage];
        }];
        return;
    }
    
    /// 进入AI计划启动页
    [self jumpToAIPlanStartPage];
}


// MARK: ———————————— 进入AI计划入口页 ————————————
- (void)jumpToAIPlanEntrancePage{
    NSString *aiPlanEntranceUrl = MRKAppAIH5LinkCombine(MRKAIPlanEntrance);
    WebViewViewController *vc = [[WebViewViewController alloc] init];
    vc.controlMediaPlayback = YES;
    vc.isHiddenNav = YES;
    vc.htmlURL = aiPlanEntranceUrl;
    vc.hidesBottomBarWhenPushed = YES;
    [[UIViewController currentViewController].navigationController pushViewController:vc animated:YES];
}

// MARK: ———————————— 进入AI计划启动页 flutter ————————————
- (void)jumpToAIPlanStartPage{
    MRKFlutterAIDietPlanStartController *vc = [[MRKFlutterAIDietPlanStartController alloc] init];
    vc.hidesBottomBarWhenPushed = YES;
    [[UIViewController currentViewController].navigationController pushViewController:vc animated:YES];
}

- (void)jumpToAIPlanResetPage:(NSString *)planId {
    MRKFlutterAIDietPlanQuitController *vc = [[MRKFlutterAIDietPlanQuitController alloc] init];
    vc.planId = planId;
    vc.hidesBottomBarWhenPushed = YES;
    [[UIViewController currentViewController].navigationController pushViewController:vc animated:YES];
}


// MARK: ———————————— 进入AI计划详情页面 flutter ————————————
- (void)jumpToAIPlanDetailPage:(NSString *)planId {
    MRKFlutterAIDietPlanDetailController *vc = [[MRKFlutterAIDietPlanDetailController alloc] init];
    vc.planId = planId;
    vc.hidesBottomBarWhenPushed = YES;
    [[UIViewController currentViewController].navigationController pushViewController:vc animated:YES];
}

- (void)jumpToAIPlanDetailPage:(NSString *)planId version:(NSInteger)version{
    if (version == 2) {
        MRKFlutterAIDietPlanDetailController *vc = [[MRKFlutterAIDietPlanDetailController alloc] init];
        vc.planId = planId;
        vc.hidesBottomBarWhenPushed = YES;
        [[UIViewController currentViewController].navigationController pushViewController:vc animated:YES];
      
    }else{
        FlutterAIPlanDetailViewController *vc = [FlutterManager aiPlanDetailPage:planId];
        vc.hidesBottomBarWhenPushed = YES;
        [[UIViewController currentViewController].navigationController pushViewController:vc animated:YES];
    }
}

// MARK: ———————————— 进入AI计划完成页面 ————————————
- (void)jumpToAIPlanDone:(MRKAIPlanCheckModel *)model{
    MRKAIPlanDoneController *vc = [[MRKAIPlanDoneController alloc] init];
    vc.model = model;
    [[UIViewController currentViewController].navigationController pushViewController:vc animated:YES];
}

// MARK: ———————————— 进入AI计划任务训练 ————————————
- (void)jumpToAIPlanTaskTrain:(MRKDailyScheduleItemModel *)model andTimeReference:(NSString *)timeReference {
    ///饮水不响应交互
    if (model.taskType == MRKTrainingTypeDrink) {
        return;
    }
    
    if (model.lock) {
        [MBProgressHUD showMessage:@"还没到开始时间呢，请按节奏进行训练" toView:nil];
        return;
    }
    
    if (timeReference.intValue == 2) { // 今天
        [self jumpToTaskTrain:model];
        return;
    }
    
    if (timeReference.intValue == 1){ // 昨天
        @weakify(self);
        ReportMrkLogParms(1, @"当日任务已过期提醒", @"page_ai_plan_day_expire_alert", @"", nil, 0, @{@"source": [self getTraceSource]});
        [self showGeneralAlertWithTitle:@"温馨提示"
                                message:@"当日任务已过期，进入训练将不改变状态"
                      cancelButtonTitle:@"暂不进入"
                     confirmButtonTitle:@"进入训练"
                                 cancel:^{ }
                                confirm:^{
            @strongify(self);
            [self jumpToTaskTrain:model];
            ReportMrkLogParms(2, @"当日任务已过期提醒", @"page_ai_plan_day_expire_alert", @"btn_ai_plan_enter_train_alert", nil, 0, @{@"source": [self getTraceSource]});
        }];
        return;
    }
}

- (void)jumpToTaskTrain:(MRKDailyScheduleItemModel *)model{
    if (model.taskType == MRKTrainingTypeCourse) {
        ///课程详情
        [[RouteManager sharedInstance] jumpToPlanCourseDetailWithId:model.targetId];
    } else if (model.taskType == MRKTrainingTypeMotion) {
        ///动作详情
        NSString *targetValue = @"";
        NSInteger trainingTargetType = model.trainingTargetType;
        switch (trainingTargetType) {
            case 1:
                targetValue = model.trainingTargetValue;
                break;
            case 2:
                targetValue = model.trainingTargetDuration;
                break;
            default:
                break;
        }
        [[RouteManager sharedInstance] jumpToMotionDetailWithId:model.targetId trainTarget:targetValue];
    }
}






// MARK: ———————————— 请求接口获取当前用户AI计划制定情况 ————————————
/// 请求接口获取当前用户AI计划制定情况
- (void)requestPlanOverview:(void(^)(MRKPlanOverViewModel *))success failure:(void(^)(void))failure {
    [MBProgressHUD showLodingWithMessage:@"" view:nil];
    [MRKAIPlanAPIClient aiplanOverviewSuccess:^(id data) {
        [MBProgressHUD hideHUD];
        MRKPlanOverViewModel *item = (MRKPlanOverViewModel *)data;
        if (item.isProcess == 1) {
            [MBProgressHUD showMessage:@"计划正在生成中" toView:nil];
            failure();
            return;
        }
        success(item);
    } failure:^(id data) {
        [MBProgressHUD hideHUD];
        failure();
    }];
}

// MARK: ———————————— 请求接口获取当前用户权益列表 ————————————
/// 请求接口获取当前用户AI计划制定情况
- (void)requestAIBenefit:(void(^)(MRKBenefitModel *))success failure:(void(^)(void))failure {
    [MRKBaseRequest mrkGetRequestUrl:@"/user/user-benefit/list"
                             andParm:nil
            completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"request.responseObject ==== %@",request.responseObject);
        id data = [request.responseObject valueForKeyPath:@"data"];
        NSArray *dataArray = [NSArray modelArrayWithClass:[MRKBenefitModel class] json:data];
        MRKBenefitModel *current;
        for (MRKBenefitModel *it in dataArray) {
            if ([it.benefitCode isEqualToString:@"MERIT_AI"]) {
                current = it;
                break;
            }
        }
        success(current);
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        failure();
    }];
}




- (BOOL)isAfterTenPM {
    NSDate *currentDate = [NSDate date];
    NSCalendar *calendar = [NSCalendar currentCalendar];
    NSDateComponents *comps = [calendar components:(NSCalendarUnitYear|NSCalendarUnitMonth|NSCalendarUnitDay)
                                          fromDate:currentDate];
    comps.hour = 22;
    comps.minute = 0;
    NSDate *tenPM = [calendar dateFromComponents:comps];
    return [currentDate compare:tenPM] == NSOrderedDescending;
}

- (void)flutterAIDietOperation:(NSInteger)operatePlanType {
    void(^selectOpertionBlock)(NSInteger index) = ^(NSInteger index){
        MRKFlutterAIDietOperationController *vc = [[MRKFlutterAIDietOperationController alloc] init];
        vc.operatePlanType = @(operatePlanType);
        vc.startMode = @(index);
        vc.hidesBottomBarWhenPushed = YES;
        vc.modalPresentationStyle = UIModalPresentationOverFullScreen;
        vc.modalTransitionStyle = UIModalTransitionStyleCrossDissolve;
        UIViewController *controller = [UIViewController currentViewController];
        [controller presentViewController:vc animated:NO completion:^{
            
        }];
    };
    
    ///判断是否当前时间是否晚于22:00
    if ([self isAfterTenPM]){
        [MrkAlertManager showAlertTitle:@"温馨提示"
                                message:@"今天比较晚了，为保证你的计划完成度，Mia建议明天再开启计划哦～"
                                 cancel:@"立即开始"
                                 ensure:@"从明天开始"
                            handleIndex:^(NSInteger index) {
            selectOpertionBlock(index+1);
        }];
        return;
    }
    
    selectOpertionBlock(1);
}

/// 开启本周并动态延长
- (void)startAndExtendCurrentWeek {
    @weakify(self);
    [self.updateWeightManager checkWeightCompleteBlock:^{
        @strongify(self)
        [self flutterAIDietOperation:1];
    }];
}

/// AI动态开启本周计划
- (void)dynamicStartCurrentWeek {
    @weakify(self);
    [self.updateWeightManager checkWeightCompleteBlock:^{
        @strongify(self)
        [self flutterAIDietOperation:0];
    }];
}




/// 销假
- (void)cancelLeave{
    @weakify(self);
    [self requestPlanOverview:^(MRKPlanOverViewModel *item) {
        @strongify(self);
        MRKCurrentAIPlanModel *currentPlan = item.currentPlan;
        ///3-请假中；
        if (currentPlan.status == 3) {
            MRKPlanLeaveInfoModel *leaveInfo = currentPlan.leaveInfo;
            NSString *leaveInfoStr = [NSString stringWithFormat:@"请假%ld天：%@ - %@", leaveInfo.leaveDays, leaveInfo.startDate, leaveInfo.endDate];
            [MrkAlertManager showAlertTitle:@"请假中，是否销假？"
                                    message:leaveInfoStr
                                     cancel:@"暂不销假"
                                     ensure:@"确定销假"
                                handleIndex:^(NSInteger index) {
                if (index == 1) {
                    [self cancelLeaveWithPlanId:currentPlan.planId version:currentPlan.version];
                }
            }];
        }
    } failure:^{
        
    }];
}

- (void)cancelLeaveWithPlanId:(NSString *)planId version:(NSInteger)version {
    [MRKAIPlanAPIClient aiplanCancelLeaveWithId:planId
                                        version:version
                                        Success:^(id data) {
        if ([data boolValue]) {
            [MBProgressHUD showMessage:@"销假成功"];
            ///刷新首页
            [self refreshMainPage];
        } else {
            [MBProgressHUD showMessage:@"销假失败"];
        }
    } failure:^(id data) {
        [MBProgressHUD showMessage:@"销假失败"];
    }];
}

/// 去记录
- (void)recordDiet:(BOOL)fromMainPage{
    NSString *date = [MRKTimeManager getNowTimeDate];
    [self recordDiet:date fromMainPage:fromMainPage];
}

/// 去记录 [默认date 结构"yyyy-MM-dd"]
- (void)recordDiet:(NSString *)date fromMainPage:(BOOL)fromMainPage{
    MRKDailyScheduleDietAlert *alert = [[MRKDailyScheduleDietAlert alloc] initWithAnimationStyle:MRKActionAlertViewTransitionStyleSlideFromBottom];
    alert.date = date;
    alert.opaquess = 0.6;
    alert.fromMainPage = fromMainPage;
    alert.refreshBlock = ^(NSInteger type) {
        
    };
    [[MRKPopupManager sharedInstance] showAlertView:alert level:MRKPopupViewLevelHeight callback:nil];
}

/// 去运动
- (void)goToTaskTrain{
    @weakify(self);
    [self requestPlanOverview:^(MRKPlanOverViewModel *item) {
        @strongify(self);
        MRKPlanCurrentTrainingDayModel *trainingDay = item.currentPlan.currentTrainingDay;
        NSArray *unfinishTaskList = trainingDay.unfinishTaskList;
        if (!unfinishTaskList || unfinishTaskList.count == 0) {
            [MBProgressHUD showMessage:@"暂无未完成运动任务" toView:nil];
            return;
        }
        
        /// 查找第一个未完成的任务（taskStatus == 0）的任务
        id firstUnfinishedTask = nil;
        for (id task in unfinishTaskList) {
            if ([task respondsToSelector:@selector(taskStatus)] && [task taskStatus] == 0) {
                firstUnfinishedTask = task;
                break;
            }
        }
        if (firstUnfinishedTask) {
            /// 跳转到该任务详情页
            [self jumpToTaskTrain:firstUnfinishedTask];
        } else {
            /// 所有任务都已完成，跳转到全部任务页面或提示
            NSLog(@"所有运动任务已完成，跳转到全部任务页");
            [self jumpToAIPlan];
        }
    } failure:^{
        
    }];
}

/// 查看报告
- (void)dietAIPlanReport:(NSString *)planId{
    ///MARK: 拉个最新体重弹窗后再跳报告
    @weakify(self);
    [self.updateWeightManager checkWeightCompleteBlock:^{
        @strongify(self)
        [self jumpToAIReport:YES planId:planId version:2];
    }];
}


/// 查看报告
- (void)AIPlanReport{
    @weakify(self);
    [self requestPlanOverview:^(MRKPlanOverViewModel *item) {
        @strongify(self);
        ///4-已完成；5-中途结束；6-未完成；
        if (item.currentPlan.status == 4 ||
            item.currentPlan.status == 5 ||
            item.currentPlan.status == 6 ) {
            
            if (item.currentPlan.version == 1){
                [self jumpToAIReport:YES planId:item.currentPlan.planId version:1];
            } else {
                [self dietAIPlanReport:item.currentPlan.planId];
            }
        }
    } failure:^{}];
}


///日程页课程/动作换一换
- (void)planTaskExchange:(MRKDailyScheduleItemModel *)model{
    MRKFlutterAIPlanAdjustAlertController *vc = [[MRKFlutterAIPlanAdjustAlertController alloc] init];
    vc.dict = @{
        @"type":model.taskType == MRKTrainingTypeCourse ? @1 : @2,
        @"taskId":model.cid ?:@"",
        @"targetId":model.targetId ?:@"",
        @"kcal":model.kcal ?:@"0",
        @"grade":model.grade ?:@"0",
        @"duration":model.duration ?:@"0"
    };
    
    vc.modalPresentationStyle = UIModalPresentationOverFullScreen;
    vc.modalTransitionStyle = UIModalTransitionStyleCrossDissolve;
    [[UIViewController currentViewController] presentViewController:vc animated:NO completion:nil];
}


///日程页动作换时长/次水
- (void)planMotionTaskTrainingTargetReplace:(MRKDailyScheduleItemModel *)model {
    if (model.taskType != 9 || model.lock) return;
    
    switch (model.trainingTargetType) {
        case 1: ///数量
        {
            [MRKDatePickerManager countPickerViewTitle:model.title
                                             withIndex:model.trainingTargetValue.doubleValue
                                           resultBlock:^(NSString * _Nullable value) {
                if ([value isNotBlank]) {
                    [self updateMotionTaskTargetValue:@{
                        @"taskId": model.cid?:@"",
                        @"trainingTargetValue": @(value.intValue)
                    }];
                }
            } cancelBlock:^{
                
            }];
            
        }   break;
        case 2: ///时间
        {
            NSString *time = [MRKTimeManager getMMSSFromSS:model.trainingTargetDuration];
            [MRKDatePickerManager dateMMSSPickerViewWithTime:time
                                                 resultBlock:^(NSDate * _Nullable selectDate, NSString * _Nullable selectValue) {
                if ([selectValue isNotBlank]) {
                    NSInteger seconds = [MRKTimeManager secondsFromTimeString:selectValue];
                    [self updateMotionTaskTargetValue:@{
                        @"taskId": model.cid?:@"",
                        @"trainingTargetValue": @(seconds)
                    }];
                }
            } cancelBlock:^{
                
            }];
        }   break;
        default:
            break;
    }
}

- (void)updateMotionTaskTargetValue:(NSDictionary *)parms {
    [MRKAIPlanAPIClient aiplanUpdateMotionTaskTargetValueWithParms:parms
                                                           Success:^(id data) {
        [self refreshDailySchedulePage];
    } failure:^(id data) {
        
    }];
}

///
- (void)refreshDailySchedulePage{
    [[NSNotificationCenter defaultCenter] postNotificationName:@"kAIPlanAdjustSuccessNotification" object:nil];
}

- (void)refreshMainPage{
    [[NSNotificationCenter defaultCenter] postNotificationName:@"kRefreshMainPageNotification" object:nil];
}



// MARK: ———————————— 埋点处理 ————————————
- (NSString *)getTraceSource{
    if ([[UIViewController currentViewController] isKindOfClass:[MRKMainPageController class]]) {
        return @"home";
    } else if ([[UIViewController currentViewController] isKindOfClass:[MRKDailyScheduleController class]]) {
        return @"schedule";
    } else if ([[UIViewController currentViewController] isKindOfClass:[WebViewViewController class]]) { // 首页
        WebViewViewController *web = (WebViewViewController *)[UIViewController currentViewController];
        NSString *aiChatUrl = MRKAppAIH5LinkCombine(MRKAIChat);
        NSString *aiPlanEntranceUrl = MRKAppAIH5LinkCombine(MRKAIPlanEntrance);
        NSString *aiReportUrl = MRKAppAIH5LinkCombine(MRKAIPlanReport);
        NSString *aiAnalysisUrl = MRKAppAIH5LinkCombine(MRKAISportDietAnalysis);
        if ([web.htmlURL isEqualToString:aiChatUrl]){
            return @"ai_chat";
        } else if ([web.htmlURL isEqualToString:aiPlanEntranceUrl]){
            return @"ai_plan_entrance";
        } else if ([web.htmlURL isEqualToString:aiReportUrl]){
            return @"ai_plan_report";
        } else if ([web.htmlURL isEqualToString:aiAnalysisUrl]){
            return @"ai_analysis";
        } else {
            return @"train_report";
        }
    } else if ([[UIViewController currentViewController] isKindOfClass:[FlutterAIPlanDetailViewController class]]) {
        return @"ai_plan_list";
    } else if ([[UIViewController currentViewController] isKindOfClass:[FlutterAIPlanStartViewController class]]) {
        return @"ai_plan_start";
    } else if ([[UIViewController currentViewController] isKindOfClass:[MRKDailyScheduleController class]]) {
        return @"schedule";
    } else if ([[UIViewController currentViewController] isKindOfClass:[MRKFlutterFoodRecordListController class]]) {
        return @"ai_food_record";
    }
    return @"else";
}

// MARK: ———————————— 弹窗统一样式 ————————————
- (void)showGeneralAlertWithTitle:(NSString *)title
                          message:(NSString *)message
                cancelButtonTitle:(NSString *)cancelButtonTitle
               confirmButtonTitle:(NSString *)confirmButtonTitle
                           cancel:(dispatch_block_t)cancelHandler
                          confirm:(dispatch_block_t)executableHandler {
    NSMutableAttributedString *titleStr = [[NSMutableAttributedString alloc] initWithString:title];
    titleStr.color = UIColorHex(#363A44);
    titleStr.font = [UIFont fontWithName:fontNameMeDium size:18];
    titleStr.alignment = NSTextAlignmentCenter;
    NSMutableAttributedString *messageStr = [[NSMutableAttributedString alloc] initWithString:message];
    messageStr.color = UIColorHex(#363A44) ;
    messageStr.font = [UIFont fontWithName:fontNamePing size:13];
    messageStr.alignment = NSTextAlignmentCenter;
    MrkGeneralAlertView *alert = [MrkGeneralAlertView build];
    alert.messageObject = MakeAttributAlertViewTitleMessageObject(titleStr, messageStr);
    alert.maskViewTapHidden = YES;
    if (cancelButtonTitle.length > 0) {
        [alert addBtnWithTitle:cancelButtonTitle btnStyle:kAlertViewButtonCancelStyle clicked:^(MrkGeneralAlertView *alertView) {
            [alertView hide];
            if (cancelHandler) {
                cancelHandler();
            }
        }];
    }
    if (confirmButtonTitle.length > 0) {
        [alert addBtnWithTitle:confirmButtonTitle btnStyle:kAlertViewButtonEnsureStyle clicked:^(MrkGeneralAlertView *alertView) {
            [alertView hide];
            if (executableHandler) {
                executableHandler();
            }
        }];
    }
    [alert showInKeyWindow];
}

- (void)showGeneralAlertWithTitle:(NSString *)title
                          message:(NSString *)message
                cancelButtonTitle:(NSString *)cancelButtonTitle
     confirmButtonAttributedTitle:(NSAttributedString *)confirmButtonTitle
                           cancel:(dispatch_block_t)cancelHandler
                          confirm:(dispatch_block_t)executableHandler {
    NSMutableAttributedString *titleStr = [[NSMutableAttributedString alloc] initWithString:title];
    titleStr.color = UIColorHex(#363A44);
    titleStr.font = [UIFont fontWithName:fontNameMeDium size:18];
    titleStr.alignment = NSTextAlignmentCenter;
    NSMutableAttributedString *messageStr = [[NSMutableAttributedString alloc] initWithString:message];
    messageStr.color = UIColorHex(#363A44) ;
    messageStr.font = [UIFont fontWithName:fontNamePing size:13];
    messageStr.alignment = NSTextAlignmentCenter;
    MrkGeneralAlertView *alert = [MrkGeneralAlertView build];
    alert.messageObject = MakeAttributAlertViewTitleMessageObject(titleStr, messageStr);
    alert.maskViewTapHidden = YES;
    if (cancelButtonTitle.length > 0) {
        [alert addBtnWithTitle:cancelButtonTitle btnStyle:kAlertViewButtonCancelStyle clicked:^(MrkGeneralAlertView *alertView) {
            [alertView hide];
            if (cancelHandler) {
                cancelHandler();
            }
        }];
    }
    if (confirmButtonTitle.length > 0) {
        [alert addBtnWithAttributedString:confirmButtonTitle btnStyle:kAlertViewButtonEnsureStyle clicked:^(MrkGeneralAlertView *alertView) {
            [alertView hide];
            if (executableHandler) {
                executableHandler();
            }
        }];
    }
    [alert showInKeyWindow];
}

@end
