//
//  MRKPlanOverViewModel.h
//  Student_IOS
//
//  Created by <PERSON><PERSON> on 2025/6/26.
//

#import <Foundation/Foundation.h>
#import "MRKDailyScheduleModel.h"

NS_ASSUME_NONNULL_BEGIN
@class MRKCurrentAIPlanModel;
@class MRKPlanLeaveInfoModel;
@class MRKPlanCurrentPeriodModel;
@class MRKPlanCurrentTrainingDayModel;
@class MRKPlanCurrentPhaseModel;
@class MRKPlanUnfinishTaskListModel;

/// 计划总览主模型
@interface MRKPlanOverViewModel : NSObject
@property (nonatomic, assign) BOOL isProcess;            ///< 是否在AI生成中
@property (nonatomic, assign) NSInteger oldPlanCount;    ///< 历史计划数量
@property (nonatomic, assign) NSInteger taskModePlanCount;   ///< 5.5.3新增，任务计划数量
@property (nonatomic, strong, nullable) MRKCurrentAIPlanModel *currentPlan; ///< 当前计划详情
@end



/// 当前计划模型
@interface MRKCurrentAIPlanModel : NSObject
@property (nonatomic, copy) NSString *planId;                ///< 计划ID
@property (nonatomic, copy) NSString *title;                 ///< 计划名称
@property (nonatomic, assign) NSInteger type;                ///< 计划类型 1-减脂减重 100-其他
@property (nonatomic, assign) NSInteger version;             ///< 1-AI计划1.0， 2-AI计划2.0
@property (nonatomic, assign) NSInteger completeMode;        ///< 5.5.3新增，每日完成模式：1-任务制，2-热量制
@property (nonatomic, copy) NSString *startDate;             ///< 计划开始时间
@property (nonatomic, copy) NSString *endDate;               ///< 计划结束时间
@property (nonatomic, assign) NSInteger consecutiveMissedDays; ///< 连续缺失天数
@property (nonatomic, assign) BOOL isStart;                  ///< 是否已开始
@property (nonatomic, assign) NSInteger status;              ///< 计划状态 训练状态：1-未开始；2-进行中；3-请假中；4-已完成；5-中途结束；6-未完成；99-废弃
@property (nonatomic, assign) NSInteger days;                ///< 总天数
@property (nonatomic, assign) NSInteger progressDays;        ///< 当前进度天数
@property (nonatomic, assign) NSInteger remainAdjustCount;   ///<当日剩余可调整训练次数
@property (nonatomic, strong, nullable) MRKPlanLeaveInfoModel *leaveInfo; ///< 请假信息（请假中才有）
@property (nonatomic, strong, nullable) MRKPlanCurrentPeriodModel *currentPeriod; ///< 当前周期信息
@property (nonatomic, strong, nullable) MRKPlanCurrentTrainingDayModel *currentTrainingDay; ///< 当前训练日信息
@property (nonatomic, strong, nullable) MRKPlanCurrentPhaseModel *currentPhase; ///< 当前训练阶段
@end



/// 请假信息模型
@interface MRKPlanLeaveInfoModel : NSObject
@property (nonatomic, assign) NSInteger leaveDays;   ///< 请假天数
@property (nonatomic, copy) NSString *startDate;     ///< 请假开始时间
@property (nonatomic, copy) NSString *endDate;       ///< 请假结束时间
@end



/// 当前周期模型
@interface MRKPlanCurrentPeriodModel : NSObject
@property (nonatomic, assign) NSInteger seq;             ///< 该周期的顺序号
@property (nonatomic, copy) NSString *startDate;         ///< 周期开始时间
@property (nonatomic, copy) NSString *endDate;           ///< 周期结束时间
@property (nonatomic, copy) NSString *startWeight;       ///< 该周期的初始体重（kg）
@property (nonatomic, copy) NSString *endWeight;         ///< 该周期的结束体重（kg）
@property (nonatomic, copy) NSString *weightLoss;        ///< 该周期的体重减少量（kg）
@property (nonatomic, copy) NSString *targetWeightLoss;  ///< 计划减重（kg）
@property (nonatomic, assign) NSInteger remainLeaveCount; ///< 该周期的剩余请假次数
@property (nonatomic, assign) NSInteger remainRestCount; ///< 该周期的剩余休息次数
@property (nonatomic, assign) BOOL isTraining;           ///< 是否锻炼过 【课程，动作是否训练过】
@property (nonatomic, assign) BOOL isExecuted;           ///< 是否执行过 【课程，动作是否训练过， 饮食是否添加过】
@property (nonatomic, assign) NSInteger status;          ///< 状态 1-进行中 2-已结束
@property (nonatomic, assign) NSInteger unStartDays;     ///< 几天未开启本周计划 []
@end



/// 当前训练日模型
@interface MRKPlanCurrentTrainingDayModel : NSObject
@property (nonatomic, assign) NSInteger seq;                    ///< 训练日序号 ()_
@property (nonatomic, assign) NSInteger status;                 ///< 训练状态：1-未开始；2-未完成；3-已完成；
@property (nonatomic, copy) NSString *title;                    ///< 计划日标题
@property (nonatomic, assign) NSInteger trainingDayType;        ///< 训练日类型(1:训练日,2:休息日)
@property (nonatomic, assign) NSInteger trainingTaskCount;      ///< 训练任务总数
@property (nonatomic, assign) NSInteger finishTrainingTaskCount; ///< 完成的训练任务数
@property (nonatomic, assign) NSInteger dietTaskCount;          ///< 饮食任务总数
@property (nonatomic, assign) NSInteger finishedDietTaskCount;  ///< 完成的饮食任务数
///<
@property (nonatomic, copy, nullable) NSString *targetSportCalories;      ///< 5.5.3新增，当日运动目标热量
@property (nonatomic, copy, nullable) NSString *sportCalories;            ///< 5.5.3新增，当日运动实际热量
@property (nonatomic, strong, nullable) NSArray <MRKDailyScheduleItemModel *> *unfinishTaskList; ///< 未完成的任务列表
@end



/// 当前阶段模型
@interface MRKPlanCurrentPhaseModel : NSObject
@property (nonatomic, copy) NSString *phaseId;          ///< 阶段ID
@property (nonatomic, copy) NSString *title;            ///< 阶段名称
@property (nonatomic, assign) NSInteger seq;            ///< 阶段序号
@property (nonatomic, copy) NSString *startDate;        ///< 阶段开始时间
@property (nonatomic, copy) NSString *endDate;          ///< 阶段结束时间
@end

NS_ASSUME_NONNULL_END
