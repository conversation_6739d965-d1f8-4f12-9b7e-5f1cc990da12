//
//  MRKAlertController.m
//  Student_IOS
//
//  Created by merit on 2022/10/11.
//

#import "MRKAlertController.h"
#import "MRKPopupManager.h"
#import "MRKHomeAlertManager.h"


@interface MRKAlertController ()
@property (nonatomic, strong) UIImageView *alertView;
@property (nonatomic, strong) UIButton *cancelBtn;
@end

@implementation MRKAlertController

+ (MRKAlertController *)controllerWithModel:(MRKAlertStatusModel *)model {
    MRKAlertController *vc = [[MRKAlertController alloc] init];
    vc.tracePageId = model.tracePageId;
    vc.model = model;
    return vc;
}

+ (MRKAlertController *)controllerWithImage:(UIImage *)image {
    MRKAlertController *vc = [[MRKAlertController alloc] init];
    vc.tracePageId = @"";
    vc.image = image;
    return vc;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    self.view.frame = [UIScreen mainScreen].bounds;
    self.view.backgroundColor = [UIColor colorWithWhite:0 alpha:0.5];
    
    ///
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(alertClick:)];
    [self.alertView addGestureRecognizer:tap];
    [self.view addSubview:self.alertView];
    if (self.image != nil) {
        ///定死宽高比 *885/1017
        [self.alertView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(self.view.mas_centerX);
            make.centerY.equalTo(self.view.mas_centerY).offset(-WKDHPX(32));
            make.width.equalTo(@(WKDHPX(295)));
            make.height.equalTo(@(WKDHPX(288)));
        }];
        self.alertView.image = self.image;
    }else{
        ///定死宽高比 *885/1017
        [self.alertView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(self.view.mas_centerX);
            make.centerY.equalTo(self.view.mas_centerY).offset(-WKDHPX(32));
            make.width.equalTo(@(WKDHPX(295)));
            make.height.equalTo(@(WKDHPX(340)));
        }];
        [self.alertView setImageWithURL:[NSURL URLWithString:self.model.imageUrl]
                            placeholder:nil
                                options:YYWebImageOptionProgressiveBlur|YYWebImageOptionSetImageWithFadeAnimation
                             completion:nil];
    }
    
    [self.view addSubview:self.cancelBtn];
    [self.cancelBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.alertView.mas_bottom).offset(WKDHPX(24));
        make.centerX.equalTo(self.alertView.mas_centerX);
        make.width.height.equalTo(@(WKDHPX(36)));
    }];
}

- (void)viewDidLayoutSubviews{
    [super viewDidLayoutSubviews];

}

- (UIImageView *)alertView{
    if (!_alertView) {
        _alertView = [[UIImageView alloc] init];
        _alertView.contentMode = UIViewContentModeScaleAspectFill;
        _alertView.clipsToBounds = YES;
        _alertView.userInteractionEnabled = YES;
    }
    return _alertView;
}

- (void)alertClick:(UIGestureRecognizer*)tap{
    self.alertView.traceEventId = @"btn_popup_click";

    if (self.image != nil) {
        if (self.alertRouterHandle) {
            self.alertRouterHandle();
        }
    }
    
    if (self.model != nil) {
        AdvertModel *model = [AdvertModel modelWithJSON:self.model.alertData];
        [[NSNotificationCenter defaultCenter] postNotificationName:AdvertSkipToPageNotification object:@{@"model":model, @"position":@(1)}];
    }

    [self dismissAlert];
}

- (void)dismissAlert{
    self.cancelBtn.traceEventId = @"btn_popup_close";
    [MRKHomeAlertManager shareManager].beingProcessed = NO;
    [UIView animateWithDuration:0.3f animations:^{
        self.view.alpha = 0.f;
    } completion:^(BOOL finished) {
        [self.view removeFromSuperview];
        [self removeFromParentViewController];

        [[MRKPopupManager sharedInstance] dismissAlertView:self callback:nil];
    }];
}

- (UIButton *)cancelBtn{
    if (!_cancelBtn) {
        UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
        [btn setImage:[UIImage imageNamed:@"icon_alert_close"] forState:UIControlStateNormal];
        [btn addTarget:self action:@selector(dismissAlertAction) forControlEvents:UIControlEventTouchUpInside];
        _cancelBtn = btn;
    }
    return _cancelBtn;
}

- (void)dismissAlertAction{
    if (self.alertDismissHandle) {
        self.alertDismissHandle();
    }
  
    [self dismissAlert];
}

/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/

@end
