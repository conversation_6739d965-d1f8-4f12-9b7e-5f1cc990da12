//
//  MRKAlertController.m
//  Student_IOS
//
//  Created by merit on 2022/10/11.
//

#import "MRKAlertController.h"
#import "MRKPopupManager.h"
#import "MRKHomeAlertManager.h"


@interface MRKAlertController ()
@property (nonatomic, strong) MRKPurchaseDiscountView *purchaseDiscountView;
@property (nonatomic, strong) UIImageView *alertView;
@property (nonatomic, strong) UIButton *cancelBtn;

@property (nonatomic, strong) MRKPurchaseDiscountModel *purchaseDiscountModel;
@property (nonatomic, strong) MRKAlertStatusModel *model;
@property (nonatomic, strong) UIImage *image;
@end

@implementation MRKAlertController

+ (MRKAlertController *)controllerWithModel:(MRKAlertStatusModel *)model {
    MRKAlertController *vc = [[MRKAlertController alloc] init];
    vc.tracePageId = model.tracePageId;
    vc.model = model;
    return vc;
}

+ (MRKAlertController *)controllerWithImage:(UIImage *)image {
    MRKAlertController *vc = [[MRKAlertController alloc] init];
    vc.tracePageId = @"";
    vc.image = image;
    return vc;
}

+ (MRKAlertController *)controllerWithPurchaseDiscountModel:(MRKPurchaseDiscountModel *)model{
    MRKAlertController *vc = [[MRKAlertController alloc] init];
    vc.tracePageId = @"";
    vc.purchaseDiscountModel = model;
    return vc;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    self.view.frame = [UIScreen mainScreen].bounds;
    self.view.backgroundColor = [UIColor colorWithWhite:0 alpha:0.5];
    
    
    UIView *targetPointView = nil;
    ///
    switch (self.alertType) {
        case MRKAlertControllerTypeImage: {
            UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(alertClick:)];
            [self.alertView addGestureRecognizer:tap];
            
            ///定死宽高比 *885/1017
            [self.view addSubview:self.alertView];
            [self.alertView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerX.equalTo(self.view.mas_centerX);
                make.centerY.equalTo(self.view.mas_centerY).offset(-WKDHPX(32));
                make.width.equalTo(@(WKDHPX(295)));
                make.height.equalTo(@(WKDHPX(288)));
            }];
            self.alertView.image = self.image;
            
            targetPointView = self.alertView;
        } break;
            
        case MRKAlertControllerTypeImageUrl: {
            UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(alertClick:)];
            [self.alertView addGestureRecognizer:tap];
            
            ///定死宽高比 *885/1017
            [self.view addSubview:self.alertView];
            [self.alertView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerX.equalTo(self.view.mas_centerX);
                make.centerY.equalTo(self.view.mas_centerY).offset(-WKDHPX(32));
                make.width.equalTo(@(WKDHPX(295)));
                make.height.equalTo(@(WKDHPX(340)));
            }];
            [self.alertView setImageWithURL:[NSURL URLWithString:self.model.imageUrl]
                                placeholder:nil
                                    options:YYWebImageOptionProgressiveBlur|YYWebImageOptionSetImageWithFadeAnimation
                                 completion:nil];
            
            targetPointView = self.alertView;
        } break;
            
        case MRKAlertControllerTypeVIP: {
            
            ///定死宽高比 *885/1017
            self.purchaseDiscountView.model = self.purchaseDiscountModel;
            [self.view addSubview:self.purchaseDiscountView];
            [self.purchaseDiscountView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerX.equalTo(self.view.mas_centerX);
                make.centerY.equalTo(self.view.mas_centerY).offset(-WKDHPX(32));
                make.width.equalTo(@(WKDHPX(295)));
                make.height.equalTo(@(WKDHPX(260)));
            }];
            
            targetPointView = self.purchaseDiscountView;
        } break;
        default:
            break;
    }
    
    if (targetPointView) {
        [self.view addSubview:self.cancelBtn];
        [self.cancelBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(targetPointView.mas_bottom).offset(WKDHPX(24));
            make.centerX.equalTo(targetPointView.mas_centerX);
            make.width.height.equalTo(@(WKDHPX(36)));
        }];
    }
}

- (void)viewDidLayoutSubviews{
    [super viewDidLayoutSubviews];
}

- (void)alertClick:(UIGestureRecognizer*)tap{
    self.alertView.traceEventId = @"btn_popup_click";

    if (self.image != nil) {
        if (self.alertRouterHandle) {
            self.alertRouterHandle();
        }
    }
    
    if (self.model != nil) {
        AdvertModel *model = [AdvertModel modelWithJSON:self.model.alertData];
        [[NSNotificationCenter defaultCenter] postNotificationName:AdvertSkipToPageNotification object:@{@"model":model, @"position":@(1)}];
    }

    [self dismissAlert];
}

- (void)purchaseDiscountReceive {
    NSURL *url = [NSURL URLWithString:self.purchaseDiscountModel.buttonUrl];
    [[RouteManager sharedInstance] applicationOpenURL:url];

    if (self.alertDismissHandle) {
        self.alertDismissHandle();
    }
    
    [self dismissAlert];
}

- (void)dismissAlert{
    self.cancelBtn.traceEventId = @"btn_popup_close";
    [MRKHomeAlertManager shareManager].beingProcessed = NO;
    [UIView animateWithDuration:0.3f animations:^{
        self.view.alpha = 0.f;
    } completion:^(BOOL finished) {
        [self.view removeFromSuperview];
        [self removeFromParentViewController];

        [[MRKPopupManager sharedInstance] dismissAlertView:self callback:nil];
    }];
}

- (MRKPurchaseDiscountView *)purchaseDiscountView{
    if (!_purchaseDiscountView) {
        _purchaseDiscountView = [[MRKPurchaseDiscountView alloc] init];
        @weakify(self);
        _purchaseDiscountView.purchaseDiscountReceiveBlock = ^{
            [self_weak_ purchaseDiscountReceive];
        };
    }
    return _purchaseDiscountView;
}

- (UIImageView *)alertView{
    if (!_alertView) {
        _alertView = [[UIImageView alloc] init];
        _alertView.contentMode = UIViewContentModeScaleAspectFill;
        _alertView.clipsToBounds = YES;
        _alertView.userInteractionEnabled = YES;
    }
    return _alertView;
}

- (UIButton *)cancelBtn{
    if (!_cancelBtn) {
        UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
        [btn setImage:[UIImage imageNamed:@"icon_alert_close"] forState:UIControlStateNormal];
        [btn addTarget:self action:@selector(dismissAlertAction) forControlEvents:UIControlEventTouchUpInside];
        _cancelBtn = btn;
    }
    return _cancelBtn;
}

- (void)dismissAlertAction{
    if (self.alertDismissHandle) {
        self.alertDismissHandle();
    }
  
    [self dismissAlert];
}

/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/

@end





@interface MRKPurchaseDiscountView()
@property (nonatomic, strong) UIImageView *backgroundImageView;
@property (nonatomic, strong) UIView *contentView;
@property (nonatomic, strong) UILabel *titleLab;
@property (nonatomic, strong) UILabel *descripLab;
@property (nonatomic, strong) UIButton *receiveBtn;
@end

@implementation MRKPurchaseDiscountView

- (instancetype)init {
    self = [super init];
    if (self) {
        self.clipsToBounds = NO;
        
        [self addSubview:self.backgroundImageView];
        [self.backgroundImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.mas_equalTo(UIEdgeInsetsZero);
        }];
        [self addSubview:self.receiveBtn];
        [self.receiveBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(self.mas_bottom).offset(-WKDHPX(15));
            make.centerX.equalTo(self.mas_centerX);
            make.width.mas_equalTo(WKDHPX(200));
            make.height.mas_equalTo(WKDHPX(48));
        }];
      
        [self addSubview:self.contentView];
        [self.contentView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(self.mas_centerX);
            make.centerY.equalTo(self.mas_centerY).offset(WKDHPX(6));;
            make.width.mas_equalTo(WKDHPX(295));
        }];
        
        [self.contentView addSubview:self.titleLab];
        [self.contentView addSubview:self.descripLab];
        [self.titleLab mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.contentView.mas_top);
            make.centerX.equalTo(self.contentView.mas_centerX);
            make.width.mas_equalTo(WKDHPX(200));
        }];
        [self.descripLab mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.titleLab.mas_bottom).offset(WKDHPX(11));
            make.centerX.equalTo(self.contentView.mas_centerX);
            make.width.mas_equalTo(WKDHPX(240));
            make.bottom.equalTo(self.contentView.mas_bottom);
        }];
    }
    return self;
}

- (void)layoutSubviews{
    [super layoutSubviews];
    
}

- (UIImageView *)backgroundImageView{
    if (!_backgroundImageView) {
        _backgroundImageView = [[UIImageView alloc] init];
        _backgroundImageView.contentMode = UIViewContentModeScaleAspectFit;
        _backgroundImageView.image = [UIImage imageNamed:@"vip_purchase_alert_bg"];
    }
    return _backgroundImageView;
}

- (UIView *)contentView{
    if (!_contentView) {
        _contentView = [[UIView alloc] init];
    }
    return _contentView;
}

- (UILabel *)titleLab{
    if (!_titleLab) {
        _titleLab = [[UILabel alloc] init];
        _titleLab.textAlignment = NSTextAlignmentCenter;
        _titleLab.font = kMedium_Font_NoDHPX(WKDHPX(20));
        _titleLab.textColor = UIColorHex(#513007);
        _titleLab.numberOfLines = 2;
    }
    return _titleLab;
}

- (UILabel *)descripLab{
    if (!_descripLab) {
        _descripLab = [[UILabel alloc] init];
        _descripLab.textAlignment = NSTextAlignmentCenter;
        _descripLab.font = kSystem_Font_NoDHPX(WKDHPX(15));
        _descripLab.textColor = UIColorHex(#513007);
        _descripLab.numberOfLines = 0;
    }
    return _descripLab;
}

- (UIButton *)receiveBtn {
    if (!_receiveBtn){
        _receiveBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        _receiveBtn.backgroundColor = UIColorHex(#202121);
        [_receiveBtn setTitleColor:UIColorHex(#FFD5B6) forState:UIControlStateNormal];
        [_receiveBtn setTitle:@"去领取" forState:UIControlStateNormal];
        _receiveBtn.titleLabel.font = [UIFont systemFontOfSize:16 weight:UIFontWeightMedium];
        [_receiveBtn addTarget:self action:@selector(subscribeAction:) forControlEvents:UIControlEventTouchUpInside];
        _receiveBtn.layer.cornerRadius = WKDHPX(48)/2;
        _receiveBtn.layer.masksToBounds = YES;
    }
    return _receiveBtn;
}

- (void)subscribeAction:(id)sender{
    if (self.purchaseDiscountReceiveBlock) {
        self.purchaseDiscountReceiveBlock();
    }
}

- (void)setModel:(MRKPurchaseDiscountModel *)model{
    _model = model;
    
    self.titleLab.text = model.title;
    self.descripLab.text = model.subTitle;
    
    if ([model.buttonText isNotBlank]){
        [self.receiveBtn setTitle:model.buttonText forState:UIControlStateNormal];
    }
}

/*
 // Only override drawRect: if you perform custom drawing.
 // An empty implementation adversely affects performance during animation.
 - (void)drawRect:(CGRect)rect {
 // Drawing code
 }
 */

@end
