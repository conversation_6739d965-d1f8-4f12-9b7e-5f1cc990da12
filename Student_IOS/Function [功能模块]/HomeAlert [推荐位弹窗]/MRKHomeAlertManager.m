//
//  MRKHomeAlertManager.m
//  Student_IOS
//
//  Created by merit on 2022/6/28.
//

#import "MRKHomeAlertManager.h"
#import "MRKPopupManager.h"
#import "MRKAlertStatusModel.h"
#import "MRKAlertController.h"
#import "MRKPlanNotificationAlert.h"
#import <UserNotifications/UserNotifications.h>

@interface MRKHomeAlertManager()

@end

@implementation MRKHomeAlertManager

+ (instancetype)shareManager {
    static MRKHomeAlertManager *instance = nil;
    static dispatch_once_t oneToken;
    dispatch_once(&oneToken,^{
        instance = [[MRKHomeAlertManager alloc] init];
    });
    return instance;
}

///  请求弹窗数据
/// @param target controller
/// @param code  唯一码
- (void)addTarget:(nullable id)target requestAlertData:(NSString *)code{
    if (self.beingProcessed) { return; }
    self.beingProcessed = YES;
    
    [MRKAdvertManager mrkRequestPositionCode:code
                    completeBlockWithSuccess:^(MRKAdvertDataModel * _Nullable model) {
        if(model.adverts.count > 0){
            AdvertModel *mode = model.adverts.firstObject;
            mode.tracePageId = [self advertTracePageIdWithCode:code];
            [self checkAlertModel:mode andCode:code addTarget:target];
        } else {
            self.beingProcessed = NO;
        }
    }];
}

- (NSString *)advertTracePageIdWithCode:(NSString *)code {
    if ([code isEqualToString:MRKRecommendPopupCode]) {
        return @"page_popup_home";
    } else if ([code isEqualToString:MRKPlanPopupCode]) {
        return @"page_popup_plan";
    } else if ([code isEqualToString:MRKUcenterPopupCode]) {
        return @"page_popup_profile";
    } else if ([code isEqualToString:MRKMemberPopupCode]) {
        return @"page_popup_profile_vip";
    }
    return @"page_popup";
}

- (void)showNotificationOpenAlert:(NSString *)code{
    NSTimeInterval currentTime = [[NSDate date] timeIntervalSince1970];
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    NSString *date = [defaults objectForKey:@"PlanNotificationAlert"];
    double time = [date doubleValue];
    if (date != nil && currentTime < 7*24*60*60 + time ) { // 7天弹出一次
        return;
    }
    
    ///判断通知弹窗
    [self isUserNotificationEnableWithCompletion:^(BOOL enabled) {
        if (!enabled) {
            @weakify(self);
            ReportMrkLogParms(1, @"开启通知提醒", @"page_pupop_notification", @"", nil, 0, nil);
            [MrkAlertManager showTopImageAlert:@"pic_push"
                                         title:[self notificationOpenTitle: code]
                                       message:[self notificationOpenContent: code]
                                        cancel:@"暂不开启"
                                        ensure:@"开启通知"
                                   handleIndex:^(NSInteger index) {
                @strongify(self);
                if (index == 1) {
                    ReportMrkLogParms(2, @"开启通知提醒点击", @"page_pupop_notification", @"btn_enable_notification", nil, 0, nil);
                    [self goToAppSystemSetting];
                }
            }];
        }
    }];
    
    NSString *str = [NSString stringWithFormat:@"%f",currentTime];
    [defaults setObject:str forKey:@"PlanNotificationAlert"];
    [defaults synchronize];
}

- (NSString *)notificationOpenTitle:(NSString *)code {
    if ([code isEqualToString:MRKDailyScheduleNotificationCode] || [code isEqualToString:MRKCourseDetailNotificationCode]) { // 日程、课程详情用统一文案
        return @"打开推送通知";
    }
    return @"开启推送通知";
}

- (NSString *)notificationOpenContent:(NSString *)code {
    if ([code isEqualToString:MRKDailyScheduleNotificationCode] || [code isEqualToString:MRKCourseDetailNotificationCode]) { // 日程、课程详情用统一文案
        return @"热门好课、火爆活动上新立即提醒";
    }
    return @"即可第一时间接收到MERIT的推送提醒";
}


- (void)isUserNotificationEnableWithCompletion:(void(^)(BOOL enabled))completion {
    if (@available(iOS 10.0, *)) {
        [[UNUserNotificationCenter currentNotificationCenter] getNotificationSettingsWithCompletionHandler:^(UNNotificationSettings * _Nonnull settings) {
            BOOL isEnable = (settings.authorizationStatus == UNAuthorizationStatusAuthorized ||
                             settings.authorizationStatus == UNAuthorizationStatusProvisional);
            if (completion) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    completion(isEnable);
                });
            }
        }];
    } else if ([[UIDevice currentDevice].systemVersion floatValue] >= 8.0f) {
        // iOS 8~9
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"
        UIUserNotificationSettings *setting = [[UIApplication sharedApplication] currentUserNotificationSettings];
        BOOL isEnable = (setting.types != UIUserNotificationTypeNone);
#pragma clang diagnostic pop
        if (completion) {
            completion(isEnable);
        }
    } else {
        // iOS 7及以下
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"
        UIRemoteNotificationType type = [[UIApplication sharedApplication] enabledRemoteNotificationTypes];
        BOOL isEnable = (type != UIRemoteNotificationTypeNone);
#pragma clang diagnostic pop
        if (completion) {
            completion(isEnable);
        }
    }
}

- (void)goToAppSystemSetting {
    [UIApplication displayAppPrivacySettings];
}

- (void)checkAlertModel:(AdvertModel *)model andCode:(NSString *)code addTarget:(nullable id)target{
    @weakify(self);
    void (^downLoadImageBlock)(void) = ^{
        @strongify(self);
        MRKAlertStatusModel *alertModel = [[MRKAlertStatusModel alloc] init];
        alertModel.userId = UserInfo.userId;
        alertModel.identifer = NSStringFromClass([target class]);
        alertModel.positionCode = code;
        alertModel.alertData = [model modelToJSONObject];
        alertModel.imageUrl = model.image;
        alertModel.alertId = model.idd;
        alertModel.tracePageId = model.tracePageId;
        dispatch_async(dispatch_get_main_queue(), ^{
            [self addAlert:alertModel];
        });
    };
    
    ///判断是否可以弹窗
    if ([self checkAlertHasDisPlay:code model:model]){
        downLoadImageBlock();
    }
}

- (BOOL)checkAlertHasDisPlay:(NSString *)code model:(AdvertModel *)model{
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    NSData *data = [userDefaults objectForKey:code];
    MRKAlertStatusModel *statusModel = [self unarchiveObjectWithData:data];
    if ( !statusModel) { return YES; }
    
    AdvertModel *oldModel = [AdvertModel modelWithJSON:statusModel.alertData];
    ///有数据 对比id 是否一致 / 是否弹出过
    if ([oldModel.idd isEqualToString:model.idd] && statusModel.hasDisplayed) {
        NSTimeInterval nowTime = (long long)[[NSDate date] timeIntervalSince1970];
        ///小于72小时内不再弹出
        if (nowTime - statusModel.openTime < 72 * 3600 ) {
            self.beingProcessed = NO;
            return NO;
        }
    }
    return YES;
}


- (void)addAlert:(MRKAlertStatusModel *)model {
    UIViewController *current = [UIViewController currentViewController];
    if ( ![current isKindOfClass:[NSClassFromString(model.identifer) class]]) {
        self.beingProcessed = NO;
        
        //存储到NSUserDefaults（转NSData存）
        NSData *data = [self archivedDataWithRootObject:model];
        NSUserDefaults *user = [NSUserDefaults standardUserDefaults];
        [user setObject:data forKey:model.positionCode];
        return;
    }
    
    [self showAlertController:model];
}

#pragma mark - 检查弹窗
- (void)addTarget:(nullable id)target  checkAlertCode:(NSString *)code{
    NSUserDefaults *alertData = [NSUserDefaults standardUserDefaults];
    NSData *data = [alertData objectForKey:code];
    //无数据不显示
    if (!data) {
        ///如果没有处理数据, 请求弹窗
        if (!self.beingProcessed) {
            [self addTarget:target requestAlertData:code];
        }
        return;
    }
    
    //如果在处理内容
    if (self.beingProcessed) {
        return;
    }
    
    MRKAlertStatusModel *model = [self unarchiveObjectWithData:data];
    //已展示过不显示
    if (model.hasDisplayed) {
        return;
    }
    
    //如果当前controller跟要弹窗的页面不一致
    UIViewController *current = [UIViewController currentViewController];
    if (![current isKindOfClass:[NSClassFromString(model.identifer) class]]) {
        return;
    }
    
    [self showAlertController:model];
}

- (void)showAlertController:(MRKAlertStatusModel *)model{
    dispatch_async(dispatch_get_main_queue(), ^{
        MRKAlertController *pageVC = [MRKAlertController controllerWithModel:model];
        pageVC.alertType = MRKAlertControllerTypeImageUrl;
        pageVC.view.alpha = 0;
        [[MRKPopupManager sharedInstance] showAlertView:pageVC level:MRKPopupViewLevelDefault callback:^{
            
            [UIView animateWithDuration:0.3  animations:^{
                pageVC.view.alpha = 1;
            } completion:^(BOOL finished) {
                model.hasDisplayed = YES;
                model.openTime = (long long)[[NSDate date] timeIntervalSince1970];
                
                NSData *data = [self archivedDataWithRootObject:model];
                NSUserDefaults *user = [NSUserDefaults standardUserDefaults];
                [user setObject:data forKey:model.positionCode];
            }];
        }];
    });
}


#pragma mark - 归档/解档兼容
- (MRKAlertStatusModel *)unarchiveObjectWithData:(NSData *)data {
    if (data.length == 0) return nil;
    
    NSError *error = nil;
    MRKAlertStatusModel *model = nil;
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"
    if (@available(iOS 11.0, *)) {
        id obj = [NSKeyedUnarchiver unarchiveTopLevelObjectWithData:data error:&error];
        if ([obj isKindOfClass:[MRKAlertStatusModel class]]) {
            model = obj;
        }
    } else {
        id obj = [NSKeyedUnarchiver unarchiveObjectWithData:data];
        if ([obj isKindOfClass:MRKAlertStatusModel.class]) {
            model = obj;
        }
    }
#pragma clang diagnostic pop
    if (error) NSLog(@"解档失败: %@", error.localizedDescription);
    return model;
}

- (NSData *)archivedDataWithRootObject:(id)object {
    if (!object) return nil;
    
    NSError *error = nil;
    NSData *data = nil;
    if (@available(iOS 11.0, *)) {
        data = [NSKeyedArchiver archivedDataWithRootObject:object
                                     requiringSecureCoding:NO
                                                     error:&error];
        if (error) NSLog(@"归档失败: %@", error.localizedDescription);
    } else {
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"
        data = [NSKeyedArchiver archivedDataWithRootObject:object];
#pragma clang diagnostic pop
    }
    return data;
}


@end






