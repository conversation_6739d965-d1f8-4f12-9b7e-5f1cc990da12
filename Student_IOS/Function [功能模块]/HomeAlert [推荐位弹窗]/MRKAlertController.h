//
//  MRKAlertController.h
//  Student_IOS
//
//  Created by merit on 2022/10/11.
//

#import <UIKit/UIKit.h>
#import "MRKAlertStatusModel.h"

NS_ASSUME_NONNULL_BEGIN


typedef NS_ENUM(NSInteger, MRKAlertControllerType) {
    MRKAlertControllerTypeImage,      // 图片
    MRKAlertControllerTypeImageUrl,   // 图片Url
    MRKAlertControllerTypeVIP,        // 会员购买
};


@interface MRKAlertController : UIViewController
+ (MRKAlertController *)controllerWithModel:(MRKAlertStatusModel *)model;
+ (MRKAlertController *)controllerWithImage:(UIImage *)image;


@property (nonatomic, strong) MRKAlertStatusModel *model;
@property (nonatomic, strong) UIImage *image;

@property (nonatomic, copy) void (^alertRouterHandle)(void);
@property (nonatomic, copy) void (^alertDismissHandle)(void);
@end


NS_ASSUME_NONNULL_END
