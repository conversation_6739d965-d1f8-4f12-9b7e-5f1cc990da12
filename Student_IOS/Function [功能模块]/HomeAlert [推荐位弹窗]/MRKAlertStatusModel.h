//
//  MRKAlertStatusModel.h
//  Student_IOS
//
//  Created by merit on 2022/10/11.
//

#import <Foundation/Foundation.h>
#import "MRKBaseModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface MRKAlertStatusModel : MRKBaseModel
@property (nonatomic, strong) NSDictionary *alertData;    //弹窗数据
@property (nonatomic, copy) NSString *userId;             //用户id
@property (nonatomic, assign) BOOL hasDisplayed;          //是否显示过
@property (nonatomic, copy) NSString *identifer;          //controller
@property (nonatomic, copy) NSString *positionCode;       //唯一码
@property (nonatomic, copy) NSString *imageUrl;           //显示的图片
@property (nonatomic, assign) NSTimeInterval openTime;    //弹出时间
@property (nonatomic, copy) NSString *alertId;            //弹窗id
@property (nonatomic, strong) NSString *tracePageId;      //页面id
@end

NS_ASSUME_NONNULL_END
