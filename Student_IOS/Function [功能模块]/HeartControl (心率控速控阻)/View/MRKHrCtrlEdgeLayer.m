//
//  MRKHrCtrlEdgeLayer.m
//  Student_IOS
//
//  Created by merit on 2022/9/6.
//

#import "MRKHrCtrlEdgeLayer.h"
#import "UIView+SJAnimationAdded.h"
#import "SJItemTags.h"
#import "SJVideoPlayerConfigurations.h"
#import "MRKHrCtrlHeartView.h"
#import "HrCtrlChartView.h"
#import "MRKTrainRateModel.h"
#import "MRKSignActivity.h"

@interface MRKHrCtrlEdgeLayer ()
@property (nonatomic, strong, readonly) SJEdgeControlButtonItem *backItem;
@property (nonatomic, strong) MRKHrCtrlHeartView *rateView;
@property (nonatomic, strong) HrCtrlChartView *chartView;
@property (nonatomic, strong) MRKHrCtrlStopLayer *stopLayer;
@property (nonatomic, strong) MRKHrCtrlProgressView *progressView;
@end

@implementation MRKHrCtrlEdgeLayer

- (void)dealloc {
     NSLog(@"😊😊😊-----dealloc-----😊😊😊%@" , NSStringFromClass([self class]));
}

- (MRKHrCtrlProgressView *)progressView{
    if (!_progressView) {
        _progressView = [[MRKHrCtrlProgressView alloc] init];
    }
    return _progressView;
}

- (MRKHrCtrlStopLayer *)stopLayer{
    if (!_stopLayer) {
        _stopLayer = [[MRKHrCtrlStopLayer alloc] init];
        @weakify(self);
        _stopLayer.tapDoneActionBlock = ^(NSInteger tag) {
            @strongify(self);
            [self _doneItemWasTapped];
        };
        _stopLayer.tapContinueActionBlock = ^(NSInteger tag) {
            @strongify(self);
            [self _continueItemWasTapped];
        };
    }
    return _stopLayer;
}

- (MRKHrCtrlHeartView *)rateView {
    if (!_rateView) {
        _rateView = [[MRKHrCtrlHeartView alloc] init];
    }
    return _rateView;
}

- (HrCtrlChartView *)chartView{
    if (!_chartView) {
        _chartView = [[HrCtrlChartView alloc] init];
    }
    return _chartView;
}

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if ( !self ) return nil;
    self.backgroundColor = [UIColor clearColor];
    self.useTop = YES;
    
    self.autoAdjustTopSpacing = NO;
    self.topHeight = DHPX(45);
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(configRateKcalView:)
                                                 name:SignActivityUseRateKcalNote
                                               object:nil];
    ///添加图表
    [self addSubview:self.chartView];
    [self.chartView mas_makeConstraints:^(MASConstraintMaker *make) {
        if (@available(iOS 11.0, *)) {
            make.left.equalTo(self.mas_safeAreaLayoutGuideLeft);
            make.right.equalTo(self.mas_safeAreaLayoutGuideRight);
            make.top.equalTo(self.mas_safeAreaLayoutGuideTop);
            make.bottom.equalTo(self.mas_safeAreaLayoutGuideBottom);
        } else {
            make.edges.mas_equalTo(UIEdgeInsetsMake(10, 10, 10, 10));
        }
    }];
    
    ///添加超然脂率
    [self addSubview:self.rateView];
    [self bringSubviewToFront:self.rateView];
    [self.rateView mas_makeConstraints:^(MASConstraintMaker *make) {
        float padding = IS_IPHONEX_SURE ? 30 : 10;
        make.right.equalTo(self.mas_right).offset(-padding);
        make.top.mas_equalTo(self.mas_top).offset(80);
        make.size.mas_equalTo(CGSizeMake(60, 60));
    }];
    
    [self _setupView];
    [self _updateAppearStateForContainerViews];
    [self _reloadAdaptersIfNeeded];
    
    return self;
}

- (void)layoutSubviews {
    [super layoutSubviews];

}

///心率消耗view配置
- (void)configRateKcalView:(NSNotification *)notification{
//    CheckActivityModel *model = notification.object;
//    dispatch_time_t delayTime = dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC));
//    dispatch_after(delayTime, dispatch_get_main_queue(), ^{
//        ///tip 心率消耗的提示弹窗
//        [self.rateView checkHeartRateAlert:self withModel:model];
//        
//        ///显示心率消耗view
//        dispatch_time_t delayTime = dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC));
//        dispatch_after(delayTime, dispatch_get_main_queue(), ^{
//            [self.rateView showRateKcalView:model completeHandler:^{
//                
//            }];
//        });
//    });
}

- (NSNumber *)getPlayerAge {
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    NSNumber *playerAge = [defaults objectForKey:@"playerAge"];
    return playerAge;
}

- (void)setRateDisPlayArr:(NSMutableArray *)rateDisPlayArr{
    _rateDisPlayArr = rateDisPlayArr;
    self.chartView.rateDisPlayArr = rateDisPlayArr;
    
    ///处理心率预警
    self.rateView.isShowHeartRateAlert = YES;
    self.rateView.layerTipView = self;
}

- (void)setHeartRateList:(NSMutableArray *)heartRateList{
    _heartRateList = heartRateList;
}

- (void)setRunningRecord:(MRKRunningRecord *)runningRecord{
    _runningRecord = runningRecord;
    
    ///进度条设置默认状态
    self.timeTarget = runningRecord.totalSeconds;
    self.statusStr = runningRecord.currentTitle;
}
- (void)setTimeTarget:(NSTimeInterval)timeTarget{
    _timeTarget = timeTarget;
    self.progressView.timeTarget = timeTarget;
    self.chartView.timeTarget = timeTarget;
}
- (void)setStatusStr:(NSString *)statusStr{
    _statusStr = statusStr;
    self.progressView.statusStr = statusStr;
}

- (void)setUserTime:(NSTimeInterval)userTime{
    _userTime = userTime;
    self.progressView.userTime = userTime;
}

///刷新图表
- (void)reloadChartView{
    self.chartView.heartRateList = self.runningRecord.heartRateList;
    self.chartView.heartRateTimeList = self.runningRecord.heartRateTimeList;
    [self.chartView reloadChartView];
}

///移除动画
- (void)removeAnimation{
    [self.rateView removeAnimation];
}

///结束按钮点击
- (void)_doneItemWasTapped{
    if ( [self.delegate respondsToSelector:@selector(doneItemWasTappedForControlLayer:)] ) {
        [self.delegate doneItemWasTappedForControlLayer:self];
    }
}

- (void)_continueItemWasTapped{
    if ( [self.delegate respondsToSelector:@selector(continueItemWasTappedForControlLayer:)] ) {
        [self.delegate continueItemWasTappedForControlLayer:self];
    }
}

///子类实现暂停按钮点击
- (void)_autoItemWasTapped{
    if ( [self.delegate respondsToSelector:@selector(pauseItemWasTappedForControlLayer:)] ) {
        [self.delegate pauseItemWasTappedForControlLayer:self];
    }
}


- (void)_showPauseLayer{
    if (!self.stopLayer.superview) {
        self.stopLayer.alpha = 0.0;
        self.stopLayer.frame = CGRectMake(0, 0, self.width, self.height);
        [self addSubview:self.stopLayer];
        [self bringSubviewToFront:self.stopLayer];
        
        self.isStopLayerAppeared = YES;
        
        @weakify(self);
        [UIView animateWithDuration:0.5 animations:^{
            @strongify(self);
            self.stopLayer.alpha = 1.0;
        } completion:^(BOOL finished) {

        }];
    }
}

- (void)_dismissPauseLayer{
    if (self.stopLayer.superview) {
        self.isStopLayerAppeared = NO;
        
        @weakify(self);
        [UIView animateWithDuration:0.5 animations:^{
            @strongify(self);
            self.stopLayer.alpha = 0.0;
        } completion:^(BOOL finished) {
            @strongify(self);
            [self.stopLayer removeFromSuperview];
        }];
    }
}


#pragma mark - setup view

- (void)_setupView {
    [self _addItemsToTopAdapter];
    [self _addItemsBottomDataAdapter];
    
    self.topContainerView.sjv_disappearDirection = SJViewDisappearAnimation_Top;
    self.bottomDataView.sjv_disappearDirection = SJViewDisappearAnimation_Bottom;
    
    sj_view_initializes(@[self.topContainerView,self.bottomDataView]);
}

- (void)_addItemsToTopAdapter {
    sj_view_makeAppear(_topContainerView, YES);
    
    [self.topContainerView addSubview:self.progressView];
    [self.progressView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.topContainerView.mas_top).offset(0);
        make.left.equalTo(self.topContainerView.mas_left).offset(0);
        make.right.equalTo(self.topContainerView.mas_right).offset(0);
        make.height.mas_equalTo(DHPX(45));
    }];
}

- (void)_addItemsBottomDataAdapter {
    ///添加设备显示模块
    [self.dataAdapter removeAllItems];
    for (MRKEqDisplayModel *model in self.deviceDisplayArr) {
        SJEdgeControlButtonItemTag tag = model.displayId.integerValue + 50000;
        SJEdgeControlButtonItem *item = [SJEdgeControlButtonItem placeholderWithSize:DHPX(90) tag:tag];
        item.dataStr = model.defaultValue;
        item.holdStr = model.name;
        [self.dataAdapter addItem:item];
    }
    [self.dataAdapter reload];
    
    ///刷新载体
    [self _updateAppearStateForBottomDataContainerView];
    
    ///刷新控制按钮
    [self _reloadDataAdapterIfNeeded];
}





#pragma mark - appear state

- (void)_updateAppearStateForContainerViews {
    [self _updateAppearStateForTopContainerView];
    [self _updateAppearStateForBottomDataContainerView];
}

- (void)_updateAppearStateForTopContainerView {
    sj_view_makeAppear(_topContainerView, YES);
}

- (void)_updateAppearStateForBottomDataContainerView {
    if ( 0 == _dataAdapter.numberOfItems ) {
        sj_view_makeDisappear(_bottomDataView, YES);
        return;
    }

    sj_view_makeAppear(_bottomDataView, YES);
}

#pragma mark - update items

- (void)_reloadAdaptersIfNeeded {
    [self _reloadDataAdapterIfNeeded];
}

- (void)_reloadDataAdapterIfNeeded {
    if ( sj_view_isDisappeared(_bottomDataView) ) return;
    
    self.usePlanCourseControl.hidden =  NO;
    // icon_pause/icon_play
    UIImage *image = [UIImage imageNamed:@"hrCtrl_sport_pause"];
    [self.usePlanCourseControl setImage:image forState:UIControlStateNormal];
    
    [_dataAdapter reload];
}


- (void)dataAdapterUpdateWithModel:(BaseEquipDataModel *)model{
    dispatch_async(dispatch_get_main_queue(), ^{
        BaseEquipDataModel *m = model;
        for (SJEdgeControlButtonItem *item in self.dataAdapter.items) {
            NSInteger tag = (NSInteger) item.tag;
            switch (tag) {
                case 50001: {///运动时间
                    NSString *currentTimeStr = [MRKToolKit MSTimeStrFromSecond:m.totalTimeSecond.intValue];
                    item.dataStr = currentTimeStr;
                }break;
                case 50002: {///消耗(kcal)
                    item.dataStr = m.energy ? [NSString convertDecimalNumber: [NSString stringWithFormat:@"%@", m.energy] num:1] : @"--";
                }break;
                case 50003: {///心率(bmp)
                    item.dataStr = m.rate ? (m.rate.intValue == 0 ? @"--" :  m.rate.stringValue) : @"--";
                }break;
                case 50004: case 50008:{///踏频(rpm)/桨频(spm)
                    item.dataStr = m.spm ? (m.spm.intValue == 0 ? @"--" :  m.spm.stringValue) : @"--";
                }break;
                case 50005: {///阻力(lv)
                    item.dataStr = m.drag ? (m.drag.intValue == 0 ? @"--" :  m.drag.stringValue) : @"--";
                }break;
                case 50006: {///坡度
                    item.dataStr = m.gradient ? (m.gradient.intValue == 0 ? @"--" : m.gradient.stringValue) : @"--";
                }break;
                case 50007: {///距离(km)
                    item.dataStr = [NSString convertDecimalNumber: [NSString stringWithFormat:@"%@", m.totalDistance] num:2 dividing: @"1000"];
                }break;
                case 50009:  case 50010: {///个数/圈数
                    item.dataStr =  m.count ? (m.count.intValue == 0 ? @"0" :  m.count.stringValue) : @"--";
                }break;
                case 50011: {///挡位
                    item.dataStr = m.grade.stringValue;
                }break;
                case 50012: { ///速度(km/h)
                    item.dataStr = [NSString stringWithFormat:@"%.1f" , m.speed.floatValue];
                }break;
                case 50013: { ///力量站次数
                    item.dataStr =  m.count ? (m.count.intValue == 0 ? @"0" :  m.count.stringValue) : @"--";
                }break;
                default: break;
            }
            [self->_dataAdapter updateContentForItemWithTag:item.tag];
        }
    });
}

///心率蓝牙数据
- (void)setEquipHeartRateInfo:(NSNumber *)heartRate{
    [self.rateView setEquipHeartRateInfo:heartRate];
}

///指令操作
- (void)instructionsOperationData:(int)speed{
    if ( sj_view_isDisappeared(_bottomDataView) ) return;
    
    int typeId = self.equipmentTypeId.intValue;
    if (typeId == TreadmillEquipment) {
        ///跑步机
        NSString *tip = @"";///拼接提示
        ///
        SJEdgeControlButtonItem *item8 = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_Speed];
        UIView *vi = item8.partView; ///锚点
       
        float speedNum = (float)speed/10; ////跑步机速度提示要除以10
        tip = [NSString stringWithFormat:@"速度调整至%.1f",speedNum];
        [MBProgressHUD showMiaToast:tip toView:self];
        
    } else if (typeId == BoatEquipment || typeId == BicycleEquipment || typeId == EllipticalEquipment) {
        //划船机,单车,椭圆机

    }
}

/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/

@end



@interface MRKHrCtrlStopLayer ()
@property (nonatomic, strong) UILabel *titleLab;
@property (nonatomic, strong) UIButton *endBtn;
@property (nonatomic, strong) UIButton *continueBtn;
@end

@implementation MRKHrCtrlStopLayer

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if ( !self ) return nil;
    
    self.backgroundColor = [UIColor colorWithWhite:0 alpha:0.7];
    
    [self addSubview:self.endBtn];
    [self addSubview:self.continueBtn];
    [self addSubview:self.titleLab];
    
    return self;
}

- (UILabel *)titleLab{
    if (!_titleLab) {
        _titleLab = [UILabel.alloc initWithFrame:CGRectZero];
        _titleLab.font = [UIFont systemFontOfSize:22 weight:UIFontWeightMedium];
        _titleLab.textColor = [UIColor whiteColor];
        _titleLab.textAlignment = 1;
        _titleLab.text = @"巨大的改变源自每一次的坚持";
    }
    return _titleLab;
}

- (UIButton *)endBtn{
    if (!_endBtn) {
        _endBtn = [UIButton.alloc initWithFrame:CGRectZero];
        [_endBtn setImage:[UIImage imageNamed:@"icon_end_title"] forState:UIControlStateNormal];
        [_endBtn addTarget:self action:@selector(btnClick:) forControlEvents:UIControlEventTouchUpInside];
        _endBtn.tag = 2000;
    }
    return _endBtn;
}

- (UIButton *)continueBtn{
    if (!_continueBtn) {
        _continueBtn = [UIButton.alloc initWithFrame:CGRectZero];
        [_continueBtn setImage:[UIImage imageNamed:@"icon_carry_on_title"] forState:UIControlStateNormal];
        [_continueBtn addTarget:self action:@selector(btnClick:) forControlEvents:UIControlEventTouchUpInside];
        _continueBtn.tag = 3000;
    }
    return _continueBtn;
}

- (void)btnClick:(UIButton *)sender{
    NSInteger tag = sender.tag;
    @weakify(self);
    [UIView animateWithDuration:0.5 animations:^{
        @strongify(self);
        self.alpha = 0;
    } completion:^(BOOL finished) {
        @strongify(self);
        if (tag == 2000) {
            if (self.tapDoneActionBlock) {
                self.tapDoneActionBlock(tag);
            }
        } else {
            if (self.tapContinueActionBlock) {
                self.tapContinueActionBlock(tag);
            }
        }
        if (finished) {
            [self removeFromSuperview];
        }
    }];
}

- (void)layoutSubviews {
    [super layoutSubviews];
    
    [self.endBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_equalTo(self.mas_centerY);
        make.right.mas_equalTo(self.mas_centerX).offset(-DHPX(60));
        make.size.mas_equalTo(CGSizeMake(DHPX(80), DHPX(80)));
    }];
   
    [self.continueBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_equalTo(self.mas_centerY);
        make.left.mas_equalTo(self.mas_centerX).offset(DHPX(60));
        make.size.mas_equalTo(CGSizeMake(DHPX(80), DHPX(80)));
    }];
    
    [self.titleLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.mas_equalTo(self.mas_centerX);
        make.bottom.mas_equalTo(self.continueBtn.mas_top).offset(-DHPX(46));
        make.size.mas_equalTo(CGSizeMake(350, DHPX(25)));
    }];
    
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
     NSLog(@"😊😊😊-----dealloc-----😊😊😊%@" , NSStringFromClass([self class]));
}

@end



#import "MrkProgressPopView.h"

@interface MRKHrCtrlProgressView ()
@property (nonatomic, strong) UILabel *timeLab;
@property (nonatomic, strong) UILabel *stepLab;
@property (nonatomic, strong) MrkProgressPopView *progressSlider;
@end

@implementation MRKHrCtrlProgressView

- (UILabel *)timeLab{
    if (!_timeLab) {
        UILabel *lab = [[UILabel alloc] init];
        lab.font = [UIFont fontWithName:Bebas_Font size:20];
        lab.textAlignment = 1;
        lab.textColor = [UIColor colorWithWhite:1 alpha:0.8];
        lab.adjustsFontSizeToFitWidth = YES;
        lab.text = @"00:00";
        _timeLab  = lab;
    }
    return _timeLab;
}

- (UILabel *)stepLab{
    if (!_stepLab) {
        UILabel *lab = [[UILabel alloc] init];
        lab.font = [UIFont systemFontOfSize:12];
        lab.textColor = [UIColor colorWithWhite:1 alpha:0.8];
        lab.adjustsFontSizeToFitWidth = YES;
        lab.text = @"热身阶段";
        _stepLab  = lab;
    }
    return _stepLab;
}

- (MrkProgressPopView *)progressSlider{
    if (!_progressSlider) {
        _progressSlider = [[MrkProgressPopView alloc] init];
        _progressSlider.trackTintColor = [UIColor colorWithWhite:1 alpha:0.2];
        _progressSlider.popUpViewAnimatedColors = @[UIColorHex(#5EDEFF),
                                                    UIColorHex(#FFB6E7),
                                                    UIColorHex(#FF5562)];
        _progressSlider.progress = 0.0;
        [_progressSlider showPopUpViewAnimated:YES];
    }
    return _progressSlider;
}

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        
        [self addSubview:self.timeLab];
        [self addSubview:self.stepLab];
        [self addSubview:self.progressSlider];
    }
    return self;
}

- (void)layoutSubviews{
    [super layoutSubviews];
    
    [self.timeLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_equalTo(self.mas_centerY);
        make.left.mas_equalTo(self.mas_left).offset(kScreenPadding);
        make.size.mas_equalTo(CGSizeMake(50, DHPX(45)));
    }];
    
    [self.stepLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_equalTo(self.mas_centerY);
        make.left.mas_equalTo(self.timeLab.mas_right).offset(5);
        make.right.mas_equalTo(self.mas_right).offset(-kScreenPadding);
    }];
    
    [self.progressSlider mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.mas_centerY).offset(5);
        make.left.mas_equalTo(self.timeLab.mas_right).offset(5);
        make.right.mas_equalTo(self.mas_right).offset(-kScreenPadding);
        make.height.mas_equalTo(5);
    }];
}

- (void)setStatusStr:(NSString *)statusStr{
    _statusStr = statusStr;
    self.stepLab.text = statusStr;
}

- (void)setProgress:(float)progress{
    _progress = progress;
    [self.progressSlider setProgress:progress  animated:NO];
}

- (void)setTimeTarget:(NSTimeInterval)timeTarget{
    _timeTarget = timeTarget;
    
    ///倒计时
    NSString *timeStr = [MRKToolKit MSTimeStrFromSecond:(int)timeTarget];
    self.timeLab.text = timeStr;
    
    ///进度
    self.progress = 0;
}

- (void)setUserTime:(NSTimeInterval)userTime{
    _userTime = userTime;
    
    ///倒计时
    userTime = MIN(userTime, self.timeTarget); //预防负数
    NSTimeInterval spaceTime = self.timeTarget - userTime;
    NSString *timeStr = [MRKToolKit MSTimeStrFromSecond:(int)spaceTime];
    self.timeLab.text = timeStr;
    
    ///进度
    self.progress = (float)userTime/self.timeTarget;
}

- (void)dealloc {
     NSLog(@"😊😊😊-----dealloc-----😊😊😊%@" , NSStringFromClass([self class]));
}


/*
 // Only override drawRect: if you perform custom drawing.
 // An empty implementation adversely affects performance during animation.
 - (void)drawRect:(CGRect)rect {
 // Drawing code
 }
 */

@end
