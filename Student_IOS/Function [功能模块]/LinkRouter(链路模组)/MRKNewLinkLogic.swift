//
//  MRKNewLinkLogic.swift
//  Student_IOS
//
//  Created by merit on 2025/8/5.
//

import Foundation

open class MRKNewLinkLogic: NSObject {
    
    /// 暂无设备点击处理逻辑处理
    @objc static func jumpNoConnect(){
        jumpToMakePlan("0", "0");// 暂无设备 也默认传徒手训练
    }
    
    /// 小件连接成功逻辑处理
    @objc static func jumpSmallConnectSuccess(_ productId: String){
        jumpToMakePlan("0", "0");// 小件 也默认传徒手训练
    }
    
    /// 四大件完成体验/暂不体验逻辑处理
    @objc static func jumpCompleteExperienceComplete(_ productId: String, experienceComplete: Bool){
        MRKAIPlanLogic.shared()?.requestPlanOverview({(it: MRKPlanOverViewModel?) in
            if (it?.currentPlan != nil || it?.isProcess == true) {
                MRKNewLinkLogic.jumpToOldCompleteExperienceSuccess(productId, experienceComplete)
            } else {
                MRKNewLinkLogic.jumpToMakePlan(productId, experienceComplete ? "1" : "0")
            }
        }, failure: {})
    }
    
    static func jumpToOldCompleteExperienceSuccess(_ productId: String, _ experienceComplete: Bool){
        let last = UIViewController.current();
        let vc = MRKCompleteEvaluationCourseController()
        vc.productId = productId
        vc.experienceComplete = experienceComplete
        last?.navigationController?.pushViewController(vc, animated: false)
        if let la = last, (la.isKind(of: NSClassFromString("NewUserEvaluationController")!) == true) {
            DispatchQueue.main.asyncAfter(deadline: DispatchTime.now() + 0.5, execute: {
                la.navigationController?.navigationRemoveContrller(la)
            })
        }
    }
    
    static func jumpToMakePlan(_ productId: String, _ type: String){
//        ///检查window根视图
//        if let window = UIApplication.shared.delegate?.window,
//           (window?.rootViewController?.isKind(of: NSClassFromString("UITabBarController")!) == true) {
//            UIViewController.current()?.navigationController?.popToRootViewController(animated: false)
//            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
//                jumpToNewLinkPlan(productId, type)
//            }
//            return
//        }
//        if Login.isLogin() {
//            Foundation.NotificationCenter.default.postNotificationOnMainThread(withName: "user_login", object: UserInfo.userId)
//            DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
//                jumpToNewLinkPlan(productId, type)
//            }
//        }
//    }
//    
//    @objc static func jumpToNewLinkPlan(_ productId: String, _ type: String){
        let vc = MRKFlutterNewLinkPlanVC()
        vc.productId = NSNumber(value: Int32(productId) ?? 0)
        vc.type = NSNumber(value: Int32(type) ?? 0)
        UIViewController.current()?.navigationController?.pushViewController(vc, animated: true)
    }
}
