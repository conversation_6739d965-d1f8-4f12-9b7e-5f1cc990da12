//
//  MRKHeartWarnManager.m
//  Student_IOS
//
//  Created by MacPro on 2023/1/4.
//

#import "MRKHeartWarnManager.h"
#import "MrkMeritHRDealManager.h"
#import "TreamillStatusManager.h"

///累计时间阀值
#define kHeartWarnOverDuration  10

///心率预警调节结束下次监测时间间隔
#define kHeartWarnIntervalTime  60

///教案冲突后，教案倒计时开始后 开始监测的间隔时间
#define kHeartWarnConflictIntervalTime  30

@interface MRKHeartWarnManager ()
@property (nonatomic , assign) NSInteger overDuration;///持续超过心率预警值的时间
@property (nonatomic , assign) NSInteger conflictTime;///教案冲突间隔时间
@property (nonatomic , assign) NSInteger intervalTime;///心率预警发送指令后的间隔时间
@property (nonatomic , strong) NSNumber *warnRateValue;///心率预警值
@property (nonatomic , strong, nullable) dispatch_source_t timer;
@property (nonatomic , assign) BOOL isSuspend;
@property (nonatomic , strong) TreamillStatusManager *treamillStatus;
@end

@implementation MRKHeartWarnManager

- (instancetype)init {
    self = [super init];
    if(self) {
        self.isSuspend = YES;
        
        self.conflictTime = kHeartWarnConflictIntervalTime;
        self.intervalTime = kHeartWarnIntervalTime;
        
        self.warnModel = [MRKRateWarnModel currentHeartWarnModel];
        self.warnRateValue = self.warnModel.threshold;
        
        ///监听心率设备连接状态
        @weakify(self);
        [[RACObserve([BlueDataStorageManager sharedInstance], connectDictionary) distinctUntilChanged] subscribeNext:^(NSDictionary * x) {
            @strongify(self);
            [self loadModel];
        }];
    }
    return self;
}

- (void)loadModel {
    ///未连接心率设备 不监测
    ///未连接当前大类设备 不监测
    if (![BluetoothManager isConnectEquipmentType:[@(HeartEquipment) stringValue]]
       || ![BluetoothManager isConnectEquipmentType:self.productId]) {
        [self suspendTimer];
        return;
    }
  
    ///没有设备信息 不处理
    if (!self.eqModel) {
        return;
    }
    
    /// 如果已经弹过弹窗，并且用户未开启心率预警，则不监测心率
    if (self.warnModel.isPopup.boolValue && !self.warnModel.isOpen.boolValue) {
        return;
    }
    
    ///开始监测
    [self startTimer];
}

- (void)dealTime {
    dispatch_async(dispatch_get_main_queue(), ^{
        NSNumber *rate = [MrkMeritHRDealManager shareManager].heartRate;
        NSLog(@"当前心率值===%@",rate);

        ///如果在教案冲突间隔内 不做处理
        if (self.conflictTime < kHeartWarnConflictIntervalTime) {
            self.conflictTime += 1;
            NSLog(@"MRKHeartWarnManager 教案冲突中%@" ,@(self.conflictTime));
            return;
        }
        
        ///如果在已调节间隔内 不做处理
        if (self.intervalTime < kHeartWarnIntervalTime) {
            self.intervalTime += 1;
            NSLog(@"MRKHeartWarnManager 已经调节间隔中%@" ,@(self.intervalTime));
            return;
        }
        
        if (rate.intValue > self.warnRateValue.intValue) {
            ///超过心率预警值
            ///未曾弹出
            if (!self.warnModel.isPopup.boolValue) {
                [self suspendTimer];
                ///弹出弹窗
                if(self.delegate && [self.delegate respondsToSelector:@selector(alertHeartRateWarnView:)]) {
                    [self.delegate alertHeartRateWarnView:self.eqModel];
                    ///调用接口通知后端弹出
                    [MRKRequestServiceData setHeartRateWarnPopup:@{} success:^(id data) {
                        NSLog(@"setHeartRateWarnPopup==%@" , data);
                        ///更新本地信息
                        [[NSNotificationCenter defaultCenter] postNotificationName:@"UpdateHeartWarnDetialNotification" object:nil];
                    } failure:^(id data) {
                        NSLog(@"setHeartRateWarnPopup_error==%@" , data);
                    }];
                    self.warnModel.isPopup = @1;
                }
                return;
            }
            
            ///已经弹出,未开启，不处理
            if (!self.warnModel.isOpen.boolValue) {
                [self suspendTimer];
                return;
            }
            
            self.overDuration += 1;///持续时间+1
            NSLog(@"MRKHeartWarnManager 超过预警心率监测中%@" , @(self.overDuration));
            ///超过时间 已经弹过，且已经开启，则判断是否可调节
            if (self.overDuration >= kHeartWarnOverDuration) {
                /// 防止极端情况 ，发送之前 再做一次判断
                /// 未连接设备 不做处理
                if (![BluetoothManager isConnectEquipmentType:self.productId]) {
                    NSLog(@"MRKHeartWarnManager 设备断开连接了");
                    [self suspendTimer];
                    return;
                }
                
                ///非超燃脂设备 不可调节 弹窗提示
                if (!self.eqModel.isElectromagneticControl) {
                    NSLog(@"MRKHeartWarnManager 我是非超燃脂设备");
                    
                    ///4.如果不涉及教案冲突，直接下发指令，下发以后开始计时60s 不再监测
                    NSString *type = self.productId;
                    if (type.intValue == TreadmillEquipment) {
                        ///调节速度
                        float speed = self.dataModel ? self.dataModel.speed.floatValue : self.showData.speed.floatValue;
                        if (speed > 1.0) {
                            [self showToast:@"达到心率预警值，建议降低速度"];
                        } else {
                            [self showToast:@"达到心率预警值，建议调整好状态后继续运动"];
                        }
                    }
                    
                    if (type.intValue == BicycleEquipment ||
                        type.intValue == BoatEquipment ||
                        type.intValue == EllipticalEquipment) {
                        ///调节阻力
                        int drag = self.dataModel ? self.dataModel.drag.intValue : self.showData.resistance.intValue;
                        if (drag > 1) {
                            [self showToast:@"达到心率预警值，建议降低阻力"];
                        } else {
                            [self showToast:@"达到心率预警值，建议调整好状态后继续运动"];
                        }
                    }
                    
                    if (type.intValue == StairClimbEquipment ) {
                        ///调节踏频
                        int spm = self.dataModel ? self.dataModel.spm.intValue : self.showData.spm.intValue;
                        if (spm > 1) {
                            [self showToast:@"达到心率预警值，建议降低踏频"];
                        } else {
                            [self showToast:@"达到心率预警值，建议调整好状态后继续运动"];
                        }
                    }

                    
                    self.intervalTime = 0;
                    self.overDuration = 0;
                    return;
                }
                
                ///3. 可调节 课程/超燃脂，是否涉及到教案，发送指令前，判断当前小节状态，如果是倒计时状态 则不下发指令，暂停监控30后继续监测
                if (self.isPlanControl) {
                    NSLog(@"MRKHeartWarnManager 我和教案冲突了");
                    self.conflictTime = 0;
                    self.overDuration = 0;
                    return;
                }
              
                ///4.如果不涉及教案冲突，直接下发指令，下发以后开始计时60s 不再监测
                NSString *type = self.productId;
                if (type.intValue == TreadmillEquipment) {
                    ///调节速度
                    float speed = self.dataModel ? self.dataModel.speed.floatValue : self.showData.speed.floatValue;
                    if (speed > 1.0) {
                        speed = MAX(speed - 1, 1);
                        NSString *titleString = [NSString stringWithFormat:@"达到心率预警值，即将降低速度至%.1f" , speed];
                        [self showToast:titleString];
                        
                        [[NSNotificationCenter defaultCenter] postNotificationName:SetResistanceSlopeSpeedNotification object:@{
                            Speed:@((int)(speed * 10)),
                            Slope:self.dataModel ? self.dataModel.speed : @(self.showData.gradient.intValue),
                            BlueDeviceType:type,
                            @"status":self.treamillStatus.currentStatus
                        }];
                    } else {
                        NSString *titleString = @"达到心率预警值，建议调整好状态后继续运动";
                        [self showToast:titleString];
                    }
                }
                
                if (type.intValue == BicycleEquipment ||
                    type.intValue == BoatEquipment ||
                    type.intValue == EllipticalEquipment) {
                    ///调节阻力
                    int drag = self.dataModel ? self.dataModel.drag.intValue : self.showData.resistance.intValue;
                    if (drag > 1) {
                        drag = drag - 1;
                        
                        NSString *titleString = [NSString stringWithFormat:@"达到心率预警值，即将降低阻力至%d", drag];
                        [self showToast:titleString];
                        
                        [[NSNotificationCenter defaultCenter] postNotificationName:SetResistanceSlopeSpeedNotification object:@{
                            Resistance:@(drag),
                            Slope:@(0),
                            BlueDeviceType:type
                        }];
                    } else {
                        NSString *titleString = [NSString stringWithFormat:@"达到心率预警值，建议降低%@，\n调整好状态后继续运动" , type.intValue == BoatEquipment ? @"桨频" : @"踏频"];
                        [self showToast:titleString];
                    }
                }
                
                self.intervalTime  = 0;
                self.overDuration = 0;
            }
        } else {
            ///未超过
            self.overDuration = 0;
        }
    });
}

- (void)showToast:(NSString *)title {
    UIViewController *vc = [UIViewController currentViewController];
    [MBProgressHUD showMiaToast:title toView:vc.view position:MBProgressHUBPositionBottom margin:(vc.view.size.height / 3.0)];
}

- (void)createDispatch_source_t {
    dispatch_queue_t queue = dispatch_queue_create("timer", DISPATCH_QUEUE_CONCURRENT);
    _timer = dispatch_source_create(DISPATCH_SOURCE_TYPE_TIMER, 0, 0, queue);
    NSTimeInterval delayTime = 0.0f;
    NSTimeInterval timeInterval = 1.0;
    dispatch_time_t startDelayTime = dispatch_time(DISPATCH_TIME_NOW, (int64_t)(delayTime * NSEC_PER_SEC));
    dispatch_source_set_timer(_timer, startDelayTime, timeInterval*NSEC_PER_SEC, 0.1*NSEC_PER_SEC);
    dispatch_source_set_event_handler(_timer,^{
        dispatch_async(dispatch_get_main_queue(), ^{
            [self dealTime];
        });
    });
    
    [self resumeTimer];
}

- (void)suspendTimer {
    if(_timer && !self.isSuspend) {
        dispatch_suspend(_timer);
        self.isSuspend = YES;
        ///心率带/设备 断开连接 则默认重置间隔值，重新连接后 直接开启监测
        self.conflictTime = kHeartWarnConflictIntervalTime;
        self.intervalTime = kHeartWarnIntervalTime;
        self.overDuration = 0;
    }
}

- (void)resumeTimer{
    if (_timer && self.isSuspend ) {
        dispatch_resume(_timer);
        self.isSuspend = NO;
    }
}

- (void)startTimer {
    if (!_timer) {
        [self createDispatch_source_t];
    } else {
        [self resumeTimer];
    }
}

- (void)closeTimer {
    if (_timer != nil) {
        [self resumeTimer];
        dispatch_cancel(_timer);
        _timer = nil;
    }
}

- (void)setEqModel:(EquipmentDetialModel *)eqModel {
    _eqModel = eqModel;
    [self loadModel];
}

- (TreamillStatusManager *)treamillStatus {
    if(!_treamillStatus) {
        _treamillStatus = [[TreamillStatusManager alloc]init];
    }
    return _treamillStatus;
}

- (void)setProductId:(NSString *)productId {
    _productId = productId;
}

- (void)dealloc {
    NSLog(@"😊😊😊-----dealloc-----😊😊😊%@" , NSStringFromClass([self class]));
}
@end
