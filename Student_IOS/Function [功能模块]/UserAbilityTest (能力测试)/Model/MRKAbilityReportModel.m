//
//  MRKAbilityReportModel.m
//  Student_IOS
//
//  Created by merit on 2022/11/28.
//

#import "MRKAbilityReportModel.h"

@implementation MRKAbilityReportModel
+ (NSDictionary *)modelContainerPropertyGenericClass {
    return @{ @"catalogues" : [AbilityCataloguesModel class],
              @"recommendPlan" : [AbilityRecommendPlanModel class]
    };
}

- (BOOL)modelCustomTransformFromDictionary:(NSDictionary *)dic {
    
    if (_productId.intValue == BoatEquipment){
        _deviceAblout = @"划行";
    }
  
    if (_productId.intValue == BicycleEquipment){
        _deviceAblout = @"骑行";
    }
    
    if (_productId.intValue == TreadmillEquipment){
        _deviceAblout = @"跑步";
    }
    
    if (_productId.intValue == EllipticalEquipment){
        _deviceAblout = @"踏行";
    }
 
    return YES;
}

@end


@implementation AbilityCataloguesModel
+ (NSDictionary *)modelContainerPropertyGenericClass {
    return @{ @"links" : [AbilityCataloguesLinkModel class]};
}
@end

@implementation AbilityRecommendPlanModel
+ (NSDictionary *)modelCustomPropertyMapper {
    return @{@"cid":@"id",
             @"descrip":@"description"
    };
}

+ (NSDictionary *)modelContainerPropertyGenericClass {
    return @{ @"light" : [AbilityPlanLightModel class]};
}
@end

@implementation AbilityCataloguesLinkModel

@end

@implementation AbilityPlanLightModel
+ (NSDictionary *)modelCustomPropertyMapper {
    return @{@"descrip":@"description"};
}
@end

