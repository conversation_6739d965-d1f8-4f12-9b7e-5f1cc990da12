//
//  AbilityTestWebVC.m
//  Student_IOS
//
//  Created by merit on 2022/11/18.
//

#import "AbilityTestWebVC.h"
#import "MRKAbilityTestPageModel.h"

#import "MRKAbilityPlayerView.h"
#import "AbilityEvaluateVC.h"
#import "MeritWebSocketUtility.h"
#import "MRKAbilityTestWebView.h"
#import "MRKAbilityAssessModel.h"
#import "MRKAbilityTestProgressView.h"

#import "MRKToolKit.h"
#import "BlueDataDealManager.h"
///数据转化类
#import "MRKDeviceJsonData.h"
#import "MRKAbilityConnectAlert.h"

///设备绑定
#import "MyDeviceListViewController.h"

#import "AbilityTestTimeManager.h"
#import "MRKDeviceJsonData.h"
#import "MeritScoketModel.h"
#import "SpeechSynthesizerManager.h"
#import "FeedbackGeneratorUtil.h"

#import "TreamillStatusManager.h"
#import "AbilityIntroduceVC.h"
#import "MRKAbilityQuitAlert.h"
#import "MRKSportDataCache.h"
#import "DeviceSearchViewController.h"
#import "MRKBuildDeviceListView.h"
/*
 测评状态枚举
 */
typedef NS_ENUM(NSInteger, ABILITY_STATUS_MODE) {
    ABILITY_STATUS_NORMAL = 0,  ///默认初始状态
    ABILITY_STATUS_FIRSTPLAYER, ///[第一个视频]
    ABILITY_STATUS_START,       ///开始测评
    ABILITY_STATUS_TESTING,     ///测评中
    ABILITY_STATUS_PAUSE,       ///测评暂停
    ABILITY_STATUS_TESTEND,     ///测评结束
    ABILITY_STATUS_ENDPLAYER,   ///[最后一个视频]
    ABILITY_STATUS_FINISH,      ///测评结束
};

static NSString *const AbilityTestAudio = @"https://merit-app.oss-cn-hangzhou.aliyuncs.com/assess/action_assess_music.mp3";

@interface AbilityTestWebVC ()<BlueDataDealManagerDelegate, AbilityTestTimeManagerDelegate>
///
@property (nonatomic, strong) MRKBuildDeviceListView *deviceListView;

@property (nonatomic, strong) MRKAbilityTestPageModel *pageModel;
///测试时间
@property (nonatomic, assign) NSInteger testTime;
///设备运行时长
@property (nonatomic, strong) NSNumber *totalTimeSecond;

///能力测试时间轴处理类
@property (nonatomic, strong) AbilityTestTimeManager *timeManager;
///设备信息
@property (nonatomic, strong) EquipmentDetialModel *eqModel;
///
@property (nonatomic, strong) BlueDataDealManager *dataManager;
///跑步机专用
@property (nonatomic, strong) TreamillStatusManager *treamillStatus;
@property (nonatomic, strong) NSNumber *currentStatus;

///播放器播放view
@property (nonatomic, strong) UIView *playerBackView;

@property (nonatomic, strong) MRKAbilityPlayerView *playerView;
@property (nonatomic, assign) BOOL playerPause;

@property (nonatomic, strong) MRKAbilityTestProgressView *progressView;
@property (nonatomic, strong) MRKAbilityTestWebView *webView;
@property (nonatomic, assign) BOOL isShowQuitAlert;

@property (nonatomic, assign) BOOL viewDidAppeared;

@property (nonatomic, strong) RACDisposable *dispose;
@property (nonatomic, strong) MRKAbilityConnectAlert *connectAlert;

///测评状态
@property (nonatomic, assign) ABILITY_STATUS_MODE abilityTestStatus;
///跨节时暂时赋值, 防止跨节失败时不补发跨节socket
@property (nonatomic, strong) Abi_AssessUnitsItem *unitChangeItem;
@property (nonatomic, strong) MRKSportDataCache *sportCache; //运动数据缓存
@property (nonatomic, assign) NSTimeInterval timeInterval;

///
@property (nonatomic, strong) MRKDeviceModel *bindModel;
@property (nonatomic, assign) DEVICE_CONNECT_STATUS DEVICE_CONNECT_STATUS;
@end

@implementation AbilityTestWebVC

- (MRKBuildDeviceListView *)deviceListView {
    if(!_deviceListView) {
        _deviceListView = [[MRKBuildDeviceListView alloc] initWithFrame:self.view.bounds];
        @weakify(self);
        _deviceListView.selectDeviceBlock = ^(MRKDeviceModel * _Nonnull model) {
            @strongify(self);
            [self connectDeviceWithModel:model];
        };
    }
    return _deviceListView;
}

- (MRKSportDataCache *)sportCache {
    if (!_sportCache) {
        _sportCache = [[MRKSportDataCache alloc] initWithType:MrkSportType_ABILITY courseID:@""];
    }
    return _sportCache;
}

- (TreamillStatusManager *)treamillStatus{
    if (!_treamillStatus) {
        _treamillStatus = [[TreamillStatusManager alloc] init];
    }
    return _treamillStatus;
}

- (MRKAbilityConnectAlert *)connectAlert{
    if (!_connectAlert) {
        MRKAbilityConnectAlert *alert = [MRKAbilityConnectAlert viewWithTypeId:self.equipmentTypeId];
        @weakify(self);
        alert.deviceConnectBlock = ^(MRKAbilityConnectAlert *_Nonnull alert) {
            @strongify(self);
            [self connectDeviceWasTappedForControlLayer];
        };
        alert.quitTestBlock = ^(MRKAbilityConnectAlert * _Nonnull alert) {
            @strongify(self);
            ///返回到介绍页面
            for (UIViewController *controller in self.navigationController.viewControllers) {
                if ([controller isKindOfClass:[AbilityIntroduceVC class]]) {
                    AbilityIntroduceVC *A =(AbilityIntroduceVC *)controller;
                    [self.navigationController popToViewController:A animated:YES];
                }
            }
        };
        _connectAlert = alert;
    }
    return _connectAlert;
}

- (void)connectDeviceWithModel:(MRKDeviceModel *)model{
    [NewConnectManager showConnectingLoading:self.view];
    
    NewConnectManager *cm = [NewConnectManager new];
    cm.connectMode = ManualDeviceConnectMode;
    @weakify(self);
    cm.connectStatusBlock = ^(id data) {
        @strongify(self);
        [NewConnectManager dismssConnectingLoading:self.view];
    };
    [cm connectDeviceModel:model];
}

/// 点击连接设备
- (void)connectDeviceWasTappedForControlLayer{
    ///已连接过设备
    if (self.bindModel != nil) {
        ///直接连接
        [self connectDeviceWithModel:self.bindModel];
        return;
    }
    
    NSInteger lists = self.pageModel.deviceBindArr.count;
    if (lists == 0) {
        ///无绑定设备进搜索  ///进入设备列表
        MyDeviceListViewController *vc = [MyDeviceListViewController new];
        vc.productID = self.equipmentTypeId;
        vc.connectAutoBack = YES;
        [self.navigationController pushViewController:vc animated:YES];
        return;
    }
    
    if ( lists == 1 ) {
        ///绑定设备只有一个直接连/已经连接过设备
        MRKDeviceModel *deviceModel = [self.pageModel.deviceBindArr firstObject];
        [self connectDeviceWithModel:deviceModel];
        return;
    }
    
    ///展开列表
    self.deviceListView.buildDeviceArr = self.pageModel.deviceBindArr;
    [self.deviceListView showIn:self.view];
}

- (MRKAbilityTestPageModel *)pageModel{
    if (!_pageModel) {
        _pageModel = [[MRKAbilityTestPageModel alloc] init];
    }
    return _pageModel;
}

- (AbilityTestTimeManager *)timeManager{
    if (!_timeManager) {
        _timeManager = [[AbilityTestTimeManager alloc] init];
    }
    return _timeManager;
}

- (MRKAbilityPlayerView *)playerView {
    if (!_playerView) {
        _playerView = [[MRKAbilityPlayerView alloc] init];
    }
    return _playerView;
}

- (MRKAbilityTestProgressView *)progressView {
    if (!_progressView) {
        _progressView = [[MRKAbilityTestProgressView alloc] init];
        @weakify(self);
        _progressView.closeBlock = ^{
            [self_weak_ disMissPage];
        };
    }
    return _progressView;
}

- (BOOL)shouldAutorotate {
    return YES;
}

- (UIInterfaceOrientationMask)supportedInterfaceOrientations {
    return UIInterfaceOrientationMaskLandscape;
}

- (UIInterfaceOrientation)preferredInterfaceOrientationForPresentation {
    return UIInterfaceOrientationLandscapeRight;
}

- (BOOL)prefersStatusBarHidden{
    return YES;
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    ///关闭手势
    self.fd_interactivePopDisabled = YES;
    ///强制横屏
    [self forceOrientationLandscape];
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    ///开启手势
    self.fd_interactivePopDisabled = NO;
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    self.viewDidAppeared = YES;
    
    dispatch_async(dispatch_get_main_queue(), ^{
        [UIApplication sharedApplication].idleTimerDisabled = YES;
    });
}

- (void)viewDidDisappear:(BOOL)animated{
    [super viewDidDisappear:animated];
    self.viewDidAppeared = NO;
    
    dispatch_async(dispatch_get_main_queue(), ^{
        [UIApplication sharedApplication].idleTimerDisabled = NO;
    });
}


- (void)dealloc{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
    
    [_dataManager clearAll];
    
    [SpeechSynthesizerManager.shared stopSpeaking];
    
    [[MeritWebSocketUtility instance] MRWebSocketClose];
    
    [_dispose dispose];
}

- (void)viewDidLoad {
    self.tracePageId = @"page_ability_dynamic_web_evaluate";
    self.tracePara = @{@"assess_id": self.assessId?:@"",
                       @"product_id": self.equipmentTypeId?:@"",
                       @"device_id": self.deviceRelId?:@""};
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    self.view.backgroundColor = UIColorHex(#05182C);
    [self.mrkContentView removeFromSuperview];
    self.mrkContentView = nil;
    
    self.abilityTestStatus = ABILITY_STATUS_NORMAL;
    
    @weakify(self);
    //rac_addObserverForName:监听通知
    [[[[NSNotificationCenter defaultCenter] rac_addObserverForName:UIApplicationDidEnterBackgroundNotification object:nil]
      takeUntil:self.rac_willDeallocSignal]
     subscribeNext:^(id x) {
        @strongify(self);
        if (self.abilityTestStatus == ABILITY_STATUS_TESTING ||
            self.abilityTestStatus == ABILITY_STATUS_PAUSE){
            [FeedbackGeneratorUtil playSoundWithTrainingReminder:@"Sound_001"];
        }
    }];
    ///加载布局
    [self layoutSubviewUI];
    
//    ///监听设备连接状态
//    [RACObserve([BluetoothManager sharedInstance], connectDic) subscribeNext:^(NSDictionary *x) {
//        @strongify(self);
//        [self updateConnectStatus];
//    }];
    
    
    ///初始化状态
    BOOL connectDevice = self.isConnectMachine;
    NSLog(@"控制器 connectDevice ===  %@",connectDevice? @" Yes":@"no");
    self.DEVICE_CONNECT_STATUS = DeviceDisconnect;
    if ( connectDevice ) {
        BluetoothModel *currentDeviceModel = [BlueDataStorageManager connectBMFromProductID:self.equipmentTypeId];
        self.bindModel = currentDeviceModel.equipmentModel;
        self.DEVICE_CONNECT_STATUS = DeviceConnected;
        NSLog(@"控制器 connectDevice connectDevice ===  %@", self.bindModel.modelDescription);
    }
    
//    [[[[[MRKConnectStatusManager sharedInstance] connectStatusWithProductID:self.equipmentTypeId] distinctUntilChanged] takeUntil:[self rac_willDeallocSignal]] subscribeNext:^(id x) {
//        @strongify(self);
//        NSLog(@"connectStatusWithProductID === %@",x);
//        self.DEVICE_CONNECT_STATUS = [x intValue];
//
//        if ([x intValue] == DeviceConnected){
//            BluetoothModel *currentDeviceModel = [BlueDataStorageManager connectBMFromProductID:self.equipmentTypeId];
//            self.bindModel = currentDeviceModel.equipmentModel;
//            NSLog(@"控制器 connectDevice connectStatusWithProductID ===  %@", self.bindModel.modelDescription);
//
//            ///蓝牙数据获取 [让currentStatus刷新]
//            [self.dataManager startGetData];
//        }
//
//        [self updateConnectStatus];
//    }];
    
    ///跑步机状态
    [[[RACObserve(self.treamillStatus, currentStatus) ignore:NULL] distinctUntilChanged] subscribeNext:^(id  _Nullable x) {
        @strongify(self);
        NSLog(@"self.treamillStatus.currentStatus === %@",x);
        self.currentStatus = x;
        
        [self treamillStatusControl];
    }];
    
    //监听页面显示状态
    [[RACObserve(self, viewDidAppeared) distinctUntilChanged] subscribeNext:^(NSNumber * x) {
        @strongify(self);
        self.playerView.controllerIsAppeared = x.boolValue;
        if (x.boolValue) {
            [self updateConnectStatus];
        }
    }];
    
    ///蓝牙启动
    self.dataManager.delegate = self;
    [self.dataManager startGetData];
    
    ///时间管理类
    self.timeManager.delegate = self;
    
    ///启动socket []
    [self scoketConfig];
    
    ///心率数据定时器
    [self heartRateTimer];
    
    ///获取数据
    if (self.abilityTestStatus == ABILITY_STATUS_NORMAL) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [MBProgressHUD showLodingWithMessage:@"" view:self.view];
        });
        [self configSignal];
    }
}


- (void)updateConnectStatus{
    ///页面没显示屏蔽交互
    if (!self.viewDidAppeared) {
        NSLog(@"updateConnectStatus ===== viewDidAppeared");
        return;
    }
    
    ///退出弹窗弹出中, 屏蔽掉监听
    if (!self.pageModel.hasRequetDeviceBindArrDone && self.bindModel == nil) {
        NSLog(@"updateConnectStatus ===== hasRequetDeviceBindArrDone && self.bindModel == nil");
        return;
    }
    
    ///退出弹窗弹出中, 屏蔽掉监听
    if (self.isShowQuitAlert) {
        NSLog(@"updateConnectStatus ===== isShowQuitAlert");
        return;
    }
    
    ///其他过程中屏蔽交互
    if (self.abilityTestStatus != ABILITY_STATUS_START &&
        self.abilityTestStatus != ABILITY_STATUS_TESTING &&
        self.abilityTestStatus != ABILITY_STATUS_PAUSE  ) {
        NSLog(@"updateConnectStatus ===== 其他过程中屏蔽交互");
        return;
    }

//    ///设备重连中...
//    NSDictionary *contentingDevice = [[BlueDataStorageManager sharedInstance] connectingDictionary];
//    if ([contentingDevice.allKeys containsObject:self.equipmentTypeId]) {
//        return;
//    }
    
    NSLog(@"updateConnectStatus ===== 其他过程中屏蔽交互");
    
    ///设备未连接
    if ( self.DEVICE_CONNECT_STATUS  == DeviceDisconnect ) {
        ///测试中暂停
        if (self.abilityTestStatus == ABILITY_STATUS_TESTING){
            [self devicePause];
        }
        
        ///弹窗连接设备
        dispatch_async(dispatch_get_main_queue(), ^{
            [self.connectAlert showInView:self.view];
        });
        return;
    }
    
    
    if ( self.DEVICE_CONNECT_STATUS  == DeviceConnected ) {
        ///弹窗消失
        dispatch_async(dispatch_get_main_queue(), ^{
            [self.connectAlert tappedCancel];
        });
    }
    
    
    ///为了判断进来没连设备后
    if (self.pageModel.eqModel == nil) {
        
        ///重新获取设备id
        @weakify(self);
        [self.pageModel getEquipmentInfo:^(NSString * _Nonnull code) {
            @strongify(self);
            NSLog(@"getEquipmentInfo ==== %@", code);
            ///刷新数据获
            EquipmentDetialModel *model = self.pageModel.eqModel;
            self.eqModel = model;
            self.dataManager.eqModel = model;
            self.webView.isElectromagneticControl = model.isElectromagneticControl;
            [self.webView reloadEquipmentData];
            
            [self startAbilityTest];
        }];
        return;
    }
    
    
    ///
    [self startAbilityTest];
}




- (void)startAbilityTest {
    
    NSString *typeId = self.equipmentTypeId;
    if (typeId.intValue == TreadmillEquipment) {
        
        NSLog(@"self.currentStatus ====== %d", self.currentStatus.intValue);
        
        ///设置启动
        switch ([self.currentStatus intValue]) {
            case DevicePauseStatus:{ ///暂停中
                ///跑步机恢复运动
                [self.treamillStatus treamillGoon];
            } break;
                
            case DeviceRuningStatus:{  ///运行中
                ///开始状态启动设备
                if (self.abilityTestStatus == ABILITY_STATUS_START){
                    [self deviceStart];
                }
                
                ///暂停状态恢复启动
                if (self.abilityTestStatus == ABILITY_STATUS_PAUSE){
                    [self deviceResume];
                }
            }break;
                
            case DevicelSlowDownStatus:  ///减速中
            case DeviceCutDownStatus: {   ///启动中
                
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    [self startAbilityTest];
                });
                
            }break;
                
            case DeviceStandbyStatus:
            case DeviceX1StandbyStatus:{ ///待机中
                /// 启动跑步机
                [self.treamillStatus treamillStart];
            }break;
                
            default:{
                
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    [self startAbilityTest];
                });
            } break;
        }
    }
    
    
    if (typeId.intValue == BoatEquipment ||
        typeId.intValue == BicycleEquipment ||
        typeId.intValue == EllipticalEquipment) {
        ///划船机,单车,椭圆机
        
        ///开始状态启动设备
        if (self.abilityTestStatus == ABILITY_STATUS_START){
            [self deviceStart];
        }
        
        ///暂停状态恢复启动
        if (self.abilityTestStatus == ABILITY_STATUS_PAUSE){
            [self deviceResume];
        }
    }
}

- (void)treamillStatusControl{
    
    ///结束过程中不处理交互
    if (self.abilityTestStatus != ABILITY_STATUS_START &&
        self.abilityTestStatus != ABILITY_STATUS_TESTING &&
        self.abilityTestStatus != ABILITY_STATUS_PAUSE  ) {
        return;
    }
    
    switch ([self.currentStatus intValue]) {
        case DevicePauseStatus: {
            ///暂停
            ///用户主动设备飞梭暂停
            ///用户主动点击暂停按钮
            if (self.abilityTestStatus == ABILITY_STATUS_TESTING){
                ///跑步机速度清零
                ///发送web数据
                Abi_AssessUnitsItem *unit = self.timeManager.unitLinkItem;
                NSDictionary *parm = @{
                    @"frequency" : @"0",                      ///速度
                    @"proposalFrequency" : unit.minNum?:@"",  ///建议速度
                    @"gear" : @"0",                           ///阻力
                    @"proposalGear" : unit.adviseNum?:@"",    ///建议阻力
                };
                [self.webView deviceData:parm];
            }
        } break;
        case DeviceRuningStatus:{
            ///运行中
            ///
            ///开始运动启动跑步机
            ///开始状态启动设备
            if (self.abilityTestStatus == ABILITY_STATUS_START){
                [self deviceStart];
            }
            
            ///暂停状态恢复启动
            if (self.abilityTestStatus == ABILITY_STATUS_PAUSE){
                [self deviceResume];
            }
        }break;
        case DevicelSlowDownStatus:{
            ///减速中
            ///
        }break;
        case DeviceCutDownStatus: {
            ///启动中
            ///
        }break;
        case DeviceStandbyStatus: case DeviceX1StandbyStatus: {
            ///待机中
            ///
            ///设备长按飞梭主动结束运动
            if (self.abilityTestStatus == ABILITY_STATUS_TESTING){
                ///跑步机速度清零发送web数据
                Abi_AssessUnitsItem *unit = self.timeManager.unitLinkItem;
                NSDictionary *parm = @{
                    @"frequency":@"0",                      ///速度
                    @"proposalFrequency":unit.minNum?:@"",  ///建议速度
                    @"gear":@"0",                           ///阻力
                    @"proposalGear":unit.adviseNum?:@"",    ///建议阻力
                };
                [self.webView deviceData:parm];
            }
        }break;
        default: break;
    }
}



- (void)configSignal {
    @weakify(self);
    self.pageModel.equipmentTypeId = self.equipmentTypeId;
    self.pageModel.assessId = self.assessId;
    [self.pageModel.updateTestSignal subscribeNext:^(id x) {
        @strongify(self);
        [MBProgressHUD hideHUDForView:self.view];
        NSLog(@"self.pageModel.updateTestSignal========%@",x);
        
        self.eqModel = self.pageModel.eqModel;
        self.dataManager.eqModel = self.eqModel;
        [self.dataManager startGetData];
        
        ///加载进度条数据
        MRKAbilityAssessModel *model = self.pageModel.assessModel;
        [self.progressView initProgressViewData:model];
        
        ///测评时间处理类
        self.timeManager.sourceArr = self.pageModel.sourceArr;
        self.timeManager.startTestTime = self.pageModel.startTestTime;
        self.timeManager.endTestTime = self.pageModel.endTestTime;
        self.timeManager.finishTestTime = self.pageModel.finishTestTime;
        
        ///webview设置测评小节数据
        self.webView.catalogues = [self.pageModel.assessModel.catalogues modelToJSONObject];
        [self.webView initSourceData:self.pageModel.sourceArr];
        
        ///加载页面
        [self reloadPageUI];
    }];
    
    [self.pageModel refreshDataSource];
}

- (void)reloadPageUI {
    if (self.abilityTestStatus == ABILITY_STATUS_NORMAL){
        ///播放视频
        self.playerView.playCellDidAppear = YES;
        @weakify(self);
        [self.playerView startToPlayVideo:self.pageModel.firstPartVideoUrl
                                   onView:self.playerBackView
                            firstRendered:^{
            @strongify(self);
            self.abilityTestStatus = ABILITY_STATUS_FIRSTPLAYER;
        } andComplete:^{
            NSLog(@"播放完成");
            @strongify(self);
            [self  abilityTestStartBackPlayer];
        }];
    }
}

#pragma mark ------------ heartRateTimer

- (void)heartRateTimer {
    self.testTime = 0;
    
    @weakify(self);
    RACSignal *deallocSignal = [self rac_willDeallocSignal];
    _dispose = [[[RACSignal interval:1.0 onScheduler:[RACScheduler scheduler]] takeUntil:deallocSignal]
                subscribeNext:^(NSDate * _Nullable x) {
        @strongify(self);
        dispatch_async(dispatch_get_main_queue(), ^{
            
            if (self.abilityTestStatus == ABILITY_STATUS_FIRSTPLAYER ||
                self.abilityTestStatus == ABILITY_STATUS_TESTING ||
                self.abilityTestStatus == ABILITY_STATUS_ENDPLAYER ) {
                
                ///视频中止不执行测评
                if (self.abilityTestStatus == ABILITY_STATUS_FIRSTPLAYER ||
                    self.abilityTestStatus == ABILITY_STATUS_ENDPLAYER ){
                    if (self.playerPause) return;
                }
                
                ///返回窗弹出来不执行测评
                if ( self.abilityTestStatus == ABILITY_STATUS_TESTING ){
                    if (self.isShowQuitAlert) return;
                }
                
                ///测评时间每秒自增
                self.testTime ++;
                
                ///更新本地时间
                [self reloadProgressTime:self.testTime];
                
                ///更新进度条
                [self.progressView setProgresTime:self.testTime];
                
                ///测评时间管理类
                self.timeManager.testingTime = self.testTime;
                
                ///提交设备数据
                if ( self.abilityTestStatus == ABILITY_STATUS_TESTING ){
                    [self uploadDeviceData];
                }
            }
        });
    }];
}


- (void)reloadProgressTime:(NSTimeInterval)time {
    NSString *timeStr = [MRKToolKit MSTimeStrFromSecond:(int)time];
    self.progressView.testTime = [NSString stringWithFormat:@"%@/%@", timeStr, self.pageModel.testTotalTimeStr];
}

/// <#Description#>
- (void)uploadDeviceData {
    
    ///判断跑步机状态在不在上传数据范围内
    NSString *typeId = self.equipmentTypeId;
    if (typeId.intValue == TreadmillEquipment) {
        int currentStatus = [self.currentStatus intValue];
        if (currentStatus != DeviceRuningStatus &&
            currentStatus != DevicelSlowDownStatus &&
            currentStatus != DeviceCutDownStatus ) {
            return;
        }
    }
    
    NSLog(@"AbilityTestWebVC updateUI ==== %ld", self.testTime);
    BaseEquipDataModel *model = self.dataManager.tyModel;
    //    ///判断设备信息有无更新
    //    if (self.totalTimeSecond.intValue == model.totalTimeSecond.intValue){
    //        return;
    //    }
    //    self.totalTimeSecond = model.totalTimeSecond;
    
    ///发送socket数据
    NSDictionary *data = ItemDataMake(model, self.deviceRelId?:@"");
    NSMutableDictionary *dic = [NSMutableDictionary dictionaryWithDictionary:data];
    // 获得本地时间
    NSDate *date = [NSDate dateWithTimeIntervalSinceNow:self.timeInterval];
    NSTimeInterval time = [date timeIntervalSince1970] * 1000;
    NSString *timeString = [NSString stringWithFormat:@"%.0f", time];
    [dic setValue:timeString forKey:@"time"];
    MeritSocketSendDataRqeuest(SOCKET_BIZTYPE_DEVICE, self.deviceRelId, timeString, dic);
    
    // 缓存socket数据
    NSDictionary *parms = @{
        @"bizType":@(SOCKET_BIZTYPE_DEVICE),
        @"bizId":self.deviceRelId?:@"",
        @"requestId":timeString?:@"",
        @"data":dic?:@""
    };
    NSString *d = [parms modelToJSONString];
    [self.sportCache addSportData:d requestID:timeString];
    
    if ( self.abilityTestStatus == ABILITY_STATUS_TESTING ) {
        ///发送web数据
        Abi_AssessUnitsItem *unit = self.timeManager.unitLinkItem;
        NSDictionary *parm = @{};
        switch (self.equipmentTypeId.intValue) {
            case BicycleEquipment: case BoatEquipment: case EllipticalEquipment:{///动感单车 | 划船机 | 椭圆机
                parm = @{
                    @"frequency":model.spm?:@"0",            ///踏频
                    @"proposalFrequency":unit.minNum?:@"0",  ///建议踏频
                    @"gear":model.drag?:@"0",                ///阻力
                    @"proposalGear":unit.adviseNum?:@"0",    ///建议阻力
                };
            } break;
            case TreadmillEquipment: { ///"跑步机
                parm = @{
                    @"frequency":model.speed?:@"0",          ///速度
                    @"proposalFrequency":unit.minNum?:@"0",  ///建议速度
                    @"gear":model.drag?:@"0",                ///阻力
                    @"proposalGear":unit.adviseNum?:@"0",    ///建议阻力
                };
            } break;
            default:  break;
        }
        [self.webView deviceData:parm];
    }
}




#pragma mark ------------ layoutSubviewUI

- (void)layoutSubviewUI {
    
    ///播放器承载view
    [self.view addSubview:self.playerBackView];
    [self.playerBackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsMake(0, 0, 0, 0));
    }];
    
    ///webview
    self.webView.alpha = 0.0;
    self.webView.hidden = YES;
    [self.view addSubview:self.webView];
    [self.webView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view.mas_top);
        make.left.equalTo(self.view.mas_left).offset(kScreenPadding);
        make.right.equalTo(self.view.mas_right).offset(-kScreenPadding);
        make.bottom.equalTo(self.view.mas_bottom);
    }];
    
    ///进度条
    [self.view addSubview:self.progressView];
    [self.progressView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view.mas_top);
        make.left.equalTo(self.view.mas_left);
        make.right.equalTo(self.view.mas_right);
        make.height.mas_equalTo(70);
    }];
}

- (void)viewDidLayoutSubviews{
    [super viewDidLayoutSubviews];
}


#pragma mark ------------ scoketConfig

- (void)scoketConfig{
    ///scoket 启动
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(mrkWebSocketDidOpen)
                                                 name:MRWebSocketDidOpenNote
                                               object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(mrkWebSocketDidReceiveMsg:)
                                                 name:MRWebSocketdidReceiveMessageNote
                                               object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(mrkWebSocketReconnect)
                                                 name:MRWebSocketReconnectSuccessNote
                                               object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(mrkWebSocketReconnectError:)
                                                 name:MRWebSocketReconnectErrorNote
                                               object:nil];
    
    [MeritWebSocketUtility instance].idd = self.assessId;
    [[MeritWebSocketUtility instance] MRWebSocketOpen];
    
    // 开始记录运动数据缓存
    [self.sportCache start];
    self.sportCache.sportID = self.assessId;
}

#pragma mark ------------ WebSocke notification

- (void)mrkWebSocketDidOpen{
    
}

- (void)mrkWebSocketReconnect{
    ///重连
    NSDictionary *data = @{
        @"type":@"reconnect",
        @"assessId":self.assessId?:@"",
        @"linkId":self.timeManager.linkid?:@""
    };
    MeritSocketSendData(SOCKET_BIZTYPE_ABILITY, self.assessId?:@"", data);
}

- (void)mrkWebSocketReconnectError:(NSNotification *)notification{
    id count = notification.object;
    NSInteger reconnectCount = [count intValue];
    
    if (reconnectCount == 0){
        [MBProgressHUD showMessage:@"当前网络异常，请检查网络后重试"];
    }
    
    if (reconnectCount >= 3){
        [self alertNetworkAlert];
    }
}

- (void)alertNetworkAlert{
    
    switch (self.abilityTestStatus) {
        case ABILITY_STATUS_NORMAL:{
            
        }  break;
        case ABILITY_STATUS_FIRSTPLAYER:{
            
            ///暂停视频
            [self.playerView pause];
            self.playerPause = YES;
            
            MRKAbilityQuitAlert *alert = [[MRKAbilityQuitAlert alloc] initWithAnimationStyle:MRKActionAlertViewTransitionStyleFade];
            alert.defaultText = @"当前网络不稳定,请检查网络后重试";
            [alert.cancelBtn setTitle:@"刷新重试" forState:UIControlStateNormal];
            [alert.ensureBtn setTitle:@"退出M超燃力环测评" forState:UIControlStateNormal];
            @weakify(self);
            alert.quitBlock = ^(NSInteger index) {
                @strongify(self);
                if(index == 0){
                    ///返回到介绍页面
                    for (UIViewController *controller in self.navigationController.viewControllers) {
                        if ([controller isKindOfClass:[AbilityIntroduceVC class]]) {
                            AbilityIntroduceVC *A =(AbilityIntroduceVC *)controller;
                            [self.navigationController popToViewController:A animated:YES];
                        }
                    }
                }else{
                    ///恢复视频播放
                    [self.playerView resume];
                    self.playerPause = NO;
                    
                    [[MeritWebSocketUtility instance] MRWebSocketReconnect];
                }
            };
            [alert show];
            
        }  break;
        case ABILITY_STATUS_START:
        case ABILITY_STATUS_TESTING:
        case ABILITY_STATUS_PAUSE:
        case ABILITY_STATUS_TESTEND: {
            
            ///暂停视频
            self.isShowQuitAlert = YES;
            
            ///暂停音乐
            dispatch_async(dispatch_get_main_queue(), ^{
                [self.playerView pause];
            });
            
            MRKAbilityQuitAlert *alert = [[MRKAbilityQuitAlert alloc] initWithAnimationStyle:MRKActionAlertViewTransitionStyleFade];
            alert.defaultText = @"当前网络不稳定,请检查网络后重试";
            [alert.cancelBtn setTitle:@"刷新重试" forState:UIControlStateNormal];
            [alert.ensureBtn setTitle:@"退出M超燃力环测评" forState:UIControlStateNormal];
            @weakify(self);
            alert.quitBlock = ^(NSInteger index) {
                @strongify(self);
                if(index == 0){
                    ///返回到介绍页面
                    for (UIViewController *controller in self.navigationController.viewControllers) {
                        if ([controller isKindOfClass:[AbilityIntroduceVC class]]) {
                            AbilityIntroduceVC *A =(AbilityIntroduceVC *)controller;
                            [self.navigationController popToViewController:A animated:YES];
                        }
                    }
                } else {
                    self.isShowQuitAlert = NO;
                    
                    ///重启音乐
                    dispatch_async(dispatch_get_main_queue(), ^{
                        [self.playerView resume];
                    });
                    
                    [self updateConnectStatus];
                    
                    ///恢复视频播放
                    [self.playerView resume];

                    [[MeritWebSocketUtility instance] MRWebSocketReconnect];
                }
            };
            [alert show];
            
        }  break;
        case ABILITY_STATUS_ENDPLAYER:  case ABILITY_STATUS_FINISH: {
            
        }  break;
        default:
            break;
    }
}

- (void)socketConnectResponse:(MeritScoketModel *)response {
    NSString *time = response.data.timestamp;
    NSString *localTime = [MRKTimeManager get13Time];
    NSDate *date = [NSDate dateWithTimeIntervalSince1970:[time doubleValue] / 1000];
    NSDate *date2 = [NSDate dateWithTimeIntervalSince1970:localTime.doubleValue / 1000 ];
    NSTimeInterval seconds = [date timeIntervalSinceDate:date2];
    self.timeInterval = seconds;
}

- (void)mrkWebSocketDidReceiveMsg:(NSNotification *)notification{
    id data = notification.object;
    MeritScoketModel *model = [MeritScoketModel modelWithJSON:data];
    
    /// 更新缓存数据
    if (model.requestId.isNotBlank && ![model.requestId isEqualToString:@"0"]) {
        [self.sportCache updateFlag:model.requestId];
    }
    if ([model.requestId isEqualToString:@"0"]) {
        [self socketConnectResponse:model];
    }
    /// 能力测评
    if (model.bizType != 2) return;
    
    MeritScoketDataModel *modelData = model.data;
    ///语音提示 并且在测评中
    if ( [modelData.guideTip isNotBlank] && self.abilityTestStatus == ABILITY_STATUS_TESTING )
    {
        dispatch_async(dispatch_get_main_queue(), ^{
            self.playerView.volume = 0.5;
        });
        @weakify(self);
        [SpeechSynthesizerManager.shared speak:modelData.guideTip complete:^{
            @strongify(self);
            dispatch_async(dispatch_get_main_queue(), ^{
                self.playerView.volume = 1.0;
            });
        }];
    }
    
    ///跨节容错处理
    if ( self.unitChangeItem )
    {
        Abi_AssessUnitsItem *unitItem = self.unitChangeItem;
        if ([unitItem.linkId isEqualToString:modelData.linkId]){
            self.unitChangeItem = nil;
        } else {
            ///跨节
            NSDictionary *data = @{
                @"type":@"unitChange",
                @"assessId":self.assessId?:@"",
                @"linkId":unitItem.linkId
            };
            MeritSocketSendData(SOCKET_BIZTYPE_ABILITY, self.assessId?:@"", data);
        }
    }
    
    ///webview交互
    if ( self.abilityTestStatus == ABILITY_STATUS_TESTING )
    {
        ///刷新web数据
        [self.webView reloadMeritRate:modelData.meritRate linkId:modelData.linkId];
        
        ///倒计数档位调整
        if (modelData.adjustValue.intValue > 0) {
            [self.webView deviceGear:@(modelData.adjustValue.intValue)];
        }
    }
}




#pragma mark ------------ 蓝牙数据

- (void)updateUI {
    
}

- (void)updateDeviceStatus:(DEVICE_CONNECT_STATUS)status{
    NSLog(@"AbilityTestWebVC updateDeviceStatus === %@", @(status));
    if (status == self.DEVICE_CONNECT_STATUS) return;
    self.DEVICE_CONNECT_STATUS = status;
    
    if (status == DeviceConnected){
        BluetoothModel *currentDeviceModel = [BlueDataStorageManager connectBMFromProductID:self.equipmentTypeId];
        self.bindModel = currentDeviceModel.equipmentModel;
        NSLog(@"控制器 connectDevice connectStatusWithProductID ===  %@", self.bindModel.modelDescription);
        
        ///蓝牙数据获取 [让currentStatus刷新]
        [self.dataManager startGetData];
    }
    
    [self updateConnectStatus];
}




#pragma mark ------------ 测评中设备操作

/*
 设备启动
 */
- (void)deviceStart{
    self.abilityTestStatus = ABILITY_STATUS_TESTING;
    
    ///蓝牙数据获取
    [self.dataManager startGetData];
    
    ///发送开始测评socket
    NSDictionary *data = @{
        @"type":@"start",
        @"assessId":self.assessId?:@""
    };
    MeritSocketSendData(SOCKET_BIZTYPE_ABILITY, self.assessId?:@"", data);
    
    ///设备调整
    Abi_AssessUnitsItem *unitItem = (Abi_AssessUnitsItem *)[self.pageModel.sourceArr firstObject];
    [self sendDeviceOrder:unitItem];
    
    ///添加背景音乐
    dispatch_async(dispatch_get_main_queue(), ^{
        self.playerView.playCellDidAppear = YES;
        [self.playerView startToPlayAudio:AbilityTestAudio];
    });
}

/*
 设备暂停
 */
- (void)devicePause{
    self.abilityTestStatus = ABILITY_STATUS_PAUSE;
    
    ///音乐暂停
    dispatch_async(dispatch_get_main_queue(), ^{
        [self.playerView pause];
    });
}

/*
 设备恢复
 */
- (void)deviceResume{
    self.abilityTestStatus = ABILITY_STATUS_TESTING;
    
    ///蓝牙数据获取
    [self.dataManager startGetData];
    
    ///音乐恢复
    dispatch_async(dispatch_get_main_queue(), ^{
        [self.playerView resume];
    });
}




#pragma mark ------------ AbilityTestTimeManagerDelegate

- (void)abilityTestStartBackPlayer{
    
}

/**
 消息类型start(开始动态测评)
 */
- (void)abilityTestStart:(nullable id)obj{
    self.abilityTestStatus = ABILITY_STATUS_START;
    
    ///2. 切换到webview, 并初始数据
    [MBProgressHUD showLodingWithMessage:@"" view:self.view];
    self.webView.hidden = NO;
    self.webView.equipmentTypeId = self.equipmentTypeId;
    self.webView.isElectromagneticControl = self.eqModel.isElectromagneticControl;
    [self.webView loadWebUrl:MRKAppH5LinkCombine(MRKLinkAbilityTestUrl)];
    
    @weakify(self);
    [UIView animateWithDuration:0.5 animations:^{
        self_weak_.webView.alpha = 1.0;
    }];
    self.webView.webViewDidFinishLoadBlock = ^{
        @strongify(self);
        [MBProgressHUD hideHUDForView:self.view];
        
        dispatch_time_t delayTime = dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC));
        dispatch_after(delayTime, dispatch_get_main_queue(), ^{
            Abi_AssessUnitsItem *unit = [self.pageModel.sourceArr firstObject];
            [self.webView deviceLinkId:unit.linkId andSource:self.pageModel.sourceArr];
        });
    };
    
    ///关闭播放器
    self.playerView.playCellDidAppear = NO;
    [self.playerView closePlayer];
    
    ///3 设备连接弹窗
    [self updateConnectStatus];
}

/**
 tips(提前20秒触发预提示),
 */
- (void)abilityTestTips:(id)obj{
    NSDictionary *data = @{
        @"type":@"tips",
        @"assessId":self.assessId?:@""
    };
    MeritSocketSendData(SOCKET_BIZTYPE_ABILITY, self.assessId?:@"", data);
}

/**
 unitChange(切换小结),
 */
- (void)abilityTestUnitChange:(id)obj{
    Abi_AssessUnitsItem *unitItem = (Abi_AssessUnitsItem *)obj;
    self.unitChangeItem = unitItem;
    NSDictionary *data = @{
        @"type":@"unitChange",
        @"assessId":self.assessId?:@"",
        @"linkId":unitItem.linkId
    };
    MeritSocketSendData(SOCKET_BIZTYPE_ABILITY, self.assessId?:@"", data);
    
    ///发送切换指令
    [self sendDeviceOrder:unitItem];
}


- (void)sendDeviceOrder:(Abi_AssessUnitsItem *)unitItem {
    if (!self.eqModel.isElectromagneticControl) return;
    
    ///发设备指令
    if (self.isConnectMachine){
        
        NSString *typeId = self.equipmentTypeId;
        if (typeId.intValue == TreadmillEquipment) {
            
            NSDictionary *parm = @{};
            ///只调整坡度
            if (unitItem.adviseNum.intValue > 0 && unitItem.minNum.intValue == 0) {
                ///获取当前速度
                BaseEquipDataModel *model = self.dataManager.tyModel;
                NSString *speedStr = [NSString stringWithFormat:@"%.1f", model.speed.floatValue];
                double speed = speedStr.doubleValue *10;
                NSNumber *speedNum = [NSNumber numberWithInt:(int)speed];
                parm = @{Speed:speedNum,
                         Slope:unitItem.adviseNum,
                         BlueDeviceType:typeId,
                         @"status":self.currentStatus};
            }
            
            ///只调整速度
            if (unitItem.adviseNum.intValue == 0 && unitItem.minNum.intValue > 0) {
                ///获取当前坡度
                BaseEquipDataModel *model = self.dataManager.tyModel;
                int speed = unitItem.minNum.doubleValue * 10;
                parm = @{Speed:@(speed),
                         Slope:model.gradient?:@0,
                         BlueDeviceType:typeId,
                         @"status":self.currentStatus};
            }
            
            //速度和坡度一起调整
            if (unitItem.adviseNum.intValue > 0 && unitItem.minNum.intValue > 0) {
                int speed = unitItem.minNum.doubleValue * 10;
                parm = @{Speed:@(speed),
                         Slope:unitItem.adviseNum,
                         BlueDeviceType:typeId,
                         @"status":self.currentStatus};
            }
            
            [[NSNotificationCenter defaultCenter] postNotificationName:SetResistanceSlopeSpeedNotification object:parm];
        }
        
        if (typeId.intValue == BoatEquipment ||
            typeId.intValue == BicycleEquipment ||
            typeId.intValue == EllipticalEquipment) {
            
            if (unitItem.adviseNum.intValue == 0) {return;}
            
            NSDictionary *parm;
            //划船机,单车,椭圆机
            if (unitItem.isChangeSlope) { // 需要调节坡度
                parm = @{Resistance:unitItem.adviseNum?:@1,
                                       Slope:unitItem.slopeNum?:@0,
                                       BlueDeviceType:typeId};
            } else { // 不调节坡度的时候默认设备自己的坡度
                parm = @{Resistance:unitItem.adviseNum?:@1,
                                       Slope:self.dataManager.tyModel.gradient?:@0,
                                       BlueDeviceType:typeId};
            }
            [[NSNotificationCenter defaultCenter] postNotificationName:SetResistanceSlopeSpeedNotification object:parm];
        }
    }
}





/**
 end(结束动态测评)
 */
- (void)abilityTestEnd:(id)obj{
    self.abilityTestStatus = ABILITY_STATUS_TESTEND;
    ///设备发结束指令
    [self.dataManager endGetData];
    
    ///禁掉语音
    [SpeechSynthesizerManager.shared stopSpeaking];
    
    ///关闭webview, 启动player
    [UIView animateWithDuration:0.3 animations:^{
        self.webView.alpha = 0.0;
    } completion:^(BOOL finished) {
        self.webView.hidden = YES;
        self.webView = nil;
    }];
    
    ///播放视频
    self.playerView.playCellDidAppear = YES;
    
    @weakify(self);
    [self.playerView startToPlayVideo:self.pageModel.lastPartVideoUrl
                               onView:self.playerBackView
                        firstRendered:^{
        @strongify(self);
        self.abilityTestStatus = ABILITY_STATUS_ENDPLAYER;
    } andComplete:^{
        NSLog(@"播放完成");
    }];
    
    [self.sportCache uploadCache:^{
        @strongify(self);
        [self endAbilityTest];
    }];
}
- (void)endAbilityTest {
    ///1. 结束测评socket
    NSDictionary *data = @{
        @"type":@"end",
        @"assessId":self.assessId?:@"",
    };
    MeritSocketSendData(SOCKET_BIZTYPE_ABILITY, self.assessId?:@"", data);
}

/**
 Finish(结束动态测评)
 */
- (void)abilityTestFinish:(nullable id)obj{
    self.abilityTestStatus = ABILITY_STATUS_FINISH;
    
    ///销毁播放器
    [self.playerView destroyPlayer];
    self.playerView = nil;
    
    ///设备发结束指令
    [self.dataManager endGetData];
    
    ///销毁
    [[NSNotificationCenter defaultCenter] removeObserver:self];
    [self.dataManager clearAll];
    [_dispose dispose];
    [[MeritWebSocketUtility instance] MRWebSocketClose];
    
    ///评估页面
    dispatch_async(dispatch_get_main_queue(), ^{
        AbilityEvaluateVC *vc = [[AbilityEvaluateVC alloc] init];
        vc.assessId = self.assessId;
        [self.navigationController pushViewController:vc animated:YES];
    });
    
    /// 结束后延迟3s发送更新小彩屏首页的通知
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [[NSNotificationCenter defaultCenter] postNotificationName:@"kUpdateHomeSportDataNotification" object:@{@"productId": self.equipmentTypeId}];
    });
}



#pragma mark ---------WebSocke notification -----------

/// 是否链接设备
- (BOOL)isConnectMachine{
    NSString *equipTypeId = self.equipmentTypeId;
    if ([BluetoothManager isConnectEquipmentType:equipTypeId]) {
        return YES;
    }
    return NO;
}


- (UIView *)playerBackView{
    if (!_playerBackView){
        _playerBackView = [[UIView alloc] init];
    }
    return _playerBackView;
}

- (MRKAbilityTestWebView *)webView{
    if (!_webView){
        _webView = [[MRKAbilityTestWebView alloc] init];
    }
    return _webView;
}


- (void)disMissPage{
    
    switch (self.abilityTestStatus) {
        case ABILITY_STATUS_NORMAL:{
            
            ///返回到介绍页面
            for (UIViewController *controller in self.navigationController.viewControllers) {
                if ([controller isKindOfClass:[AbilityIntroduceVC class]]) {
                    AbilityIntroduceVC *A =(AbilityIntroduceVC *)controller;
                    [self.navigationController popToViewController:A animated:YES];
                }
            }
            
        }  break;
        case ABILITY_STATUS_FIRSTPLAYER:{
            
            ///暂停视频
            [self.playerView pause];
            self.playerPause = YES;
            
            
            MRKAbilityQuitAlert *alert = [[MRKAbilityQuitAlert alloc] initWithAnimationStyle:MRKActionAlertViewTransitionStyleFade];
            alert.defaultText = @"本测评包含多维度测评环节，完成全部环节才能生成完整的测评报告，是否继续退出?";
            @weakify(self);
            alert.quitBlock = ^(NSInteger index) {
                @strongify(self);
                if(index == 0){
                    ///返回到介绍页面
                    for (UIViewController *controller in self.navigationController.viewControllers) {
                        if ([controller isKindOfClass:[AbilityIntroduceVC class]]) {
                            AbilityIntroduceVC *A =(AbilityIntroduceVC *)controller;
                            [self.navigationController popToViewController:A animated:YES];
                        }
                    }
                }else{
                    ///恢复视频播放
                    [self.playerView resume];
                    self.playerPause = NO;
                }
            };
            [alert show];
            
            
        }  break;
        case ABILITY_STATUS_START:  case ABILITY_STATUS_TESTING:  case ABILITY_STATUS_PAUSE:  case ABILITY_STATUS_TESTEND: {
            
            ///暂停视频
            self.isShowQuitAlert = YES;
            
            ///暂停音乐
            dispatch_async(dispatch_get_main_queue(), ^{
                [self.playerView pause];
            });
            
            MRKAbilityQuitAlert *alert = [[MRKAbilityQuitAlert alloc] initWithAnimationStyle:MRKActionAlertViewTransitionStyleFade];
            alert.defaultText = @"本测评包含多维度测评环节，完成全部环节才能生成完整的测评报告，是否继续退出?";
            @weakify(self);
            alert.quitBlock = ^(NSInteger index) {
                @strongify(self);
                if(index == 0){
                    ///返回到介绍页面
                    for (UIViewController *controller in self.navigationController.viewControllers) {
                        if ([controller isKindOfClass:[AbilityIntroduceVC class]]) {
                            AbilityIntroduceVC *A =(AbilityIntroduceVC *)controller;
                            [self.navigationController popToViewController:A animated:YES];
                        }
                    }
                }else{
                    self.isShowQuitAlert = NO;
                    ///重启音乐
                    dispatch_async(dispatch_get_main_queue(), ^{
                        [self.playerView resume];
                    });

                    [self updateConnectStatus];
                }
            };
            [alert show];
            
            
        }  break;
        case ABILITY_STATUS_ENDPLAYER:  case ABILITY_STATUS_FINISH: {
            ///暂停视频
            [self.playerView pause];
            self.playerPause = YES;
            
            MRKAbilityQuitAlert *alert = [[MRKAbilityQuitAlert alloc] initWithAnimationStyle:MRKActionAlertViewTransitionStyleFade];
            alert.defaultText = @"运动后进行充分的拉伸更能放松身体，是否继续退出";
            @weakify(self);
            alert.quitBlock = ^(NSInteger index) {
                @strongify(self);
                if(index == 0){
                    [self abilityTestFinish:nil];
                }else{
                    ///恢复视频播放
                    [self.playerView resume];
                    self.playerPause = NO;
                }
            };
            [alert show];
            
        }  break;
        default:
            break;
    }
    
}

- (BlueDataDealManager *)dataManager {
    if(!_dataManager) {
        _dataManager = [[BlueDataDealManager alloc] initWithType:self.equipmentTypeId dataType:0];
        _dataManager.uploadType = SocketUploadType;
        _dataManager.isOpenAutoConnect = YES;
        _dataManager.autoConnectNeedLoading = YES;
        _dataManager.isUItraTraining = YES;
    }
    return _dataManager;
}

/*
 #pragma mark - Navigation
 
 // In a storyboard-based application, you will often want to do a little preparation before navigation
 - (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
 // Get the new view controller using [segue destinationViewController].
 // Pass the selected object to the new view controller.
 }
 */

@end



