//
//  MRKAbilityTestWebView.m
//  Student_IOS
//
//  Created by merit on 2022/11/30.
//

#import "MRKAbilityTestWebView.h"
#import "WKWebViewJavascriptBridge.h"
#import "MRKWebJSHandler.h"

@interface MRKAbilityTestWebView()<UIScrollViewDelegate, MrkWebViewDelegate>
@property (nonatomic, strong) NSString *realTrueUrlString;
@property (nonatomic, strong) MrkWebView *pWebView;
@property (nonatomic, strong) WKWebViewJavascriptBridge *bridge;
@property (nonatomic, strong) NSMutableArray *sourceArr;
@property (nonatomic, copy) NSString *linkId; ///当前小节id
@property (nonatomic, strong) Abi_AssessUnitsItem *linkItem; ///当前小节数据
@end

@implementation MRKAbilityTestWebView

///
- (NSMutableArray *)sourceArr{
    if(!_sourceArr){
        _sourceArr = [NSMutableArray array];
    }
    return _sourceArr;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        self.pWebView = [[MrkWebView alloc] init];
        self.pWebView.backgroundColor = UIColorHex(#05182C);
        [self.pWebView setScalesPageToFit:YES];
        self.pWebView.delegate = self;
        [self addSubview:self.pWebView];
    }
    return self;
}

- (void)layoutSubviews{
    [super layoutSubviews];
    [self.pWebView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsMake(0, 0, 0, 0));
    }];
}

- (void)loadWebUrl:(NSString *)httpUrl{
    NSMutableURLRequest *requset = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:httpUrl]
                                                           cachePolicy:NSURLRequestUseProtocolCachePolicy
                                                       timeoutInterval:20];
    [self.pWebView loadRequest:requset];
    
    [self registerHandler];
}

- (void)registerHandler{
    self.bridge = [WKWebViewJavascriptBridge bridgeForWebView:self.pWebView.realWebView];
    [self.bridge setWebViewDelegate:self];
    [[MRKWebJSHandler shared]registerJSMethodHandler:self.bridge base:self];
}

- (NSDictionary *)callBackParms {
    return @{@"isElectromagneticControl":@(self.isElectromagneticControl?1:0),
             @"catalogues":self.catalogues?:@[],
             @"equipmentId":self.equipmentTypeId?:@""};
}

///小节数据
- (void)initSourceData:(NSArray *)array{
    self.sourceArr = array.mutableCopy;
}



- (void)reloadMeritRate:(NSString *)meritRate linkId:(NSString *)linkid{
    ///查出对应的linkid的meritRate
    NSMutableArray *array = self.sourceArr.mutableCopy;
    [array enumerateObjectsUsingBlock:^(id obj, NSUInteger idx, BOOL *stop) {
        NSLog(@"%ld,%@",idx,[array objectAtIndex:idx]);
        Abi_AssessUnitsItem *unitItem = (Abi_AssessUnitsItem *)obj;
        if ([linkid isEqualToString:unitItem.linkId]){
            unitItem.meritRate = meritRate;
            *stop = YES;
        }
    }];
    self.sourceArr = array;
    [self deviceLinkId:linkid andSource:self.sourceArr];
}


///小节燃脂率数据
- (void)deviceLinkId:(NSString *)linkid andSource:(NSArray *)array{
    NSMutableArray *newArr = array.mutableCopy;
    NSMutableArray *dataArr = [NSMutableArray array];
    for (Abi_AssessUnitsItem *item in newArr) {
        [dataArr addObject:@{
            @"linkName":item.linkName?:@"",
            @"meritRate":item.meritRate?:@"",
            @"linkId":item.linkId?:@"",
        }];
    }
    
    NSDictionary *data = @{
        @"type":@1,
        @"data":dataArr,
        @"linkId":linkid?:@"",
    };
    [self.bridge callHandler:[MRKWebJSHandler operatorStr: MRKWebJSCallBackMethodSourceData] data:data responseCallback:^(id responseData) {
        NSLog(@"sourceData==%@" , responseData);
    }];
}


///设备数据
- (void)deviceData:(NSDictionary *)parm{
    NSDictionary *data = @{
        @"type":@2,
        @"data":parm
    };
    [self.bridge callHandler:[MRKWebJSHandler operatorStr: MRKWebJSCallBackMethodSourceData] data:data responseCallback:^(id responseData) {
        NSLog(@"sourceData==%@" , responseData);
    }];
}


///档位调节
- (void)deviceGear:(NSNumber *)gear{
    NSDictionary *parm = @{
        @"type":@3,
        @"data":gear
    };
    [self.bridge callHandler:[MRKWebJSHandler operatorStr: MRKWebJSCallBackMethodSourceData] data:parm responseCallback:^(id responseData) {
        NSLog(@"sourceData==%@" , responseData);
    }];
}

/**
 刷新设备信息
 {type:5,data: {isElectromagneticControl:number|string,equipmentId:string}}
 */
- (void)reloadEquipmentData{
    NSDictionary *parm = @{
        @"type":@5,
        @"data":@{@"isElectromagneticControl":@(self.isElectromagneticControl?1:0),
                  @"equipmentId":self.equipmentTypeId?:@""}
    };
    [self.bridge callHandler:[MRKWebJSHandler operatorStr: MRKWebJSCallBackMethodSourceData] data:parm responseCallback:^(id responseData) {
        NSLog(@"sourceData==%@" , responseData);
    }];
}


- (void)setWebPagePause:(BOOL)webPagePause{
    _webPagePause = webPagePause;
    NSDictionary *parm = @{
        @"type":@4,
        @"data":@(webPagePause)
    };
    [self.bridge callHandler:[MRKWebJSHandler operatorStr: MRKWebJSCallBackMethodSourceData] data:parm responseCallback:^(id responseData) {
        NSLog(@"sourceData==%@" , responseData);
    }];
}

#pragma mark - WebViewDelegate
- (void)webView:(MrkWebView * _Nonnull)webView didFailLoadWithError:(NSError * _Nonnull)error {
    
}

- (void)webView:(MrkWebView * _Nonnull)webView didLoadWithTitle:(NSString * _Nonnull)title {
    
}

- (BOOL)webView:(MrkWebView * _Nonnull)webView shouldStartLoadWith:(NSURLRequest * _Nonnull)request navigationType:(WKNavigationType)navigationType {
    return YES;
}

- (void)webViewDidFinishLoad:(MrkWebView * _Nonnull)webView {
    if (self.webViewDidFinishLoadBlock){
        self.webViewDidFinishLoadBlock();
    }
}

- (void)webViewDidStartLoad:(MrkWebView * _Nonnull)webView {
    
}

- (void)dealloc {
     NSLog(@"😊😊😊-----dealloc-----😊😊😊%@" , NSStringFromClass([self class]));
}



@end
