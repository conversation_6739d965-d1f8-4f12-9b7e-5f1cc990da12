//
//  MRKAbilityConnectAlert.m
//  Student_IOS
//
//  Created by merit on 2022/12/1.
//

#import "MRKAbilityConnectAlert.h"
#import "POP.h"
#import "UIView+AZGradient.h"


@interface MRKAbilityConnectAlert ()
@property (nonatomic, strong) UIView *contentView;
@property (nonatomic, strong) UILabel *titleLab;
@property (nonatomic, strong) UIButton *devicePartView;
@property (nonatomic, strong) UIButton *connectBtn;
@property (nonatomic, strong) UIButton *quitBtn;
@end

@implementation MRKAbilityConnectAlert

+ (instancetype)viewWithTypeId:(NSString *)equipmentTypeId{
    MRKAbilityConnectAlert *view = [[self alloc] initWithEquipmentTypeId:equipmentTypeId];
    return view;
}

- (instancetype)initWithEquipmentTypeId:(NSString *)idStr{
    self = [super initWithFrame:CGRectZero];
    if (self){
        self.equipmentTypeId = idStr;
        self.backgroundColor = [UIColor colorWithRed:0 green:0 blue:0 alpha:0.7];
        self.userInteractionEnabled = YES;
        self.clipsToBounds = YES;
        
        [self setupContainerSubViews];
        
        @weakify(self);
        [RACObserve([BlueDataStorageManager sharedInstance], connectDictionary) subscribeNext:^(NSDictionary *x) {
            @strongify(self);
            BOOL connectDevice = [x.allKeys containsObject:self.equipmentTypeId];
            if (connectDevice) {
                ///隐藏弹窗
                [self tappedCancel];
            } else {
                [self refreshDevice];
            }
        }];
    }
    return self;
}



- (void)refreshDevice{
    if([BluetoothManager isConnectEquipmentType:self.equipmentTypeId]){
        //设备已连接
        [self.connectBtn setTitle:@"已连接" forState:UIControlStateNormal];
    } else {
        [self.connectBtn setTitle:@"连接设备" forState:UIControlStateNormal];
    }
}

- (UIView *)contentView{
    if (!_contentView) {
        _contentView = [[UIView alloc] init];
        _contentView.backgroundColor = [UIColor whiteColor];
        _contentView.layer.cornerRadius = 8.0f;
        _contentView.layer.masksToBounds = YES;
    }
    return _contentView;
}

- (void)layoutSubviews {
    [super layoutSubviews];
    
    [self.contentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_equalTo(self.mas_centerY);
        make.centerX.mas_equalTo(self.mas_centerX);
        make.width.mas_equalTo(DHPX(340));
//        make.size.mas_equalTo(CGSizeMake(DHPX(340), DHPX(240)));
    }];
    
    [self.titleLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.contentView.mas_top).offset(DHPX(15));
        make.centerX.mas_equalTo(self.contentView);
        make.left.right.mas_equalTo(0);
        make.height.mas_equalTo(DHPX(25));
    }];
    
    [self.devicePartView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.titleLab.mas_bottom).offset(20);
        make.centerX.mas_equalTo(self.contentView);
        make.width.mas_equalTo(DHPX(300));
        make.height.mas_equalTo(DHPX(64));
    }];
    
    NSString *name = @"";
    NSString *imageName = @"";
    switch (self.equipmentTypeId.intValue) {
        case BicycleEquipment:
            name = @"动感单车";
            imageName = @"abi_unconnect_bike";
            break;
        case TreadmillEquipment:
            name = @"跑步机";
            imageName = @"abi_unconnect_run";
            break;
        case BoatEquipment:
            name = @"划船机";
            imageName = @"abi_unconnect_boat";
            break;
        case EllipticalEquipment:
            name = @"椭圆机";
            imageName = @"abi_unconnect_electrical";
            break;
        default:
            break;
    }
    [self.devicePartView setTitleColor:MainTextColor forState:UIControlStateNormal];
    [self.devicePartView setTitle:name forState:UIControlStateNormal];
    self.devicePartView.titleLabel.font = [UIFont systemFontOfSize:20 weight:UIFontWeightMedium];
    [self.devicePartView setImage:[UIImage imageNamed:imageName] forState:UIControlStateNormal];
    
    
    [self.connectBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.devicePartView.mas_bottom).offset(DHPX(20));
        make.centerX.mas_equalTo(self.contentView);
        make.width.mas_equalTo(DHPX(220));
        make.height.mas_equalTo(DHPX(44));
    }];
    [self.connectBtn az_setGradientBackgroundWithColors:@[[UIColor colorWithHexString:@"#00FFF8"],
                                                          [UIColor colorWithHexString:@"#32E3FD"],
                                                          [UIColor colorWithHexString:@"#6483FC"]]
                                              locations:nil
                                             startPoint:CGPointMake(1, 0)
                                               endPoint:CGPointMake(0, 1)];
    
    [self.quitBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.connectBtn.mas_bottom).offset(DHPX(15));
        make.bottom.mas_equalTo(self.contentView.mas_bottom).offset(DHPX(-15));
        make.centerX.mas_equalTo(self.contentView);
        make.width.mas_equalTo(DHPX(220));
        make.height.mas_equalTo(DHPX(30));
    }];
}

- (void)showInView:(UIView *)view {
    if (!self.superview) {
        self.frame = CGRectMake(0, 0, MAX(ScreenWidth, ScreenHeight), MIN(ScreenWidth, ScreenHeight));
        self.alpha = 0;
        self.contentView.alpha = 0;
        [view addSubview:self];
    }
    
    [UIView animateWithDuration:0.3 animations:^{
        self.alpha = 1;
        self.contentView.alpha = 1;
    } completion:^(BOOL finished) {
    
    }];
    
    if (self.isStartTraining) {
        self.titleLab.text = @"开始测评前请先连接设备";
        [self.connectBtn setTitle:@"去连接" forState:UIControlStateNormal];
    } else {
        self.titleLab.text = @"设备中断,请重新连接";
        [self.connectBtn setTitle:@"去连接" forState:UIControlStateNormal];
    }
}

- (void)tappedCancel{
    [UIView animateWithDuration:0.3 animations:^{
         self.contentView.alpha = 0;
         self.alpha = 0;
    } completion:^(BOOL finished) {
        [self removeFromSuperview];
    }];
}

#pragma mark - Praviate method

- (void)setupContainerSubViews{
    [self addSubview:self.contentView];

    [self.contentView addSubview:self.titleLab];
    [self.contentView addSubview:self.devicePartView];
    [self.contentView addSubview:self.connectBtn];
    [self.contentView addSubview:self.quitBtn];
}

- (void)layoutContainerViewSubViews{
    [self layoutIfNeeded];
}

#pragma mark - Lazy
- (UILabel *)titleLab {
    if (!_titleLab) {
        UILabel *label = [[UILabel alloc] init];
        label.textAlignment = NSTextAlignmentCenter;
        label.textColor = MainTextColor;
        label.font = [UIFont systemFontOfSize:17 weight:UIFontWeightMedium];
        label.text = @"开始测评前请先连接设备";
        _titleLab = label;
    }
    return _titleLab;
}

- (UIButton *)devicePartView{
    if (!_devicePartView) {
        _devicePartView = [UIButton buttonWithType:UIButtonTypeCustom];
        _devicePartView.backgroundColor = UIColorHex(#F8F8FA);
        _devicePartView.layer.cornerRadius = 4.0f;
        _devicePartView.layer.masksToBounds = YES;
    }
    return _devicePartView;
}

- (UIButton *)connectBtn{
    if (!_connectBtn) {
        _connectBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_connectBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        [_connectBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateHighlighted];
        [_connectBtn setTitle:@"去连接" forState:UIControlStateNormal];
        _connectBtn.titleLabel.font = [UIFont systemFontOfSize:15 weight:UIFontWeightMedium];
        _connectBtn.layer.cornerRadius = DHPX(44)/2;
        _connectBtn.layer.masksToBounds = YES;
        [_connectBtn addTarget:self action:@selector(connectAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _connectBtn;
}

- (void)connectAction:(UIButton *)sender {
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        if (self.deviceConnectBlock) {
            self.deviceConnectBlock(self);
        }
    });
}

- (UIButton *)quitBtn{
    if (!_quitBtn) {
        _quitBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_quitBtn setTitleColor:UIColorHex(#999999) forState:UIControlStateNormal];
        [_quitBtn setTitle:@"暂不连接,退出测评" forState:UIControlStateNormal];
        _quitBtn.titleLabel.font = [UIFont systemFontOfSize:13];
        [_quitBtn addTarget:self action:@selector(quitAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _quitBtn;
}

- (void)quitAction:(UIButton *)sender {
    [self tappedCancel];
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        if (self.quitTestBlock) {
            self.quitTestBlock(self);
        }
    });
}

@end



