//
//  MRKFramdomTrainController.m
//  Student_IOS
//
//  Created by Jun<PERSON> on 2024/6/25.
//

#import "MRKFramdomTrainController.h"
#import "MRKHrCtrlPushConfig.h"
#import "MRKSetTargetViewController.h"
#import "MRKDeviceConnectAlertView.h"
#import "BlueDataStorageManager.h"
#import "MRKDeviceListManager.h"
#import "DeviceSearchViewController.h"
#import "MRKFreeTrainingController.h"
#import "MRKPrepareTrainManager.h"
#import "UIButton+Event.h"
#import "MRKRouteTrainController.h"
#import "MRKRouteListView.h"


@interface MRKFramdomTrainController ()<JXCategoryViewDelegate , JXCategoryListContainerViewDelegate>
@property (nonatomic, strong) NSMutableArray *titleArray;
@property (nonatomic, strong) NSMutableArray *freedomContentArray;
@property (nonatomic, strong) JXCategoryTitleView *categoryView;  //标签view
@property (nonatomic, strong) JXCategoryListContainerView *listContainerView;  //展示区容器view
@property (nonatomic, strong) NSMutableArray *trainTypeArr;

//是否开始获取数据，已经开始的 离开页面的时候需要结束
@property (nonatomic, assign) BOOL isStartData;
@property (nonatomic, copy) NSString *mapId;
@end

@implementation MRKFramdomTrainController

- (void)dealloc {
     NSLog(@"😊😊😊-----dealloc-----😊😊😊%@" , NSStringFromClass([self class]));
    ///如果小件设备开启过
    if (self.isStartData) {
        [BaseBlueCommandManager endSportWithType:self.equimentTypeId];
    }
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.tracePageId = @"page_exersice_free_mode_select";
    // Do any additional setup after loading the view.
    self.view.backgroundColor = UIColorHex(#4C5362);
    
    self.navTitle = @"自由训练";
    self.navTitleColor = [UIColor whiteColor];
    self.mrkContentView.backgroundColor = UIColorHex(#4C5362);

    ///设备大类为空，取当前首页设备的大类
    if (![self.equimentTypeId isNotBlank]){
        self.equimentTypeId = [MRKDeviceListManager shareManager].equimentTypeId;
    }
    self.trainTypeArr = [NSMutableArray array];
    
    ///通知刷新首页数据
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(refreshPageData)
                                                 name:@"kRefreshMainPageNotification"
                                               object:nil];
    
    
    //添加标签，容器view
    [self initCategoryView];
    [self reloadDatas];
}

- (void)refreshPageData{
    ///为了无设备进入后绑定设备时，回到当前页面刷新
    NSString *equimentTypeId = [MRKDeviceListManager shareManager].equimentTypeId;
    if ([self.equimentTypeId isEmpty] && ![equimentTypeId isEqualToString:self.equimentTypeId]){
        self.equimentTypeId = equimentTypeId;
        [self reloadDatas];
    }
}

- (void)viewDidAppear:(BOOL)animated{
    [super viewDidAppear:animated];
}

- (void)reloadDatas{
    NSInteger type = self.equimentTypeId.intValue;
    /// 只有四大件支持路线练，跑步机有心率智控
    if (type == TreadmillEquipment) {
        self.trainTypeArr = @[@(FreedomTrainType), @(RoadTrainType), @(AIControlTrainType),@(HeartControlTrainType)].mutableCopy;
        self.titleArray = @[@"自由练", @"路线练",  @"智控练", @"心率智控"].mutableCopy;
        self.mapId = @"";
    }else if (type == BicycleEquipment ||
              type == BoatEquipment ||
              type == EllipticalEquipment) {
        self.trainTypeArr = @[@(FreedomTrainType), @(RoadTrainType), @(AIControlTrainType)].mutableCopy;
        self.titleArray = @[@"自由练", @"路线练",  @"智控练"].mutableCopy;
        self.mapId = @"";
    }else{
        self.trainTypeArr = @[@(FreedomTrainType)].mutableCopy;
        self.titleArray = @[@"自由练"].mutableCopy;
    }
    @weakify(self);
    jxt_getSafeMainQueue(^{
        @strongify(self);
        self.categoryView.defaultSelectedIndex = 0;
        self.categoryView.titles = self.titleArray;
        [self.categoryView reloadData];
    });
}

- (void)initCategoryView {
    self.categoryView.delegate = self;
    [self.mrkContentView addSubview:self.categoryView];
    [self.mrkContentView addSubview:self.listContainerView];
    [self.categoryView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.mrkContentView).offset(kNavBarHeight);
        make.left.equalTo(self.mrkContentView).offset(WKDHPX(0));
        make.right.equalTo(self.mrkContentView).offset(-WKDHPX(0));
        make.height.mas_equalTo(WKDHPX(80));
    }];
    [self.listContainerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.categoryView.mas_bottom);
        make.left.right.equalTo(self.mrkContentView);
        make.bottom.mas_equalTo(self.mrkContentView.mas_bottom);
    }];
    self.categoryView.listContainer = self.listContainerView;
    self.categoryView.contentScrollView.scrollEnabled = NO;

    NSInteger type = self.equimentTypeId.intValue;
    switch (type) {
        case JinMoQiangEquipment:
        case PowerEquipment: {
            self.categoryView.hidden = YES;
            [self.categoryView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.height.mas_equalTo(0);
            }];
            [self.listContainerView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(self.mrkContentView).offset(kNavBarHeight);
            }];
        }  break;
        default: break;
    }
}



#pragma mark - JXCategoryViewDelegate

///点击选中或者滚动选中都会调用该方法。适用于只关心选中事件，不关心具体是点击还是滚动选中的。
- (void)categoryView:(JXCategoryBaseView *)categoryView didSelectedItemAtIndex:(NSInteger)index {
    NSLog(@"%@", NSStringFromSelector(_cmd));
    NSLog(@"JXCategoryBaseView__didSelectedItemAtIndex__%ld" , index);
}

///滚动选中的情况才会调用该方法
- (void)categoryView:(JXCategoryBaseView *)categoryView didScrollSelectedItemAtIndex:(NSInteger)index {
    NSLog(@"%@", NSStringFromSelector(_cmd));
}

#pragma mark - JXCategoryListContainerViewDelegate

///返回列表的数量
- (NSInteger)numberOfListsInlistContainerView:(JXCategoryListContainerView *)listContainerView {
    return self.titleArray.count;
}

///返回各个列表菜单下的实例，该实例需要遵守并实现 <JXCategoryListContentViewDelegate> 协议
- (id<JXCategoryListContentViewDelegate>)listContainerView:(JXCategoryListContainerView *)listContainerView initListForIndex:(NSInteger)index {
    NSLog(@"JXCategoryListContentViewDelegate_initListForIndex---%ld",(long)index);
    MRKFramdomTrainListController *vc = [[MRKFramdomTrainListController alloc] init];
    vc.equimentTypeId = self.equimentTypeId;
    NSNumber *type = [_trainTypeArr objectAtIndex:index];
    vc.trainType = (UserTrainType)type.integerValue;
    @weakify(self);
    vc.startTrainBlock = ^(UserTrainModeType trainModeType, UIButton * _Nonnull sender) {
        @strongify(self);
        [self startTrain:trainModeType sender:sender];
    };
    vc.mapIdSelectBlock = ^(NSString * _Nonnull mapId) {
        @strongify(self);
        self.mapId = mapId;
    };
    return vc;
}


#pragma mark - lazy
- (NSMutableArray *)titleArray {
    if (!_titleArray) {
        _titleArray = [[NSMutableArray alloc] init];
    }
    return _titleArray;
}

// categoryView
- (JXCategoryTitleView *)categoryView{
    if (!_categoryView) {
        JXCategoryTitleView *categoryView = [[JXCategoryTitleView alloc] init];
        categoryView.titleFont = kMedium_Font_NoDHPX(17);
        categoryView.titleColor = [[UIColor whiteColor] colorWithAlphaComponent:0.2];
        categoryView.titleSelectedFont = kMedium_Font_NoDHPX(17);
        categoryView.titleSelectedColor = [UIColor whiteColor];
        categoryView.titleColorGradientEnabled = YES;
//        categoryView.titleLabelZoomScale = 1.14;
        categoryView.titleLabelStrokeWidthEnabled = YES;
        categoryView.indicators = @[self.lineView];
        categoryView.averageCellSpacingEnabled = YES;
        categoryView.contentEdgeInsetLeft = WKDHPX(16); //左边距对齐
        categoryView.cellWidth = WKDHPX(80);
        categoryView.cellSpacing = WKDHPX(24);
        _categoryView = categoryView;
    }
    return _categoryView;
}

/// listContainerView
- (JXCategoryListContainerView *)listContainerView {
    if (!_listContainerView) {
        _listContainerView = [[JXCategoryListContainerView alloc] initWithType:JXCategoryListContainerType_ScrollView delegate:self];
    }
    return _listContainerView;
}

/// lineView
- (JXCategoryIndicatorLineView *)lineView{
    JXCategoryIndicatorLineView *lineView = [[JXCategoryIndicatorLineView alloc] init];
    lineView.indicatorColor = [UIColor whiteColor];
    lineView.indicatorWidth = 28;
    lineView.indicatorHeight = 4;
    lineView.scrollStyle = JXCategoryIndicatorScrollStyleSameAsUserScroll;
    lineView.verticalMargin = 18;
    return lineView;
}

- (BOOL)checkIndexValid:(NSInteger)index {
    NSUInteger count = self.titleArray.count;
    if (count <= 0 || index >= count) {
        return NO;
    }
    return YES;
}

- (id<JXCategoryListContentViewDelegate>)currentVC{
    NSInteger index = self.categoryView.selectedIndex;
    if (![self checkIndexValid:index]) {
        return nil;
    }

    id<JXCategoryListContentViewDelegate> list = self.listContainerView.validListDict[@(index)];
    return list;
}

- (void)startTrain:(UserTrainModeType)trainModeType sender:(UIButton *)sender{
    UserTrainType trainType = (UserTrainType)[[_trainTypeArr objectAtIndex:self.categoryView.selectedIndex] integerValue];
    if (trainType == RoadTrainType) {/// 路线练
        ReportMrkLogParms(2, @"路线练 开始训练", @"page_exersice_free_mode_select", @"btn_exersice_route_start_exersice", nil, 0, nil);
        /// 先检查是否有未结算的数据
        @weakify(self);
        [[MRKPrepareTrainManager shared] checkTrainingWithType:TrainCheckTypeRoad complete:^{
            @strongify(self);
            [self jumpToFreedomTrainType:trainType modeType:trainModeType sender: sender];
        }];
    } else if (trainType == FreedomTrainType) {/// 自由练
        /// 先检查是否有未结算的数据
        @weakify(self);
        [[MRKPrepareTrainManager shared] checkTrainingWithType:TrainCheckTypeFreedom complete:^{
            @strongify(self);
            [self jumpToFreedomTrainType:trainType modeType:trainModeType sender: sender];
        }];
    } else if (trainType == AIControlTrainType) {///超燃脂自由练
        [[RouteManager sharedInstance] jumpToUltraHomeVC:self.equimentTypeId];
        return;
    } else if (trainType == HeartControlTrainType) {///心率控速控阻
        [MRKHrCtrlPushConfig hrCtrlPushConfig:self.equimentTypeId];
        return;
    }
}

- (void)jumpToFreedomTrainType:(UserTrainType)trainType modeType:(UserTrainModeType)trainModeType sender:(UIButton *)sender{
    ///如果是健康设备/nil 跳转设备搜索页
    MRKDeviceModel *dModel = [MRKDeviceListManager shareManager].deviceModel;
    if (dModel == nil || dModel.productType.intValue == 3) {
        ///新用户链路拦截处理一下[默认是hasBind数据]
        [[MRKLinkRouterManager sharedInstance] newUserBuildComplete:^(BOOL hasBind) {
            if (hasBind){
                DeviceSearchViewController *vc = [DeviceSearchViewController new];
                [self.navigationController pushViewController:vc animated:YES];
            }
        }];
        return;
    }
    ///未连接设备
    if(![BlueDataStorageManager isConnectDeviceWithProductID:self.equimentTypeId]) {
        [MRKDeviceConnectAlertView alertConnectDeviceView:self.equimentTypeId cancel:^(id data) {}];
        return;
    }
    if (trainType == RoadTrainType) {/// 路线练
        MRKRouteTrainController *vc = [[MRKRouteTrainController alloc] init];
        vc.productId = self.equimentTypeId;
        vc.pushController = self;
        vc.mapId = self.mapId;
        MRKNavigationController *nav = [[MRKNavigationController alloc] initWithRootViewController:vc];
        nav.modalPresentationStyle = UIModalPresentationFullScreen;
        [self presentViewController:nav animated:NO completion:nil];
        return;
    }
    /// 智能壶铃 暂不支持自由训练
    if (dModel.productId.intValue == KettleBellEquipment) {
        [MBProgressHUD showMessage:@"智能壶铃暂不支持自由训练"];
        return;
    }
/*
 FramdomModeTrainType           =  0, ///自由训练
 DistanceModeTrainType          =  1, ///定距练
 TimeModeTrainType              =  2, ///定时练
 */
    switch (trainModeType) {
        case FramdomModeTrainType: {
            sender.traceEventId = @"btn_exersice_equipment_free_exersice";
            MRKFreeTrainingController *vc = [[MRKFreeTrainingController alloc] init];
            vc.productId = self.equimentTypeId;
            vc.randomType = 0;
            [self.navigationController pushViewController:vc animated:YES];
        }   break;
        case DistanceModeTrainType:
        case TimeModeTrainType:{
            [self openTargetSettingVC: trainModeType];
            
        }   break;
        default:
            break;
    }
}

/// 打开目标设置页面
- (void)openTargetSettingVC:(UserTrainModeType)trainModeType{
    // 倒计时/倒计数
    MRKSetTargetViewController *vc = [MRKSetTargetViewController new];
    vc.equipmentType = self.equimentTypeId;
    vc.randomType = (trainModeType == DistanceModeTrainType) ? 1 : 2;
    @weakify(self)
    vc.editTargetBlock = ^(TrainTargetModel * _Nullable targetModel, RandomMode randomType) {
        @strongify(self)
        ///延时跳转，增加体验
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self stratTraining:targetModel randomType:randomType];
        });
    };
    if ([UIDevice currentDevice].isPad){
        [self.navigationController pushViewController:vc animated:NO];
        return;
    }
    vc.modalPresentationStyle = UIModalPresentationFullScreen;
    [self presentViewController:vc animated:YES completion:nil];
}

#pragma mark - 设置目标后跳转到训练页面
- (void)stratTraining:(TrainTargetModel *)targetModel randomType:(RandomMode)type {
    EquipmentType deviceType = (EquipmentType)self.equimentTypeId.intValue;
    if (deviceType == SkipRopeEquipment ||
        deviceType == HoopEquipment ||
        deviceType == FLSBEquipment ) {
        ///设备有模式，设置模式需要先打开数据交互通道 22-05-12
        self.isStartData = YES;
        [BaseBlueCommandManager startSportWithType:self.equimentTypeId];
        
        NSString *observerName = nil;
        BluetoothModel *model = [BluetoothModel modelFromEquipmentType:self.equimentTypeId];
        NSNumber *dataService = model.dataServiceType;
        if (model.dataServiceType.intValue == ZJCommunicationProtocol) {
            if (deviceType == SkipRopeEquipment) {
                observerName = @"jumpStringDataCutDown";
            }
        } else if (dataService.intValue == MRKCommunicationProtocol) {
            observerName = @"MRKSetCutDownModeSuccessNotification";
        }
        
        if ([observerName isNotBlank]) {
            @weakify(self);
            __block id observer = nil;
            observer = [[NSNotificationCenter defaultCenter] addObserverForName:observerName object:nil queue:nil usingBlock:^(NSNotification * _Nonnull notification) {
                @strongify(self);
                dispatch_async(dispatch_get_main_queue(), ^{
                    [[NSNotificationCenter defaultCenter] removeObserver:observer];
                    [[NSNotificationCenter defaultCenter] removeObserver:self name:@"MRKControlModeFailureNotification" object:nil];
                    
                    [MBProgressHUD hideHUDForView:self.view];
                    [self startExcirse:targetModel randomType:type];
                });
            }];
            
            [[NSNotificationCenter defaultCenter] addObserverForName:@"MRKControlModeFailureNotification" object:nil queue:nil usingBlock:^(NSNotification * _Nonnull notification) {
                @strongify(self);
                dispatch_async(dispatch_get_main_queue(), ^{
                    [[NSNotificationCenter defaultCenter] removeObserver:observer];
                    [[NSNotificationCenter defaultCenter] removeObserver:self name:@"MRKControlModeFailureNotification" object:nil];
                    
                    [MBProgressHUD hideHUDForView:self.view];
                    [MBProgressHUD showMessage:@"模式设置失败, 请重试"];
                });
            }];
        }
        
        NSDictionary *parms = @{
            @"type"   : self.equimentTypeId ?:@"",
            @"mode"   : @(targetModel.type.intValue),
            @"target" : targetModel.number
        };
        [[NSNotificationCenter defaultCenter] postNotificationName:SetModeNotification object:parms] ;
        NSLog(@"parms === %@" , parms);
        [MBProgressHUD showLodingWithMessage:@"" view:self.view];
    } else {
        if (deviceType != TreadmillEquipment) {
            [BaseBlueCommandManager startSportWithType:self.equimentTypeId];
        }
        
        [self startExcirse:targetModel randomType:type];
    }
}

- (void)startExcirse:(TrainTargetModel *)targetModel randomType:(NSInteger)type{
    if (![BluetoothManager isConnectEquipmentType:self.equimentTypeId]) {
        [AppDelegate errorView:@"设备已断连"];
        return;
    }

    dispatch_async(dispatch_get_main_queue(), ^{
        EquipmentType deviceType = (EquipmentType)self.equimentTypeId.intValue;
        BluetoothModel *model = [BluetoothModel modelFromEquipmentType:self.equimentTypeId];
        if (model.dataServiceType.intValue == ZJCommunicationProtocol) {
            if (deviceType == SkipRopeEquipment) { ///跳绳
                [[BluetoothManager sharedInstance] sendData:[BloothTool skipStartData] type:self.equimentTypeId];
            }
        } else if (model.dataServiceType.intValue == MRKCommunicationProtocol) {
            if (deviceType == FLSBEquipment || deviceType== HoopEquipment) { ///飞力士棒/呼啦圈
                [[BluetoothManager sharedInstance] sendData:[BloothTool MRKStartData] type:self.equimentTypeId];
            }
        }

        MRKFreeTrainingController *vc = [MRKFreeTrainingController new];
        vc.productId = self.equimentTypeId;
        vc.randomType = type;
        vc.targetModel = targetModel;
        [self.navigationController pushViewController:vc animated:YES];
    });
}








#pragma mark ---------Delegate -----------

- (BOOL)viewControllerIsNeedNavBar:(MRKBaseController *)viewController {
    return YES;
}

- (BOOL)mrkNavigationBarIsHideBottomLine:(MRKNavigationBar *)navigationBar {
    return YES;
}

- (UIStatusBarStyle)navControllerStatusBarStyle:(MRKBaseController *)viewController{
    return UIStatusBarStyleLightContent;
}

- (UIImage *)mrkNavigationBarLeftButtonImage:(UIButton *)leftButton navigationBar:(MRKNavigationBar *)navigationBar {
    return [UIImage imageNamed:@"icon_back-4"];
}
/** 背景色 */
- (UIColor *)mrkNavigationBarBackgroundColor:(MRKNavigationBar *)navigationBar{
    return UIColorHex(#4C5362);
}

/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/

@end








@interface MRKFramdomTrainListController ()
@property (nonatomic, strong) UIImageView *iconImageView;

@property (nonatomic, strong) UIImageView *routeImageView;
@property (nonatomic, strong) UIView *trainBottomVue;   /// 训练底部页面
@property (nonatomic, strong) UIButton *timeBtn;        /// 定时练按钮
@property (nonatomic, strong) UIButton *distanceBtn;    /// 定距练按钮
@property (nonatomic, strong) UIButton *quickBtn;       /// 快速开始按钮
@property (nonatomic, strong) UIButton *trainBtn;       /// 开始训练按钮
@property (nonatomic, strong) MRKRouteListView *routeView;       /// 路线列表
@end

@implementation MRKFramdomTrainListController
- (void)dealloc {
    NSLog(@"😊😊😊-----dealloc-----😊😊😊%@" , NSStringFromClass([self class]));
    
}


- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    self.view.backgroundColor = UIColorHex(#4C5362);
    
    [self.view addSubview:self.iconImageView];
    [self.iconImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsMake(0, 0, WKDHPX(176), 0));
    }];
    
    switch (self.trainType) {
        case RoadTrainType: {
            
            self.routeImageView.image = [UIImage imageNamed:@"icon_train_xihu"];
            [self.view addSubview:self.routeImageView];
            [self.routeImageView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.center.equalTo(self.iconImageView);
                make.width.mas_equalTo(WKDHPX(307));
            }];
            
            self.iconImageView.image = [self routeImage];
            [self.view addSubview:self.trainBtn];
            [self.trainBtn mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerX.equalTo(self.view.mas_centerX);
                make.size.mas_equalTo(CGSizeMake(WKDHPX(343), WKDHPX(48)));
                make.bottom.equalTo(self.view.mas_bottom).offset(-WKDHPX(40));
            }];
            
            ///
            [self.view addSubview:self.routeView];
            @weakify(self);
            self.routeView.listDataRequestBlock = ^{
                
            };
            self.routeView.listSelectBlock = ^(MRKRouteListViewModel * _Nonnull model) {
                @strongify(self);
                [self.routeImageView sd_setImageWithURL:[NSURL URLWithString:model.mapCover] placeholderImage:nil];
                
                if (self.mapIdSelectBlock){
                    self.mapIdSelectBlock(model.cid);
                }
            };
            [self.routeView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerX.equalTo(self.view.mas_centerX);
                make.size.mas_equalTo(CGSizeMake(RealScreenWidth, WKDHPX(80)));
                make.bottom.equalTo(self.trainBtn.mas_top);
            }];
            
        }   break;
        case FreedomTrainType:{
            self.iconImageView.image = [self fradomImage];
            [self.view addSubview:self.trainBottomVue];
            [self.trainBottomVue mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerX.equalTo(self.view.mas_centerX);
                make.size.mas_equalTo(CGSizeMake(WKDHPX(375), WKDHPX(100)));
                make.bottom.equalTo(self.view.mas_bottom).offset(-WKDHPX(30)-kSafeArea_Bottom);
            }];
            
            [self.trainBottomVue addSubview:self.quickBtn];
            [self.quickBtn mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerY.centerX.equalTo(self.trainBottomVue);
                make.size.mas_equalTo(CGSizeMake(WKDHPX(100), WKDHPX(100)));
            }];
            [self.trainBottomVue addSubview:self.timeBtn];
            [self.timeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerY.equalTo(self.trainBottomVue);
                make.right.equalTo(self.quickBtn.mas_left).offset(-WKDHPX(16));
                make.size.mas_equalTo(CGSizeMake(WKDHPX(105), WKDHPX(49)));
            }];
            [self.trainBottomVue addSubview:self.distanceBtn];
            [self.distanceBtn mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerY.equalTo(self.trainBottomVue);
                make.left.equalTo(self.quickBtn.mas_right).offset(WKDHPX(16));
                make.size.mas_equalTo(CGSizeMake(WKDHPX(105), WKDHPX(49)));
            }];
            
            /// 跳绳是计数、计时，四大件是定距、定时，其余只有自由练
            NSInteger type = self.equimentTypeId.intValue;
            if (type == SkipRopeEquipment) {
                self.timeBtn.hidden = NO;
                self.distanceBtn.hidden = NO;
                [self.timeBtn setTitle:@" 计时练" forState:UIControlStateNormal];
                [self.distanceBtn setTitle:@" 计数练" forState:UIControlStateNormal];
            }else if (type == BicycleEquipment ||
                      type == BoatEquipment ||
                      type == TreadmillEquipment ||
                      type == EllipticalEquipment ||
                      type == StairClimbEquiment) {
                self.timeBtn.hidden = NO;
                self.distanceBtn.hidden = NO;
                [self.timeBtn setTitle:@" 定时练" forState:UIControlStateNormal];
                [self.distanceBtn setTitle:@" 定距练" forState:UIControlStateNormal];
            }else {
                self.timeBtn.hidden = YES;
                self.distanceBtn.hidden = YES;
            }
        }   break;
        case AIControlTrainType: {
            self.iconImageView.image = [self controlImage];
            [self.view addSubview:self.trainBtn];
            [self.trainBtn mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerX.equalTo(self.view.mas_centerX);
                make.size.mas_equalTo(CGSizeMake(WKDHPX(343), WKDHPX(48)));
                make.bottom.equalTo(self.view.mas_bottom).offset(-WKDHPX(51)-kSafeArea_Bottom);
            }];
        }   break;
        case HeartControlTrainType: {
            self.iconImageView.image = [self heartImage];
            [self.view addSubview:self.trainBtn];
            [self.trainBtn mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerX.equalTo(self.view.mas_centerX);
                make.size.mas_equalTo(CGSizeMake(WKDHPX(343), WKDHPX(48)));
                make.bottom.equalTo(self.view.mas_bottom).offset(-WKDHPX(51)-kSafeArea_Bottom);
            }];
        }   break;
        default:
            break;
    }
}

- (CGSize)imageSize{
    return CGSizeMake(RealScreenWidth, RealScreenHeight - WKDHPX(170) - WKDHPX(50) - kNavBarHeight);
}

- (UIImage *)fradomImage {
    CGSize size = self.imageSize;
    UIGraphicsBeginImageContextWithOptions(size, NO, [[UIScreen mainScreen] scale]);
    
    UIImage *bgImage = [UIImage imageNamed:@"icon_train_fradom"];
    UIImage *image = [bgImage imageByResizeToSize:size contentMode:UIViewContentModeScaleAspectFill];
    [image drawInRect:CGRectMake(0, 0, size.width, size.height)];
    
    UIImage *resultImage = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return resultImage;
}

- (UIImage *)routeImage {
    CGSize size = self.imageSize;
    UIGraphicsBeginImageContextWithOptions(size, NO, [[UIScreen mainScreen] scale]);
    
    UIImage *bgImage = [UIImage imageNamed:@"icon_train_fradom_route"];
    UIImage *image = [bgImage imageByResizeToSize:size contentMode:UIViewContentModeScaleAspectFill];
    [image drawInRect:CGRectMake(0, 0, size.width, size.height)];
    
    UIImage *resultImage = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return resultImage;
}

- (UIImage *)controlImage {
    CGSize size = self.imageSize;
    UIGraphicsBeginImageContextWithOptions(size, NO, [[UIScreen mainScreen] scale]);
    
    UIImage *bgImage = [UIImage imageNamed:@"icon_train_bgview"];
    UIImage *image = [bgImage imageByResizeToSize:size contentMode:UIViewContentModeScaleAspectFill];
    [image drawInRect:CGRectMake(0, 0, size.width, size.height)];
    
    //Draw control
    UIImage *cImage = [UIImage imageNamed:@"icon_train_control"];;
    [cImage drawInRect:CGRectMake(-WKDHPX(41), -WKDHPX(41), WKDHPX(151), WKDHPX(127))];
    
    //Draw Merit
    UIImage *mImage = [UIImage imageNamed:@"icon_train_merit"];;
    [mImage drawInRect:CGRectMake(size.width -WKDHPX(269) + WKDHPX(11), WKDHPX(30), WKDHPX(269), WKDHPX(61))];
    
    //Draw control2
    UIImage *c2Image = [UIImage imageNamed:@"icon_train_control_2"];;
    [c2Image drawInRect:CGRectMake((size.width - WKDHPX(304))/2, (size.height - WKDHPX(160))/2 - WKDHPX(45), WKDHPX(304), WKDHPX(160))];
    
    //Draw Merit
    UIImage *m1Image = [UIImage imageNamed:@"icon_train_merit"];
    UIImage *m1Image_new = [m1Image imageByResizeToSize:CGSizeMake(WKDHPX(198), WKDHPX(44)) contentMode:UIViewContentModeScaleAspectFill];
    [m1Image_new drawInRect:CGRectMake(-WKDHPX(4), size.height -WKDHPX(66) - WKDHPX(30), WKDHPX(198), WKDHPX(44))];
    
    //Draw control1
    UIImage *c1Image = [UIImage imageNamed:@"icon_train_control_1"];;
    [c1Image drawInRect:CGRectMake(size.width - WKDHPX(245), size.height -WKDHPX(147) - WKDHPX(30), WKDHPX(245), WKDHPX(147))];
    
    UIImage *resultImage = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return resultImage;
}


- (UIImage *)heartImage {
    CGSize size = self.imageSize;
    UIGraphicsBeginImageContextWithOptions(size, NO, [[UIScreen mainScreen] scale]);
    
    UIImage *bgImage = [UIImage imageNamed:@"icon_train_bgview"];
    UIImage *image = [bgImage imageByResizeToSize:size contentMode:UIViewContentModeScaleAspectFill];
    [image drawInRect:CGRectMake(0, 0, size.width, size.height)];
    
    //Draw control
    UIImage *cImage = [UIImage imageNamed:@"icon_train_heart"];;
    [cImage drawInRect:CGRectMake(-WKDHPX(41), -WKDHPX(41), WKDHPX(151), WKDHPX(127))];
    
    
    CGRect rect = CGRectMake((size.width - WKDHPX(330))/2, (size.height - WKDHPX(148))/2 - WKDHPX(93), WKDHPX(330), WKDHPX(148));
    //Draw Merit
    UIImage *mImage = [UIImage imageNamed:@"icon_train_merit"];;
    [mImage drawInRect:CGRectMake(size.width -WKDHPX(269) + WKDHPX(16), CGRectGetMinY(rect)+WKDHPX(8), WKDHPX(269), WKDHPX(61))];
    
    //Draw Merit
    UIImage *m1Image = [UIImage imageNamed:@"icon_train_merit"];
    UIImage *m1Image_new = [m1Image imageByResizeToSize:CGSizeMake(WKDHPX(198), WKDHPX(44)) contentMode:UIViewContentModeScaleAspectFill];
    [m1Image_new drawInRect:CGRectMake(WKDHPX(3), CGRectGetMaxY(rect)-WKDHPX(33), WKDHPX(148), WKDHPX(33))];
    
    //Draw control2
    UIImage *c2Image = [UIImage imageNamed:@"icon_train_heart_2"];
    [c2Image drawInRect:rect];
    
    
    //Draw control1
    UIImage *c1Image = [UIImage imageNamed:@"icon_train_heart_1"];;
    [c1Image drawInRect:CGRectMake((size.width - WKDHPX(228))/2, size.height -WKDHPX(188) - WKDHPX(45), WKDHPX(228), WKDHPX(188))];
    
    UIImage *resultImage = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return resultImage;
}



- (MRKRouteListView *)routeView {
    if (_routeView) return _routeView;
    _routeView = [[MRKRouteListView alloc] init];
    return _routeView;
}

- (UIImageView *)routeImageView {
    if (_routeImageView) return _routeImageView;
    UIImageView *imagev = [[UIImageView alloc] init];
    imagev.contentMode = UIViewContentModeScaleAspectFit;
    imagev.sd_imageTransition = SDWebImageTransition.fadeTransition;
    _routeImageView = imagev;
    return _routeImageView;
}

- (UIImageView *)iconImageView {
    if (_iconImageView) return _iconImageView;
    UIImageView *imagev = [[UIImageView alloc] init];
    imagev.contentMode = UIViewContentModeScaleAspectFill;
    _iconImageView = imagev;
    return _iconImageView;
}

- (UIButton *)quickBtn {
    if (!_quickBtn) {
        UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
        btn.backgroundColor = UIColorHex(#16D2E3);
        [btn setTitle:@"快速开始" forState:UIControlStateNormal];
        [btn setTitleColor:UIColorHex(#363A44) forState:UIControlStateNormal];
        btn.titleLabel.font = kMedium_Font_NoDHPX(WKDHPX(17));
        [btn addTarget:self action:@selector(startTrainAction:) forControlEvents:UIControlEventTouchUpInside];
        btn.layer.cornerRadius = WKDHPX(100)/2;
        btn.layer.masksToBounds = YES;
        _quickBtn = btn;
    }
    return _quickBtn;
}

- (UIView *)trainBottomVue {
    if (!_trainBottomVue) {
        UIView *bottomVue = [[UIView alloc] init];
        _trainBottomVue = bottomVue;
    }
    return _trainBottomVue;
}

- (UIButton *)timeBtn {
    if (!_timeBtn) {
        UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
        btn.backgroundColor = UIColorHex(#363A44);
        [btn setImage:[UIImage imageNamed:@"freedom_time_icon"] forState:UIControlStateNormal];
        [btn setTitleColor:UIColorHex(#16D2E3) forState:UIControlStateNormal];
        btn.titleLabel.font = kMedium_Font_NoDHPX(WKDHPX(16));
        [btn addTarget:self action:@selector(startTimeTrainAction:) forControlEvents:UIControlEventTouchUpInside];
        btn.layer.cornerRadius = WKDHPX(49)/2;
        btn.layer.masksToBounds = YES;
        _timeBtn = btn;
    }
    return _timeBtn;
}

- (UIButton *)distanceBtn {
    if (!_distanceBtn) {
        UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
        btn.backgroundColor = UIColorHex(#363A44);
        [btn setImage:[UIImage imageNamed:@"freedom_distance_icon"] forState:UIControlStateNormal];
        [btn setTitleColor:UIColorHex(#16D2E3) forState:UIControlStateNormal];
        btn.titleLabel.font = kMedium_Font_NoDHPX(WKDHPX(16));
        [btn addTarget:self action:@selector(startDistanceBtnTrainAction:) forControlEvents:UIControlEventTouchUpInside];
        btn.layer.cornerRadius = WKDHPX(49)/2;
        btn.layer.masksToBounds = YES;
        _distanceBtn = btn;
    }
    return _distanceBtn;
}

- (UIButton *)trainBtn {
    if (!_trainBtn) {
        UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
        btn.backgroundColor = UIColorHex(#16D2E3);
        [btn setTitle:@"开始训练" forState:UIControlStateNormal];
        [btn setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
        btn.titleLabel.font = kMedium_Font_NoDHPX(WKDHPX(18));
        [btn addTarget:self action:@selector(startTrainAction:) forControlEvents:UIControlEventTouchUpInside];
        btn.layer.cornerRadius = WKDHPX(48)/2;
        btn.layer.masksToBounds = YES;
        _trainBtn = btn;
    }
    return _trainBtn;
}

- (void)startTrainAction:(UIButton *)sender{
    if (self.startTrainBlock) {
        self.startTrainBlock(FramdomModeTrainType, sender);
    }
}

- (void)startTimeTrainAction:(UIButton *)sender{
    if (self.startTrainBlock) {
        self.startTrainBlock(TimeModeTrainType, sender);
    }
}

- (void)startDistanceBtnTrainAction:(UIButton *)sender{
    if (self.startTrainBlock) {
        self.startTrainBlock(DistanceModeTrainType, sender);
    }
}

#pragma mark - JXCategoryListContentViewDelegate

/**
 实现 <JXCategoryListContentViewDelegate> 协议方法，返回该视图控制器所拥有的「视图」
 */
- (UIView *)listView {
    return self.view;
}

//- (UIScrollView *)listScrollView{
//    
//}
//
//- (void)listViewDidScrollCallback:(void (^)(UIScrollView *scrollView))callback{
//    
//}

/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/

@end






