//
//  MRKFreeTrainingController.m
//  Student_IOS
//
//  Created by Jun<PERSON> on 2024/8/13.
//

#import "MRKFreeTrainingController.h"
#import "FreedomFinishView.h"
#import "FreedomDataView.h"
#import "TreamillView.h"
#import "MRKHeartItemView.h"
#import "MRKDeviceConnectAlertView.h"
#import "MRKSignActivity.h"
#import "FeedbackGeneratorUtil.h"
#import "MRKHeartWarnManager.h"
#import "MRKTrainManager.h"
#import "MRKFreedomTipMessageView.h"
#import "UIButton+Event.h" ///按钮防重复点击
#import "MRKDynamicIslandManager.h"
///
///
#define kTargetViewTag 0xffff

@interface MRKFreeTrainingController ()<MRKHeartWarnManagerDelegate>
@property (nonatomic , assign) BOOL isPause;
@property (nonatomic , assign) BOOL isSuspend;
@property (nonatomic , assign) BOOL isFirst;
@property (nonatomic , assign) BOOL isStart;//跑步机进入后，获取状态之前设置为NO,
@property (nonatomic , strong) NSNumber *deviceStatus; ///记录当前运动设备连接状态
///
@property (nonatomic , assign) BOOL hasAlertChangeModel;//是否切换模式
//@property (nonatomic , assign) BOOL hasSkipReport;//切换模式是否跳转到训练报告
@property (nonatomic , assign) BOOL isTargetCompletedContinue;//完成目标后是否继续
///
@property (nonatomic , strong) FramdomExerciseView *trainView;
@property (nonatomic , strong) TreamillCutdownView *cutdownView;//倒计时
@property (nonatomic , strong) TreamillSlowdownView *slowDownView;//减速中
@property (nonatomic , strong) TreamillStartView *firstStartView;//第一次的开始view

@property (nonatomic , strong) MRKHeartWarnManager *warnManager;
@property (nonatomic , strong) MRKSignActivity *checkActivity;

@property (nonatomic , assign) BOOL hasLoadModel; //是否跳过设置模式
@property (nonatomic , assign) BOOL hasGenerateReport; //是否生成过报告

@property (nonatomic , assign) BOOL hasEleTip; //是否提示过电量

@end



@implementation MRKFreeTrainingController

- (MRKSignActivity *)checkActivity{
    if(!_checkActivity) {
        _checkActivity = [[MRKSignActivity alloc] init];
    }
    return _checkActivity;
}

- (FramdomExerciseView *)trainView{
    if(!_trainView) {
        _trainView = [[FramdomExerciseView alloc] init];
        @weakify(self);
        _trainView.updateHoopBlock = ^{
            [self_weak_ updateHoop];
        };
        _trainView.treamillGoonBlock = ^(id _Nonnull sender) {
            [self_weak_ treamillGoonAction];
        };
        _trainView.treamillPauseBlock = ^(id _Nonnull sender) {
            [self_weak_ treamillPauseAction:sender];
        };
        _trainView.backClickBlock = ^{
            [self_weak_ backButtonClick];
        };
    }
    return _trainView;
}

- (void)dealloc {
    if (UIApplication.sharedApplication.applicationState == UIApplicationStateActive) {
        [[MRKDynamicIslandManager shared] end];
    }
    
    NSLog(@"😊😊😊-----dealloc-----😊😊😊%@" , NSStringFromClass([self class]));
    [[NSNotificationCenter defaultCenter] removeObserver:self];
    [[UIApplication sharedApplication] setIdleTimerDisabled:NO];
    
//    [_dataManager clearAll];
    [_trainManager stopTrain];
    [_warnManager closeTimer];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
    //关闭手势
    self.fd_interactivePopDisabled = YES;
    [[UIApplication sharedApplication] setIdleTimerDisabled:YES];
}

- (void)viewDidAppear:(BOOL)animated{
    [super viewDidAppear:YES];
}

- (UIView *)keyWindow {
    return [UIViewController keywindow];
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    //开启手势
    self.fd_interactivePopDisabled = NO;
    
    if ([self.keyWindow viewWithTag:0x123]) {
        [[self.keyWindow viewWithTag:0x123] removeFromSuperview];
    }
}

- (void)viewDidLoad {
    self.tracePageId = @"page_exersice_free";
    if([self.productId isNotEmpty]){
        self.tracePara = @{@"product_id" : self.productId};
    }
    [super viewDidLoad];
    self.mrkContentView.backgroundColor = [UIColor colorWithHexString:@"#4C5362"];
    
    
//    self.tyModel = nil;
//    self.eqModel = [EquipmentDetialModel new];
    self.isSuspend = YES;
    self.isTargetCompletedContinue = YES;
    
    [self getEquipmentDetial];
    
//    ///月月返活动中是否使用心率消耗
//    [[NSNotificationCenter defaultCenter] addObserver:self
//                                             selector:@selector(configRateKcalView:)
//                                                 name:SignActivityUseRateKcalNote
//                                               object:nil];

    //退到后台通知  开启灵动岛
    @weakify(self);
    [[[[NSNotificationCenter defaultCenter] rac_addObserverForName:UIApplicationDidEnterBackgroundNotification object:nil] takeUntil:self.rac_willDeallocSignal] subscribeNext:^(id x) {
        @strongify(self);
        if (!self.hasLoadModel) return;
        jxt_getSafeMainQueue(^{
            if (!self.hasFinishtarget) { // 已完成的状态不需要再次开启
                [[MRKDynamicIslandManager shared] start:self];
            }
        });
    }];
    
    ///
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self startTraining];
    });
}

//- (void)configRateKcalView:(NSNotification *)notification{
//    self.trainManager.enableHeartbeatKcal = YES;
//}

#pragma mark - 获取当前自由训练设备详情
- (void)getEquipmentDetial{
    NSString *name = [BluePeripheral connectDeviceNameOfType:self.productId];
    NSLog(@"name ---- %@" , name);
    
    if (![BluetoothManager isConnectEquipmentType:self.productId]) {
        [self updateDeviceStatus:DeviceDisconnect];
        return;
    }
    
    @weakify(self);
    [[RACObserve(self.trainManager, equipmentInfo.detailModel) distinctUntilChanged] subscribeNext:^(EquipmentDetialModel *model) {
        @strongify(self);
        if (model != nil){
            self.trainView.eqModel = model;
            self.warnManager.eqModel = model;
            self.checkActivity.eqModel = model;
            
            [self rac_changeUIAndConnectStatus];
        }
    }];
    
//    NSDictionary *dic = @{
//        @"equipName" : name?:@"",
//        @"oneLevelTypeId" : self.productId
//    };
//    [MRKRequestServiceData getEquipmentInfo:dic success:^(id data) {
//        self.eqModel = [EquipmentDetialModel modelWithJSON:data];
//        self.eqModel.productID = self.productId;///返回值没有这个字段
//    
//        self.dataManager.eqModel = self.eqModel;
//        self.trainView.eqModel = self.eqModel;
//        self.warnManager.eqModel = self.eqModel;
//        self.checkActivity.eqModel = self.eqModel;
//    } failure:^(id data) {
//        
//    }];
    
    ///请求活动信息
    [self.checkActivity productId:self.productId ?:@"" requestCheckActivity:^{

    }];
}

/// 获取完设备信息之后监听变化
- (void)rac_changeUIAndConnectStatus{
    @weakify(self);
    [[[RACObserve(self.trainManager, connectStatus) distinctUntilChanged] takeUntil:[self rac_willDeallocSignal]] subscribeNext:^(NSNumber *connectStatus) {
        @strongify(self);
        DEVICE_CONNECT_STATUS status = connectStatus.intValue;
        [self updateDeviceStatus:status];
    }];
    
    [[[RACObserve(self.trainManager, dataManager.showData) distinctUntilChanged] takeUntil:[self rac_willDeallocSignal]] subscribeNext:^(TrainingShowData *showData) {
        @strongify(self);
        [self updateUI];
        [[MRKDynamicIslandManager shared] update:self];
    }];
}

- (void)startTraining{
    self.hasLoadModel = YES;
    
    self.trainView.productId = self.productId;
    self.trainView.randomType = self.randomType;
    self.trainView.targetModel = self.targetModel;
    [self.mrkContentView addSubview:self.trainView];
    [self.trainView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsZero);
    }];

    RAC(self.trainView, isPause) = RACObserve(self, isPause);
    RAC(self.trainManager, hasFinishTarget) = RACObserve(self, hasFinishtarget);

    ///跑步机
    if (self.productId.intValue == TreadmillEquipment) {
        ///跑步机开始
        @weakify(self);
        self.firstStartView = [[TreamillStartView alloc] initWithFrame:[UIScreen mainScreen].bounds];
        self.firstStartView.hidden = YES;
        self.firstStartView.tag = 0x123;
        [self.firstStartView.startSignal subscribeNext:^(id x) {
            [self_weak_ startAction:x];
        }];
        [self.firstStartView.backSignal subscribeNext:^(id x) {
            [self_weak_ backButtonClick];
        }];
        [self.keyWindow addSubview:self.firstStartView];
   
        ///跑步机倒计时
        self.cutdownView = [[TreamillCutdownView alloc] initWithFrame:[UIScreen mainScreen].bounds];
        self.cutdownView.hidden = YES;
        [self.keyWindow addSubview:self.cutdownView];
        
        ///跑步机减速中
        self.slowDownView = [[TreamillSlowdownView alloc] initWithFrame:[UIScreen mainScreen].bounds];
        self.slowDownView.hidden = YES;
        [self.keyWindow addSubview:self.slowDownView];
        
        [self treamillStatusNotification];
    }else if (self.productId.intValue == JinMoQiangEquipment) {
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(pressAction:) name:JMQPressNotification object:nil];
    }
    
//    [self configRateKcalView];
    
    ///设置代理 开始显示数据 ，防止在设置的时候数据回调显示触发模式
    MRKTrainingInfo *info = [[MRKTrainingInfo alloc] init];
    info.trainingType = MRKTrainingTypeFreedom;
    info.trainingMode = [NSString stringWithFormat:@"%ld", (long)self.randomType];
    info.trainingTarget = [NSString stringWithFormat:@"%@", [self trainTargetNumber]];
    [self.trainManager startTrain:self.productId trainingInfo:info];
}


- (void)treamillStatusNotification {
    ///跑步机倒计时
    @weakify(self);
    [[RACObserve(self.trainManager, equipmentInfo.treamillCutdown) distinctUntilChanged] subscribeNext:^(NSNumber *number) {
        @strongify(self);
        [self treamillCutdown:number.intValue];
    }];
    
    [[RACObserve(self.trainManager, treamillStatus) distinctUntilChanged] subscribeNext:^(NSNumber * x) {
        if (!x) {return;}
        
        NSLog(@"MRKFreeTrainingController__TREAMILL_STATUS===%@" ,x);
        @strongify(self);
        dispatch_async(dispatch_get_main_queue(), ^{
            switch (x.intValue) {
                case DeviceStandbyStatus:
                case DeviceX1StandbyStatus: {
                    if (self.isStart) {
                        [self treamillEnd:nil];
                    } else {
                        self.firstStartView.hidden = NO;
                    }
                } break;
                    
                case DeviceCutDownStatus: {
                    self.firstStartView.hidden = YES;
                    
                } break;
                    
                case DeviceRuningStatus: {
                    self.firstStartView.hidden = YES;
                    self.isStart = YES;
                    self.isPause = NO;
                    
                    [self treamDataUpdate];
                } break;
                    
                case DevicelSlowDownStatus: {
                    self.firstStartView.hidden = YES;
                    self.isStart = YES;
                    
                    [self treamillSuspending];
                } break;
                    
                case DevicePauseStatus: {
                    self.firstStartView.hidden = YES;
                    
                    self.isStart = YES;
                    self.isPause = YES;
                    [self treamillPauseSuccess];
                } break;
                    
                case DeviceunUnknown: {
          
                } break;
                default:
                    ///显示开始按钮
                    self.firstStartView.hidden = NO;
                break;
            }
        });
    }];
}


/////心率消耗view配置
//- (void)configRateKcalView {
//    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
//        if (self.checkActivity.useRateKcal) {
//            ///tip 心率消耗的提示弹窗
//            [self.trainView.rateView checkHeartRateAlert:self.view withModel:self.checkActivity.activityModel];
//            ///显示心率消耗view
//            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
//                self.trainView.rateView.signActivityUseRateKcal = YES;
//                [self.trainView.rateView showRateKcalView:self.checkActivity.activityModel completeHandler:^{
//                    
//                }];
//            });
//        }
//    });
//}







#pragma mark - delegate update status
- (void)updateUI {
    
//    BlueNSLog(@"updateUI===%@", self.dataManager.tyModel);
//    self.tyModel = self.dataManager.tyModel;
//    self.trainView.dataModel = self.dataManager.tyModel;
//    self.warnManager.dataModel = self.dataManager.tyModel;
 
    
    TrainingShowData *data = self.trainManager.dataManager.showData.copy;
    
    self.trainView.showData = data;
    self.warnManager.showData = data;
    ///目标练，定距练更新Progress
    if (self.randomType != FreedomMode && !self.hasFinishtarget) {
        [self updateTargetProgressView];
    }
    
    NSString *equmentName = [BluePeripheral connectDeviceNameOfType:self.productId];
    if (data.isShowElectricTip && [equmentName localizedCaseInsensitiveContainsString:@"-P99"] && !self.hasEleTip) {//展示电量提示
        self.hasEleTip = YES;
        [AppDelegate errorView:@"电量低于30%，请及时充电"];
    }
}

//- (void)updateHeartRate {
//    [self.trainView.rateView updateheartWithModel:self.dataManager.tyModel];
//}

- (void)updateDeviceStatus:(DEVICE_CONNECT_STATUS)status {
    NSLog(@"%@______updateDeviceStatus==%@" , self,@(status));
    ///排除同一种状态多次执行
    if (self.deviceStatus.intValue == status) return;
    
    self.deviceStatus = @(status);
    self.trainView.infoView.connectStatus = status;
    
    switch (status) {
        case DeviceDisconnect:{
            
            self.isStart = NO;
            if (self.productId.intValue == TreadmillEquipment ) {
                if (!self.slowDownView.hidden) {
                    [self treamillPauseSuccess];
                }
            }
        }
            break;

        case DeviceConnected: {
            
        }
            break;
            
        default:

            break;
    }
}



- (void)treamillEnd:(NSNotification *)sender {
    if (!self.isStart) {
        return;
    }
    
    dispatch_async(dispatch_get_main_queue(), ^{
        if (!self.slowDownView.hidden) {
            self.slowDownView.hidden = YES;
        }
        
        NSLog(@"跑步机运动结束了");
        [self closeByExerciseReport];
    });
}



- (void)treamillStandBy:(id)sender {
    if (!self.isStart) {
        return;
    }
    
    dispatch_async(dispatch_get_main_queue(), ^{
        if (!self.slowDownView.hidden) {
            self.slowDownView.hidden = YES;
        }
        
        //9-28 直接结束训练
        [self closeByExerciseReport];
    });
}



#pragma mark - 跑步机暂停成功
- (void)treamillPauseSuccess {
    dispatch_async(dispatch_get_main_queue(), ^{
        self.trainView.startButton.hidden = NO;
        self.trainView.pauseButton.hidden = YES;
        self.slowDownView.hidden = YES;
        
//        self.trainManager.dataManager.showData.speed = @"0.0";
//        self.trainManager.dataManager.showData.gradient = @"0";
//        
//        self.trainView.freedomDataView.speed = @"0.0";
//        self.trainView.controlView.slopeView.slope = @"0";
    });
}

#pragma mark - 倒计时
//- (void)treamillCutdownAction:(NSNotification *)x {
//    dispatch_async(dispatch_get_main_queue(), ^{
//        [[kWindow viewWithTag:1000] removeFromSuperview];
//        
//        self.trainView.startButton.hidden = YES;
//        self.trainView.pauseButton.hidden = NO;
//        
//        NSNumber *time = x.object;
//        if (time.intValue == 0) {
//            self.cutdownView.cutdownNumber = @"GO";
//            return;
//        }
//        self.cutdownView.hidden = NO;
//        self.cutdownView.cutdownNumber = [NSString stringWithFormat:@"%@", time];
//        if (time.intValue == 1) {
//            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1* NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
//                self.cutdownView.cutdownNumber = @"GO";
//                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
//                    dispatch_async(dispatch_get_main_queue(), ^{
//                        self.cutdownView.hidden = YES;
//                    });
//                });
//            });
//        }
//    });
//}

#pragma mark - 跑步机开始
- (void)treamillCutdown:(NSInteger)time {
    dispatch_async(dispatch_get_main_queue(), ^{
        [[self.keyWindow viewWithTag:1000] removeFromSuperview];
        
        self.trainView.startButton.hidden = YES;
        self.trainView.pauseButton.hidden = NO;
        
        if (time == 0) {
            self.cutdownView.cutdownNumber = @"GO";
            return;
        }
        self.cutdownView.hidden = NO;
        self.cutdownView.cutdownNumber = [NSString stringWithFormat:@"%ld", (long)time];
        if (time == 1) {
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1* NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                self.cutdownView.cutdownNumber = @"GO";
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    dispatch_async(dispatch_get_main_queue(), ^{
                        self.cutdownView.hidden = YES;
                    });
                });
            });
        }
    });
}

#pragma mark - 跑步机开始
- (void)startAction:(UIButton *)sender {
    dispatch_async(dispatch_get_main_queue(), ^{
        [self.firstStartView removeFromSuperview];
    });
    self.isStart = YES;
//    [self.statusManager treamillStart];
    [self.trainManager.equipmentInfo equipmentStart];
}


#pragma mark - 跑步机开始更新数据，修改isPause状态
- (void)treamDataUpdate {
    NSLog(@"跑步机开始获取数据了");
    self.isPause = NO;
    dispatch_async(dispatch_get_main_queue(), ^{
        self.trainView.startButton.hidden = YES;
        self.trainView.pauseButton.hidden = NO;
        self.slowDownView.hidden = YES;
    });
}

#pragma mark - 跑步机 继续
- (void)treamillGoonAction {
    self.isPause = NO;
    [[self.keyWindow viewWithTag:0x888] removeFromSuperview];
    
    self.slowDownView.hidden = YES;
    [self.trainManager.equipmentInfo equipmentContinue];
}

#pragma mark - 跑步机 暂停
- (void)treamillPauseAction:(id)sender {
    ///如果设备在连接中 中断操作
    if([self checkDeviceStatus]) {
        return;
    }
    [self treamillSuspending];
    [self.trainManager.equipmentInfo equipmentPause];
}

#pragma mark - 跑步机 暂停减速中
- (void)treamillSuspending {
    if (self.isPause) {
        return;
    }
    dispatch_async(dispatch_get_main_queue(), ^{
        self.trainView.freedomDataView.speed = @"0.0";
        self.isPause = YES;
        
        self.slowDownView.hidden = NO;
        [self.keyWindow bringSubviewToFront:self.slowDownView];
    });
}





#pragma mark 弹出挽留弹窗
- (void)backButtonClick {
    NSLog(@"弹出挽留弹窗");
    if (!self.firstStartView.hidden) {
        self.firstStartView.hidden = YES;
    }
    
    dispatch_async(dispatch_get_main_queue(), ^{
        @weakify(self);
        [MRKDeviceConnectAlertView alertFreeEndViewProductID:self.productId action:^(id data) {
            @strongify(self);
            if([data intValue] == 1) {
                [self closeByExerciseReport];
            }
        }];
    });
}


#pragma mark 生成训练报告
- (void)closeByExerciseReport {
    if (self.hasGenerateReport) return;
    self.hasGenerateReport = YES;
    
    ///新用户链路, 是否在链路中
    BOOL firstInto = [self.pageRelationPath isNotBlank];
    self.trainManager.firstInto = firstInto;
    @weakify(self);
    dispatch_async(dispatch_get_main_queue(), ^{
        @strongify(self);
        [self.trainManager requestSettlement];
    });
}



#pragma mark - 手动 按 筋膜枪
- (void)pressAction:(id)sender {
    dispatch_async(dispatch_get_main_queue(), ^{
        self.trainView.jmqAlertView.hidden = NO;
        [self.trainView.infoView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.view.mas_top).offset(kStatusBarHeight + DHPX(10 + 10 + 40));
            make.left.equalTo(@(DHPX(0)));
            make.right.equalTo(@(DHPX(0)));
            make.height.equalTo(@30);
        }];
    });
}

#pragma mark - 完成目标
- (void)finishTarget {
    dispatch_async(dispatch_get_main_queue(), ^{
        if (self.hasFinishtarget) return;
        self.hasFinishtarget = YES;
        
        self.trainManager.hasFinishTarget = YES;
        self.trainManager.isTargetCompletedContinue = self.isTargetCompletedContinue = NO;
        
        /// 完成目标的时候更新一下
        [[MRKDynamicIslandManager shared] update:self];
        
        ///暂停监测
        [self.warnManager suspendTimer];
        
        NSLog(@"finishTarget_finishTarget_finishTarget");
        if ([self.view viewWithTag:kTargetViewTag] && [[self.view viewWithTag:kTargetViewTag] isDisplayedInScreen]) {
            return;
        }
        
        /// 彩条动画
        @weakify(self);
        [self.trainView.targetView finishColorAnimation:self.view completionBlock:^{
            @strongify(self);
            jxt_getSafeMainQueue(^{
                // 播放语音
                [FeedbackGeneratorUtil playSoundWithName:@"Sound_002" type:@"caf"];
                // 完成弹窗
                [self finishTargetView];
            });
        }];
    });
}

- (void)finishTargetView {
    @weakify(self);
    FreedomFinishAlertView *finish = [FreedomFinishAlertView build];
    finish.tag = kTargetViewTag;
    finish.finishBlock = ^{
        @strongify(self);
        [self closeByExerciseReport];
    };
    [finish showIn:self.view];
}



- (void)updateHoop {
//    BOOL checkResult = [self checkMode:self.tyModel];
    BOOL checkResult = [self checkMode:self.trainManager.dataManager.showData];
    if (checkResult) {
        return;
    }
    
    if (self.trainManager.dataManager.showData.isShowElectric) {
        self.trainView.infoView.elec = self.trainManager.dataManager.showData.electric;
    }
    
    [self updateTargetProgressView];
}

//- (BOOL)checkMode:(BaseEquipDataModel *)model {
- (BOOL)checkMode:(TrainingShowData *)model {
    if (self.hasFinishtarget) {
        return YES;
    }
    if (model == nil) { // model 没数据
        return YES;
    }
    int mode = model.mode.intValue;
//    if (self.isFirst == NO) {
//        if (self.randomType == FreedomMode && mode != FreedomMode) {
//            if (self.hasAlertChangeModel) {
//                return YES;
//            }
//            
//            self.hasAlertChangeModel = YES;
//            @weakify(self);
//            [MrkAlertManager showAlert:@""
//                               message:[NSString stringWithFormat:@"当前%@处于「%@」模式\n确认切换模式吗？", [MRKEquipmentTypeData nameFromProductId:self.productId] , mode == CutdownTimeMode ? @"定时计数" : @"定数计时"]
//                                cancel:@"暂不"
//                                ensure:@"确认"
//                           handleIndex:^(NSInteger index) {
//                if (index == 1) {
//                    self.trainManager.isRefresh = NO;
//                    self.trainManager.isJump = NO;//失败后不用自动返回
//                    ///切换模式之前 结算之前的数据，结算完成以后 切换模式
//                    [self.trainManager requestNormalSettlement:^{
//                        @strongify(self);
//                        [self changeMode];
//                    }];
//                } else {
//                    ///直接生成训练记录并跳转到训练报告
//                    [self closeByExerciseReport];
//                }
//            }];
//            @weakify(self);
//            [MrkAlertManager showAlert:@""
//                               message:[NSString stringWithFormat:@"当前%@处于「%@」模式\n确认切换模式吗？", [MRKEquipmentTypeData nameFromProductId:self.productId] , mode == CutdownTimeMode ? @"定时计数" : @"定数计时"]
//                                cancel:@"暂不"
//                                ensure:@"确认"
//                           handleIndex:^(NSInteger index) {
//                if (index == 1) {
//                    self.trainManager.isRefresh = NO;
//                    self.trainManager.isJump = NO;//失败后不用自动返回
//                    ///切换模式之前 结算之前的数据，结算完成以后 切换模式
//                    [self.trainManager requestNormalSettlement:^{
//                        @strongify(self);
//                        [self changeMode];
//                    }];
//                } else {
//                    ///直接生成训练记录并跳转到训练报告
//                    [self closeByExerciseReport];
//                }
//            }];
//            return YES;
//        }
//    }
    
//    if (self.isFirst && self.randomType != mode) {
//        if (self.hasSkipReport) {
//            return YES;
//        }
//        
//        self.hasSkipReport = YES;
//        @weakify(self);
//        [MrkAlertManager showAlert:@""
//                           message:@"检测到你已切换模式\n将结束此训练，并为你生成训练报告"
//                            ensure:@"确认"
//                       handleIndex:^(NSInteger index) {
//            @strongify(self);
//            [self closeByExerciseReport];
//        }];
//        return YES;
    if (self.randomType != mode) {
        if (self.hasAlertChangeModel) {
            return YES;
        }
        
        self.hasAlertChangeModel = YES;
        @weakify(self);
        [MrkAlertManager showAlert:@""
                           message:@"检测到你已切换模式\n将结束此训练，并为你生成训练报告"
                            ensure:@"确认"
                       handleIndex:^(NSInteger index) {
            @strongify(self);
            [self closeByExerciseReport];
        }];
        return YES;
    }
    return NO;
}


/////给设备发送切换模式的指令
//- (void)changeMode {
//    NSLog(@"准备切换模式了");
//    //2.7.0 清除数据，切换模式
//    [self.trainManager resetData];
//    
//    ///飞力士棒 切换模式成功后要发送开始指令
//    if (self.productId.intValue == FLSBEquipment) {
//        @weakify(self);
//        __block RACDisposable *dispose = [[[NSNotificationCenter defaultCenter] rac_addObserverForName:@"MRKSetNormalModeSuccess" object:nil] subscribeNext:^(id x) {
//            @strongify(self);
//            [[BluetoothManager sharedInstance] sendData:[BloothTool MRKStartData] type:self.productId];
//            [dispose dispose];
//        }];
//    }
//    
//    [[NSNotificationCenter defaultCenter] postNotificationName:SetModeNotification object:@{@"type":self.productId, @"mode":@(FreedomMode)}];
//    self.isFirst = YES;
//}


- (void)updateTargetProgressView {
    dispatch_async(dispatch_get_main_queue(), ^{
        if (self.randomType == FreedomMode) {
            return;
        }
        
        if (!self.hasFinishtarget && self.randomType == CutdownTimeMode) {
            int totalTimeSecond = self.trainManager.dataManager.incrementData.totalTime;
            int number = self.targetModel.number.intValue;
            float progress = (totalTimeSecond*1.0)/number;
            self.trainView.targetView.progress = MIN(progress, 1.0);
        }
        
        if (!self.hasFinishtarget && self.randomType == CutdownNumberMode) {
            if (self.productId.intValue == SkipRopeEquipment ||
                self.productId.intValue == HoopEquipment ||
                self.productId.intValue == FLSBEquipment ) {
                
                int count = self.trainManager.dataManager.incrementData.totalNum;
                int number = self.targetModel.number.intValue;
                float progress = (count*1.0)/number;
                self.trainView.targetView.progress = MIN(progress, 1.0);
            } else {
            
                int totalDistance = self.trainManager.dataManager.incrementData.totalDistance;
                float number = self.targetModel.number.doubleValue *1000;
                float progress = (totalDistance*1.0)/number;
                self.trainView.targetView.progress = MIN(progress, 1.0);
            }
        }
        
        NSLog(@"self.trainView.targetView.progress ======== %f", self.trainView.targetView.progress);
        if (!self.hasFinishtarget && self.trainManager.dataManager.showData.mode.intValue > 0 && self.trainManager.dataManager.showData.finishTarget.boolValue) {
            NSLog(@"数据tyModel判读_完成训练");
            /// 用于模式结束的时候，显示已经完成目标，但实际上并未完成目标，强制修改为目标值
            if (self.randomType == CutdownTimeMode) {
                int totalTime = MAX(self.targetModel.number.intValue, self.trainManager.dataManager.incrementData.totalTime);
                self.trainManager.dataManager.incrementData.totalTime = totalTime;
                self.trainManager.dataManager.showData.totalTime = [MRKToolKit MSTimeStrFromSecond:totalTime];
            }
            
            if (self.randomType == CutdownNumberMode) {
                if (self.productId.intValue == SkipRopeEquipment ||
                    self.productId.intValue == HoopEquipment ||
                    self.productId.intValue == FLSBEquipment ) {
                    int totalNum = MAX(self.targetModel.number.intValue, self.trainManager.dataManager.incrementData.totalNum);
                    self.trainManager.dataManager.incrementData.totalNum = totalNum;
                    self.trainManager.dataManager.showData.totalNum = [NSString stringWithFormat:@"%d", totalNum];
                } else {
                    double totalDistance = MAX(self.targetModel.number.doubleValue *1000, self.trainManager.dataManager.incrementData.totalDistance);
                    self.trainManager.dataManager.showData.totalDistance = [NSString convertDecimalNumber:[NSString stringWithFormat:@"%lf", totalDistance] num:2 dividing:@"1000"];
                }
            }
            
            ///更新一下UI
            self.trainView.targetView.progress = 1.0; /// 进度条打满
            [self updateUI];
            [self finishTarget];
            return;
        }
        
        if (!self.hasFinishtarget && self.trainView.targetView.progress >= 1.0) {
            [self finishTarget];
            NSLog(@"目标判断_完成训练");
            return;
        }
    });
}

#pragma mark - MRKHeartWarnManagerDelegate
- (void)alertHeartRateWarnView:(EquipmentDetialModel *)eqModel {
    self.trainManager.equipmentInfo.detailModel.productID = self.productId;
    ///
    [MrkAlertManager showHeartRateWarnAlertInView:self.view
                                      detailModek:self.trainManager.equipmentInfo.detailModel
                                      handleIndex:^(NSInteger index) {
        [self setWarnOpen:index];
    }];
}

- (void)setWarnOpen:(NSInteger)index {
    [MRKRequestServiceData setHeartRateWarnValueStatus:@{@"isOpen":@(index)}
                                               success:^(id data) {
        /// 刷新数据
        [[NSNotificationCenter defaultCenter] postNotificationName:@"UpdateHeartWarnDetialNotification" object:nil];
        
        /// 开启以后 开始监测
        if (index == 1) {
            self.warnManager.warnModel.isOpen = @1;
            [self.warnManager startTimer];
        }
    } failure:^(id data) {
        
    }];
}

///跑步机 暂停按钮操作判断是否在连接中
- (BOOL)checkDeviceStatus {
    ///如果设备在连接中，提示不让操作
    if(self.deviceStatus.intValue == DeviceScaning
       || self.deviceStatus.intValue == DeviceConnecting
       || self.deviceStatus.intValue == DeviceAutoConnecting) {
        
        [AppDelegate errorView:@"设备正在连接中，请稍后再试"];
        return YES;
    }
    return NO;
}

- (NSNumber *)trainTargetNumber {
    if (self.randomType == CutdownTimeMode) {
        return @(self.targetModel.number.intValue);
    }
    if (self.randomType == CutdownNumberMode) {
        switch (self.productId.intValue) {
            case SkipRopeEquipment:
            case FLSBEquipment:
            case HoopEquipment:
                return @(self.targetModel.number.intValue);
                break;
            case TreadmillEquipment:
            case BicycleEquipment:
            case EllipticalEquipment:
            case BoatEquipment:
            case StairClimbEquipment:
                return @(self.targetModel.number.doubleValue * 1000);//距离
                break;
            default:
                break;
        }
    }
    return @0;
}

#pragma mark - init
- (MRKHeartWarnManager *)warnManager {
    if(!_warnManager) {
        _warnManager = [[MRKHeartWarnManager alloc]init];
        _warnManager.productId = self.productId;
        _warnManager.delegate = self;
    }
    return _warnManager;
}

- (MRKTrainManager *)trainManager {
    if(!_trainManager) {
        _trainManager = [[MRKTrainManager alloc] init];
        _trainManager.isOpenAutoConnect = YES;
        _trainManager.autoConnectNeedLoading = YES;
    }
    return _trainManager;
}

#pragma mark - nav delegate
- (BOOL)viewControllerIsNeedNavBar:(MRKBaseController *)viewController {
    return NO;
}

- (UIColor *)mrkNavigationBarBackgroundColor:(MRKNavigationBar *)navigationBar {
    return [UIColor clearColor];
}

- (UIStatusBarStyle)navControllerStatusBarStyle:(MRKBaseController *)viewController {
    return UIStatusBarStyleLightContent;
}
@end





@interface FramdomExerciseView()
//@property (nonatomic , strong) DeviceInfoView *infoView;
//@property (nonatomic , strong) TargetProgressView *targetView; //目标进度
//@property (nonatomic , strong) FreedomDataView *freedomDataView;
//@property (nonatomic , strong) FramdomControlView *controlView;
//@property (nonatomic , strong) UIButton *endButton; ///结束训练/跑步机的结束按钮
/////
//@property (nonatomic , strong) UIButton *startButton;
//@property (nonatomic , strong) UIButton *pauseButton;
//
//@property (nonatomic , strong) MRKHeartItemView *rateView;//心率view
//@property (nonatomic , strong) PowerInfoView *powerView;
//@property (nonatomic , strong) JMQPressAlertView *jmqAlertView;
@end

@implementation FramdomExerciseView

- (void)dealloc {
    NSLog(@"😊😊😊-----dealloc-----😊😊😊%@" , NSStringFromClass([self class]));
}


- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        ///
        [self addSubview:self.infoView];
        [self.infoView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.mas_top).offset(StatusHeight +13);
            make.left.right.equalTo(@0);
            make.height.equalTo(@(60));
        }];

        self.targetView.productId = self.productId;
        [self addSubview:self.targetView];
        [self.targetView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.infoView.mas_bottom).offset(WKDHPX(30));
            make.left.right.equalTo(@0);
            make.height.equalTo(@((50)));
        }];
        
        [self addSubview:self.freedomDataView];
        [self.freedomDataView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.targetView.mas_bottom).offset(DHPX(30));
            make.left.right.equalTo(@(0));
        }];
        
        RAC(self.controlView, speed) = RACObserve(self.freedomDataView, speed);
        RAC(self.controlView, isPause) = RACObserve(self, isPause);
        [self addSubview:self.controlView];
        [self.controlView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.freedomDataView.mas_bottom).offset(DHPX(30));
            make.left.right.equalTo(@(0));
            make.height.equalTo(@(WKDHPX(125)));
        }];
        
        [self addSubview:self.endButton];
        [self.endButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.mas_equalTo(self);
            make.width.mas_equalTo(WKDHPX(300));
            make.height.mas_equalTo(WKDHPX(50));
            make.bottom.mas_equalTo(DHPX(-40));
        }];
        
        ///添加心率模块
        [self addSubview:self.rateView];
        [self.rateView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.infoView.mas_top);
            make.right.equalTo(@(0));
            make.size.mas_equalTo(CGSizeMake(126, 60));
        }];
        self.rateView.heartView.isShowHeartRateAlert = YES;
        self.rateView.heartView.layerTipView = self;
    }
    return self;
}

- (void)setEqModel:(EquipmentDetialModel *)eqModel{
    _eqModel = eqModel;
    
    NSString *name = [BluePeripheral connectDeviceNameOfType:eqModel.productID];
    NSString *modelIdName = [eqModel.bluetoothAlias isNotBlank] ? eqModel.bluetoothAlias : (name ?: @"");
    self.infoView.infoName = [NSString stringWithFormat:@"设备型号：%@", modelIdName];
  
    self.freedomDataView.eqModel = eqModel;
    self.controlView.eqModel = eqModel;
    
    ///刷新力量站UI
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        self.freedomDataView.equmentName = name;
    });
}

- (void)setRandomType:(NSInteger)randomType{
    _randomType = randomType;
    self.targetView.hidden = (randomType == FreedomMode);
}

- (void)setTargetModel:(TrainTargetModel *)targetModel{
    _targetModel = targetModel;
    
    NSString *data = self.randomType == CutdownTimeMode ? targetModel.showText : targetModel.number;
    NSString *unit = self.randomType == CutdownTimeMode ? @"" : [self unit];
    self.targetView.title = [NSString stringWithFormat:@"目标 %@%@", data, unit];
}

- (NSString *)unit {
    switch (self.productId.intValue) {
        case SkipRopeEquipment:
        case FLSBEquipment:
            return @"个";
            break;
        case HoopEquipment :
            return @"圈";
            break;
        default:
            return @"公里";
            break;
    }
    return @"";
}

- (void)setProductId:(NSString *)productId{
    _productId  = productId;
    
    self.targetView.productId = productId;
    self.controlView.productId = productId;
    if (productId.intValue == TreadmillEquipment){
        self.endButton.backgroundColor = UIColor.clearColor;
        [self.endButton setTitle:@"" forState:UIControlStateNormal];
        [self.endButton setImage:[UIImage imageNamed:@"icon_end_title"] forState:UIControlStateNormal];
        [self.endButton mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.mas_left).offset(DHPX(65));
            make.width.height.equalTo(@(DHPX(90)));
            make.bottom.equalTo(self.mas_bottom).offset(-30);
        }];
        
        [self addSubview:self.pauseButton];
        [self.pauseButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(self.mas_right).offset(-DHPX(65));
            make.width.height.equalTo(@(DHPX(90)));
            make.bottom.equalTo(self.mas_bottom).offset(-30);
        }];
        
        self.startButton.hidden = YES;
        [self addSubview:self.startButton];
        [self.startButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(self.mas_right).offset(-DHPX(65));
            make.width.height.equalTo(@(DHPX(90)));
            make.bottom.equalTo(self.mas_bottom).offset(-30);
        }];
        
    } else if (productId.intValue == PowerEquipment){
        ///力量站隐藏掉TargetView
        self.targetView.hidden = YES;
        NSString *equmentName = [BluePeripheral connectDeviceNameOfType:productId];
        if ([equmentName localizedCaseInsensitiveContainsString:@"-P03"]) {
            [self addSubview:self.powerView];
            [self.powerView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(self.infoView.mas_bottom).offset(WKDHPX(10));
                make.left.right.equalTo(@0);
                make.height.equalTo(@((50)));
            }];
        }
    } else if (productId.intValue == JinMoQiangEquipment){
        ///筋膜枪隐藏掉TargetView
        self.targetView.hidden = YES;
        [self addSubview:self.jmqAlertView];
        [self.jmqAlertView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.infoView.mas_bottom).offset(WKDHPX(20));
            make.left.right.equalTo(@0);
            make.height.equalTo(@(50));
        }];
    }
}

//- (void)setDataModel:(BaseEquipDataModel *)dataModel {
- (void)setShowData:(TrainingShowData *)showData {
    _showData = showData;
    
    dispatch_async(dispatch_get_main_queue(), ^{
        self.freedomDataView.model = showData;
        self.controlView.model = showData;
        
        switch (self.productId.intValue) {
            case BicycleEquipment:
            case EllipticalEquipment:
            case StairClimbEquipment:
            case BoatEquipment: {
                
                [self updateCar];
            } break;
                
            case TreadmillEquipment: {
                
                [self updateWalkMachine];
            } break;
                
            case JinMoQiangEquipment: {
                
                [self updateJMQ];
            }  break;
                
            case HoopEquipment:
            case SkipRopeEquipment:
            case FLSBEquipment: {
                
                [self updateHoop];
            }  break;
                
            case PowerEquipment: {
                
                [self updatePower];
            }  break;
                
            default: break;
        }
    });
}


- (void)updateWalkMachine {
    if (![[[UIViewController keywindow] viewWithTag:0x111] isHidden]) {
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            dispatch_async(dispatch_get_main_queue(), ^{
                [[UIViewController keywindow] viewWithTag:0x111].hidden = YES;
            });
        });
    }
    
    if (self.controlView.isControl) {
        return;
    }
    
    self.freedomDataView.speed = self.showData.speed;
    self.controlView.slopeView.slope = self.showData.gradient;
    
    if (self.isPause) {
        self.freedomDataView.speed = self.showData.speed;
        self.controlView.slopeView.slope = @"0";
    }
}

- (void)updateHoop {
    if (self.updateHoopBlock){
        self.updateHoopBlock();
    }
}

- (void)updateJMQ {
    if (!self.jmqAlertView.isHidden) {
        self.jmqAlertView.hidden = YES;
    }

    if (self.controlView.isControl) {
        return;
    }
    
    self.controlView.dragSlider.topText = self.showData.gear;
    self.infoView.elec = self.showData.electric;
}

- (void)updateCar {
    if (self.controlView.isControl) {
        return;
    }
    
    if (!self.controlView.dragSlider.isHidden && !self.controlView.dragSlider.recomposing) {
        self.controlView.dragSlider.topText = self.showData.resistance;
    }
    
    if(!self.controlView.dragSlider.isHidden && !self.controlView.slopeSlider.recomposing) {
        self.controlView.slopeSlider.topText = self.showData.gradient;
    }
}

- (void)updatePower {
    NSString *equmentName = [BluePeripheral connectDeviceNameOfType:self.productId];
    if ([equmentName localizedCaseInsensitiveContainsString:@"-P99"]) { // P99 显示当前电量
        self.infoView.elec = self.showData.electric;
    }
    
    if (self.controlView.isControl) {
        return;
    }
    
    if (_powerView) {
        self.powerView.elec = self.showData.electric;
        self.powerView.deviceStatus = @(self.showData.mode.intValue);
    }
}

- (DeviceInfoView *)infoView {
    if (!_infoView) {
        _infoView = [[DeviceInfoView alloc] init];
        @weakify(self);
        _infoView.dataTipMessageBlock = ^{
            [self_weak_ dataTipMessage];
        };
    }
    return _infoView;
}

- (void)dataTipMessage {
    MRKFreedomTipMessageView *view = [[MRKFreedomTipMessageView alloc] init];
    [view showIn:self];
}

- (PowerInfoView *)powerView {
    if (!_powerView) {
        _powerView = [[PowerInfoView alloc] init];
    }
    return _powerView;
}

- (JMQPressAlertView *)jmqAlertView {
    if (!_jmqAlertView) {
        _jmqAlertView = [[JMQPressAlertView alloc] init];
        _jmqAlertView.alertString = @"请先手动开启筋膜枪，即可调节档位";
    }
    return _jmqAlertView;
}

- (MRKHeartItemView *)rateView {
    if (!_rateView) {
        _rateView = [[MRKHeartItemView alloc] init];
    }
    return _rateView;
}

- (TargetProgressView *)targetView{
    if (!_targetView) {
        _targetView = [[TargetProgressView alloc] init];
    }
    return _targetView;
}

- (FreedomDataView *)freedomDataView {
    if (!_freedomDataView) {
        _freedomDataView= [[FreedomDataView alloc] init];
        @weakify(self);
        _freedomDataView.updateSpeedBlock = ^(NSString * _Nonnull speed) {
            self_weak_.controlView.speed = speed;
        };
    }
    return _freedomDataView;
}

- (FramdomControlView *)controlView {
    if (!_controlView) {
        _controlView= [[FramdomControlView alloc] init];
        @weakify(self);
        _controlView.treamillSpeedActionBlock = ^(NSString * _Nonnull speed) {
            self_weak_.freedomDataView.speed = speed;
        };
    }
    return _controlView;
}

- (UIButton *)pauseButton{
    if (!_pauseButton) {
        _pauseButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_pauseButton setImage:[UIImage imageNamed:@"icon_pause_title"] forState:UIControlStateNormal];
        [_pauseButton addTarget:self action:@selector(treamillPauseAction:) forControlEvents:UIControlEventTouchUpInside];
//        icon_end_title
    }
    return _pauseButton;
}

- (UIButton *)startButton{
    if (!_startButton) {
        _startButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_startButton setImage:[UIImage imageNamed:@"icon_carry_on_title"] forState:UIControlStateNormal];
        [_startButton addTarget:self action:@selector(treamillGoonAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _startButton;
}

- (UIButton *)endButton{
    if (!_endButton) {
        _endButton = [UIButton buttonWithType:UIButtonTypeCustom];
        _endButton.backgroundColor = MainAppColor;
        _endButton.layer.cornerRadius = WKDHPX(50)/2;
        [_endButton setTitle:@"结束训练" forState:UIControlStateNormal];
        _endButton.titleLabel.font = [UIFont fontWithName:@"Medium" size:18.0];
        [_endButton addTarget:self action:@selector(backClick) forControlEvents:UIControlEventTouchUpInside];
    }
    return _endButton;
}

#pragma mark - 跑步机 继续
- (void)treamillGoonAction:(id)sender {
    if (self.treamillGoonBlock){
        self.treamillGoonBlock(sender);
    }
}

#pragma mark - 跑步机 暂停
- (void)treamillPauseAction:(id)sender {
    if (self.treamillPauseBlock){
        self.treamillPauseBlock(sender);
    }
}

- (void)backClick{
    if (self.backClickBlock){
        self.backClickBlock();
    }
}

- (void)sendData{
    [self.controlView sendData];
}


@end








@interface FramdomControlView()<SliderViewDelegate>
@property (nonatomic, assign) BOOL isControl;
@end

@implementation FramdomControlView

- (void)dealloc {
    NSLog(@"😊😊😊-----dealloc-----😊😊😊%@" , NSStringFromClass([self class]));
}

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        
    }
    return self;
}

- (void)layoutSubviews{
    [super layoutSubviews];
}

- (void)setEqModel:(EquipmentDetialModel *)eqModel{
    _eqModel = eqModel;
    
    UIView *pointerView = nil;
    NSInteger productID = eqModel.productID.intValue;
    switch (productID) {
        case BoatEquipment:
        case BicycleEquipment:
        case EllipticalEquipment:
        case StairClimbEquipment:
        {
            self.dragSlider.hidden = !(self.eqModel.isElectromagneticControl && self.eqModel.showResistance.boolValue);
            if (!self.dragSlider.hidden){
                self.dragSlider.sliderType = DragSlider;
                self.dragSlider.eqModel = eqModel;
                [self addSubview:self.dragSlider];
                [self.dragSlider mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.top.equalTo(self.mas_top);
                    make.left.right.equalTo(@0);
                    make.height.equalTo(@(WKDHPX(50)));
                }];
                pointerView = self.dragSlider;
            }else{
                [self.dragSlider removeFromSuperview];
            }
            
            self.slopeSlider.hidden = !(self.eqModel.isElectromagneticControl && self.eqModel.showSlope.boolValue);
            if (!self.slopeSlider.hidden){
                self.slopeSlider.eqModel = eqModel;
                [self addSubview:self.slopeSlider];
                [self.slopeSlider mas_makeConstraints:^(MASConstraintMaker *make) {
                    if (pointerView){
                        make.top.equalTo(pointerView.mas_bottom).offset(WKDHPX(25));
                    }else{
                        make.top.equalTo(self.mas_top);
                    }
                    make.left.right.equalTo(@0);
                    make.height.equalTo(@(WKDHPX(50)));
                }];
            }else{
                [self.slopeSlider removeFromSuperview];
            }
        } break;
            
        case TreadmillEquipment:{
            self.speedView.hidden = !(self.eqModel.isElectromagneticControl && self.eqModel.showSpeed.boolValue);
            if (!self.speedView.hidden){
                [self addSubview:self.speedView];
                [self.speedView mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.top.equalTo(self.mas_top);
                    make.left.right.equalTo(@0);
                    make.height.equalTo(@(WKDHPX(50)));
                }];
                pointerView = self.speedView;
            }else{
                [self.speedView removeFromSuperview];
            }
            
            self.slopeView.hidden = !(self.eqModel.isElectromagneticControl && self.eqModel.showSlope.boolValue);
            if (!self.slopeView.hidden){
                self.slopeView.eqModel = eqModel;
                [self addSubview:self.slopeView];
                [self.slopeView mas_makeConstraints:^(MASConstraintMaker *make) {
                    if (pointerView){
                        make.top.equalTo(pointerView.mas_bottom).offset(WKDHPX(25));
                    }else{
                        make.top.equalTo(self.mas_top);
                    }
                    make.left.right.equalTo(@0);
                    make.height.equalTo(@(WKDHPX(50)));
                }];
            }else{
                [self.slopeView removeFromSuperview];
            }
        } break;
            
        case JinMoQiangEquipment:{
            self.dragSlider.hidden = !(self.eqModel.isElectromagneticControl && self.eqModel.showGear.boolValue);
            if (!self.dragSlider.hidden){
                self.dragSlider.sliderType = GearSlider;
                self.dragSlider.eqModel = eqModel;
                [self addSubview:self.dragSlider];
                [self.dragSlider mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.top.equalTo(self.mas_top);
                    make.left.right.equalTo(@0);
                    make.height.equalTo(@(WKDHPX(50)));
                }];
            }else{
                [self.dragSlider removeFromSuperview];
            }
        } break;
        default: break;
    }
}

- (TreamillSpeedView *)speedView{
    if (!_speedView){
        TreamillSpeedView *speedView = [[TreamillSpeedView alloc] init];
        @weakify(self);
        speedView.treamillSpeedBlock = ^(NSString * _Nonnull speed) {
            [self_weak_ treamillChangeSpeedAction:speed];
        };
        _speedView = speedView;
    }
    return _speedView;
}

- (TreamillSlopeView *)slopeView{
    if (!_slopeView){
        TreamillSlopeView *slopeView = [[TreamillSlopeView alloc] init];
        @weakify(self);
        [slopeView.increaseSignal subscribeNext:^(id x) {
            [self_weak_ treamillIncreaseSlopeAction];
        }];
        [slopeView.reduceSignal subscribeNext:^(id x) {
            [self_weak_ treamillReduceSlopeAction];
        }];
        _slopeView = slopeView;
    }
    return _slopeView;
}

- (SliderView *)dragSlider {
    if (!_dragSlider) {
        SliderView *sl = [[SliderView alloc] init];
        sl.sliderType = DragSlider;
        sl.delegate = self;
        _dragSlider = sl;
    }
    return _dragSlider;
}

- (SliderView *)slopeSlider {
    if(!_slopeSlider) {
        SliderView *sl = [[SliderView alloc] init];
        sl.sliderType = SlopeSlider;
        sl.delegate = self;
        _slopeSlider = sl;
    }
    return _slopeSlider;
}


#pragma mark -Treamill 修改速度
- (void)treamillChangeSpeedAction:(NSString *)speed {
    if (self.isPause) {
        [AppDelegate errorView:@"请先启动跑步"];
        return;
    }
    
    if (self.treamillSpeedActionBlock){
        self.treamillSpeedActionBlock(speed);
    }
    [self sendData];
}
#pragma mark -Treamill 增加坡度
- (void)treamillIncreaseSlopeAction{
    if (self.isPause) {
        [AppDelegate errorView:@"请先启动跑步"];
        return;
    }
    self.isControl = YES;
    [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(sendData) object:nil];
   
    int slope = self.slopeView.slope.intValue;
    if (slope < self.eqModel.maxSlope.intValue) {
        slope = slope + 1;
    }
    if (slope >= self.eqModel.maxSlope.intValue) {
        [AppDelegate errorView:kAlertMaxSlope];
    }
    self.slopeView.slope = [NSString stringWithFormat:@"%d", slope];
    [self performSelector:@selector(sendData) withObject:nil afterDelay:1.0];
}
#pragma mark -Treamill 减少坡度
- (void)treamillReduceSlopeAction{
    if (self.isPause) {
        [AppDelegate errorView:@"请先启动跑步"];
        return;
    }
    self.isControl = YES;
    [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(sendData) object:nil];
    int slope = self.slopeView.slope.intValue;
    if (slope > self.eqModel.minSlope.intValue) {
        slope = slope - 1;
    }
    if (slope <= self.eqModel.minSlope.intValue) {
        [AppDelegate errorView:kAlertMinSlope];
    }
    self.slopeView.slope = [NSString stringWithFormat:@"%d", slope];
    [self performSelector:@selector(sendData) withObject:nil afterDelay:1.0];
}


#pragma mark - 增加速度/坡度
- (void)increaseAction:(SliderView *)sender {
    self.isControl = YES;
    [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(sendData) object:nil];
    if (sender.sliderType == DragSlider) {
        if (self.productId.intValue == JinMoQiangEquipment){
            if (self.eqModel.maxGear.intValue == 0) return;
            int grade = [self.dragSlider.topText intValue];
            if (grade < self.eqModel.maxGear.intValue) {
                grade = grade + 1;
            }
            if (grade >= self.eqModel.maxGear.intValue) {
                [AppDelegate errorView:kAlertMaxGear];
                grade = self.eqModel.maxGear.intValue;
            }
            self.dragSlider.topText = [NSString stringWithFormat:@"%d", grade];
        }else{
            if (self.eqModel.maxResistance.intValue == 0) return;
            int drag = [self.dragSlider.topText intValue];
            if (drag < self.eqModel.maxResistance.intValue) {
                drag = drag + 1;
            }
            if (drag >= self.eqModel.maxResistance.intValue) {
                [AppDelegate errorView:kAlertMaxSlope];
                drag = self.eqModel.maxResistance.intValue;
            }
            self.dragSlider.topText = [NSString stringWithFormat:@"%d", drag];
        }
    }
    
    if (sender.sliderType == SlopeSlider) {
        if (self.eqModel.maxSlope.intValue == 0) return;
        int slope = [self.slopeSlider.topText intValue];
        if (slope < self.eqModel.maxSlope.intValue) {
            slope = slope + 1;
        }
        if (slope >= self.eqModel.maxSlope.intValue) {
            [AppDelegate errorView:kAlertMaxSlope];
            slope = self.eqModel.maxSlope.intValue;
        }
        self.slopeSlider.topText = [NSString stringWithFormat:@"%d", slope];
    }
    [self performSelector:@selector(sendData) withObject:nil afterDelay:1.0];
}


#pragma mark - 减小速度/坡度
- (void)reduceAction:(SliderView *)sender {
    self.isControl = YES;
    [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(sendData) object:nil];
    if (sender.sliderType == DragSlider) {
        if (self.productId.intValue == JinMoQiangEquipment){
            int grade = [self.dragSlider.topText intValue];
            if (grade > self.eqModel.minGear.intValue) {
                grade = grade - 1;
            }
            if (grade <= self.eqModel.minGear.intValue) {
                [AppDelegate errorView:kAlertMinGear];
            }
            self.dragSlider.topText = [NSString stringWithFormat:@"%d" , grade];
        }else{
            int drag = [self.dragSlider.topText intValue];
            if (drag > self.eqModel.minResistance.intValue) {
                drag = drag - 1;
            }
            if (drag <= self.eqModel.minResistance.intValue) {
                [AppDelegate errorView:kAlertMinResistance];
                drag = self.eqModel.minResistance.intValue;
            }
            self.dragSlider.topText = [NSString stringWithFormat:@"%d" , drag];
        }
    }
    
    if (sender.sliderType == SlopeSlider) {
        int slope = [self.slopeSlider.topText intValue];
        if (slope > self.eqModel.minSlope.intValue) {
            slope = slope - 1;
        }
        if (slope <= self.eqModel.minSlope.intValue) {
            [AppDelegate errorView:kAlertMinSlope];
            slope = self.eqModel.minSlope.intValue;
        }
        self.slopeSlider.topText = [NSString stringWithFormat:@"%d" , slope];
    }

    [self performSelector:@selector(sendData) withObject:nil afterDelay:1.0];
}

///拖动数据
- (void)dragDataAction:(SliderView *)sender{
    [self sendData];
}

- (void)sendData {
    switch (self.productId.intValue) {
        case EllipticalEquipment:
        case BicycleEquipment:
        case BoatEquipment:
        case StairClimbEquipment:
        {
            ///
            self.dragSlider.recomposing = YES;
            self.slopeSlider.recomposing = YES;
            ///延时600ms后修正状态, 设备数据过来可以显示
            dispatch_time_t delayTime = dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.6 * NSEC_PER_SEC));
            dispatch_after(delayTime, dispatch_get_main_queue(), ^{
                self.dragSlider.recomposing = NO;
                self.slopeSlider.recomposing = NO;
            });
            
            int gradient = self.slopeSlider.topText.intValue;
            int resistance = self.dragSlider.topText.intValue;
            NSMutableDictionary *parms = [NSMutableDictionary dictionaryWithDictionary: @{
                BlueDeviceType:self.productId
            }];
            ///23-11-21 增加华为协议 椭圆机坡度调节
            if (self.eqModel.communicationProtocol.intValue == FTMSCommunicationProtocol) {
                ///华为协议,调节坡度和阻力是两条指令，为了防止传入两个参数，导致操作一个发送两次指令，判断当前调整的是哪个参数
                if(self.model.gradient.intValue != gradient) { ///坡度有调整
                    [parms setObject:@(gradient) forKey:Slope];
                }
                if(self.model.resistance.intValue != resistance) { ///阻力有调整
                    [parms setObject:@(resistance) forKey:Resistance];
                }
            } else {
                ///智健协议（车表类协议坡度和阻力要同时设置）
                [parms setObject:@(resistance) forKey:Resistance];
                [parms setObject:@(gradient) forKey:Slope];
            }
            
            [[NSNotificationCenter defaultCenter] postNotificationName:SetResistanceSlopeSpeedNotification object:parms];
        } 
            break;
            
        case TreadmillEquipment:
        {
            double speed = self.speed.doubleValue;
            int gradient = self.slopeView.slope.intValue;
            NSDictionary *parms = @{
                Speed:@(speed * 10),
                Slope:@(gradient),
                BlueDeviceType:self.productId
            };
            
            [[NSNotificationCenter defaultCenter] postNotificationName:SetResistanceSlopeSpeedNotification object:parms];
        }
            break;
            
        case JinMoQiangEquipment:
        {
            int grade = self.dragSlider.topText.intValue;
            NSDictionary *parms = @{
                Gear:@(grade),
                BlueDeviceType:self.productId
            };
            [[NSNotificationCenter defaultCenter] postNotificationName:SetResistanceSlopeSpeedNotification object:parms];
        } 
            break;
            
        default:
            break;
    }
    
    self.isControl = NO;
}

@end
