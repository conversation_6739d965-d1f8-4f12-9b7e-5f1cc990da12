//
//  FreedomDataView.m
//  Student_IOS
//
//  Created by MacPro on 2021/8/4.
//

#import "FreedomDataView.h"
#import "Lottie_OC.h"
#import "MrkProgressPopView.h"




@interface FreedomDataView ()
@property (nonatomic , strong) NSString *type;

@property (nonatomic , strong) FreedomListView *timeView;
@property (nonatomic , strong) FreedomListView *kcalView;
@property (nonatomic , strong) FreedomListView *distanceView;
@property (nonatomic , strong) FreedomListView *dragView;
@property (nonatomic , strong) FreedomListView *spmView;
@property (nonatomic , strong) FreedomListView *countView;
@property (nonatomic , strong) FreedomListView *speedView;
@end

@implementation FreedomDataView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
     
    }
    return self;
}

#pragma mark -------- 设置数据--------------
- (void)setEqModel:(EquipmentDetialModel *)eqModel {
    if (!eqModel) { return; }
    _eqModel = eqModel.copy;
    
    self.type = eqModel.productID;
    switch (self.type.intValue) {
        case BicycleEquipment:
        case EllipticalEquipment:
        case BoatEquipment:{
            
            if (!eqModel.isSupportResistanceEcho ) {
                [self bicycleLayoutUINoDrag];
            }else{
                [self bicycleLayoutUI];
            }
        }
            break;
        case TreadmillEquipment:{
            
            [self treadmillUI];
        }
            break;
        case StairClimbEquiment:{
            
            [self stairClimbUI];
        }
            break;
        case SkipRopeEquipment:
        case FLSBEquipment:{
            
            [self skip];
        }
            break;
        case HoopEquipment:{
            
            [self hoop];
        }
            break;
        case JinMoQiangEquipment:{
            
            [self jmqUI];
        }
            break;
        case PowerEquipment:{
            NSString *equmentName = [BluePeripheral connectDeviceNameOfType:eqModel.productID];
            self.equmentName = equmentName;
            if ([equmentName localizedCaseInsensitiveContainsString:@"-P01"]) {
                [self powerP01UI];
                self.countView.bottomText = @"阻力(Lv)";
            } else if ([equmentName localizedCaseInsensitiveContainsString:@"-P02"]) {
                [self powerUI];
                self.countView.bottomText = @"当前重量(公斤)";
            } else if ([equmentName localizedCaseInsensitiveContainsString:@"-P03"]) {
                [self powerUI];
                self.countView.bottomText = @"档位";
            } else if ([equmentName localizedCaseInsensitiveContainsString:@"-P99"]) {
                [self powerP01UI];
                self.countView.bottomText = @"当前重量(公斤)";
            }
        }
            break;
        default: break;
    }
}

#pragma mark  === 单车/椭圆机/划船机 ====
- (void)bicycleLayoutUI {
    [self addSubview:self.timeView];
    [self addSubview:self.kcalView];
    [self addSubview:self.distanceView];
    [self addSubview:self.dragView];
    [self addSubview:self.spmView];
    [self addSubview:self.countView];
    
    NSMutableArray*topArr = @[self.timeView, self.kcalView, self.distanceView].mutableCopy;
    [topArr mas_distributeViewsAlongAxis:MASAxisTypeHorizontal withFixedSpacing:5 leadSpacing:10 tailSpacing:10];
    [topArr mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@(WKDHPX(10)));
        make.height.equalTo(@(WKDHPX(80)));
    }];
    
    NSMutableArray*bottomArr = @[self.dragView, self.spmView, self.countView].mutableCopy;
    [bottomArr mas_distributeViewsAlongAxis:MASAxisTypeHorizontal withFixedSpacing:5 leadSpacing:10 tailSpacing:10];
    [bottomArr mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@(WKDHPX(100)));
        make.height.equalTo(@(WKDHPX(80)));
        make.bottom.equalTo(@(-WKDHPX(10)));
    }];
    
    self.timeView.topLab.font = [UIFont fontWithName:Bebas_Font size:30];
    self.timeView.bottomLab.font = [UIFont fontWithName:fontNamePing size:12];
    self.timeView.bottomText = @"训练时长";
    
    self.kcalView.topLab.font = [UIFont fontWithName:Bebas_Font size:30];
    self.kcalView.bottomLab.font = [UIFont fontWithName:fontNamePing size:12];
    self.kcalView.bottomText = @"消耗(千卡)";;
    
    self.distanceView.topLab.font = [UIFont fontWithName:Bebas_Font size:30];
    self.distanceView.bottomLab.font = [UIFont fontWithName:fontNamePing size:12];
    self.distanceView.bottomText = @"距离(公里)";
    
    self.dragView.topLab.font = [UIFont fontWithName:Bebas_Font size:30];
    self.dragView.bottomLab.font = [UIFont fontWithName:fontNamePing size:12];
    self.dragView.bottomText = @"阻力(Lv)";
    
    self.spmView.topLab.font = [UIFont fontWithName:Bebas_Font size:30];
    self.spmView.bottomLab.font = [UIFont fontWithName:fontNamePing size:12];
    self.spmView.bottomText = self.type.intValue == BoatEquipment ? @"桨频(spm)" : @"踏频(rpm)";
    
    self.countView.topLab.font = [UIFont fontWithName:Bebas_Font size:30];
    self.countView.bottomLab.font = [UIFont fontWithName:fontNamePing size:12];
    self.countView.bottomText = self.type.intValue == BoatEquipment ? @"总桨数" : @"总踏数";
}

//单车/椭圆机 可调节阻力
- (void)bicycleLayoutUINoDrag {
    [self addSubview:self.timeView];
    [self addSubview:self.kcalView];
    [self addSubview:self.distanceView];
    [self addSubview:self.spmView];
    [self addSubview:self.countView];
    
    NSMutableArray*topArr = @[self.timeView, self.kcalView].mutableCopy;
    [topArr mas_distributeViewsAlongAxis:MASAxisTypeHorizontal withFixedSpacing:5 leadSpacing:10 tailSpacing:10];
    [topArr mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@(WKDHPX(10)));
        make.height.equalTo(@(WKDHPX(80)));
    }];
    
    
    NSMutableArray*bottomArr = @[self.distanceView, self.spmView, self.countView].mutableCopy;
    [bottomArr mas_distributeViewsAlongAxis:MASAxisTypeHorizontal withFixedSpacing:5 leadSpacing:10 tailSpacing:10];
    [bottomArr mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@(WKDHPX(120)));
        make.height.equalTo(@(WKDHPX(80)));
        make.bottom.equalTo(@(-WKDHPX(10)));
    }];
    
    self.timeView.topLab.font = [UIFont fontWithName:Bebas_Font size:50];
    self.timeView.bottomLab.font = [UIFont fontWithName:fontNamePing size:14];
    self.timeView.bottomText = @"训练时长";
    
    self.kcalView.topLab.font = [UIFont fontWithName:Bebas_Font size:50];
    self.kcalView.bottomLab.font = [UIFont fontWithName:fontNamePing size:14];
    self.kcalView.bottomText = @"消耗(千卡)";;
    
    self.distanceView.topLab.font = [UIFont fontWithName:Bebas_Font size:30];
    self.distanceView.bottomLab.font = [UIFont fontWithName:fontNamePing size:12];
    self.distanceView.bottomText = @"距离(公里)";
    
    self.spmView.topLab.font = [UIFont fontWithName:Bebas_Font size:30];
    self.spmView.bottomLab.font = [UIFont fontWithName:fontNamePing size:12];
    self.spmView.bottomText = self.type.intValue == BoatEquipment ? @"桨频(spm)" : @"踏频(rpm)";
    
    self.countView.topLab.font = [UIFont fontWithName:Bebas_Font size:30];
    self.countView.bottomLab.font = [UIFont fontWithName:fontNamePing size:12];
    self.countView.bottomText = self.type.intValue == BoatEquipment ? @"总桨数" : @"总踏数";
}

#pragma mark  === 跑步机 ====
- (void)treadmillUI {
    [self addSubview:self.timeView];
    [self addSubview:self.kcalView];
    
    [self addSubview:self.speedView];
    [self addSubview:self.distanceView];
    
    NSMutableArray*topArr = @[self.timeView, self.kcalView].mutableCopy;
    [topArr mas_distributeViewsAlongAxis:MASAxisTypeHorizontal withFixedSpacing:5 leadSpacing:10 tailSpacing:10];
    [topArr mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@(WKDHPX(10)));
        make.height.equalTo(@(WKDHPX(80)));
    }];
    
    
    NSMutableArray*bottomArr = @[self.speedView, self.distanceView].mutableCopy;
    [bottomArr mas_distributeViewsAlongAxis:MASAxisTypeHorizontal withFixedSpacing:5 leadSpacing:10 tailSpacing:10];
    [bottomArr mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@(WKDHPX(120)));
        make.height.equalTo(@(WKDHPX(80)));
        make.bottom.equalTo(@(-WKDHPX(10)));
    }];
    
    self.timeView.topLab.font = [UIFont fontWithName:Bebas_Font size:50];
    self.timeView.bottomLab.font = [UIFont fontWithName:fontNamePing size:14];
    self.timeView.bottomText = @"训练时长";
    
    self.kcalView.topLab.font = [UIFont fontWithName:Bebas_Font size:50];
    self.kcalView.bottomLab.font = [UIFont fontWithName:fontNamePing size:14];
    self.kcalView.bottomText = @"消耗(千卡)";;
    
    self.speedView.topLab.font = [UIFont fontWithName:Bebas_Font size:30];
    self.speedView.bottomLab.font = [UIFont fontWithName:fontNamePing size:12];
    self.speedView.bottomText = @"速度(公里/时)";
    
    self.distanceView.topLab.font = [UIFont fontWithName:Bebas_Font size:30];
    self.distanceView.bottomLab.font = [UIFont fontWithName:fontNamePing size:12];
    self.distanceView.bottomText = @"距离(公里)";
}

#pragma mark  === 爬楼机 ====
- (void)stairClimbUI {
    [self addSubview:self.timeView];
    [self addSubview:self.kcalView];
   
    [self addSubview:self.distanceView];
    [self addSubview:self.spmView];
    [self addSubview:self.countView];
    
    NSMutableArray*topArr = @[self.timeView, self.kcalView].mutableCopy;
    [topArr mas_distributeViewsAlongAxis:MASAxisTypeHorizontal withFixedSpacing:5 leadSpacing:10 tailSpacing:10];
    [topArr mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@(WKDHPX(10)));
        make.height.equalTo(@(WKDHPX(80)));
    }];
    
    NSMutableArray*bottomArr = @[self.distanceView, self.spmView, self.countView].mutableCopy;
    [bottomArr mas_distributeViewsAlongAxis:MASAxisTypeHorizontal withFixedSpacing:5 leadSpacing:10 tailSpacing:10];
    [bottomArr mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@(WKDHPX(100)));
        make.height.equalTo(@(WKDHPX(80)));
        make.bottom.equalTo(@(-WKDHPX(10)));
    }];
    
    self.timeView.topLab.font = [UIFont fontWithName:Bebas_Font size:50];
    self.timeView.bottomLab.font = [UIFont fontWithName:fontNamePing size:14];
    self.timeView.bottomText = @"训练时长";
    
    self.kcalView.topLab.font = [UIFont fontWithName:Bebas_Font size:50];
    self.kcalView.bottomLab.font = [UIFont fontWithName:fontNamePing size:14];
    self.kcalView.bottomText = @"消耗(千卡)";;
    
    self.distanceView.topLab.font = [UIFont fontWithName:Bebas_Font size:30];
    self.distanceView.bottomLab.font = [UIFont fontWithName:fontNamePing size:12];
    self.distanceView.bottomText = @"距离(公里)";
    
    self.spmView.topLab.font = [UIFont fontWithName:Bebas_Font size:30];
    self.spmView.bottomLab.font = [UIFont fontWithName:fontNamePing size:12];
    self.spmView.bottomText = @"踏频(rpm)";
    
    self.countView.topLab.font = [UIFont fontWithName:Bebas_Font size:30];
    self.countView.bottomLab.font = [UIFont fontWithName:fontNamePing size:12];
    self.countView.bottomText =  @"次数(次)";
}

#pragma mark  === 筋膜枪 ====
- (void)jmqUI {
    [self addSubview:self.timeView];
    
    NSMutableArray*topArr = @[self.timeView].mutableCopy;
//    [topArr mas_distributeViewsAlongAxis:MASAxisTypeHorizontal withFixedSpacing:5 leadSpacing:10 tailSpacing:10];
    [topArr mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@(WKDHPX(10)));
        make.left.equalTo(@(WKDHPX(10)));
        make.right.equalTo(@(WKDHPX(10)));
        make.height.equalTo(@(WKDHPX(80)));
        make.bottom.equalTo(@(-WKDHPX(10)));
    }];
    
    self.timeView.topLab.font = [UIFont fontWithName:Bebas_Font size:50];
    self.timeView.bottomLab.font = [UIFont fontWithName:fontNamePing size:14];
    self.timeView.bottomText = @"训练时长";
}

#pragma mark  === 跳绳 ====
- (void)skip {
    
    [self addSubview:self.timeView];
    [self addSubview:self.kcalView];
    
    [self addSubview:self.countView];
    [self addSubview:self.speedView];
    
    NSMutableArray*topArr = @[self.timeView, self.kcalView].mutableCopy;
    [topArr mas_distributeViewsAlongAxis:MASAxisTypeHorizontal withFixedSpacing:5 leadSpacing:10 tailSpacing:10];
    [topArr mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@(WKDHPX(10)));
        make.height.equalTo(@(WKDHPX(80)));
    }];
    
    NSMutableArray*bottomArr = @[self.countView, self.speedView].mutableCopy;
    [bottomArr mas_distributeViewsAlongAxis:MASAxisTypeHorizontal withFixedSpacing:5 leadSpacing:10 tailSpacing:10];
    [bottomArr mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@(WKDHPX(100)));
        make.height.equalTo(@(WKDHPX(80)));
        make.bottom.equalTo(@(-WKDHPX(10)));
    }];
    
    self.timeView.topLab.font = [UIFont fontWithName:Bebas_Font size:50];
    self.timeView.bottomLab.font = [UIFont fontWithName:fontNamePing size:14];
    self.timeView.bottomText = @"训练时长";
    
    self.kcalView.topLab.font = [UIFont fontWithName:Bebas_Font size:50];
    self.kcalView.bottomLab.font = [UIFont fontWithName:fontNamePing size:14];
    self.kcalView.bottomText = @"消耗(千卡)";
    
    self.countView.topLab.font = [UIFont fontWithName:Bebas_Font size:50];
    self.countView.bottomLab.font = [UIFont fontWithName:fontNamePing size:14];
    self.countView.bottomText = @"个数";
    
    self.speedView.topLab.font = [UIFont fontWithName:Bebas_Font size:50];
    self.speedView.bottomLab.font = [UIFont fontWithName:fontNamePing size:14];
    self.speedView.bottomText = @"速率(个数/分钟)";
}

#pragma mark  === 呼啦圈 ====
- (void)hoop {
    [self addSubview:self.timeView];
    [self addSubview:self.kcalView];
    
    [self addSubview:self.countView];
    [self addSubview:self.speedView];
    
    NSMutableArray*topArr = @[self.timeView, self.kcalView].mutableCopy;
    [topArr mas_distributeViewsAlongAxis:MASAxisTypeHorizontal withFixedSpacing:5 leadSpacing:10 tailSpacing:10];
    [topArr mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@(WKDHPX(10)));
        make.height.equalTo(@(WKDHPX(80)));
    }];
    
    NSMutableArray*bottomArr = @[self.countView, self.speedView].mutableCopy;
    [bottomArr mas_distributeViewsAlongAxis:MASAxisTypeHorizontal withFixedSpacing:5 leadSpacing:10 tailSpacing:10];
    [bottomArr mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@(WKDHPX(100)));
        make.height.equalTo(@(WKDHPX(80)));
        make.bottom.equalTo(@(-WKDHPX(10)));
    }];
    
    self.timeView.topLab.font = [UIFont fontWithName:Bebas_Font size:50];
    self.timeView.bottomLab.font = [UIFont fontWithName:fontNamePing size:14];
    self.timeView.bottomText = @"训练时长";
    
    self.kcalView.topLab.font = [UIFont fontWithName:Bebas_Font size:50];
    self.kcalView.bottomLab.font = [UIFont fontWithName:fontNamePing size:14];
    self.kcalView.bottomText = @"消耗(千卡)";
    
    self.countView.topLab.font = [UIFont fontWithName:Bebas_Font size:50];
    self.countView.bottomLab.font = [UIFont fontWithName:fontNamePing size:14];
    self.countView.bottomText = @"圈数";
    
    self.speedView.topLab.font = [UIFont fontWithName:Bebas_Font size:50];
    self.speedView.bottomLab.font = [UIFont fontWithName:fontNamePing size:14];
    self.speedView.bottomText = @"速率(圈数/分钟)";
}

#pragma mark  === 力量站 ====
- (void)powerUI {
    [self addSubview:self.timeView];
    [self addSubview:self.kcalView];
    
    [self addSubview:self.distanceView];
    [self addSubview:self.spmView];
    [self addSubview:self.countView];
    
    NSMutableArray*topArr = @[self.timeView, self.kcalView].mutableCopy;
    [topArr mas_distributeViewsAlongAxis:MASAxisTypeHorizontal withFixedSpacing:5 leadSpacing:10 tailSpacing:10];
    [topArr mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@(WKDHPX(10)));
        make.height.equalTo(@(WKDHPX(80)));
    }];
    
    
    NSMutableArray*bottomArr = @[self.distanceView, self.spmView, self.countView].mutableCopy;
    [bottomArr mas_distributeViewsAlongAxis:MASAxisTypeHorizontal withFixedSpacing:5 leadSpacing:10 tailSpacing:10];
    [bottomArr mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@(WKDHPX(100)));
        make.height.equalTo(@(WKDHPX(80)));
        make.bottom.equalTo(@(-WKDHPX(10)));
    }];
    
    self.timeView.topLab.font = [UIFont fontWithName:Bebas_Font size:30];
    self.timeView.bottomLab.font = [UIFont fontWithName:fontNamePing size:12];
    self.timeView.bottomText = @"训练时长";
    
    self.kcalView.topLab.font = [UIFont fontWithName:Bebas_Font size:30];
    self.kcalView.bottomLab.font = [UIFont fontWithName:fontNamePing size:12];
    self.kcalView.bottomText = @"累计次数(次)";
    
    self.distanceView.topLab.font = [UIFont fontWithName:Bebas_Font size:30];
    self.distanceView.bottomLab.font = [UIFont fontWithName:fontNamePing size:12];
    self.distanceView.bottomText =@"瞬时功率(瓦)";
    
    self.spmView.topLab.font = [UIFont fontWithName:Bebas_Font size:30];
    self.spmView.bottomLab.font = [UIFont fontWithName:fontNamePing size:12];
    self.spmView.bottomText = @"消耗(千卡)";
    
    self.countView.topLab.font = [UIFont fontWithName:Bebas_Font size:30];
    self.countView.bottomLab.font = [UIFont fontWithName:fontNamePing size:12];
    self.countView.bottomText = @"当前重量(公斤)";
}

#pragma mark  === 力量站P01 ====
- (void)powerP01UI {
    [self removeAllSubviews];
    
    [self addSubview:self.timeView];
    [self addSubview:self.kcalView];
    
    [self addSubview:self.spmView];
    [self addSubview:self.countView];
    
    NSMutableArray*topArr = @[self.timeView, self.kcalView].mutableCopy;
    [topArr mas_distributeViewsAlongAxis:MASAxisTypeHorizontal withFixedSpacing:5 leadSpacing:10 tailSpacing:10];
    [topArr mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@(WKDHPX(10)));
        make.height.equalTo(@(WKDHPX(80)));
    }];
    
    NSMutableArray*bottomArr = @[self.spmView, self.countView].mutableCopy;
    [bottomArr mas_distributeViewsAlongAxis:MASAxisTypeHorizontal withFixedSpacing:5 leadSpacing:10 tailSpacing:10];
    [bottomArr mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@(WKDHPX(100)));
        make.height.equalTo(@(WKDHPX(80)));
        make.bottom.equalTo(@(-WKDHPX(10)));
    }];
    
    self.timeView.topLab.font = [UIFont fontWithName:Bebas_Font size:30];
    self.timeView.bottomLab.font = [UIFont fontWithName:fontNamePing size:12];
    self.timeView.bottomText = @"训练时长";
    
    self.kcalView.topLab.font = [UIFont fontWithName:Bebas_Font size:30];
    self.kcalView.bottomLab.font = [UIFont fontWithName:fontNamePing size:12];
    self.kcalView.bottomText = @"累计次数(次)";
    
    self.spmView.topLab.font = [UIFont fontWithName:Bebas_Font size:30];
    self.spmView.bottomLab.font = [UIFont fontWithName:fontNamePing size:12];
    self.spmView.bottomText = @"消耗(千卡)";
    
    self.countView.topLab.font = [UIFont fontWithName:Bebas_Font size:30];
    self.countView.bottomLab.font = [UIFont fontWithName:fontNamePing size:12];
    self.countView.bottomText = @"阻力(Lv)";
}

- (void)setModel:(TrainingShowData *)model {
    _model = model;
    
    dispatch_async(dispatch_get_main_queue(), ^{
        //力量站
        if (self.type.intValue == PowerEquipment) {
            ///训练时长
            self.timeView.topText = model.totalTime;
            ///累计次数
            self.kcalView.topText = model.totalNum;
            ///P01不显示瞬时功率
            if (!([self.equmentName localizedCaseInsensitiveContainsString:@"-P01"] ||
                  [self.equmentName localizedCaseInsensitiveContainsString:@"-P99"])) {
                ///瞬时功率!
                self.distanceView.topText = model.devicePower;
            }
            ///消耗
            self.spmView.topText = model.totalKcal;
            ///当前重量或者阻力
            if ([self.equmentName localizedCaseInsensitiveContainsString:@"-P01"]) {//阻力
                self.countView.topText = model.resistance;
            } else if ([self.equmentName localizedCaseInsensitiveContainsString:@"-P02"] ||
                       [self.equmentName localizedCaseInsensitiveContainsString:@"-P99"]) {//重量
                self.countView.topText = model.weight;
            } else if ([self.equmentName localizedCaseInsensitiveContainsString:@"-P03"]){//档位
                self.countView.topText = model.gear;
            }
            return;
        }
        
        ///训练时长
        self.timeView.topText = model.totalTime;
        ///消耗
        self.kcalView.topText = model.totalKcal;
        ///距离公里
        self.distanceView.topText = model.totalDistance;
        ///阻力
        self.dragView.topText = model.resistance;
        ///踏频/浆频
        self.spmView.topText = model.spm;
        ///总踏数/总浆频
        self.countView.topText = model.totalNum;
        ///跑步机速度 跟着字段speed显示
        if (self.type.intValue != TreadmillEquipment) {
            self.speedView.topText = model.speed;
        }
    });
}

- (void)setSpeed:(NSString *)speed {
    _speed = speed.copy;
    self.speedView.topText = _speed ? [NSString stringWithFormat:@"%.1f" , _speed.floatValue] : @"--";
    if (self.updateSpeedBlock){
        self.updateSpeedBlock(_speed);
    }
}

- (FreedomListView *)timeView{
    if (!_timeView) {
        _timeView = [[FreedomListView alloc] init];
        _timeView.bottomText = @"训练时长";
    }
    return _timeView;
}

- (FreedomListView *)kcalView{
    if (!_kcalView) {
        _kcalView = [[FreedomListView alloc] init];
        _kcalView.bottomText = @"消耗(千卡)";
    }
    return _kcalView;
}

- (FreedomListView *)distanceView{
    if (!_distanceView) {
        _distanceView = [[FreedomListView alloc] init];
        _distanceView.bottomText = @"距离(公里)";
    }
    return _distanceView;
}

- (FreedomListView *)dragView{
    if (!_dragView) {
        _dragView = [[FreedomListView alloc] init];
        _dragView.bottomText = @"阻力(Lv)";
    }
    return _dragView;
}

- (FreedomListView *)spmView{
    if (!_spmView) {
        _spmView = [[FreedomListView alloc] init];
        _spmView.bottomText = @"踏频(rpm)";
    }
    return _spmView;
}

- (FreedomListView *)countView{
    if (!_countView) {
        _countView = [[FreedomListView alloc] init];
        _countView.bottomText = @"总踏数";
    }
    return _countView;
}

- (FreedomListView *)speedView{
    if (!_speedView) {
        _speedView = [[FreedomListView alloc] init];
        _speedView.bottomText = @"速率";
    }
    return _speedView;
}

- (void)dealloc {
    NSLog(@"%@_______dealloc" , NSStringFromClass([self class]));
}

@end







@implementation FreedomListView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        
        self.topLab = [UILabel new];
        self.topLab.text = @"--";
        self.topLab.textColor = [UIColor whiteColor];
        self.topLab.font = [UIFont fontWithName:Bebas_Font size:60];
        self.topLab.textAlignment = NSTextAlignmentCenter;
        [self addSubview:self.topLab];
        [self.topLab mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(@(DHPX(10)));
            make.centerX.equalTo(self.mas_centerX);
        }];
        
        self.bottomLab = [UILabel new];
        self.bottomLab.text = @"xxx unit";
        self.bottomLab.textAlignment = NSTextAlignmentCenter;
        self.bottomLab.font = [UIFont fontWithName:@"DINPro" size:16];
        self.bottomLab.textColor = [UIColor colorWithHexString:@"#A5A9B0"];
        [self addSubview:self.bottomLab];
        [self.bottomLab mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo( self.topLab.mas_bottom).offset(5);
            make.centerX.equalTo(self.mas_centerX);
        }];
    }
    return self;
}

- (void)setTopText:(NSString *)topText {
    _topText = topText;
    if ([topText isNotBlank]) {
        self.topLab.text = topText;
    }
}

- (void)setBottomText:(NSString *)bottomText {
    _bottomText = bottomText;
    if ([bottomText isNotBlank]) {
        self.bottomLab.text = bottomText;
    }
}

@end






















#import "MRKControlSlider.h"

@interface SliderView ()<MRKControlSliderDelegate>
@property (nonatomic, strong) UIButton *increaseButton;
@property (nonatomic, strong) UIButton *reduceButton;
@property (nonatomic, strong) UILabel *topLabel;
@property (nonatomic, strong) UILabel *bottomLabel;
@property (nonatomic, strong) MRKControlSlider *progressSlider;
@end

@implementation SliderView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        [self setUI];
    }
    return self;
}

- (void)setUI {
    UILabel *typeLab = [[UILabel alloc] init];
    typeLab.textAlignment = NSTextAlignmentCenter;
    typeLab.text = @"阻力";
    typeLab.textColor = [UIColor whiteColor];
    typeLab.font = [UIFont fontWithName:fontNameMeDium size:16];
    [self addSubview: typeLab];
    self.bottomLabel = typeLab;
    [self.bottomLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo( self.mas_left).offset(DHPX(16));
        make.centerY.equalTo(self.mas_centerY);
    }];
    
    
    [self addSubview:self.reduceButton];
    [self.reduceButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo( self.bottomLabel.mas_right).offset(DHPX(20));
        make.centerY.equalTo(self.mas_centerY);
        make.width.height.mas_equalTo(48);
    }];
    
    [self addSubview:self.increaseButton];
    [self.increaseButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.height.mas_equalTo(48);
        make.right.equalTo( self.mas_right).offset(-DHPX(16));
        make.centerY.equalTo(self.reduceButton.mas_centerY);
    }];
    
    self.progressSlider.delegate = self;
    [self addSubview:self.progressSlider];
    [self.progressSlider mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.reduceButton.mas_right).offset(15);
        make.right.equalTo(self.increaseButton.mas_left).offset(-15);
        make.centerY.equalTo(self.mas_centerY);
        make.height.offset(20);
    }];
    
    UILabel *topLabel = [[UILabel alloc] init];
    topLabel.textAlignment = NSTextAlignmentCenter;
    topLabel.text = @"--";
    topLabel.textColor = [UIColor whiteColor];
    topLabel.font = BebasFont_Bold_NoDHPX(40);
    [self addSubview:topLabel];
    self.topLabel = topLabel;
    [self.topLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.progressSlider.mas_top).offset(0);
        make.centerX.equalTo(self.progressSlider.thumbImageView.mas_centerX);
    }];
    
    [self.reduceButton addTarget:self action:@selector(reduceAction:) forControlEvents:UIControlEventTouchUpInside];
    [self.increaseButton addTarget:self action:@selector(increaseAction:) forControlEvents:UIControlEventTouchUpInside];
    
    @weakify(self);
    [RACObserve(self, topText) subscribeNext:^(NSString * x) {
        @strongify(self);
        switch (self.sliderType) {
            case DragSlider:
            {
                self.increaseButton.enabled = x.intValue < self.eqModel.maxResistance.intValue;
                self.reduceButton.enabled = x.intValue > self.eqModel.minResistance.intValue ;
            } break;
                
            case GearSlider:
            {
                self.increaseButton.enabled = x.intValue < self.eqModel.maxGear.intValue;
                self.reduceButton.enabled = x.intValue > self.eqModel.minGear.intValue;
            } break;
                
            case SlopeSlider:
            {
                self.increaseButton.enabled = x.intValue < self.eqModel.maxSlope.intValue;
                self.reduceButton.enabled = x.intValue > self.eqModel.minSlope.intValue;
            } break;
                
            default: break;
        }
    }];
}

///SJProgressSliderDelegate
///开始滑动
- (void)sliderWillBeginDragging:(MRKControlSlider *)slider{
    
}

///滑动中阻力显示
- (void)slider:(MRKControlSlider *)slider valueDidChange:(CGFloat)value{
    self.topLabel.text = [NSString stringWithFormat:@"%d", (int)value];
}

///滑动完成
- (void)sliderDidEndDragging:(MRKControlSlider *)slider{
    self.topLabel.text = [NSString stringWithFormat:@"%d", (int)slider.value];
    _topText = self.topLabel.text;
    self.progressSlider.value = _topText.intValue;
    if (_delegate && [_delegate respondsToSelector:@selector(dragDataAction:)]) {
        [_delegate dragDataAction:self];
    }
}

- (void)setRecomposing:(BOOL)recomposing{
    _recomposing = recomposing;
    self.progressSlider.isLoading = recomposing;
    if (recomposing){
        self.progressSlider.userInteractionEnabled = NO;
    }else{
        self.progressSlider.userInteractionEnabled = YES;
    }
}

- (void)setEqModel:(EquipmentDetialModel *)eqModel {
    _eqModel = eqModel;
    
    switch (self.sliderType) {
        case DragSlider: {
            self.bottomLabel.text = @"阻力";
            self.progressSlider.minValue = eqModel.minResistance.intValue;
            self.progressSlider.maxValue = eqModel.maxResistance.intValue;
        } break;
            
        case SlopeSlider: {
            self.bottomLabel.text = @"坡度";
            self.progressSlider.minValue = eqModel.minSlope.intValue;
            self.progressSlider.maxValue = eqModel.maxSlope.intValue;
        } break;
            
        case GearSlider: {
            self.bottomLabel.text = @"档位";
            self.progressSlider.minValue = eqModel.minGear.intValue;
            self.progressSlider.maxValue = eqModel.maxGear.intValue;
        } break;
            
        default: break;
    }
}

- (void)reduceAction:(id)sender {
    if (self.recomposing) {
        return;
    }
    
    if (_delegate && [_delegate respondsToSelector:@selector(reduceAction:)]) {
        [_delegate reduceAction:self];
    }
}

- (void)increaseAction:(id)sender {
    if (self.recomposing) {
        return;
    }
    
    if (_delegate && [_delegate respondsToSelector:@selector(increaseAction:)]) {
        [_delegate increaseAction:self];
    }
}

- (void)updateSlider {
    self.progressSlider.value = self.topText.intValue;
}

- (void)setTopText:(NSString *)topText {
    _topText = topText;
    
    dispatch_async(dispatch_get_main_queue(), ^{
        ///判断是slider不在拖动, 不在加载中
        if (!self.progressSlider.isDragging && !self.recomposing) {
            self.topLabel.text = topText;
            
            [self updateSlider];
        }
    });
}

- (void)setBottomText:(NSString *)bottomText {
    _bottomText = bottomText;
    dispatch_async(dispatch_get_main_queue(), ^{
        self.bottomLabel.text = bottomText;
    });
}

- (MRKControlSlider *)progressSlider {
    if ( _progressSlider ) return _progressSlider;
    _progressSlider = [MRKControlSlider new];
    _progressSlider.userInteractionEnabled = YES;
    _progressSlider.trackImageView.backgroundColor = [UIColor colorWithWhite:1 alpha:0.2];
    _progressSlider.traceImageView.backgroundColor = MainAppColor;
    _progressSlider.thumbImageView.image = [UIImage imageNamed:@"icon_slide_2"];
    _progressSlider.thumbImageView.userInteractionEnabled = YES;
    _progressSlider.pan.enabled = YES;
    _progressSlider.tap.enabled = YES; ///
    _progressSlider.trackHeight = 4;
    _progressSlider.round = YES;
    return _progressSlider;
}

- (UIButton *)reduceButton {
    if (!_reduceButton) {
        UIButton *btn = [[UIButton alloc]init];
        [btn setImage:[UIImage imageNamed:@"icon_reduce"] forState:UIControlStateNormal];
        [btn setImage:[UIImage imageNamed:@"icon_reduce_disabled"] forState:UIControlStateDisabled];
        _reduceButton = btn;
    }
    return _reduceButton;
}

- (UIButton *)increaseButton {
    if (!_increaseButton) {
        UIButton *btn = [[UIButton alloc]init];
        [btn setImage:[UIImage imageNamed:@"icon_add_48x48"] forState:UIControlStateNormal];
        [btn setImage:[UIImage imageNamed:@"icon_add_disabled"] forState:UIControlStateDisabled];
        _increaseButton = btn;
    }
    return _increaseButton;
}

@end







@interface TargetProgressView ()
@property (nonatomic, strong) UILabel *targetL;
@property (strong, nonatomic) MrkProgressPopView *progressView;
@end

@implementation TargetProgressView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setUI];
    }
    return self;
}

- (void)setUI {
    [self addSubview: self.targetL];
    [self.targetL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(@(DHPX(0)));
        make.right.equalTo(@(DHPX(-16)));
    }];
    
    [self addSubview:self.progressView];
    [self.progressView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.targetL.mas_top).offset(DHPX(-5));
        make.left.equalTo(@(DHPX(16)));
        make.right.equalTo(@(-DHPX(16)));
        make.height.equalTo(@4);
    }];
    
    RAC(self.targetL,text) = RACObserve(self, title);
    [RACObserve(self, progress) subscribeNext:^(NSNumber *progress) {
        if (!progress.boolValue) {
            return;
        }
        [self.progressView setProgress:progress.floatValue animated:NO];
    }];
}

- (void)setProductId:(NSString *)productId{
    _productId = productId;
    self.progressView.image = [UIImage imageNamed:[MRKEquipmentTypeData targetImageFromType:productId]];
}

// 进度完成动画
- (void)finishProgressAnimation {
    
}

- (UILabel *)targetL{
    if (!_targetL){
        _targetL = [UILabel new];
        _targetL.font = [UIFont fontWithName:fontNamePing size:12.0];
        _targetL.textColor = [UIColor whiteColor];
    }
    return _targetL;
}

- (MrkProgressPopView *)progressView{
    if (!_progressView){
        _progressView = [[MrkProgressPopView alloc] init];
        _progressView.imageLocation = PopUpViewLocationTop;
        _progressView.viewRadius = 2.0f;
        _progressView.trackTintColor = [UIColor colorWithWhite:1 alpha:0.2];
        _progressView.popUpViewAnimatedColors = @[[UIColor colorWithHexString:@"#16D2E3"]];
        _progressView.progress = 0.0;
        [_progressView showPopUpViewAnimated:YES];
    }
    return _progressView;
}

// 彩带动画
- (void)finishColorAnimation:(UIView *)view completionBlock:(void(^)(void))block {
    
//    MRKLottieView *lotColorBanding = [[MRKLottieView alloc] init];
//    lotColorBanding.animationName = @"FireworksShow.json";
//    lotColorBanding.loopAnimationCount = 1;
//    lotColorBanding.autoresizingMask = (UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight);
//    lotColorBanding.contentMode = UIViewContentModeScaleAspectFill;
//    [view addSubview:lotColorBanding];
//    [lotColorBanding mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.edges.mas_equalTo(UIEdgeInsetsMake(0, 0, 0, 0));
//    }];
//    [lotColorBanding playWithCompletion:^{
//        
//    }];
//    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
//        !block?:block();
//    });
    
    LOTAnimationView *lotColorBanding = [[LOTAnimationView alloc] init];
    lotColorBanding.autoresizingMask = (UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight);
    lotColorBanding.contentMode = UIViewContentModeScaleAspectFill;
    [view addSubview:lotColorBanding];
    [lotColorBanding mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsMake(0, 0, 0, 0));
    }];
    [lotColorBanding setAnimationNamed:@"FireworksShow.json"];
    [lotColorBanding setLoopAnimation:NO];
    [lotColorBanding playWithCompletion:^(BOOL animationFinished) {

    }];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        !block?:block();
    });
}

- (void)dealloc {
    NSLog(@"😊😊😊-----dealloc-----😊😊😊%@" , NSStringFromClass([self class]));
}
@end










//设备名称 电量
@interface DeviceInfoView()
@property (nonatomic, strong) UILabel *deviceName;
@property (nonatomic, strong) FreedomStatusView *statusView;
@property (nonatomic, strong) FreedomElecView *elecView;
@property (nonatomic, strong) FreedomDataTipView *dataTipView;

@property (nonatomic, assign) BOOL isFirst;
@end

@implementation DeviceInfoView

- (UILabel *)deviceName{
    if (!_deviceName){
        UILabel *label = [[UILabel alloc] init];
        label.font = [UIFont fontWithName:fontNamePing size:12];
        label.textColor = [UIColor whiteColor];
        label.text = @"设备型号: -- ";
        _deviceName = label;
    }
    return _deviceName;
}

- (FreedomStatusView *)statusView{
    if (!_statusView){
        FreedomStatusView *view = [[FreedomStatusView alloc] init];
        _statusView = view;
    }
    return _statusView;
}

- (FreedomElecView *)elecView{
    if (!_elecView){
        FreedomElecView *view = [[FreedomElecView alloc] init];
        _elecView = view;
    }
    return _elecView;
}

- (FreedomDataTipView *)dataTipView{
    if (!_dataTipView){
        FreedomDataTipView *view = [[FreedomDataTipView alloc] init];
        view.backgroundColor = UIColorHex(#10111D);
        [view addTarget:self action:@selector(dataTipMessage:) forControlEvents:UIControlEventTouchUpInside];
        _dataTipView = view;
    }
    return _dataTipView;
}

- (void)dataTipMessage:(UIControl *)sender {
    [self.dataTipView removeFromSuperview];
    self.dataTipView = nil;
    
    if (self.dataTipMessageBlock){
        self.dataTipMessageBlock();
    }
}

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self ) {
        
        [self setUI];
    }
    return self;
}

- (void)setUI {
    [self addSubview:self.deviceName];
    [self.deviceName mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.mas_top).offset(0);
        make.left.equalTo(@(DHPX(15)));
        make.height.equalTo(@(20));
    }];
    
    self.statusView.hidden = YES;
    [self addSubview:self.statusView];
    [self.statusView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.mas_top).offset(0);
        make.left.equalTo(self.deviceName.mas_left);
        make.height.equalTo(@(20));
    }];
    
    self.elecView.hidden = YES;
    [self addSubview:self.elecView];
    [self.elecView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.deviceName.mas_bottom).offset(WKDHPX(8));
        make.left.equalTo(self.deviceName.mas_left);
        make.height.equalTo(@(20));
    }];
    
    self.dataTipView.hidden = YES;
    [self addSubview:self.dataTipView];
    [self.dataTipView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.deviceName.mas_bottom).offset(WKDHPX(8));
        make.left.equalTo(self.mas_left);
        make.height.equalTo(@(WKDHPX(32)));
    }];
    MrkCornerMaskWithViewRadius(self.dataTipView, ViewRadiusMake(0, WKDHPX(16), 0, WKDHPX(16)));

    @weakify(self);
    [[RACObserve(self, connectStatus) distinctUntilChanged] subscribeNext:^(NSNumber * x) {
        @strongify(self);
        
        if (x && [self.infoName isNotBlank]) {
            ///刷新dataTipView是否显示
            NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
            BOOL RegulateTip = [userDefaults boolForKey:@"FreedomTrainingDataTip"];
            if (!RegulateTip){
                self.dataTipView.hidden = NO;
                [userDefaults setBool:YES forKey:@"FreedomTrainingDataTip"];
                [userDefaults synchronize];
            }
            
            DEVICE_CONNECT_STATUS status = x.intValue;
            switch (status) {
                case DeviceDisconnect: {
                    [self updataDeviceName];
                } break;
                case DeviceConnected: {
                    if (!self.isFirst){
                        self.isFirst = YES;
                        
                        [self updataDeviceName];
                    }else{
                        [self updataStatusView:x.intValue];
                        
                        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                            [self updataDeviceName];
                        });
                    }
                } break;
                default:{
                    [self updataStatusView:x.intValue];
                } break;
            }
        }
    }];
}

- (void)setInfoName:(NSString *)infoName {
    _infoName = infoName;
    if ([infoName isNotBlank]){
        [self updataDeviceName];
    }
}

- (void)setElec:(NSString *)elec{
    _elec = elec;
    if ([elec isNotBlank]){
        dispatch_async(dispatch_get_main_queue(), ^{
            self.elecView.elec = elec;
            self.elecView.hidden = NO;
        });
    }
}

- (void)updataDeviceName{
    dispatch_async(dispatch_get_main_queue(), ^{
        self.statusView.hidden = YES;
        
        self.deviceName.hidden = NO;
        self.deviceName.text = self.infoName;
    });
}

- (void)updataStatusView:(DEVICE_CONNECT_STATUS)status{
    dispatch_async(dispatch_get_main_queue(), ^{
        self.deviceName.hidden = YES;
        
        self.statusView.hidden = NO;
        self.statusView.connectStatus = status;
    });
}

- (void)dealloc {
    NSLog(@"😊😊😊-----dealloc-----😊😊😊%@" , NSStringFromClass([self class]));
}

@end











@implementation JMQPressAlertView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setUI];
    }
    return self;
}

- (void)setUI {
    self.backgroundColor = [UIColor colorWithHexString:@"#FCECEC" alpha:1.0];
    
    UIImageView *tipImageView = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"icon_prompt"]];
    [self addSubview:tipImageView];
    [tipImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@(DHPX(15)));
        make.centerY.equalTo(self.mas_centerY);
    }];
    
    UILabel *tipLab = [UILabel new];
    tipLab.font = [UIFont fontWithName:fontNamePing size:(14.0)];
    tipLab.textColor = [UIColor colorWithHexString:@"#FF4040"];
    [self addSubview:tipLab];
    [tipLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(tipImageView.mas_right).offset(DHPX(10));
        make.centerY.equalTo(tipImageView.mas_centerY);
    }];
    
    UIImageView *arrowImageView = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"icon_go_red"]];
    arrowImageView.hidden = YES;
    [self addSubview:arrowImageView];
    [arrowImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(@(DHPX(-15)));
        make.centerY.equalTo(self.mas_centerY);
    }];
   
    [RACObserve(self, alertString) subscribeNext:^(NSString * x) {
        tipLab.text = x;
    }];
    
    [RACObserve(self, showArrow) subscribeNext:^(NSNumber * x) {
        arrowImageView.hidden = !x.boolValue;
    }];
    
    [RACObserve(self, rightImage) subscribeNext:^(NSString * x) {
        if([x isNotEmpty]) {
            arrowImageView.image = [UIImage imageNamed:x];
        }
    }];
}

@end





@implementation PowerInfoView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self ) {
        
        [self setUI];
    }
    return self;
}

- (void)setUI {
    
    UILabel *typeNumbr = [[UILabel alloc] init];
    typeNumbr.font = [UIFont fontWithName:fontNamePing size:12];
    typeNumbr.textColor = [UIColor whiteColor];
    typeNumbr.text = @"当前模式: ";
    [self addSubview:typeNumbr];
    [typeNumbr mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@(DHPX(15)));
        make.centerY.equalTo(self.mas_centerY);
    }];
    
    __block UILabel *elecL = [UILabel new];
    elecL.font = [UIFont fontWithName:fontNamePing size:12.0];
    elecL.textColor = [UIColor whiteColor];
    [self addSubview:elecL];
    [elecL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(@(-DHPX(15)));
        make.centerY.equalTo(self.mas_centerY);
    }];
 
    
    UIImageView *elecImg = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"icon_electricity"]];
    [self addSubview:elecImg];
    [elecImg mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(elecL.mas_left).offset(-5);
        make.width.equalTo(@(18));
        make.centerY.equalTo(elecL.mas_centerY);
    }];
    
    __block CALayer *elecProgress = [CALayer layer];
    elecProgress.frame = CGRectMake(0, 0, 0, 9);
    elecProgress.backgroundColor = [UIColor whiteColor].CGColor;
    elecProgress.cornerRadius = 1;
    [elecImg.layer addSublayer:elecProgress];

    [RACObserve(self, elec) subscribeNext:^(NSString * x) {
        if (x) {
            elecL.text = [NSString stringWithFormat:@"%@%", x ];
            elecProgress.frame = CGRectMake(1, 1, x.intValue / 100.0 * 13, 7);
        }
    }];
    
    /// 力量站：05H:标准力模式 06H:弹簧力模式 08H:离心力模式
    [[RACObserve(self, deviceStatus) distinctUntilChanged] subscribeNext:^(NSNumber * x) {
        if (x) {
            if (x.intValue == 5){
                typeNumbr.text = @"当前模式: 标准力模式";
            }
            
            if (x.intValue == 6){
                typeNumbr.text = @"当前模式: 弹簧力模式";
            }
            
            if (x.intValue == 8){
                typeNumbr.text = @"当前模式: 离心力模式";
            }
        }
    }];
}

- (void)dealloc {
    NSLog(@"😊😊😊-----dealloc-----😊😊😊%@" , NSStringFromClass([self class]));
}
@end









//设备名称 电量
@interface FreedomStatusView()
@property (nonatomic, strong) UILabel *statusLab;
@property (nonatomic, strong) UIImageView *statusIconView;
@property (nonatomic, strong) UIActivityIndicatorView *activityIndicator;
@end

@implementation FreedomStatusView

- (UIImageView *)statusIconView{
    if (!_statusIconView){
        UIImageView *imageView = [[UIImageView alloc] init];
        imageView.image = [UIImage imageNamed:@"icon_connect_finish"];
        _statusIconView = imageView;
    }
    return _statusIconView;
}

- (UILabel *)statusLab{
    if (!_statusLab){
        UILabel *label = [[UILabel alloc] init];
        label.font = [UIFont fontWithName:fontNamePing size:12];
        label.textColor = [UIColor whiteColor];
        _statusLab = label;
    }
    return _statusLab;
}

- (UIActivityIndicatorView *)activityIndicator{
    if (!_activityIndicator){
        UIActivityIndicatorView *indicator = [[UIActivityIndicatorView alloc] initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleGray];
        indicator.hidesWhenStopped = YES;
        indicator.color = UIColor.whiteColor;
        indicator.transform = CGAffineTransformMakeScale(.85f, .85f);
        _activityIndicator = indicator;
    }
    return _activityIndicator;
}

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self ) {
        [self setUI];
    }
    return self;
}

- (void)setUI {
    self.statusIconView.hidden = YES;
    [self addSubview:self.statusIconView];
    [self.statusIconView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@(0));
        make.centerY.equalTo(self.mas_centerY);
        make.size.equalTo(@(CGSizeMake(WKDHPX(20), WKDHPX(20))));
    }];
    
    [self addSubview:self.activityIndicator];
    [self.activityIndicator mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.mas_equalTo(self.statusIconView.mas_centerX);
        make.centerY.mas_equalTo(self.statusIconView.mas_centerY);
        make.size.mas_equalTo(CGSizeMake(20, 20));
    }];

    [self addSubview:self.statusLab];
    [self.statusLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.statusIconView.mas_right).offset(WKDHPX(3));
        make.centerY.equalTo(self.mas_centerY);
        make.right.equalTo(@(-DHPX(15)));
    }];
    
    [[RACObserve(self, connectStatus) distinctUntilChanged] subscribeNext:^(NSNumber *x) {
        if(x.intValue == DeviceConnecting
           || x.intValue == DeviceScaning
           || x.intValue == DeviceAutoConnecting) {
            dispatch_async(dispatch_get_main_queue(), ^{
                self.statusIconView.hidden = YES;
                self.statusLab.text = @"设备连接中";
                
                self.activityIndicator.hidden = NO;
                [self.activityIndicator startAnimating];
            });
        } else if(x.intValue == DeviceConnected) {
            dispatch_async(dispatch_get_main_queue(), ^{
                self.statusIconView.hidden = NO;
                self.statusLab.text = @"连接成功";
                
                self.activityIndicator.hidden = YES;
                [self.activityIndicator stopAnimating];
            });
        } else if(x.intValue == DeviceDisconnect) {
            dispatch_async(dispatch_get_main_queue(), ^{
                self.statusIconView.hidden = NO;
                self.statusLab.text = @"断开连接";
                
                self.activityIndicator.hidden = YES;
                [self.activityIndicator stopAnimating];
            });
        }
    }];
}

- (void)dealloc {
    NSLog(@"😊😊😊-----dealloc-----😊😊😊%@" , NSStringFromClass([self class]));
}
@end







///设备电量
@implementation FreedomElecView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self ) {
        [self setUI];
    }
    return self;
}

- (void)setUI {
    UILabel *elec = [UILabel new];
    elec.font = [UIFont fontWithName:fontNamePing size:12.0];
    elec.textColor = [UIColor whiteColor];
    elec.text = @"当前电量:";
    [self addSubview:elec];
    [elec mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@(0));
        make.centerY.equalTo(self.mas_centerY);
    }];

    UIImageView *elecImg = [[UIImageView alloc]initWithImage:[UIImage imageNamed:@"icon_electricity"]];
    [self addSubview:elecImg];
    [elecImg mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(elec.mas_right).offset(5);
        make.width.equalTo(@(18));
        make.centerY.equalTo(elec.mas_centerY);
    }];
    
    CALayer *elecProgress = [CALayer layer];
    elecProgress.frame = CGRectMake(0, 0, 0, 9);
    elecProgress.backgroundColor = [UIColor whiteColor].CGColor;
    elecProgress.cornerRadius = 1;
    [elecImg.layer addSublayer:elecProgress];
    
    UILabel *elecL = [UILabel new];
    elecL.font = [UIFont fontWithName:fontNamePing size:12.0];
    elecL.textColor = [UIColor whiteColor];
    [self addSubview:elecL];
    [elecL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(elecImg.mas_right).offset(WKDHPX(4));
        make.centerY.equalTo(elec.mas_centerY);
    }];
    
    [RACObserve(self, elec) subscribeNext:^(NSString * x) {
        if (x) {
            elecL.text = [NSString stringWithFormat:@"%@", x];
            elecProgress.frame = CGRectMake(1, 1, x.intValue / 100.0 * 13, 7);
        }
    }];
}

- (void)dealloc {
    NSLog(@"😊😊😊-----dealloc-----😊😊😊%@" , NSStringFromClass([self class]));
}
@end





@interface FreedomDataTipView ()
@property (nonatomic, strong) UILabel *tipLab;
@end

@implementation FreedomDataTipView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        
        if (!self.tipLab) {
            self.tipLab = [UILabel new];
            self.tipLab.font = [UIFont systemFontOfSize:13 weight:UIFontWeightMedium];
            self.tipLab.textColor = UIColor.whiteColor;
            self.tipLab.textAlignment = NSTextAlignmentCenter;
        }
        [self addSubview:self.tipLab];
        [self.tipLab mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.mas_equalTo(self.mas_left).offset(WKDHPX(8));
            make.centerY.mas_equalTo(self.mas_centerY);
            make.right.mas_equalTo(self.mas_right).offset(-WKDHPX(16));
        }];
        
        self.tipLab.attributedText = ({
            NSMutableAttributedString *deviceText = [[NSMutableAttributedString alloc] initWithString:@"Tip"];
            deviceText.color = UIColorHex(#16D2E3);
            deviceText.font = [UIFont fontWithName:DOUYU_Font size:WKDHPX(18)];
            
            NSMutableAttributedString *attachText = [[NSMutableAttributedString alloc] initWithString:@" app上的数据和机器上的不一致？"];
            attachText.color = UIColorHex(#A2EDF4);
            attachText.font = [UIFont systemFontOfSize:WKDHPX(15.0)];
            [deviceText appendAttributedString:attachText];
            [deviceText addAttribute:NSBaselineOffsetAttributeName value:@2 range:NSMakeRange(deviceText.length - attachText.length, attachText.length)];
            deviceText;
        });
    }
    return self;
}

@end
