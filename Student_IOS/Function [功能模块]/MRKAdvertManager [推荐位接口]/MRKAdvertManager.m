//
//  MRKAdvertManager.m
//  Student_IOS
//
//  Created by merit on 2022/6/21.
//

#import "MRKAdvertManager.h"
#import "MRKPositionCode.h"


@interface MRKAdvertManager ()
@property (nonatomic, strong) MRKAdvertRequest *api;
@end

@implementation MRKAdvertManager

+ (NSString*)deviceBannerPositionCode:(NSNumber *)typeId{
    switch (typeId.integerValue) {
        case BicycleEquipment: //动感单车
            return MRKSpinningBannerCode;
            break;
        case TreadmillEquipment: //跑步机
            return MRKRunningBannerCode;
            break;
        case BoatEquipment: //划船机
            return MRKRowingBannerCode;
            break;
        case EllipticalEquipment: //椭圆机
            return MRKElipticalBannerCode;
            break;
        case JinMoQiangEquipment: //筋膜枪
            return MRKFasciagunBannerCode;
            break;
        case SkipRopeEquipment: //跳绳
            return MRKRopeskippingBannerCode;
            break;
        case PowerEquipment: //力量站
            return MRKPowerBannerCode;
            break;
        case KettleBellEquipment: //壶铃
            return MRKKettleBellBannerCode;
            break;
        default:
            break;
    }
    
    return MRKRecommendBannerCode;
}


+ (NSString*)deviceAlertPositionCode:(NSNumber *)typeId{
    switch (typeId.integerValue) {
        case BicycleEquipment: //动感单车
            return MRKSpinningPopupCode;
            break;
        case TreadmillEquipment: //跑步机
            return MRKRunningPopupCode;
            break;
        case BoatEquipment: //划船机
            return MRKRowingPopupCode;
            break;
        case EllipticalEquipment: //椭圆机
            return MRKElipticalPopupCode;
            break;
        case StairClimbEquiment: // 爬楼机
            break;
        case JinMoQiangEquipment: //筋膜枪
            return MRKFasciagunPopupCode;
            break;
        case SkipRopeEquipment: //跳绳
            return MRKRopeskippingPopupCode;
            break;
        case SmallEquipment: //小件
            break;
        case HeartEquipment: //心率带
            break;
        case HoopEquipment: //呼啦圈
            break;
        case FLSBEquipment: //飞力士棒
            break;
        default:
            break;
    }
    return @"";
}


+ (void)mrkRequestPositionCode:(NSString *)code completeBlockWithSuccess:(DataCompletionBlock)block{
    [self mrkRequestPositionCode:code
                       productId:@""
                        waitTime:15.0
                     notTipError:YES
        completeBlockWithSuccess:block];
}

+ (void)mrkRequestPositionCode:(NSString *)code productId:(NSString *)productid completeBlockWithSuccess:(DataCompletionBlock)block {
    [self mrkRequestPositionCode:code
                       productId:productid
                        waitTime:15.0
                     notTipError:YES
        completeBlockWithSuccess:block];
}

+ (void)mrkRequestPositionCode:(NSString *)code
                     productId:(NSString *)productid
                      waitTime:(NSTimeInterval)time
                   notTipError:(BOOL)showError
      completeBlockWithSuccess:(DataCompletionBlock)block{
    
    MRKAdvertRequest *api = [[MRKAdvertRequest alloc] init];
    api.timeoutInterval = time;
    api.notTipError = showError;
    NSMutableDictionary *parms =  @{
        @"channel" : @"merach_app",
        @"positionCode" : code?:@""
    }.mutableCopy;
    if (productid.isNotBlank) {
        parms[@"productId"] = productid;
    }
    api.requestData = parms;
    [api startWithCompletionBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        id data = [request.responseObject valueForKeyPath:@"data"];
        NSLog(@"code ====== %@", data);
        MRKAdvertDataModel *model = [MRKAdvertDataModel modelWithJSON:data];
        block(model);
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        block(nil);
    }];
}


+ (void)mrkRequestPositionCodes:(NSArray<NSString *> *)codes productId:(NSString *)productid completeBlockWithSuccess:(DatasCompletionBlock)block {
    NSString *codeString = [codes componentsJoinedByString:@","];
    NSMutableDictionary *dic =  @{
        @"channel" : @"merach_app",
        @"positionCode" : codeString?:@""
    }.mutableCopy;
    if (productid.isNotBlank) {
        [dic setValue:productid forKey:@"productId"];
    }
    [MRKBaseRequest mrkGetRequestUrl:@"/app/AppAdvertisingController/listAdverts"
                             andParm:dic
            completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        id data = [request.responseObject valueForKeyPath:@"data"];
        NSArray *array = [NSArray modelArrayWithClass:[MRKAdvertDataModel class] json:data];
        block(array);
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        block(nil);
    }];
}

@end





@implementation MRKAdvertRequest

//请求超时时间
- (NSTimeInterval)requestTimeoutInterval {
    return self.timeoutInterval;
}

- (NSString *)requestUrl {
    return @"/app/AppAdvertisingController/getAdvert";
}

- (YTKRequestMethod)requestMethod {
    return YTKRequestMethodGET;
}

- (YTKRequestSerializerType)requestSerializerType {
    return YTKRequestSerializerTypeJSON;
}

@end


