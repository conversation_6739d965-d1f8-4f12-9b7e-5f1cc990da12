//
//  MRKUltraTrainCourseDetailVC.m
//  Student_IOS
//
//  Created by merit on 2022/12/5.
//

#import "MRKUltraTrainCourseDetailVC.h"
#import "MRKUltraDetailHeadView.h"
#import "MRKUltraDetailLinksView.h"
#import "MRKUltraDetaiAdviceView.h"
#import "MRKUltraDetailFooterView.h"
#import "MRKUltraCourseViewModel.h"
#import "MRKUltraTrainController.h"
#import "MRKShareView.h"
#import "CourseTipController.h"

@interface MRKUltraTrainCourseDetailVC ()<UIScrollViewDelegate>
@property (nonatomic, strong) UIScrollView *scrollView; //滚动
@property (nonatomic, strong) UIView *contentView;      //内容承载
@property (nonatomic, strong) MRKUltraCourseDetailModel *detailModel; //课程详情
@property (nonatomic, strong) MRKCourseBottomButton *bottomButton;    //开启训练按钮
@end

@implementation MRKUltraTrainCourseDetailVC

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    // 请求用户信息，刷新课程状态
    [MRKRequestServiceData getUserInfo:@{} success:^(id data) {
        id d = data;
        if ([d isKindOfClass:[NSDictionary class]]) {
            NSDictionary *dic = [d mutableCopy];
            [Login doLogin:dic];
        }
        [self refreshBottomState];
    } failure:^(id data) {
    }];
}

- (void)viewDidLoad {
    self.tracePageId = @"page_intelligent_control_course_detail";
    if ([self.courseId isNotEmpty]) {
        self.tracePara = @{@"course_id" : self.courseId};
    }
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    [self initUI];
    
    @weakify(self);
    MrkEmptyView *emptyView = [[MrkEmptyView alloc] init];
    emptyView.fullCoverSuperView = YES;
    emptyView.tapEmptyViewBlock = ^{
        [self_weak_ initData];
    };
    emptyView.errorBtnClickBlock = ^{
        [self_weak_ initData];
    };
    self.view.pageEmptyView = emptyView;
    
    //  请求数据
    [self initData];
}
- (void)initUI {
    self.barControllDistance = DHPX(210) - kNavBarHeight;
    self.mrkContentView.backgroundColor = [UIColor colorWithHexString:@"#F8F8FA"];
    
    [self.mrkContentView addSubview:self.scrollView];
    [self.scrollView mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.edges.mas_equalTo(UIEdgeInsetsMake(0, 0, 0, 0));
        make.top.left.right.mas_equalTo(0);
    }];
    
    [self.scrollView addSubview:self.contentView];
    [self.contentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsMake(0, 0, 0, 0));
        make.width.mas_equalTo(RealScreenWidth);
    }];
}


- (void)initData {
    // 请求课程详情
    [self.view beginLoading];
    @weakify(self);
    [MRKUltraCourseViewModel ultraCourseDetail:self.courseId success:^(MRKUltraCourseDetailModel * model) {
        @strongify(self);
        [self.view endLoading];
        [self.view hiddenEmptyView];
        
        self.detailModel = model;
        // 更新课程详情UI
        [self updateUI];
//        for (MRKCourseNodeModel *node in self.detailModel.nodesArray) {
//            NSLog(@"阻力建议值：%@",node.adviseNum);
//        }
    } failer:^{
        @strongify(self);
        [self.view endLoading];
        [self.view mrkShowNetworkErrorEmptyView];
    }];
}


//  请求到数据
- (void)updateUI {
    [self.contentView removeAllSubviews];
    
    self.rollBarTitle = self.detailModel.name ?: @"";
    
    MRKUltraDetailHeadView *headView = [[MRKUltraDetailHeadView alloc] init];
    headView.backgroundColor = UIColor.whiteColor;
    [self.contentView addSubview:headView];
    [headView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.right.mas_equalTo(0);
    }];
    // 课程详情
    [headView setCourseDetail:self.detailModel];
    
    MRKUltraDetailLinksView *linksView = [[MRKUltraDetailLinksView alloc] init];
    linksView.backgroundColor = UIColor.whiteColor;
    [self.contentView addSubview:linksView];
    [linksView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(headView.mas_bottom).offset(12);
        make.left.right.mas_equalTo(0);
    }];
    // 训练环节
    [linksView setLinksDetail:self.detailModel.courseCataloguePOS];

    
    
    // 建议图表1
    MRKUltraDetaiAdviceView *view1 = [[MRKUltraDetaiAdviceView alloc] init];
    view1.backgroundColor = UIColor.whiteColor;
    [self.contentView addSubview:view1];
    [view1 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(linksView.mas_bottom).offset(12);
        make.left.right.mas_equalTo(0);
        make.height.mas_equalTo(278);
    }];
    
    // 建议图表2
    MRKUltraDetaiAdviceView *view2 = [[MRKUltraDetaiAdviceView alloc] init];
    view2.backgroundColor = UIColor.whiteColor;
    [self.contentView addSubview:view2];
    [view2 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(view1.mas_bottom).offset(12);
        make.left.right.mas_equalTo(0);
        make.height.mas_equalTo(278);
    }];
    
    // 填充建议图标数据
    [view1 setTableDetail:[self adviceView1Data]];
    [view2 setTableDetail:[self adviceView2Data]];
    
    
    // 协议
    @weakify(self);
    MRKUltraDetailProtolView *protolView = [[MRKUltraDetailProtolView alloc] init];
    [protolView readStates:NO];
    [self.contentView addSubview:protolView];
    [protolView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(view2.mas_bottom).offset(12);
        make.left.right.bottom.mas_equalTo(0);
        make.height.mas_equalTo(60);
    }];
    protolView.selectBlock = ^{
        NSLog(@"阅读协议");
        [self_weak_ gotoProtocal];
    };
    
    
    UIView *bottomView = [[UIView alloc] init];
    bottomView.backgroundColor = [UIColor colorWithHexString:@"#FF2451"];
    [self.view addSubview:bottomView];
    [bottomView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.scrollView.mas_bottom).offset(0);
        make.left.right.bottom.mas_equalTo(0);
        make.height.mas_equalTo(60 + kSafeArea_Bottom);
    }];
    
    MRKCourseBottomButton *bottomButton = [[MRKCourseBottomButton alloc] init];
    [bottomView addSubview:bottomButton];
    [bottomButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.right.mas_equalTo(0);
        make.height.mas_equalTo(60);
    }];
    self.bottomButton = bottomButton;
    // 判断需不需要会员，并且自己是不是会员
    [self refreshBottomState];
    
    bottomButton.clickdHandler = ^(NSString * _Nonnull title) {
        @strongify(self);
        [self bottomAction];
    };
}

#pragma mark - 去上课
// 刷新底部按钮状态
- (void)refreshBottomState {
    //课程是vip 非限免 并且直接不是vip
    BOOL isMember = [UserInfo isMember];
    if (self.detailModel.isVip.boolValue && !self.detailModel.isFree.boolValue && !isMember) {
        self.bottomButton.titleStr = @"成为会员 无限训练";
    }else {
        self.bottomButton.titleStr = @"开始训练";
    }
   
}
// 底部按钮点击
- (void)bottomAction {
    BOOL isMember = [UserInfo isMember];
    if (self.detailModel.isVip.boolValue && !self.detailModel.isFree.boolValue && !isMember) {
        // 去会员
        [[RouteManager sharedInstance] skipVIP];
    }else {
        self.traceEventId = @"btn_intelligent_control_course_detail_start";
        self.tracePara = @{@"course_id" : self.courseId};
        //  判断是否同意了协议
//        if (![MRKUltraCourseViewModel ultraProtocalReadState]) {
//            [self gotoProtocal];
//            return;
//        }
        // 判断是否连接设备
        @weakify(self);
        [[RouteManager sharedInstance] skipUltraTrain:self.detailModel.equipmentId skipAction:^{
            @strongify(self);
            MRKUltraTrainController *vc = [[MRKUltraTrainController alloc] init];
            vc.courseModel = self.detailModel;
//            [self.navigationController pushViewController:trainVC animated:YES];
            vc.pushController = self;
            MRKNavigationController *nav = [[MRKNavigationController alloc] initWithRootViewController:vc];
            nav.modalPresentationStyle = UIModalPresentationFullScreen;
            [self presentViewController:nav animated:NO completion:nil];
        }];
    }
}

// 去协议页面
- (void)gotoProtocal {
    jxt_getSafeMainQueue(^{
        CourseTipController *tipVC = [[CourseTipController alloc] init];
        tipVC.tipType = CourseTipTypeUltra;
        tipVC.hidesBottomBarWhenPushed = YES;
        [self.navigationController pushViewController:tipVC animated:YES];
    });
}

#pragma mark - 分享
-(UIImage *)mrkNavigationBarRightButtonImage:(UIButton *)rightButton navigationBar:(MRKNavigationBar *)navigationBar {
    return [UIImage imageNamed:@"icon_share_W"];
}
- (void)rightButtonEvent:(UIButton *)sender navigationBar:(MRKNavigationBar *)navigationBar {
    sender.traceEventId = @"btn_all_share";
    sender.tracePara = @{@"course_id": self.courseId?:@""};
    [MBProgressHUD showLodingWithMessage:@"" view:self.view];
    [MRKUltraCourseViewModel ultraShareCourse:self.courseId success:^(NSString * _Nonnull url) {
        [MBProgressHUD hideHUDForView:self.view];
        if (url.isNotBlank) {
//            MRKShareView *view = [MRKShareView shareViewWithType:ShareActionViewReport andViewController:self];
//            view.shareUrl = url;
//            view.shareTitle = self.detailModel.name;
//            [view showInView:self.view];
            [FlutterManager shareCommonVCWithTitle:self.detailModel.name text:@"" link:url thumbImage:@""];
        }
    } failer:^{
        [MBProgressHUD hideHUDForView:self.view];
        [MBProgressHUD showMessage:@"请求错误,请重试" toView:self.view];
    }];
}

- (void)changeNavBarStatus:(BOOL)isChange {
    [super changeNavBarStatus:isChange];
    UIImage *rightImage = isChange ? [UIImage imageNamed:@"icon_share_B"] : [UIImage imageNamed:@"icon_share_W"];
    if ([self.mrk_navgationBar.rightView isKindOfClass:[UIButton class]]) {
        UIButton *btn = (UIButton *)self.mrk_navgationBar.rightView;
        [btn setImage:rightImage forState:UIControlStateNormal];
    }
}

#pragma mark - lazy
- (UIScrollView *)scrollView {
    if (!_scrollView) {
        _scrollView = [[UIScrollView alloc] init];
        _scrollView.delegate = self;
    }
    return _scrollView;
}

- (UIView *)contentView {
    if (!_contentView) {
        _contentView = [[UIView alloc] init];
    }
    return _contentView;
}



#pragma mark - 整理表格数据
/*
 * 单车 ，椭圆机 展示 阻力 踏频
 * 划船机           阻力 桨频
 * 跑步机           速度 坡度
 * 阻力 / 坡度  y轴展示高中低
 * 桨频 踏频 速度 y轴展示具体值
 */
- (MRKUltraAdviceTableModel *)adviceView1Data {
    MRKUltraAdviceTableModel *model = [[MRKUltraAdviceTableModel alloc] init];
    NSInteger equimentID = self.detailModel.equipmentId.intValue;
    
    // 整理表格需要用到的数据
    NSMutableArray *section = [NSMutableArray array];
    // 大节
    for (MRKCourseCataloguePOS *pos in self.detailModel.courseCataloguePOS) {
        NSMutableArray *datas = [NSMutableArray array];
        // 小节
        for (MRKCourseNodeModel *node in pos.courseLinks) {
            int adviceNum;
            if(TreadmillEquipment == equimentID){
                adviceNum = node.maxNum.intValue / 10;
            }else {
                adviceNum = node.adviseNum.intValue;
            }
            NSLog(@"第一个view的建议值：%d",adviceNum);
            WKAdviceTableData *data = [[WKAdviceTableData alloc] initStart:node.beginTime.intValue end:node.endTime.intValue advice:adviceNum];
            [datas addObject:data];
        }
        [section addObject:datas];
    }
    
    model.tableData = section;
    
    switch (equimentID) {
        case BicycleEquipment:  //动感单车
        case EllipticalEquipment:  //椭圆机
        case BoatEquipment:     //划船机
        {
            
            NSNumber *max = [self.detailModel.nodesArray valueForKeyPath:@"@max.adviseNum.intValue"];
            int maxadvice = max.intValue <= 0 ? 32 : max.intValue;
//            maxadvice = ceil(maxadvice / 10.0) * 10;  //不满10的倍数。向上取整
            NSLog(@"最大阻力：%d",maxadvice);
            model.title = @"建议阻力";
            model.maxYAxis = maxadvice;
            model.yTitle = @[@"高",@"中",@"低"];
        }
            break;
             
        case TreadmillEquipment://跑步机
        {
            // 取出最大速度值
            NSNumber *max = [self.detailModel.nodesArray valueForKeyPath:@"@max.maxNum.intValue"];
            int maxspeed = max.intValue / 10 <= 0 ? 20 : max.intValue / 10;
//            maxspeed = ceil(maxspeed / 10.0) * 10;  //不满10的倍数。向上取整
            maxspeed = ceil(maxspeed / 2.0) * 2;  //不满10的倍数。向上取整
            NSLog(@"最大速度：%d",maxspeed);
            model.title = @"建议速度";
            model.maxYAxis = maxspeed;
            int jiange = 2;
            NSMutableArray *arr = [NSMutableArray array];
            for (int i = maxspeed; i >= 0; i = i -jiange) {
                [arr addObject:[NSString stringWithFormat:@"%d",i]];
            }
            model.yTitle = arr;
        }
            break;
            
        default:
            break;
    }
    
    return model;
}

- (MRKUltraAdviceTableModel *)adviceView2Data {
    MRKUltraAdviceTableModel *model = [[MRKUltraAdviceTableModel alloc] init];
    NSInteger equimentID = self.detailModel.equipmentId.intValue;
    
    // 整理表格需要用到的数据
    NSMutableArray *section = [NSMutableArray array];
    // 大节
    for (MRKCourseCataloguePOS *pos in self.detailModel.courseCataloguePOS) {
        NSMutableArray *datas = [NSMutableArray array];
        // 小节
        for (MRKCourseNodeModel *node in pos.courseLinks) {
            int adviceNum;
            if(TreadmillEquipment == equimentID){
                adviceNum = node.adviseNum.intValue;
            }else {
                adviceNum = node.maxNum.intValue;  //踏频 或 桨频
            }
            NSLog(@"第二个view的建议值：%d",adviceNum);
            WKAdviceTableData *data = [[WKAdviceTableData alloc] initStart:node.beginTime.intValue end:node.endTime.intValue advice:adviceNum];
            [datas addObject:data];
        }
        [section addObject:datas];
    }
    
    model.tableData = section;
    
    switch (equimentID) {
        case BicycleEquipment:  //动感单车
        case EllipticalEquipment:  //椭圆机
        {
            NSNumber *max = [self.detailModel.nodesArray valueForKeyPath:@"@max.maxNum.intValue"];
            int maxrpm = max.intValue <= 0 ? 160 : max.intValue ;
            maxrpm = ceil(maxrpm / 10.0) * 10;  //不满10的倍数。向上取整
            maxrpm = ceil(maxrpm / 20.0) * 20;  //对20除不尽，向上取整
            NSLog(@"最大踏频rpm：%d",maxrpm);
            model.title = @"建议踏频rpm";
            model.maxYAxis = maxrpm;
            int jiange = 20;
            NSMutableArray *arr = [NSMutableArray array];
            for (int i = maxrpm; i >= 0; i = i - jiange) {
                [arr addObject:[NSString stringWithFormat:@"%d",i]];
            }
            model.yTitle = arr;
        }
            break;
        case BoatEquipment:     //划船机
        {
            NSNumber *max = [self.detailModel.nodesArray valueForKeyPath:@"@max.maxNum.intValue"];
            int maxspm = max.intValue <= 0 ? 60 : max.intValue ;
            maxspm = ceil(maxspm / 10.0) * 10;  //不满10的倍数。向上取整
            NSLog(@"最大桨频spm：%d",maxspm);
            model.title = @"建议桨频spm";
            model.maxYAxis = maxspm;
            int jiange = 10;
            NSMutableArray *arr = [NSMutableArray array];
            for (int i = maxspm; i >= 0; i = i -jiange) {
                [arr addObject:[NSString stringWithFormat:@"%d",i]];
            }
            model.yTitle = arr;
        }
            break;
        case TreadmillEquipment://跑步机
        {
            NSNumber *max = [self.detailModel.nodesArray valueForKeyPath:@"@max.adviseNum.intValue"];
            int maxslope = max.intValue <= 0 ? 20 : max.intValue;
//            maxslope = ceil(maxslope / 10.0) * 10;  //不满10的倍数。向上取整
            NSLog(@"最大坡度值：%d",maxslope);
            model.title = @"建议坡度";
            model.maxYAxis = maxslope;
            model.yTitle = model.yTitle = @[@"高",@"中",@"低"];
        }
            break;
            
        default:
            break;
    }
    
    return model;
}


@end
