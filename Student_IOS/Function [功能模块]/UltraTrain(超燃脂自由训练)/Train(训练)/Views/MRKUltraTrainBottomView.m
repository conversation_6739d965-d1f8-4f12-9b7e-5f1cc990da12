//
//  MRKUltraTrainBottomView.m
//  MeritInternation
//
//  Created by merit on 2022/9/20.
//

#import "MRKUltraTrainBottomView.h"
#import "MRKAdjustAnimationView.h"

@interface MRKUltraTrainBottomView()<MRKUltraTrainProgressDelegate,MRKUltraTrainDataViewDelegate>
@property (nonatomic, strong) MRKUltraTrainProgressView *progressView; //第一屏
@property (nonatomic, strong) MRKUltraTrainDataView *trainView;        //第二屏
@end

@implementation MRKUltraTrainBottomView

- (void)dealloc {
    NSLog(@"😊😊😊-----dealloc-----😊😊😊%@" , NSStringFromClass([self class]));
}

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        [self initUI];
    }
    return self;
}

- (void)initUI {
    [self addSubview:self.progressView];
    [self addSubview:self.trainView];
    
    [self.progressView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsMake(0, kStatusBarHeight, 0, 0));
    }];
    [self.trainView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsMake(0, kStatusBarHeight, 0, 0));
    }];
}

- (void)showFirst:(BOOL)show {
    self.progressView.hidden = !show;
    self.trainView.hidden = show;
}

#pragma mark - lazy
- (MRKUltraTrainProgressView *)progressView {
    if (!_progressView) {
        _progressView = [[MRKUltraTrainProgressView alloc] init];
        _progressView.delegate = self;
    }
    return _progressView;
}

- (MRKUltraTrainDataView *)trainView {
    if (!_trainView) {
        _trainView = [[MRKUltraTrainDataView alloc] init];
        _trainView.hidden = YES;
        _trainView.delegate = self;
    }
    return _trainView;
}

#pragma mark - ui交互 MRKUltraTrainProgressDelegate  && MRKUltraTrainDataViewDelegate
// 课程暂停
- (void)ultraTrainProgressPauseClick:(UIButton *)sender {
    if (_delegate && [_delegate respondsToSelector:@selector(ultraTrainBottomPauseClick:)]) {
        [_delegate ultraTrainBottomPauseClick:sender];
    }
}
// 自动调节按钮点击
- (void)ultraTrainAdjustClick:(UIButton *)sender {
    if (_delegate && [_delegate respondsToSelector:@selector(ultraTrainBottomAdjustClick:)]) {
        [_delegate ultraTrainBottomAdjustClick:sender];
    }
}

#pragma mark - 第一屏 方法
/// 课程总时长
/// @param time  总时长
- (void)courseTotalTime:(NSTimeInterval)time {
    self.progressView.totalTime = time;
}
/// 更新进度时长
- (void)updateBottomPorgress:(NSTimeInterval)time {
    [self.progressView updateUltraProgress:time];
}

/// 更新播放状态
/// @param state  播放状态
- (void)updateBottomPlayState:(BOOL)state {
    self.progressView.pauseButton.selected = state;
}

#pragma mark - 底部 数据填充  第二屏

- (void)setEquimentModel:(EquipmentDetialModel *)equimentModel {
    _equimentModel = equimentModel;
    self.trainView.equimentModel = equimentModel;  //底部设备数据填充
}

- (void)updateBottomEquimentDisplay:(NSArray<MRKEqDisplayModel *> *)display {
    [self.trainView updateEquimentDisplay:display];
}

- (void)setCourseModel:(MRKUltraCourseDetailModel *)courseModel {
    _courseModel = courseModel;
    self.trainView.courseModel = courseModel;
}

- (void)updateBottomEquimentData:(BaseEquipDataModel *)model {
    [self.trainView updateTrainViewData:model];
}

/// 更新建议数据
- (void)updateBottomAdviceTip:(MRKCourseNodeModel *)model {
    [self.trainView updateAdviceTip:model];
}

/// 更新自动调节阻力动画
- (void)updateBottomAdjustAnimation:(MRKCourseNodeModel *)node merit:(BOOL)isMerit {
    [self.trainView updateAnimationAndPlay:node merit:isMerit];
}
@end


#pragma mark - MRKUltraTrainProgressView 第一屏进度 view
@implementation MRKUltraTrainProgressView
- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        [self initUI];
    }
    return self;
}

- (void)initUI {
    
    [self addSubview:self.pauseButton];
    [self.pauseButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_equalTo((IS_IPHONEX_SURE ? - 10: 0) - 10);
        make.left.mas_equalTo(12);
        make.size.mas_equalTo(CGSizeMake(24, 24));
    }];
    
    [self addSubview:self.timeLabel];
    [self.timeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.pauseButton.mas_right).offset(10);
        make.centerY.equalTo(self.pauseButton.mas_centerY);
        make.width.mas_equalTo(50);
    }];
    
    [self addSubview:self.progressView];
    [self.progressView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.timeLabel.mas_right).offset(12);
        make.centerY.equalTo(self.pauseButton.mas_centerY);
        make.height.mas_equalTo(2);
    }];
    
    [self addSubview:self.totalTimeLabel];
    [self.totalTimeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_equalTo(-14);
        make.centerY.equalTo(self.pauseButton.mas_centerY);
        make.left.equalTo(self.progressView.mas_right).offset(12);
        make.width.mas_equalTo(60);
    }];
    
}
- (UIButton *)pauseButton{
    if (!_pauseButton) {
        _pauseButton = [[UIButton alloc] init];
        [_pauseButton setImage:[AlivcImage imageNamed:@"icon_play"] forState:UIControlStateNormal];
        [_pauseButton setImage:[AlivcImage imageNamed:@"icon_pause"] forState:UIControlStateSelected];
        [_pauseButton addTarget:self action:@selector(playButtonClicked:) forControlEvents:UIControlEventTouchUpInside];
        [_pauseButton setEnlargeEdgeWithTop:10 right:10 bottom:10 left:10];
    }
    return _pauseButton;
}

- (MrkProgressPopView *)progressView{
    if (!_progressView){
        _progressView = [[MrkProgressPopView alloc] init];
        _progressView.trackTintColor = [UIColor colorWithWhite:1 alpha:0.2];
        _progressView.popUpViewAnimatedColors = @[UIColorHex(#FF2451),
                                                    UIColorHex(#FF2451)];
        _progressView.progress = 0.0;
        [_progressView showPopUpViewAnimated:YES];
    }
    return _progressView;
}
- (UILabel *)timeLabel{
    if (!_timeLabel) {
        _timeLabel = [[UILabel alloc] init];
        _timeLabel.textAlignment = NSTextAlignmentRight;
        _timeLabel.font = [UIFont fontWithName:fontNamePing size:14];
        [_timeLabel setTextColor:[UIColor whiteColor]];
    }
    return _timeLabel;
}

- (UILabel *)totalTimeLabel{
    if (!_totalTimeLabel) {
        _totalTimeLabel = [[UILabel alloc] init];
        _totalTimeLabel.textAlignment = NSTextAlignmentLeft;
        _totalTimeLabel.font = [UIFont fontWithName:fontNamePing size:14];
        [_totalTimeLabel setTextColor:[UIColor whiteColor]];
    }
    return _totalTimeLabel;
}


- (void)playButtonClicked:(UIButton *)sender {
    sender.selected = !sender.selected;
    if (_delegate && [_delegate respondsToSelector:@selector(ultraTrainProgressPauseClick:)]) {
        [_delegate ultraTrainProgressPauseClick:sender];
    }
}

// 设置课程总时长
- (void)setTotalTime:(NSTimeInterval)totalTime {
    _totalTime = totalTime;
    self.totalTimeLabel.text = [NSString mrk_takeTimeSecondTransForMin:totalTime];
}

//跟新进度
- (void)updateUltraProgress:(NSTimeInterval)currentTime {
    self.timeLabel.text = [NSString mrk_takeTimeSecondTransForMin:currentTime];
    [self.progressView setProgress:currentTime / self.totalTime animated:NO];
}
@end


#pragma mark - MRKUltraTrainDataView 第二屏 训练数据 view
@implementation MRKUltraTrainDataView
- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        [self initUI];
    }
    return self;
}

- (void)initUI {
    [self addSubview:self.trainDataView];
    [self addSubview:self.adjustButton];
    
    [self.trainDataView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_equalTo(IS_IPHONEX_SURE ? -10 : 0);
        make.left.mas_equalTo(12);
        make.height.mas_equalTo(90);
    }];
    
    [self.adjustButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.trainDataView.mas_right).offset(12);
        make.right.mas_equalTo(-12);
        make.size.mas_equalTo(CGSizeMake(0, 60));
        make.bottom.mas_equalTo(IS_IPHONEX_SURE ? -10 : -2);
    }];
}

// 根据请求到的数据,填充底部数据
- (void)setEquimentModel:(EquipmentDetialModel *)equimentModel {
    _equimentModel = equimentModel;
    // 更新UI 根据是否是超燃脂设备，展示 自动调节阻力按钮是否显示
    [self updateTrainDataUI];
}
// 填充设备显示数据
- (void)updateEquimentDisplay:(NSArray<MRKEqDisplayModel *> *)display {
    self.trainDataView.deviceDisplayArr = display;
    [self updateTrainDataUI];
}
// 填充课程数据
- (void)setCourseModel:(MRKUltraCourseDetailModel *)courseModel {
    _courseModel = courseModel;
    self.trainDataView.courseModel = courseModel;
}

- (void)updateTrainDataUI {
    
    //    NSInteger itemc = [self.trainDataView getItemCount];
    NSInteger itemc = 5;
    //计算自动调节阻力按钮的宽度
    CGFloat width = (MaxMainWidth - kStatusBarHeight - 24) / (itemc + 1);

    BOOL speed = [self.trainDataView trainContainView:50012];
    BOOL drag = [self.trainDataView trainContainView:50005];
    // 电磁空设备，隐藏
    if (self.equimentModel.isElectromagneticControl && !_isNoMerit && (speed || drag)) {
        self.adjustButton.hidden = NO;
        self.adjustButton.selected = YES;
        [self.adjustButton mas_updateConstraints:^(MASConstraintMaker *make) {
            make.right.mas_equalTo(-24);
            make.size.mas_equalTo(CGSizeMake(width / 2, 60));
        }];
        
        [self.trainDataView updateControlState:NO];
    }else {
        self.adjustButton.hidden = YES;
        self.adjustButton.selected = NO;
        [self.adjustButton mas_updateConstraints:^(MASConstraintMaker *make) {
            make.right.mas_equalTo(-12);
            make.size.mas_equalTo(CGSizeMake(0, 60));
        }];
    
        [self.trainDataView updateControlState:YES];
    }
}

/// 自动调节阻力
- (void)adjustAction:(UIButton *)sender {
    sender.selected = !sender.selected;
    [MBProgressHUD showMessage:sender.selected ?@"开启AI调节" : @"关闭AI调节"];
    // 根据是否自动调节,文字是否变色
    [self.trainDataView updateControlState:!sender.selected];
    
    if (_delegate && [_delegate respondsToSelector:@selector(ultraTrainAdjustClick:)]) {
        [_delegate ultraTrainAdjustClick:sender];
    }
}

//-----------  填充训练内容数据
- (void) updateTrainViewData:(BaseEquipDataModel *)model {
    ///
    NSLog(@"更新数据踏频 ==== %@", model.spm);
    [self.trainDataView dataAdapterUpdateWithModel:model];
    
    jxt_getSafeMainQueue(^{
        if (!self.trainDataView.meterView.hidden && model) {
//            [self.trainDataView.meterView updateSpmVlue:model.spm.intValue];
            [self.trainDataView.meterView setPointerValue:@(model.spm.intValue)];
        }
    });
}

// 更新建议数据 - 2024.1.15 ys -修改
- (void)updateAdviceTip:(MRKCourseNodeModel *)model {
    if (self.courseModel.equipmentId.intValue == BicycleEquipment ||
        self.courseModel.equipmentId.intValue == EllipticalEquipment) {///单车、椭圆机的坡度调节
     
        [self updateResitanceTip:model];
        return;
    }
    
    if (self.courseModel.equipmentId.intValue == TreadmillEquipment) {
        
    }else {
        [self updateResitanceTip:model];
    }
}

// 更新踏频表盘 - 2024.1.15 ys -修改
- (void)updateResitanceTip:(MRKCourseNodeModel *)model {
    BOOL rpm = [self.trainDataView trainContainView:50004];
    BOOL spm = [self.trainDataView trainContainView:50008];
    if ((model.minNum.intValue == 0 && model.maxNum.intValue == 0) || (!rpm && !spm)) {
        self.trainDataView.meterView.hidden = YES;
        return;
    }
    self.trainDataView.meterView.hidden = NO;
//    [self.trainDataView.meterView adviceValueMax:model.maxNum.intValue mianValue:model.minNum.intValue];
    self.trainDataView.meterView.dialMinValue = @(model.minNum.intValue);
    self.trainDataView.meterView.dialMaxValue = @(model.maxNum.intValue);
    [self.trainDataView.meterView reloadExerciseDialView];
}



// ------------ 更新动画内容,并播放动画
- (void)updateAnimationAndPlay:(MRKCourseNodeModel *)node merit:(BOOL)isMerit {
    if (isMerit) {
        [self meritAnimation:node];
    }else {
        [self noMeritAnimation:node];
    }
}

// 自动调节动画
- (void)meritAnimation:(MRKCourseNodeModel *)node {
  
    NSString *valueStr = @""; //值
    if (self.courseModel.equipmentId.intValue == TreadmillEquipment) {
        // 跑步机提示速度
        if (node.maxNum.intValue <= 0) {
            return;
        }
        
        ///判断是否有坡度
        if (node.adviseNum.intValue > 0 && self.equimentModel.showSlope.boolValue) {
            valueStr = [NSString stringWithFormat:@"速度将调至%.0f，坡度将调至%@", (node.maxNum.floatValue / 10.0), node.adviseNum];
        }else{
            valueStr = [NSString stringWithFormat:@"速度将调至%.0f", (node.maxNum.floatValue / 10.0)];
        }
    } else {
        //非跑步机提示阻力
        if (node.adviseNum.intValue <= 0) {
            return;
        }
        
        ///判断是否有坡度
        if (node.slopeNum.intValue > 0 && self.equimentModel.showSlope.boolValue) {
            valueStr = [NSString stringWithFormat:@"阻力将调至%@，坡度将调至%@", node.adviseNum, node.slopeNum];
        }else{
            valueStr = [NSString stringWithFormat:@"阻力将调至%@", node.adviseNum];
        }
    }
    
    NSLog(@"即将调节的数值是-%@",valueStr);
    UIViewController *controller = [UIViewController currentViewController];
    [MBProgressHUD showMiaToast:valueStr toView:controller.view afterDelay:5.0];
}

// 建议调节动画
- (void)noMeritAnimation:(MRKCourseNodeModel *)node {
    NSString *valueStr = @""; //值
    if (self.courseModel.equipmentId.intValue == TreadmillEquipment) {
        // 跑步机提示速度
        if (node.maxNum.intValue <= 0) {
            return;
        }
        
        valueStr = [NSString stringWithFormat:@"建议速度%.0f", (node.maxNum.floatValue / 10)];
    }else {
        //非跑步机提示阻力
        if (node.adviseNum.intValue <= 0) {
            return;
        }
        valueStr = [NSString stringWithFormat:@"建议阻力%@", node.adviseNum];
    }
    
    NSLog(@"即将调节的数值是-%@",valueStr);
    UIViewController *controller = [UIViewController currentViewController];
    [MBProgressHUD showMiaToast:valueStr toView:controller.view];
}

#pragma mark - lazy
- (MRKTrainDataView *)trainDataView {
    if (!_trainDataView) {
        _trainDataView = [[MRKTrainDataView alloc] init];
    }
    return _trainDataView;
}
- (UIButton *)adjustButton {
    if (!_adjustButton) {
        UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
        [button setImage:[AlivcImage imageNamed:@"icon_autocontrol_off"] forState:UIControlStateNormal];
        [button setImage:[AlivcImage imageNamed:@"icon_autocontrol_on"] forState:UIControlStateSelected];
        [button addTarget:self action:@selector(adjustAction:) forControlEvents:UIControlEventTouchUpInside];
        button.hidden = YES;
        _adjustButton = button;
    }
    return _adjustButton;
}

@end
