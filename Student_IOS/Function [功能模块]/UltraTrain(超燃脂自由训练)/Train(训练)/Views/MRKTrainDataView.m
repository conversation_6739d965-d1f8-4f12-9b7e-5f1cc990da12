//
//  MRKTrainDataView.m
//  MeritInternation
//
//  Created by merit on 2022/9/21.
//

#import "MRKTrainDataView.h"
#import "SJEdgeControlButtonItemAdapter.h"
#import <objc/message.h>

@interface MRKTrainDataView()
@property (nonatomic, strong) UIView *dataView;
@property (nonatomic, strong) TrainingListView *controlView;

@end

@implementation MRKTrainDataView

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        
        [self addSubview:self.controlImageView];
        [self addSubview:self.meterView];
        [self addSubview:self.dataView];
        [self.dataView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.mas_equalTo(UIEdgeInsetsMake(0, 0, 0, 0));
        }];
    }
    return self;
}

- (UIView *)dataView {
    if (!_dataView) {
        _dataView = [[UIView alloc] init];
    }
    return _dataView;
}

- (UIImageView *)controlImageView {
    if (!_controlImageView) {
        _controlImageView = [[UIImageView alloc] initWithImage:[AlivcImage imageNamed:@"bg_autocontrol"]];
        _controlImageView.hidden = YES;
        _controlImageView.frame = CGRectMake(0, 0, 80, 60);
        _controlImageView.contentMode = UIViewContentModeScaleAspectFit;
    }
    return _controlImageView;
}

//- (MRKCustomMeterView *)meterView {
//    if (!_meterView) {
//        _meterView = [[MRKCustomMeterView alloc] initWithFrame:CGRectMake(0, 0, 140, 70)];
//        _meterView.hidden = YES;
//    }
//    return _meterView;
//}

- (TrainDataGaugeView *)meterView {
    if (!_meterView) {
        _meterView = [[TrainDataGaugeView alloc] initWithFrame:CGRectMake(0, 0, 150, 75)];
        _meterView.hidden = YES;
    }
    return _meterView;
}

- (void)setDeviceDisplayArr:(NSArray<MRKEqDisplayModel *> *)deviceDisplayArr {
    _deviceDisplayArr = deviceDisplayArr;
    [self addItemsBottomDataAdapter];
}

/// 添加底部数据展示
- (void)addItemsBottomDataAdapter {
    ///添加设备显示模块
    [self.dataView removeAllSubviews];
//    NSArray *arrrr  = [NSMutableArray arrayWithObject:self.deviceDisplayArr.firstObject];
//    _deviceDisplayArr = [NSMutableArray arrayWithArray:arrrr];
    
    for (MRKEqDisplayModel *model in self.deviceDisplayArr) {
        NSInteger tag = model.displayId.integerValue + 50000;
        TrainingListView *view = [[TrainingListView alloc] init];
        view.topLab.text = model.defaultValue;
        view.bottomLab.text = model.name;
        view.tag = tag;
        [self.dataView addSubview:view];
    }
    
    NSArray *arr = self.dataView.subviews;
    if (arr.count > 1) {
        [arr mas_distributeViewsAlongAxis:MASAxisTypeHorizontal withFixedSpacing:0 leadSpacing:0 tailSpacing:0];
        [arr mas_makeConstraints:^(MASConstraintMaker *make) {
            make.bottom.mas_equalTo(0);
            make.height.mas_equalTo(60);
        }];

    }else{

        [arr mas_makeConstraints:^(MASConstraintMaker *make) {
            make.bottom.left.right.mas_equalTo(0);
            make.height.mas_equalTo(60);
        }];
    }
    
    [self updateUILocation];
}


- (TrainDataGaugeModel *)TrainDataGaugeModel:(EquipmentType)equipmentId andModel:(MRKUltraCourseDetailModel *)dataModel {
    TrainDataGaugeModel *model = [[TrainDataGaugeModel alloc] init];
    model.equipmentType = equipmentId;
    model.planMinDataValue = dataModel.minNum;
    model.planMaxDataValue = dataModel.maxNum;
    model.minDataValue = @0;
    if (equipmentId == BoatEquipment) { model.maxDataValue = @(60); }
    if (equipmentId == BicycleEquipment) { model.maxDataValue = @160; }
    if (equipmentId == EllipticalEquipment) { model.maxDataValue = @120; }
    if (equipmentId == StairClimbEquiment) { model.maxDataValue = @110; }
    return model;
}

- (void)updateUILocation {
    
    EquipmentType equipType = self.courseModel.equipmentId.intValue;
    
    if (equipType == TreadmillEquipment) {
        // 建议速度
        TrainingListView *item =  [self.dataView viewWithTag:50012];
        if (item) {
            [self updateControlLocation:item];
            self.controlView = item;
        }
        
    } else if(equipType ==BicycleEquipment || equipType == EllipticalEquipment) {
        
        ///共感单车/椭圆机
//        self.meterView.number = 8;
//        self.meterView.maxValue = 160;
//        [self.meterView reload];
        
        
        TrainDataGaugeModel *model = [self TrainDataGaugeModel:equipType andModel:self.courseModel];
        self.meterView.dialModel = model;
        [self.meterView reloadExerciseDialView];
        
        // 建议阻力
        TrainingListView *item =  [self.dataView viewWithTag:50005];
        if (item) {
            [self updateControlLocation:item];
            self.controlView = item;
        }
        
        // 踏频
        TrainingListView *item1 =  [self.dataView viewWithTag:50004];
        if (item1) {
            [self updateMeterLocation:item1];
        }
    } else if (equipType ==BoatEquipment) {
//        self.meterView.number = 6;
//        self.meterView.maxValue = 60;
//        [self.meterView reload];
        
        TrainDataGaugeModel *model = [self TrainDataGaugeModel:equipType andModel:self.courseModel];
        self.meterView.dialModel = model;
        [self.meterView reloadExerciseDialView];
        
        // 建议阻力
        TrainingListView *item =  [self.dataView viewWithTag:50005];
        if (item) {
            [self updateControlLocation:item];
            self.controlView = item;
        }
        // 浆频
        TrainingListView *item1 =  [self.dataView viewWithTag:50008];
        if (item1) {
            [self updateMeterLocation:item1];
        }
    }
}


// 更新自动控制image位置
- (void)updateControlLocation:(TrainingListView *)view {

    if (!view) {
        return;
    }
    [self.controlImageView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.mas_bottom).offset(0);
        make.centerX.equalTo(view.mas_centerX);
    }];
    
}


// 更新仪表盘位置
- (void)updateMeterLocation:(TrainingListView *)view {
    if (!view) {
        return;
    }
    [self.meterView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(0);
        make.centerX.equalTo(view.mas_centerX);
        make.size.mas_equalTo(CGSizeMake(150, 75));
    }];
}


- (void)updateControlState:(BOOL)hidden {
    self.controlImageView.hidden = hidden;
    if (hidden) {
        self.controlView.topLab.textColor = [UIColor whiteColor];
        self.controlView.bottomLab.textColor = [UIColor whiteColor];
    }else {
        UIImage *backgroundImage = [UIImage gradientColorImageFromColors:@[[UIColor colorWithHexString:@"#FFF7E6"],
                                                                           [UIColor colorWithHexString:@"#FF5E5E"]] gradientType:GradientTypeTopToBottom imgSize:CGSizeMake(80, 60)];
        self.controlView.topLab.textColor = [UIColor colorWithPatternImage:backgroundImage];
        self.controlView.bottomLab.textColor = [UIColor colorWithPatternImage:backgroundImage];
    }
}


- (BOOL)trainContainView:(NSInteger)tag {
    UIView *view = [self.dataView viewWithTag:tag];
    if (view) {
        return YES;
    }
    return NO;
}


- (UIView *)dataViewWith:(NSInteger)tag {
    UIView *view = [self.dataView viewWithTag:tag];
    return view;
}

- (void)dataAdapterUpdateWithModel:(BaseEquipDataModel *)model{
    dispatch_async(dispatch_get_main_queue(), ^{
        BaseEquipDataModel *m = model.copy;
        for (UIView *view in self.dataView.subviews) {
            TrainingListView *item;
            if ([view isKindOfClass:[TrainingListView class]]) {
                item = (TrainingListView *)view;
            }
            if (!item) {
                return;
            }
            NSInteger tag = (NSInteger) item.tag;
            switch (tag) {
                case 50001: {///运动时间
                    NSString *currentTimeStr = [MRKToolKit MSTimeStrFromSecond:m.totalTimeSecond.intValue];
                    item.topLab.text = currentTimeStr;
                }break;
                case 50002: {///消耗(kcal)
//                    NSString *energy = [NSString stringWithFormat:@"%f", m.energy.doubleValue];
//                    item.topLab.text = m.energy ? [NSString convertDecimalNumber:energy num:1] : @"--";
                    item.topLab.text = m.energy ? [NSString stringWithFormat:@"%.1f", floor(m.energy.floatValue*10)/10.0 ] : @"--";
                }break;
                case 50003: {///心率(bmp)
                    item.topLab.text = m.rate ? (m.rate.intValue == 0 ? @"--" :  m.rate.stringValue) : @"--";
                }break;
                case 50004: case 50008:{///踏频(rpm)/桨频(spm)
                    item.topLab.text = m.spm ? (m.spm.intValue == 0 ? @"--" :  m.spm.stringValue) : @"--";
                }break;
                case 50005: {///阻力(lv)
                    item.topLab.text = m.drag ? (m.drag.intValue == 0 ? @"--" :  m.drag.stringValue) : @"--";
                }break;
                case 50006: {///坡度
                    item.topLab.text = m.gradient ? (m.gradient.intValue == 0 ? @"--" : m.gradient.stringValue) : @"--";
                }break;
                case 50007: {///距离(km)
//                    NSString *totalDistance = [NSString stringWithFormat:@"%f", m.totalDistance.doubleValue];
//                    item.topLab.text = m.totalDistance ? [NSString convertDecimalNumber:totalDistance  num:2 dividing: @"1000"] : @"--";
                    item.topLab.text = m.totalDistance ? [NSString stringWithFormat: @"%.2f" , floor( m.totalDistance.intValue/10.0)/100.0] : @"--";
                }break;
                case 50009:  case 50010: {///个数/圈数
                    item.topLab.text = m.count.stringValue;
                }break;
                case 50011: {///挡位
                    item.topLab.text = m.grade.stringValue;
                }break;
                case 50012: { ///速度(km/h)
                    item.topLab.text = [NSString stringWithFormat:@"%.1f" , m.speed.floatValue];
                }break;
                default: break;
            }
        }
    });
}

@end




@implementation TrainingListView
- (instancetype)init{
    return  [self initWithFrame:CGRectZero];
}

- (instancetype)initWithFrame:(CGRect)frame{
    if (self = [super initWithFrame:frame]) {
        [self addSubview:self.topLab];
        [self addSubview:self.bottomLab];
    }
    return self;
}

- (void)layoutSubviews{
    [super layoutSubviews];
    
    [self.topLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsMake(0, 0, 15, 0));
    }];
    
    [self.bottomLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.topLab);
        make.left.bottom.right.equalTo(@0);
        make.height.equalTo(@15);
    }];
}

- (UILabel *)topLab{
    if (!_topLab) {
        _topLab = [[UILabel alloc] init];
        _topLab.textAlignment = NSTextAlignmentCenter;
        [_topLab setFont:[UIFont fontWithName:Bebas_Font size:32]];
        [_topLab setTextColor:UIColor.whiteColor];
    }
    return _topLab;
}

- (UILabel *)bottomLab{
    if (!_bottomLab) {
        _bottomLab = [[UILabel alloc] init];
        _bottomLab.textAlignment = NSTextAlignmentCenter;
        [_bottomLab setFont:[UIFont systemFontOfSize:12]];
        [_bottomLab setTextColor:UIColor.whiteColor];
    }
    return _bottomLab;
}

@end
