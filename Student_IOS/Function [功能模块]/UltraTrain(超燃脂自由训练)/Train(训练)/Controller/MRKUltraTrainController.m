//
//  MRKUltraTrainController.m
//  MeritInternation
//
//  Created by merit on 2022/9/19.
//

#import "MRKUltraTrainController.h"
#import "MRKUltraTrainView.h"
#import "MRKUltraBlueManager.h"
#import "MRKUltraTrainViewModel.h"
#import "MRKUltraTrainTimeManager.h"
#import "AwesomeIntroGuideView.h"
#import "MRKTimerManager.h"
#import "MRKUltraCourseViewModel.h"
//#import "MRKShareView.h"
#import "MRKAliAudioPlayer.h"
#import "MRKUltraDataReportManager.h"
#import "MRKAbilityQuitAlert.h"
#import "MRKVideoBTConnectView.h"
#import "MRKWarnControlView.h"
#import "FeedbackGeneratorUtil.h"
#import "ExerciseReportWebController.h"
#import "MRKDynamicIslandManager.h"
#import "SpeechSynthesizerManager.h"

#define  ultra_guide @"ultra_guide_location"  //储存是否展示过引导页

@interface MRKUltraTrainController ()<MRKUltraBlueManagerDelegate,MRKUltraTimeManagerDelegate,MRKUltraTrainViewDelegate,MRKUltraDataReportDelegate> {
    int nodeIndex;       //当前小节下标
    int oldIndex;        //原来小节下标 （nodeindex 和 oldindex 不相等时即更换了环节，做更换环节操作）
    BOOL isPlayStart;    //记录初始化播放
    BOOL isCurrentPage;  //是否是在当前页面(退出后台时，要不要做播放控制)
}
@property (nonatomic, assign) BOOL isAdjust;//是否开启了自动调节

@property (nonatomic, strong) MRKUltraTrainViewModel *viewModel;
// -----view
@property (nonatomic, strong) MRKUltraTrainView *ultraView;  //超燃脂自由自由训练的UI

// -----blue
@property (nonatomic, strong) MRKUltraBlueManager *blueManager;  //蓝牙相关操作

// -----课程时间操作
@property (nonatomic, strong) MRKUltraTrainTimeManager *timeManager; //课程时间操作

// -----数据上报
@property (nonatomic, strong) MRKUltraDataReportManager *reportManager; //处理socket数据上报

@property (nonatomic, strong) MRKAbilityQuitAlert *socketAlert;

@property (nonatomic, assign) pthread_rwlock_t rwlock;
@end

@implementation MRKUltraTrainController

- (void)dealloc {
    if (_ultraView.delegate != nil) {
        _ultraView.delegate = nil;
    }
    
    if (_timeManager.delegate != nil) {
        _timeManager.delegate = nil;
    }
    
    if (_blueManager.delegate != nil) {
        _blueManager.delegate = nil;
    }
    
    if (_reportManager.delegate != nil) {
        _reportManager.delegate = nil;
    }

    [[NSNotificationCenter defaultCenter] removeObserver:self];
    pthread_rwlock_destroy(&_rwlock); // 销毁锁
    [[UIApplication sharedApplication] setIdleTimerDisabled:NO];//不允许熄屏
    NSLog(@"😊😊😊-----dealloc-----😊😊😊%@" , NSStringFromClass([self class]));
    if (UIApplication.sharedApplication.applicationState == UIApplicationStateActive) {
        [[MRKDynamicIslandManager shared] end];
    }
}


- (BOOL)shouldAutorotate {
    return YES;
}

- (UIInterfaceOrientationMask)supportedInterfaceOrientations {
    return UIInterfaceOrientationMaskLandscape;
}

- (UIInterfaceOrientation)preferredInterfaceOrientationForPresentation {
    return UIInterfaceOrientationLandscapeRight;
}

- (BOOL)prefersStatusBarHidden{
    return YES;
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    [[UIApplication sharedApplication] setIdleTimerDisabled:YES];//不允许熄屏
    self.fd_interactivePopDisabled = YES;
    [self forceOrientationLandscape];
    
    isCurrentPage = YES; //可以播放控制
    if (isPlayStart && !self.socketAlert.isVisible) {
        // 播放开始
        [self trainplayCheckTreamill];
    }
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    self.fd_interactivePopDisabled = NO;
    
    isCurrentPage = NO;
    // 播放暂停
    [self trainPlayPause];
    [self.blueManager treamillPause];
}

- (void)viewDidLoad {
    [super viewDidLoad];

    //初始化 lock
    pthread_rwlock_init(&_rwlock, NULL);
    
    self.blueManager.delegate = self;
    self.reportManager.delegate = self;
    self.timeManager.delegate = self;
    
    self.isPlaybackFinished = NO;
    
    [self initUI];
    
    // 训练前的准备
    [self allReadyTrain];
    
    // 开始训练
    [self statTrain];
    
//    [[NSNotificationCenter defaultCenter] addObserver:self
//                                             selector:@selector(becomeActive)
//                                                 name:UIApplicationDidBecomeActiveNotification
//                                               object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(resignActive)
                                                 name:UIApplicationDidEnterBackgroundNotification
                                               object:nil];
  
//    [[NSNotificationCenter defaultCenter] addObserver:self
//                                             selector:@selector(resignActive)
//                                                 name:UIApplicationWillResignActiveNotification
//                                               object:nil];
}

- (void)initUI {
    self.view.backgroundColor = [UIColor whiteColor];

    // 训练页面
    [self.view addSubview:self.ultraView];
    [self.ultraView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsMake(0, 0, 0, 0));
    }];
    // 填充屏幕上UI 内容  更新UI内容
    self.ultraView.delegate = self;
    self.ultraView.courseModel = self.courseModel;
    
}

// 去连接设备
//- (void)gotoEquiment  {
//    
//    NSDictionary *param = @{
//        ParametersKey : @{
//            @"name" : self.blueManager.name ?: @""
//        },
//        BlueDeviceType : self.blueManager.equipmentId,
//        @"fromePage" : NSStringFromClass([self class])
//    };
//    [[RouteManager sharedInstance] connectDevice:param];
//}

#pragma mark -  训练前准备
- (void)allReadyTrain {
    // 蓝牙准备
    [self readyBlue];
    // 定时器准备
    [self readyTime];
    // socket准备
    [self readySocket];
}

- (void)readyBlue {
    self.blueManager.equipmentId = self.courseModel.equipmentId; //设备大类
    self.blueManager.nodeModel = self.courseModel.nodesArray.firstObject; // 初始化第一小节课程
    [self.blueManager readyUltraBlue];  //蓝牙配置
}

- (void)readyTime {
    self.timeManager.courseTotalTime = self.courseModel.courseTotalTime;  //设置播放器 总时间
    [self.timeManager readyUltraTime]; //时间配置
}

- (void)readySocket {
    self.reportManager.courseId = self.courseModel.cid;
    self.reportManager.blueData = self.blueManager;
    [self.reportManager ultraSocketOpen]; //打开socket连接
}


/// 请求设备信息 (蓝牙只要连接成功，就更新设备信息)
- (void)updateEquimentInfo {
    // 拿到设备数据，就重新更新底部数据UI，deal的设备模型
    [MRKUltraTrainViewModel requestEquimentInfo:self.courseModel.equipmentId
                                        success:^(EquipmentDetialModel * _Nonnull equiment) {
        [self allUpdateEquimentInfo:equiment];
        if (equiment) {
            [self updateCataloguePOS];
        }
    } failer:^{
        [self allUpdateEquimentInfo:nil];
    }];
}

/// 更新阻力映射教案
- (void)updateCataloguePOS {
    [MRKUltraTrainViewModel requestCourseCataloguePOS:self.courseModel.cid
                                              modelId:self.blueManager.eqModel.idd
                                              success:^(NSArray<MRKCourseNodeModel *> * _Nonnull node) {
        if (node.count > 0) {
            [self.courseModel changeResistance:node];
        }
    } failer:^{}];
}

/// 阻力映射
- (void)changeCataloguePOS:(NSArray<MRKCourseNodeModel *> *)node {
    if (node.count <= 0) {
        return;
    }
    [self.courseModel changeResistance:node];
    self.blueManager.nodeModel = self.courseModel.nodesArray.firstObject; // 初始化第一小节课程
}

/// 根据设备详细信息 更新UI 自动调节是否开启等
- (void)allUpdateEquimentInfo:(EquipmentDetialModel *)equiment {
    if (!equiment) {
        // 提示重新初始化设备信息
        @weakify(self);
        [MrkAlertManager showAlert:@"提示"
                           message:@"设备信息初始化失败"
                            cancel:@"关闭"
                            ensure:@"重试"
                       handleIndex:^(NSInteger index) {
            @strongify(self);
            if (index == 1) {
                // 重新请求
                [self updateEquimentInfo];
            }else {
                // 退出训练
                [self timeEndCreatReport];
            }
        }];
        return;
    }
    
    self.ultraView.equimentModel = equiment;  //更新设备UI数据
    self.blueManager.eqModel = equiment;      //更新蓝牙设备数据
    self.reportManager.deviceId = equiment.equipmentInfoId; //更新设备ID
    self.reportManager.equimentId = self.courseModel.equipmentId;
    // 超燃脂设备 默认开启自动调节
    if (equiment.isElectromagneticControl) {
        self.isAdjust = YES;
    }else {
        self.isAdjust = NO;
    }
}

- (void)allUpdateEquimentDisplay:(NSArray <MRKEqDisplayModel *> *)display {
    [self.ultraView updateEquimentDisplay:display];
}

#pragma mark - 超燃脂弹窗
- (MRKUltraTrainViewModel *)viewModel {
    if (!_viewModel) {
        _viewModel = [[MRKUltraTrainViewModel alloc] init];
        _viewModel.courseID = self.courseModel.cid;
    }
    return _viewModel;
}
- (void)statTrain {
    // 请求设备数据
    [self trainPlayNetWork];
}


- (BOOL)supportResistance {
    BOOL device = NO;
    NSInteger type = self.courseModel.equipmentId.integerValue;
    switch (type) {
        case BicycleEquipment:
        case BoatEquipment:
        case EllipticalEquipment: {
            device = YES;
        }  break;
            
        default:
            break;
    }
    return device;
}

- (void)trainPlayNetWork{
    // 请求设备显示信息
    @weakify(self);
    [self.viewModel.dataSignal subscribeNext:^(id x) {
        @strongify(self);
//        [MBProgressHUD hideHUD];
        // 教案阻力映射
        [self changeCataloguePOS:self.viewModel.nodesArray];
        // 填充设备数据
        [self allUpdateEquimentInfo:self.viewModel.equimentModel];
        
        ///更具设备信息查阻力不支持回显, 隐藏阻力显示
        NSMutableArray *array = self.viewModel.displayArray.mutableCopy;
        if (!self.viewModel.equimentModel.isSupportResistanceEcho && [self supportResistance]){
            NSMutableArray *arr = [NSMutableArray array];
            for (MRKEqDisplayModel *displayModel in array) {
                if (displayModel.displayId.intValue != 5){
                    [arr addObject:displayModel];
                }
            }
            array = arr;
        }
        
        self.viewModel.displayArray = array;
        
        // 填充设备显示数据
        [self allUpdateEquimentDisplay:self.viewModel.displayArray];
        // 开始播放
        [self trainPlayBlock];
    }];
//    [MBProgressHUD showLodingWithMessage:@"" view:nil];
    [self.viewModel requestEquimentData:self.courseModel.equipmentId];
}

/// 判断是否弹出温馨提示弹唱
- (void)trainPlayBlock {
    // 是超燃脂设备
    if (self.ultraView.equimentModel.isElectromagneticControl) {
        // 先判断是否有引导层
        NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
        BOOL teachPlan = [userDefaults boolForKey:ultra_guide];
        if (!teachPlan) {
            // 展示过引导页
            [userDefaults setBool:YES forKey:ultra_guide];
            [userDefaults synchronize];
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                //这种情况是在内部连接设备, 延时为了解决屏幕旋转获取准确的frame
                [self ultraGuide];
            });
        } else {
            // 开启超燃脂提示
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [self meritAlert];
//                [self trainPlay];
            });
        }
    }else {  //不是超燃脂设备，直接播放
        // 延迟1秒播放 ， 等待UI完成初始化
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self trainPlay];
        });
    }
}

// 引导页
- (void)ultraGuide {
    // 计算自动调节阻力按钮位置
    NSInteger itemc = 6;
    CGFloat width = (MaxMainWidth - kStatusBarHeight - 24) / itemc;
    CGFloat left = 12 + ((width / 4) - 52);
    
    AwesomeIntroGuideView *marksView = [[AwesomeIntroGuideView alloc] initWithFrame:CGRectMake(0, 0, MainWidth, MainHeight)];
    marksView.loadType = AwesomeIntroLoad_Sync;
    marksView.guideShape = AwesomeIntroGuideShape_Square;
    marksView.maskColor = RGBA(0, 0, 0, 0.7);
    marksView.autoCalculateGuidePoint = NO;
    marksView.animationDuration = 0.2;
    marksView.enableSkipButton = YES;
    [self.view addSubview:marksView];
    
    NSString *imgStr = @"ultra_guide_china";
    CGFloat guideY = MainHeight - (170) - (IS_IPHONEX_SURE ? 0: -10);
    [marksView loadMarks:@[self.view]];
    [marksView loadGuideImageItem:@[@{@"image":[UIImage imageNamed:imgStr],
                                      @"point":[NSValue valueWithCGPoint:CGPointMake(MainWidth - (292) - left, guideY)]}]];
    
    marksView.btnSkipCoach.frame = CGRectMake(MainWidth - 180 - left,  guideY - 50, 92, 32);
    marksView.btnSkipCoach.layer.borderWidth = CGFloatFromPixel(1.0f);
    marksView.btnSkipCoach.layer.borderColor = [UIColor whiteColor].CGColor;
    marksView.btnSkipCoach.cornerRadius = 16;
    [marksView.btnSkipCoach setTitle:@"继续" forState:UIControlStateNormal];
    [marksView.btnSkipCoach addTarget:self action:@selector(ultraGuideGoOn) forControlEvents:UIControlEventTouchUpInside];
    [marksView start];
}

// 引导页 继续
- (void)ultraGuideGoOn {
    [self meritAlert];
}

// 超燃脂弹窗
- (void)meritAlert {

    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    BOOL videoGuideLayer = [userDefaults boolForKey:@"MRKWarnControlView"];
    if (videoGuideLayer) {
        [self trainPlay];
        return;
    }

    MRKWarnControlView *warnView = [[MRKWarnControlView alloc] init];
    warnView.equipmentId = self.courseModel.equipmentId;
    warnView.equipmentName = self.courseModel.equipmentName;
    @weakify(self);
    warnView.selectBlock = ^(MRKWarnControlView * _Nonnull view, NSInteger index) {
        @strongify(self);
        if (index == 0){
//            [self.navigationController popViewControllerAnimated:YES];
            [self dismissViewControllerAnimated:NO completion:nil];
            return;
        }
        
        [view removeFromSuperview];
        [self trainPlay];
    };
    [warnView restartControlLayer];
    [self.view addSubview:warnView];
    [warnView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsMake(0, 0, 0, 0));
    }];
    
    warnView.alpha = 0.0;
    [UIView animateWithDuration:0.3 animations:^{
        warnView.alpha = 1.0;
    }];
}

#pragma mark -  开始播放
- (void)trainPlay {
    nodeIndex = 0;
    oldIndex = -1;
    // 开始运动
    [self.ultraView startTraining];   //UI操作，状态改为播放状态， 播放音乐
    // 倒计时开始
    [self.timeManager startTimer];    //开始播放（倒计时）
    // socket 开始运动
    [self.reportManager ultraSportBegin]; //运动开始
    
    // 开始获取数据
    [self.blueManager startGetDeviceData];      //开始获取设备数据
    [self.blueManager checkTreamillNeedStart];  //如果是跑步机，检测是否开启跑步机
    
    isPlayStart = YES;  //初始化完毕，开始了播放
}

#pragma mark -  播放控制
// 暂停播放
- (void)trainPlayPause {
    // 暂停
    [self.ultraView pauseTraining]; //UI暂停
    [self.timeManager stopTimer];  //时间暂停
    // 如果音乐开着，就暂停音乐
//    if (![MRKAliAudioPlayer sharePlayer].isPause) {
//        [[MRKAliAudioPlayer sharePlayer] pause];
//    }
    [[MRKAliAudioPlayer sharePlayer] pausePlay];
}

// 继续播放
- (void)trainPlayResum {
    // 播放
    [self.ultraView resumTraining];   // UI开始
    [self.timeManager startTimer];    // 时间开始
    // 如果音乐关着，就播放音乐
//    if ([MRKAliAudioPlayer sharePlayer].isPause) {
//        [[MRKAliAudioPlayer sharePlayer] pause];
//    }
    [[MRKAliAudioPlayer sharePlayer] startPlay];
}

- (void)endPlay {
    // 暂停音乐
    [[MRKAliAudioPlayer sharePlayer] destroyAudioPlayer];
    [[MRKAliAudioPlayer sharePlayer].musicListArray removeAllObjects];
    
    // 停止定时器
    [self.timeManager closeTimer];
    
    // 停止数据接受
    [self.blueManager deviceEndConnect];
}

// 开始播放，并检查是否是跑步机开始播放
- (void)trainplayCheckTreamill {
    if (self.courseModel.equipmentId.intValue == TreadmillEquipment) {
        if (![self.blueManager checkTreamillNeedStart]) {  // 如果跑步机可以开始，等开始的时候，在播放
            [self trainPlayResum];  //直接播放
        }
    }else {
        [self trainPlayResum];  //直接播放
    }
}

#pragma mark - 去生成训练报告

/// 用户主动结束 生成训练报告
- (void)userEndCreatReport {
    // 先暂停播放
    [self trainPlayPause];
    // 生成训练报告
    [self.reportManager createReportDataExit:YES time:self.timeManager.currentTime];
}

/// 课程结束，生成训练报告
- (void)timeEndCreatReport {
    // 结束播放
    [self endPlay];
    // 生成训练报告
    [self.reportManager createReportDataExit:NO time:self.timeManager.currentTime];
}

#pragma mark - ui交互操作  MRKUltraTrainViewDelegate
- (MRKUltraTrainView *)ultraView {
    if (!_ultraView) {
        _ultraView = [[MRKUltraTrainView alloc] init];
        _ultraView.backgroundColor = [UIColor colorWithHexString:@"#1C1A1A"];
    }
    return _ultraView;
}

// 返回
- (void)ultraTrainViewBack {
    [self userEndCreatReport];  //用户返回，生成训练报告
}

// 设备点击
- (void)ultraTrainViewEquimentClick:(UIButton *)sender {
    if (sender.selected) {
        //已经连接设备
        [MBProgressHUD showMessage:@"已经连接设备"];
    }else {
//        [self gotoEquiment];
        [self.blueManager connectDeviceModel];
    }
}

// 投屏
- (void)ultraTrainViewTV {
    WebViewViewController *vc = [WebViewViewController new];
    vc.htmlURL = MRKAppH5LinkCombine(MRKLinkVideoTV);
    vc.titleString = @"投屏说明";
    vc.isHiddenNav = YES;
    vc.hidesBottomBarWhenPushed = YES;
    [self.navigationController pushViewController:vc animated:YES];
}
// 分享
- (void)ultraTrainViewShare {
    NSLog(@"分享");
    [MBProgressHUD showLodingWithMessage:@"" view:self.view];
    [MRKUltraCourseViewModel ultraShareCourse:self.courseModel.cid success:^(NSString * _Nonnull url) {
        [MBProgressHUD hideHUDForView:self.view];
        if (url.isNotBlank) {
//            MRKShareView *view = [MRKShareView shareViewWithType:ShareActionViewPlayer andViewController:self];
//            view.shareUrl = url;
//            view.shareTitle = self.courseModel.name;
//            [view showInView:self.view];
            [FlutterManager shareCommonVCWithTitle:self.courseModel.name text:@"" link:url thumbImage:@""];
        }
    } failer:^{
        [MBProgressHUD hideHUDForView:self.view];
    }];
}
// 课程播放暂停
- (void)ultraTrainViewPauseClick:(UIButton *)sender {
    if (sender.isSelected) {
        NSLog(@"课程播放");
        [self trainplayCheckTreamill];
        if (self.courseModel.equipmentId.intValue == TreadmillEquipment &&
            [BluetoothManager isConnectEquipmentType:self.courseModel.equipmentId] &&
            self.blueManager.treamillStatus != DeviceRuningStatus) {
            
            [self.view endLoading];
            [self.view beginLoading];  //跑步机暂停的时候，课程loading
        }
    } else {
        NSLog(@"课程暂停");
        [self trainPlayPause];
        [self.blueManager treamillPause];   //跑步机暂停
        if (self.courseModel.equipmentId.intValue == TreadmillEquipment &&
            [BluetoothManager isConnectEquipmentType:self.courseModel.equipmentId] &&
            [self.blueManager treamillIsStart]) {
            
            [self.view endLoading];
            [self.view beginLoading];  //跑步机暂停的时候，课程loading
        }
    }
}

// 自动调节按钮点击
- (void)ultraTrainViewAdjustClick:(UIButton *)sender {
    NSLog(@"超燃脂自动调节按钮点击");
    if (sender.isSelected) {
        // 自动调节打开
        self.isAdjust = YES;
    }else {
        // 自动调节关闭
        self.isAdjust = NO;
    }
}

#pragma mark - MRKUltraBlueManagerDelegate 蓝牙相关操作
- (MRKUltraBlueManager *)blueManager {
    if (!_blueManager) {
        _blueManager = [[MRKUltraBlueManager alloc] init];
    }
    return _blueManager;
}

// 设备连接成功，更新设备信息
- (void)connectDeviceSuccess {
    [self updateEquimentInfo];
}
// 设备状态回调
- (void)updateDeviceStatus:(DEVICE_CONNECT_STATUS)status {
    // 根据设备连接状态，更新UI
    [self.ultraView updataUIWithDeviceState];
}

// 断开链接提示的弹窗回调
- (void)disconnectAlert {
    [self.view endLoading];  //跑步机暂停的时候，loading 结束
    
    /**设备断连弹窗*/
    MRKVideoBTConnectView *view = [MRKVideoBTConnectView build];
    @weakify(self);
    view.selectBlock = ^(__kindof AlertBaseView *alertView, NSInteger index) {
        @strongify(self);
        [alertView hide];
        if (index == 1) {
//            [self gotoEquiment];
            [self.blueManager connectDeviceModel];
        }
    };
    [view showIn:self.view];
}

// 跑步机暂停 (跑步机暂停，视频暂停)
- (void)treamillPause {
    jxt_getSafeMainQueue(^{
        [self.view endLoading];  //跑步机暂停的时候，loading 结束
        [self trainPlayPause];  //视频暂停
    });
}
// 跑步机播放 （跑步机播放，视频播放）
- (void)treamillResume {
    jxt_getSafeMainQueue(^{
        if (self->isPlayStart && self->isCurrentPage) {
            [self trainPlayResum];   //视屏开始播放
        }
        [self.view endLoading];  //跑步机暂停的时候，loading 结束
    });
}
// 跑步机结束
- (void)deviceExit {
    // 生成训练报告
    jxt_getSafeMainQueue(^{
        [MRKTimerManager cancelTimer]; //取消指令发送
        [self trainPlayPause];
        [self.reportManager createReportDataNoAlertTime:self.timeManager.currentTime];
    });
}

// 更新设备 数据到 UI上
- (void)updateDeviceUI:(BaseEquipDataModel *)model {
    _tyModel = model;
    
    [_ultraView updateEquimentData:model];  //更新UI
    [_reportManager ultraSocketSend:model]; //上报数据
    
    if (self.courseModel.equipmentId.intValue != TreadmillEquipment && !self.isPlaybackFinished) {
        [[MRKDynamicIslandManager shared] update:self];
    }
}


#pragma mark ------- 训练报告回调。MRKUltraDataReportDelegate ----------
// 没有生产训练报告，退出
- (void)reportBack {
    [self.reportManager ultraSocketClose]; // 停止socket
    [self endPlay];  //定时器，播放器 清理资源
    
//    [self.navigationController popViewControllerAnimated:YES];
    [self dismissViewControllerAnimated:NO completion:nil];
}

// 继续运动
- (void)reportGoOn {
    [self trainPlayResum];
}

// 生成训练报告成功
- (void)reportSuccess {
    [self.reportManager ultraSocketClose]; // 停止socket
    [self endPlay];  //定时器，播放器 清理资源
    
    jxt_getSafeMainQueue(^{
        ExerciseReportWebController *vc = [[ExerciseReportWebController alloc] init];
        vc.courseType = @"1";
        vc.exerciseID = self.reportManager.reportId?:@"";
        vc.equipmentId = self.courseModel.equipmentId?:@"";
        vc.fromTrainingView = YES;
        vc.quitMidway = YES;
        vc.playTime = @(self.timeManager.currentTime);
        @weakify(self);
        [self dismissViewControllerAnimated:NO completion:^{
            @strongify(self);
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                @strongify(self);
                UIViewController *root = self.pushController;
                [root.navigationController pushViewController:vc animated:YES completion:^{
                    [[NSNotificationCenter defaultCenter] postNotificationName:@"UpdateReportComplete" object:nil];
                }];
            });
        }];
    });
    
    /// 结束后延迟3s发送更新小彩屏首页的通知
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [[NSNotificationCenter defaultCenter] postNotificationName:@"kUpdateHomeSportDataNotification"
                                                            object:@{@"productId": self.courseModel.equipmentId}];
    });
}

- (void)socketReconnect {
    if (isCurrentPage && isPlayStart && self.socketAlert) {
        [self trainplayCheckTreamill];
    }
}

// socket重链太多次
- (void)socketReconnectNumber:(NSInteger)count {
    self.socketAlert = [[MRKAbilityQuitAlert alloc] initWithAnimationStyle:MRKActionAlertViewTransitionStyleFade];
    self.socketAlert.defaultText = @"当前网络不稳定,请检查网络后重试";
    [self.socketAlert.cancelBtn setTitle:@"刷新重试" forState:UIControlStateNormal];
    [self.socketAlert.ensureBtn setTitle:@"退出" forState:UIControlStateNormal];
    @weakify(self);
    self.socketAlert.quitBlock = ^(NSInteger index) {
        @strongify(self);
        if (index == 0) {
            [self timeEndCreatReport];
        }else {
            [self.reportManager ultraSocketReconnect];
        }
    };
    [self.socketAlert showInView:self.view];
    
    // 暂停课程
    if (isCurrentPage && isPlayStart) {
        [self trainPlayPause];
        [self.blueManager treamillPause];
    }
}

#pragma mark - socket数据上报
- (MRKUltraDataReportManager *)reportManager {
    if (!_reportManager) {
        _reportManager = [[MRKUltraDataReportManager alloc] init];
    }
    return _reportManager;
}

#pragma mark - 课程控制操作 定时器播放时间 MRKUltraTimeManagerDelegate
- (MRKUltraTrainTimeManager *)timeManager {
    if (!_timeManager) {
        _timeManager = [[MRKUltraTrainTimeManager alloc] initWithTotalTime:self.courseModel.courseTotalTime];
    }
    return _timeManager;
}


@synthesize tyModel = _tyModel;
- (BaseEquipDataModel *)tyModel {
    BaseEquipDataModel *model;
    pthread_rwlock_rdlock(&_rwlock);
    model = _tyModel;
    pthread_rwlock_unlock(&_rwlock);
    return model;
}

- (void)setTyModel:(BaseEquipDataModel *)tyModel{
    pthread_rwlock_wrlock(&_rwlock);
    _tyModel = tyModel;
    pthread_rwlock_unlock(&_rwlock);
}


// 当前进度
- (void)ultraCurrentTimePorgress:(NSTimeInterval)time {
    NSLog(@"课程进度：%f",time);
    self.blueManager.playTime = time;          //蓝牙同步 视频的播放进度,生成训练报告用
    self.playTime = time;
    if (self.courseModel.equipmentId.intValue != TreadmillEquipment && !self.isPlaybackFinished) {
        [[MRKDynamicIslandManager shared] update:self];
    }
    [self.reportManager ultraSoortCheckBegin:time];

    //当前环节模型
    MRKCourseNodeModel *node = self.courseModel.nodesArray[nodeIndex];
    if (time >= node.endTime.floatValue) {
        // 跳转到下一小节
        nodeIndex ++;
    }
    
    if (nodeIndex >= self.courseModel.nodesArray.count) {
        return;
    }
    
    //更换小节时，执行一次, 做更换小节操作
    if (oldIndex != nodeIndex) {
        MRKCourseNodeModel *node1 = self.courseModel.nodesArray[nodeIndex];
        self.blueManager.nodeModel = node1;// 同步蓝牙，当前小节数据
        [self.ultraView updateAdviceUI:node1];  //填充建议阻力值数据
        oldIndex = nodeIndex;
        NSLog(@"更换了小节，当前小节是：%d",nodeIndex);
    }
    
    //UI更新进度，热力值等
    BaseEquipDataModel *data = self.tyModel.copy;
    NSNumber *value = data.ultraProgressValue;
    [self.ultraView updateProgressTime:time nodeIndex:nodeIndex value:value];
    
    //发送指令
    [self sendCommand:time];
}

//课程结束
- (void)ultraTimeEnd:(NSTimeInterval)time {
    NSLog(@"课程结束：%f",time);
    if (self.courseModel.equipmentId.intValue != TreadmillEquipment && !self.isPlaybackFinished) {
        self.isPlaybackFinished = YES;
        [[MRKDynamicIslandManager shared] update:self];
    }
    [self timeEndCreatReport];
    
}


#pragma mark - 发送指令
- (void)sendCommand:(NSTimeInterval)time {
    // 设备未连接，或者没有打开自动调节开关，不发送指令
    if (![BluetoothManager isConnectEquipmentType:self.courseModel.equipmentId]) {
        return;
    }
    NSString *timeStr = [NSString stringWithFormat:@"%.0f", ceil(time)];
    // 每一小节，提前五秒，取出小节下标，开始发送指令
    if ([self.courseModel.beginTimeArr containsObject:timeStr]) {
        NSInteger index = [self.courseModel.beginTimeArr indexOfObject:timeStr];
        MRKCourseNodeModel *model = self.courseModel.nodesArray[index];
        
        // 第一节，不执行动画，立即发送指令
        int timer = 5;  //指令发送倒计时
        if (index == 0) {
            if (self.courseModel.equipmentId.intValue == TreadmillEquipment) {
                timer = 5; //指令立即发送
            }else {
                timer = 2;///防止第一小节指令和清零指令同时发送设备无法响应，延迟2s发送 【23-11-28 zqp 听wk的话】
            }
        } else {
            // 展示提示动画，还是自动调节动画
            [self.ultraView updateAdjustAnimation:model merit:(self.blueManager.eqModel.isElectromagneticControl && self.isAdjust)];
        }
        
        // 超燃脂设备 发送指令
        NSString *soundStr = @"";
        NSString *controlType = @"";
        NSString *controlNumber = @"";
        if (self.courseModel.equipmentId.intValue == TreadmillEquipment) {
            if ([self.blueManager isNeedInstructionWithModel:model]) {  //判断跑步机当前能否发送指令
                soundStr = [NSString stringWithFormat:@"速度将调至%@，坡度将调至%@", model.maxNum, model.adviseNum];
                controlType = @"3";
                controlNumber = model.maxNum.stringValue;
            }
        }else {
            // 其他设备
            if(self.blueManager.eqModel.showSlope.boolValue) {
                if (model.isChangeSlope) { // 需要调节坡度
                    if (model.adviseNum) {
                        soundStr = [NSString stringWithFormat:@"阻力将调至%@，坡度将调至%@", model.adviseNum, model.slopeNum];
                        controlType = @"1";
                        controlNumber = model.adviseNum.stringValue;
                    } else {
                        soundStr = [NSString stringWithFormat:@"坡度将调至%@", model.slopeNum];
                        controlType = @"2";
                        controlNumber = model.slopeNum.stringValue;
                    }
                }else{
                    soundStr = [NSString stringWithFormat:@"阻力将调至%@",model.adviseNum];
                    controlType = @"1";
                    controlNumber = model.adviseNum.stringValue;
                }
            }else{
                soundStr = [NSString stringWithFormat:@"阻力将调至%@",model.adviseNum];
                controlType = @"1";
                controlNumber = model.adviseNum.stringValue;
            }
        }
        
        
        if (self.blueManager.eqModel.isElectromagneticControl && self.isAdjust) {
            @weakify(self);
            void(^sendNotificationBlock)(void) = ^{
                @strongify(self);
                self.isSendControl = NO;
                ///关闭教案
                if (!self.isAdjust){
                    return;
                }
                ///发送指令
                [self.blueManager sendconCommand:model];
            };
            if ([[UIApplication sharedApplication] applicationState] == UIApplicationStateBackground && self.courseModel.equipmentId.intValue != TreadmillEquipment && soundStr.length > 0) { // 后台且不是跑步机
                /// 播放语音
                [[SpeechSynthesizerManager shared] speak:soundStr];
                
                self.controlType = controlType;
                self.controlNumber = controlNumber;
                self.isSendControl = YES;
                if (!self.isPlaybackFinished) {
                    [[MRKDynamicIslandManager shared] update:self];
                }
            }
            [MRKTimerManager initGCDWithSeconds:timer ongoingBlock:^(NSInteger seconds){} endBlock:^{
                sendNotificationBlock();
            }];
        }
    }
}

#pragma mark - 手机前后台操作
// app 活跃
- (void)becomeActive {
    if (self.courseModel.equipmentId.intValue != TreadmillEquipment) {
        if (isCurrentPage && isPlayStart && !self.socketAlert.isVisible) {
            NSLog(@"app活跃");
            [self trainplayCheckTreamill];
        }
    }
}

- (void)resignActive {
    if (isCurrentPage && isPlayStart) {
        NSLog(@"app 失去活跃");
        if (self.courseModel.equipmentId.intValue != TreadmillEquipment) {
            [[MRKDynamicIslandManager shared] start:self];
        } else{ // 跑步机逻辑不变，不暂停跑步机
            [self trainPlayPause];
//            [self.blueManager treamillPause];
            
            jxt_getSafeMainQueue(^{
                [FeedbackGeneratorUtil playSoundWithTrainingReminder:@"Sound_001"];
            });
        }
    }
}

@end
