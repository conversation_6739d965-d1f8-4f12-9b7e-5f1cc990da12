//
//  HealthManager.swift
//  Student_IOS
//
//  Created by merit on 2025/7/16.
//  Swift重构版本的健康数据管理器
//

import Foundation
import HealthKit
import Combine

// MARK: - 健康数据类型枚举

/// 健康数据授权状态
enum HealthAuthorizationStatus {
    case notDetermined     // 未确定
    case denied            // 拒绝
    case authorized        // 已授权
    
    init(from hkStatus: HKAuthorizationStatus) {
        switch hkStatus {
        case .notDetermined:
            self = .notDetermined
        case .sharingDenied:
            self = .denied
        case .sharingAuthorized:
            self = .authorized
        @unknown default:
            self = .notDetermined
        }
    }
}

/// 运动设备类型
enum WorkoutEquipmentType: Int, CaseIterable {
    case bicycle = 1        // 单车
    case treadmill = 2      // 跑步机
    case rowing = 5         // 划船机
    case elliptical = 6     // 椭圆机
    
    /// 对应的HealthKit运动类型
    var healthKitActivityType: HKWorkoutActivityType {
        switch self {
        case .bicycle:
            return .cycling
        case .treadmill:
            return .running
        case .elliptical:
            return .elliptical
        case .rowing:
            return .rowing
        }
    }
    
    /// 设备名称
    var displayName: String {
        switch self {
        case .bicycle:
            return "单车"
        case .treadmill:
            return "跑步机"
        case .elliptical:
            return "椭圆机"
        case .rowing:
            return "划船机"
        }
    }
}

// MARK: - 健康数据模型

/// 健康数据写入模型
struct HealthDataWriteModel {
    let trainId: String         // 训练ID
    let equipmentType: WorkoutEquipmentType  // 设备类型
    let duration: TimeInterval  // 训练时长（秒）
    let distance: Double        // 距离（米）
    let calories: Double        // 消耗卡路里
    let startDate: Date         // 开始时间
    let endDate: Date           // 结束时间
    
    /// 从字典创建模型
    init?(from dictionary: [String: Any]) {
        guard let trainId = dictionary["trainId"] as? String ?? dictionary["trainingRecordId"] as? String,
              let equipmentId = dictionary["equipmentId"] as? Int ?? dictionary["productId"] as? Int,
              let durationValue = dictionary["duration"] as? TimeInterval ?? dictionary["takeTime"] as? TimeInterval,
              let distance = dictionary["distance"] as? Double,
              let calories = dictionary["kcal"] as? Double else {
            return nil
        }

        // 检查设备类型是否为支持的四大件设备
        guard let equipmentType = WorkoutEquipmentType(rawValue: equipmentId) else {
            print("不支持的设备类型ID: \(equipmentId)，无法创建健康数据模型")
            return nil
        }

        self.trainId = trainId
        self.equipmentType = equipmentType
        self.duration = durationValue
        self.distance = distance
        self.calories = calories

        // 计算开始和结束时间
        let endDate = Date()
        self.endDate = endDate
        self.startDate = endDate.addingTimeInterval(-durationValue)
    }
}

/// 健康数据读取模型
struct HealthDataReadModel {
    let stepCount: String           // 步数
    let walkingRunningDistance: String  // 步行+跑步距离（米）
    let activeEnergyBurned: String  // 活动能量（千卡）
    let cyclingDistance: String     // 骑车距离（米）
    let height: String              // 身高（厘米）
    let weight: String              // 体重（千克）
    let birthday: String            // 生日
    let workoutDuration: String     // 体能训练时长（秒）
    let heartRate: String           // 心率
    let restingHeartRate: String    // 静息心率
    let bodyFatPercentage: String   // 体脂率
    let bodyMassIndex: String       // BMI
    let date: String                // 数据日期
    
    /// 转换为字典格式
    func toDictionary() -> [String: Any] {
        return [
            "footNum": stepCount,
            "runDistance": walkingRunningDistance,
            "kcal": activeEnergyBurned,
            "ridingDistance": cyclingDistance,
            "height": height,
            "weight": weight,
            "birthday": birthday,
            "takeTime": workoutDuration,
            "heartRate": heartRate,
            "restRate": restingHeartRate,
            "bodyFatRate": bodyFatPercentage,
            "bmi": bodyMassIndex,
            "date": date
        ]
    }
}

// MARK: - 健康数据管理器协议

/// 健康数据管理器协议
protocol HealthManagerProtocol {
    /// 检查HealthKit是否可用
    static var isHealthDataAvailable: Bool { get }
    
    /// 获取授权状态
    func authorizationStatus(for type: HKObjectType) -> HealthAuthorizationStatus
    
    /// 请求健康数据授权
    func requestAuthorization() async throws -> Bool
    
    /// 请求心率授权
    func requestHeartRateAuthorization() async throws -> Bool
    
    /// 读取健康数据
    func readHealthData(daysAgo: Int, duration: Int) async throws -> HealthDataReadModel
    
    /// 写入运动数据
    func writeWorkoutData(_ data: HealthDataWriteModel) async throws
    
    /// 写入身高体重
    func writeHeightAndWeight(height: Double?, weight: Double?) async throws
}

// MARK: - 健康数据管理器主类

/// 健康数据管理器
@MainActor
final class HealthManager: ObservableObject, HealthManagerProtocol {
    
    // MARK: - 单例
    
    static let shared = HealthManager()
    
    // MARK: - 私有属性
    
    private let healthStore = HKHealthStore()
    private let userDefaults = UserDefaults.standard
    private let lastUploadDateKey = "AppLaunch_LastDate"
    
    // MARK: - 发布属性
    
    @Published var isAuthorized = false
    @Published var isHeartRateAuthorized = false
    
    // MARK: - 初始化
    
    private init() {
        updateAuthorizationStatus()
    }
    
    // MARK: - 静态属性
    
    /// 检查HealthKit是否可用
    static var isHealthDataAvailable: Bool {
        return HKHealthStore.isHealthDataAvailable()
    }
    
    // MARK: - 私有计算属性
    
    /// 需要写入权限的数据类型
    private var writeDataTypes: Set<HKSampleType> {
        return Set([
            HKQuantityType(.stepCount),
            HKQuantityType(.distanceWalkingRunning),
            HKQuantityType(.activeEnergyBurned),
            HKQuantityType(.distanceCycling),
            HKQuantityType(.height),
            HKQuantityType(.bodyMass),
            HKQuantityType(.bodyMassIndex),
            HKQuantityType(.bodyFatPercentage),
            HKQuantityType(.heartRate),
            HKQuantityType(.restingHeartRate),
            HKWorkoutType.workoutType()
        ])
    }
    
    /// 需要读取权限的数据类型
    private var readDataTypes: Set<HKObjectType> {
        var types = Set<HKObjectType>(writeDataTypes)
        types.insert(HKCharacteristicType(.dateOfBirth))
        return types
    }
    
    // MARK: - 授权相关方法
    
    /// 获取指定类型的授权状态
    func authorizationStatus(for type: HKObjectType) -> HealthAuthorizationStatus {
        let status = healthStore.authorizationStatus(for: type)
        return HealthAuthorizationStatus(from: status)
    }
    
    /// 检查是否已完全授权
    var isFullyAuthorized: Bool {
        guard Self.isHealthDataAvailable else { return false }
        
        return writeDataTypes.allSatisfy { type in
            authorizationStatus(for: type) == .authorized
        }
    }
    
    /// 检查是否从未授权过
    var hasNeverBeenAuthorized: Bool {
        guard Self.isHealthDataAvailable else { return true }
        
        let workoutType = HKWorkoutType.workoutType()
        return authorizationStatus(for: workoutType) == .notDetermined
    }
    
    /// 检查所有类型是否都从未授权过
    var allTypesNeverAuthorized: Bool {
        guard Self.isHealthDataAvailable else { return false }
        
        return readDataTypes.allSatisfy { type in
            authorizationStatus(for: type) == .notDetermined
        }
    }
    
    /// 检查心率是否已授权
    var isHeartRateAuthorized: Bool {
        guard Self.isHealthDataAvailable else { return false }
        
        let heartRateType = HKQuantityType(.heartRate)
        return authorizationStatus(for: heartRateType) == .authorized
    }
    
    /// 检查心率是否从未授权过
    var heartRateNeverAuthorized: Bool {
        guard Self.isHealthDataAvailable else { return true }

        let heartRateType = HKQuantityType(.heartRate)
        return authorizationStatus(for: heartRateType) == .notDetermined
    }

    // MARK: - 授权请求方法

    /// 请求健康数据授权
    func requestAuthorization() async throws -> Bool {
        guard Self.isHealthDataAvailable else {
            throw HealthManagerError.healthKitNotAvailable
        }

        return try await withCheckedThrowingContinuation { continuation in
            healthStore.requestAuthorization(toShare: writeDataTypes, read: readDataTypes) { [weak self] success, error in
                if let error = error {
                    continuation.resume(throwing: error)
                } else {
                    let isAuthorized = self?.isFullyAuthorized ?? false
                    Task { @MainActor in
                        self?.updateAuthorizationStatus()
                    }
                    continuation.resume(returning: isAuthorized)
                }
            }
        }
    }

    /// 请求心率授权
    func requestHeartRateAuthorization() async throws -> Bool {
        guard Self.isHealthDataAvailable else {
            throw HealthManagerError.healthKitNotAvailable
        }

        return try await withCheckedThrowingContinuation { continuation in
            healthStore.requestAuthorization(toShare: writeDataTypes, read: readDataTypes) { [weak self] success, error in
                if let error = error {
                    continuation.resume(throwing: error)
                } else {
                    let isHeartRateAuthorized = self?.isHeartRateAuthorized ?? false
                    Task { @MainActor in
                        self?.updateAuthorizationStatus()
                    }
                    continuation.resume(returning: isHeartRateAuthorized)
                }
            }
        }
    }

    /// 更新授权状态
    private func updateAuthorizationStatus() {
        isAuthorized = isFullyAuthorized
        isHeartRateAuthorized = isHeartRateAuthorized
    }

    // MARK: - 数据读取方法

    /// 读取健康数据
    func readHealthData(daysAgo: Int, duration: Int) async throws -> HealthDataReadModel {
        guard Self.isHealthDataAvailable else {
            throw HealthManagerError.healthKitNotAvailable
        }

        // 并发读取所有健康数据
        async let stepCount = readStepCount(daysAgo: daysAgo, duration: duration)
        async let walkingDistance = readWalkingRunningDistance(daysAgo: daysAgo, duration: duration)
        async let energyBurned = readActiveEnergyBurned(daysAgo: daysAgo, duration: duration)
        async let cyclingDistance = readCyclingDistance(daysAgo: daysAgo, duration: duration)
        async let height = readHeight(daysAgo: daysAgo, duration: duration)
        async let weight = readWeight(daysAgo: daysAgo, duration: duration)
        async let heartRate = readHeartRate(daysAgo: daysAgo, duration: duration)
        async let restingHeartRate = readRestingHeartRate(daysAgo: daysAgo, duration: duration)
        async let bodyFat = readBodyFatPercentage(daysAgo: daysAgo, duration: duration)
        async let bmi = readBodyMassIndex(daysAgo: daysAgo, duration: duration)
        async let workoutDuration = readWorkoutDuration(daysAgo: daysAgo, duration: duration)

        // 读取生日
        let birthday = try await readDateOfBirth()

        // 等待所有数据读取完成
        let results = try await (
            stepCount: stepCount,
            walkingDistance: walkingDistance,
            energyBurned: energyBurned,
            cyclingDistance: cyclingDistance,
            height: height,
            weight: weight,
            heartRate: heartRate,
            restingHeartRate: restingHeartRate,
            bodyFat: bodyFat,
            bmi: bmi,
            workoutDuration: workoutDuration
        )

        return HealthDataReadModel(
            stepCount: results.stepCount,
            walkingRunningDistance: results.walkingDistance,
            activeEnergyBurned: results.energyBurned,
            cyclingDistance: results.cyclingDistance,
            height: results.height,
            weight: results.weight,
            birthday: birthday,
            workoutDuration: results.workoutDuration,
            heartRate: results.heartRate,
            restingHeartRate: results.restingHeartRate,
            bodyFatPercentage: results.bodyFat,
            bodyMassIndex: results.bmi,
            date: ""
        )
    }

    // MARK: - 私有数据读取方法

    /// 创建日期谓词
    private func createDatePredicate(daysAgo: Int, duration: Int) -> NSPredicate {
        let calendar = Calendar.current
        let now = Date().addingTimeInterval(TimeInterval(daysAgo * 24 * 60 * 60))
        let components = calendar.dateComponents([.year, .month, .day], from: now)

        guard let startDate = calendar.date(from: components),
              let endDate = calendar.date(byAdding: .day, value: duration, to: startDate) else {
            fatalError("无法创建日期")
        }

        return HKQuery.predicateForSamples(withStart: startDate, end: endDate, options: .strictStartDate)
    }

    /// 读取统计数据（累计值）
    private func readStatisticsData(
        quantityType: HKQuantityType,
        unit: HKUnit,
        daysAgo: Int,
        duration: Int
    ) async throws -> String {
        return try await withCheckedThrowingContinuation { continuation in
            let predicate = createDatePredicate(daysAgo: daysAgo, duration: duration)
            let query = HKStatisticsQuery(
                quantityType: quantityType,
                quantitySamplePredicate: predicate,
                options: .cumulativeSum
            ) { _, result, error in
                if let error = error {
                    continuation.resume(throwing: error)
                } else if let result = result, let sum = result.sumQuantity() {
                    let value = sum.doubleValue(for: unit)
                    continuation.resume(returning: String(format: "%.0f", value))
                } else {
                    continuation.resume(returning: "0")
                }
            }
            healthStore.execute(query)
        }
    }

    /// 读取样本数据（最新值）
    private func readSampleData(
        quantityType: HKQuantityType,
        unit: HKUnit,
        daysAgo: Int,
        duration: Int
    ) async throws -> String {
        return try await withCheckedThrowingContinuation { continuation in
            let predicate = createDatePredicate(daysAgo: daysAgo, duration: duration)
            let sortDescriptor = NSSortDescriptor(key: HKSampleSortIdentifierStartDate, ascending: false)

            let query = HKSampleQuery(
                sampleType: quantityType,
                predicate: predicate,
                limit: 1,
                sortDescriptors: [sortDescriptor]
            ) { _, results, error in
                if let error = error {
                    continuation.resume(throwing: error)
                } else if let sample = results?.first as? HKQuantitySample {
                    let value = sample.quantity.doubleValue(for: unit)
                    continuation.resume(returning: String(format: "%.1f", value))
                } else {
                    continuation.resume(returning: "0")
                }
            }
            healthStore.execute(query)
        }
    }

    /// 读取步数
    private func readStepCount(daysAgo: Int, duration: Int) async throws -> String {
        return try await readStatisticsData(
            quantityType: HKQuantityType(.stepCount),
            unit: .count(),
            daysAgo: daysAgo,
            duration: duration
        )
    }

    /// 读取步行跑步距离
    private func readWalkingRunningDistance(daysAgo: Int, duration: Int) async throws -> String {
        return try await readStatisticsData(
            quantityType: HKQuantityType(.distanceWalkingRunning),
            unit: .meter(),
            daysAgo: daysAgo,
            duration: duration
        )
    }

    /// 读取活动能量
    private func readActiveEnergyBurned(daysAgo: Int, duration: Int) async throws -> String {
        let result = try await readStatisticsData(
            quantityType: HKQuantityType(.activeEnergyBurned),
            unit: .kilocalorie(),
            daysAgo: daysAgo,
            duration: duration
        )
        // 格式化为两位小数
        if let value = Double(result) {
            return String(format: "%.2f", value)
        }
        return result
    }

    /// 读取骑车距离
    private func readCyclingDistance(daysAgo: Int, duration: Int) async throws -> String {
        return try await readStatisticsData(
            quantityType: HKQuantityType(.distanceCycling),
            unit: .meter(),
            daysAgo: daysAgo,
            duration: duration
        )
    }

    /// 读取身高
    private func readHeight(daysAgo: Int, duration: Int) async throws -> String {
        return try await readSampleData(
            quantityType: HKQuantityType(.height),
            unit: .meterUnit(with: .centi),
            daysAgo: daysAgo,
            duration: duration
        )
    }

    /// 读取体重
    private func readWeight(daysAgo: Int, duration: Int) async throws -> String {
        return try await readSampleData(
            quantityType: HKQuantityType(.bodyMass),
            unit: .gramUnit(with: .kilo),
            daysAgo: daysAgo,
            duration: duration
        )
    }

    /// 读取心率
    private func readHeartRate(daysAgo: Int, duration: Int) async throws -> String {
        let heartRateUnit = HKUnit.count().unitDivided(by: .minute())
        return try await readSampleData(
            quantityType: HKQuantityType(.heartRate),
            unit: heartRateUnit,
            daysAgo: daysAgo,
            duration: duration
        )
    }

    /// 读取静息心率
    private func readRestingHeartRate(daysAgo: Int, duration: Int) async throws -> String {
        let heartRateUnit = HKUnit.count().unitDivided(by: .minute())
        return try await readSampleData(
            quantityType: HKQuantityType(.restingHeartRate),
            unit: heartRateUnit,
            daysAgo: daysAgo,
            duration: duration
        )
    }

    /// 读取体脂率
    private func readBodyFatPercentage(daysAgo: Int, duration: Int) async throws -> String {
        let result = try await readSampleData(
            quantityType: HKQuantityType(.bodyFatPercentage),
            unit: .percent(),
            daysAgo: daysAgo,
            duration: duration
        )
        // 转换为百分比显示
        if let value = Double(result) {
            return String(format: "%.1f", value * 100)
        }
        return result
    }

    /// 读取BMI
    private func readBodyMassIndex(daysAgo: Int, duration: Int) async throws -> String {
        return try await readSampleData(
            quantityType: HKQuantityType(.bodyMassIndex),
            unit: .count(),
            daysAgo: daysAgo,
            duration: duration
        )
    }

    /// 读取体能训练时长
    private func readWorkoutDuration(daysAgo: Int, duration: Int) async throws -> String {
        return try await withCheckedThrowingContinuation { continuation in
            let predicate = createDatePredicate(daysAgo: daysAgo, duration: duration)
            let sortDescriptor = NSSortDescriptor(key: HKSampleSortIdentifierStartDate, ascending: false)

            let query = HKSampleQuery(
                sampleType: HKWorkoutType.workoutType(),
                predicate: predicate,
                limit: HKObjectQueryNoLimit,
                sortDescriptors: [sortDescriptor]
            ) { _, results, error in
                if let error = error {
                    continuation.resume(throwing: error)
                } else {
                    let totalDuration = results?.compactMap { $0 as? HKWorkout }
                        .reduce(0) { $0 + $1.duration } ?? 0
                    continuation.resume(returning: String(format: "%.0f", totalDuration))
                }
            }
            healthStore.execute(query)
        }
    }

    /// 读取生日
    private func readDateOfBirth() async throws -> String {
        return try await withCheckedThrowingContinuation { continuation in
            do {
                let dateComponents = try healthStore.dateOfBirthComponents()
                if let date = dateComponents.date {
                    let formatter = DateFormatter()
                    formatter.dateFormat = "yyyy-MM-dd"
                    continuation.resume(returning: formatter.string(from: date))
                } else {
                    continuation.resume(returning: "")
                }
            } catch {
                continuation.resume(throwing: error)
            }
        }
    }

    // MARK: - 数据写入方法

    /// 写入运动数据
    func writeWorkoutData(_ data: HealthDataWriteModel) async throws {
        guard Self.isHealthDataAvailable else {
            throw HealthManagerError.healthKitNotAvailable
        }

        // 注意：设备类型检查已在 HealthDataWriteModel 创建阶段完成
        // 能到达这里的 data.equipmentType 必然是支持的设备类型

        var samples: [HKSample] = []

        // 距离转换为公里
        let distanceInKm = data.distance / 1000.0
        let roundedDistance = (distanceInKm * 100).rounded() / 100  // 保留两位小数
        let roundedCalories = (data.calories * 10).rounded() / 10   // 保留一位小数

        // 创建体能训练记录
        let workout = createWorkoutSample(
            activityType: data.equipmentType.healthKitActivityType,
            duration: data.duration,
            calories: roundedCalories,
            distance: roundedDistance,
            trainId: data.trainId,
            startDate: data.startDate,
            endDate: data.endDate
        )
        samples.append(workout)

        // 创建活动能量记录
        let energySample = createQuantitySample(
            type: HKQuantityType(.activeEnergyBurned),
            quantity: HKQuantity(unit: .kilocalorie(), doubleValue: roundedCalories),
            trainId: data.trainId,
            startDate: data.startDate,
            endDate: data.endDate
        )
        samples.append(energySample)

        // 根据设备类型添加特定数据
        switch data.equipmentType {
        case .bicycle:
            // 骑车距离
            let cyclingSample = createQuantitySample(
                type: HKQuantityType(.distanceCycling),
                quantity: HKQuantity(unit: .meterUnit(with: .kilo), doubleValue: roundedDistance),
                trainId: data.trainId,
                startDate: data.startDate,
                endDate: data.endDate
            )
            samples.append(cyclingSample)

        case .treadmill:
            // 步行跑步距离
            let walkingSample = createQuantitySample(
                type: HKQuantityType(.distanceWalkingRunning),
                quantity: HKQuantity(unit: .meterUnit(with: .kilo), doubleValue: roundedDistance),
                trainId: data.trainId,
                startDate: data.startDate,
                endDate: data.endDate
            )
            samples.append(walkingSample)

        case .elliptical, .rowing:
            // 椭圆机和划船机只记录体能训练和能量消耗
            break
        }

        // 保存所有样本
        try await saveSamples(samples)
    }

    /// 写入身高体重
    func writeHeightAndWeight(height: Double?, weight: Double?) async throws {
        guard Self.isHealthDataAvailable else {
            throw HealthManagerError.healthKitNotAvailable
        }

        var samples: [HKSample] = []
        let now = Date()

        // 写入身高
        if let height = height, height > 0 {
            let heightSample = HKQuantitySample(
                type: HKQuantityType(.height),
                quantity: HKQuantity(unit: .meterUnit(with: .centi), doubleValue: height),
                start: now,
                end: now
            )
            samples.append(heightSample)
        }

        // 写入体重
        if let weight = weight, weight > 0 {
            let weightSample = HKQuantitySample(
                type: HKQuantityType(.bodyMass),
                quantity: HKQuantity(unit: .gramUnit(with: .kilo), doubleValue: weight),
                start: now,
                end: now
            )
            samples.append(weightSample)
        }

        if !samples.isEmpty {
            try await saveSamples(samples)
        }
    }

    // MARK: - 私有写入辅助方法

    /// 创建数量样本
    private func createQuantitySample(
        type: HKQuantityType,
        quantity: HKQuantity,
        trainId: String,
        startDate: Date,
        endDate: Date
    ) -> HKQuantitySample {
        let metadata = ["训练记录": trainId]
        return HKQuantitySample(
            type: type,
            quantity: quantity,
            start: startDate,
            end: endDate,
            metadata: metadata
        )
    }

    /// 创建体能训练样本
    private func createWorkoutSample(
        activityType: HKWorkoutActivityType,
        duration: TimeInterval,
        calories: Double,
        distance: Double,
        trainId: String,
        startDate: Date,
        endDate: Date
    ) -> HKWorkout {
        let energyBurned = HKQuantity(unit: .kilocalorie(), doubleValue: calories)
        let totalDistance = HKQuantity(unit: .meterUnit(with: .kilo), doubleValue: distance)
        let metadata = ["训练记录": trainId]

        return HKWorkout(
            activityType: activityType,
            start: startDate,
            end: endDate,
            duration: duration,
            totalEnergyBurned: energyBurned,
            totalDistance: totalDistance,
            metadata: metadata
        )
    }

    /// 保存样本到HealthKit
    private func saveSamples(_ samples: [HKSample]) async throws {
        try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
            healthStore.save(samples) { success, error in
                if let error = error {
                    continuation.resume(throwing: error)
                } else if success {
                    print("健康数据写入成功")
                    continuation.resume()
                } else {
                    continuation.resume(throwing: HealthManagerError.writeFailed)
                }
            }
        }
    }

    // MARK: - 数据上传方法

    /// 上传健康数据到服务器
    func uploadHealthDataToServer() async {
        // 检查授权状态
        guard isFullyAuthorized else {
            print("健康数据未授权，跳过上传")
            return
        }

        // 检查是否需要上传（每天只上传一次）
        let currentDate = Date()
        let lastUploadDate = userDefaults.object(forKey: lastUploadDateKey) as? Date

        let calendar = Calendar.current
        let currentDateString = DateFormatter.dateOnly.string(from: currentDate)
        let lastDateString = lastUploadDate.map { DateFormatter.dateOnly.string(from: $0) } ?? ""

        guard currentDateString != lastDateString else {
            print("今日已上传健康数据")
            return
        }

        do {
            // 获取服务器最后上传日期
            let serverLastDate = try await getServerLastUploadDate()

            if let serverDate = serverLastDate {
                let daysDifference = calendar.dateComponents([.day], from: serverDate, to: currentDate).day ?? 0
                if daysDifference > 0 {
                    await uploadMultipleDaysData(days: daysDifference)
                }
            } else {
                // 首次上传，上传昨天的数据
                await uploadMultipleDaysData(days: 1)
            }

            // 更新本地上传日期
            userDefaults.set(currentDate, forKey: lastUploadDateKey)

        } catch {
            print("上传健康数据失败: \(error)")
        }
    }

    /// 上传多天的健康数据
    private func uploadMultipleDaysData(days: Int) async {
        var healthDataArray: [HealthDataReadModel] = []

        // 并发读取多天数据
        await withTaskGroup(of: (Int, HealthDataReadModel?).self) { group in
            for day in 1...days {
                group.addTask { [weak self] in
                    do {
                        let data = try await self?.readHealthData(daysAgo: -day, duration: 1)
                        var updatedData = data

                        // 设置数据日期
                        let targetDate = Date().addingTimeInterval(TimeInterval(-day * 24 * 60 * 60))
                        updatedData?.date = DateFormatter.dateOnly.string(from: targetDate)

                        return (day, updatedData)
                    } catch {
                        print("读取第\(day)天健康数据失败: \(error)")
                        return (day, nil)
                    }
                }
            }

            for await (_, data) in group {
                if let data = data {
                    healthDataArray.append(data)
                }
            }
        }

        // 上传到服务器
        if !healthDataArray.isEmpty {
            await uploadHealthDataArray(healthDataArray)
        }
    }

    /// 上传健康数据数组到服务器
    private func uploadHealthDataArray(_ dataArray: [HealthDataReadModel]) async {
        let uploadData = dataArray.compactMap { data -> [String: Any]? in
            let dict = data.toDictionary()
            // 过滤空数据
            return dict.values.contains { value in
                if let stringValue = value as? String, !stringValue.isEmpty && stringValue != "0" {
                    return true
                }
                return false
            } ? dict : nil
        }

        guard !uploadData.isEmpty else {
            print("没有有效的健康数据需要上传")
            return
        }

        let requestData = ["appleUploads": uploadData]

        // 这里应该调用实际的网络请求方法
        // await NetworkManager.uploadHealthData(requestData)
        print("上传健康数据: \(requestData)")
    }

    /// 获取服务器最后上传日期
    private func getServerLastUploadDate() async throws -> Date? {
        // 这里应该调用实际的网络请求方法获取服务器最后上传日期
        // let response = try await NetworkManager.getLastUploadDate()
        // return response.lastDate

        // 临时返回nil，表示首次上传
        return nil
    }
}

// MARK: - 错误定义

/// 健康管理器错误类型
enum HealthManagerError: LocalizedError {
    case healthKitNotAvailable
    case authorizationDenied
    case readFailed
    case writeFailed
    case networkError(String)

    var errorDescription: String? {
        switch self {
        case .healthKitNotAvailable:
            return "HealthKit不可用"
        case .authorizationDenied:
            return "健康数据授权被拒绝"
        case .readFailed:
            return "读取健康数据失败"
        case .writeFailed:
            return "写入健康数据失败"
        case .networkError(let message):
            return "网络错误: \(message)"
        }
    }
}

// MARK: - 扩展

/// 日期格式化器扩展
private extension DateFormatter {
    static let dateOnly: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        return formatter
    }()
}

// MARK: - 静态方法（向后兼容）

extension HealthManager {

    /// 写入运动数据（静态方法，向后兼容）
    static func writeSportData(trainId: String) {
        print("写入苹果健康数据 - 已废弃的方法")
        // 这个方法在原代码中已被标记为废弃
    }

    /// 写入身高体重（静态方法，向后兼容）
    static func writeHeight(_ height: String, weight: String) {
        Task {
            let heightValue = Double(height)
            let weightValue = Double(weight)

            do {
                try await shared.writeHeightAndWeight(height: heightValue, weight: weightValue)
            } catch {
                print("写入身高体重失败: \(error)")
            }
        }
    }

    /// 上传健康数据（静态方法，向后兼容）
    static func uploadHealthData() {
        Task {
            await shared.uploadHealthDataToServer()
        }
    }
}
