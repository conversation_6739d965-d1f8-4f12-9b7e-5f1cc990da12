# 健康数据管理器 - 设备类型支持说明

## 概述

Swift重构版的 `HealthManager` 只支持四大件健身设备的数据写入HealthKit，对于非四大件设备的数据会进行过滤处理，确保只有有效的健康数据被写入系统。

## 支持的设备类型

### 四大件健身设备

| 设备类型 | 设备ID | HealthKit类型 | 中文名称 | 写入的健康数据 |
|---------|--------|---------------|----------|---------------|
| bicycle | 1 | HKWorkoutActivityType.cycling | 单车 | 体能训练 + 活动能量 + 骑车距离 |
| treadmill | 2 | HKWorkoutActivityType.running | 跑步机 | 体能训练 + 活动能量 + 步行跑步距离 |
| rowing | 5 | HKWorkoutActivityType.rowing | 划船机 | 体能训练 + 活动能量 |
| elliptical | 6 | HKWorkoutActivityType.elliptical | 椭圆机 | 体能训练 + 活动能量 |

### 不支持的设备类型

所有不在上述四大件范围内的设备类型都不被支持，包括但不限于：
- 其他有氧设备
- 力量训练器械
- 自由重量设备
- 功能性训练设备

## 处理机制

### 单一过滤点设计

采用**单一过滤点**的设计理念，所有设备类型检查都在模型创建阶段完成，确保类型安全和代码简洁。

```swift
/// 从字典创建模型
init?(from dictionary: [String: Any]) {
    guard let trainId = dictionary["trainId"] as? String ?? dictionary["trainingRecordId"] as? String,
          let equipmentId = dictionary["equipmentId"] as? Int ?? dictionary["productId"] as? Int,
          let durationValue = dictionary["duration"] as? TimeInterval ?? dictionary["takeTime"] as? TimeInterval,
          let distance = dictionary["distance"] as? Double,
          let calories = dictionary["kcal"] as? Double else {
        return nil
    }

    // 唯一的设备类型检查点：利用枚举的类型安全特性
    guard let equipmentType = WorkoutEquipmentType(rawValue: equipmentId) else {
        print("不支持的设备类型ID: \(equipmentId)，无法创建健康数据模型")
        return nil
    }

    // ... 其他初始化代码
}
```

```swift
/// 写入运动数据
func writeWorkoutData(_ data: HealthDataWriteModel) async throws {
    guard Self.isHealthDataAvailable else {
        throw HealthManagerError.healthKitNotAvailable
    }

    // 注意：设备类型检查已在 HealthDataWriteModel 创建阶段完成
    // 能到达这里的 data.equipmentType 必然是支持的设备类型

    // ... 写入逻辑
}
```

**设计优势**：
- **类型安全**：利用Swift枚举的类型安全特性，只有有效的设备类型才能创建模型
- **代码简洁**：避免重复的类型检查，减少代码冗余
- **维护性好**：新增设备类型只需扩展枚举，无需修改检查逻辑
- **性能优化**：减少运行时检查，提高执行效率

## 使用示例

### 正确的使用方式

```swift
// 支持的设备类型数据
let bicycleData: [String: Any] = [
    "trainId": "12345",
    "productId": 1, // 单车
    "duration": 1800.0,
    "distance": 5000.0,
    "kcal": 250.5
]

if let workoutData = HealthDataWriteModel(from: bicycleData) {
    do {
        try await HealthManager.shared.writeWorkoutData(workoutData)
        print("单车运动数据写入成功")
    } catch {
        print("写入失败: \(error)")
    }
} else {
    print("无法创建运动数据模型")
}
```

### 不支持设备类型的处理

```swift
// 不支持的设备类型数据
let unsupportedData: [String: Any] = [
    "trainId": "12345",
    "productId": 99, // 不支持的设备类型
    "duration": 1800.0,
    "distance": 5000.0,
    "kcal": 250.5
]

// 模型创建会返回nil
if let workoutData = HealthDataWriteModel(from: unsupportedData) {
    // 这个分支不会执行
    try await HealthManager.shared.writeWorkoutData(workoutData)
} else {
    print("不支持的设备类型，数据不会写入HealthKit")
    // 可以在这里处理不支持设备的逻辑，比如：
    // 1. 记录到本地数据库
    // 2. 上传到服务器
    // 3. 显示用户提示
}
```

## 错误处理

### 日志输出

当遇到不支持的设备类型时，系统会输出相应的日志：

```
不支持的设备类型ID: 99，无法创建健康数据模型
不支持的设备类型: 未知设备，跳过健康数据写入
```

### 静默处理

- 不支持的设备类型不会抛出异常
- 采用静默过滤的方式，避免影响其他功能
- 通过日志记录便于问题排查

## 扩展支持

采用单一过滤点设计后，扩展新设备类型变得非常简单，只需要两步：

### 1. 扩展枚举定义

```swift
enum WorkoutEquipmentType: Int, CaseIterable {
    case bicycle = 1
    case treadmill = 2
    case rowing = 5
    case elliptical = 6
    case newEquipment = 7  // 新增设备类型

    var healthKitActivityType: HKWorkoutActivityType {
        switch self {
        // ... 现有cases
        case .newEquipment:
            return .other  // 或其他合适的HealthKit类型
        }
    }

    var displayName: String {
        switch self {
        // ... 现有cases
        case .newEquipment:
            return "新设备"
        }
    }
}
```

### 2. 更新写入逻辑

```swift
switch data.equipmentType {
// ... 现有cases
case .newEquipment:
    // 添加新设备特定的健康数据写入逻辑
    let newEquipmentSample = createQuantitySample(
        type: HKQuantityType(.someNewType),
        quantity: HKQuantity(unit: .someUnit(), doubleValue: someValue),
        trainId: data.trainId,
        startDate: data.startDate,
        endDate: data.endDate
    )
    samples.append(newEquipmentSample)
}
```

### 优势

- **无需修改检查逻辑**：由于使用了 `CaseIterable`，新的枚举值会自动被支持
- **编译时安全**：Swift编译器会确保所有switch语句都处理了新的case
- **维护简单**：只需要在两个地方添加代码，不会遗漏

## 总结

通过在模型创建和数据写入两个阶段进行设备类型检查，确保了：

1. **数据完整性**：只有有效的健康数据被写入HealthKit
2. **系统稳定性**：避免无效数据导致的异常
3. **可维护性**：清晰的过滤逻辑便于后续扩展
4. **用户体验**：静默处理不会影响用户使用

这种设计既保证了当前四大件设备的正常使用，又为将来的功能扩展留下了空间。
