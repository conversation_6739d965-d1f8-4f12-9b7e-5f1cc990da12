# 健康数据管理器 - 设备类型支持说明

## 概述

Swift重构版的 `HealthManager` 只支持四大件健身设备的数据写入HealthKit，对于非四大件设备的数据会进行过滤处理，确保只有有效的健康数据被写入系统。

## 支持的设备类型

### 四大件健身设备

| 设备类型 | 设备ID | HealthKit类型 | 中文名称 | 写入的健康数据 |
|---------|--------|---------------|----------|---------------|
| bicycle | 1 | HKWorkoutActivityType.cycling | 单车 | 体能训练 + 活动能量 + 骑车距离 |
| treadmill | 2 | HKWorkoutActivityType.running | 跑步机 | 体能训练 + 活动能量 + 步行跑步距离 |
| elliptical | 3 | HKWorkoutActivityType.elliptical | 椭圆机 | 体能训练 + 活动能量 |
| rowing | 4 | HKWorkoutActivityType.rowing | 划船机 | 体能训练 + 活动能量 |

### 不支持的设备类型

所有不在上述四大件范围内的设备类型都不被支持，包括但不限于：
- 其他有氧设备
- 力量训练器械
- 自由重量设备
- 功能性训练设备

## 处理机制

### 1. 模型创建阶段过滤

```swift
/// 从字典创建模型
init?(from dictionary: [String: Any]) {
    guard let trainId = dictionary["trainId"] as? String ?? dictionary["trainingRecordId"] as? String,
          let equipmentId = dictionary["equipmentId"] as? Int ?? dictionary["productId"] as? Int,
          let durationValue = dictionary["duration"] as? TimeInterval ?? dictionary["takeTime"] as? TimeInterval,
          let distance = dictionary["distance"] as? Double,
          let calories = dictionary["kcal"] as? Double else {
        return nil
    }
    
    // 检查设备类型是否为支持的四大件设备
    guard let equipmentType = WorkoutEquipmentType(rawValue: equipmentId) else {
        print("不支持的设备类型ID: \(equipmentId)，无法创建健康数据模型")
        return nil
    }
    
    // ... 其他初始化代码
}
```

**特点**：
- 在数据模型创建阶段就进行过滤
- 不支持的设备类型直接返回 `nil`
- 避免了无效数据进入后续处理流程

### 2. 写入阶段双重检查

```swift
/// 写入运动数据
func writeWorkoutData(_ data: HealthDataWriteModel) async throws {
    guard Self.isHealthDataAvailable else {
        throw HealthManagerError.healthKitNotAvailable
    }
    
    // 检查是否为支持的四大件设备类型
    guard isSupportedEquipmentType(data.equipmentType) else {
        print("不支持的设备类型: \(data.equipmentType.displayName)，跳过健康数据写入")
        return
    }
    
    // ... 写入逻辑
}

/// 检查是否为支持的设备类型（四大件设备）
private func isSupportedEquipmentType(_ equipmentType: WorkoutEquipmentType) -> Bool {
    let supportedTypes: [WorkoutEquipmentType] = [.bicycle, .treadmill, .elliptical, .rowing]
    return supportedTypes.contains(equipmentType)
}
```

**特点**：
- 在写入阶段进行二次验证
- 即使模型创建成功，也会再次检查设备类型
- 提供清晰的日志输出，便于调试

## 使用示例

### 正确的使用方式

```swift
// 支持的设备类型数据
let bicycleData: [String: Any] = [
    "trainId": "12345",
    "productId": 1, // 单车
    "duration": 1800.0,
    "distance": 5000.0,
    "kcal": 250.5
]

if let workoutData = HealthDataWriteModel(from: bicycleData) {
    do {
        try await HealthManager.shared.writeWorkoutData(workoutData)
        print("单车运动数据写入成功")
    } catch {
        print("写入失败: \(error)")
    }
} else {
    print("无法创建运动数据模型")
}
```

### 不支持设备类型的处理

```swift
// 不支持的设备类型数据
let unsupportedData: [String: Any] = [
    "trainId": "12345",
    "productId": 99, // 不支持的设备类型
    "duration": 1800.0,
    "distance": 5000.0,
    "kcal": 250.5
]

// 模型创建会返回nil
if let workoutData = HealthDataWriteModel(from: unsupportedData) {
    // 这个分支不会执行
    try await HealthManager.shared.writeWorkoutData(workoutData)
} else {
    print("不支持的设备类型，数据不会写入HealthKit")
    // 可以在这里处理不支持设备的逻辑，比如：
    // 1. 记录到本地数据库
    // 2. 上传到服务器
    // 3. 显示用户提示
}
```

## 错误处理

### 日志输出

当遇到不支持的设备类型时，系统会输出相应的日志：

```
不支持的设备类型ID: 99，无法创建健康数据模型
不支持的设备类型: 未知设备，跳过健康数据写入
```

### 静默处理

- 不支持的设备类型不会抛出异常
- 采用静默过滤的方式，避免影响其他功能
- 通过日志记录便于问题排查

## 扩展支持

如果将来需要支持新的设备类型，只需要：

1. **扩展枚举**：
```swift
enum WorkoutEquipmentType: Int, CaseIterable {
    case bicycle = 1
    case treadmill = 2
    case elliptical = 3
    case rowing = 4
    case newEquipment = 5  // 新增设备类型
    
    var healthKitActivityType: HKWorkoutActivityType {
        switch self {
        // ... 现有cases
        case .newEquipment:
            return .other  // 或其他合适的HealthKit类型
        }
    }
    
    var displayName: String {
        switch self {
        // ... 现有cases
        case .newEquipment:
            return "新设备"
        }
    }
}
```

2. **更新支持检查**：
```swift
private func isSupportedEquipmentType(_ equipmentType: WorkoutEquipmentType) -> Bool {
    let supportedTypes: [WorkoutEquipmentType] = [
        .bicycle, .treadmill, .elliptical, .rowing, .newEquipment
    ]
    return supportedTypes.contains(equipmentType)
}
```

3. **更新写入逻辑**：
```swift
switch data.equipmentType {
// ... 现有cases
case .newEquipment:
    // 添加新设备特定的健康数据写入逻辑
    break
}
```

## 总结

通过在模型创建和数据写入两个阶段进行设备类型检查，确保了：

1. **数据完整性**：只有有效的健康数据被写入HealthKit
2. **系统稳定性**：避免无效数据导致的异常
3. **可维护性**：清晰的过滤逻辑便于后续扩展
4. **用户体验**：静默处理不会影响用户使用

这种设计既保证了当前四大件设备的正常使用，又为将来的功能扩展留下了空间。
