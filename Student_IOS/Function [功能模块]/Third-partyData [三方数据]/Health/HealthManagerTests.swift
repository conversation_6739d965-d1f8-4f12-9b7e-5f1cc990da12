//
//  HealthManagerTests.swift
//  Student_IOS
//
//  Created by merit on 2025/7/16.
//  健康数据管理器测试用例
//

import XCTest
import HealthKit
@testable import Student_IOS

/// 健康数据管理器测试类
@MainActor
final class HealthManagerTests: XCTestCase {
    
    var healthManager: HealthManager!
    
    override func setUp() {
        super.setUp()
        healthManager = HealthManager.shared
    }
    
    override func tearDown() {
        healthManager = nil
        super.tearDown()
    }
    
    // MARK: - 基础功能测试
    
    /// 测试HealthKit可用性
    func testHealthKitAvailability() {
        // 在模拟器上HealthKit不可用，在真机上可用
        let isAvailable = HealthManager.isHealthDataAvailable
        print("HealthKit可用性: \(isAvailable)")
        
        // 这个测试在不同环境下结果不同，所以只是验证方法能正常调用
        XCTAssertNotNil(isAvailable)
    }
    
    /// 测试授权状态获取
    func testAuthorizationStatus() {
        let heartRateType = HKQuantityType(.heartRate)
        let status = healthManager.authorizationStatus(for: heartRateType)
        
        // 验证返回的是有效的授权状态
        XCTAssertTrue([.notDetermined, .denied, .authorized].contains(status))
    }
    
    /// 测试设备类型枚举
    func testWorkoutEquipmentType() {
        // 测试所有设备类型
        let bicycle = WorkoutEquipmentType.bicycle
        XCTAssertEqual(bicycle.rawValue, 1)
        XCTAssertEqual(bicycle.healthKitActivityType, .cycling)
        XCTAssertEqual(bicycle.displayName, "单车")
        
        let treadmill = WorkoutEquipmentType.treadmill
        XCTAssertEqual(treadmill.rawValue, 2)
        XCTAssertEqual(treadmill.healthKitActivityType, .running)
        XCTAssertEqual(treadmill.displayName, "跑步机")
        
        let elliptical = WorkoutEquipmentType.elliptical
        XCTAssertEqual(elliptical.rawValue, 3)
        XCTAssertEqual(elliptical.healthKitActivityType, .elliptical)
        XCTAssertEqual(elliptical.displayName, "椭圆机")
        
        let rowing = WorkoutEquipmentType.rowing
        XCTAssertEqual(rowing.rawValue, 4)
        XCTAssertEqual(rowing.healthKitActivityType, .rowing)
        XCTAssertEqual(rowing.displayName, "划船机")
    }

    /// 测试设备类型支持检查
    func testEquipmentTypeSupport() {
        // 测试四大件设备都支持
        let supportedTypes: [WorkoutEquipmentType] = [.bicycle, .treadmill, .elliptical, .rowing]

        for equipmentType in supportedTypes {
            let testData: [String: Any] = [
                "trainId": "test123",
                "productId": equipmentType.rawValue,
                "duration": 1800.0,
                "distance": 5000.0,
                "kcal": 250.5
            ]

            let model = HealthDataWriteModel(from: testData)
            XCTAssertNotNil(model, "\(equipmentType.displayName) 应该被支持")
            XCTAssertEqual(model?.equipmentType, equipmentType)
        }

        // 测试不支持的设备类型ID
        let unsupportedIds = [0, 5, 10, 99, -1]

        for unsupportedId in unsupportedIds {
            let testData: [String: Any] = [
                "trainId": "test123",
                "productId": unsupportedId,
                "duration": 1800.0,
                "distance": 5000.0,
                "kcal": 250.5
            ]

            let model = HealthDataWriteModel(from: testData)
            XCTAssertNil(model, "设备类型ID \(unsupportedId) 不应该被支持")
        }
    }
    
    // MARK: - 数据模型测试
    
    /// 测试健康数据写入模型创建
    func testHealthDataWriteModelCreation() {
        // 测试有效数据（四大件设备）
        let validData: [String: Any] = [
            "trainId": "12345",
            "productId": 1,
            "duration": 1800.0,
            "distance": 5000.0,
            "kcal": 250.5
        ]

        let model = HealthDataWriteModel(from: validData)
        XCTAssertNotNil(model)
        XCTAssertEqual(model?.trainId, "12345")
        XCTAssertEqual(model?.equipmentType, .bicycle)
        XCTAssertEqual(model?.duration, 1800.0)
        XCTAssertEqual(model?.distance, 5000.0)
        XCTAssertEqual(model?.calories, 250.5)

        // 测试无效数据（缺少必要字段）
        let invalidData: [String: Any] = [
            "trainId": "12345"
            // 缺少必要字段
        ]

        let invalidModel = HealthDataWriteModel(from: invalidData)
        XCTAssertNil(invalidModel)

        // 测试不支持的设备类型
        let unsupportedEquipmentData: [String: Any] = [
            "trainId": "12345",
            "productId": 99, // 不支持的设备类型ID
            "duration": 1800.0,
            "distance": 5000.0,
            "kcal": 250.5
        ]

        let unsupportedModel = HealthDataWriteModel(from: unsupportedEquipmentData)
        XCTAssertNil(unsupportedModel, "不支持的设备类型应该返回nil")
    }
    
    /// 测试健康数据读取模型转换
    func testHealthDataReadModelToDictionary() {
        let model = HealthDataReadModel(
            stepCount: "10000",
            walkingRunningDistance: "5000",
            activeEnergyBurned: "250.5",
            cyclingDistance: "0",
            height: "175",
            weight: "70.5",
            birthday: "1990-01-01",
            workoutDuration: "1800",
            heartRate: "75",
            restingHeartRate: "60",
            bodyFatPercentage: "15.5",
            bodyMassIndex: "23.0",
            date: "2025-07-16"
        )
        
        let dictionary = model.toDictionary()
        
        XCTAssertEqual(dictionary["footNum"] as? String, "10000")
        XCTAssertEqual(dictionary["runDistance"] as? String, "5000")
        XCTAssertEqual(dictionary["kcal"] as? String, "250.5")
        XCTAssertEqual(dictionary["height"] as? String, "175")
        XCTAssertEqual(dictionary["weight"] as? String, "70.5")
        XCTAssertEqual(dictionary["birthday"] as? String, "1990-01-01")
        XCTAssertEqual(dictionary["heartRate"] as? String, "75")
        XCTAssertEqual(dictionary["restRate"] as? String, "60")
    }
    
    // MARK: - 错误处理测试
    
    /// 测试错误类型
    func testHealthManagerError() {
        let healthKitError = HealthManagerError.healthKitNotAvailable
        XCTAssertEqual(healthKitError.errorDescription, "HealthKit不可用")
        
        let authError = HealthManagerError.authorizationDenied
        XCTAssertEqual(authError.errorDescription, "健康数据授权被拒绝")
        
        let readError = HealthManagerError.readFailed
        XCTAssertEqual(readError.errorDescription, "读取健康数据失败")
        
        let writeError = HealthManagerError.writeFailed
        XCTAssertEqual(writeError.errorDescription, "写入健康数据失败")
        
        let networkError = HealthManagerError.networkError("连接超时")
        XCTAssertEqual(networkError.errorDescription, "网络错误: 连接超时")
    }
    
    // MARK: - 异步方法测试
    
    /// 测试授权请求（模拟）
    func testRequestAuthorization() async {
        // 注意：这个测试在模拟器上会失败，因为HealthKit不可用
        // 在真机上需要用户交互才能完成授权
        
        do {
            let result = try await healthManager.requestAuthorization()
            print("授权结果: \(result)")
            // 在模拟器上通常会抛出异常
        } catch {
            print("授权失败（预期行为）: \(error)")
            XCTAssertTrue(error is HealthManagerError)
        }
    }
    
    /// 测试健康数据读取（模拟）
    func testReadHealthData() async {
        do {
            let data = try await healthManager.readHealthData(daysAgo: -1, duration: 1)
            print("读取到健康数据: \(data)")
            
            // 验证数据结构
            XCTAssertNotNil(data.stepCount)
            XCTAssertNotNil(data.walkingRunningDistance)
            XCTAssertNotNil(data.activeEnergyBurned)
            
        } catch {
            print("读取健康数据失败（预期行为）: \(error)")
            // 在没有授权或模拟器环境下会失败
            XCTAssertTrue(error is HealthManagerError)
        }
    }
    
    /// 测试运动数据写入（模拟）
    func testWriteWorkoutData() async {
        // 测试支持的设备类型
        let testData = HealthDataWriteModel(from: [
            "trainId": "test123",
            "productId": 1, // 单车
            "duration": 1800.0,
            "distance": 5000.0,
            "kcal": 250.5
        ])

        guard let data = testData else {
            XCTFail("无法创建测试数据")
            return
        }

        do {
            try await healthManager.writeWorkoutData(data)
            print("运动数据写入成功")
        } catch {
            print("运动数据写入失败（预期行为）: \(error)")
            // 在没有授权或模拟器环境下会失败
            XCTAssertTrue(error is HealthManagerError)
        }
    }

    /// 测试不支持设备类型的写入处理
    func testWriteUnsupportedEquipmentData() async {
        // 由于不支持的设备类型在模型创建阶段就会返回nil，
        // 所以这里主要测试模型创建的行为

        let unsupportedData: [String: Any] = [
            "trainId": "test123",
            "productId": 99, // 不支持的设备类型
            "duration": 1800.0,
            "distance": 5000.0,
            "kcal": 250.5
        ]

        let model = HealthDataWriteModel(from: unsupportedData)
        XCTAssertNil(model, "不支持的设备类型应该无法创建模型")

        // 如果模型为nil，writeWorkoutData方法不会被调用
        // 这样就避免了写入不支持的设备数据
    }
    
    // MARK: - 性能测试
    
    /// 测试并发数据读取性能
    func testConcurrentDataReading() async {
        let startTime = CFAbsoluteTimeGetCurrent()
        
        // 模拟并发读取多项数据
        await withTaskGroup(of: Void.self) { group in
            for i in 1...5 {
                group.addTask {
                    do {
                        _ = try await self.healthManager.readHealthData(daysAgo: -i, duration: 1)
                    } catch {
                        print("并发读取失败: \(error)")
                    }
                }
            }
        }
        
        let timeElapsed = CFAbsoluteTimeGetCurrent() - startTime
        print("并发读取耗时: \(timeElapsed)秒")
        
        // 验证性能（应该在合理时间内完成）
        XCTAssertLessThan(timeElapsed, 10.0, "并发读取耗时过长")
    }
    
    // MARK: - 向后兼容性测试
    
    /// 测试静态方法向后兼容性
    func testBackwardCompatibility() {
        // 测试静态方法不会崩溃
        HealthManager.writeSportData(trainId: "test123")
        HealthManager.writeHeight("175", weight: "70")
        HealthManager.uploadHealthData()
        
        // 这些方法应该能正常调用而不崩溃
        XCTAssertTrue(true, "向后兼容方法调用成功")
    }
}
