# HealthManager - Swift重构版健康数据管理器

## 概述

`HealthManager` 是基于原始 `MRKHealthManager` 的 Swift 重构版本，充分利用了 Swift 的现代特性，包括 async/await、枚举、结构体、协议、泛型等，提供了更安全、更易用的健康数据管理功能。

## 主要特性

### 🏋️ 设备类型支持

**仅支持四大件健身设备**：
- 单车 (ID: 1) → HKWorkoutActivityType.cycling
- 跑步机 (ID: 2) → HKWorkoutActivityType.running
- 椭圆机 (ID: 3) → HKWorkoutActivityType.elliptical
- 划船机 (ID: 4) → HKWorkoutActivityType.rowing

**重要说明**：
- 只有四大件设备的运动数据会被写入HealthKit
- 其他设备类型的数据会在模型创建阶段被过滤
- 不支持的设备类型会返回nil，避免无效数据写入

### 🚀 Swift 现代特性

1. **async/await 异步编程**
   - 所有网络请求和数据操作都使用 async/await
   - 避免了回调地狱，代码更清晰

2. **强类型枚举**
   - `HealthAuthorizationStatus`: 授权状态枚举
   - `WorkoutEquipmentType`: 运动设备类型枚举

3. **结构体数据模型**
   - `HealthDataWriteModel`: 健康数据写入模型
   - `HealthDataReadModel`: 健康数据读取模型

4. **协议导向编程**
   - `HealthManagerProtocol`: 定义健康管理器接口

5. **并发编程**
   - 使用 `TaskGroup` 并发读取多项健康数据
   - 提高数据读取效率

6. **错误处理**
   - 自定义 `HealthManagerError` 枚举
   - 符合 `LocalizedError` 协议

### 📊 功能对比

| 功能 | 原版本 | Swift版本 | 改进 |
|------|--------|-----------|------|
| 异步处理 | 回调闭包 | async/await | 更清晰的异步代码 |
| 错误处理 | 可选错误参数 | throws/Result | 强制错误处理 |
| 数据模型 | NSDictionary | 强类型结构体 | 类型安全 |
| 并发处理 | RAC信号 | TaskGroup | 原生并发支持 |
| 代码组织 | 单一类 | 协议+扩展 | 更好的模块化 |

## 使用示例

### 基本使用

```swift
import SwiftUI

struct HealthView: View {
    @StateObject private var healthManager = HealthManager.shared
    @State private var healthData: HealthDataReadModel?
    @State private var isLoading = false
    @State private var errorMessage: String?
    
    var body: some View {
        VStack {
            // 授权状态显示
            HStack {
                Text("授权状态:")
                Text(healthManager.isAuthorized ? "已授权" : "未授权")
                    .foregroundColor(healthManager.isAuthorized ? .green : .red)
            }
            
            // 请求授权按钮
            if !healthManager.isAuthorized {
                Button("请求健康数据授权") {
                    Task {
                        do {
                            let success = try await healthManager.requestAuthorization()
                            print("授权结果: \(success)")
                        } catch {
                            errorMessage = error.localizedDescription
                        }
                    }
                }
                .buttonStyle(.borderedProminent)
            }
            
            // 读取数据按钮
            Button("读取健康数据") {
                Task {
                    isLoading = true
                    do {
                        healthData = try await healthManager.readHealthData(
                            daysAgo: -1, 
                            duration: 1
                        )
                    } catch {
                        errorMessage = error.localizedDescription
                    }
                    isLoading = false
                }
            }
            .disabled(!healthManager.isAuthorized || isLoading)
            
            // 数据显示
            if let data = healthData {
                HealthDataView(data: data)
            }
            
            // 错误信息
            if let error = errorMessage {
                Text("错误: \(error)")
                    .foregroundColor(.red)
            }
        }
        .padding()
    }
}

struct HealthDataView: View {
    let data: HealthDataReadModel
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("健康数据")
                .font(.headline)
            
            Group {
                Text("步数: \(data.stepCount)")
                Text("距离: \(data.walkingRunningDistance)米")
                Text("消耗: \(data.activeEnergyBurned)千卡")
                Text("体重: \(data.weight)kg")
                Text("心率: \(data.heartRate)次/分")
            }
            .font(.body)
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(8)
    }
}
```

### 写入运动数据

```swift
// 创建支持的设备运动数据
let supportedWorkoutData = HealthDataWriteModel(from: [
    "trainId": "12345",
    "productId": 1, // 单车（支持）
    "duration": 1800, // 30分钟
    "distance": 5000, // 5公里
    "kcal": 250.5
])

// 写入支持的设备数据
if let data = supportedWorkoutData {
    do {
        try await HealthManager.shared.writeWorkoutData(data)
        print("运动数据写入成功")
    } catch {
        print("写入失败: \(error)")
    }
} else {
    print("无法创建运动数据模型")
}

// 尝试创建不支持的设备运动数据
let unsupportedWorkoutData = HealthDataWriteModel(from: [
    "trainId": "12345",
    "productId": 99, // 不支持的设备类型
    "duration": 1800,
    "distance": 5000,
    "kcal": 250.5
])

// 不支持的设备数据会返回nil
if unsupportedWorkoutData == nil {
    print("不支持的设备类型，数据不会写入HealthKit")
}
```

### 写入身高体重

```swift
do {
    try await HealthManager.shared.writeHeightAndWeight(
        height: 175.0, // 175cm
        weight: 70.5   // 70.5kg
    )
    print("身高体重写入成功")
} catch {
    print("写入失败: \(error)")
}
```

### 上传数据到服务器

```swift
// 自动上传健康数据
await HealthManager.shared.uploadHealthDataToServer()
```

## 架构设计

### 类图结构

```
HealthManagerProtocol
        ↑
HealthManager (主类)
    ├── HealthDataWriteModel (写入模型)
    ├── HealthDataReadModel (读取模型)
    ├── WorkoutEquipmentType (设备类型枚举)
    ├── HealthAuthorizationStatus (授权状态枚举)
    └── HealthManagerError (错误类型枚举)
```

### 方法分组

1. **授权管理**
   - `requestAuthorization()`: 请求完整授权
   - `requestHeartRateAuthorization()`: 请求心率授权
   - `authorizationStatus(for:)`: 获取授权状态

2. **数据读取**
   - `readHealthData(daysAgo:duration:)`: 读取健康数据
   - 私有读取方法：`readStepCount`, `readWeight` 等

3. **数据写入**
   - `writeWorkoutData(_:)`: 写入运动数据
   - `writeHeightAndWeight(height:weight:)`: 写入身高体重

4. **数据上传**
   - `uploadHealthDataToServer()`: 上传到服务器
   - 私有上传方法：`uploadMultipleDaysData`, `uploadHealthDataArray`

## 优化亮点

### 1. 类型安全

```swift
// 原版本 - 弱类型
- (void)saveSportData:(MRKHealthKitWriteModel *)data;

// Swift版本 - 强类型
func writeWorkoutData(_ data: HealthDataWriteModel) async throws
```

### 2. 错误处理

```swift
// 原版本 - 可选错误
- (void)authorizateHealthKit:(void(^)(BOOL))resultBlock;

// Swift版本 - 强制错误处理
func requestAuthorization() async throws -> Bool
```

### 3. 并发处理

```swift
// 原版本 - RAC信号
[[RACSignal combineLatest:actions] subscribeNext:^(id x) {
    // 处理结果
}];

// Swift版本 - TaskGroup
async let stepCount = readStepCount(daysAgo: daysAgo, duration: duration)
async let walkingDistance = readWalkingRunningDistance(daysAgo: daysAgo, duration: duration)
// ... 其他数据读取

let results = try await (stepCount, walkingDistance, ...)
```

### 4. 枚举优化

```swift
// 原版本 - 魔法数字
case BicycleEquipment: //单车
    return [self writeWorkoutHealthData:HKWorkoutActivityTypeCycling ...];

// Swift版本 - 类型安全枚举
enum WorkoutEquipmentType: Int, CaseIterable {
    case bicycle = 1
    
    var healthKitActivityType: HKWorkoutActivityType {
        switch self {
        case .bicycle: return .cycling
        }
    }
}
```

## 向后兼容

为了保持与现有代码的兼容性，提供了静态方法：

```swift
// 兼容原有调用方式
HealthManager.writeHeight("175", weight: "70")
HealthManager.uploadHealthData()
```

## 总结

Swift重构版的 `HealthManager` 通过以下方式显著提升了代码质量：

1. **类型安全**: 使用强类型结构体和枚举
2. **异步编程**: 采用 async/await 模式
3. **错误处理**: 强制错误处理机制
4. **并发优化**: 使用 TaskGroup 提高性能
5. **代码组织**: 清晰的方法分组和协议设计
6. **可测试性**: 协议导向设计便于单元测试

这个重构版本不仅保持了原有功能的完整性，还大幅提升了代码的可读性、可维护性和安全性。
