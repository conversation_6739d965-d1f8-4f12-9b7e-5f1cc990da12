//
//  MRKHealthManager.m
//  Student_IOS
//
//  Created by merit on 2023/5/31.
//

#import "MRKHealthManager.h"
#import "NSDate+BRPickerView.h"
#import "MRKHealthViewModel.h"
@interface MRKHealthManager()

@property (nonatomic, strong) HKHealthStore *store;
@end

@implementation MRKHealthManager
#pragma mark -  ------ 外部方法  ------

/// 写入苹果健康数据
+ (void)writeSportData:(NSString *)trainID {
    MLog(@"写入苹果健康数据========请求接口写入废弃");
    
//    [MRKBaseRequest mrkGetRequestUrl:@"/user/training/detail"
//                             andParm:@{@"trainId":trainID, @"cycleTime":@"10"}
//            completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
//        id data = [request.responseObject objectForKey:@"data"];
//        MRKHealthKitWriteModel *model = [MRKHealthKitWriteModel modelWithJSON:data];
//        [[MRKHealthManager shared] saveSportData:model];
//    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
//        
//        MLog(@"写入苹果健康数据========接口失败");
//    }];
}

+ (void)writeHeight:(NSString *)height weight:(NSString *)weight {
    NSMutableArray *array = [NSMutableArray array];
    NSDate *today = [NSDate date];
    if (height.floatValue >0) {
        HKQuantityType *heightType = [HKQuantityType quantityTypeForIdentifier:HKQuantityTypeIdentifierHeight];
        HKQuantity *heightQuantity = [HKQuantity quantityWithUnit:[HKUnit meterUnitWithMetricPrefix:HKMetricPrefixCenti] doubleValue:height.floatValue];
        HKQuantitySample *heightSample = [HKQuantitySample quantitySampleWithType:heightType quantity:heightQuantity startDate:today endDate:today];
        [array addObject:heightSample];
    }
    
    if (weight.floatValue >0) {
        HKQuantityType *weightType = [HKQuantityType quantityTypeForIdentifier:HKQuantityTypeIdentifierBodyMass];
        HKQuantity *weightQuantity = [HKQuantity quantityWithUnit:[HKUnit gramUnitWithMetricPrefix:HKMetricPrefixKilo] doubleValue:weight.floatValue];
        HKQuantitySample *weightSample = [HKQuantitySample quantitySampleWithType:weightType quantity:weightQuantity startDate:today endDate:today];
        [array addObject:weightSample];
    }
    [[MRKHealthManager shared].store saveObjects:array withCompletion:^(BOOL success, NSError * _Nullable error) {
        NSLog(@"%d",success);
    }];
}

/// 上报苹果健康数据
+ (void)uploadHealthData {
    // 授权
    BOOL b = [[MRKHealthManager shared] healthKitContent];
    if (!b) {
        return;
    }
    // 已授权
    NSUserDefaults *userDefault = [NSUserDefaults standardUserDefaults];
    NSDate *currentDate = [NSDate date];
    NSDate *lastDate = [userDefault objectForKey:AppLaunch_LastDate];
    NSString *lastDateString = [MRKTimeManager getDateTimeString:lastDate];
    NSString *currentDateString = [MRKTimeManager getDateTimeString:currentDate];
    
    if ([lastDateString isEqualToString:currentDateString]) {
    } else {
        // 需要执行的方法写在这里
        [MRKHealthViewModel requestSportLastDate:^(BOOL hasTime, NSString * _Nonnull lastDate) {
            if (hasTime) {
                NSDate *start = [MRKTimeManager dateFromDateString:lastDate formate:@"yyyy-MM-dd"];
                NSDate *end = [[NSDate date] br_getNewDateToDays:-1];
                NSInteger day = [MRKTimeManager computeInsertStarTime:start andInsertEndTime:end];
                if (day > 0) {
                    [MRKHealthManager queryBeforeData:day];
                }
            }else {
                [MRKHealthManager queryBeforeData:1];
            }
        }];
        
        // 上传数据
        //        [[MRKHealthManager shared] readSportData:-1 success:^(MRKHealthKitReadModel * _Nonnull model) {
        //            NSDate *before = [[NSDate date] br_getNewDateToDays:-1];
        //            model.date = [MRKTimeManager getDateTimeString:before];
        //            [MRKHealthViewModel requestUploadHealthData:[model modelToJSONObject]];
        //        }];
    }
}

/// 查询之前的数据
/// - Parameter days: 前几天
+ (void)queryBeforeData:(NSInteger)days {
    NSUserDefaults *userDefault = [NSUserDefaults standardUserDefaults];
    NSDate *date = [NSDate date];
    [userDefault setObject:date forKey:AppLaunch_LastDate];
    [userDefault synchronize];
    
    NSMutableArray *actions = [NSMutableArray array];
    NSMutableArray *datas = [NSMutableArray array];
    for (NSInteger i = days; i > 0; i--) {
        RACSignal *action = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
            [[MRKHealthManager shared] readSportData:-i duration:1 success:^(MRKHealthKitReadModel * _Nonnull model) {
                NSDate *before = [[NSDate date] br_getNewDateToDays:-i];
                model.date = [MRKTimeManager getDateTimeString:before];
                [datas addObject:model];
                [subscriber sendNext:@1];
            }];
            return nil;
        }];
        [actions addObject:action];
    }
    // 全部查询完成
    [[RACSignal combineLatest:actions] subscribeNext:^(id x) {
        NSLog(@"%@",datas);
        [MRKHealthManager uploadAppleSportData:datas];
    }];
}
+ (void)uploadAppleSportData:(NSArray *)data {
    NSMutableArray *array = [NSMutableArray array];
    for (MRKHealthKitReadModel *model in data) {
        id obj = [model modelToJSONObject];
        if ([obj isNotEmpty]) {
            [array addObject:obj];
        }
    }
    if (array.count > 0) {
        NSDictionary *dic = @{@"appleUploads":array};
        [MRKHealthViewModel requestUploadHealthData:dic];
    }
}







#pragma mark - -----------------------
- (HKHealthStore *)store {
    if (!_store) {
        _store = [[HKHealthStore alloc] init];
    }
    return _store;
}

static MRKHealthManager *_healthManager = nil;
+ (instancetype)shared
{
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        _healthManager = [[MRKHealthManager alloc] init];
    });
    return _healthManager;
}

/// 读写权限的集合
- (NSSet *)dataWriteTypes {
    //步数、步行+跑步距离、活动能量、骑车距离、身高、身高体重指数、体能训练、体脂率、体重、静息心率、心率。
    HKQuantityType *stepType = [HKQuantityType quantityTypeForIdentifier:HKQuantityTypeIdentifierStepCount]; //步行
    HKQuantityType *walkingRunningType = [HKQuantityType quantityTypeForIdentifier:HKQuantityTypeIdentifierDistanceWalkingRunning]; //步行+跑步
    HKQuantityType *energyBurned = [HKQuantityType quantityTypeForIdentifier:HKQuantityTypeIdentifierActiveEnergyBurned]; //活动能量
    HKQuantityType *cycleType = [HKQuantityType quantityTypeForIdentifier:HKQuantityTypeIdentifierDistanceCycling]; //骑车距离
    HKQuantityType *bodyType = [HKQuantityType quantityTypeForIdentifier:HKQuantityTypeIdentifierBodyMassIndex]; //身高体重指数
    HKWorkoutType *workOut = [HKWorkoutType workoutType]; //体能训练
    HKQuantityType *bodyFatType = [HKQuantityType quantityTypeForIdentifier:HKQuantityTypeIdentifierBodyFatPercentage]; //体脂率
    HKQuantityType *heightType = [HKQuantityType quantityTypeForIdentifier:HKQuantityTypeIdentifierHeight]; //身高
    HKQuantityType *weightType = [HKQuantityType quantityTypeForIdentifier:HKQuantityTypeIdentifierBodyMass]; //体重
    HKObjectType* rate = [HKObjectType quantityTypeForIdentifier:HKQuantityTypeIdentifierHeartRate];  //心率
    HKObjectType * restingHeartRateType = [HKObjectType quantityTypeForIdentifier:HKQuantityTypeIdentifierRestingHeartRate]; //静息心率
    return [NSSet setWithObjects:stepType, walkingRunningType, cycleType, heightType, weightType, bodyType, bodyFatType, energyBurned,rate,restingHeartRateType,workOut,nil];
}

- (NSSet *)dataReadTypes {
    HKQuantityType *stepType = [HKQuantityType quantityTypeForIdentifier:HKQuantityTypeIdentifierStepCount]; //步行
    HKQuantityType *walkingRunningType = [HKQuantityType quantityTypeForIdentifier:HKQuantityTypeIdentifierDistanceWalkingRunning]; //步行+跑步
    HKQuantityType *cycleType = [HKQuantityType quantityTypeForIdentifier:HKQuantityTypeIdentifierDistanceCycling]; //骑车距离
    HKQuantityType *heightType = [HKQuantityType quantityTypeForIdentifier:HKQuantityTypeIdentifierHeight]; //身高
    HKQuantityType *weightType = [HKQuantityType quantityTypeForIdentifier:HKQuantityTypeIdentifierBodyMass]; //体重
    HKQuantityType *bodyType = [HKQuantityType quantityTypeForIdentifier:HKQuantityTypeIdentifierBodyMassIndex]; //身高体重指数
    HKQuantityType *bodyFatType = [HKQuantityType quantityTypeForIdentifier:HKQuantityTypeIdentifierBodyFatPercentage]; //体脂率
    HKQuantityType *energyBurned = [HKQuantityType quantityTypeForIdentifier:HKQuantityTypeIdentifierActiveEnergyBurned]; //活动能量
    HKObjectType * restingHeartRateType = [HKObjectType quantityTypeForIdentifier:HKQuantityTypeIdentifierRestingHeartRate]; //静息心率
    HKObjectType *rate = [HKObjectType quantityTypeForIdentifier:HKQuantityTypeIdentifierHeartRate];  //心率
    
    HKObjectType *dateOfBirthType = [HKObjectType characteristicTypeForIdentifier:HKCharacteristicTypeIdentifierDateOfBirth];
    HKWorkoutType *workOut = [HKWorkoutType workoutType]; //体能训练
    return [NSSet setWithObjects:stepType, walkingRunningType, cycleType, heightType, weightType, bodyType, bodyFatType, energyBurned, restingHeartRateType,rate,dateOfBirthType,workOut,nil];
}

/// 是否支持苹果健康
+ (BOOL)isHealthDataAvailable {
    return [HKHealthStore isHealthDataAvailable];
}

/// 获取当前项目的权限
- (int)authorizationStatusForType:(HKObjectType *)type {
    HKAuthorizationStatus statu = [self.store authorizationStatusForType:type];
    switch (statu) {
        case HKAuthorizationStatusNotDetermined:
            return -1;
        case HKAuthorizationStatusSharingDenied:
            return 0;
        case HKAuthorizationStatusSharingAuthorized:
            return 1; //已授权
        default:
            return -1;
    }
}

- (BOOL)healthKitContent {
    if (![MRKHealthManager isHealthDataAvailable]) {
        return NO;
    }
//    HKWorkoutType *workOut = [HKWorkoutType workoutType]; //体能训练
//    int i = [self authorizationStatusForType:workOut];
//    return i > 0;
    // 苹果健康，全部授权，才算授权
    NSSet *writeDataTypes = [self dataWriteTypes];
    NSMutableArray *tmpArr = [NSMutableArray arrayWithArray:writeDataTypes.allObjects];
    for (HKObjectType *type in tmpArr) {
        int i = [self authorizationStatusForType:type];
        if (i < 1) {
            return NO;
        }
    }
    return YES;
}

/// 苹果健康从未授权过
- (BOOL)healthKitNoAutho {
    if (![MRKHealthManager isHealthDataAvailable]) {
        return YES;
    }
    HKWorkoutType *workOut = [HKWorkoutType workoutType]; //体能训练
    int i = [self authorizationStatusForType:workOut];
    return i == -1;
}

/// 苹果健康所有类型从未授权过
- (BOOL)healthKitAllNoAutho {
    if (![MRKHealthManager isHealthDataAvailable]) {
        return NO;
    }
    NSSet *writeDataTypes = [self dataReadTypes];
    NSMutableArray *tmpArr = [NSMutableArray arrayWithArray:writeDataTypes.allObjects];
    for (HKObjectType *type in tmpArr) {
        int i = [self authorizationStatusForType:type];
        if (i != -1) {
            return NO;
        }
    }
    return YES;
}

/// 请求系统权限
- (void)authorizateHealthKit:(void(^)(BOOL))resultBlock {
    if (![MRKHealthManager isHealthDataAvailable]) {
        resultBlock(NO);
        return;
    }
    NSSet *writeDataTypes = [self dataWriteTypes];
    NSSet *readDataTypes = [self dataReadTypes];
    @weakify(self);
    [self.store requestAuthorizationToShareTypes:writeDataTypes
                                       readTypes:readDataTypes
                                      completion:^(BOOL success, NSError * _Nullable error) {
        BOOL connect = [self_weak_ healthKitContent];
        resultBlock(connect);
    }];
}

/// 苹果健康心率是否连接
- (BOOL)healthKitHeartContent{
    if (![MRKHealthManager isHealthDataAvailable]) {
        MLog(@"healthKitHeartContent === disAvailable");
        return NO;
    }
    HKQuantityType *heartType = [HKQuantityType quantityTypeForIdentifier:HKQuantityTypeIdentifierHeartRate]; //心率
    int i = [self authorizationStatusForType:heartType];
    
    MLog(@"healthKitHeartContent === %d", i);
    return i > 0;
}

/// 苹果健康心率从未授权过
- (BOOL)healthKitHeartNoAutho{
    if (![MRKHealthManager isHealthDataAvailable]) {
        return YES;
    }
    
    HKQuantityType *heartType = [HKQuantityType quantityTypeForIdentifier:HKQuantityTypeIdentifierHeartRate]; //心率
    int i = [self authorizationStatusForType:heartType];
    return i == -1;
}

/// 请求系统权限后判断心率权限状态
- (void)heartAuthorizateHealthKit:(void(^)(BOOL))resultBlock {
    if (![MRKHealthManager isHealthDataAvailable]) {
        resultBlock(NO);
        return;
    }
    NSSet *writeDataTypes = [self dataWriteTypes];
    NSSet *readDataTypes = [self dataReadTypes];
    @weakify(self);
    [self.store requestAuthorizationToShareTypes:writeDataTypes
                                       readTypes:readDataTypes
                                      completion:^(BOOL success, NSError * _Nullable error) {
        BOOL connect = [self_weak_ healthKitHeartContent];
        resultBlock(connect);
    }];
}
///// 读写权限的集合
//- (NSSet *)writeDataTypes {
//    HKQuantityType *heartType = [HKQuantityType quantityTypeForIdentifier:HKQuantityTypeIdentifierHeartRate]; //心率
//    HKWorkoutType *workOut = [HKWorkoutType workoutType]; //体能训练
//    return [NSSet setWithObjects:heartType, workOut, nil];
//}
///// 读写权限的集合
//- (NSSet *)readDataTypes {
//    HKQuantityType *heartType = [HKQuantityType quantityTypeForIdentifier:HKQuantityTypeIdentifierHeartRate]; //心率
//    return [NSSet setWithObjects:heartType, nil];
//}








//当前日期的前一天 那天的数据
+ (NSPredicate *)predicateForSamplesToday:(NSInteger)before duration:(NSInteger)duration{
    // 过滤条件
    NSCalendar *calendar = [NSCalendar currentCalendar];
    NSDate *now = [[NSDate date] br_getNewDateToDays:before];
    NSDateComponents *components = [calendar components:NSCalendarUnitYear|NSCalendarUnitMonth|NSCalendarUnitDay fromDate:now];
    // 开始日期
    NSDate *startDate = [calendar dateFromComponents:components];
    // 结束日期
    NSDate *endDate = [calendar dateByAddingUnit:NSCalendarUnitDay value:duration toDate:startDate options:0];
    NSPredicate *predicate = [HKQuery predicateForSamplesWithStartDate:startDate endDate:endDate options:HKQueryOptionStrictStartDate];
    return predicate;
}

#pragma mark - --------- 苹果健康读数据 ---------
/// 读数据
- (void)readSampleHealthData:(HKQuantityType *)type unit:(HKUnit *)unit before:(NSInteger)before duration:(NSInteger)duration resault:(void(^)(double value))resalut {
    NSPredicate *p = [MRKHealthManager predicateForSamplesToday:before duration:duration];
    NSSortDescriptor *sort = [[NSSortDescriptor alloc] initWithKey:HKSampleSortIdentifierStartDate ascending:NO];
    HKSampleQuery *query = [[HKSampleQuery alloc] initWithSampleType:type predicate:p limit:HKObjectQueryNoLimit sortDescriptors:@[sort] resultsHandler:^(HKSampleQuery * _Nonnull query, NSArray<__kindof HKSample *> * _Nullable results, NSError * _Nullable error) {
        HKQuantitySample *sample = results.firstObject;
        if (resalut) {
            double value = [sample.quantity doubleValueForUnit:unit];
            resalut(value);
        }
    }];
    [self.store executeQuery:query];
}

/// 统计查询
- (void)readStaticHealthData:(HKQuantityType *)type unit:(HKUnit *)unit before:(NSInteger)before duration:(NSInteger)duration resault:(void(^)(double value))resalut  {
    NSPredicate *p = [MRKHealthManager predicateForSamplesToday:before duration:duration];
    //    NSSortDescriptor *sort = [[NSSortDescriptor alloc] initWithKey:HKSampleSortIdentifierStartDate ascending:NO];
    HKStatisticsQuery *query = [[HKStatisticsQuery alloc] initWithQuantityType:type quantitySamplePredicate:p options:HKStatisticsOptionCumulativeSum completionHandler:^(HKStatisticsQuery * _Nonnull query, HKStatistics * _Nullable result, NSError * _Nullable error) {
        HKQuantity *quantity = result.sumQuantity;
        double count = [quantity doubleValueForUnit:unit];
        resalut(count);
    }];
    [self.store executeQuery:query];
}

/// 体能训练查询
- (void)readWorkoutHealthData:(NSInteger)before duration:(NSInteger)duration resalut:(void(^)(double v))resalut {
    NSPredicate *p = [MRKHealthManager predicateForSamplesToday:before duration:duration];
    NSSortDescriptor *sort = [[NSSortDescriptor alloc] initWithKey:HKSampleSortIdentifierStartDate ascending:NO];
    HKWorkoutType *type = [HKWorkoutType workoutType];
    HKSampleQuery *query = [[HKSampleQuery alloc] initWithSampleType:type predicate:p limit:HKObjectQueryNoLimit sortDescriptors:@[sort] resultsHandler:^(HKSampleQuery * _Nonnull query, NSArray<__kindof HKSample *> * _Nullable results, NSError * _Nullable error) {
        double count = 0;
        for (HKWorkout *work in results) {
            count += work.duration;
        }
        resalut(count);
    }];
    [self.store executeQuery:query];
}

#pragma mark - --------- 苹果健康写数据 ---------
/// 写数据
- (HKSample *)writeSampleHealthData:(HKQuantityType *)type quantity:(HKQuantity *)quantity trainId:(NSString *)trainId start:(NSDate *)start end:(NSDate *)end{
    NSDictionary *metadata = @{@"训练记录":trainId};
    HKQuantitySample *sample = [HKQuantitySample quantitySampleWithType:type quantity:quantity startDate:start endDate:end metadata:metadata];
    return sample;
}

/// 写体能训练
- (HKSample *)writeWorkoutHealthData:(HKWorkoutActivityType)type
                            duration:(double)duration
                        energyBurned:(double)kcal
                            distance:(double)dis
                             trainId:(NSString *)trainId start:(NSDate *)start end:(NSDate *)end {
    HKQuantity *energBurned = [HKQuantity quantityWithUnit:[HKUnit kilocalorieUnit] doubleValue:kcal];
    HKQuantity *distance = [HKQuantity quantityWithUnit:[HKUnit meterUnitWithMetricPrefix:HKMetricPrefixKilo] doubleValue:dis];
    NSDictionary *metadata = @{@"训练记录":trainId};
    HKWorkout *workout = [HKWorkout workoutWithActivityType:type startDate:start endDate:end duration:duration totalEnergyBurned:energBurned totalDistance:distance metadata:metadata];
    return workout;
}

#pragma mark - 读取健康数据
- (void)readStep:(NSInteger)before duration:(NSInteger)duration results:(void(^)(NSString *count))results {
    HKQuantityType *type = [HKQuantityType quantityTypeForIdentifier:HKQuantityTypeIdentifierStepCount];
    [self readStaticHealthData:type unit:[HKUnit countUnit] before:before duration:duration resault:^(double value) {
        results([NSString stringWithFormat:@"%.0f",value]);
    }];
}
- (void)readDistanceWalkingRunning:(NSInteger)before duration:(NSInteger)duration results:(void(^)(NSString *count))results {
    HKQuantityType *type = [HKQuantityType quantityTypeForIdentifier:HKQuantityTypeIdentifierDistanceWalkingRunning];
    [self readStaticHealthData:type unit:[HKUnit meterUnit] before:before duration:duration resault:^(double value) {
        results([NSString stringWithFormat:@"%.0f",value]);
    }];
}
- (void)readEnergyBurned:(NSInteger)before duration:(NSInteger)duration results:(void(^)(NSString *count))results {
    HKQuantityType *type = [HKQuantityType quantityTypeForIdentifier:HKQuantityTypeIdentifierActiveEnergyBurned];
    [self readStaticHealthData:type unit:[HKUnit kilocalorieUnit] before:before duration:duration resault:^(double value) {
        results([NSString stringWithFormat:@"%.2f",value]);
    }];
}
- (void)readDistanceCycling:(NSInteger)before duration:(NSInteger)duration results:(void(^)(NSString *count))results {
    HKQuantityType *type = [HKQuantityType quantityTypeForIdentifier:HKQuantityTypeIdentifierDistanceCycling];
    [self readStaticHealthData:type unit:[HKUnit meterUnit] before:before duration:duration resault:^(double value) {
        results([NSString stringWithFormat:@"%.0f",value]);
    }];
}


- (void)readHeight:(NSInteger)before duration:(NSInteger)duration results:(void(^)(NSString *count))results {
    HKQuantityType *type = [HKQuantityType quantityTypeForIdentifier:HKQuantityTypeIdentifierHeight];
    //[HKUnit meterUnitWithMetricPrefix:HKMetricPrefixCenti] cm
    //    [self readStaticHealthData:type unit:[HKUnit meterUnit] resault:^(double value) {
    //        results([NSString stringWithFormat:@"%.2f",value]);
    //    }];
    [self readSampleHealthData:type unit:[HKUnit meterUnitWithMetricPrefix:HKMetricPrefixCenti] before:before duration:duration resault:^(double value) {
        results([NSString stringWithFormat:@"%.0f",value]);
    }];
}

- (void)readWeight:(NSInteger)before duration:(NSInteger)duration results:(void(^)(NSString *count))results {
    HKQuantityType *type = [HKQuantityType quantityTypeForIdentifier:HKQuantityTypeIdentifierBodyMass];
    [self readSampleHealthData:type unit:[HKUnit gramUnitWithMetricPrefix:HKMetricPrefixKilo] before:before duration:duration resault:^(double value) {
        results([NSString stringWithFormat:@"%.1f",value]);
    }];
}

- (void)readRate:(NSInteger)before duration:(NSInteger)duration results:(void(^)(NSString *count))results {
    HKQuantityType *type = [HKQuantityType quantityTypeForIdentifier:HKQuantityTypeIdentifierHeartRate];
    HKUnit *heartRateUnit = [[HKUnit countUnit] unitDividedByUnit:[HKUnit minuteUnit]];
    [self readSampleHealthData:type unit:heartRateUnit before:before duration:duration resault:^(double value) {
        results([NSString stringWithFormat:@"%.0f",value]);
    }];
}
- (void)readRestingRate:(NSInteger)before duration:(NSInteger)duration results:(void(^)(NSString *count))results {
    HKQuantityType *type = [HKQuantityType quantityTypeForIdentifier:HKQuantityTypeIdentifierRestingHeartRate];
    HKUnit *heartRateUnit = [[HKUnit countUnit] unitDividedByUnit:[HKUnit minuteUnit]];
    [self readSampleHealthData:type unit:heartRateUnit before:before duration:duration resault:^(double value) {
        results([NSString stringWithFormat:@"%.0f",value]);
    }];
}

- (void)readBodyFat:(NSInteger)before duration:(NSInteger)duration results:(void(^)(NSString *count))results {
    HKQuantityType *type = [HKQuantityType quantityTypeForIdentifier:HKQuantityTypeIdentifierBodyFatPercentage];
    [self readSampleHealthData:type unit:[HKUnit percentUnit] before:before duration:duration resault:^(double value) {
        results([NSString stringWithFormat:@"%.1f",value * 100]);
    }];
}

- (void)readBodyMassIndex:(NSInteger)before duration:(NSInteger)duration results:(void(^)(NSString *count))results {
    HKQuantityType *type = [HKQuantityType quantityTypeForIdentifier:HKQuantityTypeIdentifierBodyMassIndex];
    [self readSampleHealthData:type unit:[HKUnit countUnit] before:before duration:duration resault:^(double value) {
        results([NSString stringWithFormat:@"%.1f",value]);
    }];
}





- (void)readSportData:(NSInteger)before duration:(NSInteger)duration success:(void(^)(MRKHealthKitReadModel *model))success {
    
    // 每次都刷新的接口
    @weakify(self);
    RACSignal *stepAction = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self readStep:before duration:duration results:^(NSString *count) {
            [subscriber sendNext:count];
        }];
        return nil;
    }];
    RACSignal *distanceAction = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self readDistanceWalkingRunning:before duration:duration results:^(NSString *count) {
            [subscriber sendNext:count];
        }];
        return nil;
    }];
    RACSignal *energyBurnedAction = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self readEnergyBurned:before duration:duration results:^(NSString *count) {
            [subscriber sendNext:count];
        }];
        return nil;
    }];
    RACSignal *cyclingAction = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self readDistanceCycling:before duration:duration results:^(NSString *count) {
            [subscriber sendNext:count];
        }];
        return nil;
    }];
    RACSignal *heightAction = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self readHeight:before duration:duration results:^(NSString *count) {
            [subscriber sendNext:count];
        }];
        return nil;
    }];
    
    RACSignal *weightAction = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self readWeight:before duration:duration results:^(NSString *count) {
            [subscriber sendNext:count];
        }];
        return nil;
    }];
    
    RACSignal *rateAction = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self readRate:before duration:duration results:^(NSString *count) {
            [subscriber sendNext:count];
        }];
        return nil;
    }];
    
    RACSignal *restingRateAction = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self readRestingRate:before duration:duration results:^(NSString *count) {
            [subscriber sendNext:count];
        }];
        return nil;
    }];
    RACSignal *readBodyFatActioin = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self readBodyFat:before duration:duration results:^(NSString *count) {
            [subscriber sendNext:count];
        }];
        return nil;
    }];
    RACSignal *bodyMassIndexAction = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self readBodyMassIndex:before duration:duration results:^(NSString *count) {
            [subscriber sendNext:count];
        }];
        return nil;
    }];
    
    RACSignal *workoutAction = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self readWorkoutHealthData:before duration:duration resalut:^(double v) {
            [subscriber sendNext:[NSString stringWithFormat:@"%.0f",v]];
        }];
        return nil;
    }];
    
    NSDate *birth = [self readBirth];
    NSString *birthString = [MRKTimeManager getDateTimeString:birth];
    
    [[RACSignal combineLatest:@[stepAction, distanceAction, energyBurnedAction, cyclingAction, heightAction, weightAction, rateAction,restingRateAction,readBodyFatActioin,bodyMassIndexAction,workoutAction]
                       reduce:^id(NSString *step, NSString *distance, NSString *energy, NSString *cycling, NSString *height,NSString *weight, NSString *rate,NSString *restingRate,NSString *bodyFat,NSString *bodyMassIndex, NSString *workout){
        NSLog(@"readSportData %@------------%@ ----%@----weight%@----restingRate%@ rate%@ bodyFat%@ bodyMassIndex%@", step, distance, workout, weight, restingRate,rate , bodyFat , bodyMassIndex);
        NSDictionary *dic = @{
            @"footNum":step,
            @"runDistance":distance,
            @"kcal":energy,
            @"ridingDistance":cycling,
            @"height":height,
            @"takeTime":workout,
            @"weight":weight,
            @"heartRate":rate,
            @"restRate":restingRate,
            @"birthday":birthString ?: @"",
            @"bodyFatRate":bodyFat,
            @"bmi":bodyMassIndex,
        };
        MRKHealthKitReadModel *model = [MRKHealthKitReadModel modelWithDictionary:dic];
        success(model);
        return @(step.length && step.length);
    }] subscribeNext:^(id x) {}];
    
}

- (NSDate *)readBirth {
    NSError *birtherror;
    NSDateComponents *birth = [self.store dateOfBirthComponentsWithError:&birtherror];
    return birth.date;
}

- (NSInteger)readSex {
    NSError *sexerror;
    HKBiologicalSexObject *objc = [self.store biologicalSexWithError:&sexerror];
    if (!objc) {
        return 0;
    }
    return objc.biologicalSex;
}

#pragma mark - 写入健康数据
// 步数+跑步距离
- (HKSample *)saveDistanceWalking:(double)count trainId:(NSString *)trainId start:(NSDate *)start end:(NSDate *)end {
    HKQuantityType *type = [HKQuantityType quantityTypeForIdentifier:HKQuantityTypeIdentifierDistanceWalkingRunning];
    HKQuantity *quantity = [HKQuantity quantityWithUnit:[HKUnit meterUnitWithMetricPrefix:HKMetricPrefixKilo] doubleValue:count];  //单位km
    return [self writeSampleHealthData:type quantity:quantity trainId:trainId start:start end:end];
}
// 活动能量
- (HKSample *)saveEnergyBurned:(double)count trainId:(NSString *)trainId start:(NSDate *)start end:(NSDate *)end {
    HKQuantityType *type = [HKQuantityType quantityTypeForIdentifier:HKQuantityTypeIdentifierActiveEnergyBurned];
    HKQuantity *quantity = [HKQuantity quantityWithUnit:[HKUnit kilocalorieUnit] doubleValue:count];  //单位 kcal
    return [self writeSampleHealthData:type quantity:quantity trainId:trainId start:start end:end];
}
//骑车距离
- (HKSample *)saveDistanceCycling:(double)count trainId:(NSString *)trainId start:(NSDate *)start end:(NSDate *)end {
    HKQuantityType *type = [HKQuantityType quantityTypeForIdentifier:HKQuantityTypeIdentifierDistanceCycling];
    HKQuantity *quantity = [HKQuantity quantityWithUnit:[HKUnit meterUnitWithMetricPrefix:HKMetricPrefixKilo] doubleValue:count];  //单位m
    return [self writeSampleHealthData:type quantity:quantity trainId:trainId start:start end:end];
}
//体能训练
- (HKSample *)saveWorkout:(int)type distance:(double)distance time:(double)time kcal:(double)kcal trainId:(NSString *)trainId start:(NSDate *)start end:(NSDate *)end {
    //HKWorkoutActivityTypeRowing 划船
    //HKWorkoutActivityTypeCycling 自行车
    //HKWorkoutActivityTypeElliptical 椭圆机
    //HKWorkoutActivityTypeRunning 跑步end
    switch (type) {
        case BicycleEquipment: //单车
            return [self writeWorkoutHealthData:HKWorkoutActivityTypeCycling duration:time energyBurned:kcal distance:distance trainId:trainId start:start end:end];
        case TreadmillEquipment:
            return [self writeWorkoutHealthData:HKWorkoutActivityTypeRunning duration:time energyBurned:kcal distance:distance trainId:trainId start:start end:end];
        case TuoYuanEquipment:
            return [self writeWorkoutHealthData:HKWorkoutActivityTypeElliptical duration:time energyBurned:kcal distance:distance trainId:trainId start:start end:end];
        case BoatEquipment:
            return [self writeWorkoutHealthData:HKWorkoutActivityTypeRowing duration:time energyBurned:kcal distance:distance trainId:trainId start:start end:end];
    }
    return nil;
}

- (void)saveSportJsonData:(id)data {
    MLog(@"写入苹果健康数据========手动写入");
    MRKHealthKitWriteModel *model = [MRKHealthKitWriteModel modelWithJSON:data];
    [self saveSportData:model];
}

- (void)saveSportData:(MRKHealthKitWriteModel *)data {
    MLog(@"写入苹果健康数据========%@", data.modelToJSONString);
    
    // 划船机 消耗
    // 椭圆机 消耗
    // 跑步机 跑步距离 + 消耗
    // 单车。骑行距离 + 消耗
    // all 体能训练
    
    // distance 四舍五入 km
    // kcal 四舍五入 一位小数
    double distanceKm = [NSString stringWithFormat:@"%.2f" , [data.distance floatValue]/1000.0].doubleValue;
    double kcalK = [NSString stringWithFormat:@"%.1f", [data.kcal floatValue]].doubleValue;
    
    NSMutableArray *objects = [NSMutableArray array];
    int equiment = data.equipmentId.intValue;
    if (equiment == BicycleEquipment || equiment == TreadmillEquipment || equiment == TuoYuanEquipment ||  equiment == BoatEquipment) {
        // 体能训练
        HKSample *workout = [self saveWorkout:data.equipmentId.intValue distance:distanceKm time:data.takeTime.intValue kcal:kcalK trainId:data.trainId start:data.beginDate end:data.endDate];
        if (workout) {
            [objects addObject:workout];
        }
        // 消耗
        HKSample *kcal = [self saveEnergyBurned:kcalK trainId:data.trainId start:data.beginDate end:data.endDate];
        [objects addObject:kcal];
    }
    if (equiment == BicycleEquipment) {
        HKSample *cycling = [self saveDistanceCycling:distanceKm trainId:data.trainId start:data.beginDate end:data.endDate]; //骑车距离
        [objects addObject:cycling];
    }
    if (equiment == TreadmillEquipment) {
        HKSample *distance = [self saveDistanceWalking:distanceKm trainId:data.trainId start:data.beginDate end:data.endDate]; //步数+跑步距离
        [objects addObject:distance];
    }
    
    [self.store saveObjects:objects withCompletion:^(BOOL success, NSError * _Nullable error) {
        NSLog(@"%d",success);
        if (success) {
            [MRKHealthViewModel requestDataTag:data.trainId success:^(BOOL success) {}];
            MLog(@"写入苹果健康数据========写入成功");
        }else{
            MLog(@"写入苹果健康数据========写入失败");
        }
    }];
}

@end







/**
 distance = 0;
 duration = 155;
 isSuccess = 1;
 kcal = "5.466666691005228";
 productId = 32;
 trainingRecordId = 1952488222720709;
 userId = 1415291775388712961;
 */

/**
 trainId;     //训练ID
 equipmentId; //训练类型（设备大类ID)
 takeTime;    //训练时间 s
 distance;    //距离(m)
 kcal;        //消耗（kcal）
 createTime;  //创建时间
 beginDate;   //开始时间
 endDate;     //结束时间
 */

@implementation MRKHealthKitWriteModel

+ (NSDictionary *)modelCustomPropertyMapper {
    return @{
        @"trainId" : @[@"trainingRecordId", @"trainId"],
        @"takeTime" : @[@"takeTime", @"duration"],
        @"equipmentId" : @[@"productId", @"equipmentId"],
    };
}

- (BOOL)modelCustomTransformFromDictionary:(NSDictionary *)dic {
    
    NSDate *end = [NSDate date];
    int time = _takeTime.intValue;
    _beginDate = [end dateByAddingTimeInterval:-time];
    _endDate = end;
    
    
//    // 计算这条运动的开始和结束时间
//    if ([_createTime isNotBlank]) {
//        NSDate *end = [MRKTimeManager dateFromDateString:_createTime formate:@"yyyy-MM-dd HH:mm"];
//        int time = _takeTime.intValue;
//        _beginDate = [end dateByAddingTimeInterval:-time];
//        _endDate = end;
//    }else {
//        _beginDate = _endDate = [NSDate date];
//    }
    return YES;
}

@end

@implementation MRKHealthKitReadModel

@end
