//
//  MRKHealthManager.h
//  Student_IOS
//
//  Created by merit on 2023/5/31.
//

#import <Foundation/Foundation.h>
#import <HealthKit/HealthKit.h>
#define AppLaunch_LastDate     @"AppLaunch_LastDate"  //app每天启动的时间


@class MRKHealthKitWriteModel;
@class MRKHealthKitReadModel;
NS_ASSUME_NONNULL_BEGIN

@interface MRKHealthManager : NSObject

+ (instancetype)shared;

/// 是否支持苹果健康
+ (BOOL)isHealthDataAvailable;
/// 获取当前项目的权限
- (int)authorizationStatusForType:(HKObjectType *)type;

///=========================================================================================
///=========================================================================================
///=========================================================================================
///
/// 苹果健康是否连接
- (BOOL)healthKitContent;
/// 苹果健康从未授权过
- (BOOL)healthKitNoAutho;
/// 苹果健康所有类型从未授权过 (如果不支持苹果健康，为no)
- (BOOL)healthKitAllNoAutho;
/// 请求系统权限
- (void)authorizateHealthKit:(void(^)(BOOL))resultBlock;

///=========================================================================================
///=========================4.3.0.0Watch版本权限以心率读写权限为阀值===================================
///=========================================================================================
///
/// 苹果健康心率是否连接
- (BOOL)healthKitHeartContent;
/// 苹果健康心率从未授权过
- (BOOL)healthKitHeartNoAutho;
/// 请求系统权限后判断心率权限状态
- (void)heartAuthorizateHealthKit:(void(^)(BOOL))resultBlock;


/// 保存运动数据
- (void)saveSportData:(MRKHealthKitWriteModel *)data;

/// 读取运动数据
/// - Parameters:
///   - before: 当前时间，的几天前
///   - success:
///   - duration: 读几天数据
- (void)readSportData:(NSInteger)before duration:(NSInteger)duration success:(void(^)(MRKHealthKitReadModel *model))success;


/// 上传运动健康数据
+ (void)uploadHealthData;


/// 写入运动记录到苹果健康
/// - Parameter trainID: 运动ID
+ (void)writeSportData:(NSString *)trainID;

/// 写入身高体重
/// - Parameters:
///   - height: 身高 cm
///   - weight: 体重 kg
+ (void)writeHeight:(NSString *)height weight:(NSString *)weight;



/// 健康写入
- (void)saveSportJsonData:(id)data;
- (void)saveSportData:(MRKHealthKitWriteModel *)data;
@end


/*
 —>给第三方数据
 步数+跑步距离=跑步机的距离
 活动能量=消耗卡路里
 骑车距离=单车的运动距离
 身高=身高
 体重=体重
 体能训练=椭圆机+划船机+单车+跑步机
 */
@interface MRKHealthKitWriteModel : MRKBaseModel
@property (nonatomic, copy) NSString *trainId;     //训练ID
@property (nonatomic, copy) NSString *equipmentId; //训练类型（设备大类ID)
@property (nonatomic, copy) NSString *takeTime;    //训练时间 s
@property (nonatomic, copy) NSString *distance;    //距离(m)
@property (nonatomic, copy) NSString *kcal;        //消耗（kcal）
@property (nonatomic, copy) NSString *createTime;  //创建时间
@property (nonatomic, strong) NSDate *beginDate;   //开始时间
@property (nonatomic, strong) NSDate *endDate;     //结束时间
@end



@interface MRKHealthKitReadModel : MRKBaseModel
@property (nonatomic, copy) NSString *footNum; //步数
@property (nonatomic, copy) NSString *runDistance;  //步行+跑步 m
@property (nonatomic, copy) NSString *kcal; //活动能量 kcal
@property (nonatomic, copy) NSString *ridingDistance; //骑车距离 m
@property (nonatomic, copy) NSString *height;  //身高
@property (nonatomic, copy) NSString *weight;  //体重
@property (nonatomic, copy) NSString *birthday; //生日yyyy-MM-dd
@property (nonatomic, copy) NSString *takeTime; //体能训练 (训练时长 s)
@property (nonatomic, copy) NSString *date;

@property (nonatomic, copy) NSString *restRate;    //静息心率
@property (nonatomic, copy) NSString *heartRate;    //心率
@property (nonatomic, copy) NSString *bodyFatRate;  //体脂率
@property (nonatomic, copy) NSString *bmi; //身高体重指数

@end
NS_ASSUME_NONNULL_END
