//
//  MRKLandPathView.m
//  Student_IOS
//
//  Created by Junq on 2025/1/13.
//

#import "MRKLandPathView.h"
#import <CoreGraphics/CoreGraphics.h>
#import "UIBezierPath+BezierPoint.h"
#import "POP.h"



@interface MRKLandPathView ()
@property (nonatomic, strong, nullable) MRKLandPathModel *landModel;
@property (nonatomic, strong) MRKUserIndicateView *movingPointerView;
@property (nonatomic, strong) UIImageView *startView;

@property (nonatomic, strong) UIView *movingView;
@property (nonatomic, strong) UIBezierPath *landPath;

@property (nonatomic, strong) CAShapeLayer *pathLayer;
@property (nonatomic, strong) CAShapeLayer *lineLayer;
@property (nonatomic, strong) CAShapeLayer *shadowLayer;

@property (nonatomic, assign) BOOL dataHasReload;
@property (nonatomic, strong) NSMutableArray *landUserArray;

@property (nonatomic, copy) NSString *totalDistance;
@property (nonatomic, assign) CGFloat pathLength;
@end


@implementation MRKLandPathView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        self.landUserArray = [NSMutableArray array];
        [self addPartView];
        
        /**
         [RACScheduler scheduler] 异步线程，不会对主线程造成堵塞，异步执行
         [RACScheduler currentScheduler]  当前线程
         加takeUntil条件限制一下,否则当控制器pop后依旧会执行
         */
        @weakify(self);
        RACSignal *signal = [RACSignal interval:1.0 onScheduler:[RACScheduler currentScheduler]];
        [[signal takeUntil:self.rac_willDeallocSignal] subscribeNext:^(id x) {
            @strongify(self);
            
            if (self.dataHasReload){
                [self updateLandUserData];
            }
        }];
    }
    return self;
}

///
- (void)updateLandUserData{
    if (self.landUserArray.count == 0) return;
    
    /// 更新模拟用户运动距离
    for (MapLandUserModel *object in self.landUserArray) {
        double distance = (object.totalDistance.doubleValue *1000 + self.preSecondLength)/1000;
        object.totalDistance = @(round(distance * 1000)/1000.0);
    }
}

///每秒随机距离 （米）
- (NSInteger)preSecondLength{
    NSInteger random = 0;
    NSInteger type = self.productId.integerValue;
    switch (type) {
        case TreadmillEquipment:
            random = arc4random_uniform(10);
            break;
        case BicycleEquipment:
            random = arc4random_uniform(18);
            break;
        case BoatEquipment:
            random = arc4random_uniform(28);
            break;
        case EllipticalEquipment:
            random = arc4random_uniform(18);
            break;
        default:
            random = arc4random_uniform(10);
            break;
    }
    return random;
}

- (void)addPartView{
    self.landPath = nil;
    /// 创建赛道
    [self.layer addSublayer:self.pathLayer];
    /// 创建虚线
    [self.layer addSublayer:self.lineLayer];
    
    
//    /// 创建阴影图层
//    self.shadowLayer.path = path.CGPath;
//    [self.layer addSublayer:self.shadowLayer];
//    [self.layer insertSublayer:self.shadowLayer above:self.pathLayer];
    
    // 计算法线方向（切线方向的垂直方向）
    // 旋转 90 度得到法线方向
    // CGFloat normalAngle = tangentAngle + M_PI_2;
    self.startView.size = CGSizeMake(10, 24);
    [self addSubview:self.startView];
    

    self.movingPointerView.frame = CGRectMake(0, 0, 30, 41);
    self.movingPointerView.layer.anchorPoint = CGPointMake(0.5, 1); /// 修改 anchorPoint
    [self addSubview:self.movingPointerView];
    [self bringSubviewToFront:self.movingPointerView];
}

- (void)updateUIdata:(TrainingShowData *)data{
    if (self.landModel.length.doubleValue <= 0){
        return;
    }
    
    double start = self.totalDistance.doubleValue;
    double end = data.totalDistance.doubleValue;
    if (start >= end){
        return;
    }
    
    double roadLength = self.landModel.length.doubleValue;
    float progress = fmod(end/roadLength, 1.0);
    
    CGPoint endPoint = [self.landPath pointAtProgress:progress with:self.pathLength];
    NSLog(@"endPoint CGPoint: (%.2f, %.2f)", endPoint.x, endPoint.y);
    
    if (CGPointEqualToPoint(self.movingPointerView.layer.position, CGPointZero)) {
        self.movingPointerView.layer.position = endPoint;
    }else{
        @weakify(self);
        POPBasicAnimation *positionAnimation = [POPBasicAnimation animationWithPropertyNamed:kPOPLayerPosition];
        positionAnimation.fromValue = [NSValue valueWithCGPoint:self.movingPointerView.layer.position];
        positionAnimation.toValue = [NSValue valueWithCGPoint:endPoint];
        positionAnimation.duration = 0.3;
        positionAnimation.timingFunction = [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionLinear];
        positionAnimation.removedOnCompletion = YES;
        positionAnimation.beginTime = CACurrentMediaTime();
        positionAnimation.completionBlock = ^(POPAnimation *anim, BOOL finished) {
            @strongify(self);
            self.movingPointerView.layer.position = endPoint;
        };
        [self.movingPointerView.layer pop_addAnimation:positionAnimation forKey:@"positionAnimation"];
    }

    self.totalDistance = data.totalDistance;
}


///新用户添加运动
- (void)userJoinTraining:(MapLandUserModel *)model{
    if (self.landUserArray.count >= 30) return;
    
    ///屏蔽掉自己加入
    if ([model.userId isNotBlank] && [model.userId isEqualToString:UserInfo.userId]){
        return;
    }
    model.totalDistance = @(0); ///从头开始运动
    [self.landUserArray addObject:model];
    
    /// 添加模拟运动小白点
    MRKLandIndicateView *view = [[MRKLandIndicateView alloc] init];
    view.size = CGSizeMake(10, 10);
    view.userModel = model;
    view.pathLength = self.pathLength;
    view.landPath = self.landPath;
    view.model = self.landModel;
    [self addSubview:view];
    [view startAnimationPoint];
}


///
- (void)reloadLandPathModel:(MRKLandPathModel *)model{
    if (model == nil) return;
    _landModel = model;
    
    [self.landPath removeAllPoints];
    if (model.coordinates.count > 0)
    {
        ///添加起始点
        MapCoordinateModel *coordinates = [model.coordinates firstObject];
        [self.landPath moveToPoint:CGPointMake(coordinates.x.doubleValue, coordinates.y.doubleValue)];
        
        NSMutableArray *location = model.coordinates.mutableCopy;
        [location removeFirstObject];///移除第一个
        
        NSMutableArray<MRKLandPoint *> *groups = [NSMutableArray array];
        /// 按 3 个点一组分组
        for (NSInteger i = 0; i < location.count; i += 3)
        {
            if (i + 2 < location.count)
            {
                MRKLandPoint *m = [[MRKLandPoint alloc] init];
                MapCoordinateModel *coordinates1 = location[i];
                m.controlPoint1 = CGPointMake(coordinates1.x.doubleValue, coordinates1.y.doubleValue);
                MapCoordinateModel *coordinates2 = location[i+1];
                m.controlPoint2 = CGPointMake(coordinates2.x.doubleValue, coordinates2.y.doubleValue);
                MapCoordinateModel *coordinates = location[i+2];
                m.endPoint = CGPointMake(coordinates.x.doubleValue, coordinates.y.doubleValue);
                [groups addObject:m];
            }
        }
        
        /// 绘制3阶曲线
        for (MRKLandPoint *point in groups)
        {
            [self.landPath addCurveToPoint:point.endPoint controlPoint1:point.controlPoint1 controlPoint2:point.controlPoint2];
        }
        
        ///是否闭合路径
        if (model.isClosedLoop && !self.landPath.isEmpty)
        {
            [self.landPath closePath];
        }
        
        ///
        if (!self.landPath.isEmpty)
        {
            UIBezierPath *path = self.landPath;
            
            // 计算路径的总长度
            CGFloat pathLength = [path bezierPathLength];
            self.pathLength = pathLength;
            
            /// 创建赛道
            self.pathLayer.path = path.CGPath;
            self.pathLayer.lineWidth = model.lineWidth.intValue;
            self.lineLayer.path = path.CGPath;
            
            // 获取路径的起始点和切线方向
            CGPoint startPoint = [path startPoint];
            CGFloat tangentAngle = [path tangentAngleAtStart];
            
            self.startView.center = startPoint; /// 设置起始位置
            self.startView.transform = CGAffineTransformMakeRotation(tangentAngle); /// 设置方向
            ///
            ///调整 position ,startPoint 是贝塞尔曲线的起点
            self.movingPointerView.layer.position = startPoint;
        }
    }
    
    //scale the path up to a reasonable size for display
//      [path applyTransform:CGAffineTransformMakeScale(200, 200)];
    
    ///刷新location Pointer
    [self reloadPointer:model];
    
    
    if (!self.landPath.isEmpty)
    {
        ///添加移动白点
        [self reloadMovingPointer:model];
    }
}

- (void)reloadPointer:(MRKLandPathModel *)model{
    ///
    for (UIView *view in self.subviews) {
        if ([view isKindOfClass:[MRKLandPointerView class]]){
            [view removeFromSuperview];
        }
    }

    ///添加地标
    for (MapLandmarkModel *mod in model.landmarks) {
        MRKLandPointerView *view = [[MRKLandPointerView alloc] init];
        view.model = mod;
        [self addSubview:view];
        [view mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.mas_top).offset(mod.coordinate.y.doubleValue-WKDHPX(24)/2);
            make.left.mas_equalTo(self.mas_left).offset(mod.coordinate.x.doubleValue-WKDHPX(24)/2);
        }];
    }
}


- (void)reloadMovingPointer:(MRKLandPathModel *)model{
    ///
    [self.landUserArray removeAllObjects];
    for (UIView *view in self.subviews) {
        if ([view isKindOfClass:[MRKLandIndicateView class]]){
            [view removeFromSuperview];
        }
    }
    
    ///创建模拟运动用户，后续更新MapLandUserModel的totalDistance来模拟运动
    NSInteger count = MIN(model.entryCount.intValue, 30);
    for (NSInteger i = 0; i < count; i++)
    {
        MapLandUserModel *object = [[MapLandUserModel alloc] init];
        object.userId = [NSString stringWithFormat:@"%ld",i];
        double randomValue = ((double)arc4random()/UINT32_MAX) * model.length.doubleValue;
        object.totalDistance = @(round(randomValue * 1000)/1000.0);
        [self.landUserArray addObject:object];
    }
    
    /// 添加模拟运动小白点
    for (MapLandUserModel *object in self.landUserArray) {
        MRKLandIndicateView *view = [[MRKLandIndicateView alloc] init];
        view.size = CGSizeMake(10, 10);
        view.userModel = object;
        view.pathLength = self.pathLength;
        view.landPath = self.landPath;
        view.model = model;
        [self addSubview:view];
        
        [view startAnimationPoint];
    }
    
    /// 启动数据模拟
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self bringSubviewToFront:self.movingPointerView];
        self.dataHasReload = YES;
    });
}



- (UIBezierPath *)landPath{
    if (!_landPath){
        _landPath = [UIBezierPath bezierPath];
    }
    return _landPath;
}

- (CAShapeLayer *)pathLayer{
    if (!_pathLayer){
        CAShapeLayer *pathLayer = [CAShapeLayer layer];
        pathLayer.strokeColor = UIColorHex(#323946).CGColor;
        pathLayer.fillColor = [UIColor clearColor].CGColor;
        pathLayer.lineWidth = 10.0;///默认
        pathLayer.lineCap = kCALineCapRound;
        pathLayer.lineJoin = kCALineCapRound;
        _pathLayer = pathLayer;
    }
    return _pathLayer;
}

- (CAShapeLayer *)lineLayer{
    if (!_lineLayer){
        CAShapeLayer *lineLayer = [CAShapeLayer layer];
        lineLayer.strokeColor = UIColorHexAlpha(#FFFFFF, 0.1).CGColor;
        lineLayer.fillColor = [UIColor clearColor].CGColor;
        lineLayer.lineWidth = 1.0;
        lineLayer.lineDashPattern = @[@8, @8];
        _lineLayer = lineLayer;
    }
    return _lineLayer;
}

- (CAShapeLayer *)shadowLayer{
    if (!_shadowLayer){
        CAShapeLayer *shadowLayer = [CAShapeLayer layer];
        shadowLayer.strokeColor = [UIColor clearColor].CGColor; // 阴影不影响主路径颜色
        shadowLayer.fillColor = [UIColor clearColor].CGColor;
        shadowLayer.lineWidth = 15.0; // 阴影线宽与路径线宽一致
        shadowLayer.shadowColor = [UIColor blackColor].CGColor; // 阴影颜色
        shadowLayer.shadowOffset = CGSizeMake(0, 0); // 阴影无偏移
        shadowLayer.shadowRadius = 5.0; // 阴影模糊半径
        shadowLayer.shadowOpacity = 0.5; // 阴影透明度
        _shadowLayer = shadowLayer;
    }
    return _shadowLayer;
}

- (MRKUserIndicateView *)movingPointerView{
    if (!_movingPointerView){
        _movingPointerView = [[MRKUserIndicateView alloc] init];
    }
    return _movingPointerView;
}

- (UIImageView *)startView{
    if (!_startView) {
        _startView = [[UIImageView alloc] init];
        _startView.image = [UIImage imageNamed:@"landPath_start"];
        _startView.contentMode = UIViewContentModeScaleToFill;
    }
    return _startView;
}

/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/

@end










@implementation MRKLandPoint
@end






@interface MRKLandPointerView ()
@property (nonatomic, strong) UIImageView *pointImageView;
@property (nonatomic, strong) UILabel *pointNameLab;
@end

@implementation MRKLandPointerView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        
        [self addSubview:self.pointImageView];
        [self addSubview:self.pointNameLab];
    }
    return self;
}

- (void)layoutSubviews {
    [super layoutSubviews];
    
    [self.pointImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.bottom.mas_equalTo(self);
        make.size.mas_equalTo(CGSizeMake(WKDHPX(24), WKDHPX(24)));
    }];
    
    [self.pointNameLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_equalTo(self.mas_centerY);
        make.left.mas_equalTo(self.pointImageView.mas_right).offset(4);
        make.right.mas_equalTo(self.mas_right);
    }];
}

- (UIImageView *)pointImageView{
    if (!_pointImageView) {
        _pointImageView = [[UIImageView alloc] init];
        _pointImageView.contentMode = UIViewContentModeScaleAspectFit;
    }
    return _pointImageView;
}

- (UILabel *)pointNameLab {
    if (!_pointNameLab) {
        _pointNameLab = [[UILabel alloc] init];
        [_pointNameLab setFont:[UIFont systemFontOfSize:11]];
        [_pointNameLab setTextColor:UIColorHexAlpha(#FFFFFF, 0.7)];
    }
    return _pointNameLab;
}

- (void)setModel:(MapLandmarkModel *)model{
    _model = model;
    self.pointNameLab.text = model.landmarkName;
    [self.pointImageView setImageWithURL:[NSURL URLWithString:model.landmarkIcon]
                             placeholder:[UIImage imageNamed:@"landPath_position"]
                                 options:YYWebImageOptionProgressiveBlur|YYWebImageOptionSetImageWithFadeAnimation
                              completion:nil];
}

/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/

@end






@interface MRKUserIndicateView ()
@property (nonatomic, strong) UIImageView *headerView;
@property (nonatomic, strong) UIImageView *targetView;
@property (nonatomic, strong) UIImageView *pointerView;
@end


@implementation MRKUserIndicateView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        
        ///
        [self addSubview:self.targetView];
        [self addSubview:self.pointerView];
        [self addSubview:self.headerView];
        
        ///添加头像
        [self.headerView setImageWithURL:[NSURL URLWithString:UserInfo.avatar]
                             placeholder:UserInfo.avatarHoldingImage];
    }
    return self;
}


- (void)layoutSubviews {
    [super layoutSubviews];
    
    [self.targetView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.mas_equalTo(self);
        make.bottom.mas_equalTo(self.mas_bottom);
        make.size.mas_equalTo(CGSizeMake(28, 23));
    }];
    
    [self.pointerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.mas_equalTo(self);
        make.centerY.mas_equalTo(self.mas_bottom).offset(0);
        make.size.mas_equalTo(CGSizeMake(10, 10));
    }];
    
    [self.headerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.mas_equalTo(self);
        make.top.mas_equalTo(self.mas_top).offset(0);
        make.bottom.mas_equalTo(self.mas_bottom).offset(-11);
        make.size.mas_equalTo(CGSizeMake(30, 30));
    }];
}

- (UIImageView *)headerView{
    if (!_headerView) {
        _headerView = [[UIImageView alloc] init];
        _headerView.contentMode = UIViewContentModeScaleToFill;
        _headerView.layer.cornerRadius = 30/2;
        _headerView.layer.masksToBounds = YES;
        _headerView.layer.borderWidth = 1.0;
        _headerView.layer.borderColor = UIColor.whiteColor.CGColor;
    }
    return _headerView;
}

- (UIImageView *)targetView{
    if (!_targetView) {
        _targetView = [[UIImageView alloc] init];
        _targetView.image = [UIImage imageNamed:@"landPath_target"];
        _targetView.contentMode = UIViewContentModeScaleToFill;
    }
    return _targetView;
}

- (UIImageView *)pointerView{
    if (!_pointerView) {
        _pointerView = [[UIImageView alloc] init];
        _pointerView.image = [UIImage imageNamed:@"landPath_minePointer"];
        _pointerView.contentMode = UIViewContentModeScaleToFill;
    }
    return _pointerView;
}


/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/

@end







@interface MRKLandIndicateView ()
@property (nonatomic, strong) UIImageView *pointerView;
@property (nonatomic, strong) NSNumber *totalDistance;
@end


@implementation MRKLandIndicateView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        ///
        [self addSubview:self.pointerView];
        [self.pointerView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.center.mas_equalTo(self);
            make.size.mas_equalTo(CGSizeMake(10, 10));
        }];
        
        @weakify(self);
        [[[[RACObserve(self, userModel.totalDistance) skip:2] distinctUntilChanged] takeUntil:[self rac_willDeallocSignal]] subscribeNext:^(NSNumber *x) {
            @strongify(self);
            if (!x) {return;}
            [self startAnimationPoint];
        }];
    }
    return self;
}

- (UIImageView *)pointerView{
    if (!_pointerView) {
        _pointerView = [[UIImageView alloc] init];
        _pointerView.image = [UIImage imageNamed:@"landPath_pointer"];
        _pointerView.contentMode = UIViewContentModeScaleToFill;
    }
    return _pointerView;
}

- (void)removeFromSuperview{
    [super removeFromSuperview];
    
}

- (void)setUserModel:(MapLandUserModel *)userModel{
    _userModel = userModel;
    
    ///设置一个初始值
    if ([self.totalDistance.stringValue isEmpty]){
        self.totalDistance = userModel.totalDistance;
    }
}

- (void)startAnimationPoint{
    if (self.model.length.doubleValue <= 0){
        return;
    }
    
    double start = self.totalDistance.doubleValue;
    double end = self.userModel.totalDistance.doubleValue;
    if (start >= end){
        return;
    }
    
    double roadLength = self.model.length.doubleValue;
    float progress = fmod(end/roadLength, 1.0);
    
    CGPoint endPoint = [self.landPath pointAtProgress:progress with:self.pathLength];
    NSLog(@"endPoint CGPoint: (%.2f, %.2f)", endPoint.x, endPoint.y);
    
    if (CGPointEqualToPoint(self.layer.position, CGPointMake(5, 5))) {
        self.layer.position = endPoint;
    }else{
        
        @weakify(self);
        POPBasicAnimation *positionAnimation = [POPBasicAnimation animationWithPropertyNamed:kPOPLayerPosition];
        positionAnimation.fromValue = [NSValue valueWithCGPoint:self.layer.position];
        positionAnimation.toValue = [NSValue valueWithCGPoint:endPoint];
        positionAnimation.duration = 0.3;
        positionAnimation.timingFunction = [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionLinear];
        positionAnimation.removedOnCompletion = YES;
        positionAnimation.beginTime = CACurrentMediaTime();
        positionAnimation.completionBlock = ^(POPAnimation *anim, BOOL finished) {
            @strongify(self);
            self.layer.position = endPoint;
        };
        [self.layer pop_addAnimation:positionAnimation forKey:@"positionAnimation"];
    }
    self.totalDistance = self.userModel.totalDistance;
}






/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/

@end


/**

  要实现一个基于贝塞尔曲线的路径，且在该路径上以动态速度（例如10km/h）移动的动画，
  你需要根据路径的总长度（如1km）和视图的速度（如10km/h）来动态调整动画的 `duration` 和 `view.layer.speed`。
  同时，由于速度是动态更新的，我们需要能够根据速度的变化实时更新动画的播放速度。

  ### 关键思路：
  1. **计算路径的总长度**：首先需要计算贝塞尔曲线的总长度，假设路径的总长度为1km。
  2. **计算速度与时间的关系**：根据视图的速度（如10km/h）计算动画的 `duration`，即动画从起点到终点所需的时间。
  3. **动态更新速度**：由于速度是动态变化的，需要实时更新 `view.layer.speed` 和 `animation.duration`。

  ### 步骤

  1. **计算路径总长度**：
     - 使用之前提到的计算路径长度的方法，假设路径的总长度是 1km。

  2. **计算初始动画持续时间**：
     - 根据速度和路径的长度，计算动画的初始 `duration`。
     - 例如，路径长度为 1km，视图的速度为 10km/h，那么初始的 `duration` 可以通过公式计算：
       \[
       \text{duration} = \frac{\text{path length}}{\text{speed}} = \frac{1 \text{ km}}{10 \text{ km/h}} = 0.1 \text{ hours} = 360 \text{ seconds}
       \]
     - 初始 `duration` 为 360秒。

  3. **设置动画的 `duration` 和 `view.layer.speed`**：
     - 使用 `CAKeyframeAnimation` 时，你可以根据当前的速度来动态更新 `duration` 和 `view.layer.speed`。
     - 假设速度是动态更新的，我们可以通过以下方式更新 `duration` 和 `speed`。

  4. **动态更新 `view.layer.speed`**：
     - `view.layer.speed` 可以通过动态更新来调整动画的播放速度。你可以根据当前速度与初始速度的比值来调整 `view.layer.speed`。

  ### 示例代码

  假设路径长度已经计算出来，视图的速度是动态变化的，以下是如何设置 `view.layer.speed` 和 `CAKeyframeAnimation.duration` 的示例代码：

  ```objc
  // 假设路径的总长度为 1km（1000米）
  CGFloat pathLengthInMeters = 1000.0; // 1km

  // 假设初始速度为 10km/h (即 10000米/小时)
  CGFloat initialSpeedInMetersPerHour = 10000.0;
  CGFloat initialSpeedInMetersPerSecond = initialSpeedInMetersPerHour / 3600.0; // 转换为米/秒

  // 当前速度（假设动态变化）
  CGFloat currentSpeedInMetersPerSecond = 10.0; // 例如 10米/秒

  // 计算动画的持续时间
  CGFloat animationDuration = pathLengthInMeters / currentSpeedInMetersPerSecond; // 总长度 / 当前速度 = 持续时间（秒）

  // 创建动画
  CAKeyframeAnimation *animation = [CAKeyframeAnimation animationWithKeyPath:@"position"];
  animation.path = path.CGPath; // 贝塞尔曲线路径
  animation.duration = animationDuration; // 动态计算的动画持续时间
  animation.repeatCount = HUGE_VALF; // 循环播放

  // 设置动画的 speed（动态更新）
  view.layer.speed = currentSpeedInMetersPerSecond / initialSpeedInMetersPerSecond;

  // 启动动画
  [view.layer addAnimation:animation forKey:@"moveAlongPath"];

  // 动态更新动画的速度
  - (void)updateAnimationSpeed:(CGFloat)newSpeedInMetersPerSecond) {
      // 更新速度
      currentSpeedInMetersPerSecond = newSpeedInMetersPerSecond;
      
      // 重新计算动画的持续时间
      animation.duration = pathLengthInMeters / currentSpeedInMetersPerSecond;
      
      // 更新 layer.speed
      view.layer.speed = currentSpeedInMetersPerSecond / initialSpeedInMetersPerSecond;
      
      // 更新动画的 beginTime 以确保动画继续进行
      CFTimeInterval pausedTime = view.layer.timeOffset;
      view.layer.beginTime = CACurrentMediaTime() - pausedTime;
      
      // 更新动画的持续时间
      [view.layer removeAnimationForKey:@"moveAlongPath"];
      [view.layer addAnimation:animation forKey:@"moveAlongPath"];
  }
  ```

  ### 代码解释：

  1. **路径总长度**：路径的总长度设为 1km（1000米）。你需要根据实际的贝塞尔路径计算其总长度。
     
  2. **速度和持续时间计算**：
     - 初始速度为 10km/h，转换为米/秒后用于计算动画的初始持续时间。
     - 当前速度是动态变化的，`currentSpeedInMetersPerSecond` 可以随时更新，并重新计算 `animation.duration`。
     
  3. **动态更新 `view.layer.speed`**：
     - `view.layer.speed` 控制动画的播放速度。它是当前速度与初始速度的比值。通过 `view.layer.speed` 调整动画的播放速率，使得动画的播放速度与当前速度一致。

  4. **更新动画的 `duration`**：
     - 动态更新 `animation.duration` 使得动画的持续时间与当前速度保持一致。

  5. **重新设置动画**：
     - 动态更新速度后，需要重新设置动画的 `beginTime` 和 `duration`，确保动画继续流畅地播放。

  ### 关键点：

  - **路径长度**：首先需要计算路径的总长度，这对于计算 `duration` 非常重要。
  - **速度**：根据动态变化的速度来更新动画的 `duration` 和 `view.layer.speed`，使得动画的速度始终与视图的速度一致。
  - **平滑过渡**：动态更新动画时，确保重新计算 `beginTime` 和 `duration`，以避免动画中断或抖动。

  通过这种方式，你可以实现一个视图沿着贝塞尔曲线路径以动态速度运动的效果。
  
  **/

// */


/**
 
     // 设置每 0.3 秒的更新间隔
     CGFloat updateInterval = 0.3;
     CGFloat totalDuration = pathLength * updateInterval;
 
     // 创建关键帧动画
     CAKeyframeAnimation *animation = [CAKeyframeAnimation animationWithKeyPath:@"position"];
     animation.path = path.CGPath;  // 设置路径
     animation.duration = totalDuration;  // 动画的总持续时间
 
     // 设置关键帧时间，确保每 0.3 秒更新一次
     NSInteger numberOfFrames = (NSInteger)(pathLength / updateInterval);
     NSMutableArray *keyTimes = [NSMutableArray array];
     NSMutableArray *values = [NSMutableArray array];
 
     for (NSInteger i = 0; i <= numberOfFrames; i++) {
         CGFloat progress = (CGFloat)i / numberOfFrames;
         [keyTimes addObject:@(progress)];
         [values addObject:[NSValue valueWithCGPoint:[self positionAtProgress:progress path:path.CGPath]]];
     }
 
     animation.keyTimes = keyTimes;
     animation.values = values;
 
     // 设置其他动画属性
     animation.rotationMode = nil; // 不自动旋转
     animation.repeatCount = HUGE_VALF; // 无限循环
     animation.calculationMode = kCAAnimationPaced; // 均匀速度
     animation.autoreverses = NO; // 不反向
     animation.fillMode = kCAFillModeForwards;
     animation.removedOnCompletion = NO;
     animation.beginTime = CACurrentMediaTime() + 2.0;  // 延迟2秒开始
     animation.timingFunction = [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionLinear];
 
     // 添加动画
     [self.movingPointerView.layer addAnimation:animation forKey:@"moveAlongPath"];
     
     // 假设你有一个动画对象 animation
     animation.speed = 0;  // 暂停动画
     animation.timeOffset = [animation beginTime] - CACurrentMediaTime();  // 记录当前时间

     // 恢复动画
     animation.speed = 1;  // 恢复正常播放速度
     animation.beginTime = CACurrentMediaTime() - animation.timeOffset;  // 恢复起始时间

 
     /// 创建关键帧动画
     CAKeyframeAnimation *animation = [CAKeyframeAnimation animationWithKeyPath:@"position"];
     animation.path = path.CGPath; /// 设置路径
     animation.duration = 5.0; // 动画持续时间
     animation.rotationMode = nil; // kCAAnimationRotateAuto 自动旋转以匹配路径方向
     animation.repeatCount = HUGE_VALF; // 无限循环
     animation.calculationMode = kCAAnimationPaced; // 均匀速度
     animation.autoreverses = NO; // 不反向运动
     animation.fillMode = kCAFillModeForwards;
     animation.removedOnCompletion = NO;
     animation.beginTime = CACurrentMediaTime() + 2.0;
     animation.timingFunction = [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionLinear];
     [self.movingPointerView.layer addAnimation:animation forKey:@"moveAlongPath"];
 
 
 
 - (CGPoint)positionAtProgress:(CGFloat)progress path:(CGPathRef)path {
     // 控制点初始化
     __block CGPoint p0, p1, p2, p3;

     // 创建一个块来存储路径元素，并传递给 CGPathApply
     void (^pathBlock)(const CGPathElement *element) = ^(const CGPathElement *element) {
         if (element->type == kCGPathElementMoveToPoint) {
             p0 = element->points[0];  // 起点
         } else if (element->type == kCGPathElementAddCurveToPoint) {
             p1 = element->points[0];  // 第一个控制点
             p2 = element->points[1];  // 第二个控制点
             p3 = element->points[2];  // 终点
         }
     };

     // 使用 CGPathApply 遍历路径元素，并调用 block
     CGPathApply(path, (__bridge void *)pathBlock, CGPathApplierFunctionWrapper);

     // 计算贝塞尔曲线的位置
     CGFloat t = progress;
     CGFloat u = 1 - t;

     // 三次贝塞尔曲线公式
     CGPoint position;
     position.x = (pow(u, 3) * p0.x) + (3 * pow(u, 2) * t * p1.x) + (3 * u * pow(t, 2) * p2.x) + (pow(t, 3) * p3.x);
     position.y = (pow(u, 3) * p0.y) + (3 * pow(u, 2) * t * p1.y) + (3 * u * pow(t, 2) * p2.y) + (pow(t, 3) * p3.y);

     return position;
 }

 /// 该函数用来包装调用块
 void CGPathApplierFunctionWrapper(void *info, const CGPathElement *element) {
     void (^block)(const CGPathElement *element) = (__bridge void (^)(const CGPathElement *))info;
     block(element);
 }

 */
