//
//  MRKUserTestingController.h
//  Student_IOS
//
//  Created by <PERSON><PERSON> on 2023/12/18.
//

#import <UIKit/UIKit.h>
#import "MRKBaseController.h"
#import "EquipmentDetialModel.h"


///测评过程枚举
typedef enum: NSUInteger {
    UserTestPreStatus   =  0,   //准备
    UserTestingStatus   =  1,   //测评中
    UserTestPauseStatus =  2,   //测评暂停
    UserTestEndStatus   =  3,   //测评结束
} UserTestStatus;


NS_ASSUME_NONNULL_BEGIN

@interface MRKUserTestingController : MRKBaseController
@property (nonatomic, strong) EquipmentDetialModel *equipModel; ///设备详情数据
@property (nonatomic, copy) NSString *productId;
/// 来源
//@property (nonatomic, copy) NSString *source;
@end

NS_ASSUME_NONNULL_END
