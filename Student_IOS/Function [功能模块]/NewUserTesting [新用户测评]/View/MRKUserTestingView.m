//
//  MRKUserTestingView.m
//  Student_IOS
//
//  Created by Junq on 2023/12/18.
//

#import "MRKUserTestingView.h"
#import "LOTAnimationView.h"
#import "UIColor+AlivcHelper.h"

@interface MRKUserTestingView ()

@end

@implementation MRKUserTestingView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = UIColor.whiteColor;
        self.clipsToBounds = NO;
        
        [self addSubview:self.startView];
        [self.startView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.mas_equalTo(UIEdgeInsetsZero);
        }];
        
        [self addSubview:self.progressView];
        [self.progressView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.mas_equalTo(UIEdgeInsetsZero);
        }];
    }
    return self;
}

- (void)setTestStatus:(UserTestStatus)testStatus {
    _testStatus = testStatus;
    switch (testStatus) {
        case UserTestPreStatus: {
            [self.startView setHiddenAnimated:NO];
            [self.progressView setHiddenAnimated:YES];
        
        }   break;
        case UserTestingStatus: case UserTestPauseStatus: {
            [self.startView setHiddenAnimated:YES];
            [self.progressView setHiddenAnimated:NO];
        
        }   break;
        case UserTestEndStatus: {
            [self.startView setHiddenAnimated:YES];
            [self.progressView setHiddenAnimated:NO];
        
        }   break;
        default:
            break;
    }
}

- (void)setEquipModel:(EquipmentDetialModel *)equipModel {
    _equipModel = equipModel;
    self.startView.equipModel = equipModel;
    self.progressView.equipModel = equipModel;
}
- (void)setDeviceDetailModel:(MRKDeviceModel *)deviceDetailModel{
    _deviceDetailModel = deviceDetailModel;
    self.startView.deviceDetailModel = deviceDetailModel;
    self.progressView.deviceDetailModel = deviceDetailModel;
}
- (void)setTimeSeconds:(NSInteger)timeSeconds{
    _timeSeconds = timeSeconds;
    self.progressView.timeSeconds = 60 - timeSeconds;
}

- (MRKTestingStartView *)startView{
    if (!_startView) {
        _startView = [MRKTestingStartView new];
    }
    return _startView;
}
- (MRKTestingProgressView *)progressView{
    if (!_progressView) {
        _progressView = [MRKTestingProgressView new];
        _progressView.hidden = YES;
        _progressView.alpha = 0;
    };
    return _progressView;
}

/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/

@end






@interface MRKTestingStartView ()
@property (nonatomic, strong) UIImageView *backBorderImageView;
@property (nonatomic, strong) UIImageView *vipImageView;

@property (nonatomic, strong) UILabel *titleLab;
@property (nonatomic, strong) UIImageView *deviceImageView;
@property (nonatomic, strong) UILabel *deviceNameLab;
@end

@implementation MRKTestingStartView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {

        [self addSubview:self.backBorderImageView];
        [self.backBorderImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.mas_equalTo(UIEdgeInsetsZero);
        }];
        
        [self addSubview:self.vipImageView];
        [self.vipImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.mas_top);
            make.centerX.equalTo(self.mas_centerX);
            make.size.mas_equalTo(CGSizeMake(WKDHPX(105), WKDHPX(20)));
        }];
        
        [self addSubview:self.titleLab];
        [self addSubview:self.deviceNameLab];
        [self addSubview:self.deviceImageView];
        [self.titleLab mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.mas_top).offset(WKDHPX(35));
            make.centerX.equalTo(self.mas_centerX);
            make.width.mas_equalTo(WKDHPX(225));
        }];
        [self.deviceNameLab mas_makeConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(self.mas_bottom).offset(-WKDHPX(24));
            make.centerX.equalTo(self.mas_centerX);
            make.width.mas_equalTo(WKDHPX(225));
        }];
        [self.deviceImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(self.mas_centerY).offset(WKDHPX(20));
            make.centerX.equalTo(self.mas_centerX);
            make.width.mas_equalTo(WKDHPX(180));
            make.height.mas_equalTo(WKDHPX(180));
        }];
    }
    return self;
}

- (void)reloadMasonryConstraint{
    [self.deviceNameLab mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.mas_bottom).offset(-WKDHPX(30));
        make.centerX.equalTo(self.mas_centerX);
        make.width.mas_equalTo(WKDHPX(225));
    }];
    
    [self.deviceImageView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.deviceNameLab.mas_top).offset(-WKDHPX(10));
        make.centerX.equalTo(self.mas_centerX);
        make.width.mas_equalTo(WKDHPX(180));
        make.height.mas_equalTo(WKDHPX(180));
    }];
}

- (void)setEquipModel:(EquipmentDetialModel *)equipModel {
    _equipModel = equipModel;
    
    ///适配页面UI
    int productType = equipModel.productID.intValue;
    switch (productType) {
        case BicycleEquipment: {
            self.titleLab.text = @"骑上你的单车，1分钟体验智能骑行的乐趣吧";
        } break;
        case EllipticalEquipment: {
            self.titleLab.text = @"踩上你的椭圆机，1分钟体验智能运动的乐趣吧";
        } break;
        case BoatEquipment: {
            self.titleLab.text = @"划动你的划船机，1分钟体验智能运动的乐趣吧";
        } break;
        case TreadmillEquipment: {
            self.titleLab.text = @"开启你的跑步机，1分钟体验智能运动的乐趣吧";
        } break;
        default: break;
    }
}

- (void)setDeviceDetailModel:(MRKDeviceModel *)deviceDetailModel{
    _deviceDetailModel = deviceDetailModel;
    
    self.deviceNameLab.text = deviceDetailModel.bluetoothName;
    [self.deviceImageView setImageWithURL:[NSURL URLWithString:deviceDetailModel.cover]
                              placeholder:[UIImage imageNamed:@"merit_holder_1_1"]
                                  options:YYWebImageOptionProgressiveBlur|YYWebImageOptionSetImageWithFadeAnimation
                               completion:nil];
}

- (void)reloadDisplayVipUI{
    [self.backBorderImageView setHiddenAnimated:NO];
    [self.vipImageView setHiddenAnimated:NO];
}

- (UIImageView *)backBorderImageView{
    if (!_backBorderImageView) {
        _backBorderImageView = [UIImageView new];
        _backBorderImageView.contentMode = UIViewContentModeScaleToFill;
        _backBorderImageView.clipsToBounds = YES;
        _backBorderImageView.image = [UIImage imageNamed:@"user_test_vipback"];
        _backBorderImageView.hidden = YES;
        _backBorderImageView.alpha = 0;
    }
    return _backBorderImageView;
}
- (UIImageView *)vipImageView{
    if (!_vipImageView) {
        _vipImageView = [UIImageView new];
        _vipImageView.contentMode = UIViewContentModeScaleAspectFill;
        _vipImageView.clipsToBounds = YES;
        _vipImageView.image = [UIImage imageNamed:@"user_test_vip"];
        _vipImageView.hidden = YES;
        _vipImageView.alpha = 0;
    }
    return _vipImageView;
}
- (UILabel *)titleLab {
    if (!_titleLab) {
        _titleLab = [[UILabel alloc] init];
        _titleLab.textColor = UIColorHex(#05182C);
        _titleLab.font = kMedium_Font_NoDHPX(17);
        _titleLab.text = @"";
        _titleLab.textAlignment = NSTextAlignmentCenter;
        _titleLab.numberOfLines = 2;
    }
    return _titleLab;
}
- (UILabel *)deviceNameLab {
    if (!_deviceNameLab) {
        _deviceNameLab = [[UILabel alloc] init];
        _deviceNameLab.textColor = UIColorHex(#05182C);
        _deviceNameLab.font = kMedium_Font_NoDHPX(17);
        _deviceNameLab.textAlignment = NSTextAlignmentCenter;
    }
    return _deviceNameLab;
}
- (UIImageView *)deviceImageView{
    if (!_deviceImageView) {
        _deviceImageView = [UIImageView new];
        _deviceImageView.contentMode = UIViewContentModeScaleAspectFill;
        _deviceImageView.autoresizingMask = UIViewAutoresizingFlexibleTopMargin | UIViewAutoresizingFlexibleLeftMargin;
        _deviceImageView.clipsToBounds = YES;
    }
    return _deviceImageView;
}

/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/
@end





@interface MRKTestingProgressView ()
@property (nonatomic, strong) UILabel *titleLab;
@property (nonatomic, strong) MRKTestingItemView *leftItem;
@property (nonatomic, strong) MRKTestingItemView *rightItem;
@property (nonatomic, strong) LOTAnimationView *animatioView;///
@end

@implementation MRKTestingProgressView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        
        [self addSubview:self.titleLab];
        [self addSubview:self.leftItem];
        [self addSubview:self.rightItem];
        
        [self.titleLab mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.mas_top).offset(WKDHPX(35));
            make.centerX.equalTo(self.mas_centerX);
            make.width.mas_equalTo(WKDHPX(250));
        }];
        [self.leftItem mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.mas_top).offset(WKDHPX(140));
            make.centerX.equalTo(self.mas_centerX).offset(-WKDHPX(131)/2);
            make.width.mas_equalTo(WKDHPX(131));
        }];
        [self.rightItem mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.mas_top).offset(WKDHPX(140));
            make.centerX.equalTo(self.mas_centerX).offset(WKDHPX(131)/2);
            make.width.mas_equalTo(WKDHPX(131));
        }];
    }
    return self;
}

- (void)setProductType:(int)productType{
    _productType = productType;
    
}

- (void)setEquipModel:(EquipmentDetialModel *)equipModel {
    _equipModel = equipModel;
    
    ///适配页面UI
    int productType = equipModel.productID.intValue;
    self.productType = productType;
    switch (productType) {
        case BicycleEquipment:
        case EllipticalEquipment:
        case BoatEquipment: {
            
            self.leftItem.detailLab.text = productType == BoatEquipment ? @"当前桨频" : @"当前踏频";
            self.rightItem.detailLab.text = @"当前阻力";
            if (equipModel.isElectromagneticControl || equipModel.isSupportResistanceEcho) {
                
                [self addSubview:self.animatioView];
                [self.animatioView mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.left.equalTo(self.rightItem.titleLab.mas_right).offset(WKDHPX(7));
                    make.bottom.equalTo(self.rightItem.titleLab.mas_bottom).offset(-WKDHPX(10));
                    make.width.mas_equalTo(WKDHPX(60));
                    make.height.mas_equalTo(WKDHPX(120));
                }];
            }else{
                
                [self.leftItem mas_updateConstraints:^(MASConstraintMaker *make) {
                    make.centerX.equalTo(self.mas_centerX);
                }];
                
                ///移除居中一个
                [_rightItem removeFromSuperview];
                _rightItem = nil;
            }
            
        } break;
            
        case TreadmillEquipment: {
            ///移除一个显示
            [self.rightItem removeFromSuperview];
            self.rightItem = nil;
            
            self.leftItem.detailLab.text = @"当前速度";
            [self.leftItem mas_updateConstraints:^(MASConstraintMaker *make) {
                make.centerX.equalTo(self.mas_centerX);
            }];
        
            [self addSubview:self.animatioView];
            [self.animatioView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.left.equalTo(self.leftItem.titleLab.mas_right).offset(WKDHPX(7));
                make.bottom.equalTo(self.leftItem.titleLab.mas_bottom).offset(-WKDHPX(10));
                make.width.mas_equalTo(WKDHPX(60));
                make.height.mas_equalTo(WKDHPX(120));
            }];
        } break;

        default: break;
    }
}


- (void)setDeviceDetailModel:(MRKDeviceModel *)deviceDetailModel{
    _deviceDetailModel = deviceDetailModel;
}



- (void)setTyModel:(BaseEquipDataModel *)tyModel{
    _tyModel = tyModel;
    
    switch (self.productType) {
        case BicycleEquipment:
        case EllipticalEquipment:
        case BoatEquipment: {
            
            self.leftItem.titleLab.text = tyModel.spm ? (tyModel.spm.intValue == 0 ? @"0" :  tyModel.spm.stringValue) : @"--";
            self.rightItem.titleLab.text = tyModel.drag ? (tyModel.drag.intValue == 0 ? @"0" :  tyModel.drag.stringValue) : @"--";
            if (self.equipModel.maxResistance.doubleValue >0){
                float ratio = tyModel.drag.doubleValue/self.equipModel.maxResistance.doubleValue;
                self.rightItem.titleLab.textColor = [UIColor loadFromColor:@"#00BC93" toColor:@"#FF2020" andRatio:ratio];
            }else{
                self.rightItem.titleLab.textColor = UIColorHex(#00BC93);
            }
            
        } break;
            
        case TreadmillEquipment: {
            
            self.leftItem.titleLab.text = [NSString stringWithFormat:@"%.1f", tyModel.speed.doubleValue];
            if (self.equipModel.maxSpeed.doubleValue >0){
                float ratio = tyModel.speed.doubleValue/self.equipModel.maxSpeed.doubleValue;
                self.leftItem.titleLab.textColor = [UIColor loadFromColor:@"#00BC93" toColor:@"#FF2020" andRatio:ratio];
            }else{
                self.leftItem.titleLab.textColor = UIColorHex(#00BC93);
            }
        } break;

        default: break;
    }
}

- (void)setTimeSeconds:(NSInteger)timeSeconds{
    _timeSeconds = timeSeconds;

    switch (self.productType) {
        case BicycleEquipment:
        case EllipticalEquipment:
        case BoatEquipment: {
            if ( timeSeconds <= 10){
                self.titleLab.text = @"加速试试，你的运动数据会同步变化哦";
                if (timeSeconds == 7 && self.equipModel.isElectromagneticControl){
                    self.rightItem.detailLab.text = @"即将调整阻力";
//                    self.rightItem.detailLab.textColor = UIColorHex(#FF3E23);
                    self.rightItem.detailLab.gradientColors = @[
                        [UIColor colorWithHexString:@"#17D2E3"],
                        [UIColor colorWithHexString:@"#AA69FF"],
                        [UIColor colorWithHexString:@"#FF8FB4"]
                    ];
                }
                if (timeSeconds == 10 && self.equipModel.isElectromagneticControl){
                    self.rightItem.detailLab.text = @"当前阻力";
                    self.rightItem.detailLab.textColor = UIColorHex(#05182C);
                    self.rightItem.detailLab.gradientColors = @[];
                }
                
                ///指令指令速度4
                if (timeSeconds == 10){
                    NSInteger resistance = 0;
                    NSInteger maxResistance = self.equipModel.maxResistance.intValue;
                    if (maxResistance == 8){
                        resistance = 3;
                    }else if (maxResistance == 16){
                        resistance = 6;
                    }else if (maxResistance == 24){
                        resistance = 8;
                    }else if (maxResistance == 32){
                        resistance = 10;
                    }

                    [self sendDeviceNotification:@{
                        Slope : @(self.tyModel.gradient.intValue),
                        Resistance:@(resistance),
                        BlueDeviceType:@(self.productType)
                    }];
                }
            } else if (timeSeconds > 10 && timeSeconds <= 40){
                if (self.equipModel.isElectromagneticControl){
                    self.titleLab.text = @"接下来「AI调阻」将为您自动调节阻力，解放双手，跟着练就行了";
                }else if (self.equipModel.isSupportResistanceEcho){
                    self.titleLab.text = @"继续运动，试试调节设备的阻力，感受不同阻力的运动变化";
                }else{
                    self.titleLab.text = @"加速试试，你的运动数据会同步变化哦";
                }
                
                if (self.equipModel.isElectromagneticControl){
                    if (timeSeconds == 17){
                        self.rightItem.detailLab.text = @"即将调整阻力";
//                        self.rightItem.detailLab.textColor = UIColorHex(#FF3E23);
                        self.rightItem.detailLab.gradientColors = @[
                            [UIColor colorWithHexString:@"#17D2E3"],
                            [UIColor colorWithHexString:@"#AA69FF"],
                            [UIColor colorWithHexString:@"#FF8FB4"]
                        ];
                    }
                    if (timeSeconds == 27){
                        self.rightItem.detailLab.text = @"即将调整至最高阻力";
//                        self.rightItem.detailLab.textColor = UIColorHex(#FF3E23);
                        self.rightItem.detailLab.gradientColors = @[
                            [UIColor colorWithHexString:@"#17D2E3"],
                            [UIColor colorWithHexString:@"#AA69FF"],
                            [UIColor colorWithHexString:@"#FF8FB4"]
                        ];
                    }
                    if (timeSeconds == 20 || timeSeconds == 30){
                        self.rightItem.detailLab.text = @"当前阻力";
                        self.rightItem.detailLab.textColor = UIColorHex(#05182C);
                        self.rightItem.detailLab.gradientColors = @[];
                    }
                }

                ///指令指令速度4
                if (timeSeconds == 20){
                    NSInteger resistance = 0;
                    NSInteger maxResistance = self.equipModel.maxResistance.intValue;
                    if (maxResistance == 8){
                        resistance = 6;
                    }else if (maxResistance == 16){
                        resistance = 12;
                    }else if (maxResistance == 24){
                        resistance = 16;
                    }else if (maxResistance == 32){
                        resistance = 20;
                    }

                    [self sendDeviceNotification:@{
                        Slope : @(self.tyModel.gradient.intValue),
                        Resistance:@(resistance),
                        BlueDeviceType:@(self.productType)
                    }];
                }
                
                ///指令指令速度6
                if (timeSeconds == 30){
                    NSInteger maxResistance = self.equipModel.maxResistance.intValue;
                    [self sendDeviceNotification:@{
                        Slope : @(self.tyModel.gradient.intValue),
                        Resistance:@(maxResistance),
                        BlueDeviceType:@(self.productType)
                    }];
                }
            }else if (timeSeconds > 40){
                self.titleLab.text = @"坚持住, 感受运动的快乐";
            }
           
        } break;
            
        case TreadmillEquipment: {
          
            if ( timeSeconds <= 10){
                self.titleLab.text = @"运动数据实时同步";
                if (timeSeconds == 7){
                    self.leftItem.detailLab.text = @"即将调整速度";
//                    self.leftItem.detailLab.textColor = UIColorHex(#FF3E23);
                    self.leftItem.detailLab.gradientColors = @[
                        [UIColor colorWithHexString:@"#17D2E3"],
                        [UIColor colorWithHexString:@"#AA69FF"],
                        [UIColor colorWithHexString:@"#FF8FB4"]
                    ];
                }
                if (timeSeconds == 10 ){
                    self.leftItem.detailLab.text = @"当前速度";
                    self.leftItem.detailLab.textColor = UIColorHex(#05182C);
                    self.leftItem.detailLab.gradientColors = @[];
                }
                
                ///发送指令速度2
                if (timeSeconds == 10){
                    [self sendDeviceNotification:@{
                        Speed : @20, //跑步机速度指令*10
                        Slope : @(self.tyModel.gradient.intValue),
                        BlueDeviceType : @(TreadmillEquipment),
                        @"status" : @(self.currentStatus.intValue)
                    }];
                }
                
            } else if (timeSeconds > 10 && timeSeconds <= 40) {
                self.titleLab.text = @"接下来「AI调速」将为您自动调节速度，解放双手，跟着练就行了";
                if (timeSeconds == 17 || timeSeconds == 27){
                    self.leftItem.detailLab.text = @"即将调整速度";
//                    self.leftItem.detailLab.textColor = UIColorHex(#FF3E23);
                    self.leftItem.detailLab.gradientColors = @[
                        [UIColor colorWithHexString:@"#17D2E3"],
                        [UIColor colorWithHexString:@"#AA69FF"],
                        [UIColor colorWithHexString:@"#FF8FB4"]
                    ];
                }
                if ( timeSeconds == 20 || timeSeconds == 30){
                    self.leftItem.detailLab.text = @"当前速度";
                    self.leftItem.detailLab.textColor = UIColorHex(#05182C);
                    self.leftItem.detailLab.gradientColors = @[];
                }

                ///指令指令速度4
                if (timeSeconds == 20){
                    [self sendDeviceNotification:@{
                        Speed : @40,
                        Slope : @(self.tyModel.gradient.intValue),
                        BlueDeviceType : @(TreadmillEquipment),
                        @"status" : @(self.currentStatus.intValue)
                    }];
                }
                ///指令指令速度6
                if (timeSeconds == 30){
                    [self sendDeviceNotification:@{
                        Speed : @60,
                        Slope : @(self.tyModel.gradient.intValue),
                        BlueDeviceType : @(TreadmillEquipment),
                        @"status" : @(self.currentStatus.intValue)
                    }];
                }
            } else if (timeSeconds > 40) {
                self.titleLab.text = @"坚持住, 感受运动的快乐";
            }
        } break;

        default: break;
    }
}


///发送设备指令
- (void)sendDeviceNotification:(NSDictionary *)data{
    if (!self.equipModel.isElectromagneticControl){
        return;
    }

    ///发送指令
    [[NSNotificationCenter defaultCenter] postNotificationName:SetResistanceSlopeSpeedNotification object:data];
    
    ///礼花动画
    if(!self.animatioView.isAnimationPlaying) {
        [self.animatioView setAnimationNamed:@"upvote_data.json"];
        self.animatioView.loopAnimation = NO;
        [self.animatioView play];
    }
    
    ///动画
    [[NSNotificationCenter defaultCenter] postNotificationName:@"showTestGood" object:data];
}

- (UILabel *)titleLab {
    if (!_titleLab) {
        _titleLab = [[UILabel alloc] init];
        _titleLab.textColor = UIColorHex(#05182C);
        _titleLab.font = kMedium_Font_NoDHPX(17);
        _titleLab.text = @"加速试试，你的运动数据会同步变化哦";
        _titleLab.textAlignment = NSTextAlignmentCenter;
        _titleLab.numberOfLines = 0;
    }
    return _titleLab;
}
- (MRKTestingItemView *)leftItem{
    if (!_leftItem) {
        _leftItem = [MRKTestingItemView new];
    }
    return _leftItem;
}
- (MRKTestingItemView *)rightItem{
    if (!_rightItem) {
        _rightItem = [MRKTestingItemView new];
    }
    return _rightItem;
}

- (LOTAnimationView *)animatioView {
    if(!_animatioView) {
        LOTAnimationView *img = [LOTAnimationView new];
        [img setAnimationNamed:@"upvote_data.json"];
        img.loopAnimation = YES;
        _animatioView = img;
    }
    return _animatioView;
}
/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/
@end


@interface MRKTestingEndView ()
@property (nonatomic, strong) UILabel *titleLab;
@property (nonatomic, strong) MRKTestingCourseView *courseView;
@property (nonatomic, strong) UIImageView *runImageView;
@end

@implementation MRKTestingEndView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        
        [self addSubview:self.titleLab];
        [self addSubview:self.courseView];
        self.courseView.userInteractionEnabled = YES;
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(addCourseTapGesture)];
        [self.courseView addGestureRecognizer:tap];

        [self addSubview:self.runImageView];
        self.runImageView.userInteractionEnabled = YES;
        UITapGestureRecognizer *tap1 = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(addTrainTapGesture)];
        [self.runImageView addGestureRecognizer:tap1];
    }
    return self;
}

- (void)addCourseTapGesture {
    if (self.courseHandleBlock){
        self.courseHandleBlock();
    }
}

- (void)addTrainTapGesture {
    if (self.trainHandleBlock){
        self.trainHandleBlock();
    }
}

- (void)setModel:(MRKCourseModel *)model{
    _model = model;

    NSString *url = [model.cover imageUrlAdaptReSize:CGSizeMake(WKDHPX(343), WKDHPX(236))];
    [self.courseView.courseImageView setImageWithURL:[NSURL URLWithString:url]
                                         placeholder:[UIImage imageNamed:@"pic_1"]
                                             options:YYWebImageOptionProgressiveBlur|YYWebImageOptionSetImageWithFadeAnimation
                                          completion:nil];
    self.courseView.courseTitleLab.text = model.name;
    self.courseView.courseDetailLab.text = model.introduce;
}

- (void)setEquipModel:(EquipmentDetialModel *)equipModel {
    _equipModel = equipModel;
}

- (void)setExperienceComplete:(BOOL)experienceComplete{
    _experienceComplete = experienceComplete;
    [self layoutIfNeeded];
    [self setNeedsLayout];
}

- (void)setProductId:(NSString *)productId{
    _productId = productId;
    
    if (productId.intValue == BicycleEquipment){
        self.runImageView.image = [UIImage imageNamed:@"test_bicycle_holder"];
    }
    if (productId.intValue == TreadmillEquipment){
        self.runImageView.image = [UIImage imageNamed:@"test_runing_holder"];
    }
    if (productId.intValue == BoatEquipment){
        self.runImageView.image = [UIImage imageNamed:@"test_rowing_holder"];
    }
    if (productId.intValue == EllipticalEquipment){
        self.runImageView.image = [UIImage imageNamed:@"test_elliptical_holder"];
    }
}

//15 105

- (void)layoutSubviews{
    [super layoutSubviews];
    
    UIInterfaceOrientation interfaceOrientation = [[UIApplication sharedApplication] statusBarOrientation];
    if (UIInterfaceOrientationIsLandscape(interfaceOrientation)) {
        
        if (self.experienceComplete){
            [self.titleLab mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(self.mas_top).offset(WKDHPX(24));
                make.centerX.equalTo(self.mas_centerX);
                make.width.mas_equalTo(WKDHPX(305));
            }];
            [self.courseView mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(self.titleLab.mas_bottom).offset(WKDHPX(14));
                make.left.equalTo(self.mas_left).offset(-WKDHPX(40));
                make.width.mas_equalTo(WKDHPX(308));
                make.height.mas_equalTo(WKDHPX(212));
                make.bottom.equalTo(self.mas_bottom).offset(-WKDHPX(26));
            }];
            [self.runImageView mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.centerY.equalTo(self.courseView.mas_centerY);
                make.left.equalTo(self.courseView.mas_right).offset(WKDHPX(15));
                make.right.equalTo(self.mas_right).offset(WKDHPX(105));
                make.width.mas_equalTo(WKDHPX(342));
                make.height.mas_equalTo(WKDHPX(220));
            }];
        }else{
            [self.courseView mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(self.mas_top).offset(WKDHPX(26));
                make.left.equalTo(self.mas_left).offset(-WKDHPX(40));
                make.width.mas_equalTo(WKDHPX(308));
                make.height.mas_equalTo(WKDHPX(212));
                make.bottom.equalTo(self.mas_bottom).offset(-WKDHPX(26));
            }];
            [self.runImageView mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.centerY.equalTo(self.courseView.mas_centerY);
                make.left.equalTo(self.courseView.mas_right).offset(WKDHPX(15));
                make.right.equalTo(self.mas_right).offset(WKDHPX(105));
                make.width.mas_equalTo(WKDHPX(342));
                make.height.mas_equalTo(WKDHPX(220));
            }];
        }
    
    } else {
        
        if (self.experienceComplete){
            [self.titleLab mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(self.mas_top).offset(WKDHPX(24));
                make.centerX.equalTo(self.mas_centerX);
            }];
            [self.courseView mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(self.titleLab.mas_bottom).offset(WKDHPX(18));
                make.centerX.equalTo(self.mas_centerX);
                make.width.mas_equalTo(WKDHPX(343));
                make.height.mas_equalTo(WKDHPX(236));
            }];
            [self.runImageView mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(self.courseView.mas_bottom).offset(0);
                make.centerX.equalTo(self.mas_centerX);
                make.width.mas_equalTo(WKDHPX(342));
                make.height.mas_equalTo(WKDHPX(220));
                make.bottom.equalTo(self.mas_bottom).offset(WKDHPX(81));
            }];
            
        }else{
            
            [self.courseView mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(self.mas_top).offset(WKDHPX(25));
                make.centerX.equalTo(self.mas_centerX);
                make.width.mas_equalTo(WKDHPX(343));
                make.height.mas_equalTo(WKDHPX(236));
             
            }];
            [self.runImageView mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(self.courseView.mas_bottom).offset(0);
                make.centerX.equalTo(self.mas_centerX);
                make.width.mas_equalTo(WKDHPX(342));
                make.height.mas_equalTo(WKDHPX(220));
                make.bottom.equalTo(self.mas_bottom).offset(WKDHPX(81));
            }];
        }
    }
}

- (UILabel *)titleLab {
    if (!_titleLab) {
        _titleLab = [[UILabel alloc] init];
        _titleLab.textColor = UIColorHex(#05182C);
        _titleLab.font = kMedium_Font_NoDHPX(17);
        _titleLab.text = @"正式开始Merit运动之旅吧";
        _titleLab.textAlignment = NSTextAlignmentCenter;
        _titleLab.numberOfLines = 2;
    }
    return _titleLab;
}

- (MRKTestingCourseView *)courseView{
    if (!_courseView) {
        _courseView = [[MRKTestingCourseView alloc] init];
        _courseView.backgroundColor = UIColor.whiteColor;
        _courseView.layer.cornerRadius = 20;
//        _courseView.layer.masksToBounds = YES;
        _courseView.layer.borderWidth = 2.0;
        _courseView.layer.borderColor = UIColor.whiteColor.CGColor;
        
        _courseView.layer.shadowColor = [UIColor blackColor].CGColor;
        _courseView.layer.shadowOpacity = .2;
        _courseView.layer.shadowRadius = 30;
        _courseView.layer.shadowOffset = CGSizeMake(10, 10);
    }
    return _courseView;
}

- (UIImageView *)runImageView{
    if (!_runImageView) {
        _runImageView = [UIImageView new];
        _runImageView.contentMode = UIViewContentModeScaleToFill;
        _runImageView.image = [UIImage imageNamed:@"test_bicycle_holder"];
    }
    return _runImageView;
}
/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/

@end




#import "MRKProgressView.h"

@interface MRKTestingTopView ()
@property (nonatomic, strong) UIImageView *avatarImageView;
@property (nonatomic, strong) UIImageView *statusImageView;
@property (nonatomic, strong) UIImageView *colouredImageView; ///彩带
///
@property (nonatomic, strong) UIImageView *goodImageView;
@property (nonatomic, strong) MRKProgressView *progressView;
@property (nonatomic, strong) UILabel *timerLab;
@end

@implementation MRKTestingTopView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self addSubview:self.colouredImageView];
        [self.colouredImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(self.mas_bottom).offset(-WKDHPX(22));
            make.left.equalTo(self.mas_left).offset(WKDHPX(13));
            make.width.mas_equalTo(WKDHPX(260));
            make.height.mas_equalTo(WKDHPX(76));
        }];

        [self addSubview:self.avatarImageView];
        [self.avatarImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.mas_top).offset(WKDHPX(4));
            make.centerX.equalTo(self.mas_centerX);
            make.width.mas_equalTo(WKDHPX(110));
            make.height.mas_equalTo(WKDHPX(110));
        }];
        
        [self addSubview:self.statusImageView];
        [self.statusImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(self.mas_bottom).offset(-WKDHPX(10));
            make.centerX.equalTo(self.mas_centerX);
            make.width.mas_equalTo(WKDHPX(167));
            make.height.mas_equalTo(WKDHPX(50));
        }];
        
        
        [self.progressView setHiddenAnimated:YES];
        [self addSubview:self.progressView];
        [self.progressView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.mas_left).offset(WKDHPX(93));
            make.bottom.equalTo(self.mas_bottom).offset(-WKDHPX(29));
            make.width.mas_equalTo(WKDHPX(132));
            make.height.mas_equalTo(WKDHPX(9));
        }];
        
        self.goodImageView.alpha = 0;
        [self addSubview:self.goodImageView];
        self.goodImageView.centerY = self.progressView.centerY;
        
        [self.timerLab setHiddenAnimated:YES];
        [self addSubview:self.timerLab];
        [self.timerLab mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.progressView.mas_right).offset(WKDHPX(19));
            make.centerY.equalTo(self.progressView.mas_centerY);
            make.height.mas_equalTo(WKDHPX(58));
        }];
        
        
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(showTestGoodAnimation:) name:@"showTestGood" object:nil];
    }
    return self;
}

///切换动画
- (void)showTestGoodAnimation:(NSNotification *)sender {
    [self.progressView setHiddenAnimated:YES];
    
    self.goodImageView.left = self.width;
    self.goodImageView.centerY = self.progressView.centerY;
    
    [UIView animateWithDuration:0.5
                          delay:0
         usingSpringWithDamping:0.8
          initialSpringVelocity:20
                        options:UIViewAnimationOptionAllowUserInteraction animations:^{
        self.goodImageView.alpha = 1;
        self.goodImageView.left = self.progressView.left;
        
    } completion:^(BOOL finished) {
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self.progressView setHiddenAnimated:NO];
            
            self.goodImageView.alpha = 0;
            self.goodImageView.left = self.width;
        });
    }];
}

///
- (void)setStatusImage:(UIImage *)statusImage{
    self.statusImageView.image = statusImage;
}










- (void)setTotalSeconds:(NSInteger)totalSeconds{
    _totalSeconds = totalSeconds;
}

- (void)setTimeSeconds:(NSInteger)timeSeconds{
    _timeSeconds = timeSeconds;
    
    if (timeSeconds < 0) return;
    self.timerLab.text = [NSString stringWithFormat:@"%ld”", timeSeconds];
    float progress = (float)timeSeconds/self.totalSeconds;
    self.progressView.progress = 1-progress;
}

- (void)testingPrepare {
    self.statusImageView.image = [UIImage imageNamed:@"user_test_connect"];
}

- (void)testingStart{
    [self.colouredImageView setHiddenAnimated:YES];
    [self.statusImageView setHiddenAnimated:YES];
    
    [UIView animateWithDuration:0.3 animations:^{
        dispatch_async(dispatch_get_main_queue(), ^{
            [self.progressView setHiddenAnimated:NO];
            [self.timerLab setHiddenAnimated:NO];
            
            [self.avatarImageView mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.left.equalTo(self.mas_left).offset(WKDHPX(9));
                make.bottom.equalTo(self.mas_bottom);
                make.width.mas_equalTo(WKDHPX(100));
                make.height.mas_equalTo(WKDHPX(100));
            }];
            [self.avatarImageView setNeedsUpdateConstraints];
            [self.avatarImageView updateConstraintsIfNeeded];
        });
    }];
}

- (void)testingEnd{
    [self.colouredImageView setHiddenAnimated:NO];
    [self.statusImageView setHiddenAnimated:NO];
    
    [UIView animateWithDuration:0.3 animations:^{
        dispatch_async(dispatch_get_main_queue(), ^{
            [self.progressView setHiddenAnimated:YES];
            [self.timerLab setHiddenAnimated:YES];
            
            [self.avatarImageView mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(self.mas_top).offset(WKDHPX(4));
                make.centerX.equalTo(self.mas_centerX);
                make.width.mas_equalTo(WKDHPX(110));
                make.height.mas_equalTo(WKDHPX(110));
            }];
            [self.avatarImageView setNeedsUpdateConstraints];
            [self.avatarImageView updateConstraintsIfNeeded];
            
            
            self.statusImageView.image = [UIImage imageNamed:@"user_test_end"];
        });
    }];
}


- (void)testingDone{
    [self.colouredImageView setHiddenAnimated:NO];
    [self.statusImageView setHiddenAnimated:NO];
    
    [UIView animateWithDuration:0.3 animations:^{
        dispatch_async(dispatch_get_main_queue(), ^{
            [self.progressView setHiddenAnimated:YES];
            [self.timerLab setHiddenAnimated:YES];
            
            [self.avatarImageView mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(self.mas_top).offset(WKDHPX(4));
                make.centerX.equalTo(self.mas_centerX);
                make.width.mas_equalTo(WKDHPX(110));
                make.height.mas_equalTo(WKDHPX(110));
            }];
            [self.avatarImageView setNeedsUpdateConstraints];
            [self.avatarImageView updateConstraintsIfNeeded];
            
            
            self.statusImageView.image = [UIImage imageNamed:@"user_test_done"];
        });
    }];
}

- (UIImageView *)avatarImageView{
    if (!_avatarImageView) {
        _avatarImageView = [UIImageView new];
        _avatarImageView.contentMode = UIViewContentModeScaleAspectFill;
        User *user = [Login curLoginUser];
        NSNumber *sex = user.basicInfo.sex;
        if (sex.intValue == 2) {
            _avatarImageView.image = [UIImage imageNamed:@"user_test_woman_avatar"];
        }else{
            _avatarImageView.image = [UIImage imageNamed:@"user_test_man_avatar"];
        }
    }
    return _avatarImageView;
}

- (UIImageView *)statusImageView{
    if (!_statusImageView) {
        _statusImageView = [UIImageView new];
        _statusImageView.contentMode = UIViewContentModeScaleAspectFill;
        _statusImageView.image = [UIImage imageNamed:@"user_test_connect"];
    }
    return _statusImageView;
}

- (UIImageView *)colouredImageView{
    if (!_colouredImageView) {
        _colouredImageView = [UIImageView new];
        _colouredImageView.contentMode = UIViewContentModeScaleAspectFill;
        _colouredImageView.image = [UIImage imageNamed:@"user_test_coloured"];
    }
    return _colouredImageView;
}

- (UIImageView *)goodImageView{
    if (!_goodImageView) {
        _goodImageView = [UIImageView new];
        _goodImageView.contentMode = UIViewContentModeScaleAspectFill;
        _goodImageView.image = [UIImage imageNamed:@"user_test_good"];
        _goodImageView.size = CGSizeMake(107, 46);
    }
    return _goodImageView;
}

- (MRKProgressView *)progressView{
    if (!_progressView) {
        _progressView = [[MRKProgressView alloc] init];
        _progressView.progressRemainingColor = [UIColor.whiteColor colorWithAlphaComponent:0.4];
        _progressView.progressColor = UIColorHex(#16D2E3);

        _progressView.hidden = YES;
        _progressView.alpha = 0;
    }
    return _progressView;
}

- (UILabel *)timerLab {
    if (!_timerLab) {
        _timerLab = [[UILabel alloc] init];
        _timerLab.textColor = UIColor.whiteColor;
        _timerLab.font = [UIFont fontWithName:Bebas_Font size:40];
        _timerLab.text = @"30”";
        _timerLab.textAlignment = NSTextAlignmentCenter;
        
        _timerLab.hidden = YES;
        _timerLab.alpha = 0;
    }
    return _timerLab;
}

/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/

@end











@implementation MRKTestingItemView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self addSubview:self.titleLab];
        [self addSubview:self.detailLab];
    }
    return self;
}
- (void)layoutSubviews{
    [super layoutSubviews];
    
    [self.titleLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.mas_top);
        make.centerX.equalTo(self.mas_centerX);
        make.height.mas_equalTo(WKDHPX(100));
    }];
    
    [self.detailLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.titleLab.mas_bottom).offset(WKDHPX(15));
        make.centerX.equalTo(self.mas_centerX);
        make.width.mas_equalTo(self.mas_width).offset(-10);
        make.bottom.equalTo(self.mas_bottom);
    }];
}
- (UILabel *)titleLab {
    if (!_titleLab) {
        _titleLab = [[UILabel alloc] init];
        _titleLab.textColor = UIColorHex(#05182C);
        _titleLab.font = [UIFont fontWithName:Bebas_Font size:80];
        _titleLab.text = @"0";
        _titleLab.textAlignment = NSTextAlignmentCenter;
    }
    return _titleLab;
}
//- (UILabel *)detailLab {
//    if (!_detailLab) {
//        _detailLab = [[UILabel alloc] init];
//        _detailLab.textColor = UIColorHex(#05182C);
//        _detailLab.font = kMedium_Font_NoDHPX(20);
//        _detailLab.text = @"当前踏频";
//        _detailLab.textAlignment = NSTextAlignmentCenter;
//        _detailLab.numberOfLines = 0;
//    }
//    return _detailLab;
//}

- (GradientLayerLabel *)detailLab {
    if (_detailLab) return _detailLab;
    _detailLab = [[GradientLayerLabel alloc] init];
    _detailLab.textColor = UIColorHex(#05182C);
    _detailLab.font = kMedium_Font_NoDHPX(20);
    _detailLab.textAlignment = NSTextAlignmentCenter;
    _detailLab.text = @"当前踏频";
    _detailLab.numberOfLines = 0;
    return _detailLab;
}

//_detailLab.gradientColors = @[
//    [UIColor colorWithHexString:@"#17D2E3"],
//    [UIColor colorWithHexString:@"#AA69FF"],
//    [UIColor colorWithHexString:@"#FF8FB4"]
//];

/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/

@end







@interface MRKTestingCourseView ()
@property (nonatomic, strong) UIView *contentView;
@property (nonatomic, strong) UIImageView *playImageView;
@end

@implementation MRKTestingCourseView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        
        [self addSubview:self.courseImageView];
        [self addSubview:self.contentView];
        [self.contentView addSubview:self.courseTitleLab];
        [self.contentView addSubview:self.courseDetailLab];
        [self addSubview:self.playImageView];
    }
    return self;
}

- (void)layoutSubviews{
    [super layoutSubviews];
    
    [self.courseImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsZero);
    }];
    [self.contentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.mas_bottom);
        make.left.equalTo(self.mas_left);
        make.right.equalTo(self.mas_right);
        make.height.mas_equalTo(WKDHPX(70));
    }];
    [self.courseTitleLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.contentView.mas_centerY).offset(-WKDHPX(1));
        make.left.equalTo(self.contentView.mas_left).offset(WKDHPX(18));
        make.right.equalTo(self.contentView.mas_right).offset(-WKDHPX(18));
    }];
    [self.courseDetailLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentView.mas_centerY).offset(WKDHPX(1));
        make.left.equalTo(self.contentView.mas_left).offset(WKDHPX(18));
        make.right.equalTo(self.contentView.mas_right).offset(-WKDHPX(18));
    }];
    [self.playImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.mas_centerY).offset(-WKDHPX(18));
        make.centerX.equalTo(self.mas_centerX);
        make.width.mas_equalTo(WKDHPX(46));
        make.height.mas_equalTo(WKDHPX(46));
    }];
}


- (UIImageView *)courseImageView{
    if (!_courseImageView) {
        _courseImageView = [UIImageView new];
        _courseImageView.contentMode = UIViewContentModeScaleAspectFill;
        _courseImageView.layer.cornerRadius = 20.0f;
        _courseImageView.layer.masksToBounds = YES;
    }
    return _courseImageView;
}

- (UIImageView *)playImageView{
    if (!_playImageView) {
        _playImageView = [UIImageView new];
        _playImageView.contentMode = UIViewContentModeScaleAspectFill;
        _playImageView.image = [UIImage imageNamed:@"test_paly_icon"];
    }
    return _playImageView;
}

- (UIView *)contentView{
    if (!_contentView) {
        _contentView = [UIView new];
        _contentView.backgroundColor = [UIColor.whiteColor colorWithAlphaComponent:0.68];
        MrkCornerMaskWithViewRadius(_contentView, ViewRadiusMake(0, 0, 20, 20));
    }
    return _contentView;
}

- (UILabel *)courseTitleLab {
    if (!_courseTitleLab) {
        _courseTitleLab = [[UILabel alloc] init];
        _courseTitleLab.textColor = UIColor.mrk_363A44_T1;
        _courseTitleLab.font = [UIFont systemFontOfSize:17 weight:UIFontWeightSemibold];
        _courseTitleLab.text = @"新手入门骑行指导课程";
    }
    return _courseTitleLab;
}

- (UILabel *)courseDetailLab {
    if (!_courseDetailLab) {
        _courseDetailLab = [[UILabel alloc] init];
        _courseDetailLab.textColor = UIColor.mrk_848A9B_T2;
        _courseDetailLab.font = [UIFont systemFontOfSize:13 weight:UIFontWeightMedium];
        _courseDetailLab.text = @"专业教练指导，快速掌握基础骑行动作要领";
    }
    return _courseDetailLab;
}

/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/

@end




@interface MRKTestMaskTimerView ()
@property (nonatomic, strong) UIImageView *timeImageView;
@end

@implementation MRKTestMaskTimerView
- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = [UIColor.blackColor colorWithAlphaComponent:0.6];
        
        [self addSubview:self.timeImageView];
        [self.timeImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(self.mas_centerY);
            make.centerX.equalTo(self.mas_centerX);
            make.height.mas_equalTo(WKDHPX(100));
        }];
    }
    return self;
}

- (void)setSeconds:(NSInteger)seconds{
    _seconds = seconds;
    
    if (seconds == 3) {
        self.timeImageView.image = [UIImage imageNamed:@"user_test_time3"];
    }else if (seconds == 2) {
        self.timeImageView.image = [UIImage imageNamed:@"user_test_time2"];
    }else if (seconds == 1) {
        self.timeImageView.image = [UIImage imageNamed:@"user_test_time1"];
    }else if (seconds == 0) {
        self.timeImageView.image = [UIImage imageNamed:@"user_test_go"];
    }
}

- (UIImageView *)timeImageView{
    if (!_timeImageView) {
        _timeImageView = [UIImageView new];
        _timeImageView.contentMode = UIViewContentModeScaleAspectFill;
        _timeImageView.autoresizingMask = UIViewAutoresizingFlexibleTopMargin | UIViewAutoresizingFlexibleLeftMargin;
    }
    return _timeImageView;
}

/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/
@end
