//
//  MRKUserTestingView.h
//  Student_IOS
//
//  Created by Junq on 2023/12/18.
//

#import <UIKit/UIKit.h>
#import "EquipmentDetialModel.h"
#import "NewUserEvaluationController.h"
#import "MRKDeviceModel.h"

NS_ASSUME_NONNULL_BEGIN

@class MRKTestingStartView;
@class MRKTestingProgressView;
@class MRKTestingEndView;

@interface MRKUserTestingView : UIView
@property (nonatomic, assign) NSInteger timeSeconds; ///倒计时长
@property (nonatomic, strong) EquipmentDetialModel *equipModel; ///设备配置数据
@property (nonatomic, strong) MRKDeviceModel *deviceDetailModel; ///设备详情
@property (nonatomic, assign) UserTestStatus testStatus; ///测评状态
///
@property (nonatomic, strong) MRKTestingStartView *startView;
@property (nonatomic, strong) MRKTestingProgressView *progressView;
@end



@interface MRKTestingStartView : UIView
@property (nonatomic, strong) EquipmentDetialModel *equipModel; ///设备详情数据
@property (nonatomic, strong) MRKDeviceModel *deviceDetailModel; ///设备详情
///
- (void)reloadMasonryConstraint;
- (void)reloadDisplayVipUI;
@end



@interface MRKTestingProgressView : UIView
@property (nonatomic, assign) int productType;
@property (nonatomic, strong) EquipmentDetialModel *equipModel; ///设备详情数据
@property (nonatomic, strong) MRKDeviceModel *deviceDetailModel; ///设备详情
@property (nonatomic, strong) BaseEquipDataModel *tyModel; ///设备上报数据
@property (nonatomic, assign) NSInteger timeSeconds; ///倒计时长
///
@property (nonatomic, strong) NSNumber *currentStatus;
@end



@interface MRKTestingEndView : UIView
@property (nonatomic, strong) MRKCourseModel *model;

@property (nonatomic, copy) NSString *productId;
@property (nonatomic, assign) BOOL experienceComplete; ///体验是否完成
@property (nonatomic, strong) EquipmentDetialModel *equipModel; ///设备详情数据
///
@property (nonatomic, copy) dispatch_block_t courseHandleBlock;
@property (nonatomic, copy) dispatch_block_t trainHandleBlock;
@end




@interface MRKTestingTopView : UIView
@property (nonatomic, assign) NSInteger totalSeconds; ///倒计时长
@property (nonatomic, assign) NSInteger timeSeconds; ///倒计时长
///
- (void)setStatusImage:(UIImage *)statusImage;
///
- (void)testingPrepare;
- (void)testingStart;
- (void)testingEnd;
- (void)testingDone;
@end




@interface MRKTestingItemView : UIView
@property (nonatomic, strong) UILabel *titleLab;
//@property (nonatomic, strong) UILabel *detailLab;

/// 名字
@property (nonatomic, strong) GradientLayerLabel *detailLab;
@end


@interface MRKTestingCourseView : UIView
@property (nonatomic, strong) UIImageView *courseImageView;
@property (nonatomic, strong) UILabel *courseTitleLab;
@property (nonatomic, strong) UILabel *courseDetailLab;
@end


@interface MRKTestMaskTimerView : UIView
@property (nonatomic, assign) NSInteger seconds; ///
@end

NS_ASSUME_NONNULL_END
