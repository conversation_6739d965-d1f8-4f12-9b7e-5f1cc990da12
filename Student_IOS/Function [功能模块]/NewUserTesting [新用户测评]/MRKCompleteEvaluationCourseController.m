//
//  MRKCompleteEvaluationCourseController.m
//  Student_IOS
//
//  Created by <PERSON><PERSON> on 2023/12/25.
//

#import "MRKCompleteEvaluationCourseController.h"
#import "MRKUserTestingView.h"
#import "MRKUserTestBreakAlert.h"
#import "MRKDeviceURLRequest.h"
#import "MRKTimerManager.h"
#import "MRKFreeTrainingController.h"
#import "MRKVideoExperienceController.h"
#import "MRKDeviceConnectAlertView.h"


@interface MRKCompleteEvaluationCourseController ()
@property (nonatomic, strong) MRKTestingTopView *testTopView;
@property (nonatomic, strong) MRKTestingEndView *testEndView;
@property (nonatomic, strong) UILabel *descripLab;
@property (nonatomic, strong) UIButton *stepBtn;
@property (nonatomic, strong) MRKCourseModel *model;
@property (nonatomic, strong) MRKCourseModel *trainingModel;
@property (nonatomic, strong) MRKDeviceModel *deviceModel;
@end

@implementation MRKCompleteEvaluationCourseController

/// 是否链接设备
- (BOOL)isConnectMachine{
    NSString *equipTypeId = self.productId;
    if ([BluetoothManager isConnectEquipmentType:equipTypeId]) {
        return YES;
    }
    return NO;
}

- (void)viewDidAppear:(BOOL)animated{
    [super viewDidAppear:animated];
    dispatch_async(dispatch_get_main_queue(), ^{
        [UIApplication sharedApplication].idleTimerDisabled = YES;
    });
}

- (void)viewDidDisappear:(BOOL)animated{
    [super viewDidDisappear:animated];
    dispatch_async(dispatch_get_main_queue(), ^{
        [UIApplication sharedApplication].idleTimerDisabled = NO;
    });
}

- (void)viewDidLoad {
    self.tracePageId = @"page_iexperience_accomplish";
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    self.view.backgroundColor = UIColorHex(#05182C);
    [self.mrkContentView removeFromSuperview];
    self.mrkContentView = nil;
    
    [self layoutSubviewUI];
    
    ///防止跨页面传值为空
    if(![self.productId isNotBlank]) {
        
        [MRKDeviceURLRequest requestMyDevice:@{
            @"productType" : @"1"
        } success:^(NSArray *data) {
            if (data.count > 0){
                MRKDeviceModel *model = [data firstObject];
                self.deviceModel = model;
                self.productId = model.productId;
            }
            self.testEndView.productId = self.productId;
            [self requestCourseInfo];
        } fail:^(NSError * data) { }];
    }else{
        self.testEndView.productId = self.productId;
        [self requestCourseInfo];
        
        [MRKDeviceURLRequest requestMyDevice:@{
            @"productType" : @"1"
        } success:^(NSArray *data) {
            if (data.count > 0){
                MRKDeviceModel *model = [data firstObject];
                self.deviceModel = model;
            }
        } fail:^(NSError * data) { }];
    }
}

///新手指引课
- (void)requestCourseInfo{
    [MRKBaseRequest mrkGetRequestUrl:@"/course/guide_course"
                             andParm:@{@"productId":self.productId?:@""}
            completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"request.responseObject====== %@", request.responseObject);
        id data = [request.responseObject valueForKeyPath:@"data"];
        MRKCourseModel *model = [MRKCourseModel modelWithJSON:data];
        self.model = model;
        self.testEndView.model = model;
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        
    }];
}

- (void)layoutSubviewUI{
    [self.view addSubview:self.testEndView];  ///中央区域
    ///
    if (self.experienceComplete) {
        [self.view addSubview:self.testTopView];  ///头部
        [self.testTopView setStatusImage:[UIImage imageNamed:@"user_test_done"]];
    }else{
        [self.view addSubview:self.descripLab];   ///头部
    }
    [self.view addSubview:self.stepBtn];      ///底部按钮
}

- (void)viewWillTransitionToSize:(CGSize)size withTransitionCoordinator:(id<UIViewControllerTransitionCoordinator>)coordinator {
    [super viewWillTransitionToSize:size withTransitionCoordinator:coordinator];
    
    [self.view layoutIfNeeded];
    [self.view setNeedsLayout];
}


- (void)viewDidLayoutSubviews {
    [super viewDidLayoutSubviews];
    
    UIInterfaceOrientation interfaceOrientation = [[UIApplication sharedApplication] statusBarOrientation];
    if (UIInterfaceOrientationIsLandscape(interfaceOrientation)) {
        [self.testEndView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(self.view.mas_centerY);
            make.centerX.equalTo(self.view.mas_centerX);
        }];
  
        if (self.experienceComplete) {
            [self.testTopView mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.bottom.equalTo(self.testEndView.mas_top);
                make.centerX.equalTo(self.testEndView.mas_centerX);
                make.width.mas_equalTo(WKDHPX(305));
                make.height.mas_equalTo(WKDHPX(156));
            }];
        }else{
            [self.descripLab mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.bottom.equalTo(self.testEndView.mas_top).offset(-35);
                make.centerX.equalTo(self.testEndView.mas_centerX);
            }];
        }
  
        [self.stepBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.view.mas_left).offset(50);
            make.right.equalTo(self.view.mas_right).offset(-50);
            make.height.mas_equalTo(WKDHPX(20));
            make.bottom.mas_equalTo(-(kSafeArea_Bottom + WKDHPX(20)));
        }];
    
    } else {
        [self.testEndView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(self.view.mas_centerY);
            make.centerX.equalTo(self.view.mas_centerX);
            make.width.mas_equalTo(WKDHPX(305));
        }];
        
        if (self.experienceComplete) {
            [self.testTopView mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.bottom.equalTo(self.testEndView.mas_top);
                make.centerX.equalTo(self.testEndView.mas_centerX);
                make.width.mas_equalTo(WKDHPX(305));
                make.height.mas_equalTo(WKDHPX(156));
            }];
        }else{
            [self.descripLab mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.bottom.equalTo(self.testEndView.mas_top).offset(-35);
                make.centerX.equalTo(self.testEndView.mas_centerX);
            }];
        }
        
        [self.stepBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.view.mas_left).offset(50);
            make.right.equalTo(self.view.mas_right).offset(-50);
            make.height.mas_equalTo(WKDHPX(20));
            make.bottom.mas_equalTo(-(kSafeArea_Bottom + WKDHPX(20)));
        }];
    }
}

- (UILabel *)descripLab {
    if (!_descripLab) {
        _descripLab = [[UILabel alloc] init];
        _descripLab.textColor = UIColorHex(#FFE8B9);
        _descripLab.font = [UIFont systemFontOfSize:WKDHPX(22) weight:UIFontWeightMedium];
        _descripLab.text = @"开启你的第一次正式训练吧";
        _descripLab.textAlignment = NSTextAlignmentCenter;
        _descripLab.numberOfLines = 0;
    }
    return _descripLab;
}
- (MRKTestingTopView *)testTopView{
    if (!_testTopView) {
        _testTopView = [[MRKTestingTopView alloc] init];
    }
    return _testTopView;
}
- (MRKTestingEndView *)testEndView{
    if (!_testEndView) {
        _testEndView = [[MRKTestingEndView alloc] init];
        _testEndView.backgroundColor = UIColor.whiteColor;
        _testEndView.layer.cornerRadius = 30;
        _testEndView.experienceComplete = self.experienceComplete;
        @weakify(self);
        _testEndView.courseHandleBlock = ^{
            [self_weak_ openCourseTraining];
        };
        _testEndView.trainHandleBlock = ^{
            [self_weak_ framdomTraining];
        };
    }
    return _testEndView;
}

///打开课程训练
- (void)openCourseTraining{
    if (self.model == nil) return;
    
    if (self.trainingModel != nil) {
        [self courseTraining];
        return;
    }
    
    [MBProgressHUD showLodingWithMessage:@"" view:self.view];
    [MRKBaseRequest mrkRequestType:YTKRequestMethodGET
                               url:@"/course/detail"
                           andParm:@{@"courseId": self.model.courseId ?: @"",
                                     @"terminal": @(kTerminal)}
          completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        [MBProgressHUD hideHUDForView:self.view];
        NSLog(@"______%@", request.responseObject);
        id data = [request.responseObject valueForKeyPath:@"data"];
        MRKCourseModel *model = [MRKCourseModel modelWithJSON:data];
        self.trainingModel = model;
        
        [self courseTraining];
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        [MBProgressHUD hideHUDForView:self.view];
    }];
}


- (void)courseTraining{
    ReportMrkLogParms(2, @"入门指导课", @"page_iexperience_accomplish", @"btn_introduction_course", nil, 0, nil);
    
    ///已经连接了设备
    if([BlueDataStorageManager isConnectDeviceWithProductID:self.productId]) {
        MRKVideoExperienceController *vc = [[MRKVideoExperienceController alloc] init];
        vc.liveModel = self.trainingModel;
        vc.hidesBottomBarWhenPushed = YES;
        vc.pageRelationPath = [[MRKLinkRouterManager sharedInstance] filterPathWithClass:self];
        [self.navigationController pushViewController:vc animated:YES];
        return;
    }

    
    [MBProgressHUD showLodingWithMessage:@"连接中..." view:self.view];
    NewConnectManager *cm = [NewConnectManager new];
    cm.connectMode = ManualDeviceConnectMode;
    cm.connectStatusBlock = ^(id data) {
        [MBProgressHUD hideHUDForView:self.view];
        NSLog(@"我是通过连接弹窗，连接%@的" , [data boolValue] ? @"成功" : @"失败");
        if ([data boolValue]) {
            
            ///再走一遍
            [self courseTraining];
        }else{
            [MBProgressHUD showMessage:@"连接失败, 请点击重试"];
        }
    };
    [cm connectDeviceModel:self.deviceModel];
}


///打开自由
- (void)framdomTraining{
    ReportMrkLogParms(2, @"自由训练", @"page_iexperience_accomplish", @"btn_exersice_free", nil, 0, nil);
    
    ///已经连接了设备
    if([BlueDataStorageManager isConnectDeviceWithProductID:self.productId]) {
        MRKFreeTrainingController *vc = [MRKFreeTrainingController new];
        vc.productId = self.productId;
        vc.pageRelationPath = [[MRKLinkRouterManager sharedInstance] filterPathWithClass:self];
        [self.navigationController pushViewController:vc animated:YES];
        return;
    }
    
    
    [MBProgressHUD showLodingWithMessage:@"连接中..." view:self.view];
    NewConnectManager *cm = [NewConnectManager new];
    cm.connectMode = ManualDeviceConnectMode;
    cm.connectStatusBlock = ^(id data) {
        [MBProgressHUD hideHUDForView:self.view];
        NSLog(@"我是通过连接弹窗，连接%@的" , [data boolValue] ? @"成功" : @"失败");
        if ([data boolValue]) {
            
            ///再走一遍
            [self framdomTraining];
        }else{
            [MBProgressHUD showMessage:@"连接失败, 请点击重试"];
        }
    };
    [cm connectDeviceModel:self.deviceModel];
  
}

- (UIButton *)stepBtn {
    if (!_stepBtn) {
        _stepBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_stepBtn setTitle:@"搞不动了，去首页逛逛" forState:UIControlStateNormal];
        [_stepBtn setTitleColor:UIColorHex(#848A9B) forState:UIControlStateNormal];
        _stepBtn.titleLabel.font = [UIFont systemFontOfSize:13 weight:UIFontWeightRegular];
        [_stepBtn addTarget:self action:@selector(quiteLoginAction) forControlEvents:UIControlEventTouchUpInside];
    }
    return _stepBtn;
}

- (void)quiteLoginAction {
    ///检查window根视图
    UIWindow *window = [UIApplication sharedApplication].delegate.window;
    if ([window.rootViewController isKindOfClass:[UITabBarController class]]){
        [self.navigationController popToRootViewControllerAnimated:NO];
        return;
    }

    if ([Login isLogin]){
        [[NSNotificationCenter defaultCenter] postNotificationName:kLogin_Notification object:UserInfo.userId];
    }
    ReportMrkLogParms(2, @"进入首页逛逛", @"page_iexperience_accomplish", @"btn_enter_homepage", nil, 0, nil);
}

- (BOOL)viewControllerIsNeedNavBar:(MRKBaseController *)viewController{
    return NO;
}

- (UIStatusBarStyle)navControllerStatusBarStyle:(MRKBaseController *)viewController{
    return UIStatusBarStyleLightContent;
}

/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/

@end



#import "SJRouter.h"
@interface MRKCompleteEvaluationCourseController (RouteHandler)<SJRouteHandler>
@end

@implementation MRKCompleteEvaluationCourseController (RouteHandler)
+ (NSString *)routePath {
    return @"router_novice_experience_finish";
}

+ (void)handleRequest:(SJRouteRequest *)request topViewController:(UIViewController *)topViewController completionHandler:(SJCompletionHandler)completionHandler {
    [MRKNewLinkLogic jumpCompleteExperienceComplete:[request.prts objectForKey:@"productId"] experienceComplete:[[request.prts objectForKey:@"experienceComplete"] boolValue]];
}

@end
