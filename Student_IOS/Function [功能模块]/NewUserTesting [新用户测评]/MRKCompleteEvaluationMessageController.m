//
//  MRKCompleteEvaluationMessageController.m
//  Student_IOS
//
//  Created by <PERSON><PERSON> on 2023/12/25.
//

#import "MRKCompleteEvaluationMessageController.h"
#import "MRKUserTestingView.h"
#import "MRKDeviceURLRequest.h"
#import "MRKCompleteEvaluationCourseController.h"


@interface MRKCompleteEvaluationMessageController ()
@property (nonatomic, strong) MRKTestingTopView *testTopView;
@property (nonatomic, strong) MRKUserTestingView *testingView;
@property (nonatomic, strong) UILabel *descripLab;
@property (nonatomic, strong) UIButton *testingBtn;
@property (nonatomic, strong) UIButton *stepBtn;
@end

@implementation MRKCompleteEvaluationMessageController

/// 是否链接设备
- (BOOL)isConnectMachine{
    NSString *equipTypeId = self.productId;
    if ([BluetoothManager isConnectEquipmentType:equipTypeId]) {
        return YES;
    }
    return NO;
}

- (void)viewDidAppear:(BOOL)animated{
    [super viewDidAppear:animated];
    dispatch_async(dispatch_get_main_queue(), ^{
        [UIApplication sharedApplication].idleTimerDisabled = YES;
    });
}

- (void)viewDidDisappear:(BOOL)animated{
    [super viewDidDisappear:animated];
    dispatch_async(dispatch_get_main_queue(), ^{
        [UIApplication sharedApplication].idleTimerDisabled = NO;
    });
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)viewDidLoad {
    
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    self.view.backgroundColor = UIColorHex(#05182C);
    [self.mrkContentView removeFromSuperview];
    self.mrkContentView = nil;
    
    [self layoutSubviewUI];
    
    ///防止跨页面传值为空
    if(![self.productId isNotBlank]) {
        NSDictionary *parms = @{
            @"productType" : @"1",
            @"size"        : @10,
            @"current"     : @1,
        };
        [MRKDeviceURLRequest requestMyDevice:parms success:^(NSArray *data) {
            if (data.count > 0){
                MRKDeviceModel *model = [data firstObject];
                self.productId = model.productId;
            }
        } fail:^(NSError * data) {
        }];
    }
}

- (void)layoutSubviewUI{
    ///头部
    [self.view addSubview:self.testTopView];
    [self.testTopView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view.mas_top).offset(kNavBarHeight);
        make.centerX.equalTo(self.view.mas_centerX);
        make.width.mas_equalTo(WKDHPX(305));
        make.height.mas_equalTo(WKDHPX(156));
    }];

    
    [self.view addSubview:self.descripLab];
    [self.descripLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.testTopView.mas_bottom).offset(WKDHPX(25));
        make.centerX.equalTo(self.testTopView.mas_centerX);
        make.width.mas_equalTo(WKDHPX(305));
        make.height.mas_equalTo(WKDHPX(50));
    }];
    
    ///开始体验btn
    [self.view addSubview:self.testingBtn];
    [self.testingBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.descripLab.mas_bottom).offset(WKDHPX(120));
        make.centerX.equalTo(self.view.mas_centerX);
        make.width.mas_equalTo(WKDHPX(280));
        make.height.mas_equalTo(WKDHPX(50));
    }];
    
    ///底部按钮
    [self.view addSubview:self.stepBtn];
    [self.stepBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.view.mas_left).offset(50);
        make.right.equalTo(self.view.mas_right).offset(-50);
        make.height.mas_equalTo(WKDHPX(20));
        make.bottom.mas_equalTo(-(kSafeArea_Bottom + WKDHPX(20)));
    }];
 
    
    if (self.experienceComplete){
        ///体验完成
        [self.testTopView testingDone];
        [self.testTopView setStatusImage:[UIImage imageNamed:@"user_test_done"]];
        [self.stepBtn setTitle:@"下次再填写" forState:UIControlStateNormal];
    }else{
        ///体验中止
        [self.testTopView testingEnd];
        [self.testTopView setStatusImage:[UIImage imageNamed:@"user_test_end"]];
        [self.stepBtn setTitle:@"暂不填写" forState:UIControlStateNormal];
    }
}

///断连弹窗
- (void)dismissConnectingActionAlert{
    
}


- (MRKTestingTopView *)testTopView{
    if (!_testTopView) {
        _testTopView = [[MRKTestingTopView alloc] init];
    }
    return _testTopView;
}

- (UILabel *)descripLab {
    if (!_descripLab) {
        _descripLab = [[UILabel alloc] init];
        _descripLab.textColor = UIColor.whiteColor;
        _descripLab.font = [UIFont systemFontOfSize:16];
        _descripLab.text = @"完善个人信息，了解你的身体现状，\n为你推荐更适合的训练内容";
        _descripLab.textAlignment = NSTextAlignmentCenter;
        _descripLab.numberOfLines = 0;
    }
    return _descripLab;
}

- (UIButton *)testingBtn {
    if (!_testingBtn) {
        _testingBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        _testingBtn.backgroundColor = UIColor.mainColor;
        [_testingBtn setTitle:@"填写个人基础信息" forState:UIControlStateNormal];
        [_testingBtn setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
        _testingBtn.titleLabel.font = [UIFont systemFontOfSize:16 weight:UIFontWeightMedium];
        [_testingBtn addTarget:self action:@selector(testStartAction) forControlEvents:UIControlEventTouchUpInside];
        _testingBtn.layer.cornerRadius = WKDHPX(50)/2;
        _testingBtn.layer.masksToBounds = YES;
    }
    return _testingBtn;
}

- (UIButton *)stepBtn {
    if (!_stepBtn) {
        _stepBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_stepBtn setTitle:@"暂不填写" forState:UIControlStateNormal];
        [_stepBtn setTitleColor:UIColorHex(#848A9B) forState:UIControlStateNormal];
        _stepBtn.titleLabel.font = [UIFont systemFontOfSize:13 weight:UIFontWeightRegular];
        [_stepBtn addTarget:self action:@selector(quiteLoginAction) forControlEvents:UIControlEventTouchUpInside];
    }
    return _stepBtn;
}

///个人信息填写页面
- (void)testStartAction {
    ///默认的flutter 信息填写页面
    SJRouteRequest *routerRequest = [[SJRouteRequest alloc] initWithPath:@"router_perfect_information" parameters:nil];
    [SJRouter.shared handleRequest:routerRequest completionHandler:nil];
}

///暂不填写
- (void)quiteLoginAction {
    
    ///完成互动
    NSString *resourceUrl = [self.pageRouterData valueForKeyPath:@"SKIP_PERFECT_INFORMATION.resourceUrl"];
    if ([resourceUrl isNotBlank]){
        NSDictionary *parms = nil;
        if ([resourceUrl isEqualToString:@"router_novice_experience_finish"] ){
            parms = @{@"productId":self.productId?:@"", @"experienceComplete":@(NO)};
        }
        [[MRKLinkRouterManager sharedInstance] routerResourcePath:resourceUrl
                                                 handleParameters:parms
                                                completionHandler:nil];
    }else{
        [MRKNewLinkLogic jumpCompleteExperienceComplete:self.productId experienceComplete:NO];
    }
}

- (BOOL)viewControllerIsNeedNavBar:(MRKBaseController *)viewController{
    return NO;
}

- (UIStatusBarStyle)navControllerStatusBarStyle:(MRKBaseController *)viewController{
    return UIStatusBarStyleLightContent;
}

/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/

@end


#import "SJRouter.h"
@interface MRKCompleteEvaluationMessageController (RouteHandler)<SJRouteHandler>
@end

@implementation MRKCompleteEvaluationMessageController (RouteHandler)
+ (NSString *)routePath {
    return @"router_novice_experience_finish2";
}

+ (void)handleRequest:(SJRouteRequest *)request topViewController:(UIViewController *)topViewController completionHandler:(SJCompletionHandler)completionHandler {
    MRKCompleteEvaluationMessageController *vc = [[MRKCompleteEvaluationMessageController alloc] init];
    vc.productId = [request.prts objectForKey:@"productId"];
    vc.experienceComplete = [[request.prts objectForKey:@"experienceComplete"] boolValue];
    [topViewController.navigationController pushViewController:vc animated:NO];
    if ([topViewController isKindOfClass:[NewUserEvaluationController class]]) {
        [topViewController.navigationController navigationRemoveContrller: topViewController];
    }
}

@end
