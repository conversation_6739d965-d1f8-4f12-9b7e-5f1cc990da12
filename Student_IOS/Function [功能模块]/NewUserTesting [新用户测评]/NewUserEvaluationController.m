//
//  MRKUserTestingController.m
//  Student_IOS
//
//  Created by Junq on 2023/12/18.
//

#import "NewUserEvaluationController.h"
#import "MRKUserTestingView.h"
#import "BlueDataDealManager.h"
#import "TreamillStatusManager.h"
#import "MRKUserTestBreakAlert.h"
#import "MRKDeviceURLRequest.h"
#import "MRKTimerManager.h"


#import "MRKCompleteEvaluationMessageController.h"
#import "MRKCompleteEvaluationCourseController.h"
#import "UIView+AZGradient.h"
#import "MRKAIPlanLogic.h"


@interface NewUserEvaluationController ()<BlueDataDealManagerDelegate>{
    BOOL isControl; ///是否调控中
    ///
    dispatch_time_t timeout;
    BOOL isPause;
    BOOL isSuspend;
    BOOL isFirst;
    RACDisposable *disopose;
    BOOL isStart;//跑步机进入后，获取状态之前设置为NO,
    /**
     获取到状态以后:status
     0a：不显示开始按钮，直接显示暂停页面 isStart=YES
     03:  不显示开始按钮  , isStart=YES
     02:  不显示开始按钮，直接显示倒计时页面 isStart=YES
     04:  不显示开始按钮，直接显示倒计时页面 isStart=YES
     01/00:  显示开始按钮，点击开始，发送之间就绪开始指令
     */
}
@property (nonatomic, assign) BOOL viewDidAppeared;
@property (nonatomic, assign) NSInteger testTotalTime;
@property (nonatomic, strong) MRKDeviceModel *bindModel;
@property (nonatomic, assign) UserTestStatus testStatus;
@property (nonatomic, assign) DEVICE_CONNECT_STATUS DEVICE_CONNECT_STATUS;
@property (nonatomic, strong) NSNumber *deviceStatus; ///记录当前运动设备连接状态

@property (nonatomic, strong) MRKTestingTopView *testTopView;
@property (nonatomic, strong) MRKUserTestingView *testingView;
@property (nonatomic, strong) MRKTestMaskTimerView *maskTimerView;
@property (nonatomic, assign) BOOL maskTimerDone;
@property (nonatomic, strong) MRKUserTestBreakAlert *disConnectAlert;
@property (nonatomic, strong) UIButton *testingBtn;
@property (nonatomic, strong) UIButton *stepBtn;
//@property (nonatomic, strong) UIButton *vipExperienceBtn;

@property (nonatomic, strong) BlueDataDealManager *dataManager;//蓝牙管理类
@property (nonatomic, strong) TreamillStatusManager *treamillStatusManager;//跑步机运动状态管理类
@property (nonatomic, strong) BaseEquipDataModel *tyModel;
@property (nonatomic, strong) RACDisposable *dispose;
@property (nonatomic, assign) BOOL vipExperienceDone;
@end

@implementation NewUserEvaluationController

/// 是否链接设备
- (BOOL)isConnectMachine{
    NSString *equipTypeId = self.productId;
    if ([BluetoothManager isConnectEquipmentType:equipTypeId]) {
        return YES;
    }
    return NO;
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    self.viewDidAppeared = YES;
    
    dispatch_async(dispatch_get_main_queue(), ^{
        [UIApplication sharedApplication].idleTimerDisabled = YES;
    });
}

- (void)viewDidDisappear:(BOOL)animated{
    [super viewDidDisappear:animated];
    self.viewDidAppeared = NO;
    
    dispatch_async(dispatch_get_main_queue(), ^{
        [UIApplication sharedApplication].idleTimerDisabled = NO;
    });
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
    ///结束倒计时
    [_dispose dispose];
    
    ///销毁datamanager
    [_dataManager endGetData];
    [_dataManager clearAll];
}


- (void)viewDidLoad {
    self.tracePageId = @"page_equipment_successful";
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    self.view.backgroundColor = UIColorHex(#05182C);
    [self.mrkContentView removeFromSuperview];
    self.mrkContentView = nil;
    
    
    self.testTotalTime = 60; ///总的测评时长
    self.testTopView.totalSeconds = 60;
    self.testStatus = UserTestPreStatus;
    self.testingView.testStatus = UserTestPreStatus;
    
    ///初始化状态
    BOOL connectDevice = self.isConnectMachine;
    NSLog(@"控制器 connectDevice ===  %@",connectDevice? @" Yes":@"no");
    self.DEVICE_CONNECT_STATUS = DeviceDisconnect;
    if ( connectDevice ) {
        BluetoothModel *bluetoothModel = [BlueDataStorageManager connectBMFromProductID:self.productId];
        self.bindModel = bluetoothModel.equipmentModel;
        self.DEVICE_CONNECT_STATUS = DeviceConnected;
        NSLog(@"控制器 connectDevice connectDevice ===  %@", self.bindModel.modelDescription);
    }
    
    [self layoutSubviewUI];
    [self getEquipmentDetial];
    
    if (self.productId.intValue == TreadmillEquipment) {
        [self treamillStatusNotification];
    }
    
    [self RACObserveTestStatus];
    [self testConfigTimer];
    
    //监听页面显示状态
    @weakify(self);
    [[RACObserve(self, viewDidAppeared) distinctUntilChanged] subscribeNext:^(NSNumber * x) {
        @strongify(self);
        if (x.boolValue) {
            [self updateConnectStatus];
        }
    }];
    
    ///蓝牙启动
    self.dataManager.delegate = self;
    [self.dataManager startGetData];
    
//    if ([self.source isEqualToString:@"goodGift"]) {// 新人好礼进来领取7天会员
//        [self vipExperienceAction];
//    }
}

/////领取7天会员体验卡
//- (void)vipExperienceAction {
//    [MBProgressHUD showLodingWithMessage:@"" view:self.view];
//    [MRKBaseRequest mrkRequestType:YTKRequestMethodPOST
//                               url:@"/user/user-member/reward?type=BIND_EQUIPMENT"
//                           andParm:nil
//          completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
//        [MBProgressHUD hideHUDForView:self.view];
//        
//        [MBProgressHUD showMessage:@"会员领取成功"];
//        [self.testingView.startView reloadDisplayVipUI];
//        self.vipExperienceDone = YES;
//        [self.view setNeedsLayout];
//        [self.view layoutIfNeeded];
//    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
//        [MBProgressHUD hideHUDForView:self.view];
//    }];
//}

- (void)layoutSubviewUI{
    [self.view addSubview:self.testingView];///中央区域
    [self.view addSubview:self.testTopView];  ///头部
    [self.view addSubview:self.testingBtn]; ///开始体验btn
//    [self.view addSubview:self.vipExperienceBtn]; ///开始体验btn
    [self.view addSubview:self.stepBtn];///底部按钮
    [self.view addSubview:self.maskTimerView];  ///倒计时view
}


- (void)viewWillTransitionToSize:(CGSize)size withTransitionCoordinator:(id<UIViewControllerTransitionCoordinator>)coordinator {
    [super viewWillTransitionToSize:size withTransitionCoordinator:coordinator];
    
    [self.view layoutIfNeeded];
    [self.view setNeedsLayout];
}


- (void)viewDidLayoutSubviews {
    [super viewDidLayoutSubviews];
    
    ///
    UIInterfaceOrientation interfaceOrientation = [[UIApplication sharedApplication] statusBarOrientation];
    if (UIInterfaceOrientationIsLandscape(interfaceOrientation)) {
        [self.testingView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(self.view.mas_centerY);
            make.centerX.equalTo(self.view.mas_centerX);
            make.width.mas_equalTo(WKDHPX(520));
            make.height.mas_equalTo(WKDHPX(310));
        }];
        [self.testTopView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(self.testingView.mas_top);
            make.centerX.equalTo(self.testingView.mas_centerX);
            make.width.mas_equalTo(WKDHPX(305));
            make.height.mas_equalTo(WKDHPX(156));
        }];
        
//        if (self.vipExperienceDone) {
//            self.vipExperienceBtn.hidden = YES;
            
            [self.testingBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.centerY.equalTo(self.testingView.mas_bottom);
                make.centerX.equalTo(self.view.mas_centerX);
                make.width.mas_equalTo(WKDHPX(305));
                make.height.mas_equalTo(WKDHPX(48));
            }];
//        }else{
//            [self.testingBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
//                make.centerY.equalTo(self.testingView.mas_bottom);
//                make.centerX.equalTo(self.view.mas_centerX).offset(-WKDHPX(100));
//                make.width.mas_equalTo(WKDHPX(104));
//                make.height.mas_equalTo(WKDHPX(50));
//            }];
//            [self.vipExperienceBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
//                make.centerY.equalTo(self.testingView.mas_bottom);
//                make.centerX.equalTo(self.view.mas_centerX).offset(WKDHPX(57));
//                make.width.mas_equalTo(WKDHPX(189));
//                make.height.mas_equalTo(WKDHPX(50));
//            }];
//        }

        [self.stepBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.view.mas_left).offset(50);
            make.right.equalTo(self.view.mas_right).offset(-50);
            make.height.mas_equalTo(WKDHPX(20));
            make.bottom.mas_equalTo(-(kSafeArea_Bottom + WKDHPX(20)));
        }];
        [self.maskTimerView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.edges.mas_equalTo(UIEdgeInsetsZero);
        }];
    } else {
        [self.testingView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(self.view.mas_centerY);
            make.centerX.equalTo(self.view.mas_centerX);
            make.width.mas_equalTo(WKDHPX(305));
            make.height.mas_equalTo(WKDHPX(365));
        }];
        [self.testTopView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(self.testingView.mas_top);
            make.centerX.equalTo(self.testingView.mas_centerX);
            make.width.mas_equalTo(WKDHPX(305));
            make.height.mas_equalTo(WKDHPX(156));
        }];
        
//        if (self.vipExperienceDone) {
//            self.vipExperienceBtn.hidden = YES;
            
            [self.testingBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(self.testingView.mas_bottom).offset(WKDHPX(50));
                make.centerX.equalTo(self.view.mas_centerX);
                make.width.mas_equalTo(WKDHPX(305));
                make.height.mas_equalTo(WKDHPX(48));
            }];
//        }else{
//            [self.testingBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
//                make.top.equalTo(self.testingView.mas_bottom).offset(WKDHPX(50));
//                make.centerX.equalTo(self.view.mas_centerX).offset(-WKDHPX(100));
//                make.width.mas_equalTo(WKDHPX(104));
//                make.height.mas_equalTo(WKDHPX(48));
//            }];
//            [self.vipExperienceBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
//                make.top.equalTo(self.testingView.mas_bottom).offset(WKDHPX(50));
//                make.centerX.equalTo(self.view.mas_centerX).offset(WKDHPX(57));
//                make.width.mas_equalTo(WKDHPX(189));
//                make.height.mas_equalTo(WKDHPX(48));
//            }];
//        }

        [self.stepBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.view.mas_left).offset(50);
            make.right.equalTo(self.view.mas_right).offset(-50);
            make.height.mas_equalTo(WKDHPX(20));
            make.bottom.mas_equalTo(-(kSafeArea_Bottom + WKDHPX(20)));
        }];
        [self.maskTimerView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.edges.mas_equalTo(UIEdgeInsetsZero);
        }];
    }
}



- (void)testConfigTimer {
    RAC(self.testTopView, timeSeconds) = RACObserve(self, testTotalTime);///绑定测评时长
    RAC(self.testingView, timeSeconds) = RACObserve(self, testTotalTime);///绑定测评时长
    ///
    @weakify(self);
    RACSignal *deallocSignal = [self rac_willDeallocSignal];
    self.dispose = [[[RACSignal interval:1.0 onScheduler:[RACScheduler scheduler]] takeUntil:deallocSignal] subscribeNext:^(NSDate * _Nullable x) {
         @strongify(self);
         dispatch_async(dispatch_get_main_queue(), ^{
             if (self.testStatus == UserTestingStatus) {
                 
                 ///跑步时延时4s
                 if (self.productId.intValue == TreadmillEquipment){
                     if (self.maskTimerDone) {
                         self.testTotalTime --;
                     }
                 }else{
                     self.testTotalTime --;
                 }
                 
                 ///结束本次测评
                 if (self.testTotalTime == 0) {
                     self.testStatus = UserTestEndStatus;
                     ///结束倒计时
                     [self.dispose dispose];
                 }
             }
        });
    }];
}


- (void)treamillStatusNotification {

    
    @weakify(self);
    [[RACObserve(self.treamillStatusManager, currentStatus) distinctUntilChanged] subscribeNext:^(NSNumber *x) {
        @strongify(self);
        if (!x) {return;}
        ///同步状态至view指令
        self.testingView.progressView.currentStatus = x;
        
        NSLog(@"treamillStatusManager__TREAMILL_STATUS===%@" ,x);
        dispatch_async(dispatch_get_main_queue(), ^{
            switch (x.intValue) {
                case DeviceStandbyStatus: case DeviceX1StandbyStatus:
                { ///待机中
                   
                } break;
                case DeviceCutDownStatus:
                {///启动中
                   
                } break;
                    
                case DeviceRuningStatus:
                {///运行中
                
                    self->isStart = YES;
                    self->isPause = NO;
                    [self treamDataUpdate];
                } break;
                    
                case DevicelSlowDownStatus:
                {///减速中
                    self->isStart = YES;
                    [self treamillSuspending];
                } break;
                    
                case DevicePauseStatus:
                {///暂停中
                 
                    self->isStart = YES;
                    self->isPause = YES;
                    [self treamillPauseSuccess];
                } break;
                default: break;
            }
        });
    }];
}



#pragma mark - 跑步机暂停成功
- (void)treamillPauseSuccess {
//    dispatch_async(dispatch_get_main_queue(), ^{
//        self.startButton.hidden = NO;
//        self.pauseButton.hidden = YES;
//        self.slowDownView.hidden = YES;
//
//        self.dataManager.tyModel.speed = @0;
//        self.dataManager.tyModel.gradient = @0;
//
//        self.freedomDataView.speed = @"0.0";
//        self.slopeView.slope = @"0";
//    });
}

#pragma mark - 跑步机开始更新数据，修改isPause状态
- (void)treamDataUpdate {
    NSLog(@"跑步机开始获取数据了");
//    [[NSNotificationCenter defaultCenter] removeObserver:self name:TreamillUpdateDataNotification object:nil];
//    isPause = NO;
//
//    dispatch_async(dispatch_get_main_queue(), ^{
//        self.startButton.hidden = YES;
//        self.pauseButton.hidden = NO;
//        self.slowDownView.hidden = YES;
//    });
}

#pragma mark - 跑步机 继续
- (void)goonAction {
//
//    isPause = NO;
//    [[kWindow viewWithTag:0x888] removeFromSuperview];
//    self.slowDownView.hidden = YES;
//    [[NSNotificationCenter defaultCenter] postNotificationName:TreamillGoOnSportNotification object:nil];
}

#pragma mark - 跑步机 暂停
- (void)treamillPauseAction:(id)sender {
    
//    ///如果设备在连接中 中断操作
//    if([self checkDeviceStatus]) {
//        return;
//    }
//    [self treamillSuspending];
//    [[NSNotificationCenter defaultCenter] postNotificationName:TreamillPauseSportNotification object:nil];
}

#pragma mark - 跑步机 暂停减速中
- (void)treamillSuspending {
    
//    if (isPause) {
//        return;
//    }
//
//    dispatch_async(dispatch_get_main_queue(), ^{
//        self.freedomDataView.speed = @"0.0";
//        self-> isPause =YES;
//
//        [kWindow bringSubviewToFront:self.slowDownView];
//        self.slowDownView.hidden = NO;
//    });
}



#pragma mark - 获取当前自由训练设备详情
- (void)setProductId:(NSString *)productId{
    _productId = productId;
    self.testingView.progressView.productType = productId.intValue;
}
- (void)getEquipmentDetial{
 
    if (!self.isConnectMachine) {
        [self updateDeviceStatus:DeviceDisconnect];
    }
    
    NSDictionary *parms = @{
        @"productType" : @"1",
        @"size" : @10,
        @"current" : @1 ,
        @"productId" : self.productId ? : @""
    };
    [MRKDeviceURLRequest requestMyDevice:parms success:^(NSArray *data) {
        if (data.count > 0){
            MRKDeviceModel *model = [data firstObject];
            self.testingView.deviceDetailModel = model;
            
            if (![self.productId isNotBlank]){
                self.productId = model.productId;
            }
            
            [self requestDeviceConfig:model];
        }
    } fail:^(NSError * data) {
        
        
    }];
}


- (void)requestDeviceConfig:(MRKDeviceModel *)model{
    NSDictionary *parms = @{
        @"equipName" : model.bluetoothName?:@"",
        @"oneLevelTypeId" : model.productId
    };
    [MRKBaseRequest mrkGetRequestUrl:@"/equip/equipment/equipmentInfoController/getEquipTypeInfoByName/v2"
                             andParm:parms
            completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"request.responseObject====== %@", request.responseObject);
        id data = [request.responseObject valueForKeyPath:@"data"];
        self.equipModel = [EquipmentDetialModel modelWithDictionary:data];
        self.equipModel.productID = model.productId;
        
        self.dataManager.eqModel = self.equipModel;
        self.testingView.equipModel = self.equipModel;
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {}];
}

#pragma mark ------------ 蓝牙数据

- (void)updateUI {
    dispatch_async(dispatch_get_main_queue(), ^{
        self.tyModel = self.dataManager.tyModel;
        self.testingView.progressView.tyModel = self.tyModel;
    });
}

- (void)updateDeviceStatus:(DEVICE_CONNECT_STATUS)status{
    NSLog(@"NewUserEvaluationController updateDeviceStatus === %@", @(status));
    if (self.DEVICE_CONNECT_STATUS == status) return;
    self.DEVICE_CONNECT_STATUS = status;
    
    if (status == DeviceConnected){
        BluetoothModel *bluetoothModel = [BlueDataStorageManager connectBMFromProductID:self.productId];
        self.bindModel = bluetoothModel.equipmentModel;
        NSLog(@"控制器 connectDevice connectStatusWithProductID ===  %@", self.bindModel.modelDescription);
        
        ///蓝牙数据获取 [让currentStatus刷新]
        [self.dataManager startGetData];
    }
  
    [self updateConnectStatus];
}

- (void)updateConnectStatus{
    ///页面没显示屏蔽交互
    if (!self.viewDidAppeared) {
        NSLog(@"updateConnectStatus ===== viewDidAppeared");
        return;
    }
    
    ///其他过程中屏蔽交互
    if (self.testStatus != UserTestingStatus && self.testStatus != UserTestPauseStatus ) {
        NSLog(@"updateConnectStatus ===== 其他过程中屏蔽交互");
        return;
    }
    
    NSLog(@"updateConnectStatus ===== 其他过程中屏蔽交互");
    ///设备未连接
    if ( self.DEVICE_CONNECT_STATUS  == DeviceDisconnect ) {
        ///测试中暂停
        if (self.testStatus == UserTestingStatus){
            self.testStatus = UserTestPauseStatus;
            
            ///弹窗连接设备
            dispatch_async(dispatch_get_main_queue(), ^{
                [self.disConnectAlert showIn:self.view];
            });
        }
        return;
    }
    
    ///弹窗消失
    if ( self.DEVICE_CONNECT_STATUS == DeviceConnected ) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [self.disConnectAlert hide];
        });
        
        [self.dataManager startGetData];
    }
    
    ///为了判断进来没连设备后
    if (self.equipModel == nil) {
//        [self getEquipmentDetial];
        return;
    }
}

///断连弹窗
- (void)dismissConnectingActionAlert{
    
}

- (MRKUserTestBreakAlert *)disConnectAlert{
    if (!_disConnectAlert) {
        _disConnectAlert = [[MRKUserTestBreakAlert alloc] init];
        @weakify(self);
        _disConnectAlert.endTestHandle = ^(NSInteger index) {
            @strongify(self);
            //            self.testStatus = UserTestEndStatus;///结束测评
            [self.testTopView testingEnd];
            
            ///结束倒计时
            [self.dispose dispose];
            
            ///销毁datamanager
            [self.dataManager endGetData];
            [self.dataManager clearAll];
            
            ///
            NSString *resourceUrl = [self.pageRouterData valueForKeyPath:@"NOVICE_EXPERIENCE_ERROR.resourceUrl"];
            if ([resourceUrl isNotBlank]){
                NSDictionary *parms = nil;
                if ([resourceUrl isEqualToString:@"router_novice_experience_finish"] ||
                    [resourceUrl isEqualToString:@"router_novice_experience_finish2"] ){
                    parms = @{@"productId":self.productId?:@"", @"experienceComplete":@(NO)};
                }
                [[MRKLinkRouterManager sharedInstance] routerResourcePath:resourceUrl
                                                         handleParameters:parms
                                                        completionHandler:nil];
            }else{
                [MRKNewLinkLogic jumpCompleteExperienceComplete:self.productId experienceComplete:NO];
            }
         
            if (self.testStatus == UserTestingStatus || self.testStatus == UserTestPauseStatus ){
                ReportMrkLogParms(2, @"数据交互", @"page_intercative_experience", @"btn_popclose_experience", nil, 0, nil);
            }else{
                ReportMrkLogParms(2, @"连接成功", @"page_equipment_successful", @"btn_popclose_experience", nil, 0, nil);
            }
        };
    }
    return _disConnectAlert;
}

- (BlueDataDealManager *)dataManager {
    if (!_dataManager) {
        _dataManager = [[BlueDataDealManager alloc] initWithType:self.productId source:self];
        _dataManager.delegate = self;
        _dataManager.isOpenAutoConnect = NO;
        _dataManager.autoConnectNeedLoading = YES;
        _dataManager.uploadType = SocketUploadType;
        _dataManager.isUItraTraining = YES;
    }
    return _dataManager;;
}

- (TreamillStatusManager *)treamillStatusManager {
    if (!_treamillStatusManager) {
        _treamillStatusManager = [TreamillStatusManager new];
    }
    return _treamillStatusManager;
}

- (MRKTestingTopView *)testTopView{
    if (!_testTopView) {
        _testTopView = [[MRKTestingTopView alloc] init];
    }
    return _testTopView;
}
- (MRKUserTestingView *)testingView{
    if (!_testingView) {
        _testingView = [[MRKUserTestingView alloc] init];
        _testingView.layer.cornerRadius = 32;
    }
    return _testingView;
}
- (MRKTestMaskTimerView *)maskTimerView{
    if (!_maskTimerView) {
        _maskTimerView = [[MRKTestMaskTimerView alloc] init];
        _maskTimerView.hidden = YES;
        _maskTimerView.alpha = 0;
    }
    return _maskTimerView;
}

- (UIButton *)testingBtn {
    if (!_testingBtn) {
        _testingBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        _testingBtn.backgroundColor = UIColorHex(#16D2E3);
        _testingBtn.traceEventId = @"btn_experience_start";
        [_testingBtn setTitle:@"开始体验" forState:UIControlStateNormal];
        [_testingBtn setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
        _testingBtn.titleLabel.font = [UIFont systemFontOfSize:16 weight:UIFontWeightMedium];
        [_testingBtn addTarget:self action:@selector(testStartAction) forControlEvents:UIControlEventTouchUpInside];
        _testingBtn.layer.cornerRadius = WKDHPX(48)/2;
        _testingBtn.layer.masksToBounds = YES;
    }
    return _testingBtn;
}

- (UIButton *)stepBtn {
    if (!_stepBtn) {
        _stepBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        _stepBtn.traceEventId = @"btn_close_experience";
        [_stepBtn setTitle:@"暂不体验" forState:UIControlStateNormal];
        [_stepBtn setTitleColor:UIColorHex(#848A9B) forState:UIControlStateNormal];
        _stepBtn.titleLabel.font = [UIFont systemFontOfSize:13 weight:UIFontWeightRegular];
        [_stepBtn addTarget:self action:@selector(quiteLoginAction) forControlEvents:UIControlEventTouchUpInside];
    }
    return _stepBtn;
}

//- (UIButton *)vipExperienceBtn {
//    if (!_vipExperienceBtn) {
//        _vipExperienceBtn = [UIButton buttonWithType:UIButtonTypeCustom];
//        _vipExperienceBtn.traceEventId = @"btn_close_experience";
//        [_vipExperienceBtn setBackgroundImage:[UIImage imageNamed:@"user_test_vipBtn"] forState:UIControlStateNormal];
//        [_vipExperienceBtn setImage:[UIImage imageNamed:@"user_test_vipimage"] forState:UIControlStateNormal];
//        [_vipExperienceBtn setTitle:@" 领取10天会员" forState:UIControlStateNormal];
//        [_vipExperienceBtn setTitleColor:UIColorHex(#672F15) forState:UIControlStateNormal];
//        _vipExperienceBtn.titleLabel.font = [UIFont systemFontOfSize:16 weight:UIFontWeightMedium];
//        [_vipExperienceBtn addTarget:self action:@selector(vipExperienceAction) forControlEvents:UIControlEventTouchUpInside];
//        _vipExperienceBtn.layer.cornerRadius = WKDHPX(48)/2;
//        _vipExperienceBtn.layer.masksToBounds = YES;
//    }
//    return _vipExperienceBtn;
//}

- (void)RACObserveTestStatus {
    @weakify(self);
    [[RACObserve(self, testStatus) distinctUntilChanged] subscribeNext:^(id x) {
        @strongify(self);
        if (x == nil) {return;}
        UserTestStatus status = (UserTestStatus)[x intValue];
        self.testingView.testStatus = status;
        switch (status) {
            case UserTestPreStatus: {
                self.testingBtn.hidden = NO;
//                self.vipExperienceBtn.hidden = NO;
                [self.stepBtn setTitle:@"暂不体验" forState:UIControlStateNormal];
                [self.testTopView testingPrepare];
            }  break;
            case UserTestingStatus: case UserTestPauseStatus: {
                self.testingBtn.hidden = YES;
//                self.vipExperienceBtn.hidden = YES;
                [self.stepBtn setTitle:@"结束体验" forState:UIControlStateNormal];
                [self.testTopView testingStart];
                
                ///埋点
                self.tracePageId = @"page_intercative_experience";
                self.currentAppearTime = NSDate.date; 
                ReportMrkLogParms(1, @"数据交互", @"page_intercative_experience", @"", nil, 0, nil);
            }  break;
            case UserTestEndStatus: {
                self.testingBtn.hidden = YES;
//                self.vipExperienceBtn.hidden = YES;
                [self.stepBtn setTitle:@"搞不动了，进入首页逛逛" forState:UIControlStateNormal];
                [self.testTopView testingDone];
                
                ///结束倒计时
                [self.dispose dispose];
                
                ///销毁datamanager
                [self.dataManager endGetData];
                [self.dataManager clearAll];
                
                ///完成互动
                NSString *resourceUrl = [self.pageRouterData valueForKeyPath:@"NOVICE_EXPERIENCE_FINISH.resourceUrl"];
                if ([resourceUrl isNotBlank]){
                    NSDictionary *parms = nil;
                    if ([resourceUrl isEqualToString:@"router_novice_experience_finish"] ||
                        [resourceUrl isEqualToString:@"router_novice_experience_finish2"] ){
                        parms = @{@"productId":self.productId?:@"", @"experienceComplete":@(YES)};
                    }
                    [[MRKLinkRouterManager sharedInstance] routerResourcePath:resourceUrl
                                                             handleParameters:parms
                                                            completionHandler:nil];
                }else{
                    [MRKNewLinkLogic jumpCompleteExperienceComplete:self.productId experienceComplete:YES];
                }
            }  break;
            default:
                break;
        }
    }];
}


- (void)testStartAction {
    self.maskTimerDone = NO;
    self.testStatus = UserTestingStatus;
    [self updateConnectStatus];
    
    ///跑步机开启倒计时
    if (self.productId.intValue == TreadmillEquipment) {
        [self.maskTimerView setHiddenAnimated:NO];
        
        ///待机状态启动跑步机
        if (self.treamillStatusManager.currentStatus.intValue == DeviceStandbyStatus ||
            self.treamillStatusManager.currentStatus.intValue == DeviceX1StandbyStatus) {
            [self.treamillStatusManager treamillStart];
        }else if (self.treamillStatusManager.currentStatus.intValue == DevicePauseStatus) {
            [self.treamillStatusManager treamillGoon];
        }
        
        @weakify(self);
        TimerGCDWithInterval(1, 4, ^(NSInteger count) {
            @strongify(self);
            self.maskTimerView.seconds = 4 -count;
        }, ^{
            @strongify(self);
            [self.maskTimerView setHiddenAnimated:YES];
            self.maskTimerDone = YES;
        });
    }
}



///暂不体验
- (void)quiteLoginAction {
    ///
    ///结束倒计时
    [self.dispose dispose];
    
    ///销毁datamanager
    [self.dataManager endGetData];
    [self.dataManager clearAll];
    
    
    NSString *resourceUrl = [self.pageRouterData valueForKeyPath:@"SKIP_SUCCESS.resourceUrl"];
    if ([resourceUrl isNotBlank]){
        NSDictionary *parms = nil;
        if ([resourceUrl isEqualToString:@"router_novice_experience_finish"] ||
            [resourceUrl isEqualToString:@"router_novice_experience_finish2"] ){
            parms = @{@"productId":self.productId?:@"", @"experienceComplete":@(NO)};
        }
        [[MRKLinkRouterManager sharedInstance] routerResourcePath:resourceUrl
                                                 handleParameters:parms
                                                completionHandler:nil];

    } else {
        [MRKNewLinkLogic jumpCompleteExperienceComplete:self.productId experienceComplete:NO];
    }
}

- (BOOL)viewControllerIsNeedNavBar:(MRKBaseController *)viewController{
    return NO;
}

- (UIStatusBarStyle)navControllerStatusBarStyle:(MRKBaseController *)viewController{
    return UIStatusBarStyleLightContent;
}

/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/

@end



#import "SJRouter.h"
@interface NewUserEvaluationController (RouteHandler)<SJRouteHandler>
@end

@implementation NewUserEvaluationController (RouteHandler)
+ (NSString *)routePath {
    return @"router_novice_experience_start";
}

+ (void)handleRequest:(SJRouteRequest *)request topViewController:(UIViewController *)topViewController completionHandler:(SJCompletionHandler)completionHandler {
    NewUserEvaluationController *vc = [[NewUserEvaluationController alloc] init];
    vc.productId = [request.prts objectForKey:@"productId"];
//    vc.source = [request.prts objectForKey:@"source"];
    [topViewController.navigationController pushViewController:vc animated:YES];
}

@end

