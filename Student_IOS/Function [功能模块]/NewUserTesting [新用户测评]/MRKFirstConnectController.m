//
//  MRKFirstConnectController.m
//  Student_IOS
//
//  Created by <PERSON><PERSON> on 2023/12/28.
//

#import "MRKFirstConnectController.h"
#import "MRKUserTestingView.h"
#import "MRKDeviceURLRequest.h"
#import "MRKAIPlanAPIClient.h"

@interface MRKFirstConnectController ()
@property (nonatomic, assign) BOOL viewDidAppeared;
@property (nonatomic, strong) MRKTestingTopView *testTopView;
@property (nonatomic, strong) MRKUserTestingView *testingView;
@property (nonatomic, strong) UIButton *testingBtn;
@property (nonatomic, assign) BOOL hasPlan;
@end

@implementation MRKFirstConnectController

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
}

- (void)viewDidDisappear:(BOOL)animated{
    [super viewDidDisappear:animated];
}

- (void)viewDidLoad {
    self.tracePageId = @"page_equipment_successful";
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    self.view.backgroundColor = UIColorHex(#05182C);
    [self.mrkContentView removeFromSuperview];
    self.mrkContentView = nil;
    
    [self layoutSubviewUI];
    [self getEquipmentDetial];
}
- (void)layoutSubviewUI{
    [self.view addSubview:self.testingView];///中央区域
    [self.view addSubview:self.testTopView];  ///头部
    [self.view addSubview:self.testingBtn]; ///开始体验btn
}


- (void)viewWillTransitionToSize:(CGSize)size withTransitionCoordinator:(id<UIViewControllerTransitionCoordinator>)coordinator {
    [super viewWillTransitionToSize:size withTransitionCoordinator:coordinator];
    
    [self.view layoutIfNeeded];
    [self.view setNeedsLayout];
}


- (void)viewDidLayoutSubviews {
    [super viewDidLayoutSubviews];

    [self.testingView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.view.mas_centerY);
        make.centerX.equalTo(self.view.mas_centerX);
        make.width.mas_equalTo(WKDHPX(305));
        make.height.mas_equalTo(WKDHPX(305));
    }];
    [self.testTopView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.testingView.mas_top);
        make.centerX.equalTo(self.testingView.mas_centerX);
        make.width.mas_equalTo(WKDHPX(305));
        make.height.mas_equalTo(WKDHPX(156));
    }];
    [self.testingBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.view.mas_bottom).offset(-WKDHPX(20)-kSafeArea_Bottom);
        make.centerX.equalTo(self.view.mas_centerX);
        make.width.mas_equalTo(WKDHPX(280));
        make.height.mas_equalTo(WKDHPX(50));
    }];
}



#pragma mark - 获取当前自由训练设备详情

- (void)getEquipmentDetial{
    NSDictionary *parms = @{
        @"productType" : @"1",
        @"size" : @10,
        @"current" : @1 ,
        @"productId" : self.productId ? : @"6"
    };
    [MRKDeviceURLRequest requestMyDevice:parms success:^(NSArray *data) {
        if (data.count > 0){
            MRKDeviceModel *model = [data firstObject];
            self.testingView.deviceDetailModel = model;
        }
    } fail:^(NSError * data) {
        
        
    }];
    [MRKAIPlanAPIClient aiplanOverviewSuccess:^(id data) {
        MRKPlanOverViewModel *item = (MRKPlanOverViewModel *)data;
        self.hasPlan = item.currentPlan || item.isProcess;
        [self.testingBtn setTitle:self.hasPlan ? @"去首页逛逛" : @"继续" forState:UIControlStateNormal];
    } failure:^(id data) {
        self.hasPlan = NO;
        [self.testingBtn setTitle:@"去首页逛逛" forState:UIControlStateNormal];
    }];
}



- (MRKTestingTopView *)testTopView{
    if (!_testTopView) {
        _testTopView = [[MRKTestingTopView alloc] init];
    }
    return _testTopView;
}
- (MRKUserTestingView *)testingView{
    if (!_testingView) {
        _testingView = [[MRKUserTestingView alloc] init];
        _testingView.layer.cornerRadius = 30;
        _testingView.testStatus = UserTestPreStatus;
        
        _testingView.layer.shadowColor = [UIColor blackColor].CGColor;
        _testingView.layer.shadowOpacity = .2;
        _testingView.layer.shadowRadius = 5;
        _testingView.layer.shadowOffset = CGSizeMake(5, 5);
        
        [_testingView.startView  reloadMasonryConstraint];
    }
    return _testingView;
}

- (UIButton *)testingBtn {
    if (!_testingBtn) {
        _testingBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        _testingBtn.backgroundColor = UIColor.mainColor;
        [_testingBtn setTitle:@"去首页逛逛" forState:UIControlStateNormal];
        [_testingBtn setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
        _testingBtn.titleLabel.font = [UIFont systemFontOfSize:16 weight:UIFontWeightMedium];
        [_testingBtn addTarget:self action:@selector(testStartAction) forControlEvents:UIControlEventTouchUpInside];
        _testingBtn.layer.cornerRadius = WKDHPX(50)/2;
        _testingBtn.layer.masksToBounds = YES;
    }
    return _testingBtn;
}

- (void)testStartAction {
    if (self.hasPlan) {// 有AI计划走老逻辑
        ///检查window根视图
        UIWindow *window = [UIApplication sharedApplication].delegate.window;
        if ([window.rootViewController isKindOfClass:[UITabBarController class]]){
            [self.navigationController popToRootViewControllerAnimated:NO];
            return;
        }
        if ([Login isLogin]){
            [[NSNotificationCenter defaultCenter] postNotificationName:kLogin_Notification object:UserInfo.userId];
        }
    } else { // 走定制计划页——徒手计划样式
        [MRKNewLinkLogic jumpSmallConnectSuccess:self.productId];
    }
}

- (BOOL)viewControllerIsNeedNavBar:(MRKBaseController *)viewController{
    return NO;
}

/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/

@end


