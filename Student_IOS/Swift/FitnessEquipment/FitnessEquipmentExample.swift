//
//  FitnessEquipmentExample.swift
//  Student_IOS
//
//  Created by merit on 2025/8/29.
//  FitnessEquipment 使用示例
//

import UIKit

/// FitnessEquipment 使用示例控制器
class FitnessEquipmentExampleViewController: UIViewController {
    
    private let scrollView = UIScrollView()
    private let stackView = UIStackView()
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupExamples()
    }
    
    private func setupUI() {
        title = "健身器材示例"
        view.backgroundColor = .systemBackground
        
        // 滚动视图
        scrollView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(scrollView)
        
        // 堆栈视图
        stackView.axis = .vertical
        stackView.spacing = 20
        stackView.translatesAutoresizingMaskIntoConstraints = false
        scrollView.addSubview(stackView)
        
        // 设置约束
        NSLayoutConstraint.activate([
            scrollView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            scrollView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            scrollView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            scrollView.bottomAnchor.constraint(equalTo: view.bottomAnchor),
            
            stackView.topAnchor.constraint(equalTo: scrollView.topAnchor, constant: 20),
            stackView.leadingAnchor.constraint(equalTo: scrollView.leadingAnchor, constant: 20),
            stackView.trailingAnchor.constraint(equalTo: scrollView.trailingAnchor, constant: -20),
            stackView.bottomAnchor.constraint(equalTo: scrollView.bottomAnchor, constant: -20),
            stackView.widthAnchor.constraint(equalTo: scrollView.widthAnchor, constant: -40)
        ])
    }
    
    private func setupExamples() {
        // 1. 基本信息展示
        addSection("📱 基本信息展示") {
            self.createBasicInfoExample()
        }
        
        // 2. 图标展示
        addSection("🎨 图标展示") {
            self.createIconExample()
        }
        
        // 3. IconFont 展示
        addSection("🔤 IconFont 展示") {
            self.createIconFontExample()
        }
        
        // 4. 分类展示
        addSection("📂 分类展示") {
            self.createCategoryExample()
        }
        
        // 5. 搜索功能
        addSection("🔍 搜索功能") {
            self.createSearchExample()
        }
        
        // 6. 运动数据
        addSection("📊 运动数据") {
            self.createDataExample()
        }
    }
    
    private func addSection(_ title: String, content: () -> UIView) {
        // 添加标题
        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = UIFont.boldSystemFont(ofSize: 18)
        titleLabel.textColor = .label
        stackView.addArrangedSubview(titleLabel)
        
        // 添加内容
        let contentView = content()
        stackView.addArrangedSubview(contentView)
    }
    
    // MARK: - 示例内容创建
    
    private func createBasicInfoExample() -> UIView {
        let containerView = createContainerView()
        let contentStack = createContentStackView()
        containerView.addSubview(contentStack)
        
        // 选择几个器材展示基本信息
        let equipments: [FitnessEquipment] = [.treadmill, .stationaryBike, .massageGun]
        
        for equipment in equipments {
            let equipmentView = createEquipmentInfoView(equipment)
            contentStack.addArrangedSubview(equipmentView)
        }
        
        setupContentConstraints(contentStack, in: containerView)
        return containerView
    }
    
    private func createIconExample() -> UIView {
        let containerView = createContainerView()
        let contentStack = createContentStackView()
        containerView.addSubview(contentStack)
        
        let equipment = FitnessEquipment.treadmill
        
        // 创建图标展示行
        let iconStyles: [(IconStyle, String)] = [
            (.white, "白色图标"),
            (.black, "黑色图标"),
            (.colored, "彩色图标"),
            (.outline, "线条图标")
        ]
        
        for (style, styleName) in iconStyles {
            let iconView = createIconStyleView(equipment: equipment, style: style, styleName: styleName)
            contentStack.addArrangedSubview(iconView)
        }
        
        setupContentConstraints(contentStack, in: containerView)
        return containerView
    }
    
    private func createIconFontExample() -> UIView {
        let containerView = createContainerView()
        let contentStack = createContentStackView()
        containerView.addSubview(contentStack)
        
        let equipments: [FitnessEquipment] = [.treadmill, .stationaryBike, .kettlebell]
        
        for equipment in equipments {
            let iconFontView = createIconFontView(equipment)
            contentStack.addArrangedSubview(iconFontView)
        }
        
        setupContentConstraints(contentStack, in: containerView)
        return containerView
    }
    
    private func createCategoryExample() -> UIView {
        let containerView = createContainerView()
        let contentStack = createContentStackView()
        containerView.addSubview(contentStack)
        
        // 按分类展示器材
        for category in FitnessEquipmentCategory.allCases {
            let categoryView = createCategoryView(category)
            contentStack.addArrangedSubview(categoryView)
        }
        
        setupContentConstraints(contentStack, in: containerView)
        return containerView
    }
    
    private func createSearchExample() -> UIView {
        let containerView = createContainerView()
        let contentStack = createContentStackView()
        containerView.addSubview(contentStack)
        
        // 搜索示例
        let searchTerms = ["跑步", "bike", "力量"]
        
        for term in searchTerms {
            let results = FitnessEquipment.search(by: term)
            let searchView = createSearchResultView(term: term, results: results)
            contentStack.addArrangedSubview(searchView)
        }
        
        setupContentConstraints(contentStack, in: containerView)
        return containerView
    }
    
    private func createDataExample() -> UIView {
        let containerView = createContainerView()
        let contentStack = createContentStackView()
        containerView.addSubview(contentStack)
        
        let equipments: [FitnessEquipment] = [.treadmill, .stationaryBike, .jumpRope]
        
        for equipment in equipments {
            let dataView = createEquipmentDataView(equipment)
            contentStack.addArrangedSubview(dataView)
        }
        
        setupContentConstraints(contentStack, in: containerView)
        return containerView
    }
    
    // MARK: - 视图创建辅助方法
    
    private func createContainerView() -> UIView {
        let view = UIView()
        view.backgroundColor = .secondarySystemBackground
        view.layer.cornerRadius = 12
        view.layer.masksToBounds = true
        return view
    }
    
    private func createContentStackView() -> UIStackView {
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 12
        stackView.translatesAutoresizingMaskIntoConstraints = false
        return stackView
    }
    
    private func setupContentConstraints(_ contentStack: UIStackView, in container: UIView) {
        NSLayoutConstraint.activate([
            contentStack.topAnchor.constraint(equalTo: container.topAnchor, constant: 16),
            contentStack.leadingAnchor.constraint(equalTo: container.leadingAnchor, constant: 16),
            contentStack.trailingAnchor.constraint(equalTo: container.trailingAnchor, constant: -16),
            contentStack.bottomAnchor.constraint(equalTo: container.bottomAnchor, constant: -16)
        ])
    }
    
    private func createEquipmentInfoView(_ equipment: FitnessEquipment) -> UIView {
        let view = UIView()
        
        let nameLabel = UILabel()
        nameLabel.text = "\(equipment.displayName) (\(equipment.englishName))"
        nameLabel.font = UIFont.boldSystemFont(ofSize: 16)
        nameLabel.textColor = equipment.themeColor
        
        let descLabel = UILabel()
        descLabel.text = equipment.description
        descLabel.font = UIFont.systemFont(ofSize: 14)
        descLabel.textColor = .secondaryLabel
        descLabel.numberOfLines = 0
        
        nameLabel.translatesAutoresizingMaskIntoConstraints = false
        descLabel.translatesAutoresizingMaskIntoConstraints = false
        
        view.addSubview(nameLabel)
        view.addSubview(descLabel)
        
        NSLayoutConstraint.activate([
            nameLabel.topAnchor.constraint(equalTo: view.topAnchor),
            nameLabel.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            nameLabel.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            
            descLabel.topAnchor.constraint(equalTo: nameLabel.bottomAnchor, constant: 4),
            descLabel.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            descLabel.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            descLabel.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])
        
        return view
    }
    
    private func createIconStyleView(equipment: FitnessEquipment, style: IconStyle, styleName: String) -> UIView {
        let view = UIView()
        
        let iconImageView = UIImageView()
        iconImageView.image = equipment.icon(for: style) ?? UIImage(systemName: "questionmark.circle")
        iconImageView.contentMode = .scaleAspectFit
        iconImageView.backgroundColor = style == .white ? .black : .clear
        iconImageView.layer.cornerRadius = 4
        
        let nameLabel = UILabel()
        nameLabel.text = "\(styleName): \(equipment.iconName(for: style))"
        nameLabel.font = UIFont.systemFont(ofSize: 14)
        nameLabel.textColor = .label
        
        iconImageView.translatesAutoresizingMaskIntoConstraints = false
        nameLabel.translatesAutoresizingMaskIntoConstraints = false
        
        view.addSubview(iconImageView)
        view.addSubview(nameLabel)
        
        NSLayoutConstraint.activate([
            iconImageView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            iconImageView.centerYAnchor.constraint(equalTo: view.centerYAnchor),
            iconImageView.widthAnchor.constraint(equalToConstant: 32),
            iconImageView.heightAnchor.constraint(equalToConstant: 32),
            
            nameLabel.leadingAnchor.constraint(equalTo: iconImageView.trailingAnchor, constant: 12),
            nameLabel.centerYAnchor.constraint(equalTo: view.centerYAnchor),
            nameLabel.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            
            view.heightAnchor.constraint(equalToConstant: 44)
        ])
        
        return view
    }
    
    private func createIconFontView(_ equipment: FitnessEquipment) -> UIView {
        let view = UIView()
        
        let iconLabel = equipment.createIconFontLabel(fontSize: 24, color: equipment.themeColor)
        
        let infoLabel = UILabel()
        infoLabel.text = "\(equipment.displayName)\nUnicode: \(equipment.iconFontUnicode)\nHex: \(equipment.iconFontHex)"
        infoLabel.font = UIFont.systemFont(ofSize: 12)
        infoLabel.textColor = .secondaryLabel
        infoLabel.numberOfLines = 0
        
        iconLabel.translatesAutoresizingMaskIntoConstraints = false
        infoLabel.translatesAutoresizingMaskIntoConstraints = false
        
        view.addSubview(iconLabel)
        view.addSubview(infoLabel)
        
        NSLayoutConstraint.activate([
            iconLabel.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            iconLabel.centerYAnchor.constraint(equalTo: view.centerYAnchor),
            iconLabel.widthAnchor.constraint(equalToConstant: 40),
            iconLabel.heightAnchor.constraint(equalToConstant: 40),
            
            infoLabel.leadingAnchor.constraint(equalTo: iconLabel.trailingAnchor, constant: 12),
            infoLabel.centerYAnchor.constraint(equalTo: view.centerYAnchor),
            infoLabel.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            
            view.heightAnchor.constraint(greaterThanOrEqualToConstant: 50)
        ])
        
        return view
    }
    
    private func createCategoryView(_ category: FitnessEquipmentCategory) -> UIView {
        let view = UIView()
        
        let titleLabel = UILabel()
        titleLabel.text = category.rawValue
        titleLabel.font = UIFont.boldSystemFont(ofSize: 14)
        titleLabel.textColor = .label
        
        let equipmentList = FitnessEquipment.equipment(in: category)
        let equipmentNames = equipmentList.map { $0.displayName }.joined(separator: ", ")
        
        let listLabel = UILabel()
        listLabel.text = equipmentNames
        listLabel.font = UIFont.systemFont(ofSize: 12)
        listLabel.textColor = .secondaryLabel
        listLabel.numberOfLines = 0
        
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        listLabel.translatesAutoresizingMaskIntoConstraints = false
        
        view.addSubview(titleLabel)
        view.addSubview(listLabel)
        
        NSLayoutConstraint.activate([
            titleLabel.topAnchor.constraint(equalTo: view.topAnchor),
            titleLabel.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            titleLabel.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            
            listLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 4),
            listLabel.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            listLabel.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            listLabel.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])
        
        return view
    }
    
    private func createSearchResultView(term: String, results: [FitnessEquipment]) -> UIView {
        let view = UIView()
        
        let searchLabel = UILabel()
        searchLabel.text = "搜索 '\(term)' 找到 \(results.count) 个结果:"
        searchLabel.font = UIFont.boldSystemFont(ofSize: 14)
        searchLabel.textColor = .label
        
        let resultNames = results.map { $0.displayName }.joined(separator: ", ")
        let resultLabel = UILabel()
        resultLabel.text = resultNames.isEmpty ? "无结果" : resultNames
        resultLabel.font = UIFont.systemFont(ofSize: 12)
        resultLabel.textColor = .secondaryLabel
        resultLabel.numberOfLines = 0
        
        searchLabel.translatesAutoresizingMaskIntoConstraints = false
        resultLabel.translatesAutoresizingMaskIntoConstraints = false
        
        view.addSubview(searchLabel)
        view.addSubview(resultLabel)
        
        NSLayoutConstraint.activate([
            searchLabel.topAnchor.constraint(equalTo: view.topAnchor),
            searchLabel.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            searchLabel.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            
            resultLabel.topAnchor.constraint(equalTo: searchLabel.bottomAnchor, constant: 4),
            resultLabel.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            resultLabel.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            resultLabel.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])
        
        return view
    }
    
    private func createEquipmentDataView(_ equipment: FitnessEquipment) -> UIView {
        let view = UIView()
        
        let nameLabel = UILabel()
        nameLabel.text = equipment.displayName
        nameLabel.font = UIFont.boldSystemFont(ofSize: 14)
        nameLabel.textColor = equipment.themeColor
        
        let dataText = """
        卡路里/分钟: \(equipment.estimatedCaloriesPerMinute)
        支持心率监测: \(equipment.supportsHeartRateMonitoring ? "是" : "否")
        支持卡路里追踪: \(equipment.supportsCalorieTracking ? "是" : "否")
        """
        
        let dataLabel = UILabel()
        dataLabel.text = dataText
        dataLabel.font = UIFont.systemFont(ofSize: 12)
        dataLabel.textColor = .secondaryLabel
        dataLabel.numberOfLines = 0
        
        nameLabel.translatesAutoresizingMaskIntoConstraints = false
        dataLabel.translatesAutoresizingMaskIntoConstraints = false
        
        view.addSubview(nameLabel)
        view.addSubview(dataLabel)
        
        NSLayoutConstraint.activate([
            nameLabel.topAnchor.constraint(equalTo: view.topAnchor),
            nameLabel.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            nameLabel.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            
            dataLabel.topAnchor.constraint(equalTo: nameLabel.bottomAnchor, constant: 4),
            dataLabel.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            dataLabel.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            dataLabel.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])
        
        return view
    }
}

// MARK: - 使用示例代码

extension FitnessEquipmentExampleViewController {
    
    /// 演示基本使用方法
    func demonstrateBasicUsage() {
        // 1. 基本信息获取
        let treadmill = FitnessEquipment.treadmill
        print("器材名称: \(treadmill.displayName)")
        print("英文名称: \(treadmill.englishName)")
        print("描述: \(treadmill.description)")
        print("分类: \(treadmill.category.rawValue)")
        
        // 2. 图标使用
        let whiteIcon = treadmill.icon(for: .white)
        let blackIcon = treadmill.icon(for: .black)
        
        // 3. IconFont 使用
        let iconFontLabel = treadmill.createIconFontLabel(fontSize: 24, color: .blue)
        
        // 4. 主题色使用
        let themeColor = treadmill.themeColor
        let lightThemeColor = treadmill.lightThemeColor
        
        // 5. 分类查询
        let cardioEquipment = FitnessEquipment.cardioEquipment
        let strengthEquipment = FitnessEquipment.strengthEquipment
        
        // 6. 搜索功能
        let searchResults = FitnessEquipment.search(by: "跑步")
        
        // 7. 运动数据
        let calories = treadmill.estimatedCaloriesPerMinute
        let supportsHeartRate = treadmill.supportsHeartRateMonitoring
    }
}
