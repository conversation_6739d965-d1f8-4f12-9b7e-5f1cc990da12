//
//  FitnessEquipment.swift
//  Student_IOS
//
//  Created by Junq on 2025/8/28.
//


import UIKit

enum FitnessEquipment: Int, CaseIterable {
    case stationaryBike   = 1    // 动感单车
    case treadmill        = 2    // 跑步机
    case smallEquipment   = 3    // 小件
    case rowingMachine    = 5    // 划船机
    case elliptical       = 6    // 椭圆机
    case stairClimber     = 7    // 爬楼机
    case massageGun       = 9    // 筋膜枪
    case jumpRope         = 10   // 跳绳
    case multiGym         = 11   // 力量站（多功能综合训练器）
    case kettlebell       = 12   // 壶铃
    case hulaHoop         = 21   // 呼啦圈
    case flexiBar         = 27   // 飞力士棒
    case other            = 32   // 其他
    case bodyFatScale     = 41   // 体脂秤
    case heartRateMonitor = 100  // 心率带
}

extension FitnessEquipment {
    
    /// 名称
    var displayName: String {
        switch self {
        case .stationaryBike: return "动感单车"
        case .treadmill: return "跑步机"
        case .rowingMachine: return "划船机"
        case .elliptical: return "椭圆机"
        case .stairClimber: return "爬楼机"
        case .massageGun: return "筋膜枪"
        case .jumpRope: return "跳绳"
        case .multiGym: return "力量站"
        case .kettlebell: return "壶铃"
        case .smallEquipment: return "小件"
        case .heartRateMonitor: return "心率带"
        case .hulaHoop: return "呼啦圈"
        case .flexiBar: return "飞力士棒"
        case .bodyFatScale: return "体脂秤"
        case .other: return "其他"
        }
    }
    
    
    

}
