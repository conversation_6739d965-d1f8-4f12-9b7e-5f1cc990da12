//
//  KeyboardManager.swift
//  Student_IOS
//
//  Created by Junq on 2025/9/1.
//

import UIKit

/// 键盘事件代理 (OC 可用)
@objc public protocol KeyboardManagerDelegate: AnyObject {
    /// 键盘高度变化时回调
    /// - Parameters:
    ///   - manager: KeyboardManager 实例
    ///   - height: 当前键盘高度 (单位: pt)，收起时为 0
    ///   - duration: 键盘动画时长
    ///   - options: 键盘动画曲线选项
    func keyboardManager(_ manager: KeyboardManager,
                         didUpdateKeyboardHeight height: CGFloat,
                         animationDuration duration: TimeInterval,
                         animationOptions options: UIView.AnimationOptions)
}

/// 键盘管理类
@objcMembers
public final class KeyboardManager: NSObject {

    // MARK: - 属性
    public weak var delegate: KeyboardManagerDelegate?

    /// 当前键盘高度（收起时为 0）
    private(set) public var currentKeyboardHeight: CGFloat = 0

    /// 当前设备方向缓存
    private var lastKnownOrientation: UIInterfaceOrientation = .unknown

    /// 单例
    public static let shared = KeyboardManager()

    /// 避免外部直接 init
    private override init() {
        super.init()
        // 监听设备方向变化
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(deviceOrientationDidChange),
            name: UIDevice.orientationDidChangeNotification,
            object: nil
        )
    }
    
    // MARK: - 监听控制
    /// 开始监听键盘事件
    public func startObserving() {
        NotificationCenter.default.addObserver(self,
                                               selector: #selector(handleKeyboardWillShow(_:)),
                                               name: UIResponder.keyboardWillShowNotification,
                                               object: nil)
        
        NotificationCenter.default.addObserver(self,
                                               selector: #selector(handleKeyboardWillHide(_:)),
                                               name: UIResponder.keyboardWillHideNotification,
                                               object: nil)
        
        NotificationCenter.default.addObserver(self,
                                               selector: #selector(handleKeyboardWillChangeFrame(_:)),
                                               name: UIResponder.keyboardWillChangeFrameNotification,
                                               object: nil)
    }
    
    /// 停止监听键盘事件
    public func stopObserving() {
        NotificationCenter.default.removeObserver(self)
    }
    
    deinit {
        stopObserving()
    }

    // MARK: - 设备方向变化处理
    @objc private func deviceOrientationDidChange() {
        // 设备方向变化时重置键盘高度缓存
        let currentOrientation = UIApplication.shared.statusBarOrientation
        if lastKnownOrientation != .unknown && lastKnownOrientation != currentOrientation {
            print("🔄 KeyboardManager: 设备方向变化 \(lastKnownOrientation.rawValue) -> \(currentOrientation.rawValue)")
            // 方向变化时重置键盘高度，避免使用旧坐标系的数据
            currentKeyboardHeight = 0
        }
        lastKnownOrientation = currentOrientation
    }
    
    // MARK: - 通知处理
    @objc private func handleKeyboardWillShow(_ notification: Notification) {
        updateKeyboardHeight(notification)
    }
    
    @objc private func handleKeyboardWillHide(_ notification: Notification) {
        updateKeyboardHeight(notification, isHiding: true)
    }
    
    @objc private func handleKeyboardWillChangeFrame(_ notification: Notification) {
        updateKeyboardHeight(notification)
    }
    
    // MARK: - 高度更新逻辑
    private func updateKeyboardHeight(_ notification: Notification, isHiding: Bool = false) {
        guard let userInfo = notification.userInfo else { return }

        // 键盘动画时长
        let duration = (userInfo[UIResponder.keyboardAnimationDurationUserInfoKey] as? NSNumber)?.doubleValue ?? 0.25

        // 键盘动画曲线
        let curveRaw = (userInfo[UIResponder.keyboardAnimationCurveUserInfoKey] as? NSNumber)?.uintValue ?? UIView.AnimationOptions.curveEaseInOut.rawValue
        let animationOptions = UIView.AnimationOptions(rawValue: curveRaw << 16)

        // 键盘最终 frame
        let endFrame = (userInfo[UIResponder.keyboardFrameEndUserInfoKey] as? NSValue)?.cgRectValue ?? .zero

        print("📱 KeyboardManager: 原始 endFrame = \(endFrame)")

        // 计算键盘高度
        let keyboardHeight: CGFloat
        if isHiding {
            keyboardHeight = 0
        } else {
            keyboardHeight = calculateKeyboardHeight(from: endFrame)
        }

        print("📱 KeyboardManager: 计算得到键盘高度 = \(keyboardHeight)")

        // 存储
        currentKeyboardHeight = keyboardHeight

        // 通知代理
        delegate?.keyboardManager(self,
                                  didUpdateKeyboardHeight: keyboardHeight,
                                  animationDuration: duration,
                                  animationOptions: animationOptions)
    }

    /// 根据键盘 frame 计算正确的键盘高度
    /// - Parameter keyboardFrame: 键盘的 frame
    /// - Returns: 键盘高度
    private func calculateKeyboardHeight(from keyboardFrame: CGRect) -> CGFloat {
        // 获取当前屏幕尺寸
        let screenBounds = UIScreen.main.bounds
        let screenWidth = screenBounds.width
        let screenHeight = screenBounds.height

        print("📱 当前屏幕尺寸: \(screenBounds)")

        // 检查 frame 是否有效
        guard !keyboardFrame.isNull && !keyboardFrame.isInfinite else {
            print("⚠️ 键盘 frame 无效")
            return 0
        }

        // 检查键盘 frame 的坐标系是否正确
        // 如果 x 坐标等于屏幕宽度，说明可能是横屏时的坐标系
        if abs(keyboardFrame.origin.x - screenWidth) < 1.0 {
            print("🔄 检测到可能的坐标系问题，键盘 x = \(keyboardFrame.origin.x), 屏幕宽度 = \(screenWidth)")

            // 这种情况下，键盘的 width 实际上是高度，height 是宽度
            let correctedHeight = keyboardFrame.width
            print("🔧 修正后的键盘高度: \(correctedHeight)")
            return max(0, correctedHeight)
        }

        // 检查 y 坐标是否为负数，这通常表示坐标系问题
        if keyboardFrame.origin.y < 0 {
            print("🔄 检测到负 y 坐标: \(keyboardFrame.origin.y)")

            // 尝试使用键盘的高度作为实际高度
            let correctedHeight = keyboardFrame.height
            print("🔧 使用键盘 frame 的 height 作为键盘高度: \(correctedHeight)")
            return max(0, min(correctedHeight, screenHeight))
        }

        // 正常情况：键盘在屏幕底部
        if keyboardFrame.origin.y >= 0 && keyboardFrame.origin.y <= screenHeight {
            let height = max(0, screenHeight - keyboardFrame.origin.y)
            print("✅ 正常计算键盘高度: \(height)")
            return height
        }

        // 其他异常情况，返回键盘 frame 的高度
        print("⚠️ 异常情况，直接使用键盘 frame 高度: \(keyboardFrame.height)")
        return max(0, min(keyboardFrame.height, screenHeight))
    }
}
