//
//  DeviceMetrics.swift
//  Student_IOS
//
//  Created by Junq on 2025/8/8.
//  设备度量工具类 - 提供屏幕尺寸、安全区域、设备特性等信息
//  最低支持 iOS 13.0
//

import Foundation
import UIKit

/// 设备度量工具类
/// 提供屏幕尺寸、安全区域、设备特性、控制器查找等功能
/// 支持 iOS 13.0+，兼容多场景应用和横竖屏切换
@objcMembers
public final class DeviceMetrics: NSObject {
    
    // MARK: - 屏幕尺寸与安全区域

    /// 屏幕宽度（逻辑像素）
    /// - Returns: 当前屏幕的宽度，单位为点
    @objc public class var screenWidth: CGFloat {
        return UIScreen.main.bounds.width
    }

    /// 屏幕高度（逻辑像素）
    /// - Returns: 当前屏幕的高度，单位为点
    @objc public class var screenHeight: CGFloat {
        return UIScreen.main.bounds.height
    }

    /// 屏幕尺寸（逻辑像素）
    /// - Returns: 当前屏幕的尺寸，单位为点
    @objc public class var screenSize: CGSize {
        return UIScreen.main.bounds.size
    }

    /// 屏幕分辨率（物理像素）
    /// - Returns: 当前屏幕的物理分辨率
    @objc public class var screenResolution: CGSize {
        let bounds = UIScreen.main.bounds
        let scale = UIScreen.main.scale
        return CGSize(width: bounds.width * scale, height: bounds.height * scale)
    }

    /// 屏幕缩放比例
    /// - Returns: 屏幕的缩放比例（1.0, 2.0, 3.0等）
    @objc public class var screenScale: CGFloat {
        return UIScreen.main.scale
    }

    /// 状态栏高度
    /// - Returns: 当前状态栏的高度，如果无法获取则返回0
    /// - Note: iOS 13+ 使用 WindowScene 方式获取，向下兼容
    @objc public class var statusBarHeight: CGFloat {
        if #available(iOS 13.0, *) {
            return UIApplication.shared.connectedScenes
                .compactMap { $0 as? UIWindowScene }
                .first?.statusBarManager?.statusBarFrame.height ?? 0
        } else {
            return UIApplication.shared.statusBarFrame.height
        }
    }

    /// 导航栏标准高度
    /// - Returns: 导航栏的标准高度 44.0
    @objc public class var navigationBarHeight: CGFloat {
        return 44.0
    }

    /// 顶部栏总高度（状态栏 + 导航栏）
    /// - Returns: 状态栏和导航栏的总高度
    @objc public class var topBarHeight: CGFloat {
        return statusBarHeight + navigationBarHeight
    }

    /// 底部安全区域高度
    /// - Returns: 底部安全区域的高度，刘海屏设备通常为34，非刘海屏为0
    @objc public class var bottomSafeAreaHeight: CGFloat {
        return keyWindow?.safeAreaInsets.bottom ?? 0
    }

    /// 顶部安全区域高度
    /// - Returns: 顶部安全区域的高度
    @objc public class var topSafeAreaHeight: CGFloat {
        return keyWindow?.safeAreaInsets.top ?? 0
    }

    /// 左侧安全区域宽度
    /// - Returns: 左侧安全区域的宽度，横屏时可能有值
    @objc public class var leftSafeAreaWidth: CGFloat {
        return keyWindow?.safeAreaInsets.left ?? 0
    }

    /// 右侧安全区域宽度
    /// - Returns: 右侧安全区域的宽度，横屏时可能有值
    @objc public class var rightSafeAreaWidth: CGFloat {
        return keyWindow?.safeAreaInsets.right ?? 0
    }

    /// 完整的安全区域边距
    /// - Returns: 包含上下左右的安全区域边距
    @objc public class var safeAreaInsets: UIEdgeInsets {
        return keyWindow?.safeAreaInsets ?? .zero
    }

    /// 标签栏总高度（标签栏 + 底部安全区域）
    /// - Returns: 标签栏的总高度，包含底部安全区域
    @objc public class var tabBarHeight: CGFloat {
        return 49.0 + bottomSafeAreaHeight
    }

    // MARK: - 设备特性判断

    /// 是否是刘海屏设备
    /// - Returns: 根据底部安全区域判断，有底部安全区域的设备认为是刘海屏
    /// - Note: iPhone X 及以后的设备都有底部安全区域
    @objc public class var isNotchedScreen: Bool {
        return bottomSafeAreaHeight > 0
    }

    /// 是否是灵动岛设备
    /// - Returns: 根据顶部安全区域高度判断，iPhone 14 Pro 系列的灵动岛设备顶部安全区域更高
    /// - Note: 灵动岛设备的顶部安全区域通常大于 51
    @objc public class var isDynamicIslandDevice: Bool {
        return topSafeAreaHeight > 51
    }

    /// 当前是否是横屏模式
    /// - Returns: 当前界面是否处于横屏状态
    @objc public class var isLandscape: Bool {
        return interfaceOrientation.isLandscape
    }

    /// 当前是否是竖屏模式
    /// - Returns: 当前界面是否处于竖屏状态
    @objc public class var isPortrait: Bool {
        return interfaceOrientation.isPortrait
    }

    /// 当前是否是 iPad 设备
    /// - Returns: 当前设备是否为 iPad
    @objc public class var isPad: Bool {
        return UIDevice.current.userInterfaceIdiom == .pad
    }

    /// 当前是否是 iPhone 设备
    /// - Returns: 当前设备是否为 iPhone
    @objc public class var isPhone: Bool {
        return UIDevice.current.userInterfaceIdiom == .phone
    }

    /// 当前界面方向
    /// - Returns: 当前的界面方向，iOS 13+ 使用 WindowScene 获取
    @objc public class var interfaceOrientation: UIInterfaceOrientation {
        if #available(iOS 13.0, *) {
            return UIApplication.shared.connectedScenes
                .compactMap { $0 as? UIWindowScene }
                .first?.interfaceOrientation ?? .unknown
        } else {
            return UIApplication.shared.statusBarOrientation
        }
    }

    /// 当前方向的原始值
    /// - Returns: 界面方向的 rawValue，便于 Objective-C 调用
    @objc public class var interfaceOrientationRaw: Int {
        return interfaceOrientation.rawValue
    }

    /// 设备型号标识符
    /// - Returns: 设备的型号标识符，如 "iPhone14,2"
    @objc public class var deviceModelIdentifier: String {
        var systemInfo = utsname()
        uname(&systemInfo)
        let machineMirror = Mirror(reflecting: systemInfo.machine)
        let identifier = machineMirror.children.reduce("") { identifier, element in
            guard let value = element.value as? Int8, value != 0 else { return identifier }
            return identifier + String(UnicodeScalar(UInt8(value)))
        }
        return identifier
    }

    /// 系统版本号
    /// - Returns: iOS 系统版本号字符串
    @objc public class var systemVersion: String {
        return UIDevice.current.systemVersion
    }

    /// 是否支持多任务（分屏）
    /// - Returns: 当前设备是否支持多任务功能
    @objc public class var supportsMultitasking: Bool {
        return UIDevice.current.isMultitaskingSupported
    }

    // MARK: - 控制器查找

    /// 当前顶层控制器
    /// - Returns: 当前最顶层的 ViewController，用于弹窗等场景
    /// - Note: 会递归查找到最顶层的控制器，包括模态弹出、导航控制器、标签控制器等
    @objc public class var currentViewController: UIViewController? {
        guard let rootVC = keyWindow?.rootViewController else { return nil }
        return findTopViewController(from: rootVC)
    }

    /// 根控制器
    /// - Returns: 应用的根控制器
    @objc public class var rootViewController: UIViewController? {
        return keyWindow?.rootViewController
    }

    /// 当前导航控制器
    /// - Returns: 当前顶层控制器所在的导航控制器，如果不在导航控制器中则返回 nil
    @objc public class var currentNavigationController: UINavigationController? {
        if let nav = currentViewController as? UINavigationController {
            return nav
        }
        return currentViewController?.navigationController
    }

    /// 当前标签控制器
    /// - Returns: 当前顶层控制器所在的标签控制器，如果不在标签控制器中则返回 nil
    @objc public class var currentTabBarController: UITabBarController? {
        if let tab = currentViewController as? UITabBarController {
            return tab
        }
        return currentViewController?.tabBarController
    }

    /// 递归查找顶层控制器
    /// - Parameter vc: 起始控制器
    /// - Returns: 最顶层的控制器
    private class func findTopViewController(from vc: UIViewController) -> UIViewController {
        // 优先处理模态弹出的控制器
        if let presented = vc.presentedViewController {
            return findTopViewController(from: presented)
        }

        // 处理导航控制器
        if let nav = vc as? UINavigationController {
            if let visible = nav.visibleViewController {
                return findTopViewController(from: visible)
            }
            return nav
        }

        // 处理标签控制器
        if let tab = vc as? UITabBarController {
            if let selected = tab.selectedViewController {
                return findTopViewController(from: selected)
            }
            return tab
        }

        // 处理分割控制器（iPad 常用）
        if let split = vc as? UISplitViewController {
            if let detail = split.viewControllers.last {
                return findTopViewController(from: detail)
            }
            return split
        }

        return vc
    }

    // MARK: - 窗口与场景管理

    /// 当前的关键窗口
    /// - Returns: 当前活跃的关键窗口，iOS 13+ 使用 WindowScene 方式获取
    /// - Note: 优先获取前台活跃场景中的关键窗口，确保获取到正确的窗口
    @objc public class var keyWindow: UIWindow? {
        if #available(iOS 13.0, *) {
            // iOS 13+ 使用 WindowScene 方式
            return UIApplication.shared.connectedScenes
                .filter { $0.activationState == .foregroundActive }
                .compactMap { $0 as? UIWindowScene }
                .first?.windows
                .first { $0.isKeyWindow }
        } else {
            // iOS 13 以下使用传统方式
            return UIApplication.shared.keyWindow
        }
    }

    /// 所有活跃的窗口
    /// - Returns: 当前所有活跃的窗口数组
    @objc public class var allWindows: [UIWindow] {
        if #available(iOS 13.0, *) {
            return UIApplication.shared.connectedScenes
                .compactMap { $0 as? UIWindowScene }
                .flatMap { $0.windows }
        } else {
            return UIApplication.shared.windows
        }
    }

    /// 当前活跃的窗口场景
    /// - Returns: 当前前台活跃的窗口场景
    @available(iOS 13.0, *)
    @objc public class var activeWindowScene: UIWindowScene? {
        return UIApplication.shared.connectedScenes
            .filter { $0.activationState == .foregroundActive }
            .compactMap { $0 as? UIWindowScene }
            .first
    }

    // MARK: - 便利方法

    /// 在主线程执行代码块
    /// - Parameter block: 要执行的代码块
    @objc public class func executeOnMainThread(_ block: @escaping () -> Void) {
        if Thread.isMainThread {
            block()
        } else {
            DispatchQueue.main.async {
                block()
            }
        }
    }

    /// 延迟在主线程执行代码块
    /// - Parameters:
    ///   - delay: 延迟时间（秒）
    ///   - block: 要执行的代码块
    @objc public class func executeOnMainThread(after delay: TimeInterval, _ block: @escaping () -> Void) {
        DispatchQueue.main.asyncAfter(deadline: .now() + delay) {
            block()
        }
    }

    /// 获取当前时间戳（毫秒）
    /// - Returns: 当前时间戳，单位为毫秒
    @objc public class var currentTimestamp: Int64 {
        return Int64(Date().timeIntervalSince1970 * 1000)
    }

    /// 获取应用版本号
    /// - Returns: 应用的版本号字符串
    @objc public class var appVersion: String {
        return Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "Unknown"
    }

    /// 获取应用构建号
    /// - Returns: 应用的构建号字符串
    @objc public class var appBuildNumber: String {
        return Bundle.main.infoDictionary?["CFBundleVersion"] as? String ?? "Unknown"
    }

    /// 获取应用标识符
    /// - Returns: 应用的 Bundle Identifier
    @objc public class var appBundleIdentifier: String {
        return Bundle.main.bundleIdentifier ?? "Unknown"
    }

    // MARK: - 调试信息

    /// 打印设备信息（调试用）
    @objc public class func printDeviceInfo() {
        print("=== DeviceMetrics 设备信息 ===")
        print("屏幕尺寸: \(screenWidth) × \(screenHeight)")
        print("屏幕分辨率: \(screenResolution)")
        print("屏幕缩放比例: \(screenScale)")
        print("安全区域: \(safeAreaInsets)")
        print("状态栏高度: \(statusBarHeight)")
        print("设备类型: \(isPad ? "iPad" : "iPhone")")
        print("设备型号: \(deviceModelIdentifier)")
        print("系统版本: \(systemVersion)")
        print("界面方向: \(interfaceOrientation)")
        print("是否刘海屏: \(isNotchedScreen)")
        print("是否灵动岛: \(isDynamicIslandDevice)")
        print("应用版本: \(appVersion) (\(appBuildNumber))")
        print("Bundle ID: \(appBundleIdentifier)")
        print("==============================")
    }
}
