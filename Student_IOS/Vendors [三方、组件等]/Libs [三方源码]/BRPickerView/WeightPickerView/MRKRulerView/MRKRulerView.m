//
//  MRKRulerView.m
//  Student_IOS
//
//  Created by MacPro on 2022/10/24.
//

#import "MRKRulerView.h"
#import "MRKDailyLogic.h"

@implementation MRKRulerView

+ (RulerView *)weightRulerViewWithFrame:(CGRect)frame {
    RulerView *rulerView = [[RulerView alloc] initWithFrame:frame];
    rulerView.backgroundColor = [UIColor whiteColor];
    
    CAGradientLayer *  gradientLayer = [CAGradientLayer layer];
    gradientLayer.frame = rulerView.bounds;
    gradientLayer.locations = @[@(0), @(.2), @(.8), @(1)];
    gradientLayer.startPoint = CGPointMake(0, .5);
    gradientLayer.endPoint = CGPointMake(1, .5);
    gradientLayer.colors = @[(id)RGBA(255, 255, 255, 0).CGColor,
                             (id)RGBA(255, 255, 255, 1).CGColor,
                             (id)RGBA(255, 255, 255, 1).CGColor,
                             (id)RGBA(255, 255, 255, 0).CGColor];
    rulerView.layer.mask = gradientLayer;
    
    //增加一条线
    CALayer *lineLayer  = [CALayer layer];
    lineLayer.frame = CGRectMake(CGRectGetMinX(rulerView.frame), 0, CGRectGetWidth(rulerView.frame), 1);
    [rulerView.layer addSublayer:lineLayer];
    lineLayer.backgroundColor = UIColorHex(#DADCDF).CGColor;
    
    RulerConfig *config = [[RulerConfig alloc] init];
    //刻度高度
    config.shortScaleLength = 24;
    config.longScaleLength = 32;
    //刻度宽度
    config.scaleWidth = 1;
    //刻度起始位置
    config.shortScaleStart = 0; //22;
    config.longScaleStart = 0;//18;
    //刻度颜色
    config.scaleColor = UIColorHex(#D8DADE);
    //刻度之间的距离
    config.distanceBetweenScale = 10;
    //刻度距离数字的距离
    config.distanceFromScaleToNumber = 26;
    //指示视图属性设置
    config.pointSize = CGSizeMake(2, 44);
    config.pointColor = UIColorHex(#16D2E3);
    config.pointStart = 0;
    //文字属性
    config.numberFont = [UIFont fontWithName:fontNamePing size:14];
    config.numberColor = [UIColor colorWithHexString:@"#A9AAAB"];
    config.unit = @"";
    //数字所在位置方向
    config.numberDirection = numberBottom;
    
    //取值范围
    config.max = 500;
    config.min = 1;
    //默认值
    config.defaultNumber = 60;
    config.infiniteLoop = NO;
    config.isDecimal = YES;
    config.offset = 1;
    config.perStepValue = 1;
    
    rulerView.rulerConfig = config;
    
    return  rulerView;
}

+(RulerView *)drinkRulerViewWithFrame:(CGRect)frame{
    RulerView *rulerView = [[RulerView alloc] initWithFrame:frame];
    rulerView.backgroundColor = [UIColor whiteColor];
    
    
    CAGradientLayer *  gradientLayer = [CAGradientLayer layer];
    gradientLayer.frame = rulerView.bounds;
    gradientLayer.locations = @[@(0), @(.2), @(.8), @(1)];
    gradientLayer.startPoint = CGPointMake(0, .5);
    gradientLayer.endPoint = CGPointMake(1, .5);
    gradientLayer.colors = @[(id)RGBA(255, 255, 255, 0).CGColor,
                             (id)RGBA(255, 255, 255, 1).CGColor,
                             (id)RGBA(255, 255, 255, 1).CGColor,
                             (id)RGBA(255, 255, 255, 0).CGColor];
    rulerView.layer.mask = gradientLayer;
    
    //增加一条线
    CALayer *lineLayer  = [CALayer layer];
    lineLayer.frame = CGRectMake(CGRectGetMinX(rulerView.frame), 0, CGRectGetWidth(rulerView.frame), 1);
    [rulerView.layer addSublayer:lineLayer];
    lineLayer.backgroundColor = UIColorHex(#DADCDF).CGColor;
    
    RulerConfig *config = [[RulerConfig alloc] init];
    //刻度高度
    config.shortScaleLength = 8;
    config.longScaleLength = 25;
    //刻度宽度
    config.scaleWidth = 1;
    //刻度起始位置
    config.shortScaleStart = 0; //22;
    config.longScaleStart = 0;//18;
    //刻度颜色
    config.scaleColor = UIColorHex(#D8DADE);
    //刻度之间的距离
    config.distanceBetweenScale = 10;
    //刻度距离数字的距离
    config.distanceFromScaleToNumber = 20;
    //指示视图属性设置
    config.pointSize = CGSizeMake(2, 25);
    config.pointColor = UIColorHex(#16D2E3);
    config.pointStart = 0;
    //文字属性
    config.numberFont = [UIFont fontWithName:fontNamePing size:12];
    config.numberColor = [UIColor colorWithHexString:@"#A9AAAB"];
    config.unit = @"";
    //数字所在位置方向
    config.numberDirection = numberBottom;
    //取值范围
    config.max = [[MRKDailyLogic shared] drinkSelectMost];
    config.min = [[MRKDailyLogic shared] drinkSelectLeast];
    //默认值
    config.defaultNumber = 1800;
    config.infiniteLoop = NO;
    config.isDecimal = NO;
    config.offset = 1;
    config.perStepValue = 50;
    
    rulerView.rulerConfig = config;
    
    return  rulerView;
}

+ (RulerView *)heightRulerViewWithFrame:(CGRect)frame; {
    
    RulerView *rulerView = [[RulerView alloc] initWithFrame:frame];
    rulerView.backgroundColor = [UIColor whiteColor];
    
    
    CAGradientLayer *  gradientLayer = [CAGradientLayer layer];
    gradientLayer.frame = rulerView.bounds;
    gradientLayer.locations = @[@(0), @(.2), @(.8), @(1)];
    gradientLayer.startPoint = CGPointMake(0, .5);
    gradientLayer.endPoint = CGPointMake(1, .5);
    gradientLayer.colors = @[(id)RGBA(255, 255, 255, 0).CGColor,
                             (id)RGBA(255, 255, 255, 1).CGColor,
                             (id)RGBA(255, 255, 255, 1).CGColor,
                             (id)RGBA(255, 255, 255, 0).CGColor];
    rulerView.layer.mask = gradientLayer;
    
    //增加一条线
    CALayer *lineLayer  = [CALayer layer];
    lineLayer.frame = CGRectMake(CGRectGetMinX(rulerView.frame), 0, CGRectGetWidth(rulerView.frame), 1);
    [rulerView.layer addSublayer:lineLayer];
    lineLayer.backgroundColor = UIColorHex(#DADCDF).CGColor;
    
    RulerConfig *config = [[RulerConfig alloc] init];
    //刻度高度
    config.shortScaleLength = 24;
    config.longScaleLength = 32;
    //刻度宽度
    config.scaleWidth = 1;
    //刻度起始位置
    config.shortScaleStart = 0; //22;
    config.longScaleStart = 0;//18;
    //刻度颜色
    config.scaleColor = UIColorHex(#D8DADE);
    //刻度之间的距离
    config.distanceBetweenScale = 10;
    //刻度距离数字的距离
    config.distanceFromScaleToNumber = 26;
    //指示视图属性设置
    config.pointSize = CGSizeMake(2, 44);
    config.pointColor = UIColorHex(#16D2E3);
    config.pointStart = 0;
    //文字属性
    config.numberFont = [UIFont fontWithName:fontNamePing size:14];
    config.numberColor = [UIColor colorWithHexString:@"#A9AAAB"];
    config.unit = @"";
    //数字所在位置方向
    config.numberDirection = numberBottom;
    
    //取值范围
    config.max = 300;
    config.min = 50;
    //默认值
    config.defaultNumber = 170;
    config.infiniteLoop = NO;
    config.isDecimal = NO;
    config.offset = 1;
    config.perStepValue = 1;
    
    rulerView.rulerConfig = config;
    
    return  rulerView;
}


+ (RulerView *)countRulerViewWithFrame:(CGRect)frame {
    RulerView *rulerView = [[RulerView alloc] initWithFrame:frame];
    rulerView.backgroundColor = [UIColor whiteColor];
    
    CAGradientLayer *  gradientLayer = [CAGradientLayer layer];
    gradientLayer.frame = rulerView.bounds;
    gradientLayer.locations = @[@(0), @(.2), @(.8), @(1)];
    gradientLayer.startPoint = CGPointMake(0, .5);
    gradientLayer.endPoint = CGPointMake(1, .5);
    gradientLayer.colors = @[(id)RGBA(255, 255, 255, 0).CGColor,
                             (id)RGBA(255, 255, 255, 1).CGColor,
                             (id)RGBA(255, 255, 255, 1).CGColor,
                             (id)RGBA(255, 255, 255, 0).CGColor];
    rulerView.layer.mask = gradientLayer;
    
    //增加一条线
    CALayer *lineLayer  = [CALayer layer];
    lineLayer.frame = CGRectMake(CGRectGetMinX(rulerView.frame), 0, CGRectGetWidth(rulerView.frame), 1);
    [rulerView.layer addSublayer:lineLayer];
    lineLayer.backgroundColor = UIColorHex(#DADCDF).CGColor;
    
    RulerConfig *config = [[RulerConfig alloc] init];
    //刻度高度
    config.shortScaleLength = 24;
    config.longScaleLength = 32;
    //刻度宽度
    config.scaleWidth = 1;
    //刻度起始位置
    config.shortScaleStart = 0; //22;
    config.longScaleStart = 0;//18;
    //刻度颜色
    config.scaleColor = UIColorHex(#D8DADE);
    //刻度之间的距离
    config.distanceBetweenScale = 10;
    //刻度距离数字的距离
    config.distanceFromScaleToNumber = 26;
    //指示视图属性设置
    config.pointSize = CGSizeMake(2, 44);
    config.pointColor = UIColorHex(#16D2E3);
    config.pointStart = 0;
    //文字属性
    config.numberFont = [UIFont fontWithName:fontNamePing size:14];
    config.numberColor = [UIColor colorWithHexString:@"#A9AAAB"];
    config.unit = @"";
    //数字所在位置方向
    config.numberDirection = numberBottom;
    
    //取值范围
    config.max = 999;
    config.min = 1;
    //默认值
    config.defaultNumber = 170;
    config.infiniteLoop = NO;
    config.isDecimal = NO;
    config.offset = 1;
    config.perStepValue = 1;
    
    rulerView.rulerConfig = config;
    return  rulerView;
}

@end





@interface MRKRuleValueView ()<RulerViewDelegate>
@property (nonatomic, strong) UILabel *dataLab;
@property (nonatomic, strong) RulerView *numberBottomRulerView;
@property (nonatomic, assign) RulerViewType ruleType;
@property (nonatomic, assign) double defaultNumber;
@end



@implementation MRKRuleValueView

#pragma mark - 初始化自定义选择器
- (instancetype)initWithFrame:(CGRect)frame type:(RulerViewType)type defalt:(double)defaultNumber{
    if (self = [super initWithFrame:frame]) {
        self.ruleType = type;
        self.defaultNumber = defaultNumber;
        if (type == WeightRulerViewType) {
            self.unit = @"公斤";
        }else if (type == HeightRulerViewType){
            self.unit = @"厘米";
        }else if (type == DrinkRulerViewType){
            self.unit = @"毫升";
        }else if (type == CountRulerViewType){
            self.unit = @"个";
        }
        
        [self addViews];
    }
    return self;
}



- (void)addViews {
    [self addSubview:self.dataLab];
    [self addSubview:self.numberBottomRulerView];
}


- (RulerView *)numberBottomRulerView {
    if (!_numberBottomRulerView) {
     
        CGRect frame = CGRectMake(0, CGRectGetMaxY(self.dataLab.frame) + (20), CGRectGetWidth(self.frame), 120);
        if (self.ruleType == DrinkRulerViewType ) {
            frame = CGRectMake(0, CGRectGetMaxY(self.dataLab.frame) + (20), CGRectGetWidth(self.frame), 70);
        }
        
        switch (self.ruleType) {
            case WeightRulerViewType:
                _numberBottomRulerView = [MRKRulerView weightRulerViewWithFrame:frame];
                break;
            case HeightRulerViewType:
                _numberBottomRulerView = [MRKRulerView heightRulerViewWithFrame:frame];
                break;
            case DrinkRulerViewType:
                _numberBottomRulerView = [MRKRulerView drinkRulerViewWithFrame:frame];
                break;
            case CountRulerViewType:
                _numberBottomRulerView = [MRKRulerView countRulerViewWithFrame:frame];
                break;
                
            default:
                break;
        }
        _numberBottomRulerView.backgroundColor = [UIColor whiteColor];
        _numberBottomRulerView.tag = 1;
        _numberBottomRulerView.delegate = self;
        
        RulerConfig *config = _numberBottomRulerView.rulerConfig;
        config.defaultNumber = self.defaultNumber;
        
        _numberBottomRulerView.rulerConfig = config;
    }
    return _numberBottomRulerView;
}


- (UILabel *)dataLab{
    if (!_dataLab) {
        _dataLab = [[UILabel alloc] init];
        _dataLab.textAlignment = NSTextAlignmentCenter;
        _dataLab.frame = CGRectMake(0, 0, CGRectGetWidth(self.frame), 40);
    }
    return _dataLab;
}

#pragma mark - 代理方法
- (void)rulerSelectValue:(double)value tag:(NSInteger)tag {
    NSLog(@"value = %lf", value);
    self.value = value;
    
    
    NSString *str = @"";
    switch (self.ruleType) {
        case WeightRulerViewType:  case HeightRulerViewType:
            str = [NSString stringWithFormat:@"%.1f", value];

            break;
        case DrinkRulerViewType: case CountRulerViewType:
            str = [NSString stringWithFormat:@"%0.0f", value];
            
            break;
        default:
            break;
    }
    
    NSMutableAttributedString *data = [[NSMutableAttributedString alloc] initWithString: str];
    data.color = UIColorHex(#16D2E3);
    data.font = [UIFont fontWithName:Bebas_Font size:self.ruleType == DrinkRulerViewType ? 44 : 40];
    
    NSMutableAttributedString *str2 = [[NSMutableAttributedString alloc] initWithString:self.unit];
    str2.color = UIColorHex(#16D2E3);
    str2.font = (self.ruleType == DrinkRulerViewType) ? [UIFont fontWithName:fontNameMeDium size:16] : [UIFont fontWithName:Bebas_Font size: 24];
    [data appendAttributedString:str2];
    self.dataLab.attributedText = data;
}

- (void)setUnit:(NSString *)unit {
    _unit = unit;
}
@end
