//
//  YYTextKeyboardManager_Fix.m
//  修复横竖屏坐标转换问题的补丁
//
//  问题：横屏调用后再竖屏调用 convertRect:toView: 返回错误坐标
//  原因：设备方向变化时状态缓存没有完全重置
//

#import "YYTextKeyboardManager.h"
#import <objc/runtime.h>

@implementation YYTextKeyboardManager (OrientationFix)

// 修复后的设备方向变化处理方法
- (void)deviceOrientationDidChange_Fixed:(NSNotification *)notif {
    NSLog(@"🔄 YYTextKeyboardManager: 设备方向发生变化");
    
    // 获取当前方向
    UIInterfaceOrientation currentOrientation = [UIApplication sharedApplication].statusBarOrientation;
    NSLog(@"🔄 当前方向: %ld", (long)currentOrientation);
    
    // 横竖屏切换时，强制刷新 keyboardView
    [self _initFrameObserver];
    
    // 完全重置所有缓存状态
    [self resetAllCachedStates];
    
    // 重新获取 keyboard frame
    [self _notifyAllObservers];
    
    NSLog(@"🔄 YYTextKeyboardManager: 方向变化处理完成");
}

// 重置所有缓存状态的方法
- (void)resetAllCachedStates {
    // 使用 runtime 访问私有变量
    Ivar fromFrameIvar = class_getInstanceVariable([self class], "_fromFrame");
    Ivar fromVisibleIvar = class_getInstanceVariable([self class], "_fromVisible");
    Ivar fromOrientationIvar = class_getInstanceVariable([self class], "_fromOrientation");
    
    Ivar notificationFromFrameIvar = class_getInstanceVariable([self class], "_notificationFromFrame");
    Ivar notificationToFrameIvar = class_getInstanceVariable([self class], "_notificationToFrame");
    Ivar observedToFrameIvar = class_getInstanceVariable([self class], "_observedToFrame");
    
    Ivar hasNotificationIvar = class_getInstanceVariable([self class], "_hasNotification");
    Ivar hasObservedChangeIvar = class_getInstanceVariable([self class], "_hasObservedChange");
    Ivar lastIsNotificationIvar = class_getInstanceVariable([self class], "_lastIsNotification");
    
    // 重置 frame 相关状态
    if (fromFrameIvar) {
        CGRect zeroRect = CGRectZero;
        object_setIvar(self, fromFrameIvar, [NSValue valueWithCGRect:zeroRect]);
    }
    
    if (fromVisibleIvar) {
        object_setIvar(self, fromVisibleIvar, @(NO));
    }
    
    if (fromOrientationIvar) {
        object_setIvar(self, fromOrientationIvar, @(UIInterfaceOrientationUnknown));
    }
    
    // 重置通知相关状态
    if (notificationFromFrameIvar) {
        CGRect zeroRect = CGRectZero;
        object_setIvar(self, notificationFromFrameIvar, [NSValue valueWithCGRect:zeroRect]);
    }
    
    if (notificationToFrameIvar) {
        CGRect zeroRect = CGRectZero;
        object_setIvar(self, notificationToFrameIvar, [NSValue valueWithCGRect:zeroRect]);
    }
    
    if (observedToFrameIvar) {
        CGRect zeroRect = CGRectZero;
        object_setIvar(self, observedToFrameIvar, [NSValue valueWithCGRect:zeroRect]);
    }
    
    // 重置标志位
    if (hasNotificationIvar) {
        object_setIvar(self, hasNotificationIvar, @(NO));
    }
    
    if (hasObservedChangeIvar) {
        object_setIvar(self, hasObservedChangeIvar, @(NO));
    }
    
    if (lastIsNotificationIvar) {
        object_setIvar(self, lastIsNotificationIvar, @(NO));
    }
    
    NSLog(@"✅ YYTextKeyboardManager: 所有缓存状态已重置");
}

// 增强的 convertRect:toView: 方法
- (CGRect)convertRect_Enhanced:(CGRect)rect toView:(UIView *)view {
    NSLog(@"🔍 convertRect 输入: %@", NSStringFromCGRect(rect));
    
    // 检查当前方向是否与缓存方向一致
    UIInterfaceOrientation currentOrientation = [UIApplication sharedApplication].statusBarOrientation;
    
    // 获取缓存的方向
    Ivar fromOrientationIvar = class_getInstanceVariable([self class], "_fromOrientation");
    UIInterfaceOrientation cachedOrientation = UIInterfaceOrientationUnknown;
    if (fromOrientationIvar) {
        NSNumber *orientationNumber = object_getIvar(self, fromOrientationIvar);
        cachedOrientation = [orientationNumber integerValue];
    }
    
    NSLog(@"🔍 当前方向: %ld, 缓存方向: %ld", (long)currentOrientation, (long)cachedOrientation);
    
    // 如果方向不一致，强制重置状态
    if (cachedOrientation != UIInterfaceOrientationUnknown && 
        cachedOrientation != currentOrientation) {
        
        NSLog(@"⚠️ 检测到方向不一致，强制重置状态");
        [self resetAllCachedStates];
        
        // 重新通知观察者
        [self _notifyAllObservers];
    }
    
    // 调用原始的转换方法
    CGRect result = [self convertRect_Original:rect toView:view];
    
    NSLog(@"🔍 convertRect 输出: %@", NSStringFromCGRect(result));
    
    return result;
}

// 保存原始方法的引用
- (CGRect)convertRect_Original:(CGRect)rect toView:(UIView *)view {
    // 这里会在 load 方法中被替换为原始实现
    return CGRectZero;
}

// 在类加载时进行方法交换
+ (void)load {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        Class class = [self class];
        
        // 交换 deviceOrientationDidChange: 方法
        SEL originalSelector1 = @selector(deviceOrientationDidChange:);
        SEL swizzledSelector1 = @selector(deviceOrientationDidChange_Fixed:);
        
        Method originalMethod1 = class_getInstanceMethod(class, originalSelector1);
        Method swizzledMethod1 = class_getInstanceMethod(class, swizzledSelector1);
        
        if (originalMethod1 && swizzledMethod1) {
            method_exchangeImplementations(originalMethod1, swizzledMethod1);
            NSLog(@"✅ YYTextKeyboardManager: deviceOrientationDidChange 方法已修复");
        }
        
        // 交换 convertRect:toView: 方法
        SEL originalSelector2 = @selector(convertRect:toView:);
        SEL swizzledSelector2 = @selector(convertRect_Enhanced:toView:);
        
        Method originalMethod2 = class_getInstanceMethod(class, originalSelector2);
        Method swizzledMethod2 = class_getInstanceMethod(class, swizzledSelector2);
        
        if (originalMethod2 && swizzledMethod2) {
            // 先保存原始实现
            IMP originalIMP = method_getImplementation(originalMethod2);
            Method originalRefMethod = class_getInstanceMethod(class, @selector(convertRect_Original:toView:));
            method_setImplementation(originalRefMethod, originalIMP);
            
            // 然后交换实现
            method_exchangeImplementations(originalMethod2, swizzledMethod2);
            NSLog(@"✅ YYTextKeyboardManager: convertRect:toView: 方法已增强");
        }
    });
}

@end

// 便捷的调试方法
@implementation YYTextKeyboardManager (Debug)

- (void)logCurrentState {
    NSLog(@"📊 YYTextKeyboardManager 当前状态:");
    NSLog(@"   当前方向: %ld", (long)[UIApplication sharedApplication].statusBarOrientation);
    
    // 使用 runtime 获取私有变量的值
    Ivar fromOrientationIvar = class_getInstanceVariable([self class], "_fromOrientation");
    if (fromOrientationIvar) {
        NSNumber *orientation = object_getIvar(self, fromOrientationIvar);
        NSLog(@"   缓存方向: %ld", (long)[orientation integerValue]);
    }
    
    Ivar notificationToFrameIvar = class_getInstanceVariable([self class], "_notificationToFrame");
    if (notificationToFrameIvar) {
        NSValue *frameValue = object_getIvar(self, notificationToFrameIvar);
        NSLog(@"   通知ToFrame: %@", NSStringFromCGRect([frameValue CGRectValue]));
    }
    
    Ivar observedToFrameIvar = class_getInstanceVariable([self class], "_observedToFrame");
    if (observedToFrameIvar) {
        NSValue *frameValue = object_getIvar(self, observedToFrameIvar);
        NSLog(@"   观察ToFrame: %@", NSStringFromCGRect([frameValue CGRectValue]));
    }
    
    Ivar hasNotificationIvar = class_getInstanceVariable([self class], "_hasNotification");
    if (hasNotificationIvar) {
        NSNumber *hasNotification = object_getIvar(self, hasNotificationIvar);
        NSLog(@"   有通知: %@", [hasNotification boolValue] ? @"YES" : @"NO");
    }
    
    Ivar hasObservedChangeIvar = class_getInstanceVariable([self class], "_hasObservedChange");
    if (hasObservedChangeIvar) {
        NSNumber *hasObservedChange = object_getIvar(self, hasObservedChangeIvar);
        NSLog(@"   有观察变化: %@", [hasObservedChange boolValue] ? @"YES" : @"NO");
    }
}

@end
