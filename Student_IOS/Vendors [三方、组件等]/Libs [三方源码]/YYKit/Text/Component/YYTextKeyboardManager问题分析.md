# YYTextKeyboardManager 横竖屏坐标转换问题分析

## 🔍 问题描述

**现象**：
- 正常竖屏调用：`toFrame====={{0, 625}, {428, 301}}`
- 横屏调用后再竖屏调用：`toFrame====={{187, -498}, {241, 926}}`

**问题代码**：
```objc
CGRect toFrame = [[YYTextKeyboardManager defaultManager] convertRect:transition.toFrame toView:self];
NSLog(@"toFrame=====%@",NSStringFromCGRect(toFrame));
```

## 🕵️ 根本原因分析

### 1. 状态缓存问题

`YYTextKeyboardManager` 使用了多个实例变量来缓存键盘状态：

```objc
@implementation YYTextKeyboardManager {
    CGRect _fromFrame;                    // 上次键盘frame
    BOOL _fromVisible;                    // 上次键盘可见性
    UIInterfaceOrientation _fromOrientation; // 上次界面方向
    
    CGRect _notificationFromFrame;        // 通知中的起始frame
    CGRect _notificationToFrame;          // 通知中的目标frame
    CGRect _observedToFrame;              // 观察到的frame
    
    BOOL _hasNotification;                // 是否有通知
    BOOL _hasObservedChange;              // 是否有观察到的变化
    BOOL _lastIsNotification;             // 最后是否是通知触发
}
```

### 2. 设备方向变化处理不完整

在 `deviceOrientationDidChange:` 方法中：

```objc
- (void)deviceOrientationDidChange:(NSNotification *)notif {
    // 横竖屏切换时，强制刷新 keyboardView
    [self _initFrameObserver];
    
    // 重置缓存，确保下次 frame 计算正确
    _fromFrame = CGRectZero;                    // ✅ 重置了
    _fromOrientation = UIInterfaceOrientationUnknown; // ✅ 重置了
    
    // ❌ 但是没有重置这些关键状态：
    // _notificationToFrame 没有重置
    // _observedToFrame 没有重置
    // _hasNotification 没有重置
    // _hasObservedChange 没有重置
    
    [self _notifyAllObservers];
}
```

### 3. 坐标转换逻辑中的问题

在 `_notifyAllObservers` 方法中，frame 的选择逻辑：

```objc
// to frame 的选择逻辑
if (_lastIsNotification || (_hasObservedChange && CGRectEqualToRect(_observedToFrame, _notificationToFrame))) {
    trans.toFrame = _notificationToFrame;  // 使用通知中的frame
} else {
    trans.toFrame = _observedToFrame;      // 使用观察到的frame
}
```

**问题**：横屏时的 `_notificationToFrame` 或 `_observedToFrame` 被缓存，在竖屏时仍然被使用。

### 4. convertRect:toView: 方法的坐标系问题

```objc
- (CGRect)convertRect:(CGRect)rect toView:(UIView *)view {
    UIWindow *mainWindow = app.mrkKeyWindow;
    
    // 1. 将rect从屏幕坐标系转换到mainWindow坐标系
    rect = [mainWindow convertRect:rect fromWindow:nil];
    
    // 2. 再转换到目标view的坐标系
    if (mainWindow == toWindow) 
        return [mainWindow convertRect:rect toView:view];
    
    // 3. 跨窗口转换
    rect = [mainWindow convertRect:rect toView:mainWindow];
    rect = [toWindow convertRect:rect fromWindow:mainWindow];
    rect = [view convertRect:rect fromView:toWindow];
    
    return rect;
}
```

**问题**：如果传入的 `rect` 是横屏时的坐标，但当前是竖屏状态，坐标转换会出现错误。

## 🔧 问题解决方案

### 方案1：完善设备方向变化时的状态重置

修改 `deviceOrientationDidChange:` 方法：

```objc
- (void)deviceOrientationDidChange:(NSNotification *)notif {
    // 横竖屏切换时，强制刷新 keyboardView
    [self _initFrameObserver];
    
    // 完全重置所有缓存状态
    _fromFrame = CGRectZero;
    _fromOrientation = UIInterfaceOrientationUnknown;
    
    // ✅ 新增：重置通知相关状态
    _notificationFromFrame = CGRectZero;
    _notificationToFrame = CGRectZero;
    _observedToFrame = CGRectZero;
    
    // ✅ 新增：重置标志位
    _hasNotification = NO;
    _hasObservedChange = NO;
    _lastIsNotification = NO;
    
    // 重新获取 keyboard frame
    [self _notifyAllObservers];
}
```

### 方案2：在 convertRect 前检查方向一致性

在调用 `convertRect:toView:` 前，检查当前方向：

```objc
// 在你的代码中添加方向检查
- (CGRect)safeConvertRect:(CGRect)rect toView:(UIView *)view {
    YYTextKeyboardManager *manager = [YYTextKeyboardManager defaultManager];
    
    // 检查当前方向是否与缓存的方向一致
    UIInterfaceOrientation currentOrientation = [UIApplication sharedApplication].statusBarOrientation;
    
    // 如果方向发生变化，强制刷新键盘管理器状态
    if (manager->_fromOrientation != UIInterfaceOrientationUnknown && 
        manager->_fromOrientation != currentOrientation) {
        
        // 手动触发方向变化处理
        [manager deviceOrientationDidChange:nil];
    }
    
    return [manager convertRect:rect toView:view];
}
```

### 方案3：添加方向变化检测

在 `_notifyAllObservers` 方法中添加方向检测：

```objc
- (void)_notifyAllObservers {
    UIApplication *app = [UIApplication sharedExtensionApplication];
    if (!app) return;
    
    // ✅ 新增：检查方向是否发生变化
    UIInterfaceOrientation currentOrientation = app.statusBarOrientation;
    if (_fromOrientation != UIInterfaceOrientationUnknown && 
        _fromOrientation != currentOrientation) {
        
        // 方向发生变化，重置相关状态
        _notificationToFrame = CGRectZero;
        _observedToFrame = CGRectZero;
        _hasNotification = NO;
        _hasObservedChange = NO;
    }
    
    // 原有逻辑...
}
```

## 🧪 验证方案

### 测试步骤

1. **竖屏测试**：
   ```objc
   // 在竖屏状态下调用
   CGRect frame = [[YYTextKeyboardManager defaultManager] convertRect:transition.toFrame toView:self];
   NSLog(@"竖屏 toFrame: %@", NSStringFromCGRect(frame));
   ```

2. **横屏测试**：
   ```objc
   // 切换到横屏，调用键盘相关功能
   // 然后切换回竖屏
   CGRect frame = [[YYTextKeyboardManager defaultManager] convertRect:transition.toFrame toView:self];
   NSLog(@"横屏后竖屏 toFrame: %@", NSStringFromCGRect(frame));
   ```

3. **预期结果**：
   - 竖屏：`{{0, 625}, {428, 301}}`
   - 横屏后竖屏：应该与第一次竖屏结果相同或相近

### 调试日志

添加详细的调试日志：

```objc
- (CGRect)convertRect:(CGRect)rect toView:(UIView *)view {
    NSLog(@"🔍 convertRect 输入: %@", NSStringFromCGRect(rect));
    NSLog(@"🔍 当前方向: %ld", (long)[UIApplication sharedApplication].statusBarOrientation);
    NSLog(@"🔍 缓存方向: %ld", (long)_fromOrientation);
    NSLog(@"🔍 _notificationToFrame: %@", NSStringFromCGRect(_notificationToFrame));
    NSLog(@"🔍 _observedToFrame: %@", NSStringFromCGRect(_observedToFrame));
    
    CGRect result = [self originalConvertRect:rect toView:view];
    NSLog(@"🔍 convertRect 输出: %@", NSStringFromCGRect(result));
    
    return result;
}
```

## 📝 推荐的修复方案

**最佳方案**：结合方案1和方案3

1. **修改 `deviceOrientationDidChange:` 方法**，完全重置所有状态
2. **在 `_notifyAllObservers` 中添加方向检测**，确保状态一致性
3. **添加调试日志**，便于后续问题排查

这样可以确保：
- 方向变化时状态被正确重置
- 避免使用错误方向下的缓存数据
- 坐标转换结果的准确性

## ⚠️ 注意事项

1. **线程安全**：确保所有修改都在主线程进行
2. **性能影响**：方向检测不会显著影响性能
3. **兼容性**：修改后需要在不同iOS版本和设备上测试
4. **副作用**：重置状态可能会影响动画的连续性，但准确性更重要

通过这些修改，可以解决横竖屏切换后坐标转换错误的问题。
