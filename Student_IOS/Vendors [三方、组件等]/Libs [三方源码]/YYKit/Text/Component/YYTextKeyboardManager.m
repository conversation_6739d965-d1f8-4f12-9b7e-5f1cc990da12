//
//  YYTextKeyboardManager.m
//  YYKit <https://github.com/ibireme/YYKit>
//
//  Created by ibireme on 15/6/3.
//  Copyright (c) 2015 ibireme.
//
//  This source code is licensed under the MIT-style license found in the
//  LICENSE file in the root directory of this source tree.
//

#import "YYTextKeyboardManager.h"
#import "UIApplication+YYAdd.h"
#import <objc/runtime.h>
#import "NSArray+Extension.h"
#import "UIApplication+Add.h"

static int _YYTextKeyboardViewFrameObserverKey;

/// Observer for view's frame/bounds/center/transform
@interface _YYTextKeyboardViewFrameObserver : NSObject
@property (nonatomic, copy) void (^notifyBlock)(UIView *keyboard);
- (void)addToKeyboardView:(UIView *)keyboardView;
+ (instancetype)observerForView:(UIView *)keyboardView;
@end


@implementation _YYTextKeyboardViewFrameObserver {
    __unsafe_unretained UIView *_keyboardView;
}
- (void)addToKeyboardView:(UIView *)keyboardView {
    if (_keyboardView == keyboardView) return;
    if (_keyboardView) {
        [self removeFrameObserver];
        objc_setAssociatedObject(_keyboardView, &_YYTextKeyboardViewFrameObserverKey, nil, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    }
    _keyboardView = keyboardView;
    if (keyboardView) {
        [self addFrameObserver];
    }
    objc_setAssociatedObject(keyboardView, &_YYTextKeyboardViewFrameObserverKey, self, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
}

- (void)removeFrameObserver {
    [_keyboardView removeObserver:self forKeyPath:@"frame"];
    [_keyboardView removeObserver:self forKeyPath:@"center"];
    [_keyboardView removeObserver:self forKeyPath:@"bounds"];
    [_keyboardView removeObserver:self forKeyPath:@"transform"];
    _keyboardView = nil;
}

- (void)addFrameObserver {
    if (!_keyboardView) return;
    [_keyboardView addObserver:self forKeyPath:@"frame" options:kNilOptions context:NULL];
    [_keyboardView addObserver:self forKeyPath:@"center" options:kNilOptions context:NULL];
    [_keyboardView addObserver:self forKeyPath:@"bounds" options:kNilOptions context:NULL];
    [_keyboardView addObserver:self forKeyPath:@"transform" options:kNilOptions context:NULL];
}

- (void)dealloc {
    [self removeFrameObserver];
}

+ (instancetype)observerForView:(UIView *)keyboardView {
    if (!keyboardView) return nil;
    return objc_getAssociatedObject(keyboardView, &_YYTextKeyboardViewFrameObserverKey);
}

- (void)observeValueForKeyPath:(NSString *)keyPath ofObject:(id)object change:(NSDictionary *)change context:(void *)context {
    
    BOOL isPrior = [[change objectForKey:NSKeyValueChangeNotificationIsPriorKey] boolValue];
    if (isPrior) return;
    
    NSKeyValueChange changeKind = [[change objectForKey:NSKeyValueChangeKindKey] integerValue];
    if (changeKind != NSKeyValueChangeSetting) return;
    
    id newVal = [change objectForKey:NSKeyValueChangeNewKey];
    if (newVal == [NSNull null]) newVal = nil;
    
    if (_notifyBlock) {
        _notifyBlock(_keyboardView);
    }
}

@end



@implementation YYTextKeyboardManager {
    NSHashTable *_observers;
    
    CGRect _fromFrame;
    BOOL _fromVisible;
    UIInterfaceOrientation _fromOrientation;
    
    CGRect _notificationFromFrame;
    CGRect _notificationToFrame;
    NSTimeInterval _notificationDuration;
    UIViewAnimationCurve _notificationCurve;
    BOOL _hasNotification;
    
    CGRect _observedToFrame;
    BOOL _hasObservedChange;
    
    BOOL _lastIsNotification;
}

- (instancetype)init {
    @throw [NSException exceptionWithName:@"YYTextKeyboardManager init error" reason:@"Use 'defaultManager' to get instance." userInfo:nil];
    return [super init];
}

- (instancetype)_init {
    self = [super init];
    _observers = [[NSHashTable alloc] initWithOptions:NSPointerFunctionsWeakMemory|NSPointerFunctionsObjectPointerPersonality capacity:0];
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(_keyboardFrameWillChangeNotification:)
                                                 name:UIKeyboardWillChangeFrameNotification
                                               object:nil];

    [[NSNotificationCenter defaultCenter] addObserver:self
                                         selector:@selector(deviceOrientationDidChange:)
                                             name:UIDeviceOrientationDidChangeNotification
                                           object:nil];
    // for iPad (iOS 9)
    if ([UIDevice currentDevice].systemVersion.floatValue >= 9) {
        [[NSNotificationCenter defaultCenter] addObserver:self
                                                 selector:@selector(_keyboardFrameDidChangeNotification:)
                                                     name:UIKeyboardDidChangeFrameNotification
                                                   object:nil];
    }
    return self;
}

- (void)_initFrameObserver {
    UIView *keyboardView = self.keyboardView;
    if (!keyboardView) return;
    __weak typeof(self) _self = self;
    _YYTextKeyboardViewFrameObserver *observer = [_YYTextKeyboardViewFrameObserver observerForView:keyboardView];
    if (!observer) {
        observer = [_YYTextKeyboardViewFrameObserver new];
        observer.notifyBlock = ^(UIView *keyboard) {
            [_self _keyboardFrameChanged:keyboard];
        };
        [observer addToKeyboardView:keyboardView];
    }
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

+ (instancetype)defaultManager {
    static YYTextKeyboardManager *mgr = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        if (![UIApplication isAppExtension]) {
            mgr = [[self alloc] _init];
        }
    });
    return mgr;
}

+ (void)load {
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self defaultManager];
    });
}

- (void)addObserver:(id<YYTextKeyboardObserver>)observer {
    if (!observer) return;
    [_observers addObject:observer];
}

- (void)removeObserver:(id<YYTextKeyboardObserver>)observer {
    if (!observer) return;
    [_observers removeObject:observer];
}

- (UIWindow *)keyboardWindow {
    for (UIWindow *window in UIApplication.sharedApplication.windows) {
        if ([self _getKeyboardViewFromWindow:window]) {
            return window;
        }
    }
    UIWindow *window = [UIApplication.sharedApplication.windows qmui_firstMatchWithBlock:^BOOL(__kindof UIWindow * _Nonnull item) {
        return [NSStringFromClass(item.class) isEqualToString:@"UIRemoteKeyboardWindow"];
    }];
    if (window) {
        return window;
    }
    window = [UIApplication.sharedApplication.windows qmui_firstMatchWithBlock:^BOOL(__kindof UIWindow * _Nonnull item) {
        return [NSStringFromClass(item.class) isEqualToString:@"UITextEffectsWindow"];
    }];
    return window;
}

- (UIView *)keyboardView {
    UIApplication *app = [UIApplication sharedExtensionApplication];
    if (!app) return nil;
    
    UIWindow *window = nil;
    UIView *view = nil;
    for (window in app.windows) {
        view = [self _getKeyboardViewFromWindow:window];
        if (view) return view;
    }
    window = app.mrkKeyWindow;
    view = [self _getKeyboardViewFromWindow:window];
    if (view) return view;
    return nil;
}

- (BOOL)isKeyboardVisible {
    UIWindow *window = self.keyboardWindow;
    if (!window) return NO;
    UIView *view = self.keyboardView;
    if (!view) return NO;
    CGRect rect = CGRectIntersection(window.bounds, view.frame);
    if (CGRectIsNull(rect)) return NO;
    if (CGRectIsInfinite(rect)) return NO;
    return rect.size.width > 0 && rect.size.height > 0;
}

- (CGRect)keyboardFrame {
    UIView *keyboard = [self keyboardView];
    if (!keyboard) return CGRectNull;
    
    CGRect frame = CGRectNull;
    UIWindow *window = keyboard.window;
    if (window) {
        frame = [window convertRect:keyboard.frame toWindow:nil];
    } else {
        frame = keyboard.frame;
    }
    return frame;
}

#pragma mark - private

- (CGFloat)_systemVersion {
    static CGFloat v;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        v = [UIDevice currentDevice].systemVersion.floatValue;
    });
    return v;
}






/**
 从给定的 window 里寻找代表键盘当前布局位置的 view。
 iOS 15 及以前（包括用 Xcode 13 编译的 App 运行在 iOS 16 上的场景），键盘的 UI 层级是：
 |- UIApplication.windows
    |- UIRemoteKeyboardWindow
        |- UIInputSetContainerView
            |- UIInputSetHostView - 键盘及 webView 里的输入工具栏（上下键、Done键）
                |- _UIKBCompatInputView - 键盘主体按键
                |- TUISystemInputAssistantView - 键盘顶部的候选词栏、emoji 键盘顶部的搜索框
                |- _UIRemoteKeyboardPlaceholderView - webView 里的输入工具栏的占位（实际的 view 在 UITextEffectsWindow 里）
 
 iOS 16 及以后（仅限用 Xcode 14 及以上版本编译的 App），UIApplication.windows 里已经不存在 UIRemoteKeyboardWindow 了，所以退而求其次，我们通过 UITextEffectsWindow 里的 UIInputSetHostView 来获取键盘的位置——这两个 window 在布局层面可以理解为镜像关系。
 |- UIApplication.windows
    |- UITextEffectsWindow
        |- UIInputSetContainerView
            |- UIInputSetHostView - 键盘及 webView 里的输入工具栏（上下键、Done键）
                |- _UIRemoteKeyboardPlaceholderView - 整个键盘区域，包含顶部候选词栏、emoji 键盘顶部搜索栏（有时候不一定存在）
                |- UIWebFormAccessory - webView 里的输入工具栏的占位
                |- TUIInputAssistantHostView - 外接键盘时可能存在，此时不一定有 placeholder
        |- UIInputSetHostView - 可能存在多个，但只有一个里面有 _UIRemoteKeyboardPlaceholderView
 
 所以只要找到 UIInputSetHostView 即可，优先从 UIRemoteKeyboardWindow 找，不存在的话则从 UITextEffectsWindow 找。
 */
- (UIView *)_getKeyboardViewFromWindow:(UIWindow *)window {
    if (!window) return nil;
    
    NSString *windowName = NSStringFromClass(window.class);
    if ([windowName isEqualToString:@"UIRemoteKeyboardWindow"]) {
        UIView *result = [[window.subviews qmui_firstMatchWithBlock:^BOOL(__kindof UIView * _Nonnull subview) {
            return [NSStringFromClass(subview.class) isEqualToString:@"UIInputSetContainerView"];
        }].subviews qmui_firstMatchWithBlock:^BOOL(__kindof UIView * _Nonnull subview) {
            return [NSStringFromClass(subview.class) isEqualToString:@"UIInputSetHostView"];
        }];
        return result;
    }
    if ([windowName isEqualToString:@"UITextEffectsWindow"]) {
        UIView *result = [[window.subviews qmui_firstMatchWithBlock:^BOOL(__kindof UIView * _Nonnull subview) {
            return [NSStringFromClass(subview.class) isEqualToString:@"UIInputSetContainerView"];
        }].subviews qmui_firstMatchWithBlock:^BOOL(__kindof UIView * _Nonnull subview) {
            return [NSStringFromClass(subview.class) isEqualToString:@"UIInputSetHostView"] && subview.subviews.count;
        }];
        return result;
    }
    return nil;
}

- (void)deviceOrientationDidChange:(NSNotification *)notif {
    // 横竖屏切换时，强制刷新 keyboardView
    [self _initFrameObserver];
    
    // 重置缓存，确保下次 frame 计算正确
    _fromFrame = CGRectZero;
    _fromOrientation = UIInterfaceOrientationUnknown;
    
    // 重新获取 keyboard frame
    [self _notifyAllObservers];
}

- (void)_keyboardFrameWillChangeNotification:(NSNotification *)notif {
    if (![notif.name isEqualToString:UIKeyboardWillChangeFrameNotification]) return;
    NSDictionary *info = notif.userInfo;
    if (!info) return;
    
    [self _initFrameObserver];
    
    NSValue *beforeValue = info[UIKeyboardFrameBeginUserInfoKey];
    NSValue *afterValue = info[UIKeyboardFrameEndUserInfoKey];
    NSNumber *curveNumber = info[UIKeyboardAnimationCurveUserInfoKey];
    NSNumber *durationNumber = info[UIKeyboardAnimationDurationUserInfoKey];
    
    CGRect before = beforeValue.CGRectValue;
    CGRect after = afterValue.CGRectValue;
    UIViewAnimationCurve curve = curveNumber.integerValue;
    NSTimeInterval duration = durationNumber.doubleValue;
    
    // ignore zero end frame
    if (after.size.width <= 0 && after.size.height <= 0) return;
    
    _notificationFromFrame = before;
    _notificationToFrame = after;
    _notificationCurve = curve;
    _notificationDuration = duration;
    _hasNotification = YES;
    _lastIsNotification = YES;
    
    [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(_notifyAllObservers) object:nil];
    if (duration == 0) {
        [self performSelector:@selector(_notifyAllObservers) withObject:nil afterDelay:0 inModes:@[NSRunLoopCommonModes]];
    } else {
        [self _notifyAllObservers];
    }
}

- (void)_keyboardFrameDidChangeNotification:(NSNotification *)notif {
    if (![notif.name isEqualToString:UIKeyboardDidChangeFrameNotification]) return;
    NSDictionary *info = notif.userInfo;
    if (!info) return;
    
    [self _initFrameObserver];
    
    NSValue *afterValue = info[UIKeyboardFrameEndUserInfoKey];
    CGRect after = afterValue.CGRectValue;
    
    // ignore zero end frame
    if (after.size.width <= 0 && after.size.height <= 0) return;
    
    _notificationToFrame = after;
    _notificationCurve = UIViewAnimationCurveEaseInOut;
    _notificationDuration = 0;
    _hasNotification = YES;
    _lastIsNotification = YES;
    
    [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(_notifyAllObservers) object:nil];
    [self performSelector:@selector(_notifyAllObservers) withObject:nil afterDelay:0 inModes:@[NSRunLoopCommonModes]];
}

- (void)_keyboardFrameChanged:(UIView *)keyboard {
    if (keyboard != self.keyboardView) return;
    
    UIWindow *window = keyboard.window;
    if (window) {
        _observedToFrame = [window convertRect:keyboard.frame toWindow:nil];
    } else {
        _observedToFrame = keyboard.frame;
    }
    _hasObservedChange = YES;
    _lastIsNotification = NO;
    
    [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(_notifyAllObservers) object:nil];
    [self performSelector:@selector(_notifyAllObservers) withObject:nil afterDelay:0 inModes:@[NSRunLoopCommonModes]];
}

- (void)_notifyAllObservers {
    UIApplication *app = [UIApplication sharedExtensionApplication];
    if (!app) return;
    
    UIView *keyboard = self.keyboardView;
    UIWindow *window = keyboard.window;
    if (!window) {
        window = app.mrkKeyWindow;
    }
    if (!window) {
        window = app.windows.firstObject;
    }
    
    YYTextKeyboardTransition trans = {0};
    
    // from
    if (_fromFrame.size.width == 0 && _fromFrame.size.height == 0) { // first notify
        _fromFrame.size.width = window.bounds.size.width;
        _fromFrame.size.height = trans.toFrame.size.height;
        _fromFrame.origin.x = trans.toFrame.origin.x;
        _fromFrame.origin.y = window.bounds.size.height;
    }
    trans.fromFrame = _fromFrame;
    trans.fromVisible = _fromVisible;
    
    // to
    if (_lastIsNotification || (_hasObservedChange && CGRectEqualToRect(_observedToFrame, _notificationToFrame))) {
        trans.toFrame = _notificationToFrame;
        trans.animationDuration = _notificationDuration;
        trans.animationCurve = _notificationCurve;
        trans.animationOption = _notificationCurve << 16;
        
        // Fix iPad(iOS7) keyboard frame error after rorate device when the keyboard is not docked to bottom.
        if (((int)[self _systemVersion]) == 7) {
            UIInterfaceOrientation ori = app.statusBarOrientation;
            if (_fromOrientation != UIInterfaceOrientationUnknown && _fromOrientation != ori) {
                switch (ori) {
                    case UIInterfaceOrientationPortrait: {
                        if (CGRectGetMaxY(trans.toFrame) != window.frame.size.height) {
                            trans.toFrame.origin.y -= trans.toFrame.size.height;
                        }
                    } break;
                    case UIInterfaceOrientationPortraitUpsideDown: {
                        if (CGRectGetMinY(trans.toFrame) != 0) {
                            trans.toFrame.origin.y += trans.toFrame.size.height;
                        }
                    } break;
                    case UIInterfaceOrientationLandscapeLeft: {
                        if (CGRectGetMaxX(trans.toFrame) != window.frame.size.width) {
                            trans.toFrame.origin.x -= trans.toFrame.size.width;
                        }
                    } break;
                    case UIInterfaceOrientationLandscapeRight: {
                        if (CGRectGetMinX(trans.toFrame) != 0) {
                            trans.toFrame.origin.x += trans.toFrame.size.width;
                        }
                    } break;
                    default: break;
                }
            }
        }
    } else {
        trans.toFrame = _observedToFrame;
    }
    
    if (window && trans.toFrame.size.width > 0 && trans.toFrame.size.height > 0) {
        CGRect rect = CGRectIntersection(window.bounds, trans.toFrame);
        if (!CGRectIsNull(rect) && !CGRectIsEmpty(rect)) {
            trans.toVisible = YES;
        }
    }
    
    if (!CGRectEqualToRect(trans.toFrame, _fromFrame)) {
        for (id<YYTextKeyboardObserver> observer in _observers.copy) {
            if ([observer respondsToSelector:@selector(keyboardChangedWithTransition:)]) {
                [observer keyboardChangedWithTransition:trans];
            }
        }
    }
    
    _hasNotification = NO;
    _hasObservedChange = NO;
    _fromFrame = trans.toFrame;
    _fromVisible = trans.toVisible;
    _fromOrientation = app.statusBarOrientation;
}

- (CGRect)convertRect:(CGRect)rect toView:(UIView *)view {
    UIApplication *app = [UIApplication sharedExtensionApplication];
    if (!app) return CGRectZero;
    
    if (CGRectIsNull(rect)) return rect;
    if (CGRectIsInfinite(rect)) return rect;
    
    UIWindow *mainWindow = app.keyWindow;
    if (!mainWindow) mainWindow = app.windows.firstObject;
    if (!mainWindow) { // no window ?!
        if (view) {
            [view convertRect:rect fromView:nil];
        } else {
            return rect;
        }
    }
    
    rect = [mainWindow convertRect:rect fromWindow:nil];
    if (!view) return [mainWindow convertRect:rect toWindow:nil];
    if (view == mainWindow) return rect;
    
    UIWindow *toWindow = [view isKindOfClass:[UIWindow class]] ? (id)view : view.window;
    if (!mainWindow || !toWindow) return [mainWindow convertRect:rect toView:view];
    if (mainWindow == toWindow) return [mainWindow convertRect:rect toView:view];
    
    // in different window
    rect = [mainWindow convertRect:rect toView:mainWindow];
    rect = [toWindow convertRect:rect fromWindow:mainWindow];
    rect = [view convertRect:rect fromView:toWindow];
    
    NSLog(@"mainWindow: %@", mainWindow);
    NSLog(@"toWindow: %@", toWindow);
    NSLog(@"view.window: %@", view.window);
    NSLog(@"rect: %@", NSStringFromCGRect(rect));
    NSLog(@"window.bounds: %@", NSStringFromCGRect(mainWindow.bounds));
    NSLog(@"window.frame: %@", NSStringFromCGRect(mainWindow.frame));
    NSLog(@"orientation: %ld", (long)[UIApplication sharedApplication].statusBarOrientation);
    
    return rect;
}

@end
