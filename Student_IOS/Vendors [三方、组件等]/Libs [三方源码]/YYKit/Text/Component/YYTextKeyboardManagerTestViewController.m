//
//  YYTextKeyboardManagerTestViewController.m
//  测试 YYTextKeyboardManager 横竖屏坐标转换修复效果
//

#import <UIKit/UIKit.h>
#import "YYTextKeyboardManager.h"

@interface YYTextKeyboardManagerTestViewController : UIViewController <YYTextKeyboardObserver>

@property (nonatomic, strong) UITextView *textView;
@property (nonatomic, strong) UILabel *statusLabel;
@property (nonatomic, strong) UIButton *testButton;
@property (nonatomic, strong) UIScrollView *logScrollView;
@property (nonatomic, strong) UITextView *logTextView;

@property (nonatomic, strong) NSMutableArray<NSString *> *logMessages;

@end

@implementation YYTextKeyboardManagerTestViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.title = @"键盘管理器测试";
    self.view.backgroundColor = [UIColor systemBackgroundColor];
    self.logMessages = [NSMutableArray array];
    
    [self setupUI];
    [self setupKeyboardObserver];
}

- (void)setupUI {
    // 状态标签
    self.statusLabel = [[UILabel alloc] init];
    self.statusLabel.text = @"当前状态：等待测试";
    self.statusLabel.font = [UIFont systemFontOfSize:16];
    self.statusLabel.textAlignment = NSTextAlignmentCenter;
    self.statusLabel.backgroundColor = [UIColor systemGray6Color];
    self.statusLabel.layer.cornerRadius = 8;
    self.statusLabel.clipsToBounds = YES;
    [self.view addSubview:self.statusLabel];
    
    // 测试按钮
    self.testButton = [UIButton buttonWithType:UIButtonTypeSystem];
    [self.testButton setTitle:@"测试坐标转换" forState:UIControlStateNormal];
    self.testButton.backgroundColor = [UIColor systemBlueColor];
    [self.testButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    self.testButton.layer.cornerRadius = 8;
    [self.testButton addTarget:self action:@selector(testCoordinateConversion) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:self.testButton];
    
    // 文本输入框
    self.textView = [[UITextView alloc] init];
    self.textView.text = @"点击这里弹出键盘，然后旋转设备测试坐标转换";
    self.textView.font = [UIFont systemFontOfSize:16];
    self.textView.backgroundColor = [UIColor systemGray6Color];
    self.textView.layer.cornerRadius = 8;
    self.textView.layer.borderWidth = 1;
    self.textView.layer.borderColor = [UIColor systemGray4Color].CGColor;
    [self.view addSubview:self.textView];
    
    // 日志滚动视图
    self.logScrollView = [[UIScrollView alloc] init];
    self.logScrollView.backgroundColor = [UIColor systemGray6Color];
    self.logScrollView.layer.cornerRadius = 8;
    [self.view addSubview:self.logScrollView];
    
    // 日志文本视图
    self.logTextView = [[UITextView alloc] init];
    self.logTextView.font = [UIFont fontWithName:@"Menlo" size:12];
    self.logTextView.backgroundColor = [UIColor clearColor];
    self.logTextView.editable = NO;
    self.logTextView.text = @"📋 测试日志:\n";
    [self.logScrollView addSubview:self.logTextView];
    
    // 设置约束
    [self setupConstraints];
}

- (void)setupConstraints {
    self.statusLabel.translatesAutoresizingMaskIntoConstraints = NO;
    self.testButton.translatesAutoresizingMaskIntoConstraints = NO;
    self.textView.translatesAutoresizingMaskIntoConstraints = NO;
    self.logScrollView.translatesAutoresizingMaskIntoConstraints = NO;
    self.logTextView.translatesAutoresizingMaskIntoConstraints = NO;
    
    [NSLayoutConstraint activateConstraints:@[
        // 状态标签
        [self.statusLabel.topAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.topAnchor constant:20],
        [self.statusLabel.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:20],
        [self.statusLabel.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor constant:-20],
        [self.statusLabel.heightAnchor constraintEqualToConstant:44],
        
        // 测试按钮
        [self.testButton.topAnchor constraintEqualToAnchor:self.statusLabel.bottomAnchor constant:20],
        [self.testButton.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:20],
        [self.testButton.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor constant:-20],
        [self.testButton.heightAnchor constraintEqualToConstant:44],
        
        // 文本输入框
        [self.textView.topAnchor constraintEqualToAnchor:self.testButton.bottomAnchor constant:20],
        [self.textView.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:20],
        [self.textView.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor constant:-20],
        [self.textView.heightAnchor constraintEqualToConstant:100],
        
        // 日志滚动视图
        [self.logScrollView.topAnchor constraintEqualToAnchor:self.textView.bottomAnchor constant:20],
        [self.logScrollView.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:20],
        [self.logScrollView.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor constant:-20],
        [self.logScrollView.bottomAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.bottomAnchor constant:-20],
        
        // 日志文本视图
        [self.logTextView.topAnchor constraintEqualToAnchor:self.logScrollView.topAnchor constant:8],
        [self.logTextView.leadingAnchor constraintEqualToAnchor:self.logScrollView.leadingAnchor constant:8],
        [self.logTextView.trailingAnchor constraintEqualToAnchor:self.logScrollView.trailingAnchor constant:-8],
        [self.logTextView.bottomAnchor constraintEqualToAnchor:self.logScrollView.bottomAnchor constant:-8],
        [self.logTextView.widthAnchor constraintEqualToAnchor:self.logScrollView.widthAnchor constant:-16]
    ]];
}

- (void)setupKeyboardObserver {
    [[YYTextKeyboardManager defaultManager] addObserver:self];
}

- (void)dealloc {
    [[YYTextKeyboardManager defaultManager] removeObserver:self];
}

#pragma mark - Actions

- (void)testCoordinateConversion {
    [self addLog:@"🧪 开始测试坐标转换"];
    
    YYTextKeyboardManager *manager = [YYTextKeyboardManager defaultManager];
    
    // 获取当前键盘frame
    CGRect keyboardFrame = manager.keyboardFrame;
    [self addLog:[NSString stringWithFormat:@"📐 原始键盘frame: %@", NSStringFromCGRect(keyboardFrame)]];
    
    // 测试转换到当前视图
    CGRect convertedFrame = [manager convertRect:keyboardFrame toView:self.view];
    [self addLog:[NSString stringWithFormat:@"🔄 转换到当前视图: %@", NSStringFromCGRect(convertedFrame)]];
    
    // 测试转换到文本框
    CGRect convertedToTextView = [manager convertRect:keyboardFrame toView:self.textView];
    [self addLog:[NSString stringWithFormat:@"🔄 转换到文本框: %@", NSStringFromCGRect(convertedToTextView)]];
    
    // 更新状态
    UIInterfaceOrientation orientation = [UIApplication sharedApplication].statusBarOrientation;
    NSString *orientationStr = [self orientationString:orientation];
    self.statusLabel.text = [NSString stringWithFormat:@"当前方向：%@ | 键盘：%@", 
                            orientationStr, manager.isKeyboardVisible ? @"显示" : @"隐藏"];
}

#pragma mark - YYTextKeyboardObserver

- (void)keyboardChangedWithTransition:(YYTextKeyboardTransition)transition {
    [self addLog:@"⌨️ 键盘状态发生变化"];
    [self addLog:[NSString stringWithFormat:@"   从: %@ (可见:%@)", 
                 NSStringFromCGRect(transition.fromFrame), 
                 transition.fromVisible ? @"是" : @"否"]];
    [self addLog:[NSString stringWithFormat:@"   到: %@ (可见:%@)", 
                 NSStringFromCGRect(transition.toFrame), 
                 transition.toVisible ? @"是" : @"否"]];
    [self addLog:[NSString stringWithFormat:@"   动画时长: %.2f秒", transition.animationDuration]];
    
    // 测试坐标转换
    YYTextKeyboardManager *manager = [YYTextKeyboardManager defaultManager];
    CGRect convertedFrame = [manager convertRect:transition.toFrame toView:self.view];
    [self addLog:[NSString stringWithFormat:@"🔄 转换后frame: %@", NSStringFromCGRect(convertedFrame)]];
    
    // 更新状态标签
    UIInterfaceOrientation orientation = [UIApplication sharedApplication].statusBarOrientation;
    NSString *orientationStr = [self orientationString:orientation];
    self.statusLabel.text = [NSString stringWithFormat:@"当前方向：%@ | 键盘：%@", 
                            orientationStr, transition.toVisible ? @"显示" : @"隐藏"];
}

#pragma mark - Helper Methods

- (void)addLog:(NSString *)message {
    NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
    formatter.dateFormat = @"HH:mm:ss.SSS";
    NSString *timestamp = [formatter stringFromDate:[NSDate date]];
    
    NSString *logMessage = [NSString stringWithFormat:@"[%@] %@", timestamp, message];
    [self.logMessages addObject:logMessage];
    
    // 更新日志显示
    NSString *allLogs = [@"📋 测试日志:\n" stringByAppendingString:[self.logMessages componentsJoinedByString:@"\n"]];
    self.logTextView.text = allLogs;
    
    // 滚动到底部
    dispatch_async(dispatch_get_main_queue(), ^{
        if (self.logTextView.text.length > 0) {
            NSRange bottom = NSMakeRange(self.logTextView.text.length - 1, 1);
            [self.logTextView scrollRangeToVisible:bottom];
        }
    });
    
    // 控制台输出
    NSLog(@"%@", logMessage);
}

- (NSString *)orientationString:(UIInterfaceOrientation)orientation {
    switch (orientation) {
        case UIInterfaceOrientationPortrait:
            return @"竖屏";
        case UIInterfaceOrientationPortraitUpsideDown:
            return @"倒竖屏";
        case UIInterfaceOrientationLandscapeLeft:
            return @"左横屏";
        case UIInterfaceOrientationLandscapeRight:
            return @"右横屏";
        default:
            return @"未知";
    }
}

- (void)viewWillTransitionToSize:(CGSize)size withTransitionCoordinator:(id<UIViewControllerTransitionCoordinator>)coordinator {
    [super viewWillTransitionToSize:size withTransitionCoordinator:coordinator];
    
    [self addLog:[NSString stringWithFormat:@"🔄 视图即将转换到尺寸: %@", NSStringFromCGSize(size)]];
    
    [coordinator animateAlongsideTransition:^(id<UIViewControllerTransitionCoordinatorContext> context) {
        // 转换过程中
        [self addLog:@"🔄 视图转换进行中..."];
    } completion:^(id<UIViewControllerTransitionCoordinatorContext> context) {
        // 转换完成
        [self addLog:@"✅ 视图转换完成"];
        
        // 延迟测试坐标转换
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self testCoordinateConversion];
        });
    }];
}

@end
