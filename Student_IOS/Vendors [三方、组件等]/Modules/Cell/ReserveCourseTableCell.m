//
//  ReserveCourseTableCell.m
//  Student_IOS
//
//  Created by 闻飞 on 2021/3/21.
//

#import "ReserveCourseTableCell.h"
#import "MRKCourseModel.h"
#import "UIView+Effects.h"
#import "MRKTagsView.h"
#import "MRKRankTagView.h"
#import "MRKHomePageModels.h"
#import "MRKToolKit.h"
#import "MRKCourseListModel.h"
#import "MRKCourseFilterModels.h"

@interface ReserveCourseTableCell ()
@property (nonatomic, strong) UIView *bgView;             //背景内容
@property (nonatomic, strong) UIImageView *couseIconView; //左边课程大图
@property (nonatomic, strong) UIImageView *tagImageView;  //限免标签
@property (nonatomic, strong) UILabel *topTitleLable;     //标题label
@property (nonatomic, strong) YYLabel *describeLabel;     //详细内容label
@property (nonatomic, strong) UILabel *timeLable;         //最后一行 label
@property (nonatomic, strong) MRKTagsView *tagsView;      //标签view
@property (nonatomic, strong) MRKRankTagView *rankView;   //排行
@end


@implementation ReserveCourseTableCell

- (MRKTagsView *)tagsView {
    if (_tagsView) return _tagsView;
    _tagsView = [[MRKTagsView alloc] init];
    _tagsView.tagBGColor = [UIColor colorWithHexString:@"#000000" alpha:0.4];
    _tagsView.tagTextColor = [UIColor whiteColor];
    _tagsView.tagFont = kSystem_Font_NoDHPX(WKDHPX(12));
    _tagsView.tagCorner = WKDHPX(4);
    _tagsView.tagHeight = WKDHPX(20);
    _tagsView.titleSpacing = WKDHPX(6);
    _tagsView.tagMargin = WKDHPX(8);
    _tagsView.oneLine = YES;
    _tagsView.clipsToBounds = YES;
    _tagsView.hidden = YES;
    return _tagsView;
}

- (MRKRankTagView *)rankView {
    if (!_rankView) {
        _rankView = [[MRKRankTagView alloc] init];
        _rankView.hidden = YES;
    }
    return _rankView;
}

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier{
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        [self uiLayoutView];
    }
    return self;
}

- (void)uiLayoutView {
    ///背景承载图
    self.bgView = [[UIView alloc] init];
    self.bgView.backgroundColor = UIColor.whiteColor;
    [self.contentView addSubview:self.bgView];
    [self.bgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsMake(WKDHPX(12), WKDHPX(16), 0, WKDHPX(16)));
    }];
    
    ///左边课程大图
    self.couseIconView = [[UIImageView alloc] init];
    self.couseIconView.contentMode = UIViewContentModeScaleAspectFill;
    self.couseIconView.sd_imageTransition = SDWebImageTransition.fadeTransition;
    self.couseIconView.layer.cornerRadius = 8;
    self.couseIconView.layer.masksToBounds = YES;
    [self.bgView addSubview:self.couseIconView];
    [self.couseIconView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.bottom.mas_equalTo(0);
        make.size.mas_equalTo(CGSizeMake(WKDHPX(160), WKDHPX(90)));
    }];
    
  
    /**图片标签**/
    self.tagImageView = [[UIImageView alloc] init];
    self.tagImageView.contentMode = UIViewContentModeScaleAspectFit;
    self.tagImageView.hidden = YES;
    [self.couseIconView addSubview:self.tagImageView];
    [self.tagImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.mas_equalTo(WKDHPX(8));
    }];
    
    ///标题label
    self.topTitleLable = [[UILabel alloc] init];
    self.topTitleLable.textColor = [UIColor colorWithHexString:@"#363A44"];
    self.topTitleLable.lineBreakMode = NSLineBreakByTruncatingTail;
    self.topTitleLable.font = [UIFont systemFontOfSize:WKDHPX(15) weight:UIFontWeightMedium];
    [self.bgView addSubview:self.topTitleLable];
    [self.topTitleLable mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.couseIconView.mas_right).offset(WKDHPX(12));
        make.top.equalTo(self.couseIconView.mas_top).offset(WKDHPX(8));
        make.right.mas_equalTo(0);
    }];
    
    self.describeLabel = [[YYLabel alloc] init];
    self.describeLabel.textColor = [UIColor colorWithHexString:@"#848A9B"];
    self.describeLabel.textVerticalAlignment = YYTextVerticalAlignmentCenter;
    self.describeLabel.font = [UIFont fontWithName:@"iconfont" size:WKDHPX(13)];
    [self.bgView addSubview:self.describeLabel];
    [self.describeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.topTitleLable.mas_bottom).offset(WKDHPX(4));
        make.left.mas_equalTo(self.topTitleLable.mas_left);
        make.right.mas_equalTo(0);
    }];
    
    ///时间label
    self.timeLable = [[UILabel alloc] init];
    self.timeLable.font = kSystem_Font_NoDHPX(WKDHPX(13));
    self.timeLable.textColor = [UIColor colorWithHexString:@"#363A44"];
    [self.bgView addSubview:self.timeLable];
    [self.timeLable mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.couseIconView.mas_right).offset(WKDHPX(12));
        make.bottom.equalTo(self.couseIconView.mas_bottom).offset(-WKDHPX(8));
        make.right.mas_equalTo(0);
    }];
    
    ///排行
    [self.bgView addSubview:self.rankView];
    [self.rankView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.mas_equalTo(WKDHPX(8));
        make.size.mas_equalTo(CGSizeMake(WKDHPX(22), WKDHPX(22)));
    }];
    
    ///标签
    [self.bgView addSubview:self.tagsView];
    [self.tagsView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.couseIconView.mas_right).offset(WKDHPX(12));
        make.bottom.equalTo(self.couseIconView.mas_bottom).offset(-WKDHPX(8));
        make.right.mas_equalTo(0);
        make.height.mas_equalTo(WKDHPX(20));
    }];
}



#pragma mark - 搜索结果
- (void)setCourseFilterCell_Model:(MRKCourseFilterListModel *)model keyWord:(NSString *)text{
    self.rankView.hidden = YES;

    NSString *PicStr = [NSString imageUrlClip:model.cover andSize:CGSizeMake(WKDHPX(160), WKDHPX(90))];
    [self.couseIconView sd_setImageWithURL:[NSURL URLWithString:PicStr] placeholderImage:placeImage];
    
    NSMutableAttributedString *title = [[NSMutableAttributedString alloc] initWithString:model.name];
    NSRange textRang = [model.name rangeOfString:text?:@""];
    [title setColor:[UIColor colorWithHexString:@"#16D2E3"] range:textRang];
    self.topTitleLable.attributedText = title;
    
    self.describeLabel.attributedText = ({
        NSString *device = [MRKEquipmentTypeData deviceImageFromIconFont:model.equipmentId];
        NSMutableAttributedString *deviceText = [[NSMutableAttributedString alloc] initWithString:device];
        deviceText.font = [UIFont fontWithName:@"iconfont" size:WKDHPX(18)];
        
        NSMutableAttributedString *nameStr = [[NSMutableAttributedString alloc] initWithString:model.coachName?:@""];
        NSRange nameRang = [model.coachName rangeOfString:text?:@""];
        [nameStr setColor:[UIColor colorWithHexString:@"#16D2E3"] range:nameRang];
        
        NSString *desc = [NSString stringWithFormat:@"｜%@", model.gradeDesc];
        NSMutableAttributedString *descStr = [[NSMutableAttributedString alloc] initWithString:desc];
        NSRange descRang = [desc rangeOfString:text?:@""];
        [descStr setColor:[UIColor colorWithHexString:@"#16D2E3"] range:descRang];
        
        NSString *time = [NSString stringWithFormat:@"｜%@分钟", model.courseTime];
        NSMutableAttributedString *timeStr = [[NSMutableAttributedString alloc] initWithString:time];
        NSRange timeRang = [time rangeOfString:text?:@""];
        [timeStr setColor:[UIColor colorWithHexString:@"#16D2E3"] range:timeRang];
        
        NSMutableAttributedString *describie = [[NSMutableAttributedString alloc] init];
        [describie appendAttributedString:deviceText];
        
        NSMutableAttributedString *attachText = [[NSMutableAttributedString alloc] init];
        [attachText appendAttributedString:nameStr];
        [attachText appendAttributedString:descStr];
        [attachText appendAttributedString:timeStr];
        
        [describie appendAttributedString:attachText];
        [describie addAttribute:NSBaselineOffsetAttributeName value:@(0.36 *(WKDHPX(18) -WKDHPX(13))) range:NSMakeRange(describie.length - attachText.length, attachText.length)];
        describie;
    });
    
    {   ///限免优先级高, Vip图标低
        self.tagImageView.hidden = YES;
        if (model.isFree) {
            self.tagImageView.hidden = NO;
            self.tagImageView.image = [UIImage imageNamed:@"courseTag_free"];
        } else {
            NSString *imageName = @"";
            NSInteger viptype = model.vipType.intValue;
            switch (viptype) {
                case 10: case 20:
                    imageName = @"courseTag_vip";
                    break;
                case 30:
                    imageName = @"courseTag_enjoyvip";
                    break;
                default:
                    break;
            }
            if (imageName.length > 0) {
                self.tagImageView.hidden = NO;
                self.tagImageView.image = [UIImage imageNamed:imageName];
            }
        }
    }
    
    
    
    NSMutableArray *tags = [NSMutableArray array];
    if (model.lastTrainTime.length > 0) {
        [tags appendObject:model.lastTrainTime];
    }
    [self.tagsView.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
    [self.tagsView setTags: tags];
    self.tagsView.hidden = tags.count == 0;
}

#pragma mark - 搜索——超燃人气榜 填充cell
- (void)setTopCourseCell_Model:(HomeTopCourseModel *)model{
    self.topTitleLable.attributedText = nil;
    self.rankView.hidden = NO;
    self.tagsView.hidden = YES;
    
    NSString *url = [model.cover imageUrlAdaptReSize:CGSizeMake(WKDHPX(160), WKDHPX(90))];
    [self.couseIconView sd_setImageWithURL:[NSURL URLWithString:url] placeholderImage:placeImage];
    [self.rankView.bgImageView sd_setImageWithURL: [NSURL URLWithString:model.rankIcon]];
    self.topTitleLable.text = model.name;
    self.describeLabel.attributedText = ({
        NSString *device = [MRKEquipmentTypeData deviceImageFromIconFont:model.productId];
        NSMutableAttributedString *deviceText = [[NSMutableAttributedString alloc] initWithString:device];
        deviceText.color = [UIColor colorWithHexString:@"#848A9B"];
        deviceText.font = [UIFont fontWithName:@"iconfont" size:WKDHPX(18)];

        NSString *descrip = [NSString stringWithFormat:@"%@｜%@｜%@分钟", model.coachName, model.gradeDesc, model.courseTime];
        NSMutableAttributedString *attachText = [[NSMutableAttributedString alloc] initWithString:descrip];
        attachText.color = [UIColor colorWithHexString:@"#848A9B"];
        attachText.font = [UIFont systemFontOfSize:WKDHPX(13)];
        [deviceText appendAttributedString:attachText];
        [deviceText addAttribute:NSBaselineOffsetAttributeName value:@(0.36 *(WKDHPX(18) -WKDHPX(13))) range:NSMakeRange(deviceText.length - attachText.length, attachText.length)];
        deviceText;
    });
    self.describeLabel.textVerticalAlignment = YYTextVerticalAlignmentCenter;
}

#pragma mark - 练过的课程 / 最近练过
- (void)configWithItem:(MRKCourseListModel *)model {
    
    ///dongzuo
    if (model.type.intValue == 2) {
        [self.couseIconView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.size.mas_equalTo(CGSizeMake(WKDHPX(100), WKDHPX(75)));
        }];
        
        NSString *url = [model.cover imageUrlAdaptReSize:CGSizeMake(WKDHPX(100), WKDHPX(75))];
        [self.couseIconView sd_setImageWithURL:[NSURL URLWithString:url] placeholderImage:placeImage];
        
        self.topTitleLable.text = model.name;
        self.describeLabel.attributedText = ({
            NSString *descrip = [NSString stringWithFormat:@"%@", model.part];
            NSMutableAttributedString *attachText = [[NSMutableAttributedString alloc] initWithString:descrip];
            attachText.color = [UIColor colorWithHexString:@"#848A9B"];
            attachText.font = [UIFont systemFontOfSize:WKDHPX(13)];
            attachText;
        });
        self.describeLabel.textVerticalAlignment = YYTextVerticalAlignmentCenter;
        self.timeLable.text = model.trainingDate;
        self.tagsView.hidden = YES;
        return;
    }
    
    NSString *url = [model.cover imageUrlAdaptReSize:CGSizeMake(WKDHPX(160), WKDHPX(90))];
    [self.couseIconView sd_setImageWithURL:[NSURL URLWithString:url] placeholderImage:placeImage];
    
    self.topTitleLable.text = model.name;
    self.describeLabel.attributedText = ({
        NSString *device = [MRKEquipmentTypeData deviceImageFromIconFont:model.productId];
        NSMutableAttributedString *deviceText = [[NSMutableAttributedString alloc] initWithString:device];
        deviceText.color = [UIColor colorWithHexString:@"#848A9B"];
        deviceText.font = [UIFont fontWithName:@"iconfont" size:WKDHPX(18)];

        NSString *descrip = [NSString stringWithFormat:@"%@｜%@｜%@分钟", model.coachName, model.gradeDesc, model.duration];
        NSMutableAttributedString *attachText = [[NSMutableAttributedString alloc] initWithString:descrip];
        attachText.color = [UIColor colorWithHexString:@"#848A9B"];
        attachText.font = [UIFont systemFontOfSize:WKDHPX(13)];
        [deviceText appendAttributedString:attachText];
        [deviceText addAttribute:NSBaselineOffsetAttributeName value:@(0.36 *(WKDHPX(18) -WKDHPX(13))) range:NSMakeRange(deviceText.length - attachText.length, attachText.length)];
        deviceText;
    });
    self.describeLabel.textVerticalAlignment = YYTextVerticalAlignmentCenter;
    self.timeLable.text = model.trainingDate;
    self.tagsView.hidden = YES;
}


#pragma mark - 收藏的课程 填充cell
- (void)setStoreCell_Model:(MRKCourseListModel *)model {
    self.tagImageView.hidden = YES; // !model.isFree;
    
    if (model.type.intValue == 2) {
        [self.couseIconView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.size.mas_equalTo(CGSizeMake(WKDHPX(100), WKDHPX(75)));
        }];
        
        NSString *url = [model.cover imageUrlAdaptReSize:CGSizeMake(WKDHPX(100), WKDHPX(75))];
        [self.couseIconView sd_setImageWithURL:[NSURL URLWithString:url] placeholderImage:placeImage];
        
        self.topTitleLable.text = model.name;
        self.describeLabel.attributedText = ({
            NSString *descrip = [NSString stringWithFormat:@"%@", model.part];
            NSMutableAttributedString *attachText = [[NSMutableAttributedString alloc] initWithString:descrip];
            attachText.color = [UIColor colorWithHexString:@"#848A9B"];
            attachText.font = [UIFont systemFontOfSize:WKDHPX(13)];
            attachText;
        });
        self.describeLabel.textVerticalAlignment = YYTextVerticalAlignmentCenter;
     
        self.tagsView.hidden = YES;
        return;
    }
    
    NSString *url = [model.cover imageUrlAdaptReSize:CGSizeMake(WKDHPX(160), WKDHPX(90))];
    [self.couseIconView sd_setImageWithURL:[NSURL URLWithString:url] placeholderImage:placeImage];
    
    self.topTitleLable.text = model.name;
    self.describeLabel.attributedText = ({
        NSString *device = [MRKEquipmentTypeData deviceImageFromIconFont:model.productId];
        NSMutableAttributedString *deviceText = [[NSMutableAttributedString alloc] initWithString:device];
        deviceText.color = [UIColor colorWithHexString:@"#848A9B"];
        deviceText.font = [UIFont fontWithName:@"iconfont" size:WKDHPX(18)];

        NSString *descrip = [NSString stringWithFormat:@"%@｜%@｜%@分钟", model.coachName, model.gradeDesc, model.duration];
        NSMutableAttributedString *attachText = [[NSMutableAttributedString alloc] initWithString:descrip];
        attachText.color = [UIColor colorWithHexString:@"#848A9B"];
        attachText.font = [UIFont systemFontOfSize:WKDHPX(13)];
        [deviceText appendAttributedString:attachText];
        [deviceText addAttribute:NSBaselineOffsetAttributeName value:@(0.36 *(WKDHPX(18) -WKDHPX(13))) range:NSMakeRange(deviceText.length - attachText.length, attachText.length)];
        deviceText;
    });
    self.describeLabel.textVerticalAlignment = YYTextVerticalAlignmentCenter;
    self.tagsView.hidden = YES;
}


@end

