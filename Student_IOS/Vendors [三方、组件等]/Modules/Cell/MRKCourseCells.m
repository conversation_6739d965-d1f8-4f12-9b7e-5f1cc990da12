//
//  MRKCourseCells.m
//  Student_IOS
//
//  Created by merit on 2023/3/17.
//

#import "MRKCourseCells.h"
#import "UIView+AZGradient.h"

#pragma mark - 首页主题课
@implementation MRKHomeThemeCourseCell
- (UIImageView *)courseImageView {
    if (_courseImageView) return _courseImageView;
    UIImageView *imagev = [[UIImageView alloc] init];
    imagev.sd_imageTransition = SDWebImageTransition.fadeTransition;
    imagev.contentMode = UIViewContentModeScaleAspectFill;
    imagev.cornerRadius = WKDHPX(6);
    _courseImageView = imagev;
    return _courseImageView;
}
- (UILabel *)titleLabel {
    if (_titleLabel) return _titleLabel;
    UILabel *label = [[UILabel alloc] init];
    label.font = kMedium_Font_NoDHPX(WKDHPX(15));
    label.textColor = [UIColor colorWithHexString:@"#333333"];
    _titleLabel = label;
    return _titleLabel;
}
- (UILabel *)describeLabel {
    if (_describeLabel) return _describeLabel;
    UILabel *label = [[UILabel alloc] init];
    label.font = kSystem_Font_NoDHPX(WKDHPX(13));
    label.textColor = [UIColor colorWithHexString:@"#666666"];
    _describeLabel = label;
    return _describeLabel;
}

- (void)createUI {
    [self.contentView addSubview:self.courseImageView];
    [self.contentView addSubview:self.titleLabel];
    [self.contentView addSubview:self.describeLabel];
    
    [self.courseImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(WKDHPX(12));
        make.left.bottom.mas_equalTo(0);
        make.size.mas_equalTo(CGSizeMake(WKDHPX(80), WKDHPX(60)));
    }];
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.courseImageView.mas_right).offset(WKDHPX(8));
        make.right.mas_equalTo(0);
        make.bottom.equalTo(self.courseImageView.mas_centerY).offset(-WKDHPX(2));
    }];
    [self.describeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.titleLabel.mas_left);
        make.right.mas_equalTo(0);
        make.top.equalTo(self.titleLabel.mas_bottom).offset(WKDHPX(2));
    }];
}

- (void)setHomeThemeDetail:(MRKCourseModel *)model {
    self.titleLabel.text = model.name;
    self.describeLabel.text = [NSString stringWithFormat:@"%@｜%@｜%@分钟",model.gradeDesc, model.coachName, model.courseTime];
    NSString *picStr = [NSString imageUrlClip:model.cover andSize:CGSizeMake(WKDHPX(80), WKDHPX(60))];
    [self.courseImageView sd_setImageWithURL:[NSURL URLWithString:picStr] placeholderImage:[UIImage imageNamed:@"pic_1"]];
}

- (void)setDetail {
    self.titleLabel.text = @"燃脂特训 · 马甲线突击计划";
    self.describeLabel.text = @"M3进阶｜小宇｜35分钟";
    self.courseImageView.backgroundColor = [UIColor systemPinkColor];
}
@end




#pragma mark - ----------------全部课程筛选cell----------------
@interface MRKCourseFilterListCell()
@property (nonatomic, strong) MRKCourseMainPictureView *courseView;
@end
@implementation MRKCourseFilterListCell
- (MRKCourseMainPictureView *)courseView {
    if (!_courseView) {
        _courseView = [[MRKCourseMainPictureView alloc] init];
        _courseView.cornerRadius = Corner_8;
    }
    return _courseView;
}
- (void)createUI {
    [self.contentView addSubview:self.courseView];
    [self.courseView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsMake(0, WKDHPX(16), WKDHPX(12), WKDHPX(16)));
    }];
}
- (void)setCourseListDetail:(MRKCourseFilterListModel *)model {
    
//    NSString *middleURl = [NSString imageUrlClip:model.cover andSize:CGSizeMake(WKDHPX(350), WKDHPX(160))];
//    [self.courseView.courseImageView sd_setImageWithURL:[NSURL URLWithString:middleURl] placeholderImage:placeImage];
    
    [self.courseView.courseImageView setImageWithImageURL:model.cover];
    
    self.courseView.iconImageView.image = [UIImage imageNamed:[MRKEquipmentTypeData targetImageFromIconType:model.equipmentId color:1]];
    self.courseView.titleLabel.text = model.name;
    self.courseView.practicedLabel.hidden = ![model.lastTrainTime isNotBlank];
    self.courseView.practicedLabel.text = [NSString stringWithFormat:@" %@ ", model.lastTrainTime];
    self.courseView.describeLabel.text = [NSString stringWithFormat:@"%@｜%@分钟｜%@人练过",model.gradeDesc, model.courseTime, model.trainNum.stringValue];
    {   ///限免优先级高, Vip图标低
        self.courseView.tagImageView.hidden = YES;
        if (model.isFree) {
            self.courseView.tagImageView.hidden = NO;
            self.courseView.tagImageView.image = [UIImage imageNamed:@"courseTag_free"];
        } else {
            NSString *imageName = @"";
            NSInteger viptype = model.vipType.intValue;
            switch (viptype) {
                case 10:
                    imageName = @"courseTag_vip";
                    break;
                case 30:
                    imageName = @"courseTag_enjoyvip";
                    break;
                default:
                    break;
            }
            if (imageName.length > 0) {
                self.courseView.tagImageView.hidden = NO;
                self.courseView.tagImageView.image = [UIImage imageNamed:imageName];
            }
        }
    }
}
@end





#pragma mark - ----------------为你推荐 课程大图cell----------------
@interface MRKRecommendCourseListCell()
@property (nonatomic, strong) MRKCourseMainPictureView *courseView;
@end
@implementation MRKRecommendCourseListCell
- (MRKCourseMainPictureView *)courseView {
    if (!_courseView) {
        _courseView = [[MRKCourseMainPictureView alloc] init];
        _courseView.cornerRadius = Corner_8;
        _courseView.newTagLabel.hidden = YES;
        _courseView.practicedLabel.hidden = NO;
        _courseView.practicedNumberLabel.hidden = NO;
        _courseView.titleLabel.font = kMedium_Font_NoDHPX(WKDHPX(20));
        
        _courseView.courseTagLab.textColor = [UIColor whiteColor];
        _courseView.courseTagLab.backgroundColor = [UIColor clearColor];
        _courseView.courseTagLab.font = kMedium_Font_NoDHPX(WKDHPX(12));
        [_courseView.courseTagLab az_setGradientBackgroundWithColors:@[[UIColor colorWithHexString:@"#FF9EBA"],
                                                                       [UIColor colorWithHexString:@"#F6A37D"]]
                                                           locations:@[@0,@1]
                                                          startPoint:CGPointMake(0, 0)
                                                            endPoint:CGPointMake(1, 0)];
    }
    return _courseView;
}

- (void)createUI {
    [self.contentView addSubview:self.courseView];
    [self.courseView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsMake(0, PageMargin_16, WKDHPX(12), PageMargin_16));
    }];
}

- (void)setRecommendDetail:(MRKCourseModel *)model {
    NSString *imageUrl = [NSString imageUrlClip:model.cover andSize:CGSizeMake(kScreenWidth -PageMargin_16*2,  WKDHPX(149 + 12))];
    [self.courseView.courseImageView sd_setImageWithURL:[NSURL URLWithString:imageUrl] placeholderImage:placeImage];
    self.courseView.titleLabel.text = model.name;
    self.courseView.describeLabel.text = [NSString stringWithFormat:@"%@｜%@｜%@分钟", model.coachName, model.gradeDesc, model.courseTime];
    self.courseView.iconImageView.image = [UIImage imageNamed:[MRKEquipmentTypeData targetImageFromIconType:model.equipmentId color: 1]];
    
    self.courseView.practicedLabel.hidden = YES;
    self.courseView.practicedLabel.text = @" 刚刚练过 ";
    
    self.courseView.practicedNumberLabel.hidden = YES;
    self.courseView.practicedNumberLabel.text = [NSString stringWithFormat:@" %ld人练过 ",model.trainNum];
    
    {   ///限免优先级高, Vip图标低
        self.courseView.tagImageView.hidden = YES;
        if (model.isFree) {
            self.courseView.tagImageView.hidden = NO;
            self.courseView.tagImageView.image = [UIImage imageNamed:@"courseTag_free"];
        } else {
            NSString *imageName = @"";
            NSInteger viptype = model.vipType.intValue;
            switch (viptype) {
                case 10:
                    imageName = @"courseTag_vip";
                    break;
                case 30:
                    imageName = @"courseTag_enjoyvip";
                    break;
                default:
                    break;
            }
            if (imageName.length > 0) {
                self.courseView.tagImageView.hidden = NO;
                self.courseView.tagImageView.image = [UIImage imageNamed:imageName];
            }
        }
    }
    
    self.courseView.courseTagLab.hidden = ![model.tagName isNotBlank];
    self.courseView.courseTagLab.text = model.tagName;
    [self.courseView.courseTagLab mas_updateConstraints:^(MASConstraintMaker *make) {
        if (self.courseView.tagImageView.hidden){
            make.left.equalTo(self.courseView.mas_left).offset(WKDHPX(12));
        } else {
            make.left.equalTo(self.courseView.tagImageView.mas_right).offset(WKDHPX(8));
        }
    }];
}
@end





#pragma mark - ----------------课程列表大图----------------
@interface MRKCourseMainPictureView()
@end
@implementation MRKCourseMainPictureView
//- (UIImageView *)courseImageView {
//    if (_courseImageView) return _courseImageView;
//    UIImageView *imagev = [[UIImageView alloc] init];
//    imagev.sd_imageTransition = SDWebImageTransition.fadeTransition;
//    imagev.contentMode = UIViewContentModeScaleAspectFill;
//    _courseImageView = imagev;
//    return _courseImageView;
//}

- (MRKImageView *)courseImageView {
    if (!_courseImageView) {
        MRKImageView *imagev = [[MRKImageView alloc] init];
        imagev.imageType = MRKImageViewTypeRectangle;
        _courseImageView = imagev;
    }
    return _courseImageView;
}

- (UIView *)maskLayerView{
    if (_maskLayerView) return _maskLayerView;
     _maskLayerView = [[UIView alloc] init];
    [_maskLayerView az_setGradientBackgroundWithColors:@[[UIColor colorWithWhite:0 alpha:0],
                                                         [UIColor colorWithWhite:0 alpha:0.54]]
                                             locations:@[@0, @1.0]
                                            startPoint:CGPointMake(0.5, 0)
                                              endPoint:CGPointMake(0.5, 1)];
    return _maskLayerView;
}
- (UIImageView *)iconImageView {
    if (_iconImageView) return _iconImageView;
    UIImageView *imagev = [[UIImageView alloc] init];
    imagev.alpha = 0.8;
    _iconImageView = imagev;
    return _iconImageView;
}
- (UILabel *)newTagLabel{
    if (_newTagLabel) return _newTagLabel;
    UILabel *label = [[UILabel alloc] init];
    label.font = [UIFont systemFontOfSize:WKDHPX(13) weight:UIFontWeightSemibold];
    label.textColor = [UIColor whiteColor];
    label.backgroundColor = [UIColor colorWithHexString:@"#16D2E3"];
    label.textAlignment = NSTextAlignmentCenter;
    label.hidden = YES;
    _newTagLabel = label;
    return _newTagLabel;
}
- (UILabel *)titleLabel {
    if (_titleLabel) return _titleLabel;
    UILabel *label = [[UILabel alloc] init];
    label.font = kMedium_Font_NoDHPX(WKDHPX(15));
    label.textColor = [UIColor colorWithHexString:@"#FFFFFF"];
    _titleLabel = label;
    return _titleLabel;
}
- (UILabel *)describeLabel {
    if (_describeLabel) return _describeLabel;
    UILabel *label = [[UILabel alloc] init];
    label.font = kSystem_Font_NoDHPX(WKDHPX(13));
    label.textColor = [UIColor colorWithHexString:@"#FFFFFF" alpha:0.7];
    _describeLabel = label;
    return _describeLabel;
}
- (UILabel *)practicedLabel {
    if (_practicedLabel) return _practicedLabel;
    UILabel *label = [[UILabel alloc] init];
    label.font = kMedium_Font_NoDHPX(WKDHPX(11));
    label.textColor = [UIColor whiteColor];
    label.backgroundColor = [UIColor colorWithHexString:@"#000000" alpha:0.4];
    label.cornerRadius = WKDHPX(4);
    _practicedLabel = label;
    return _practicedLabel;
}
- (UILabel *)practicedNumberLabel {
    if(!_practicedNumberLabel){
        UILabel *label = [[UILabel alloc] init];
        label.font = kSystem_Font_NoDHPX(WKDHPX(13));
        label.textColor = [UIColor colorWithHexString:@"#FFFFFF" alpha:0.7];
        label.hidden = YES;
        _practicedNumberLabel = label;
    }
    return _practicedNumberLabel;
}

/**图片标签**/
- (UIImageView *)tagImageView{
    if (!_tagImageView) {
        _tagImageView = [[UIImageView alloc] init];
        _tagImageView.contentMode = UIViewContentModeScaleAspectFit;
    }
    return _tagImageView;
}

- (MRKEdgeLabel *)courseTagLab{
    if (!_courseTagLab) {
        _courseTagLab = [[MRKEdgeLabel alloc] init];
        _courseTagLab.textColor = [UIColor whiteColor];
        _courseTagLab.backgroundColor = [UIColor colorWithHexString:@"#000000" alpha:0.2];
        _courseTagLab.font = [UIFont systemFontOfSize:WKDHPX(11)];
        _courseTagLab.textAlignment = NSTextAlignmentCenter;
        _courseTagLab.hidden = YES;
        _courseTagLab.layer.cornerRadius = WKDHPX(4);
        _courseTagLab.layer.masksToBounds = YES;
        _courseTagLab.edgeInsets = UIEdgeInsetsMake(0, WKDHPX(4), 0, WKDHPX(4));
    }
    return _courseTagLab;
}

#pragma mark - lazy load

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        [self createUI];
    }
    return self;
}
- (instancetype)init {
    return [self initWithFrame:CGRectZero];
}

- (void)createUI {
    [self addSubview:self.courseImageView];
    [self addSubview:self.maskLayerView];
    [self addSubview:self.newTagLabel];
    
    [self addSubview:self.iconImageView];
    [self addSubview:self.describeLabel];
    [self addSubview:self.titleLabel];
    
    [self addSubview:self.practicedLabel];
    [self addSubview:self.practicedNumberLabel];
    [self addSubview:self.tagImageView];
    [self addSubview:self.courseTagLab];
    [self layoutUI];
}

- (void)layoutSubviews {
    [super layoutSubviews];
    [self.newTagLabel viewCornerWith:ViewRadiusMake(0, 0, 0, Corner_8)];
}

- (void)layoutUI {
    [self.courseImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsMake(0, 0, 0, 0));
    }];
    [self.maskLayerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.mas_bottom);
        make.left.right.mas_equalTo(0);
        make.height.mas_equalTo(WKDHPX(75));
    }];
    [self.newTagLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.mas_equalTo(0);
        make.size.mas_equalTo(CGSizeMake(WKDHPX(40), WKDHPX(22)));
    }];
    
    [self.iconImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_equalTo(-WKDHPX(12));
        make.left.mas_equalTo(WKDHPX(12));
        make.size.mas_equalTo(CGSizeMake(WKDHPX(16), WKDHPX(16)));
    }];
    [self.describeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.iconImageView);
        make.left.equalTo(self.iconImageView.mas_right).offset(WKDHPX(4));
    }];
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.iconImageView.mas_top).offset(-WKDHPX(4));
        make.left.mas_equalTo(WKDHPX(12));
        make.right.mas_equalTo(-WKDHPX(12));
    }];
    
    [self.practicedLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(WKDHPX(8));
        make.right.mas_equalTo(-WKDHPX(8));
        make.height.mas_equalTo(WKDHPX(18));
    }];
    [self.practicedNumberLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_equalTo(-WKDHPX(8));
        make.centerY.equalTo(self.describeLabel.mas_centerY);
    }];
    [self.tagImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.equalTo(@(WKDHPX(12)));
        make.height.mas_equalTo(WKDHPX(18));
    }];
    [self.courseTagLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.tagImageView.mas_centerY);
        make.left.equalTo(self.tagImageView.mas_right).offset(WKDHPX(8));
        make.height.mas_equalTo(WKDHPX(20));
    }];
}

- (void)setDetail {
    self.newTagLabel.text = @"New";
    self.practicedLabel.text = @"500人练过";
    self.titleLabel.text = @"单车垫上主动拉伸";
    self.describeLabel.text = @"M4强化｜35分钟｜450千卡";
}
@end






