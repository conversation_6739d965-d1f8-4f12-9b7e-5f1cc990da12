//
//  MRKLiveCourseCells.m
//  Student_IOS
//
//  Created by merit on 2023/3/17.
//

#import "MRKLiveCourseCells.h"
#import "UIView+AZGradient.h"


@interface LiveVideoTableViewCell ()
@property (nonatomic, strong) UIImageView *bgImageView;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *timeLabel;
@property (nonatomic, strong) UIView *gradientView;
@property (nonatomic, strong) UIImageView *iconView;
@end

@implementation LiveVideoTableViewCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        self.contentView.backgroundColor = [UIColor colorWithHexString:@"#F8F8FA"];
        [self setUI];
    }
    return self;
}

-(void)setUI {
    [self.contentView addSubview:self.bgImageView];
    [self.bgImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.contentView).insets(UIEdgeInsetsMake(16.0, 14.0, 0.0, 14.0));
    }];
    
    [self.bgImageView addSubview:self.titleLabel];
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@(DHPX(12)));
        make.right.mas_equalTo(self.bgImageView.mas_right).offset(-100);
        make.bottom.equalTo(@(DHPX(-12)));
        make.height.equalTo(@25);
    }];
    
    
    [self.bgImageView addSubview:self.timeLabel];
    [self.timeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(@(DHPX(-12)));
        make.bottom.equalTo(@(DHPX(-12)));
        make.height.equalTo(@25);
    }];
    
    [self.bgImageView addSubview:self.iconView];
    [self.iconView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_equalTo(self.timeLabel.mas_left).offset(-4);
        make.size.mas_equalTo(CGSizeMake(16, 16));
        make.centerY.equalTo(self.timeLabel.mas_centerY);
    }];
}

- (UIImageView *)bgImageView {
    if (!_bgImageView) {
        UIImageView *img = [UIImageView new];
        img.contentMode = UIViewContentModeScaleToFill;
        img.layer.cornerRadius = 8;
        img.layer.backgroundColor = [UIColor colorWithHexString:@"#d8d8d8"].CGColor;
        img.layer.masksToBounds = YES;
        img.sd_imageTransition = SDWebImageTransition.fadeTransition;
        _bgImageView = img;
    }
    return  _bgImageView;
}

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        UILabel *l = [UILabel new];
        l.font = [UIFont fontWithName:fontNameMeDium size:23.0];
        l.textColor = [UIColor colorWithHexString:@"#ffffff"];
        _titleLabel = l;
    }
    return  _titleLabel;
}

- (UILabel *)timeLabel {
    if (!_timeLabel) {
        UILabel *l = [UILabel new];
        l.font = [UIFont fontWithName:fontNameMeDium size:13.0];
        l.textColor = [UIColor colorWithHexString:@"#ffffff"];
        _timeLabel = l;
    }
    return _timeLabel;
}

- (UIImageView *)iconView{
    if (!_iconView) {
        _iconView = [[UIImageView alloc]init];
        _iconView.contentMode = UIViewContentModeScaleAspectFit;
    }
    return _iconView;
}

- (void)configWithItem:(LiveModel *)item {
    if (item) {
        self.titleLabel.text = item.title;
        
        NSString *middleUrl = [NSString imageUrlClip:item.image andSize:CGSizeMake(RealScreenWidth -28, DHPX(149))];
        [self.bgImageView sd_setImageWithURL:[NSURL URLWithString:middleUrl]
                            placeholderImage:[UIImage imageNamed:@"pic_1"]];
        self.timeLabel.text = [NSString stringWithFormat:@"%@分钟" , item.videoDuration];
        [self.iconView sd_setImageWithURL:[NSURL URLWithString:item.equipmentTypeIcon] placeholderImage:nil];
    }
}


@end



#pragma mark - ----------- 实景视频 tableview cell -----------------
@implementation MRKLiveCourseTableCell
- (UIImageView *)mainImageView {
    if (_mainImageView) return _mainImageView;
    UIImageView *imagev = [[UIImageView alloc] init];
    imagev.contentMode = UIViewContentModeScaleAspectFill;
    imagev.sd_imageTransition = SDWebImageTransition.fadeTransition;
    imagev.cornerRadius = Corner_8;
    _mainImageView = imagev;
    return _mainImageView;
}
- (UIImageView *)shadowView{
    if (!_shadowView) {
        _shadowView = [[UIImageView alloc] init];
        [_shadowView az_setGradientBackgroundWithColors:@[[UIColor colorWithWhite:0 alpha:0.2],
                                                          [UIColor clearColor],
                                                        [UIColor colorWithWhite:0 alpha:0.6]]
                                                 locations:nil
                                                startPoint:CGPointMake(0, 0)
                                                  endPoint:CGPointMake(0, 1)];
        
    }
    return _shadowView;
}
/**图片标签**/
- (UIImageView *)tagImageView{
    if (!_tagImageView) {
        _tagImageView = [[UIImageView alloc] init];
        _tagImageView.contentMode = UIViewContentModeScaleAspectFit;
    }
    return _tagImageView;
}
- (UILabel *)titleLabel {
    if (_titleLabel) return _titleLabel;
    UILabel *label = [[UILabel alloc] init];
    label.font = kMedium_Font_NoDHPX(WKDHPX(20));
    label.textColor = [UIColor whiteColor];
    _titleLabel = label;
    return _titleLabel;
}
//- (UILabel *)practicedLabel {
//    if (_practicedLabel) return _practicedLabel;
//    UILabel *label = [[UILabel alloc] init];
//    label.font = kMedium_Font_NoDHPX(WKDHPX(11));
//    label.textColor = [UIColor whiteColor];
//    label.backgroundColor = [UIColor colorWithHexString:@"#000000" alpha:0.4];
//    label.cornerRadius = WKDHPX(4);
//    _practicedLabel = label;
//    return _practicedLabel;
//}

- (UILabel *)joinLabel {
    if (_joinLabel) return _joinLabel;
    UILabel *label = [[UILabel alloc] init];
    label.textColor = [UIColor whiteColor];
    label.backgroundColor = [UIColor colorWithHexString:@"#16D2E3"];
    label.font = kMedium_Font_NoDHPX(WKDHPX(14));
    label.textAlignment = NSTextAlignmentCenter;
    _joinLabel = label;
    return _joinLabel;
}

- (MRKLiveCourseRankVue *)rankVue {
    if (_rankVue) return _rankVue;
    MRKLiveCourseRankVue *vue = [[MRKLiveCourseRankVue alloc] initWithFrame:CGRectMake(0, 0, WKDHPX(130), WKDHPX(134))];
    _rankVue = vue;
    return _rankVue;
}

- (void)createUI {
    [self.contentView addSubview:self.mainImageView];
    [self.mainImageView addSubview:self.shadowView];
    [self.mainImageView addSubview:self.tagImageView];
    [self.mainImageView addSubview:self.titleLabel];
//    [self.mainImageView addSubview:self.practicedLabel];
    [self.mainImageView addSubview:self.rankVue];
    [self.mainImageView addSubview:self.joinLabel];
    [self layoutUI];
}

- (void)layoutUI {
    [self.mainImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsMake(WKDHPX(12), PageMargin_16, 0, PageMargin_16));
    }];
    [self.shadowView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsMake(0, 0, 0, 0));
    }];
    [self.tagImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.equalTo(@(WKDHPX(10)));
        make.height.mas_equalTo(WKDHPX(18));
    }];
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(WKDHPX(12));
        make.right.mas_offset(-WKDHPX(138));
        make.bottom.mas_offset(-WKDHPX(12));
        make.height.mas_equalTo(WKDHPX(28));
    }];
//    [self.practicedLabel mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.right.equalTo(@(WKDHPX(-4)));
//        make.top.equalTo(@(WKDHPX(4)));
//    }];
    [self.rankVue mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.right.mas_equalTo(0);
        make.width.mas_equalTo(WKDHPX(130));
        make.height.mas_equalTo(WKDHPX(134));
    }];
    [self.rankVue viewCornerWith:ViewRadiusMake(0, 0, WKDHPX(4), WKDHPX(4))];
    
    [self.joinLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_equalTo(-WKDHPX(12));
        make.centerY.equalTo(self.titleLabel);
        make.size.mas_equalTo(CGSizeMake(WKDHPX(72), WKDHPX(28)));
    }];
    self.joinLabel.cornerRadius = WKDHPX(14);
    self.joinLabel.text = @"立即挑战";
    self.joinLabel.hidden = true;
    self.rankVue.hidden = true;
}

- (void)configWithItem:(LiveModel *)item {
    if (item) {
        self.titleLabel.text = item.title;
        if ([item.color isNotBlank]) {
            [self refreshColor:[UIColor colorWithHexString:item.color]];
        }
        
        NSString *url = [NSString imageUrlClip:item.image andSize:CGSizeMake(RealScreenWidth - PageMargin_16 * 2 , WKDHPX(150))];
        @weakify(self);
        [self.mainImageView setImageWithURL:[NSURL URLWithString:url]
                                placeholder:[UIImage imageNamed:@"pic_1"]
                                    options:YYWebImageOptionProgressiveBlur|YYWebImageOptionSetImageWithFadeAnimation
                                 completion:^(UIImage * _Nullable image, NSURL * _Nonnull url, YYWebImageFromType from, YYWebImageStage stage, NSError * _Nullable error) {
            @strongify(self);
            if (stage == YYWebImageStageFinished && image && ![item.color isNotBlank]) {
                //*github使用方式二*/
                [image getSubjectColor:^(UIColor * _Nonnull color) {
                    item.color = color.hexString;
                    [self refreshColor:color];
                }];
            }
        }];
        
//        //23-06-06 V3.0.1 版本修改 zqp
//        self.practicedLabel.text = @" 刚刚练过 ";
//        self.practicedLabel.hidden = YES;
        
        //23-11-08 多阶会员
        self.tagImageView.hidden = YES;
        NSString *imageName = @"";
        NSInteger viptype = item.vipType.intValue;
        switch (viptype) {
            case 10: case 20:
                imageName = @"courseTag_vip";
                break;
            case 30:
                imageName = @"courseTag_enjoyvip";
                break;
            default:
                break;
        }
        if (imageName.length > 0) {
            self.tagImageView.hidden = NO;
            self.tagImageView.image = [UIImage imageNamed:imageName];
        }
        
        NSMutableArray *rankArray = [[NSMutableArray alloc] init];
        BOOL isHasMyRank = NO;
        NSInteger ind = MIN(item.rankInfo.topRanks.count, 4); // 判断是否大于4,取前四名
        for (int i = 0; i < ind; i ++) {
            LiveChallengeRankItemModel *it = item.rankInfo.topRanks[i];
            if ([it.userId isEqualToString: item.rankInfo.myRank.userId]) { // 判断是同一用户
                isHasMyRank = YES;
                it.isRankSelected = YES; // 标识出自己
                [rankArray appendObject:it];
            } else {
                [rankArray appendObject:it];
            }
        }
        if (!isHasMyRank && item.rankInfo.myRank != nil) {
            LiveChallengeRankItemModel *it = item.rankInfo.myRank;
            it.isRankSelected = YES; // 标识出自己
            if (rankArray.count == 4) {
                [rankArray replaceObjectAtIndex:3 withObject:it];
            } else {
                [rankArray appendObject:it];
            }
        }
        [self.rankVue configWithRankArray:rankArray type:@"1"];
        self.rankVue.hidden = !(item.equipTypeId.intValue != TreadmillEquipment && rankArray.count > 0); // 跑步机不参与实景挑战
        self.joinLabel.hidden = !(item.equipTypeId.intValue != TreadmillEquipment && rankArray.count == 0);
    }
}

- (void)refreshColor:(UIColor *)color {
    dispatch_async(dispatch_get_main_queue(), ^{
        [self.shadowView az_setGradientBackgroundWithColors:@[[color colorWithAlphaComponent:.2],[color colorWithAlphaComponent:.0],
                                                              [color colorWithAlphaComponent:.6]]
                                                  locations:@[@0,@0.5,@1.0]
                                                 startPoint:CGPointMake(0, 0)
                                                   endPoint:CGPointMake(0, 1)];
    });
}
@end


