//
//  MRKPlanCourseCells.m
//  Student_IOS
//
//  Created by merit on 2023/3/17.
//

#import "MRKPlanCourseCells.h"
#import "UIView+AZGradient.h"

@interface MRKPlanCourseTableCell()
@property (nonatomic, strong) MRKPlanCourseContentView *planView;

@end
@implementation MRKPlanCourseTableCell
- (void)createUI {
    [self.contentView addSubview:self.planView];
    [self.planView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsMake(WKDHPX(12), PageMargin_16, 0, PageMargin_16));
    }];
}

- (MRKPlanCourseContentView *)planView {
    if (!_planView) {
        _planView = [[MRKPlanCourseContentView alloc] init];
        _planView.cornerRadius = Corner_8;
    }
    return _planView;
}
- (void)setPlanDetail:(MRKCoursePlanModel *)item {
    [self.planView setPlanDetail:item];
}
@end



@implementation MRKPlanCourseContentView
- (UIImageView *)mainImageView {
    if (!_mainImageView) {
        UIImageView *imagev = [[UIImageView alloc] init];
        imagev.contentMode = UIViewContentModeScaleAspectFill;
        imagev.backgroundColor = [UIColor whiteColor];
        _mainImageView = imagev;
    }
    return _mainImageView;
}
- (UIImageView *)shadowView{
    if (!_shadowView) {
        _shadowView = [[UIImageView alloc] init];
        [_shadowView az_setGradientBackgroundWithColors:@[[UIColor clearColor],
                                                          [UIColor colorWithWhite:0 alpha:0.4]]
                                              locations:nil
                                             startPoint:CGPointMake(0, 0)
                                               endPoint:CGPointMake(0, 1)];
        
    }
    return _shadowView;
}
/**图片标签**/
- (UIImageView *)tagImageView{
    if (!_tagImageView) {
        _tagImageView = [[UIImageView alloc] init];
        _tagImageView.contentMode = UIViewContentModeScaleAspectFit;
    }
    return _tagImageView;
}

- (UILabel *)titleLab {
    if (_titleLab) return _titleLab;
    UILabel *label = [[UILabel alloc] init];
    label.font = kMedium_Font_NoDHPX(WKDHPX(20));
    label.textColor = [UIColor whiteColor];
    _titleLab = label;
    return _titleLab;
}

- (MRKPlanLabel *)tipLabel{
    if (_tipLabel) return _tipLabel;
    _tipLabel = [[MRKPlanLabel alloc] init];
    [_tipLabel planUILayout];
    return _tipLabel;
}

- (instancetype)init {
    if (self = [super init]) {
        [self initUI];
    }
    return self;
}
- (void)initUI {
    [self addSubview:self.mainImageView];
    [self.mainImageView addSubview:self.shadowView];
    [self.mainImageView addSubview:self.tagImageView];
    [self.mainImageView addSubview:self.titleLab];
    [self.mainImageView addSubview:self.tipLabel];
    
    [self layoutUI];
}
- (void)layoutUI {
    [self.mainImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsMake(0, 0, 0, 0));
    }];
    [self.shadowView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.bottom.right.mas_equalTo(0);
        make.top.equalTo(self.mainImageView.mas_centerY);
    }];
    [self.tagImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.equalTo(@(WKDHPX(10)));
        make.height.mas_equalTo(WKDHPX(18));
    }];
    [self.titleLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(WKDHPX(12));
        make.right.bottom.mas_equalTo(-WKDHPX(12));
    }];
    [self.tipLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(WKDHPX(12));
        make.height.mas_equalTo(WKDHPX(22));
        make.bottom.equalTo(self.titleLab.mas_top).offset(-WKDHPX(8));
    }];
}

- (void)setPlanDetail:(MRKCoursePlanModel *)item {
    NSString *middleURl = [NSString imageUrlClip:item.mainFigure andSize:CGSizeMake(MainWidth - PageMargin_16*2, WKDHPX(150))];
    [self.mainImageView setImageWithURL:[NSURL URLWithString:middleURl]
                            placeholder:[UIImage imageNamed:@"pic_1"]
                                options:YYWebImageOptionProgressiveBlur|YYWebImageOptionSetImageWithFadeAnimation
                             completion:nil];
    
    self.titleLab.text = item.title;
    self.tipLabel.cycletime = item.planningCycle;
    self.tipLabel.describe = [NSString stringWithFormat:@"%@｜%@", item.difficulty?:@"", item.equipmentTypeName?:@""];
    
    if (!item.hiddenTagIcon) {
        self.tagImageView.hidden = YES;
        NSString *imageName = @"";
        NSInteger viptype = item.vipType.intValue;
        switch (viptype) {
            case 10:
                imageName = @"courseTag_vip";
                break;
            case 30:
                imageName = @"courseTag_enjoyvip";
                break;
            default:
                break;
        }
        if (imageName.length > 0) {
            self.tagImageView.hidden = NO;
            self.tagImageView.image = [UIImage imageNamed:imageName];
        }
    }
}

- (void)setDetail {
    self.titleLab.text = @"夏季滑行燃脂大决斗";
    self.tipLabel.cycletime = @"2周";
    self.tipLabel.describe = @"小宇｜M4强化｜动感单车";
}
@end


