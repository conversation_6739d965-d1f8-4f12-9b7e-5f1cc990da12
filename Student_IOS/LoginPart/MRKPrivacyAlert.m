//
//  MRKPrivacyAlert.m
//  MeritInternation
//
//  Created by merit on 2022/1/13.
//

#import "MRKPrivacyAlert.h"
#import "WebViewViewController.h"

@interface MRKPrivacyAlert ()<UITextViewDelegate>
@property (nonatomic, strong) UIView *containerView;
@property (nonatomic, strong) UILabel *titleLab;
@property (nonatomic, strong) UIButton *ensureBtn;
@property (nonatomic, strong) UIButton *closeBtn;
@property (nonatomic, strong) UITextView *textView;
@end

@implementation MRKPrivacyAlert

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = [UIColor colorWithWhite:0 alpha:0.5];
        
        [self addSubview:self.containerView];
        [self.containerView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.mas_equalTo(self.mas_centerY);
            make.centerX.mas_equalTo(self.mas_centerX);
            make.width.mas_equalTo(DHPX(300));
            make.width.mas_equalTo(DHPX(420));
        }];
        
        [self setupContainerSubViews];
    }
    return self;
}

- (void)setupContainerSubViews{
    
    [self.containerView addSubview:self.titleLab];
    [self.titleLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.containerView.mas_top).offset(DHPX(20));
        make.centerX.mas_equalTo(self.containerView);
        make.width.mas_equalTo(DHPX(230));
        make.height.mas_equalTo(DHPX(25));
    }];
    
    [self.containerView addSubview:self.textView];
    [self.textView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.titleLab.mas_bottom).offset(DHPX(16));
        make.left.mas_equalTo(16);
        make.right.mas_equalTo(-16);
        make.height.mas_equalTo(DHPX(230));
    }];
    
    [self.containerView addSubview:self.ensureBtn];
    [self.ensureBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.textView.mas_bottom).offset(DHPX(16));
        make.centerX.mas_equalTo(self.containerView);
        make.width.mas_equalTo(DHPX(230));
        make.height.mas_equalTo(46);
    }];
    
    [self.containerView addSubview:self.closeBtn];
    [self.closeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.ensureBtn.mas_bottom).offset(DHPX(4));
        make.bottom.mas_equalTo(self.containerView.mas_bottom).offset(-DHPX(5));
        make.centerX.mas_equalTo(self.containerView);
        make.width.mas_equalTo(DHPX(230));
        make.height.mas_equalTo(46);
    }];
}

- (void)layoutContainerViewSubViews{
    [self layoutIfNeeded];
}

#pragma mark - Lazy
- (UIView *)containerView{
    if (!_containerView) {
        _containerView = [[UIView alloc] init];
        _containerView.backgroundColor = [UIColor whiteColor];
        _containerView.layer.cornerRadius = 8;
        _containerView.layer.masksToBounds = YES;
    }
    return _containerView;
}

- (UILabel *)titleLab {
    if (!_titleLab) {
        UILabel *label = [[UILabel alloc] init];
        label.textAlignment = NSTextAlignmentCenter;
        label.textColor = UIColorHex(#333333);
        label.font = [UIFont systemFontOfSize:16 weight:UIFontWeightMedium];
        label.text = @"温馨提示";
        _titleLab = label;
    }
    return _titleLab;
}

- (UITextView *)textView{
    if (!_textView) {
        UITextView *contentTextView = [UITextView new];
        contentTextView.editable = NO;
        contentTextView.delegate = self;
        
        NSString *str = @"亲爱的用户，感谢您信任并使用MERIT超燃脂！\n为了更好地保护您的权益，我们将通过《用户服务协议》和《隐私协议》帮助您了解我们收集、使用、存储和共享个人信息的情况，以及您所享有的相关权利。\n1、为了记录您的运动信息，我们会向您申请位置权限；\n2、为了连接您的运动设备，我们会向您申请开启蓝牙权限；\n3、为了账号安全，我们会向您申请系统设备权限收集设备信息、日志信息；\n4、为实现识别用户渠道，参加相关活动、综合统计分析等目的，我们可能会使用与功能相关的最小必要信息（口令、链接、统计参数等）；\n5、我们会努力采取各种安全技术保护您的个人信息。未经您授权同意，我们不会从第三方获取、共享或对外提供你的信息；\n6、您还可以访问、更正、删除您的个人信息，我们为您提供了注销、投诉等多种不同方式。";
        
        NSRange range1 = [str rangeOfString:@"《用户服务协议》"];
        NSRange range2 = [str rangeOfString:@"《隐私协议》"];
        NSMutableAttributedString *string = [[NSMutableAttributedString alloc] initWithString:str
                                                                                   attributes: @{NSFontAttributeName: [UIFont systemFontOfSize:14],
                                                                                                 NSForegroundColorAttributeName: UIColorHex(#333333)}];
        string.lineSpacing = 4;
        string.paragraphSpacing = 10;
        
        [string addAttributes:@{NSForegroundColorAttributeName: UIColorHex(#16D2E3)} range:range1];
        [string addAttributes:@{NSFontAttributeName: [UIFont systemFontOfSize:14 weight:UIFontWeightMedium]} range:range1];
        
        [string addAttributes:@{NSForegroundColorAttributeName: UIColorHex(#16D2E3)} range:range2];
        [string addAttributes:@{NSFontAttributeName: [UIFont systemFontOfSize:14 weight:UIFontWeightMedium]} range:range2];
        
        [string addAttribute:NSLinkAttributeName value:@"user://" range:range1];
        [string addAttribute:NSLinkAttributeName value:@"private://" range:range2];
        contentTextView.attributedText = string;
        
        _textView = contentTextView;
    }
    return _textView;
}

- (BOOL)textView:(UITextView *)textView shouldInteractWithURL:(NSURL *)URL inRange:(NSRange)characterRange interaction:(UITextItemInteraction)interaction {
    NSLog(@"url --- %@" , URL.absoluteURL);
    if ([[URL scheme] isEqualToString:@"user"]) {
        //《用户协议》点击事件
        [self userInteractWithType:0];
    }else if ([[URL scheme] isEqualToString:@"private"]) {
        //《隐私协议》点击事件
        [self userInteractWithType:1];
    }
    return NO;
}

- (void)userInteractWithType:(NSInteger)type{
        //《用户协议》点击事件
    WebViewViewController *vc = [WebViewViewController new];
    switch (type) {
        case 0:
            vc.titleString = @"用户服务协议";
            vc.htmlURL = MRKAppH5LinkCombine(MRKLinkUserProtocol);
            break;
        case 1:
            vc.titleString = @"隐私政策";
            vc.htmlURL = MRKAppH5LinkCombine(MRKLinkPrivacyProtocol);
            break;
        default:
            break;
    }
    
    MRKNavigationController *nav = [[MRKNavigationController alloc] initWithRootViewController:vc];
    nav.modalPresentationStyle = UIModalPresentationFullScreen;
    UIViewController *controller = [MRKPushManager presentingVC];
    [controller presentViewController:nav animated:YES completion:nil];
}

- (UIButton *)ensureBtn{
    if (!_ensureBtn) {
        UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
        btn.backgroundColor = UIColorHex(#16D2E3);
        [btn setTitle:@"同意并使用" forState:UIControlStateNormal];
        [btn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        btn.titleLabel.font = [UIFont systemFontOfSize:15 weight:UIFontWeightMedium];
        [btn addTarget:self action:@selector(ensureBtnClick:) forControlEvents:UIControlEventTouchUpInside];
        btn.layer.cornerRadius = 23;
        btn.layer.masksToBounds = YES;
        _ensureBtn = btn;
    }
    return _ensureBtn;
}

- (void)ensureBtnClick:(UIButton *)sender{
    NSString *activityKey = @"PrivateProtocol";
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    [defaults setBool:YES forKey:activityKey];
    [defaults synchronize];
    
    [[NSNotificationCenter defaultCenter] postNotificationName:@"AgreenPrivateProtocol" object:nil];
    
    [UIView animateWithDuration:0.3 animations:^{
        self.alpha = 0;
    } completion:^(BOOL finished) {
        [self removeFromSuperview];
    }];
}

- (UIButton *)closeBtn{
    if (!_closeBtn) {
        UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
        [btn setTitle:@"不同意并退出" forState:UIControlStateNormal];
        [btn setTitleColor:UIColorHex(#333333) forState:UIControlStateNormal];
        btn.titleLabel.font = [UIFont systemFontOfSize:13];
        [btn addTarget:self action:@selector(touchAction:) forControlEvents:UIControlEventTouchUpInside];
        _closeBtn = btn;
    }
    return _closeBtn;
}

- (void)touchAction:(UIButton *)sender {
    exit(0);
}
@end


