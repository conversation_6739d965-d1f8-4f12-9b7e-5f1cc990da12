//
//  MRKLocalPhoneLogin.m
//  Student_IOS
//
//  Created by merit on 2021/11/15.
//

#import "MRKLocalPhoneLogin.h"
#import "JVERIFICATIONService.h"
#import "MRKTraceManager.h"

@interface MRKLocalPhoneLogin ()
@property (nonatomic, strong) JVUIConfig *uiConfig;  //一键登录的UI配置
@end

@implementation MRKLocalPhoneLogin

- (instancetype)init {
    if (self = [super init]) {
        [self uiConfig];
    }
    return self;
}

- (void)localLoginFromVC:(UIViewController *)vc
          showAlertError:(BOOL)alertError
                  sucess:(void(^)(NSDictionary *result))sucessBlock{
    
    //先取消登录，在开始登陆，要不然登录成功之后再退出登录，不可以使用一键登录
    [JVERIFICATIONService dismissLoginControllerAnimated:NO completion:^{
        
    }];
    
    @weakify(self);
    //自定义一键登录页面
    [JVERIFICATIONService customUIWithConfig:self.uiConfig customViews:^(UIView *customAreaView) {
        @strongify(self);
        //授权登录页面，自定义页面
        UILabel *lable = [[UILabel alloc] init];
        lable.text = @"登录";
        [lable sizeToFit];
        lable.textColor = [UIColor colorWithHexString:@"#FFFFFF"];
        lable.font = [UIFont fontWithName:fontNameMeDium size:28];
        [customAreaView addSubview:lable];
        [lable mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(customAreaView.mas_top).offset(HDHPX(kNavBarHeight));
            make.left.equalTo(customAreaView.mas_left).offset(32);
            make.height.mas_equalTo(40);
            make.width.mas_equalTo(60);
        }];
        
        ///切换按钮
        UIButton * moreLogintype = [[UIButton alloc]init];
        moreLogintype.traceEventId = @"btn_login_code_login";
        [moreLogintype addTarget:self action:@selector(moreLogintypeClickShow) forControlEvents:UIControlEventTouchUpInside];
        [moreLogintype setTitle:@"使用其他手机号登录" forState:UIControlStateNormal];
        [moreLogintype setTitleColor:[UIColor colorWithHexString:@"#16D2E3"] forState:UIControlStateNormal];
        moreLogintype.titleLabel.font = [UIFont fontWithName:@"PingFangSC-Medium" size:14];
        [moreLogintype setImage:[UIImage imageNamed:@"login_arrow_iocn"]  forState:UIControlStateNormal];
        moreLogintype.semanticContentAttribute = UISemanticContentAttributeForceRightToLeft;
        [customAreaView addSubview:moreLogintype];
        [moreLogintype mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(customAreaView.mas_centerY).offset(HDHPX(50));
            make.centerX.equalTo(customAreaView.mas_centerX);
            make.height.mas_equalTo(40);
            make.width.mas_equalTo(200);
        }];
        
        // 添加背景播放
        [customAreaView addSubview:self.backVideo];
        [customAreaView sendSubviewToBack:self.backVideo];
        [self.backVideo mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.edges.mas_equalTo(UIEdgeInsetsMake(0, 0, 0, 0));
        }];
    }];
    
    if(![JVERIFICATIONService checkVerifyEnable]) {
        if (alertError) {
            [MBProgressHUD showMessage:@"当前网络环境不支持认证" toView:nil];
        }
        MLog(@"当前网络环境不支持认证");
        return;
    }
    
    ///授权登录，预约取号
    [JVERIFICATIONService preLogin:5*1000
                        completion:^(NSDictionary *result) {
        @strongify(self);
        NSLog(@"登录预取号 result:%@", result);
        
        //7000取号成功，弹出授权登录页面
        NSInteger code = [[result objectForKey:@"code"] integerValue];
        if (code == 7000) {
            [self quicklyloginButtonClick:vc sucess:sucessBlock];
        }
    }];
}

///授权成功之后，进入一键登录页面
- (void)quicklyloginButtonClick:(UIViewController *)vc sucess:(void(^)(NSDictionary *result))sucessBlock {
//    [self.backVideo playBackVideo];
    @weakify(self);
    [JVERIFICATIONService getAuthorizationWithController:vc
                                                    hide:NO
                                                animated:NO
                                                 timeout:5*1000
                                              completion:^(NSDictionary *result) {
        @strongify(self);
        UIViewController *base = [vc.view baseParentController];
        [[MRKTraceManager sharedInstance] manualUploadTraceType:2 pageTitle:base.title pageId:base.tracePageId eventId:@"btn_login_click_login" route:base.tracePageRoute duration:0 extendPara:@{}];
        MLog(@"登录--一键登录：%@",result);
        if ([[result allKeys] containsObject:@"loginToken"]) {
            if (sucessBlock) {
                [JVERIFICATIONService dismissLoginControllerAnimated:NO completion:nil];
                sucessBlock(result);
//                [self.backVideo pauseBackVideo];
                MLog(@"登录--一键登录成功");
            }
        }else if([[NSString stringWithFormat:@"%@",[result objectForKey:@"code"]] isEqualToString:@"6002"]){
            MLog(@"登录--一键登录按钮点击事件取消");
        }else{
            MLog(@"登录--一获取一键登录的信息失败");
//            [MBProgressHUD showMessage:@"授权登录失败！" toView:nil];
        }
        
        //无论成功失败 都需要清除缓存 22-02-22
        [JVERIFICATIONService clearPreLoginCache];
        
    } actionBlock:^(NSInteger type, NSString *content) {
        if (type == 6) {
            //登录
        }else{
            
        }
        NSLog(@"一键登录 actionBlock :%ld %@", (long)type , content);
    }];
}


- (void)moreLogintypeClickShow {
//    [self.backVideo pauseBackVideo];
    [JVERIFICATIONService dismissLoginControllerAnimated:NO completion:nil];
}


#pragma mark - lazy
- (JVUIConfig *)uiConfig {
    if (!_uiConfig) {
        
        //授权登录页面，页面view自定义配置
        JVUIConfig *config = [[JVUIConfig alloc] init];
        config.navCustom = YES;
        config.logoHidden = YES;
        //    config.prefersStatusBarHidden = YES;
        config.shouldAutorotate = [UIDevice.currentDevice isPad] ? YES :NO;
        config.autoLayout = YES;
        config.orientation = UIInterfaceOrientationPortrait;
        
        config.navText = [[NSAttributedString alloc] initWithString:@"登录统一认证"];
        config.navReturnImg = [UIImage imageNamed:@"icon_back"];
        config.navReturnHidden = YES;
        
        config.privacyTextFontSize = 12;
        config.privacyTextAlignment = NSTextAlignmentLeft;
        config.modalTransitionStyle = UIModalTransitionStyleCoverVertical;
        config.customLoadingViewBlock = ^(UIView *View) {
            MBProgressHUD *hud = [MBProgressHUD showHUDAddedTo:View animated:YES];
            hud.bezelView.layer.cornerRadius = 8.0f;
            hud.bezelView.style = MBProgressHUDBackgroundStyleBlur;
            hud.bezelView.blurEffectStyle = UIBlurEffectStyleLight;
            hud.label.text = @"加载中...";
            hud.label.font = [UIFont systemFontOfSize:14.0];
            hud.contentColor = [UIColor whiteColor];
            hud.margin = 20.f;
            hud.removeFromSuperViewOnHide = YES;
        };
        
        //logo
        config.logoImg = [UIImage imageNamed:@""];
        CGFloat logoWidth = config.logoImg.size.width?:100;
        CGFloat logoHeight = logoWidth;
        JVLayoutConstraint *logoConstraintX = [JVLayoutConstraint constraintWithAttribute:NSLayoutAttributeCenterX relatedBy:NSLayoutRelationEqual toItem:JVLayoutItemSuper attribute:NSLayoutAttributeCenterX multiplier:1 constant:0];
        JVLayoutConstraint *logoConstraintY = [JVLayoutConstraint constraintWithAttribute:NSLayoutAttributeBottom relatedBy:NSLayoutRelationEqual toItem:JVLayoutItemSuper attribute:NSLayoutAttributeCenterY multiplier:1 constant:-HDHPX(90)];
        JVLayoutConstraint *logoConstraintW = [JVLayoutConstraint constraintWithAttribute:NSLayoutAttributeWidth relatedBy:NSLayoutRelationEqual toItem:JVLayoutItemNone attribute:NSLayoutAttributeWidth multiplier:1 constant:logoWidth];
        JVLayoutConstraint *logoConstraintH = [JVLayoutConstraint constraintWithAttribute:NSLayoutAttributeHeight relatedBy:NSLayoutRelationEqual toItem:JVLayoutItemNone attribute:NSLayoutAttributeHeight multiplier:1 constant:logoHeight];
        config.logoConstraints = @[logoConstraintX,logoConstraintY,logoConstraintW,logoConstraintH];
        config.logoHorizontalConstraints = config.logoConstraints;
        
        //号码栏
        JVLayoutConstraint *numberConstraintX = [JVLayoutConstraint constraintWithAttribute:NSLayoutAttributeCenterX relatedBy:NSLayoutRelationEqual toItem:JVLayoutItemSuper attribute:NSLayoutAttributeCenterX multiplier:1 constant:0];
        JVLayoutConstraint *numberConstraintY = [JVLayoutConstraint constraintWithAttribute:NSLayoutAttributeCenterY relatedBy:NSLayoutRelationEqual toItem:JVLayoutItemSuper attribute:NSLayoutAttributeCenterY multiplier:1 constant:-HDHPX(170)];
        JVLayoutConstraint *numberConstraintW = [JVLayoutConstraint constraintWithAttribute:NSLayoutAttributeWidth relatedBy:NSLayoutRelationEqual toItem:JVLayoutItemNone attribute:NSLayoutAttributeWidth multiplier:1 constant:230];
        JVLayoutConstraint *numberConstraintH = [JVLayoutConstraint constraintWithAttribute:NSLayoutAttributeHeight relatedBy:NSLayoutRelationEqual toItem:JVLayoutItemNone attribute:NSLayoutAttributeHeight multiplier:1 constant:30];
        config.numberConstraints = @[numberConstraintX,numberConstraintY, numberConstraintW, numberConstraintH];
        config.numberHorizontalConstraints = config.numberConstraints;
        
        //slogan展示
        JVLayoutConstraint *sloganConstraintX = [JVLayoutConstraint constraintWithAttribute:NSLayoutAttributeCenterX relatedBy:NSLayoutRelationEqual toItem:JVLayoutItemSuper attribute:NSLayoutAttributeCenterX multiplier:1 constant:0];
        JVLayoutConstraint *sloganConstraintY = [JVLayoutConstraint constraintWithAttribute:NSLayoutAttributeTop relatedBy:NSLayoutRelationEqual toItem:JVLayoutItemNumber attribute:NSLayoutAttributeBottom multiplier:1 constant:10];
        JVLayoutConstraint *sloganConstraintW = [JVLayoutConstraint constraintWithAttribute:NSLayoutAttributeWidth relatedBy:NSLayoutRelationEqual toItem:JVLayoutItemNone attribute:NSLayoutAttributeWidth multiplier:1 constant:130];
        JVLayoutConstraint *sloganConstraintH = [JVLayoutConstraint constraintWithAttribute:NSLayoutAttributeHeight relatedBy:NSLayoutRelationEqual toItem:JVLayoutItemNone attribute:NSLayoutAttributeHeight multiplier:1 constant:20];
        config.sloganConstraints = @[sloganConstraintX,sloganConstraintY, sloganConstraintW, sloganConstraintH];
        config.sloganHorizontalConstraints = config.sloganConstraints;
        
        //登录按钮
        UIImage *login_nor_image = [UIImage imageNamed:@"login_third_btnBg"];
        UIImage *login_dis_image = [UIImage imageNamed:@"login_third_btnDisableBg"];//取下按钮的
        UIImage *login_hig_image = [UIImage imageNamed:@"login_third_btnBg"];
        if (login_nor_image && login_dis_image && login_hig_image) {
            config.logBtnImgs = @[login_nor_image, login_dis_image, login_hig_image];
        }
        CGFloat loginButtonWidth = MinMainWidth - 64;
        CGFloat loginButtonHeight = 50;
        JVLayoutConstraint *loginConstraintX = [JVLayoutConstraint constraintWithAttribute:NSLayoutAttributeCenterX relatedBy:NSLayoutRelationEqual toItem:JVLayoutItemSuper attribute:NSLayoutAttributeCenterX multiplier:1 constant:0];
        JVLayoutConstraint *loginConstraintY = [JVLayoutConstraint constraintWithAttribute:NSLayoutAttributeCenterY relatedBy:NSLayoutRelationEqual toItem:JVLayoutItemSuper attribute:NSLayoutAttributeCenterY multiplier:1 constant:0];
        JVLayoutConstraint *loginConstraintW = [JVLayoutConstraint constraintWithAttribute:NSLayoutAttributeWidth relatedBy:NSLayoutRelationEqual toItem:JVLayoutItemNone attribute:NSLayoutAttributeWidth multiplier:1 constant:loginButtonWidth];
        JVLayoutConstraint *loginConstraintH = [JVLayoutConstraint constraintWithAttribute:NSLayoutAttributeHeight relatedBy:NSLayoutRelationEqual toItem:JVLayoutItemNone attribute:NSLayoutAttributeHeight multiplier:1 constant:loginButtonHeight];
        config.logBtnConstraints = @[loginConstraintX,loginConstraintY,loginConstraintW,loginConstraintH];
        config.logBtnHorizontalConstraints = config.logBtnConstraints;
        
        //勾选框
        CGFloat checkViewWidth = 15;
        config.uncheckedImg = [UIImage imageNamed:@"protocol_unSelected"];
        config.checkedImg = [UIImage imageNamed:@"protocol_selected"];
        JVLayoutConstraint *checkViewConstraintX = [JVLayoutConstraint constraintWithAttribute:NSLayoutAttributeLeft relatedBy:NSLayoutRelationEqual toItem:JVLayoutItemSuper attribute:NSLayoutAttributeLeft multiplier:1 constant:45];
        JVLayoutConstraint *checkViewConstraintY = [JVLayoutConstraint constraintWithAttribute:NSLayoutAttributeCenterY relatedBy:NSLayoutRelationEqual toItem:JVLayoutItemPrivacy attribute:NSLayoutAttributeCenterY multiplier:1 constant:0];
        JVLayoutConstraint *checkViewConstraintW = [JVLayoutConstraint constraintWithAttribute:NSLayoutAttributeWidth relatedBy:NSLayoutRelationEqual toItem:JVLayoutItemNone attribute:NSLayoutAttributeWidth multiplier:1 constant:checkViewWidth];
        JVLayoutConstraint *checkViewConstraintH = [JVLayoutConstraint constraintWithAttribute:NSLayoutAttributeHeight relatedBy:NSLayoutRelationEqual toItem:JVLayoutItemNone attribute:NSLayoutAttributeHeight multiplier:1 constant:checkViewWidth];
        config.checkViewConstraints = @[checkViewConstraintX,checkViewConstraintY,checkViewConstraintW,checkViewConstraintH];
        config.checkViewHorizontalConstraints = config.checkViewConstraints;
        
        //隐私
        CGFloat spacing = checkViewWidth + 20 + 5;
        config.privacyState = YES;
        config.privacyLineSpacing = 4;
        JVLayoutConstraint *privacyConstraintX = [JVLayoutConstraint constraintWithAttribute:NSLayoutAttributeLeft relatedBy:NSLayoutRelationEqual toItem:JVLayoutItemSuper attribute:NSLayoutAttributeLeft multiplier:1 constant:spacing+25];
        JVLayoutConstraint *privacyConstraintX2 = [JVLayoutConstraint constraintWithAttribute:NSLayoutAttributeRight relatedBy:NSLayoutRelationEqual toItem:JVLayoutItemSuper attribute:NSLayoutAttributeRight multiplier:1 constant:-spacing-20];
        JVLayoutConstraint *privacyConstraintY = [JVLayoutConstraint constraintWithAttribute:NSLayoutAttributeBottom relatedBy:NSLayoutRelationEqual toItem:JVLayoutItemSuper attribute:NSLayoutAttributeBottom multiplier:1 constant:-HDHPX(20)];
        JVLayoutConstraint *privacyConstraintH = [JVLayoutConstraint constraintWithAttribute:NSLayoutAttributeHeight relatedBy:NSLayoutRelationEqual toItem:JVLayoutItemNone attribute:NSLayoutAttributeHeight multiplier:1 constant:35];
        config.privacyConstraints = @[privacyConstraintX,privacyConstraintX2,privacyConstraintY,privacyConstraintH];
        config.privacyHorizontalConstraints = config.privacyConstraints;
        
        
        //loading
        JVLayoutConstraint *loadingConstraintX = [JVLayoutConstraint constraintWithAttribute:NSLayoutAttributeCenterX relatedBy:NSLayoutRelationEqual toItem:JVLayoutItemSuper attribute:NSLayoutAttributeCenterX multiplier:1 constant:0];
        JVLayoutConstraint *loadingConstraintY = [JVLayoutConstraint constraintWithAttribute:NSLayoutAttributeCenterY relatedBy:NSLayoutRelationEqual toItem:JVLayoutItemSuper attribute:NSLayoutAttributeCenterY multiplier:1 constant:0];
        JVLayoutConstraint *loadingConstraintW = [JVLayoutConstraint constraintWithAttribute:NSLayoutAttributeWidth relatedBy:NSLayoutRelationEqual toItem:JVLayoutItemNone attribute:NSLayoutAttributeWidth multiplier:1 constant:30];
        JVLayoutConstraint *loadingConstraintH = [JVLayoutConstraint constraintWithAttribute:NSLayoutAttributeHeight relatedBy:NSLayoutRelationEqual toItem:JVLayoutItemNone attribute:NSLayoutAttributeHeight multiplier:1 constant:30];
        config.loadingConstraints = @[loadingConstraintX,loadingConstraintY,loadingConstraintW,loadingConstraintH];
        config.loadingHorizontalConstraints = config.loadingConstraints;
        
        config.authPageBackgroundImage = [UIImage imageWithColor:[UIColor colorWithHexString:@"#1E2528" alpha:0.8]];
        config.preferredStatusBarStyle = UIStatusBarStyleLightContent;
        
        config.agreementNavReturnImage = [UIImage imageNamed:@"icon_back-4"];
        config.logoHidden = YES;
        config.logBtnText = @"本机号码一键登录";
        config.logBtnTextColor = [UIColor whiteColor];
        config.logBtnFont = [UIFont systemFontOfSize:15 weight:UIFontWeightMedium];
        config.numberColor = [UIColor whiteColor];
      
        config.appPrivacyOne = @[@"《用户协议》",MRKAppH5LinkCombine(MRKLinkUserProtocol)];
        config.appPrivacyTwo = @[@"《隐私政策》",MRKAppH5LinkCombine(MRKLinkPrivacyProtocol)];
        config.privacyComponents = @[@"请先阅读并同意",@"、",@"和",@" "];
        config.appPrivacyColor = @[[UIColor whiteColor], [UIColor colorWithHexString:@"#16D2E3"]];
        config.sloganTextColor = [UIColor whiteColor];
        config.numberSize = 24;
        config.numberFont = [UIFont fontWithName:@"DIN Alternate" size:30];
        config.privacyState = YES;
        
        _uiConfig = config;
    }
    return _uiConfig;
}

- (void)dealloc {
    NSLog(@"😊😊😊😊 MRKLocalPhoneLogin dealloc 😊😊😊😊");
}
@end

