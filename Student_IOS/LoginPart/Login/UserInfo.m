//
//  UserInfo.m
//  Student_IOS
//
//  Created by merit on 2022/11/8.
//

#import "UserInfo.h"
#import "Login.h"
#import "User.h"
#import "MRKLoginModel.h"
#import "MRKUserInfoRequest.h"
#import "MRKBuglyManager.h"
#import "MRKUserInfoRequest.h"
#import "MRKTimeManager.h"


@implementation UserInfo

+ (NSString *)creatTime {
    User *user = [Login curLoginUser];
    NSString *userCreatTime = user.basicInfo.createTime;
    return [MRKTimeManager stringDatesToMMdd:userCreatTime];
}

+ (NSString *)userHeartRateTipStr{
    User *user = [Login curLoginUser];
    NSString *minRate = user.otherInfo.minBurnRate.stringValue;
    NSString *maxRate = user.otherInfo.maxBurnRate.stringValue;
    if (user && [minRate isNotBlank] && [maxRate isNotBlank]) {
        return [NSString stringWithFormat:@"(%@~%@)",minRate, maxRate];
    }
    return @"(100~180)";
}

#pragma mark sex
+ (NSString *)sex{
    User *user = [Login curLoginUser];
    return user.basicInfo.sex.stringValue?:@"";
}

#pragma mark phoneNum
+ (NSString *)phoneNum{
    User *user = [Login curLoginUser];
    return user.basicInfo.mobile?:@"";
}

#pragma mark age
+ (int)age{
    User *user = [Login curLoginUser];
    return user.basicInfo.age;
}

#pragma mark vipDays
+ (int)vipDays{
    User *user = [Login curLoginUser];
    return user.memberInfo.days;
}

#pragma mark weight
+ (NSNumber *)weight{
    User *user = [Login curLoginUser];
    return user.healthInfo.weight;
}

#pragma mark height
+ (NSNumber *)height{
    User *user = [Login curLoginUser];
    return user.healthInfo.height;
}

#pragma mark bmi
+ (NSNumber *)bmi{
    User *user = [Login curLoginUser];
    return user.healthInfo.bmi;
}

#pragma mark isMember
+ (int)vipType{
    User *user = [Login curLoginUser];
    return user.memberInfo.vipType;
}

+ (BOOL)isMember{
    User *user = [Login curLoginUser];
    return user.memberInfo.isMember;
}

+ (BOOL)isXEnjoyVip{
    User *user = [Login curLoginUser];
    return (user.memberInfo.vipType == 30) && user.memberInfo.isMember;
}

+ (BOOL)isXEnjoy{
    User *user = [Login curLoginUser];
    return user.memberInfo.vipType == 30;
}

+ (BOOL)isNormalMember{
    User *user = [Login curLoginUser];
    return (user.memberInfo.vipType == 10) && !user.memberInfo.isMember;
}

#pragma mark avatar
+ (NSString *)avatar {
    User *user = [Login curLoginUser];
    NSString *avatar = user.basicInfo.avatar;
    if ([avatar isNotBlank]) {
        return avatar;
    }
    return @"";
}

+ (UIImage *)avatarHoldingImage {
    return [UIImage imageNamed:@"avater_holder"];
}

#pragma mark avatarBox
+ (NSString *)avatarBox {
    User *user = [Login curLoginUser];
    NSString *avatarBox = user.levelInfo.avatarBox;
    if ([avatarBox isNotBlank]) {
        return avatarBox;
    }
    return @"";
}

#pragma mark levelIcon
+ (NSString *)levelIcon {
    User *user = [Login curLoginUser];
    NSString *levelIcon = user.levelInfo.icon;
    if ([levelIcon isNotBlank]) {
        return levelIcon;
    }
    return @"";
}

#pragma mark level
+ (int)level {
    User *user = [Login curLoginUser];
    return user.levelInfo.level;
}

#pragma mark medalNum
+ (int)medalNum {
    User *user = [Login curLoginUser];
    return user.medalInfo.userMedalSize;
}

#pragma mark nickName
+ (NSString *)nickName{
    User *user = [Login curLoginUser];
    NSString *nickname = user.basicInfo.nickname;
    if ([nickname isNotBlank]) {
        return nickname;
    }
    return @"";
}

#pragma mark userVipIcon
+ (UIImage *)userVipIcon{
    if (UserInfo.isXEnjoyVip) {
        return [UIImage imageNamed:@"mine_top_xenjoy"];
    } else if (UserInfo.isMember) {
        return [UIImage imageNamed:@"mine_top_vip"];
    }
    return nil;
}

+ (UIImage *)userIcon{
    if (UserInfo.isXEnjoyVip) {
        return [UIImage imageNamed:@"mine_top_xenjoy"];
    } else if (UserInfo.isMember) {
        return [UIImage imageNamed:@"mine_top_vip"];
    } else if (UserInfo.isNormalMember) {
        return [UIImage imageNamed:@"mine_top_notvip"];
    }
    return nil;
}

#pragma mark userVipStoneIcon
+ (UIImage *)userVipStoneIcon {
    if (!UserInfo.isMember){
        return nil;
    }
    
    UIImage *icon = nil;
    int vipType = UserInfo.vipType;
    switch (vipType) {
        case 10:
            icon = [UIImage imageNamed:@"record_vip"];
            break;
        case 30:
            icon = [UIImage imageNamed:@"record_xenjoy"];
            break;
        default:
            break;
    }
    return icon;
}


#pragma mark targetWeight
+ (NSString *)targetWeight{
    User *user = [Login curLoginUser];
    NSString *targetWeight = user.healthInfo.targetWeight;
    if ([targetWeight isNotBlank]) {
        return targetWeight;
    }
    return @"";
}











#pragma mark userId
+ (NSString *)userId {
    User *user = [Login curLoginUser];
    NSString *accountId = user.basicInfo.accountId;
    if ([accountId isNotBlank]) {
        return accountId;
    }
    return @"";
}

#pragma mark token
+ (NSString *)token {
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    NSString *token = [defaults objectForKey:@"token"];
    return token ?:@"";
}

+ (void)setToken:(NSString *)token {
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    if ([token isNotBlank]){
        [defaults setObject:token forKey:@"token"];
        [defaults synchronize];
        return;
    }
    [defaults removeObjectForKey:@"token"];
    [defaults synchronize];
}

///删除用户信息
+ (void)deleteUserInfo {
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    [defaults removeObjectForKey:kLoginUserDict];
    [defaults removeObjectForKey:kLoginStatus];
    [defaults removeObjectForKey:WeightScaleUserID];
    [defaults removeObjectForKey:@"CourseNotesTip"]; ///课程须知
    [defaults removeObjectForKey:@"CourseLiveChallengeNotesTip"]; ///实景挑战退出须知阅读状态
    [defaults synchronize];
}


///更新用户信息
+ (void)updateUserKey:(NSString *)key value:(NSString *)value {
    if (!value || value.length <= 0) {
        return;
    }
    
    NSDictionary *data = [[NSUserDefaults standardUserDefaults] objectForKey:kLoginUserDict];
    NSMutableDictionary *newData = data.mutableCopy;
    [newData setObject:value forKey:key];
    if (newData) {
        NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
        [defaults setObject:newData forKey:kLoginUserDict];
        [defaults synchronize];
        
        [Login changeValueWithcurLoginUser];
    }
}

///接口更新用户信息
+ (void)updateUserInfoWithParm:(NSDictionary *)parm success:(YTKRequestCompletionBlock)successBlock failure:(YTKRequestCompletionBlock)failureBlock{
    PutUserInfoRequest *reg = [[PutUserInfoRequest alloc] init];
    reg.requestData = parm.mutableCopy;
    [reg startWithCompletionBlockWithSuccess:successBlock failure:failureBlock];
}

///登录成功，存储登录信息
+ (void)loginTokenStorage:(id)data{
    if ([data isKindOfClass:[MRKLoginModel class]]) {
        MRKLoginModel *model = (MRKLoginModel *)data;
        UserInfo.token = model.token;
        [Login doLogin:model.userInfoData];  ///存储用户信息
        [[MRKBuglyManager sharedInstance] setUserIdentifier:model.accountId]; ///bugly上报添加id
    }
}




///用户信息
+ (void)updateUserInfo{
    [UserInfo requestUserInfoCompletion:^{
            
    }];
}

///用户信息
+ (void)requestUserInfoCompletion:(void (^)(void))completion{
    MRKUserInfoRequest *req = [[MRKUserInfoRequest alloc] init];
    req.notTipError = YES;
    [req startWithCompletionBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSDictionary *data = [request.responseObject valueForKeyPath:@"data"];
        [Login doLogin:data];
        
        NSString *userId = [request.responseObject valueForKeyPath:@"data.userBasicDataDTO.accountId"];
        if([userId isNotBlank]){
            [[MRKBuglyManager sharedInstance] setUserIdentifier:userId];
        }
        completion();
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        
    }];
}

@end
