//
//  UserInfo.h
//  Student_IOS
//
//  Created by merit on 2022/11/8.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface UserInfo : NSObject
@property (class, readonly, nonatomic, copy) NSString *creatTime;///yyyy-MM-dd
@property (class, readonly, nonatomic, copy) NSString *userHeartRateTipStr;///用户建议心率区间
@property (class, readonly, nonatomic, copy) NSString *phoneNum;
@property (class, readonly, nonatomic, copy) NSString *sex;
@property (class, readonly, nonatomic, assign) int age;
@property (class, readonly, nonatomic, assign) int vipDays; ///剩余会员天数
@property (class, readonly, nonatomic, strong) NSNumber *weight;
@property (class, readonly, nonatomic, strong) NSNumber *height;
@property (class, readonly, nonatomic, strong) NSNumber *bmi;

@property (class, readonly, nonatomic, assign) int vipType;
@property (class, readonly, nonatomic, assign) BOOL isMember;     ///是否VIP
@property (class, readonly, nonatomic, assign) BOOL isXEnjoyVip;  ///是否绝影VIP
@property (class, readonly, nonatomic, assign) BOOL isXEnjoy;     ///是否绝影
@property (class, readonly, nonatomic, assign) BOOL isNormalMember; ///是否是普通用户
///
@property (class, readonly, nonatomic, copy) NSString *avatar;
@property (class, readonly, nonatomic, strong) UIImage *avatarHoldingImage;

@property (class, readonly, nonatomic, copy) NSString *avatarBox;
@property (class, readonly, nonatomic, copy) NSString *levelIcon; ///等级icon
@property (class, readonly, nonatomic, assign) int level; ///等级
///
@property (class, readonly, nonatomic, assign) int medalNum; ///勋章数量
@property (class, readonly, nonatomic, copy) NSString *nickName;
@property (class, readonly, nonatomic, copy) NSString *userId;

@property (class, readonly, nonatomic, strong) UIImage *userVipIcon;///vip对应身份图标
@property (class, readonly, nonatomic, strong) UIImage *userIcon;///用户对应身份图标
@property (class, readonly, nonatomic, strong) UIImage *userVipStoneIcon;///vip对应钻石图标


///目标体重
@property (class, readonly, nonatomic, copy) NSString *targetWeight;


/// token 为nil, @"", null 会清除token数据
@property (class, readwrite, nonatomic, nullable, copy) NSString *token;

///删除用户信息
+ (void)deleteUserInfo;

///本地储存更新用户信息
+ (void)updateUserKey:(NSString *)key value:(NSString *)value;

///接口更新用户信息
+ (void)updateUserInfoWithParm:(NSDictionary *)parm success:(YTKRequestCompletionBlock)successBlock failure:(YTKRequestCompletionBlock)failureBlock;

///登录成功，存储登录信息
+ (void)loginTokenStorage:(id)data;

///用户信息
+ (void)updateUserInfo;

///用户信息
+ (void)requestUserInfoCompletion:(void (^)(void))completion;

@end

NS_ASSUME_NONNULL_END
