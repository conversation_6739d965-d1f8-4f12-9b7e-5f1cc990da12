//
//  User.h
//  Taogong_AO
//
//  Created by JQ on 2020/1/10.
//  Copyright © 2020 Taogong. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "MRKBaseModel.h"


@class UserBasicDataDTO;
@class UserHealthDataDTO;
@class MemberInfoDataDTO;
@class MemberInfoExclusiveCodes;
@class UserLevelDataDTO;
@class UserMedalInfoDataDTO;
@class UserOtherDataDTO;


@interface User : MRKBaseModel
@property (nonatomic, strong) UserBasicDataDTO *basicInfo;
@property (nonatomic, strong) UserHealthDataDTO *healthInfo;
@property (nonatomic, strong) MemberInfoDataDTO *memberInfo;
@property (nonatomic, strong) UserLevelDataDTO *levelInfo;
@property (nonatomic, strong) UserMedalInfoDataDTO *medalInfo;
@property (nonatomic, strong) UserOtherDataDTO *otherInfo;
@end





///用户基础信息
@interface UserBasicDataDTO : MRKBaseModel
@property (nonatomic, copy) NSString *createTime;
@property (nonatomic, copy) NSString *accountId;
@property (nonatomic, copy) NSString *nickname;
@property (nonatomic, copy) NSString *mobile;
@property (nonatomic, copy) NSString *avatar;
@property (nonatomic, strong) NSNumber *sex; ///性别：0-未知，1-男，2-女
@property (nonatomic, copy) NSString *email;
@property (nonatomic, copy) NSString *province;
@property (nonatomic, copy) NSString *provinceNo;
@property (nonatomic, copy) NSString *city;
@property (nonatomic, copy) NSString *cityNo;
@property (nonatomic, copy) NSString *area;
@property (nonatomic, copy) NSString *areaNo;
@property (nonatomic, copy) NSString *birthday;
@property (nonatomic, assign) int age;
@end





///用户健康信息
@interface UserHealthDataDTO : MRKBaseModel
@property (nonatomic, strong) NSNumber *height;       ///身高，单位：CM
@property (nonatomic, strong) NSNumber *heightFt;     ///
@property (nonatomic, strong) NSNumber *heightIn;     ///
@property (nonatomic, strong) NSNumber *weight;       ///体重，单位：kg
@property (nonatomic, strong) NSNumber *bmi;          ///BMI
@property (nonatomic, assign) NSInteger restRate;     ///静息心率
@property (nonatomic, assign) NSInteger maxRate;      ///最大心率
@property (nonatomic, assign) NSInteger reserveRate;  ///储备心率
@property (nonatomic, copy) NSString *bodyFatRate;
@property (nonatomic, copy) NSString *targetWeight;
@end





///用户会员信息
@interface MemberInfoDataDTO : MRKBaseModel
@property (nonatomic, assign) BOOL isMember;      ///是否是会员：0-否，1-是
@property (nonatomic, copy) NSString *expireDate; ///过期日期
@property (nonatomic, assign) int level;          ///会员等级
@property (nonatomic, assign) int days;           ///剩余会员天数 [向下取整]
@property (nonatomic, assign) int expireDays;     ///会员过期天数
@property (nonatomic, assign) int vipType;        ///会员类型 :   10-VIP   20-SVIP【废弃】   30-XVIP(绝影)
///
@property (nonatomic, assign) BOOL isExpire;      ///1过期, 0有效期内 🚨这个字段在下面items里才有返回
@property (nonatomic, strong) NSMutableArray <MemberInfoDataDTO *> *items; /// vip. svip. 绝影vip对应的会员信息
@property (nonatomic, strong) NSMutableArray <MemberInfoExclusiveCodes *> *exclusiveCodes; ///
///
@property (nonatomic, assign) BOOL manualPurchase;            ///手动续费用户[不判断是否是会员]
///
@property (nonatomic, assign) BOOL isAutoRenewal;             ///是否正订阅连续付费服务
@property (nonatomic, assign) BOOL isOpened;                  ///是否开通过会员[简单理解兑换的之类]
@property (nonatomic, assign) BOOL isPaid;                    ///是否购买过会员[简单理解花过钱的]
@end
@interface MemberInfoExclusiveCodes : MRKBaseModel
@property (nonatomic, copy) NSString *groupCode; ///权益组code
@property (nonatomic, copy) NSString *exclusiveCode; ///权益code
@end




///用户等级信息
@interface UserLevelDataDTO : MRKBaseModel
@property (nonatomic, copy) NSString *name;         ///等级名称
@property (nonatomic, copy) NSString *avatarBox;    ///等级头像框
@property (nonatomic, assign) int level;            ///等级
@property (nonatomic, assign) int nextLevel;        ///下个等级
@property (nonatomic, copy) NSString *icon;         ///等级图标
@property (nonatomic, assign) NSInteger burnPoint;  ///当前总燃力值
@property (nonatomic, assign) NSInteger todayPoint; ///当日燃力值
@property (nonatomic, assign) NSInteger levelPoint; ///等级总燃力值
@end





///用户徽章信息
@interface UserMedalInfoDataDTO : MRKBaseModel
@property (nonatomic, assign) int userMedalSize;    ///用户徽章数量
@end





///用户其他数据信息
@interface UserOtherDataDTO : MRKBaseModel
@property (nonatomic, strong) NSNumber *defaultWeight;   ///默认体重
@property (nonatomic, assign) int defaultAge;            ///默认年龄
@property (nonatomic, strong) NSNumber *minBurnRate;     ///最小燃脂心率  0.6
@property (nonatomic, strong) NSNumber *maxBurnRate;     ///最大燃脂心率  0.8
@property (nonatomic, strong) NSNumber *warnBurnRate;    ///心率预警阀值  0.9
@end


