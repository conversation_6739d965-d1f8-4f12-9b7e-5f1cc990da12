//
//  MRKLoginTipAlert.m
//  Student_IOS
//
//  Created by <PERSON>q on 2023/12/6.
//

#import "MRKLoginTipAlert.h"
#import "UIView+AZGradient.h"
#import "UIView+HyExtension.h"
#import "POP.h"




@interface MRKLoginTipAlert ()
@property (nonatomic, strong) UIView *blackView;
@property (nonatomic, strong) UIView *containerView;

@property (nonatomic, strong) UILabel *titleLab;
@property (nonatomic, strong) YYLabel *descripLab;
@property (nonatomic, strong) UIButton *cancelBtn;
@property (nonatomic, strong) UIButton *ensureBtn;
@end


@implementation MRKLoginTipAlert

- (void)show {
    if (self.contentView) {
        [self.contentView addSubview:self];
        
        [self createBlackView];
        [self createMessageView];
    }
}

- (void)hide {
    [super hide];
    
    if (self.contentView) {
        [self removeViews];
    }
}

- (void)createBlackView {
    self.blackView = [[UIView alloc] init];
    self.blackView.backgroundColor = [UIColor colorWithWhite:0 alpha:0.4];
    [self addSubview:self.blackView];
    [self.blackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsMake(0, 0, 0, 0));
    }];
}

- (void)createMessageView {
    // 创建信息窗体view
    self.containerView = [[UIView alloc] init];
    self.containerView.size = CGSizeMake(WKDHPX(295), WKDHPX(165));
    self.containerView.backgroundColor = [UIColor whiteColor];
    self.containerView.center = self.contentView.middlePoint;
    self.containerView.alpha = 0.f;
    [self addSubview:self.containerView];
    MrkCornerMaskWithViewRadius(self.containerView, ViewRadiusMake(8, 8, 8, 8));
    
    
    [self setupContainerSubViews];
    
    
    // 执行动画
    POPBasicAnimation  *alpha = [POPBasicAnimation animationWithPropertyNamed:kPOPViewAlpha];
    alpha.toValue             = @(1.f);
    alpha.duration            = 0.3f;
    [self.containerView pop_addAnimation:alpha forKey:nil];
    
    POPSpringAnimation *scale = [POPSpringAnimation animationWithPropertyNamed:kPOPLayerScaleXY];
    scale.fromValue           = [NSValue valueWithCGSize:CGSizeMake(1.1f, 1.1f)];
    scale.toValue             = [NSValue valueWithCGSize:CGSizeMake(1.f, 1.f)];
    scale.dynamicsTension     = 200;
    scale.dynamicsMass        = 1.3;
    scale.dynamicsFriction    = 10.3;
    scale.springSpeed         = 10;
    scale.springBounciness    = 10;
    [self.containerView.layer pop_addAnimation:scale forKey:nil];
    
    @weakify(self);
    scale.completionBlock = ^(POPAnimation *anim, BOOL finished) {
        @strongify(self);
        [self.containerView.subviews enumerateObjectsUsingBlock:^(__kindof UIView *obj, NSUInteger idx, BOOL * _Nonnull stop) {
            if ([obj isKindOfClass:[UIButton class]]) {
                UIButton *btn = obj;
                btn.userInteractionEnabled = YES;
            }
        }];
    };
}


- (void)removeViews {
    if (self.willDisappearBlock){
        self.willDisappearBlock(self);
    }
    [UIView animateWithDuration:0.2f animations:^{
        self.containerView.alpha     = 0.f;
        self.containerView.transform = CGAffineTransformMakeScale(0.9f, 0.9f);
    } completion:^(BOOL finished) {
        [self removeFromSuperview];
        if (self.didDisappearBlock){
            self.didDisappearBlock(self);
        }
    }];
}


- (void)setupContainerSubViews{
    [self.containerView addSubview:self.titleLab];
    [self.titleLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.containerView.mas_top).offset(WKDHPX(20));
        make.centerX.mas_equalTo(self.containerView.mas_centerX);
        make.height.mas_equalTo(WKDHPX(26));
    }];
    
    [self.containerView addSubview:self.descripLab];
    [self.descripLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.titleLab.mas_bottom).offset(WKDHPX(16));
        make.centerX.mas_equalTo(self.containerView.mas_centerX);
        make.width.mas_equalTo(WKDHPX(295));
        make.height.mas_equalTo(WKDHPX(25));
    }];
    
    [self.containerView addSubview:self.cancelBtn];
    [self.cancelBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_equalTo(self.containerView.mas_bottom).offset(-WKDHPX(20));
        make.right.mas_equalTo(self.containerView.mas_centerX).offset(-WKDHPX(5));
        make.size.mas_equalTo(CGSizeMake(WKDHPX(124), WKDHPX(40)));
    }];
    
    [self.containerView addSubview:self.ensureBtn];
    [self.ensureBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_equalTo(self.containerView.mas_bottom).offset(-WKDHPX(20));
        make.left.mas_equalTo(self.containerView.mas_centerX).offset(WKDHPX(5));
        make.size.mas_equalTo(CGSizeMake(WKDHPX(124), WKDHPX(40)));
    }];
    
   ////
    NSString *protocolL = self.alertType == LoginTipAlertStyleVip ? @"《会员服务协议》":@"《用户协议》";
    NSMutableAttributedString *message = [[NSMutableAttributedString alloc] init];
    NSMutableAttributedString *text1 = [[NSMutableAttributedString alloc] initWithString:protocolL];
    text1.font = [UIFont systemFontOfSize:14 weight:UIFontWeightRegular];
    text1.underlineStyle = NSUnderlineStyleSingle;
    [text1 setTextHighlightRange:text1.rangeOfAll
                           color:[UIColor colorWithHexString:@"#16D2E3"]
                 backgroundColor:[UIColor clearColor]
                       tapAction:^(UIView * _Nonnull containerView, NSAttributedString * _Nonnull text, NSRange range, CGRect rect) {
        
        [self userInteractWithType:0];
    }];
    [message appendAttributedString:text1];
    
    NSMutableAttributedString *text2= [[NSMutableAttributedString alloc] initWithString:@"和"];
    text2.font = [UIFont systemFontOfSize:14 weight:UIFontWeightRegular];
    text2.color = [UIColor colorWithHexString:@"#363A44"];
    [message appendAttributedString:text2];
    
    NSString *protocolR = self.alertType == LoginTipAlertStyleVip ? @"《自动续费服务协议》":@"《隐私协议》";
    NSMutableAttributedString *text3= [[NSMutableAttributedString alloc] initWithString:protocolR];
    text3.font = [UIFont systemFontOfSize:14 weight:UIFontWeightRegular];
    text3.underlineStyle = NSUnderlineStyleSingle;
    [text3 setTextHighlightRange:text3.rangeOfAll
                           color:[UIColor colorWithHexString:@"#16D2E3"]
                 backgroundColor:[UIColor clearColor]
                       tapAction:^(UIView * _Nonnull containerView, NSAttributedString * _Nonnull text, NSRange range, CGRect rect) {
        
        [self userInteractWithType:1];
    }];
    [message appendAttributedString:text3];
    
    self.descripLab.attributedText = message;
    self.descripLab.textAlignment = NSTextAlignmentCenter;
}

- (void)layoutContainerViewSubViews{
    [self layoutIfNeeded];
}

- (UILabel *)titleLab {
    if (!_titleLab) {
        UILabel *label = [[UILabel alloc] init];
        label.textAlignment = NSTextAlignmentCenter;
        label.textColor = UIColorHex(#363A44);
        label.font = kMedium_Font_NoDHPX(17);
        label.text = @"请先阅读并同意";
        _titleLab = label;
    }
    return _titleLab;
}

- (YYLabel *)descripLab {
    if (!_descripLab) {
        YYLabel *label = [[YYLabel alloc] init];
        _descripLab = label;
    }
    return _descripLab;
}

- (UIButton *)cancelBtn{
    if (!_cancelBtn) {
        UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
        btn.traceEventId = @"btn_login_close_click";
        [btn setBackgroundColor:UIColorHex(#F3F5F9)];
        [btn setTitle:@"不同意" forState:UIControlStateNormal];
        [btn setTitleColor:UIColorHex(#363A44) forState:UIControlStateNormal];
        [btn addTarget:self action:@selector(ensureBtnClick:) forControlEvents:UIControlEventTouchUpInside];
        btn.titleLabel.font = [UIFont systemFontOfSize:14 weight:UIFontWeightRegular];
        btn.layer.cornerRadius = WKDHPX(40)/2;
        btn.layer.masksToBounds = YES;
        _cancelBtn = btn;
    }
    return _cancelBtn;
}

- (UIButton *)ensureBtn{
    if (!_ensureBtn) {
        UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
        btn.traceEventId = @"btn_login_agree_continue_click";
        [btn setBackgroundColor:UIColorHex(#16D2E3)];
        [btn setTitle:@"同意并继续" forState:UIControlStateNormal];
        [btn setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
        [btn addTarget:self action:@selector(ensureBtnClick:) forControlEvents:UIControlEventTouchUpInside];
        btn.titleLabel.font = [UIFont systemFontOfSize:14 weight:UIFontWeightRegular];
        btn.layer.cornerRadius = WKDHPX(40)/2;
        btn.layer.masksToBounds = YES;
        _ensureBtn = btn;
    }
    return _ensureBtn;
}

- (void)ensureBtnClick:(UIButton *)sender{
    [self hide];
    
    NSInteger type = 0;
    if (sender == self.ensureBtn) {
        type = 1;
    }
    if (self.handle){
        self.handle(type);
    }
}

- (void)userInteractWithType:(NSInteger)type{
    if (self.routerHandle){
        self.routerHandle(type);
    }
}


/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/

@end

