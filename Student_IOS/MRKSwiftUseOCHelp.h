//
//  MRKSwiftUseOCHelp.h
//  Student_IOS
//
//  Created by merit on 2024/1/2.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface MRKSwiftUseOCHelp : NSObject

+ (NSString *)MRKAppH5LinkCombineSwift:(NSString *)remainingAbsoluteString;

+ (NSString *)MRKAppH5AILinkCombineSwift:(NSString *)remainingAbsoluteString;

+ (void)appMlog:(NSString *)log;

+ (void)manualUploadTraceType:(int)type
                    pageTitle:(NSString *)pageTitle
                       pageId:(NSString *)pageId
                      eventId:(NSString *)eventId
                        route:(NSString *)route
                     duration:(NSTimeInterval)duration
                   extendPara:(NSDictionary *)extendPara;
@end

NS_ASSUME_NONNULL_END

