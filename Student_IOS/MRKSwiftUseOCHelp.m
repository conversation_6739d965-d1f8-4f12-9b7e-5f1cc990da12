//
//  MRKSwiftUseOCHelp.m
//  Student_IOS
//
//  Created by merit on 2024/1/2.
//

#import "MRKSwiftUseOCHelp.h"

@implementation MRKSwiftUseOCHelp

+ (NSString *)MRKAppH5LinkCombineSwift:(NSString *)remainingAbsoluteString {
    return MRKAppH5LinkCombine(remainingAbsoluteString);
}

+ (NSString *)MRKAppH5AILinkCombineSwift:(NSString *)remainingAbsoluteString {
    return MRKAppAIH5LinkCombine(remainingAbsoluteString);
}

+ (void)MRKAppMlog:(NSString *)log{
    if ([log isNotBlank]) {
        MLog(log);
    }
}

+ (void)manualUploadTraceType:(int)type
                    pageTitle:(NSString *)pageTitle
                       pageId:(NSString *)pageId
                      eventId:(NSString *)eventId
                        route:(NSString *)route
                     duration:(NSTimeInterval)duration
                   extendPara:(NSDictionary *)extendPara {
    
    [[MRKTraceManager sharedInstance] manualUploadTraceType:type
                                                  pageTitle:pageTitle
                                                     pageId:pageId
                                                    eventId:eventId
                                                      route:route
                                                   duration:duration
                                                 extendPara:extendPara];
}


@end

