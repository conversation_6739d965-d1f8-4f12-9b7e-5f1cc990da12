//
//  CocoaPhotoViewViewController.m
//  Student_IOS
//
//  Created by MacPro on 2021/3/23.
//

#import "CocoaPhotoViewViewController.h"
#import "UIView+LXShadowPath.h"
#import "YYPhotoGroupView.h"


static NSString *cellIdentifier = @"CollectionViewCellIdentifier";

@interface CocoaPhotoViewViewController ()<UICollectionViewDataSource,UICollectionViewDelegateFlowLayout>
@property (nonatomic, strong) UICollectionView *collectionView;//容器视图
@property (nonatomic, strong) NSMutableArray * picInnerArray;
@property (nonatomic, assign) int page;
@end

@implementation CocoaPhotoViewViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.navTitle = @"个人相册";
    
    self.page = 1;
    self.picInnerArray = [[NSMutableArray alloc]init];
    [self uiViewLayout];
    // Do any additional setup after loading the view.
}

- (void)uiViewLayout {
    UICollectionViewFlowLayout *layout = [[UICollectionViewFlowLayout alloc]init];
    layout.scrollDirection = UICollectionViewScrollDirectionVertical;
    layout.minimumInteritemSpacing = 9;
    self.collectionView = [[UICollectionView alloc]initWithFrame:CGRectZero collectionViewLayout:layout];
    self.collectionView.showsVerticalScrollIndicator = NO;
    self.collectionView.delegate = self;
    self.collectionView.dataSource = self;
    self.collectionView.backgroundColor = [UIColor whiteColor];
    [self.collectionView registerClass:[CocoaViewCell class] forCellWithReuseIdentifier:cellIdentifier];
    [self.mrkContentView addSubview:self.collectionView];
    [self.collectionView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.mrkContentView.mas_top).offset(kNavBarHeight);
        make.width.mas_equalTo(RealScreenWidth);
        make.centerX.mas_equalTo(self.mrkContentView.mas_centerX);
        if (@available(iOS 11.0, *)) {
            make.bottom.equalTo(self.view.mas_safeAreaLayoutGuideBottom).offset(0);
        }else{
            make.bottom.equalTo(self.view.mas_bottom).offset(0);
        }
    }];
    
    @weakify(self);
    MJDIYAutoFooter *footer = [MJDIYAutoFooter footerWithRefreshingBlock:^{
        @strongify(self);
        self.page ++;
        [self refresh];
    }];
    footer.automaticallyChangeAlpha = YES;
    self.collectionView.mj_footer = footer;
    
    MJDIYHeader *header = [MJDIYHeader headerWithRefreshingBlock:^{
        @strongify(self);
        self.page = 1;
        [self refresh];
    }];
    header.automaticallyChangeAlpha = YES;
    self.collectionView.mj_header = header;
    
    [self.collectionView.mj_header beginRefreshing];
}

- (void)refresh {
    NSDictionary *parms = @{
        @"current":@(self.page),
        @"size":@"30",
        @"coachId":self.cocoaId?:@""
    };
    
    [MRKBaseRequest mrkGetRequestUrl:@"/user/coach/images"
                             andParm:parms
            completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        [self.collectionView.mj_header endRefreshing];
        [self.collectionView.mj_footer endRefreshing];
        
        if (self.page == 1) {
            [self.picInnerArray removeAllObjects];
        }
        
        NSArray *dataArray = [request.responseObject valueForKeyPath:@"data.records"];
        [self.picInnerArray addObjectsFromArray:dataArray];
        [self.collectionView reloadData];
        
        NSNumber *total = [request.responseObject valueForKeyPath:@"data.total"];
        self.collectionView.mj_footer.hidden = self.picInnerArray.count >= total.integerValue;
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        [self.collectionView.mj_header endRefreshing];
        [self.collectionView.mj_footer endRefreshing];
        
    }];
}



//设置容器中有多少个组
- (NSInteger)numberOfSectionsInCollectionView:(UICollectionView *)collectionView{
    return 1;
}

//设置每个组有多少个方块
- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section{
    return self.picInnerArray.count;
}

//设置方块的视图
- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath{
    CocoaViewCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:cellIdentifier forIndexPath:indexPath];
    [cell setImageUrl:self.picInnerArray[indexPath.row]];
    return cell;
}

#pragma mark - UICollectionViewDelegateFlowLayout
//设置各个方块的大小尺寸
- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath{
    CGFloat width = (RealScreenWidth -44)/3;
    return CGSizeMake(width, width);
}

//设置每一组的上下左右间距
- (UIEdgeInsets)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout insetForSectionAtIndex:(NSInteger)section{
    return UIEdgeInsetsMake(10, 10, 10, 10);
}

#pragma mark - UICollectionViewDelegate
//方块被选中会调用
- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath{
    NSLog(@"点击选择了第%ld组，第%ld个方块",indexPath.section,indexPath.row);
    
    UIImageView *fromView = nil;
    NSMutableArray *items = [NSMutableArray new];
    NSArray<NSString *> *images = self.picInnerArray;
    
    for (NSUInteger i = 0, max = images.count; i < max; i++) {
        CocoaViewCell *cell = (CocoaViewCell *)[collectionView cellForItemAtIndexPath:[NSIndexPath indexPathForItem:i inSection:0]];
        UIImageView *imgView = cell.middleImgeView;
        NSString *img = images[i];
        
        YYPhotoGroupItem *item = [YYPhotoGroupItem new];
        item.thumbView = imgView;
        item.largeImageURL = [NSURL URLWithString:img];
        item.largeImageSize = kScreenSize;
        [items addObject:item];
        if (i == indexPath.row) {
            fromView = imgView;
        }
    }
    
    YYPhotoGroupView *v = [[YYPhotoGroupView alloc] initWithGroupItems:items];
    [v presentFromImageView:fromView toContainer:self.navigationController.view animated:YES completion:nil];
}

//方块取消选中会调用
- (void)collectionView:(UICollectionView *)collectionView didDeselectItemAtIndexPath:(NSIndexPath *)indexPath{
    NSLog(@"取消选择第%ld组，第%ld个方块",indexPath.section,indexPath.row);
}



#pragma mark ---------Delegate -----------
- (BOOL)viewControllerIsNeedNavBar:(MRKBaseController *)viewController {
    return YES;
}


/*
 #pragma mark - Navigation
 
 // In a storyboard-based application, you will often want to do a little preparation before navigation
 - (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
 // Get the new view controller using [segue destinationViewController].
 // Pass the selected object to the new view controller.
 }
 */

@end



@implementation CocoaViewCell

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        [self addSubviewShow];
    }
    return self;
}

- (void)addSubviewShow {
    self.middleImgeView = [[UIImageView alloc] init];
    self.middleImgeView.layer.cornerRadius = 4;
    self.middleImgeView.layer.masksToBounds = YES;
    [self.contentView addSubview:self.middleImgeView];
    self.middleImgeView.contentMode = UIViewContentModeScaleAspectFill;
    
    [self.middleImgeView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.top.bottom.equalTo(self);
    }];
}

- (void)setImageUrl:(NSString *)url {
//    NSString *str = [MRKToolKit getPicCaijian:url andWith:self.bounds.size.width andHeight:self.bounds.size.height];
    [self.middleImgeView setImageWithURL:[NSURL URLWithString:url]
                          placeholder:placeImage
                              options:YYWebImageOptionProgressiveBlur|YYWebImageOptionSetImageWithFadeAnimation
                           completion:nil];
}
@end
