//
//  MRKSchedulePageViewModel.m
//  Student_IOS
//
//  Created by Jun<PERSON> on 2023/7/31.
//

#import "MRKSchedulePageViewModel.h"
#import "MRKDailyScheduleModel.h"
#import "MRKTrainingDataAPIClient.h"
#import "MRKAIPlanAPIClient.h"
static NSString *const detailSiganlName = @"SchedulePageSignalName";
static NSString *const errorSiganlName = @"updateErrorSignal";

@interface MRKSchedulePageViewModel ()
@property (nonatomic, strong) MRKPlanOverViewModel *planOverViewModel;           ///AI计划概览
@property (nonatomic, strong) RACSubject *updateDetailSignal;
@property (nonatomic, strong) RACSubject *updateErrorSignal;
@property (nonatomic, copy) NSString *currentDate; //当前日期
@end

@implementation MRKSchedulePageViewModel

- (instancetype)init {
    self = [super init];
    if(self){
        self.updateDetailSignal = [[RACSubject subject] setNameWithFormat:detailSiganlName];
        self.updateErrorSignal = [[RACSubject subject] setNameWithFormat:errorSiganlName];
        
        ///通知刷新日常目标数据
        [[NSNotificationCenter defaultCenter] addObserver:self
                                                 selector:@selector(refreshDataSource)
                                                     name:@"kRefreshDailyTargetNotification"
                                                   object:nil];
        ///
        [[NSNotificationCenter defaultCenter] addObserver:self
                                                 selector:@selector(refreshDataSource)
                                                     name:@"kRefreshDailyDietNotification"
                                                   object:nil];
    }
    return self;
}

- (void)dealloc
{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

#pragma mark - HTTP -
- (void)requestCalendarData:(void(^)(BOOL success))completion {
    @weakify(self);
    [MRKBaseRequest mrkGetRequestUrl:@"/user/health-calendar"
                             andParm:nil
            completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        @strongify(self);
        NSLog(@"request.responseObject ==== %@",request.responseObject);
        id data = [request.responseObject valueForKeyPath:@"data"];
        NSArray *array = [NSArray modelArrayWithClass:[CalendarModel class] json:data];
        self.dataArray = array.mutableCopy;
        
        ///查找数据是否匹配 [查找今日数据]
        __block NSInteger index = NSNotFound;
        [array enumerateObjectsUsingBlock:^(id  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
            CalendarModel *model = (CalendarModel *)obj;
            if (model.timeReference.intValue == 2){
                index = idx;
                *stop = YES;
            }
        }];
        
        if (index != NSNotFound) {
            CalendarModel *model = [array objectAtIndex:index];
            
            ///初次进来selectCalendarModel是空的, 默认选中当前数据
            if (!self.selectCalendarModel){
                self.selectCalendarModel = model;
                self.currentDate = model.date;
            }
            
            ///页面viewWillAppear时刷数据日期对不上
            if (self.currentDate && ![self.currentDate isEqualToString:model.date]){
                self.selectCalendarModel = model;
                self.currentDate = model.date;
            }
        }
        
        completion(YES);
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        completion(NO);
        [(RACSubject *)self.updateErrorSignal sendNext:@"endRefresh"];
    }];
}

- (void)refreshDataSource {
    ///当前选中数据为空, 初次进来
    if (self.selectCalendarModel == nil) {
        @weakify(self);
        [self requestCalendarData:^(BOOL success) {
            @strongify(self);
            if (success){
                [self refresh];
            }
        }];
        return;
    }
    /// 日期发生变化的时候
    NSString *date = [MRKToolKit getCurrentData];
    if (![date isEqualToString:self.currentDate]) {
        @weakify(self);
        [self requestCalendarData:^(BOOL success) {
            @strongify(self);
            if (success){
                [self refresh];
            }
        }];
        return;
    }
    
    [self refresh];
}



- (void)refresh{
    @weakify(self)
    RACSignal *dailyAction = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self requestDailyScheduleData:^{
            [subscriber sendNext:nil];
        }];
        return nil;
    }];
    
    RACSignal *dietAction = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self requestDietData:^{
            [subscriber sendNext:nil];
        }];
        return nil;
    }];
    
    //  AI计划概览
    RACSignal *planOverViewAction = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self requestPlanOverViewData:^{
            [subscriber sendNext:nil];
        }];
        return nil;
    }];
    
//    RACSignal *aiPlanInfoAction = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
//        @strongify(self);
//        [self requestCourseAIPlanInfoData:^{
//            [subscriber sendNext:nil];
//        }];
//        return nil;
//    }];
//    
//    RACSignal *planInfoAction = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
//        @strongify(self);
//        [self requestCoursePlanInfoData:^{
//            [subscriber sendNext:nil];
//        }];
//        return nil;
//    }];
    
 
    ///目标
    RACSignal *targetAction = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self requestTargetData:^{
            [subscriber sendNext:nil];
        }];
        return nil;
    }];
    
    RACSignal *bannerAction = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self requestDailyScheduleBannerData:^{
            [subscriber sendNext:nil];
        }];
        return nil;
    }];
    
    
    [[RACSignal combineLatest:@[targetAction, dietAction, planOverViewAction, dailyAction, bannerAction]] subscribeNext:^(id x) {
        @strongify(self);
        [(RACSubject *)self.updateDetailSignal sendNext:@"endRefresh"];
    }];
}


- (NSString *)selectDate {
    if (!self.selectCalendarModel){
        return @"";
    }
    CalendarModel *model = self.selectCalendarModel;
    return model.date;
}

///查询用户指定日期目标
- (void)requestTargetData:(void(^)(void))completion{
    @weakify(self);
    [MRKBaseRequest mrkGetRequestUrl:@"/user/get-daily-target"
                             andParm:@{@"date":self.selectDate?:@""}
            completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        @strongify(self);
        id data = [request.responseObject valueForKeyPath:@"data"];
        self.targetData = [DailyTargetModel modelWithJSON:data];
        
        /// 同步数据
        self.selectCalendarModel.calorie = self.targetData.calorie;
        self.selectCalendarModel.targetCalorie = self.targetData.targetCalorie;
        self.selectCalendarModel.sportTime = self.targetData.sportTime;
        self.selectCalendarModel.sportTargetTime = self.targetData.sportTargetTime;
        
        completion();
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        self.targetData = [[DailyTargetModel alloc] init];
        completion();
    }];
}

///饮食数据
- (void)requestDietData:(void(^)(void))completion{
    @weakify(self);
    [MRKBaseRequest mrkGetRequestUrl:@"/user/user-schedule/diet"
                             andParm:@{@"date":self.selectDate?:@""}
            completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        @strongify(self);
        id data = [request.responseObject valueForKeyPath:@"data"];
        NSLog(@"requestDietData %@" , data);
        self.dietData = [DailyDietModel modelWithJSON:data];
        completion();
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        self.dietData = [[DailyDietModel alloc] init];
        completion();
    }];
}

// 计划概览
- (void)requestPlanOverViewData:(void(^)(void))completion {
    @weakify(self);
    [MRKAIPlanAPIClient aiplanOverviewSuccess:^(id data) {
        @strongify(self);
        self.planOverViewModel = (MRKPlanOverViewModel *)data;
        completion();
    } failure:^(id data) {
        completion();
    }];
}

///当日运动
- (void)requestDailyScheduleData:(void(^)(void))completion {
    NSLog(@"self.selectDate ===== %@", self.selectDate);
    @weakify(self);
    [MRKBaseRequest mrkGetRequestUrl:@"/user/user-schedule/v2"
                             andParm:@{@"date":self.selectDate?:@""}
            completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        @strongify(self);
        NSLog(@"requestDailyScheduleData ==== %@",request.responseObject);
        id data = [request.responseObject valueForKeyPath:@"data"];
        NSArray *array = [NSArray modelArrayWithClass:[MRKDailyScheduleModel class] json:data];
        self.dailyScheduleList = [NSMutableArray arrayWithArray:array];
        completion();
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        completion();
    }];
}

/////当日ai计划
//- (void)requestCourseAIPlanInfoData:(void(^)(void))completion {
//    [MRKBaseRequest mrkGetRequestUrl:@"/course/training-plan/effective-plan"
//                             andParm:@{@"date":self.date?:@""}
//            completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
//        NSLog(@"requestCoursePlanInfoData ==== %@",request.responseObject);
//        id data = [request.responseObject valueForKeyPath:@"data"];
//        self.aiPlanInfo = [MRKCourseTrainingCurrentAIPlanModel modelWithJSON:data];
//        completion();
//    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
//        completion();
//    }];
//}

/////当日计划
//- (void)requestCoursePlanInfoData:(void(^)(void))completion {
//    [MRKBaseRequest mrkGetRequestUrl:@"/course/coursePlanUserAssociatedController/coursePlanInfo"
//                             andParm:@{@"date":self.date?:@""}
//            completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
//        NSLog(@"requestCoursePlanInfoData ==== %@",request.responseObject);
//        id data = [request.responseObject valueForKeyPath:@"data"];
//        NSArray *array = [NSArray modelArrayWithClass:[MRKPlanInfoModel class] json:data];
//        
//        BOOL unlock = (self.selectCalendarModel.timeReference.intValue != 3);
//        ///拆分数据[数据添加是否解锁]
//        for (MRKPlanInfoModel *model in array) {
//            model.unlock = unlock;
//            model.timeReference = self.selectCalendarModel.timeReference;
//            for (MRKCourseModel *mod in model.courseInfoList) {
//                mod.unlock = unlock;
//            }
//        }
//        self.planInfoArray = array.mutableCopy;
//        
//        completion();
//    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
//        completion();
//    }];
//}

///banner
- (void)requestDailyScheduleBannerData:(void(^)(void))completion {
    @weakify(self);
    [MRKAdvertManager mrkRequestPositionCode:MRKPlanBottomBannerCode
                    completeBlockWithSuccess:^(MRKAdvertDataModel * _Nullable model) {
        @strongify(self);
        NSLog(@"日程金刚区Banner model == %@" , model);
        self.bannerArray = model.adverts.mutableCopy;
        completion();
    }];
}

/////推荐计划
//- (void)requestTopPlanData:(void(^)(void))completion {
////    if (self.topPlanArray.count > 0) {
////        completion();
////        return;
////    }
//    [MRKBaseRequest mrkGetRequestUrl:@"/course/coursePlanUserAssociatedController/top-plan"
//                             andParm:nil
//            completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
//        NSLog(@"requestCoursePlanInfoData ==== %@",request.responseObject);
//        id data = [request.responseObject valueForKeyPath:@"data"];
//        self.topPlanArray = [NSArray modelArrayWithClass:[MRKCoursePlanModel class] json:data];
//        completion();
//    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
//        completion();
//    }];
//}

@end

