//
//  MRKDailyScheduleController.m
//  Student_IOS
//
//  Created by <PERSON><PERSON> on 2023/7/31.
//

#import "MRKDailyScheduleController.h"
#import "MRKHomeAlertManager.h"

#import "MRKDailyScheduleCalendarView.h"
#import "MRKDailyScheduleAlert.h"
#import "MRKDailyTrainTableView.h"

#import "MRKSchedulePageViewModel.h"
#import "MRKCourseModel.h"

#import "ExerciseReportWebController.h"
#import "MRKActionProgressVC.h"
#import "MRKTrainingProgressVC.h"
#import "MRKCategoryCourseController.h"
#import "MRKCoursePlanController.h"
#import "MRKCategoryCourseController.h"
#import "MRKAdjustTargetController.h"
#import "MRKDailyLogic.h"

@interface MRKDailyScheduleController ()<MRKDailyTrainTableViewDelegate>
@property (nonatomic, strong) MRKDailyTrainTableView *trainTableView;
@property (nonatomic, strong) MRKSchedulePageViewModel *viewModel;
@property (nonatomic, strong) MRKDailyScheduleCalendarView *calendarView;
@property (nonatomic, assign) BOOL viewDidLoaded; ///记录viewDidLoaded是否已加载
@end

@implementation MRKDailyScheduleController

- (MRKSchedulePageViewModel *)viewModel {
    if(!_viewModel) {
        _viewModel = [[MRKSchedulePageViewModel alloc] init];
    }
    return _viewModel;
}

- (MRKDailyScheduleCalendarView *)calendarView{
    if (!_calendarView) {
        _calendarView = [[MRKDailyScheduleCalendarView alloc] init];
        @weakify(self);
        _calendarView.operatorSelectBlock = ^{
            @strongify(self);
            [self toChangeTrainTarget];
        };
        _calendarView.calendarDateSelectBlock = ^(CalendarModel * _Nonnull model) {
            @strongify(self);
            [self selectCalendarModel:model];
        };
        _calendarView.calendarSelectBlock = ^(NSMutableArray * _Nonnull dataArray) {
            @strongify(self);
            [self openCalendarModule:dataArray];
        };
    }
    return _calendarView;
}

- (MRKDailyTrainTableView *)trainTableView{
    if(!_trainTableView){
        _trainTableView = [[MRKDailyTrainTableView alloc] initWithFrame:CGRectZero style:UITableViewStyleGrouped];
    }   
    return _trainTableView;
}

- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    
    ///切换至日程板块时弹出
    [[MRKHomeAlertManager shareManager] showNotificationOpenAlert:MRKDailyScheduleNotificationCode];
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
}

- (void)viewDidDisappear:(BOOL)animated{
    [super viewDidDisappear:animated];
    self.viewDidLoaded = NO;
    self.trainTableView.autoScroll = NO;
    
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    self.trainTableView.autoScroll = YES;
   
    ///检查推荐位页面弹窗弹出状态
    [[MRKHomeAlertManager shareManager] addTarget:self checkAlertCode:MRKPlanPopupCode];
    
    ///刷新当前日历数据
    if (_viewModel && !self.viewDidLoaded) {
        [self refreshAllData];
    }
}

- (void)viewDidLoad {
    self.tracePageId = @"page_plan";
    [super viewDidLoad];
    self.mrkContentView.backgroundColor = [UIColor whiteColor];
    self.viewDidLoaded = YES;
    
    ///计划创建成功后刷新
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(refreshData:) name:@"PlanCreateSuc" object:nil];
    
    ///训练数据列表删除数据后刷新
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(refreshData:) name:@"DeteleRecordUpdate" object:nil];
    
    ///调整成功之后刷新日程数据
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(refreshData:) name:@"kAIPlanAdjustSuccessNotification" object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(refreshData:) name:@"kAIPlanOpenNextPeriodSuccessNotification" object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(createDietRecipeSuccess) name:@"kAIPlanCreateDietRecipeSuccessNotification" object:nil];
    [self addLayoutView];
    
    @weakify(self);
    MrkEmptyView *emptyView = [MrkEmptyView emptyViewWithImage:[UIImage imageNamed:@"icon_nonetwork_holder"]
                                                      titleStr:@""
                                                     detailStr:@"数据异常, 刷新重试"];
    emptyView.fullCoverSuperView = YES;
    emptyView.errorBtnClickBlock = ^{
        @strongify(self);
        [self refreshAllData];
    };
    self.view.pageEmptyView = emptyView;
    
    ///请求数据
    [self.view beginLoading];
    [self configSignal];
    
    ///加载推荐位页面弹窗
    [[MRKHomeAlertManager shareManager] addTarget:self requestAlertData:MRKPlanPopupCode];
}

- (void)createDietRecipeSuccess{
    [self refreshAllData];
    @weakify(self);
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        @strongify(self);
        [[MRKAIPlanLogic shared] jumpToDietRecipePage:self.viewModel.planOverViewModel.currentPlan.planId date:self.viewModel.selectCalendarModel.date];
    });
}

- (void)addLayoutView {
    [self.mrkContentView addSubview:self.calendarView];
    [self.calendarView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.mrkContentView.mas_top);
        make.centerX.mas_equalTo(self.mrkContentView);
        make.size.mas_equalTo(CGSizeMake(kScreenWidth, DHPX(100)+kNavBarHeight));
    }];
    
    ///日程页面
    self.trainTableView.listDelegate = self;
    self.trainTableView.viewModel = self.viewModel;
    [self.mrkContentView addSubview:self.trainTableView];
    [self.trainTableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsMake(kNavBarHeight+DHPX(100), 0, 0, 0));
    }];
    self.trainTableView.hidden = YES;
    self.trainTableView.alpha = 0;
}

- (void)viewDidLayoutSubviews{
    [super viewDidLayoutSubviews];
}

#pragma mark  - 通知重置运动数据 -
- (void)refreshData:(NSNotification *)sender {
    [self refreshAllData];
}

#pragma mark  - 刷新全部数据 -
- (void)refreshAllData{
    @weakify(self);
    [self.viewModel requestCalendarData:^(BOOL success) {
        @strongify(self);
        if (success){
            [self.viewModel refreshDataSource];
        }
    }];
}

#pragma mark  - 请求数据 -
- (void)configSignal{
    @weakify(self);
    [self.viewModel.updateDetailSignal subscribeNext:^(id x) {
        @strongify(self);
        {
            [self.view endLoading];
            [self.trainTableView endRefresh];
            [MBProgressHUD hideHUDForView:self.view];
        }
        self.calendarView.selectModel = self.viewModel.selectCalendarModel;
        self.calendarView.dataArray = self.viewModel.dataArray;
        
        if (self.calendarView.dataArray.count > 0) {
            [self.view hiddenEmptyView];
        }
        
        [self.trainTableView setHiddenAnimated:NO];
        [self.trainTableView endRefresh];
        [self.trainTableView refreshTableView];
    }];
    
    ///请求错误时
    [self.viewModel.updateErrorSignal subscribeNext:^(id x) {
        @strongify(self);
        {
            [self.view endLoading];
            [self.trainTableView endRefresh];
            [MBProgressHUD hideHUDForView:self.view];
        }
        
        if (self.calendarView.dataArray.count == 0) {
            [self.view mrkShowNetworkErrorEmptyView];
        }
    }];
    
    [self.viewModel refreshDataSource];
}

- (BOOL)pageNoData {
    return self.viewModel.dailyScheduleList.count == 0;
}

#pragma mark - 日历点击日期
- (void)selectCalendarModel:(CalendarModel *)model{
    [MBProgressHUD showLodingWithMessage:@"" view:self.view];
    self.viewModel.selectCalendarModel = model;
    [self.viewModel refreshDataSource];
    ///log
    ReportMrkLogParms(2, @"日历模块切换日期", @"page_plan", @"btn_page_plan_module_calendar_switch", nil, 0, @{@"date": model.date});
}

#pragma mark - 点击日历弹窗组件
- (void)openCalendarModule:(NSMutableArray *)array{
    if (array.count == 0) return;

    @weakify(self);
    MRKDailyScheduleAlert *alert= [MRKDailyScheduleAlert actionAlertViewWithAnimationStyle:MRKActionAlertViewTransitionStyleSlideFromBottom];
    [alert updateAlertData:array andSelectModel:self.viewModel.selectCalendarModel];
    alert.calendarBlock = ^(CalendarModel * _Nonnull model) {
        @strongify(self);
        [self selectCalendarModel:model];
        
        ///log
        ReportMrkLogParms(2, @"日历浮层切换日期", @"page_plan", @"btn_page_plan_floating_calendar_choose", nil, 0, nil);
    };
    [alert show];

    ///log
    ReportMrkLogParms(2, @"点击打开日历浮层", @"page_plan", @"btn_page_plan_calendar", nil, 0, nil);
}

#pragma mark - 刷新数据
- (void)refreshTableData:(id)data{
    [self.viewModel refreshDataSource];
}

#pragma mark - 课程详情
- (void)toCourseDetailController:(MRKDailyScheduleModel *)data{
    [[RouteManager sharedInstance] jumpToCourseDetailWithId:data.targetId];
}

#pragma mark - 训练报告
- (void)toExerciseReportController:(MRKDailyScheduleItemModel *)model{
    ExerciseReportWebController *vc = [[ExerciseReportWebController alloc] init];
    vc.courseType = model.type;
    vc.exerciseID = model.trainId;
    vc.fromTrainingView = NO;
    vc.courseName = model.title;
    vc.courseImage = model.cover;
    vc.equipmentId = model.equipmentId;
    vc.hidesBottomBarWhenPushed = YES;
    [self.navigationController pushViewController:vc animated:YES];
}

#pragma mark - 运动数据——改为跳到数据中心 8.15
//- (void)toTrainDataController:(id)data{
//    ///数据中心
//    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
//        [[NSNotificationCenter defaultCenter] postNotificationName:@"SetTabBarItemIndex" object:@2];
//    });
//    
//    
//    ///log
//    ReportMrkLogParms(2, @"查看运动数据总览", @"page_plan", @"btn_page_plan_dataoverview", nil, 0, nil);
//}

- (void)toChangeTrainTarget {
    MRKAdjustTargetController *vc = [[MRKAdjustTargetController alloc] init];
    vc.hidesBottomBarWhenPushed = YES;
    [self.navigationController pushViewController:vc animated:YES];
}

#pragma mark - banner点击
- (void)didClickBannerAdvert:(AdvertModel *)data{
    [[NSNotificationCenter defaultCenter] postNotificationName:AdvertSkipToPageNotification object:@{ @"model":data , @"position":@(BannerPage)}];
}

//#pragma mark - 计划管理
//- (void)manageUserPlan:(MRKPlanInfoModel *)model{
//    if (model.type == 2) {
//        //活动进度
//        MRKActionProgressVC *vc = [[MRKActionProgressVC alloc] init];
//        vc.model = model;
//        vc.hidesBottomBarWhenPushed = YES;
//        [self.navigationController pushViewController:vc animated:YES];
//    } else {
//        //训练进度
//        MRKTrainingProgressVC *vc = [[MRKTrainingProgressVC alloc] init];
//        vc.model = model;
//        vc.hidesBottomBarWhenPushed = YES;
//        [self.navigationController pushViewController:vc animated:YES];
//    }
//}

/**
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/

@end


