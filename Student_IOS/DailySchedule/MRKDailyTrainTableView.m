//
//  MRKDailyTrainTableView.m
//  Student_IOS
//
//  Created by <PERSON><PERSON> on 2023/12/8.
//

#import "MRKDailyTrainTableView.h"
#import "MJDIYHeader.h"
#import "UITableView+Additions.h"
#import "MRKDailyDietTableViewCell.h"
#import "MRKDailyLogic.h"
#import "MRKCourseModel.h"
#import "MRKScheduleSrctionHeaderView.h"
#import "MRKScheduleNoneFooterView.h"
#import "MRKDailyAIPlanCell.h"
#import "MRKDailyScheduleOverView.h"
#import "MRKDailyAIPlanEditCell.h"
#import "MRKDailyTaskCell.h"


@interface MRKDailyTrainTableView()<UITableViewDelegate, UITableViewDataSource>
@property (nonatomic, strong) MRKDailyScheduleOverView *headerView;
@property (nonatomic, strong) MRKScheduleFooterView *footerView;
@property (nonatomic, assign) BOOL showDietManagerView; ///是否显示饮食管理模块
@property (nonatomic, assign) BOOL showAIPlanStepTipView; ///是否显示节点提示
@end

@implementation MRKDailyTrainTableView

- (instancetype)initWithFrame:(CGRect)frame style:(UITableViewStyle)style {
    if (self = [super initWithFrame:frame style:style]) {
        self.backgroundColor = [UIColor clearColor];
        self.rowHeight = UITableViewAutomaticDimension;
        self.showsVerticalScrollIndicator = NO;
        self.delegate = self;
        self.dataSource = self;
        self.separatorStyle = UITableViewCellSeparatorStyleNone;
        self.contentInset = UIEdgeInsetsMake(0, 0, 0, 0);
        [self registerClass:[MRKDailyDietTableViewCell class] forCellReuseIdentifier:@"MRKDailyDietTableViewCell"];
        [self registerClass:[MRKDailyAIPlanEditCell class] forCellReuseIdentifier:@"MRKDailyAIPlanEditCell"];
        [self registerClass:[MRKDailyTaskCell class] forCellReuseIdentifier:@"MRKDailyTaskCell"];
        
        @weakify(self);
        MJDIYHeader *header = [MJDIYHeader headerWithRefreshingBlock:^{
            [self_weak_ refreshDataSource];
        }];
        self.mj_header = header;
        self.mj_header.automaticallyChangeAlpha = YES;
    }
    return self;
}

- (void)refreshDataSource{
    if (self.listDelegate && [self.listDelegate respondsToSelector:@selector(refreshTableData:)]){
        [self.listDelegate refreshTableData:self];
    }
}

- (void)refreshTableView{
    ///未来不展示tableHeaderView
    if (self.viewModel.selectCalendarModel.timeReference.intValue == 3) {
        self.tableHeaderView = UIView.new;
    }else{
        //触发布局计算
        [self.headerView setNeedsLayout];
        [self.headerView layoutIfNeeded];
        
        CGSize headerSize = [self.headerView systemLayoutSizeFittingSize:UILayoutFittingCompressedSize];
        self.headerView.frame = CGRectMake(0, 0, kScreenWidth, headerSize.height);
        [self.headerView updateWithModel:self.viewModel.targetData current:self.viewModel.selectCalendarModel];
        self.tableHeaderView = self.headerView;
    }
    
    ///底部banner
    if (self.viewModel.bannerArray.count > 0) {
        [self.footerView.bannerView setDiscoverData:self.viewModel.bannerArray];
        self.tableFooterView = self.footerView;
    }else{
        self.tableFooterView = UIView.new;
    }
    
    [self relaodDietManagerStatus];
    [self reloadAIPlanStepTip];
    
    [self reloadData];
}

/// 判断是否显示饮食管理模块
- (void)relaodDietManagerStatus {
    ///< 时间参考：1-过去 2-今日 3-未来
    NSInteger timeReference = self.viewModel.selectCalendarModel.timeReference.intValue;
    switch (timeReference) {
        case 1: case 2: {
            __block BOOL hasDrinkTask = NO;
            ///判断ai计划任务中是否包含饮水任务
            [self.viewModel.dailyScheduleList enumerateObjectsUsingBlock:^(MRKDailyScheduleModel * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
                if (obj.targetType.integerValue == 3 && obj.aiTrainingDay.tasks.count > 0) {
                    for (MRKDailyScheduleItemModel *model in obj.aiTrainingDay.tasks) {
                        if (model.taskType == 101) {
                            hasDrinkTask = YES;
                            *stop = YES;
                            break;
                        }
                    }
                }
            }];
            self.showDietManagerView = !hasDrinkTask;
        }   break;
        case 3:
            self.showDietManagerView = NO;
            break;
        default:
            break;
    }
}

/// 判断是否显示AI计划节点View
- (void)reloadAIPlanStepTip{
    ///< 时间参考：1-过去 2-今日 3-未来
    NSInteger timeReference = self.viewModel.selectCalendarModel.timeReference.intValue;
    switch (timeReference) {
        case 1: case 3:
            self.showAIPlanStepTipView = NO;
            break;
        case 2: {
            MRKCurrentAIPlanModel *currentPlan = self.viewModel.planOverViewModel.currentPlan;
            if (currentPlan.status == 2 && currentPlan.currentPeriod.status == 2) {
                self.showAIPlanStepTipView = YES;
            }else{
                self.showAIPlanStepTipView = NO;
            }
        }   break;
            
        default:
            break;
    }
}








#pragma mark  - UITableViewDelegate && UITableViewDataSource -

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return 2;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    if (section == 0) {
        return self.showDietManagerView ? 1 : 0;
    } else if (section == 1) {
        return self.viewModel.dailyScheduleList.count + (self.showAIPlanStepTipView ? 1 : 0);
    }
    return 0;
}


- (CGFloat)tableView:(UITableView *)tableView estimatedHeightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return UITableViewAutomaticDimension;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (indexPath.section == 0) {
        return DHPX(205);
    }
    
    return UITableViewAutomaticDimension;
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    if (section == 0) {
        if (self.viewModel.selectCalendarModel.timeReference.intValue == 3 || !self.showDietManagerView){
            return 0.1;
        }
    }
    
    return DHPX(58);
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    if (section == 0){
        if (self.viewModel.selectCalendarModel.timeReference.intValue == 3 || !self.showDietManagerView){
            return UIView.new;
        }
        
        MRKScheduleSrctionHeaderView *view = [[MRKScheduleSrctionHeaderView alloc] init];
        view.moreTitle = @"";
        view.sectionTitle = @"饮食管理";
        view.moreTitle = @"饮食记录";
        view.moreHandleActionBlock = ^{
            ReportMrkLogParms(2, @"点击 右上角“饮食记录”", @"page_plan", @"btn_diet_record", nil, 0, @{});
            [[MRKDailyLogic shared] jumpToDietRecord:self.viewModel.selectCalendarModel.date index:0];
        };
        return view;
    }
    
    if (section == 1){
        MRKScheduleSrctionHeaderView *view = [[MRKScheduleSrctionHeaderView alloc] init];
        view.moreTitle = @"";
        view.sectionTitle = (self.viewModel.selectCalendarModel.timeReference.intValue == 2) ? @"今日任务" : @"当日任务";
        return view;
    }
    return UIView.new;
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    if (section == 0){
        return 0.1;
    }
    
    if (section == 1){
        return self.viewModel.dailyScheduleList.count > 0 ? DHPX(15) : DHPX(270);
    }
    
    return 0.1;
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    if (section == 0) {
        return UIView.new;
    }
    
    if (section == 1 && self.viewModel.dailyScheduleList.count == 0){
        MRKScheduleNoneFooterView *view = [[MRKScheduleNoneFooterView alloc] init];
        return view;
    }
    
    return nil;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (indexPath.section == 0) {
        MRKDailyDietTableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:@"MRKDailyDietTableViewCell"];
        if (!cell) {
            cell = [[MRKDailyDietTableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"MRKDailyDietTableViewCell"];
        }
        cell.selectionStyle = UITableViewCellSelectionStyleNone;
        [cell updateWithDietModel:self.viewModel.dietData current:self.viewModel.selectCalendarModel];
        return cell;
    }
    
    if (indexPath.section == 1) {
        
        ///aiPlan节点cell
        if (self.showAIPlanStepTipView && indexPath.row == 0) {
            MRKDailyAIPlanEditCell *cell = [tableView dequeueReusableCellWithIdentifier:@"MRKDailyAIPlanEditCell"];
            if (!cell) {
                cell = [[MRKDailyAIPlanEditCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"MRKDailyAIPlanEditCell"];
            }
            cell.selectionStyle = UITableViewCellSelectionStyleNone;
            cell.model = self.viewModel.planOverViewModel;
            return cell;
        }
        
        
        NSInteger index = self.showAIPlanStepTipView ? indexPath.row -1 : indexPath.row;
        MRKDailyScheduleModel *model = [self.viewModel.dailyScheduleList objectAtIndex:index];
        /// 日程内容类型：1-课程，2-动作，3-AI训练日
        if (model.targetType.intValue == 3){ // AI训练日
            MRKDailyAIPlanCell *cell = [tableView dequeueReusableCellWithIdentifier:@"MRKDailyAIPlanCell"];
            if (!cell) {
                cell = [[MRKDailyAIPlanCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"MRKDailyAIPlanCell"];
            }
            cell.selectionStyle = UITableViewCellSelectionStyleNone;
            [cell setItem:model dietModel:self.viewModel.dietData calendar:self.viewModel.selectCalendarModel overview:self.viewModel.planOverViewModel
                   target:self.viewModel.targetData];
            cell.updateHeightBlock = ^(CGFloat height) {
                [tableView beginUpdates];
                [tableView endUpdates];
            };
            
            return cell;
        } else {
            // 老计划课程 // 动作 //直播
            MRKDailyTaskCell *cell = [tableView dequeueReusableCellWithIdentifier:@"MRKDailyTaskCell"];
            if (!cell) {
                cell = [[MRKDailyTaskCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"MRKDailyTaskCell"];
            }
            cell.selectionStyle = UITableViewCellSelectionStyleNone;
            [cell setModel:model andTimeReference:self.viewModel.selectCalendarModel.timeReference];
            return cell;
        }
    }
    
    return [UITableViewCell new];
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    
    if (indexPath.section == 1)
    {
        MRKDailyScheduleModel *model = [self.viewModel.dailyScheduleList objectAtIndex:indexPath.row];
        /// 不是AI训练日， 只处理老计划和直播课的跳转
        if (model.targetType.intValue != 3){
            
            MRKDailyScheduleItemModel *item = model.targetType.intValue == 1 ? model.course : model.motion;
            tableView.traceEventId = model.targetType.intValue == 1 ? @"btn_plan_order_class" : @"btn_plan_order_motion_class";
            tableView.tracePara = @{@"target_id": model.targetId};
            ///直播课直接进详情
            if (model.channel.intValue == 1) {
                if (self.listDelegate && [self.listDelegate respondsToSelector:@selector(toCourseDetailController:)]) {
                    [self.listDelegate toCourseDetailController:model];
                }
                ///log
                ReportMrkLogParms(2, @"今日运动中直播点击", @"page_plan", @"btn_page_plan_schedule_live", nil, 0, nil);
                return;
            }
            
            ///未解锁
            if (item.lock) {
                [MBProgressHUD showMessage:@"课程需在训练当日解锁" toView:nil];
                return;
            }
            
            ///已训练 ->  训练报告详情
            if (model.finished && [item.trainId isNotEmpty]) {
                if (self.listDelegate && [self.listDelegate respondsToSelector:@selector(toExerciseReportController:)]) {
                    [self.listDelegate toExerciseReportController:item];
                }
                return;
            }
            
            ///课程详情
            if (self.listDelegate && [self.listDelegate respondsToSelector:@selector(toCourseDetailController:)]) {
                [self.listDelegate toCourseDetailController:model];
            }
            ///log
            ReportMrkLogParms(2, @"今日运动中课程点击", @"page_plan", @"btn_page_plan_schedule_course", nil, 0, nil);
            return;
        } else {
            //            [[MRKAIPlanLogic shared] jumpToAIPlanDetailPageAndJudgeExpire:model.aiTrainingDay.planId];
        }
    }
    
    //    else if (indexPath.section == 2) {
    //        if ((self.viewModel.aiPlanInfo.planId.longLongValue > 0 ? 1 : 0) > 0 && indexPath.row == 0) {
    //            [[MRKAIPlanLogic shared] jumpToAIPlanDetailPageAndJudgeExpire:self.viewModel.aiPlanInfo.planId];
    //        }
    //    }
}

- (void)trainingPlanCourseSelect:(MRKCourseModel *)model andTableView:(UITableView *)tableView {
    tableView.traceEventId = @"btn_plan_attend_class";
    tableView.tracePara = @{@"course_id":model.courseId};
    
    if (model) {
        if(!model.unlock){
            [MBProgressHUD showMessage:@"课程需在训练当日解锁" toView:nil];
            return;
        };
        
        [[RouteManager sharedInstance] jumpToCourseDetailWithModel:model];
    }
}

- (MRKDailyScheduleOverView *)headerView{
    if (!_headerView) {
        _headerView = [[MRKDailyScheduleOverView alloc] init];
        @weakify(self);
        _headerView.updateHeightBlock = ^(CGFloat height) {
            @strongify(self);
            //触发布局计算
            [self.headerView setNeedsLayout];
            [self.headerView layoutIfNeeded];
            
            CGSize headerSize = [self.headerView systemLayoutSizeFittingSize:UILayoutFittingCompressedSize];
            self.headerView.frame = CGRectMake(0, 0, kScreenWidth, headerSize.height);
            self.tableHeaderView = self.headerView;
        };
    }
    return _headerView;
}

- (MRKScheduleFooterView *)footerView{
    if (!_footerView) {
        _footerView = [[MRKScheduleFooterView alloc] init];
        _footerView.frame = CGRectMake(0, 0, RealScreenWidth, DHPX(115));
        @weakify(self);
        _footerView.bannerView.bannerSelectBlock = ^(AdvertModel * _Nonnull model) {
            @strongify(self);
            if (self.listDelegate && [self.listDelegate respondsToSelector:@selector(didClickBannerAdvert:)]){
                [self.listDelegate didClickBannerAdvert:model];
            }
        };
    }
    return _footerView;
}

- (void)setAutoScroll:(BOOL)autoScroll{
    _autoScroll = autoScroll;
    self.footerView.bannerView.autoScroll = autoScroll;
}
/*
 // Only override drawRect: if you perform custom drawing.
 // An empty implementation adversely affects performance during animation.
 - (void)drawRect:(CGRect)rect {
 // Drawing code
 }
 */

@end
