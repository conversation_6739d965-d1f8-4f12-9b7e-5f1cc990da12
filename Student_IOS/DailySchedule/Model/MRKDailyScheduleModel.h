//
//  MRKDailyScheduleModel.h
//  Student_IOS
//
//  Created by Junq on 2023/8/10.
//

#import <Foundation/Foundation.h>
#import "MRKCourseModel.h"

NS_ASSUME_NONNULL_BEGIN

@class MRKDailyScheduleItemModel;
@class MRKDailyScheduleAIPlanDayModel;

/// 日程主模型，包含一天的日程安排（课程、动作、AI训练日等）
@interface MRKDailyScheduleModel : NSObject
@property (nonatomic, copy) NSString *title;          ///< 格式化时间 例：09:00、18:00
@property (nonatomic, copy) NSString *scheduleTime;   ///< 日程时间（原始时间字符串）
@property (nonatomic, copy) NSString *targetType;     ///< 日程内容类型：1-课程，2-动作，3-AI训练日
@property (nonatomic, copy) NSString *targetId;       ///< 课程ID、动作ID、训练日ID
@property (nonatomic, copy) NSString *finishTime;     ///< 完成时间
@property (nonatomic, copy) NSString *channel;        ///< 渠道：1-预约，2-计划
@property (nonatomic, assign) BOOL finished;          ///< 是否已经完成 true已完成 ｜ false未完成
@property (nonatomic, strong) MRKDailyScheduleItemModel *course;             ///< 课程信息（targetType为1时）
@property (nonatomic, strong) MRKDailyScheduleItemModel *motion;             ///< 动作信息（targetType为2时）
@property (nonatomic, strong) MRKDailyScheduleAIPlanDayModel *aiTrainingDay; ///< AI训练日内容（targetType为3时）
@end



/// AI训练日模型，包含AI计划下某一天的详细内容
@interface MRKDailyScheduleAIPlanDayModel : NSObject
@property (nonatomic, copy) NSString *planTitle;   ///< 大标题: 28天极致蜕变月
@property (nonatomic, copy) NSString *planId;      ///< 计划id
@property (nonatomic, copy) NSString *title;       ///< 当日小标题: 当日激活
@property (nonatomic, copy) NSString *seq;         ///< 第几天: 1
@property (nonatomic, copy) NSString *type;        ///< 计划日类型：1-训练，2-休息
@property (nonatomic, copy) NSString *isNeedGenerateRecipe;  ///< 是否需要生成食谱
@property (nonatomic, strong) NSArray<MRKDailyScheduleItemModel *> *tasks; ///< 具体任务列表
@end



/// 日程任务项模型，包含课程、动作等详细信息
@interface MRKDailyScheduleItemModel : NSObject
@property (nonatomic, copy) NSString *cid;         ///< id
@property (nonatomic, copy) NSString *title;       ///< 标题
@property (nonatomic, copy) NSString *cover;       ///< 封面图片URL
@property (nonatomic, copy) NSString *type;        ///< 类型 例：model.type.integerValue == 3 ? @"比赛中" : @"直播中"
@property (nonatomic, assign) MRKTrainingType taskType; ///< 任务内容类型：1课程训练 2自由训练 6实景视频 20绝影之竞-游戏 5顽鹿 9动作 101饮水
@property (nonatomic, copy) NSString *kcal;        ///< 消耗能量（千卡）
@property (nonatomic, assign) BOOL lock;           ///< 是否锁定 true不可看 ｜ false可看
@property (nonatomic, copy) NSString *liveTime;    ///< 课程直播时间
@property (nonatomic, copy) NSString *equipmentId; ///< 设备类型Id
@property (nonatomic, copy) NSString *duration;    ///< 时长
@property (nonatomic, copy) NSString *trainId;     ///< 单次训练ID
@property (nonatomic, copy) NSString *coachName;   ///< 教练名称
@property (nonatomic, copy) NSString *grade;       ///< 课程等级：1-零基础（M1），2-入门（M2），3-进阶（M3），4-强化（M4），5-挑战（M5）
@property (nonatomic, copy) NSString *gradeDesc;   ///< 难度描述
@property (nonatomic, copy) NSString *part;        ///< 锻炼部位(一级部位):背部
@property (nonatomic, copy) NSString *status;      ///< 课程状态
@property (nonatomic, assign) NSInteger taskStatus;  ///< 状态 0-未完成，1-已完成
@property (nonatomic, copy) NSString *targetId;    ///< 课程ID、动作ID、训练日ID
@property (nonatomic, assign) NSInteger trainingTargetType;   ///< 动作目标类型 1数量 2时间
@property (nonatomic, copy) NSString *trainingTargetValue;  ///< 动作目标值
@property (nonatomic, copy) NSString *trainingTargetDuration;///< 完成目标总时长
@end

NS_ASSUME_NONNULL_END
