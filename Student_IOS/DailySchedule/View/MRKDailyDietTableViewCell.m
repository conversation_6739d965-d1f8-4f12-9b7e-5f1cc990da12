//
//  MRKDailyDietTableViewCell.m
//  Student_IOS
//

#import "MRKDailyDietTableViewCell.h"
#import "MrkGradientView.h"
#import "MRKDailyLogic.h"

/// 常量定义
static CGFloat const kSideMargin = 16.0;
static CGFloat const kMealViewHeight = 36.0;
static CGFloat const kMealViewTop = 0.0;
static CGFloat const kMealViewSpacing = 0.0;

@interface MRKDailyDietTableViewCell()

@property (nonatomic, strong) MRKDailyDietMealView *breakfastView;
@property (nonatomic, strong) MRKDailyDietMealView *lunchView;
@property (nonatomic, strong) MRKDailyDietMealView *dinnerView;
@property (nonatomic, strong) MRKDailyDietMealView *snackView;

@property (nonatomic, strong) MrkGradientView *photoView;
@property (nonatomic, strong) MrkGradientView *textView;
@property (nonatomic, strong) MrkGradientView *drinkView;

@property (nonatomic, strong) UILabel *drinkTitleLabel;
@property (nonatomic, strong) UILabel *drinkLabel;

@property (nonatomic, strong) CalendarModel *currentCalendar;

@end

@implementation MRKDailyDietTableViewCell

#pragma mark - 初始化

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        self.height = DHPX(205);
        [self setupSubviews];
    }
    return self;
}

#pragma mark - 数据更新

- (void)updateWithDietModel:(DailyDietModel *)dietModel current:(CalendarModel *)calendar {
    self.currentCalendar = calendar;
    self.breakfastView.kcal = [self kcalStringFrom:dietModel.breakfast.totalCalorie];
    self.lunchView.kcal     = [self kcalStringFrom:dietModel.lunch.totalCalorie];
    self.dinnerView.kcal    = [self kcalStringFrom:dietModel.dinner.totalCalorie];
    self.snackView.kcal     = [self kcalStringFrom:dietModel.snack.totalCalorie];
    
    self.drinkTitleLabel.text = [NSString stringWithFormat:@"%@ml", dietModel.drink.waterIntake ?: @"--"];
    self.drinkLabel.text = [NSString stringWithFormat:@"目标%@", dietModel.drink.targetWaterIntake ?: @"--"];
}

- (NSString *)kcalStringFrom:(NSString *)calorie {
    if ([calorie isNotBlank] && calorie.doubleValue > 0) {
        return [NSString stringWithFormat:@"%.1f千卡", ceil(calorie.doubleValue * 10)/10.0];
    }
    return @"--千卡";
}

#pragma mark - 事件响应

- (void)onDietButtonTapped:(UIButton *)sender {
    sender.traceEventId = @"btn_daily_diet_record";
    [[MRKDailyLogic shared] jumpToDietRecord:self.currentCalendar.date index:0];
}

- (void)onAddDrinkButtonTapped:(UIButton *)sender {
    sender.traceEventId = @"btn_add_200ml_water";
    NSString *date = self.currentCalendar.date;
    if (self.currentCalendar.timeReference.intValue == 2) {
        NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
        formatter.dateFormat = @"HH:mm:ss";
        date = [NSString stringWithFormat:@"%@ %@", date, [formatter stringFromDate:[NSDate date]]];
    } else {
        date = [NSString stringWithFormat:@"%@ 00:00:00", date];
    }
    [[MRKDailyLogic shared] addPerDrinkRecord:date];
}

- (void)onPhotoButtonTapped:(UIButton *)sender {
    sender.traceEventId = @"btn_photo_record";
    [[MRKDailyLogic shared] jumpToImageRecognition:self.currentCalendar.date];
}

- (void)onTextButtonTapped:(UIButton *)sender {
    sender.traceEventId = @"btn_text_record";
    [[MRKDailyLogic shared] jumpToTextRecognition:self.currentCalendar.date];
}

- (void)onDrinkButtonTapped:(UIButton *)sender {
    sender.traceEventId = @"btn_water_module";
    [[MRKDailyLogic shared] jumpToDrinkRecord:self.currentCalendar.date];
}

#pragma mark - UI搭建

- (void)setupSubviews {
    CGFloat width = (RealScreenWidth - DHPX(32)) / 4.0;
    NSArray *mealViews = @[self.breakfastView, self.lunchView, self.dinnerView, self.snackView];
    UIView *lastView = nil;
    for (MRKDailyDietMealView *mealView in mealViews) {
        [self.contentView addSubview:mealView];
        [mealView mas_makeConstraints:^(MASConstraintMaker *make) {
            if (lastView) {
                make.left.equalTo(lastView.mas_right);
            } else {
                make.left.mas_equalTo(DHPX(kSideMargin));
            }
            make.top.equalTo(self.contentView).offset(DHPX(kMealViewTop));
            make.size.mas_equalTo(CGSizeMake(width, DHPX(kMealViewHeight)));
        }];
        lastView = mealView;
    }
    
    
    UIButton *dietBtn = [[UIButton alloc] init];
    [dietBtn addTarget:self action:@selector(onDietButtonTapped:) forControlEvents:UIControlEventTouchUpInside];
    [self.contentView addSubview:dietBtn];
    [dietBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.bottom.left.equalTo(self.breakfastView);
        make.right.equalTo(self.snackView);
    }];
    
    // 下方功能区
    CGFloat bottomWidth = (RealScreenWidth - DHPX(16)*2 -DHPX(10)) / 2.0;
    {
        [self.contentView addSubview:self.photoView];
        self.photoView.cornerRadius = DHPX(8);
        [self.photoView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.mas_equalTo(DHPX(16));
            make.top.equalTo(self.breakfastView.mas_bottom).offset(DHPX(16));
            make.size.mas_equalTo(CGSizeMake(bottomWidth, DHPX(138)));
        }];
        UIImageView *bgImageView = [[UIImageView alloc] init];
        bgImageView.image = [UIImage imageNamed:@"daily_diet_photo_bg"];
        [self.photoView addSubview:bgImageView];
        [bgImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.photoView).offset(-DHPX(43));
            make.bottom.equalTo(self.photoView).offset(DHPX(43));
            make.size.mas_equalTo(CGSizeMake(DHPX(97), DHPX(99)));
        }];
        UIImageView *imageView = [[UIImageView alloc] init];
        imageView.image = [UIImage imageNamed:@"daily_diet_photo"];
        [self.photoView addSubview:imageView];
        [imageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(self.photoView).offset(-DHPX(10));
            make.bottom.equalTo(self.photoView).offset(-DHPX(10));
            make.size.mas_equalTo(CGSizeMake(DHPX(77), DHPX(67)));
        }];
        UILabel *titleLabel = [[UILabel alloc] init];
        titleLabel.textColor = [UIColor colorWithHexString:@"#475D8F"];
        titleLabel.font = kMedium_Font_NoDHPX(WKDHPX(20));
        titleLabel.text = @"AI拍照识卡";
        [self.photoView addSubview:titleLabel];
        [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.photoView).offset(DHPX(15));
            make.top.equalTo(self.photoView).offset(DHPX(26));
        }];
        UIButton *photoBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [photoBtn addTarget:self action:@selector(onPhotoButtonTapped:) forControlEvents:UIControlEventTouchUpInside];
        [self.photoView addSubview:photoBtn];
        [photoBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.equalTo(self.photoView);
        }];
    }
   
    
    CGFloat bottomHeight = (DHPX(138) -DHPX(8)) / 2.0;
    {
        [self.contentView addSubview:self.textView];
        self.textView.cornerRadius = DHPX(8);
        [self.textView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.photoView.mas_right).offset(DHPX(10));
            make.top.equalTo(self.photoView);
            make.size.mas_equalTo(CGSizeMake(bottomWidth, bottomHeight));
        }];
        UIImageView *bgImageViewText = [[UIImageView alloc] init];
        bgImageViewText.image = [UIImage imageNamed:@"daily_diet_text_bg"];
        [self.textView addSubview:bgImageViewText];
        [bgImageViewText mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.textView).offset(-DHPX(32));
            make.right.equalTo(self.textView).offset(DHPX(25));
            make.size.mas_equalTo(CGSizeMake(DHPX(70), DHPX(70)));
        }];
        UIImageView *imageViewText = [[UIImageView alloc] init];
        imageViewText.image = [UIImage imageNamed:@"daily_diet_text"];
        [self.textView addSubview:imageViewText];
        [imageViewText mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(self.textView);
            make.right.equalTo(self.textView);
            make.size.mas_equalTo(CGSizeMake(DHPX(44), DHPX(41)));
        }];
        UILabel *titleLabelText = [[UILabel alloc] init];
        titleLabelText.textColor = [UIColor colorWithHexString:@"#475D8F"];
        titleLabelText.font = kMedium_Font(13);
        titleLabelText.text = @"AI语音记录";
        [self.textView addSubview:titleLabelText];
        [titleLabelText mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.textView).offset(DHPX(12));
            make.top.equalTo(self.textView).offset(DHPX(18));
            make.height.mas_equalTo(DHPX(13));
        }];
        UILabel *textLabel = [[UILabel alloc] init];
        textLabel.textColor = [UIColor colorWithHexString:@"#848A9B"];
        textLabel.font = kSystem_Font(11);
        textLabel.text = @"语音/文字记饮食";
        [self.textView addSubview:textLabel];
        [textLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(titleLabelText);
            make.top.equalTo(titleLabelText.mas_bottom).offset(DHPX(5));
            make.right.equalTo(imageViewText.mas_left);
        }];
        UIButton *textBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [textBtn addTarget:self action:@selector(onTextButtonTapped:) forControlEvents:UIControlEventTouchUpInside];
        [self.textView addSubview:textBtn];
        [textBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.equalTo(self.textView);
        }];
    }
    
    
    {
        self.drinkView.cornerRadius = DHPX(8);
        [self.contentView addSubview:self.drinkView];
        [self.drinkView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.textView);
            make.bottom.equalTo(self.photoView);
            make.size.mas_equalTo(CGSizeMake(bottomWidth, bottomHeight));
        }];
        UIButton *drinkBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [drinkBtn addTarget:self action:@selector(onDrinkButtonTapped:) forControlEvents:UIControlEventTouchUpInside];
        [self.drinkView addSubview:drinkBtn];
        [drinkBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.equalTo(self.drinkView);
        }];
        UIButton *addBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [addBtn setImage:[UIImage imageNamed:@"daily_drink_add"] forState:UIControlStateNormal];
        [addBtn addTarget:self action:@selector(onAddDrinkButtonTapped:) forControlEvents:UIControlEventTouchUpInside];
        [self.drinkView addSubview:addBtn];
        [addBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(self.drinkView);
            make.right.equalTo(self.drinkView).offset(-DHPX(10));
            make.size.mas_equalTo(CGSizeMake(DHPX(77), DHPX(33)));
        }];
        UILabel *titleLabelDrink = [[UILabel alloc] init];
        titleLabelDrink.textColor = [UIColor colorWithHexString:@"#475D8F"];
        titleLabelDrink.font = kMedium_Font(13);
        titleLabelDrink.text = @"喝水";
        [self.drinkView addSubview:titleLabelDrink];
        [titleLabelDrink mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.drinkView).offset(DHPX(12));
            make.top.equalTo(self.drinkView).offset(DHPX(11));
            make.height.mas_equalTo(DHPX(13));
        }];
        self.drinkTitleLabel = [[UILabel alloc] init];
        self.drinkTitleLabel.textColor = [UIColor colorWithHexString:@"#475D8F"];
        self.drinkTitleLabel.font = kMedium_Font(13);
        self.drinkTitleLabel.text = @"";
        [self.drinkView addSubview:self.drinkTitleLabel];
        [self.drinkTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.drinkView).offset(DHPX(12));
            make.top.equalTo(titleLabelDrink.mas_bottom).offset(DHPX(2));
            make.height.mas_equalTo(DHPX(13));
        }];
        self.drinkLabel = [[UILabel alloc] init];
        self.drinkLabel.textColor = [UIColor colorWithHexString:@"#848A9B"];
        self.drinkLabel.font = kSystem_Font(12);
        self.drinkLabel.text = @"";
        [self.drinkView addSubview:self.drinkLabel];
        [self.drinkLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.drinkTitleLabel);
            make.top.equalTo(self.drinkTitleLabel.mas_bottom).offset(DHPX(4));
            make.right.equalTo(addBtn.mas_left);
        }];
    }
}

#pragma mark - 懒加载

- (MRKDailyDietMealView *)breakfastView {
    if (!_breakfastView) {
        _breakfastView = [[MRKDailyDietMealView alloc] init];
        _breakfastView.title = @"早餐";
    }
    return _breakfastView;
}
- (MRKDailyDietMealView *)lunchView {
    if (!_lunchView) {
        _lunchView = [[MRKDailyDietMealView alloc] init];
        _lunchView.title = @"午餐";
    }
    return _lunchView;
}
- (MRKDailyDietMealView *)dinnerView {
    if (!_dinnerView) {
        _dinnerView = [[MRKDailyDietMealView alloc] init];
        _dinnerView.title = @"晚餐";
    }
    return _dinnerView;
}
- (MRKDailyDietMealView *)snackView {
    if (!_snackView) {
        _snackView = [[MRKDailyDietMealView alloc] init];
        _snackView.title = @"加餐";
    }
    return _snackView;
}
- (MrkGradientView *)photoView {
    if (!_photoView) {
        _photoView = [[MrkGradientView alloc] init];
        _photoView.drawsGradientBackground = YES;
        _photoView.gradientStartPoint = CGPointMake(0, 0.5);
        _photoView.gradientEndPoint = CGPointMake(1, 0.5);
        _photoView.gradientLocations = @[@0, @1];
        _photoView.gradientLayerColors = @[(__bridge id)[[UIColor colorWithHexString:@"#E5FDFF"] CGColor],
                                           (__bridge id)[[UIColor colorWithHexString:@"#D3FBFF"] CGColor]];
    }
    return _photoView;
}
- (MrkGradientView *)textView {
    if (!_textView) {
        _textView = [[MrkGradientView alloc] init];
        _textView.drawsGradientBackground = YES;
        _textView.gradientStartPoint = CGPointMake(0, 0.5);
        _textView.gradientEndPoint = CGPointMake(1, 0.5);
        _textView.gradientLocations = @[@0, @1];
        _textView.gradientLayerColors = @[(__bridge id)[[UIColor colorWithHexString:@"#EEEFFC"] CGColor],
                                           (__bridge id)[[UIColor colorWithHexString:@"#EEEDFF"] CGColor]];
    }
    return _textView;
}
- (MrkGradientView *)drinkView {
    if (!_drinkView) {
        _drinkView = [[MrkGradientView alloc] init];
        _drinkView.drawsGradientBackground = YES;
        _drinkView.gradientStartPoint = CGPointMake(0, 0.5);
        _drinkView.gradientEndPoint = CGPointMake(1, 0.5);
        _drinkView.gradientLocations = @[@0, @1];
        _drinkView.gradientLayerColors = @[(__bridge id)[[UIColor colorWithHexString:@"#DEEDFE"] CGColor],
                                           (__bridge id)[[UIColor colorWithHexString:@"#DDEDFD"] CGColor]];
    }
    return _drinkView;
}

@end

#pragma mark - MRKDailyDietMealView

@interface MRKDailyDietMealView()
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *kcalLabel;
@end

@implementation MRKDailyDietMealView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupSubviews];
    }
    return self;
}

- (void)setTitle:(NSString *)title {
    self.titleLabel.text = title;
}

- (void)setKcal:(NSString *)kcal {
    self.kcalLabel.text = kcal;
}

- (void)setupSubviews {
    [self addSubview:self.titleLabel];
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self);
        make.top.equalTo(self);
        make.height.mas_equalTo(DHPX(20));
    }];
    [self addSubview:self.kcalLabel];
    [self.kcalLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self);
        make.top.equalTo(self.titleLabel.mas_bottom);
        make.height.mas_equalTo(DHPX(16));
    }];
}

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.textColor = [UIColor colorWithHexString:@"#05182C"];
        _titleLabel.font = kSystem_Font_NoDHPX(15);
        _titleLabel.textAlignment = NSTextAlignmentCenter;
    }
    return _titleLabel;
}

- (UILabel *)kcalLabel {
    if (!_kcalLabel) {
        _kcalLabel = [[UILabel alloc] init];
        _kcalLabel.textColor = [UIColor colorWithHexString:@"#848A9B"];
        _kcalLabel.font = kSystem_Font_NoDHPX(12);
        _kcalLabel.textAlignment = NSTextAlignmentCenter;
    }
    return _kcalLabel;
}

@end
