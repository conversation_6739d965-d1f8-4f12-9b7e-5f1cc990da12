//
//  MRKDailyTaskCell.m
//  Student_IOS
//
//  Created by Junq on 2025/6/25.
//

#import "MRKDailyTaskCell.h"
#import "NSString+StringSize.h"
#import "MRKCourseTagStatusView.h"
#import "UIButton+Event.h" ///按钮防重复点击


@interface MRKDailyTaskCell ()
@property (nonatomic, strong) UIView *contentItemView;
@property (nonatomic, strong) UIView *pointView;
@property (nonatomic, strong) UILabel *titleLabel;
///
@property (nonatomic, strong) UIImageView *statusImageView;
@property (nonatomic, strong) UIButton *exchangeBtn;
@property (nonatomic, strong) MRKCourseTagStatusView *statusView;

@property (nonatomic, strong) UIView *lineView;
@property (nonatomic, strong) UIView *itemView;
@property (nonatomic, strong) MRKDailyAIPlanItemView *courseItemView;
@property (nonatomic, strong) MRKDailyFoodItemView *foodItemView;

///
@property (nonatomic, strong) MRKDailyScheduleItemModel *targetItem;
@end

@implementation MRKDailyTaskCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier{
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        self.backgroundColor = UIColor.clearColor;
        self.contentView.backgroundColor = UIColor.clearColor;
        
        [self.contentView addSubview:self.contentItemView];
        [self.contentItemView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.contentView.mas_top).offset(DHPX(8));
            make.left.equalTo(self.contentView.mas_left).offset(DHPX(16));
            make.right.equalTo(self.contentView.mas_right).offset(-DHPX(16));
            make.bottom.equalTo(self.contentView.mas_bottom).offset(-DHPX(4));
        }];
        
        [self.contentItemView addSubview:self.pointView];
        [self.contentItemView addSubview:self.titleLabel];
        [self.contentItemView addSubview:self.lineView];
        [self.pointView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.contentItemView.mas_top).offset(DHPX(18));
            make.left.equalTo(self.contentItemView.mas_left).offset(DHPX(10));
            make.size.mas_equalTo(CGSizeMake(DHPX(4), DHPX(4)));
        }];
        
        [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(self.pointView.mas_centerY);
            make.left.equalTo(self.pointView.mas_right).offset(DHPX(8));
            make.right.equalTo(self.contentItemView.mas_right).offset(-DHPX(70));
        }];
        
        [self.lineView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.contentItemView.mas_top).offset(DHPX(42));
            make.left.equalTo(self.contentItemView.mas_left).offset(DHPX(12));
            make.right.equalTo(self.contentItemView.mas_right).offset(-DHPX(12));
            make.height.mas_equalTo(CGFloatFromPixel(1.0));
        }];
        
        {
            self.statusImageView.hidden = YES;
            self.exchangeBtn.hidden = YES;
            self.statusView.hidden = YES;
            [self.contentItemView addSubview:self.statusImageView];
            [self.contentItemView addSubview:self.exchangeBtn];
            [self.contentItemView addSubview:self.statusView];
            SJCornerMaskSetRectCorner(self.statusView, UIRectCornerBottomLeft, 8);
            
            [self.statusImageView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerY.equalTo(self.pointView.mas_centerY);
                make.right.equalTo(self.contentItemView.mas_right).offset(-DHPX(10));
                make.size.mas_equalTo(CGSizeMake(DHPX(20),DHPX(20)));
            }];
            [self.exchangeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerY.equalTo(self.pointView.mas_centerY);
                make.right.equalTo(self.contentItemView.mas_right).offset(-DHPX(6));
                make.height.mas_equalTo(DHPX(22));
            }];
            [self.statusView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(self.contentItemView.mas_top).offset(0);
                make.right.equalTo(self.contentItemView.mas_right);
                make.height.mas_equalTo(DHPX(22));
            }];
        }
        
        ///内容容器
        [self.contentItemView addSubview:self.itemView];
        [self.itemView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.contentItemView.mas_top).offset(DHPX(54));
            make.left.equalTo(self.contentItemView.mas_left).offset(DHPX(12));
            make.right.equalTo(self.contentItemView.mas_right).offset(-DHPX(12));
            make.bottom.equalTo(self.contentItemView.mas_bottom).offset(-DHPX(12));
            make.height.mas_greaterThanOrEqualTo(DHPX(68));
        }];
    }
    return self;
}

- (void)layoutSubviews {
    [super layoutSubviews];
}

- (void)showCourseItem {
    [self.itemView.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
    [self.itemView addSubview:self.courseItemView];
    [self.courseItemView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.itemView);
    }];
    self.courseItemView.hidden = NO;
    self.foodItemView.hidden = YES;
}

- (void)showFoodItem {
    [self.itemView.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
    [self.itemView addSubview:self.foodItemView];
    [self.foodItemView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.itemView);
    }];
    self.foodItemView.hidden = NO;
    self.courseItemView.hidden = YES;
}


/// 老计划课程/ 预约的直播课
- (void)setModel:(MRKDailyScheduleModel *)model andTimeReference:(NSString *)timeReference{
    {
        self.statusImageView.hidden = YES;
        self.exchangeBtn.hidden = YES;
        self.statusView.hidden = NO;
    }
    
    [self showCourseItem];
    
    MRKDailyScheduleItemModel *courseModel = nil;
    NSInteger targetType = model.targetType.integerValue;
    switch (targetType) {
        case 1: //课程
        {
            model.course.taskType = [model.targetType integerValue]; ///老计划里model.course.taskType无值
            courseModel = model.course;
            [self.courseItemView setModel:model.course andTimeReference:timeReference];
        } break;
        case 2: //动作
        {
            courseModel = model.motion;
            [self.courseItemView setModel:model.motion andTimeReference:timeReference];
        } break;
        case 3:
            break;
        default:
            break;
    }
    
    ///日程渠道：1-直播课，2-计划
    NSInteger channel = model.channel.integerValue;
    
    self.statusView.hidden = YES;
    if (channel == 1) { ///日程渠道: 1-直播
        self.titleLabel.text = [NSString stringWithFormat:@"直播课 %@", model.title];
        self.pointView.backgroundColor = UIColorHex(#9ADEE2);
        /// 1(过去)  2(今日)  3(未来)
        switch (timeReference.intValue) {
            case 1:
            {
                [self courseTag:@"已结束" image:nil];
            } break;
            case 2:
            {
                NSInteger status = [courseModel.status integerValue];
                switch (status) {
                    case 30:{
                        [self courseTag:@"已预约" image:[UIImage imageNamed:@"icon_trained"]];
                    } break;
                    case 35: case 40:{
                        NSString *tag = courseModel.type.integerValue == 3 ? @"比赛中" : @"直播中";
                        [self courseCellLiving:tag];
                    } break;
                    default:{
                        [self courseTag:@"已结束" image:nil];
                    } break;
                }
            } break;
            case 3:
            {
                [self courseTag:@"已预约" image:[UIImage imageNamed:@"icon_trained"]];
            } break;
            default: break;
        }
    } else if (channel == 2) {  ///日程渠道: 2-计划
        self.titleLabel.text = [NSString stringWithFormat:@"经典计划 %@", model.title];
        self.pointView.backgroundColor = UIColorHex(#B7C2E2);
        ///
        NSString *tagIconStr = @"";
        UIImage *tagIconImage = nil;
        if (courseModel.lock) {
            tagIconStr = @"当日解锁";
            tagIconImage = [UIImage imageNamed:@"icon_lock"];
        } else {
            tagIconStr = model.finished ? @"已训练":@"未训练";
            tagIconImage = model.finished ? [UIImage imageNamed:@"icon_trained"]:nil;
        }
        [self courseTag:tagIconStr image:tagIconImage];
    }
}

- (NSString *)numberToChinese:(int)number{
    NSNumberFormatter  *formatter = [[NSNumberFormatter alloc] init];
    NSLocale *locale = [[NSLocale alloc] initWithLocaleIdentifier:@"zh_Hans"];
    formatter.locale = locale;
    formatter.numberStyle = NSNumberFormatterSpellOutStyle;
    NSString *ch_string = [formatter stringFromNumber:@(number)];
    return ch_string;
}

///AI计划子item
///
- (void)setItemModel:(MRKDailyScheduleItemModel *)model
           dietModel:(DailyDietModel *)dietModel
            calendar:(CalendarModel *)calendar
            overview:(MRKPlanOverViewModel *)overview
                 day:(MRKDailyScheduleAIPlanDayModel *)day
              target:(DailyTargetModel *)target
           indexPath:(NSIndexPath *)indexPath{
    {
        self.statusImageView.hidden = YES;
        self.exchangeBtn.hidden = YES;
        self.statusView.hidden = YES;
        self.pointView.backgroundColor = UIColorHex(#363A44);
    }
    
    ///
    self.targetItem = model;
    
    if (model.taskType == MRKTrainingTypeDrink) {
        NSString *title =  calendar.timeReference.intValue == 2 ? @"今日饮食" : @"当日饮食";
        self.titleLabel.text = [NSString stringWithFormat:@"记录%@", title];
        
        [self showFoodItem];
        ///
        self.foodItemView.model = model;
        self.foodItemView.calendar = calendar;
        self.foodItemView.dietModel = dietModel;
        self.foodItemView.overview = overview;
        self.foodItemView.day = day;
        self.foodItemView.targetModel = target;
        
        if (model.taskStatus == 1 ) {
            self.statusImageView.hidden = NO;
        }
    } else {
        NSString *title = model.taskType == 1 ? @"课程训练" : @"动作训练";
        self.titleLabel.text  = [NSString stringWithFormat:@"推荐%@ %@", [self numberToChinese:(int)indexPath.row], title];
        
        [self showCourseItem];
        [self.courseItemView setModel:model andTimeReference:calendar.timeReference];
        
        if (model.taskStatus == 1 ) {
            self.statusImageView.hidden = NO;
        } else {
            ///只有今天有换一换
            if (calendar.timeReference.intValue == 2 ) {
                self.exchangeBtn.hidden = NO;
            }
        }
    }
}


- (void)courseCellLiving:(NSString *)tag {
    self.statusView.hidden = NO;
    
    self.statusView.backgroundColor = UIColorHex(#FF5363);
    self.statusView.icon = [UIImage imageNamed:@"icon_play"];
    self.statusView.title = tag;
    self.statusView.layerColors = @[
        (__bridge id)[UIColorHex(#FF2451) CGColor],
        (__bridge id)[UIColorHex(#FFFFFF) CGColor]
    ];
}

- (void)courseTag:(NSString *)tag image:(UIImage *)image{
    self.statusView.hidden = NO;
    
    self.statusView.backgroundColor = UIColorHex(#B3B5B9);
    self.statusView.icon = image;
    self.statusView.title = tag;
    self.statusView.layerColors = nil;
}

- (UIView *)contentItemView{
    if (!_contentItemView) {
        _contentItemView = [[UIView alloc] init];
        _contentItemView.backgroundColor = UIColor.whiteColor;
        _contentItemView.layer.cornerRadius = 8.0;
        _contentItemView.layer.masksToBounds = YES;
    }
    return _contentItemView;
}

- (UIView *)pointView{
    if (!_pointView) {
        _pointView = [[UIView alloc] init];
        _pointView.backgroundColor = UIColorHex(#363A44);
        _pointView.layer.cornerRadius = DHPX(4)/2;
        _pointView.layer.masksToBounds = YES;
    }
    return _pointView;
}

- (UILabel *)titleLabel{
    if (!_titleLabel) {
        _titleLabel= [[UILabel alloc]init];
        _titleLabel.font = [UIFont systemFontOfSize:WKDHPX(14)];
        _titleLabel.textColor = [UIColor colorWithHexString:@"#848A9B"];
    }
    return _titleLabel;
}

- (UIButton *)exchangeBtn {
    if(!_exchangeBtn){
        _exchangeBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_exchangeBtn setImage:[UIImage imageNamed:@"daily_plan_exchange_icon"] forState:UIControlStateNormal];
        [_exchangeBtn setTitle:@" 换一换" forState:UIControlStateNormal];
        [_exchangeBtn setTitleColor:UIColorHex(#B3B5B9) forState:UIControlStateNormal];
        _exchangeBtn.titleLabel.font = kSystem_Font_NoDHPX(WKDHPX(12));
        [_exchangeBtn addTarget:self action:@selector(exchangeAIPlanItem:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _exchangeBtn;
}

- (void)exchangeAIPlanItem:(id)sender{
    if (self.targetItem){
        [[MRKAIPlanLogic shared] planTaskExchange:self.targetItem];
    }
}

- (UIImageView *)statusImageView{
    if (!_statusImageView) {
        _statusImageView = [[UIImageView alloc] init];
        _statusImageView.image = [UIImage imageNamed:@"schedule_completed"];
    }
    return _statusImageView;
}

- (MRKCourseTagStatusView *)statusView{
    if (!_statusView) {
        _statusView = [[MRKCourseTagStatusView alloc] init];
    }
    return _statusView;
}

- (UIView *)lineView{
    if (!_lineView) {
        _lineView = [[UIView alloc] init];
        _lineView.backgroundColor =  UIColorHexAlpha(#363A44, 0.06);
    }
    return _lineView;
}

- (UIView *)itemView{
    if (!_itemView) {
        _itemView = [[UIView alloc] init];
    }
    return _itemView;
}

- (MRKDailyAIPlanItemView *)courseItemView{
    if (!_courseItemView) {
        _courseItemView = [[MRKDailyAIPlanItemView alloc] init];
    }
    return _courseItemView;
}

- (MRKDailyFoodItemView *)foodItemView{
    if (!_foodItemView) {
        _foodItemView = [[MRKDailyFoodItemView alloc] init];
    }
    return _foodItemView;
}

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];
    
    // Configure the view for the selected state
}

@end





@interface MRKDailyAIPlanItemView()
@property (nonatomic, strong) UIImageView *playImageView;
@property (nonatomic, strong) UIImageView *coverImageView;

@property (nonatomic, strong) YYLabel *titleLabel;
@property (nonatomic, strong) UILabel *descLabel;
@end

@implementation MRKDailyAIPlanItemView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {

        [self addSubview:self.coverImageView];
        [self.coverImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.mas_equalTo(self.mas_left);
            make.centerY.equalTo(self.mas_centerY);
            make.size.mas_equalTo(CGSizeMake(DHPX(90), DHPX(68)));
        }];

        [self.coverImageView addSubview:self.playImageView];
        [self.playImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.center.equalTo(self.coverImageView);
            make.size.mas_equalTo(CGSizeMake(DHPX(24), DHPX(24)));
        }];

        [self addSubview:self.titleLabel];
        [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.mas_top).offset(DHPX(12));
            make.left.equalTo(self.coverImageView.mas_right).offset(DHPX(8));
            make.right.mas_equalTo(self.mas_right).offset(-DHPX(8));
            make.height.mas_greaterThanOrEqualTo(DHPX(20));
        }];

        [self addSubview:self.descLabel];
        [self.descLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.titleLabel.mas_bottom).offset(DHPX(8));
            make.left.right.equalTo(self.titleLabel);
            make.bottom.equalTo(self.mas_bottom).offset(-DHPX(14));
            make.height.mas_greaterThanOrEqualTo(DHPX(16));
        }];

    }
    return self;
}

- (void)layoutSubviews
{
    [super layoutSubviews];
    
}

///
- (void)setModel:(MRKDailyScheduleItemModel *)model andTimeReference:(NSString *)timeReference{
    NSString *middleURl = [NSString imageUrlClip:model.cover andSize:CGSizeMake(DHPX(90), DHPX(68))];
    [self.coverImageView setImageWithURL:[NSURL URLWithString:middleURl]
                              placeholder:[UIImage imageNamed:@"pic_1"]
                                  options:YYWebImageOptionProgressiveBlur|YYWebImageOptionSetImageWithFadeAnimation
                               completion:nil];
    
    if (model.taskType == MRKTrainingTypeCourse) {
        self.titleLabel.text = model.title;
        
        NSString *time = [MRKTimeManager secondsTransformTimeString:model.duration.intValue];
        self.descLabel.attributedText = ({
            NSMutableAttributedString *deviceText = [[NSMutableAttributedString alloc] init];
            ///添加设备图标
            NSString *device = [MRKEquipmentTypeData deviceImageFromIconFont:model.equipmentId];
            NSMutableAttributedString *equipmentText = [[NSMutableAttributedString alloc] initWithString:device];
            equipmentText.font = [UIFont fontWithName:@"iconfont" size:WKDHPX(18)];
            equipmentText.color = UIColorHex(#B3B5B9);
            [deviceText appendAttributedString:equipmentText];
            
            NSString *descrip = [NSString stringWithFormat:@"%@  %@  %@千卡", model.gradeDesc, time, model.kcal];
            NSMutableAttributedString *attachText = [[NSMutableAttributedString alloc] initWithString:descrip];
            attachText.color = UIColorHex(#B3B5B9);
            attachText.font = [UIFont systemFontOfSize:WKDHPX(12)];
            [deviceText appendAttributedString:attachText];
            [deviceText addAttribute:NSBaselineOffsetAttributeName value:@(0.36 *(WKDHPX(18) -WKDHPX(12))) range:NSMakeRange(deviceText.length - attachText.length, attachText.length)];
            deviceText;
        });
    } else if (model.taskType == MRKTrainingTypeMotion) {
        NSString *time = [MRKTimeManager secondsTransformTimeString:model.trainingTargetDuration.intValue];
        NSString *count = [NSString stringWithFormat:@"%@个", model.trainingTargetValue];
        ///动作目标类型 1 数量  2 时间
        NSString *tips = @"";
        switch (model.trainingTargetType) {
            case 1:
                tips = count;
                break;
            case 2:
                tips = time;
                break;
            default:
                break;
        }
        
        ///
        BOOL canEdit = timeReference.integerValue == 2 && model.taskStatus == 0;
        
        ///箭头图标
        NSString *str = [NSString stringWithFormat:@"%@%@", tips, canEdit ? @"\u{000e614}":@""];
        NSMutableAttributedString *equipmentText = [[NSMutableAttributedString alloc] initWithString:str];
        equipmentText.font = [UIFont fontWithName:@"iconfont" size:WKDHPX(14)];
        equipmentText.color = UIColorHex(#848A9B);
        
        NSMutableAttributedString *attributedTitle = [[NSMutableAttributedString alloc] initWithString:model.title?:@""];
        attributedTitle.color = UIColorHex(#363A44);
        attributedTitle.font = [UIFont systemFontOfSize:WKDHPX(15)];
        [attributedTitle appendAttributedString:equipmentText];
        
        NSRange tapRange = [[attributedTitle string] rangeOfString:[equipmentText string]];
        [attributedTitle setTextHighlightRange:tapRange
                                         color:UIColorHex(#848A9B)
                               backgroundColor:[UIColor clearColor]
                                     tapAction:^(UIView *containerView, NSAttributedString *text, NSRange range, CGRect rect) {
            if (canEdit){
                [[MRKAIPlanLogic shared] planMotionTaskTrainingTargetReplace:model];
            }
        }];
        self.titleLabel.attributedText = attributedTitle;
        self.titleLabel.numberOfLines = 0;

        ///
        self.descLabel.attributedText = ({
            NSString *descrip = [NSString stringWithFormat:@"%@  %@  %@千卡", model.part ?: @"", time, model.kcal];
            NSMutableAttributedString *attachText = [[NSMutableAttributedString alloc] initWithString:descrip];
            attachText.color = UIColorHex(#B3B5B9);
            attachText.font = [UIFont systemFontOfSize:WKDHPX(12)];
            attachText;
        });
    }
}

- (UIImageView *)coverImageView{
    if (!_coverImageView) {
        _coverImageView = [[UIImageView alloc] init];
        _coverImageView.contentMode = UIViewContentModeScaleAspectFill;
        _coverImageView.clipsToBounds = YES;
        _coverImageView.layer.cornerRadius = 8.0f;
        _coverImageView.layer.masksToBounds = YES;
    }
    return _coverImageView;
}

- (UIImageView *)playImageView{
    if (!_playImageView) {
        _playImageView = [[UIImageView alloc] init];
        _playImageView.contentMode = UIViewContentModeScaleAspectFill;
        _playImageView.clipsToBounds = YES;
        _playImageView.image = [UIImage imageNamed:@"daily_course_play_icon"];
    }
    return _playImageView;
}

- (YYLabel *)titleLabel{
    if (!_titleLabel) {
        _titleLabel = [[YYLabel alloc] init];
        _titleLabel.font = [UIFont systemFontOfSize:WKDHPX(15)];
        _titleLabel.textColor = [UIColor colorWithHexString:@"#363A44"];
        _titleLabel.numberOfLines = 0;
        float labelWidth = RealScreenWidth - DHPX(16) * 2 - DHPX(12) * 2 - DHPX(90) - DHPX(8) * 2;
        _titleLabel.preferredMaxLayoutWidth = labelWidth;///换行必须设置
    }
    return _titleLabel;
}

- (UILabel *)descLabel{
    if (!_descLabel) {
        _descLabel = [[UILabel alloc] init];
        _descLabel.font = [UIFont systemFontOfSize:WKDHPX(12)];
        _descLabel.textColor = [UIColor colorWithHexString:@"#848A9B"];
        _descLabel.numberOfLines = 0;
    }
    return _descLabel;
}

@end






@interface MRKDailyFoodItemView()
@property (nonatomic, strong) MRKFoodItemView *breakfastView;
@property (nonatomic, strong) MRKFoodItemView *lunchView;
@property (nonatomic, strong) MRKFoodItemView *dinnerView;
@property (nonatomic, strong) MRKFoodItemView *snackView;
@property (nonatomic, strong) MRKFoodItemView *drinkView;

@property (nonatomic, strong) UIButton *foodRecordBtn;
@property (nonatomic, strong) UIButton *dietRecipeBtn;
@end

@implementation MRKDailyFoodItemView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = [UIColor clearColor];
        
        /// 添加健康数据
        [self addSubview:self.breakfastView];
        [self addSubview:self.lunchView];
        [self addSubview:self.dinnerView];
        [self addSubview:self.snackView];
        [self addSubview:self.drinkView];
        
        NSMutableArray *arr = @[self.breakfastView, self.lunchView, self.dinnerView, self.snackView, self.drinkView].mutableCopy;
        [arr mas_distributeViewsAlongAxis:MASAxisTypeHorizontal withFixedSpacing:DHPX(8) leadSpacing:0 tailSpacing:0];
        [arr mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.mas_top).offset(0);
            make.height.mas_equalTo(DHPX(70));
        }];
        
        CGFloat width = (RealScreenWidth - DHPX(28 * 2) - DHPX(10)) / (132.0+177.0);
        
        [self addSubview:self.dietRecipeBtn];
        [self.dietRecipeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.mas_top).offset(DHPX(86));
            make.bottom.equalTo(self.mas_bottom).offset(0);
            make.left.mas_equalTo(DHPX(0));
            make.height.mas_equalTo(DHPX(40));
            make.width.mas_equalTo(width * 132.0);
        }];
        
        [self addSubview:self.foodRecordBtn];
        [self.foodRecordBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.mas_top).offset(DHPX(86));
            make.bottom.equalTo(self.mas_bottom).offset(0);
            make.right.mas_equalTo(-DHPX(0));
            make.height.mas_equalTo(DHPX(40));
            make.width.mas_equalTo(width * 177.0);
        }];
    }
    return self;
}

- (void)layoutSubviews
{
    [super layoutSubviews];
}

- (void)setCalendar:(CalendarModel *)calendar {
    _calendar = calendar;
}

- (void)setOverview:(MRKPlanOverViewModel *)overview{
    [_dietRecipeBtn setTitle:overview.currentPlan.type == 1 ? @" 减脂食谱" : @" 健康食谱"  forState:UIControlStateNormal];
}

- (void)setDietModel:(DailyDietModel *)dietModel
{
    _dietModel = dietModel;

    // 早餐
    if (dietModel.breakfast.targetCalorie.doubleValue > 0) {
        self.breakfastView.progress = dietModel.breakfast.totalCalorie.doubleValue / dietModel.breakfast.targetCalorie.doubleValue;
    } else {
        self.breakfastView.progress = 0;
    }
    self.breakfastView.value = [self kcalStringFrom:dietModel.breakfast.totalCalorie];

    // 午餐
    if (dietModel.lunch.targetCalorie.doubleValue > 0) {
        self.lunchView.progress = dietModel.lunch.totalCalorie.doubleValue / dietModel.lunch.targetCalorie.doubleValue;
    } else {
        self.lunchView.progress = 0;
    }
    self.lunchView.value = [self kcalStringFrom:dietModel.lunch.totalCalorie];

    // 晚餐
    if (dietModel.dinner.targetCalorie.doubleValue > 0) {
        self.dinnerView.progress = dietModel.dinner.totalCalorie.doubleValue / dietModel.dinner.targetCalorie.doubleValue;
    } else {
        self.dinnerView.progress = 0;
    }
    self.dinnerView.value = [self kcalStringFrom:dietModel.dinner.totalCalorie];

    // 加餐
    if (dietModel.snack.targetCalorie.doubleValue > 0) {
        self.snackView.progress = dietModel.snack.totalCalorie.doubleValue / dietModel.snack.targetCalorie.doubleValue;
    } else {
        self.snackView.progress = 0;
    }
    self.snackView.value = [self kcalStringFrom:dietModel.snack.totalCalorie];

    // 饮水
    if (dietModel.drink.targetWaterIntake.doubleValue > 0) {
        self.drinkView.progress = dietModel.drink.waterIntake.doubleValue / dietModel.drink.targetWaterIntake.doubleValue;
    } else {
        self.drinkView.progress = 0;
    }
    self.drinkView.value = dietModel.drink.waterIntake;
}

- (NSString *)kcalStringFrom:(NSString *)calorie {
    return [NSString stringWithFormat:@"%.1f", ceil(calorie.doubleValue * 10)/10.0];
}

- (MRKFoodItemView *)breakfastView {
    if (!_breakfastView) {
        _breakfastView = [[MRKFoodItemView alloc] init];
        _breakfastView.dietType = MRKDailyDietTypeBreakfast;
        [_breakfastView addTarget:self action:@selector(dietRecordAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _breakfastView;
}

- (MRKFoodItemView *)lunchView {
    if (!_lunchView) {
        _lunchView = [[MRKFoodItemView alloc] init];
        _lunchView.dietType = MRKDailyDietTypeLunch;
        [_lunchView addTarget:self action:@selector(dietRecordAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _lunchView;
}

- (MRKFoodItemView *)dinnerView {
    if (!_dinnerView) {
        _dinnerView = [[MRKFoodItemView alloc] init];
        _dinnerView.dietType = MRKDailyDietTypeDinner;
        [_dinnerView addTarget:self action:@selector(dietRecordAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _dinnerView;
}

- (MRKFoodItemView *)snackView {
    if (!_snackView) {
        _snackView = [[MRKFoodItemView alloc] init];
        _snackView.dietType = MRKDailyDietTypeSnack;
        [_snackView addTarget:self action:@selector(dietRecordAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _snackView;
}

- (MRKFoodItemView *)drinkView {
    if (!_drinkView) {
        _drinkView = [[MRKFoodItemView alloc] init];
        _drinkView.dietType = MRKDailyDietTypeDrink;
        [_drinkView addTarget:self action:@selector(dietRecordAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _drinkView;
}

- (UIButton *)foodRecordBtn {
    if (!_foodRecordBtn) {
        _foodRecordBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_foodRecordBtn setBackgroundColor:[UIColor colorWithHexString:@"#F3F5F9"]];
        [_foodRecordBtn setImage:[UIImage imageNamed:@"daily_add_food_icon"] forState:UIControlStateNormal];
        [_foodRecordBtn setTitle:@" AI饮食记录" forState:UIControlStateNormal];
        [_foodRecordBtn setTitleColor:[UIColor colorWithHexString:@"#363A44"] forState:UIControlStateNormal];
        _foodRecordBtn.titleLabel.font = kMedium_PingFangSC(15);
        _foodRecordBtn.layer.cornerRadius = DHPX(40)/2;
        _foodRecordBtn.layer.masksToBounds = YES;
        [_foodRecordBtn addTarget:self action:@selector(foodRecordBtnTapped:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _foodRecordBtn;
}

- (UIButton *)dietRecipeBtn {
    if (!_dietRecipeBtn) {
        _dietRecipeBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_dietRecipeBtn setBackgroundColor:[UIColor colorWithHexString:@"#F3F5F9"]];
        [_dietRecipeBtn setImage:[[UIImage imageNamed:@"daily_lunch"] scaleToSize:CGSizeMake(DHPX(16), DHPX(16))] forState:UIControlStateNormal];
        [_dietRecipeBtn setTitleColor:[UIColor colorWithHexString:@"#363A44"] forState:UIControlStateNormal];
        _dietRecipeBtn.titleLabel.font = kMedium_PingFangSC(15);
        _dietRecipeBtn.layer.cornerRadius = DHPX(40)/2;
        _dietRecipeBtn.layer.masksToBounds = YES;
        _dietRecipeBtn.traceEventId = @"btn_schedule_fat_loss_recipe";
        [_dietRecipeBtn addTarget:self action:@selector(dietRecipeBtnTapped:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _dietRecipeBtn;
}

- (NSMutableAttributedString *)attributedString:(NSString *)title
                                       andImage:(UIImage *)image{
    NSMutableAttributedString *nameText = [[NSMutableAttributedString alloc] initWithString: title?:@""];
    nameText.color = UIColorHex(#363A44);
    nameText.font = [UIFont systemFontOfSize:15 weight:UIFontWeightMedium];
    NSMutableAttributedString *attachText = [NSMutableAttributedString attachmentStringWithContent:image
                                                                                       contentMode:UIViewContentModeCenter
                                                                                    attachmentSize:image.size
                                                                                       alignToFont:[UIFont systemFontOfSize:WKDHPX(16)]
                                                                                         alignment:YYTextVerticalAlignmentCenter];
    [nameText insertAttributedString:attachText atIndex:0];
    nameText.alignment = NSTextAlignmentCenter;
    return nameText;
}


- (void)dietRecordAction:(id)sender {
    
    if (self.model.lock) {
        [MBProgressHUD showMessage:@"还没到开始时间呢，请按节奏进行训练" toView:nil];
        return;
    }
    
    if ([sender isKindOfClass:[MRKFoodItemView class]]){
        MRKFoodItemView *item = (MRKFoodItemView *)sender;
        switch (item.dietType) {
            case MRKDailyDietTypeDrink:
                [[MRKDailyLogic shared] jumpToDietRecord:self.calendar.date index:1];
                break;
            default:
                [[MRKDailyLogic shared] jumpToDietRecord:self.calendar.date index:0];
                break;
        }
    }
 
}

- (void)foodRecordBtnTapped:(UIButton *)sender {
    if (self.model.lock) {
        [MBProgressHUD showMessage:@"还没到开始时间呢，请按节奏进行训练" toView:nil];
        return;
    }
    
    [[MRKAIPlanLogic shared] recordDiet:self.calendar.date fromMainPage:NO];
}

- (void)dietRecipeBtnTapped:(UIButton *)sender {
    if ([self.day.isNeedGenerateRecipe boolValue]) {
        [[MRKAIPlanLogic shared] dynamicCreateDietRecipe:self.day.planId date:self.calendar.date targetDietCalories:self.targetModel.targetDietCalories];
    } else {
        [[MRKAIPlanLogic shared] jumpToDietRecipePage:self.day.planId date:self.calendar.date];
    }
}
@end






@interface MRKFoodItemView()
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) MrkProgressBar *progressView;
@property (nonatomic, strong) YYLabel *descLabel;
@end

@implementation MRKFoodItemView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = [UIColor whiteColor];
        self.layer.cornerRadius = 8.0f;
        self.layer.masksToBounds = YES;
    
        [self addSubview:self.titleLabel];
        [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.mas_top).offset(DHPX(8));
            make.centerX.mas_equalTo(self.mas_centerX);
            make.width.mas_equalTo(self.mas_width);
        }];
        
        [self addSubview:self.progressView];
        [self.progressView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.titleLabel.mas_bottom).offset(DHPX(8));
            make.centerX.mas_equalTo(self.mas_centerX);
            make.size.mas_equalTo(CGSizeMake(DHPX(40), DHPX(4)));
        }];
        
        [self addSubview:self.descLabel];
        [self.descLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.progressView.mas_bottom).offset(DHPX(8));
            make.bottom.mas_equalTo(self.mas_bottom).offset(-DHPX(8));
            make.centerX.mas_equalTo(self.mas_centerX);
            make.height.mas_equalTo(WKDHPX(13));
            make.width.mas_equalTo(self.mas_width);
        }];
    }
    return self;
}

- (void)layoutSubviews {
    [super layoutSubviews];
    
}

- (void)setProgress:(double)progress {
    _progress = progress;
    self.progressView.progress = MIN(progress, 1.0);
    
    if (progress <= 1.0) {
        self.progressView.progressColor = UIColorHex(#16D2E3);
    } else if (progress <= 1.5 && progress > 1.0) {
        self.progressView.progressColor = UIColorHex(#FD9F5B);
    } else {
        self.progressView.progressColor = UIColorHex(#F1554A);
    }
}

- (void)setValue:(NSString *)value {
    _value = value;
    self.titleLabel.text = value ?: @"--";
}

- (void)setDietType:(MRKDailyDietType)dietType{
    _dietType = dietType;
    
    NSMutableAttributedString *attributedString = nil;
    switch (dietType) {
        case MRKDailyDietTypeBreakfast:
            attributedString = [self attributedString:@"早餐" andImage:[UIImage imageNamed:@"daily_breakfast"]];
            break;
        case MRKDailyDietTypeLunch:
            attributedString = [self attributedString:@"午餐" andImage:[UIImage imageNamed:@"daily_lunch"]];
            break;
        case MRKDailyDietTypeDinner:
            attributedString = [self attributedString:@"晚餐" andImage:[UIImage imageNamed:@"daily_lunch"]];
            break;
        case MRKDailyDietTypeSnack:
            attributedString = [self attributedString:@"加餐" andImage:[UIImage imageNamed:@"daily_snack"]];
            break;
        case MRKDailyDietTypeDrink:
            attributedString = [self attributedString:@"饮水" andImage:[UIImage imageNamed:@"daily_drink"]];
            break;
        default:
            break;
    }
    
    if (attributedString){
        self.descLabel.attributedText =  attributedString;
        self.descLabel.textAlignment = NSTextAlignmentCenter;
    }
}

- (NSMutableAttributedString *)attributedString:(NSString *)title
                                       andImage:(UIImage *)image{
    NSMutableAttributedString *nameText = [[NSMutableAttributedString alloc] initWithString: title?:@""];
    nameText.color = UIColorHex(#363A44);
    nameText.font = [UIFont systemFontOfSize:WKDHPX(12)];
    NSMutableAttributedString *attachText = [NSMutableAttributedString attachmentStringWithContent:image
                                                                                       contentMode:UIViewContentModeCenter
                                                                                    attachmentSize:image.size
                                                                                       alignToFont:[UIFont systemFontOfSize:WKDHPX(11)]
                                                                                         alignment:YYTextVerticalAlignmentCenter];
    [nameText insertAttributedString:attachText atIndex:0];
    nameText.alignment = NSTextAlignmentCenter;
    return nameText;
}

- (MrkProgressBar *)progressView {
    if (!_progressView) {
        _progressView = [[MrkProgressBar alloc] init];
        _progressView.progressColor = UIColorHex(#16D2E3);
        _progressView.backgroundProgressColor = UIColorHex(#E1E5EE);
        _progressView.layer.cornerRadius = DHPX(4)/2;
        _progressView.clipsToBounds = YES;
        _progressView.progress = 0.0;
    }
    return _progressView;
}

- (UILabel *)titleLabel{
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.font = [UIFont fontWithName:Bebas_Font size:WKDHPX(20)];
        _titleLabel.textColor = [UIColor colorWithHexString:@"#363A44"];
        _titleLabel.textAlignment = NSTextAlignmentCenter;
    }
    return _titleLabel;
}

- (YYLabel *)descLabel{
    if (!_descLabel) {
        _descLabel = [[YYLabel alloc] init];
        _descLabel.textAlignment = NSTextAlignmentCenter;
        _descLabel.textVerticalAlignment = YYTextVerticalAlignmentCenter;
        _descLabel.textColor = [UIColor colorWithHexString:@"#B3B5B9"];
        _descLabel.font = [UIFont systemFontOfSize:WKDHPX(12)];
    }
    return _descLabel;
}

@end
