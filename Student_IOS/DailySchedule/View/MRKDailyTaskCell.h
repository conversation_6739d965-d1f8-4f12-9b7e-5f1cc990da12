//
//  MRKDailyTaskCell.h
//  Student_IOS
//
//  Created by <PERSON>q on 2025/6/25.
//

#import <UIKit/UIKit.h>
#import "MRKDailyScheduleModel.h"
#import "CalendarModel.h"
NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, MRKDailyDietType) {
    MRKDailyDietTypeBreakfast = 0,  // 早餐
    MRKDailyDietTypeLunch,          // 午餐
    MRKDailyDietTypeDinner,         // 晚餐
    MRKDailyDietTypeSnack,          // 加餐
    MRKDailyDietTypeDrink           // 饮水
};


@interface MRKDailyTaskCell : UITableViewCell
///
- (void)setModel:(MRKDailyScheduleModel *)model andTimeReference:(NSString *)timeReference;
///
- (void)setItemModel:(MRKDailyScheduleItemModel *)model
           dietModel:(DailyDietModel *)dietModel
            calendar:(CalendarModel *)calendar
            overview:(MRKPlanOverViewModel *)overview
                 day:(MRKDailyScheduleAIPlanDayModel *)day
              target:(DailyTargetModel *)target
           indexPath:(NSIndexPath *)indexPath;
@end


@interface MRKDailyAIPlanItemView : UIView
//@property (nonatomic, strong) MRKDailyScheduleItemModel *model;
- (void)setModel:(MRKDailyScheduleItemModel *)model andTimeReference:(NSString *)timeReference;
@end



@interface MRKDailyFoodItemView : UIView
@property (nonatomic, strong) MRKDailyScheduleItemModel *model;
@property (nonatomic, strong) DailyTargetModel *targetModel;
@property (nonatomic, strong) DailyDietModel *dietModel;
@property (nonatomic, strong) CalendarModel *calendar;
@property (nonatomic, strong) MRKPlanOverViewModel *overview;
@property (nonatomic, strong) MRKDailyScheduleAIPlanDayModel *day;
@end



@interface MRKFoodItemView : UIControl
@property (nonatomic, assign) MRKDailyDietType dietType;
@property (nonatomic, assign) double progress;
@property (nonatomic, copy) NSString *value;
@end

NS_ASSUME_NONNULL_END
