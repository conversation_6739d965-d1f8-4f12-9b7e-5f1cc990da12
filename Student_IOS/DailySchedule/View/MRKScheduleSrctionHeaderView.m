//
//  MRKScheduleSrctionHeaderView.m
//  Student_IOS
//
//  Created by <PERSON><PERSON> on 2023/8/2.
//

#import "MRKScheduleSrctionHeaderView.h"
#import "UIView+AZGradient.h"

@interface MRKScheduleSrctionHeaderView ()
@property (nonatomic, strong) UIView *lineView;
@property (nonatomic, strong) UIView *pointView;
@property (nonatomic, strong) UILabel *sectionTitleLab;
@property (nonatomic, strong) UIButton *moreBtn;
@end

@implementation MRKScheduleSrctionHeaderView

- (instancetype)init{
    self = [super init];
    if (self) {
        self.frame = CGRectMake(0, 0, kScreenWidth, DHPX(58));
        self.backgroundColor = UIColorHex(#FFFFFF);
      
        [self addSubview:self.lineView];
        [self addSubview:self.sectionTitleLab];
        [self addSubview:self.pointView];
        [self addSubview:self.moreBtn];
    }
    return self;
}

- (void)layoutSubviews {
    [super layoutSubviews];
    
    [self.lineView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self);
        make.centerX.equalTo(self.mas_centerX);
        make.size.mas_equalTo(CGSizeMake(RealScreenWidth, DHPX(8)));
    }];

    [self.sectionTitleLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self).offset(DHPX(4));
        make.left.mas_equalTo(DHPX(28));
        make.right.mas_equalTo(-DHPX(100));
        make.height.mas_equalTo(DHPX(26));
    }];
    
    [self.pointView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.sectionTitleLab);
        make.left.mas_equalTo(DHPX(16));
        make.size.mas_equalTo(CGSizeMake(DHPX(4), DHPX(18)));
    }];
    
    [self.moreBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.sectionTitleLab);
        make.right.equalTo(self.mas_right).offset(-DHPX(16));
    }];
}

- (void)setShowGradientLayer:(BOOL)showGradientLayer {
    _showGradientLayer = showGradientLayer;
    
    
    if (showGradientLayer) {
        UIView *gradientView = [[UIView alloc] initWithFrame:cell.bounds];
        CAGradientLayer *gradientLayer = [CAGradientLayer layer];
        gradientLayer.frame = CGRectMake(0, 0, kScreenWidth, 100);
        gradientLayer.colors = @[
            (id)[UIColor whiteColor].CGColor,
            (id)UIColorHex(#F3F5F9).CGColor
        ];
        gradientLayer.startPoint = CGPointMake(0.5, 0);
        gradientLayer.endPoint = CGPointMake(0.5, 1);
        [gradientView.layer insertSublayer:gradientLayer atIndex:0];
        cell.backgroundView = gradientView;
    }
   
}





- (void)setSectionTitle:(NSString *)sectionTitle{
    _sectionTitle = sectionTitle;
    self.sectionTitleLab.text = sectionTitle;
    
}

- (void)setMoreTitle:(NSString *)moreTitle{
    self.moreBtn.hidden = moreTitle.length == 0;
    [_moreBtn setTitle:moreTitle forState:UIControlStateNormal];
}

- (UIView *)lineView{
    if (!_lineView) {
        _lineView = [[UIView alloc] init];
        _lineView.backgroundColor = UIColorHex(#F3F5F9);
    }
    return _lineView;
}

- (UIView *)pointView{
    if (!_pointView) {
        _pointView = [[UIView alloc] init];
        [_pointView az_setGradientBackgroundWithColors:@[[UIColor colorWithHexString:@"#00DDF1"],
                                                         [UIColor colorWithHexString:@"#31FBFF"]]
                                             locations:@[@(0), @(1.0f)]
                                            startPoint:CGPointMake(0.5, 0)
                                              endPoint:CGPointMake(0.5, 1)];
        _pointView.layer.cornerRadius = DHPX(2.0f);
        _pointView.layer.masksToBounds = YES;
    }
    return _pointView;
}

- (UILabel *)sectionTitleLab{
    if (!_sectionTitleLab) {
        _sectionTitleLab = [[UILabel alloc]init];
        _sectionTitleLab.font = kMedium_Font_NoDHPX(WKDHPX(18));
        _sectionTitleLab.textColor = UIColorHex(#363A44);
        _sectionTitleLab.text = @"今日任务";
    }
    return _sectionTitleLab;
}

- (UIButton *)moreBtn {
    if (!_moreBtn) {
        _moreBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_moreBtn setTitle:@"全部计划" forState:UIControlStateNormal];
        [_moreBtn setTitleColor:[UIColor colorWithHexString:@"#999999"] forState:UIControlStateNormal];
        _moreBtn.titleLabel.font = kSystem_Font(12);
        [_moreBtn addTarget:self action:@selector(planMoreClick:) forControlEvents:UIControlEventTouchUpInside];
        UIImage *image = [[UIImage imageNamed:@"icon_cell_right_arrow"] imageByResizeToSize:CGSizeMake(16, 16)];
        [_moreBtn setImage:image forState:UIControlStateNormal];
        [_moreBtn layoutButtonWithEdgeInsetsStyle:ButtonEdgeInsetsStyleRight imageTitleSpace:4];
        _moreBtn.hidden = YES;
    }
    return _moreBtn;
}

- (void)planMoreClick:(UIButton *)sender {
    if (self.moreHandleActionBlock) {
        self.moreHandleActionBlock();
    }
}

/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/

@end
