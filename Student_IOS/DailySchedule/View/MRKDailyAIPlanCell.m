//
//  MRKDailyAIPlanCell.m
//  Student_IOS
//
//  Created by merit on 2025/2/27.
//

#import "MRKDailyAIPlanCell.h"
#import "MRKAIPlanLogic.h"
#import "MRKDailyTaskCell.h"

@interface MRKDailyAIPlanCell()
@property (nonatomic, strong) MRKDailyAIPlanTableView *taskTableView;
@end

@implementation MRKDailyAIPlanCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        [self setupTableView];
    }
    return self;
}

/**
 * 设置内部表格视图
 */
- (void)setupTableView {
    [self.contentView addSubview:self.taskTableView];
    [self.taskTableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsMake(0, 0, 0, 0));
        make.height.mas_equalTo(0);
    }];
    
    
    @weakify(self);
    self.taskTableView.contentSizeChangedBlock = ^(CGFloat height) {
        @strongify(self);
        self.height = height;
        if (self.updateHeightBlock) {
            self.updateHeightBlock(height);
        }
    };
}

- (void)layoutSubviews {
    [super layoutSubviews];
}

/**
 * 设置数据并刷新UI
 * @param model 日程模型
 * @param dietModel 饮食模型
 * @param calendar 日历模型
 * @param overview 概览模型
 */
- (void)setItem:(MRKDailyScheduleModel *)model
      dietModel:(DailyDietModel *)dietModel
       calendar:(CalendarModel *)calendar
       overview:(MRKPlanOverViewModel *)overview
         target:(DailyTargetModel *)target{
    
    [self.taskTableView setItem:model dietModel:dietModel calendar:calendar overview:overview target:target];
}

#pragma mark - UITableView Lazy Loading

- (MRKDailyAIPlanTableView *)taskTableView {
    if (!_taskTableView) {
        _taskTableView = [[MRKDailyAIPlanTableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
    }
    return _taskTableView;
}


- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];
    
    // Configure the view for the selected state
}

@end




@interface MRKDailyAIPlanTableView ()<UITableViewDataSource, UITableViewDelegate>
@property (nonatomic, strong) NSMutableArray<MRKDailyScheduleItemModel *> *dataArray;
@property (nonatomic, strong) MRKDailyScheduleAIPlanDayModel *trainingDay;
@property (nonatomic, strong) DailyDietModel *dietModel;
@property (nonatomic, strong) CalendarModel *calendar;
@property (nonatomic, strong) DailyTargetModel *targetModel;
@property (nonatomic, strong) MRKPlanOverViewModel *overviewModel;
@end

@implementation MRKDailyAIPlanTableView

- (instancetype)initWithFrame:(CGRect)frame style:(UITableViewStyle)style {
    if (self = [super initWithFrame:frame style:style]) {
        self.backgroundColor = [UIColor clearColor];
        self.showsVerticalScrollIndicator = NO;
        self.showsHorizontalScrollIndicator = NO;
        self.scrollEnabled = NO;
        self.delegate = self;
        self.dataSource = self;
        self.separatorStyle = UITableViewCellSeparatorStyleNone;
        self.rowHeight = UITableViewAutomaticDimension;
        self.estimatedRowHeight = DHPX(134);
        [self registerClass:[MRKDailyTaskCell class] forCellReuseIdentifier:@"MRKDailyTaskCell"];
        
        ///监听高度
        @weakify(self);
        [[RACObserve(self, contentSize) ignore:NULL] subscribeNext:^(id  _Nullable x) {
            @strongify(self);
            [self updateContentSize];
        }];
    }
    return self;
}

- (void)updateContentSize {
    CGFloat height = self.contentSize.height;
    if (height > 0) {
        [self mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(height);
        }];
        if (self.contentSizeChangedBlock) {
            self.contentSizeChangedBlock(height);
        }
    }
}

- (void)layoutSubviews {
    [super layoutSubviews];
}

- (void)setItem:(MRKDailyScheduleModel *)model
      dietModel:(DailyDietModel *)dietModel
       calendar:(CalendarModel *)calendar
       overview:(MRKPlanOverViewModel *)overview
         target:(DailyTargetModel *)target{
    
    self.dataArray = model.aiTrainingDay.tasks.mutableCopy;
    self.trainingDay = model.aiTrainingDay;
    self.dietModel = dietModel;
    self.calendar = calendar;
    self.overviewModel = overview;
    self.targetModel = target;
}

- (void)setDataArray:(NSMutableArray<MRKDailyScheduleItemModel *> *)dataArray{
    _dataArray = dataArray;
    [self reloadData];
    
    
//    // 等待下一个runloop获取正确高度（立即获取可能不准）
//    [self setNeedsLayout];
//    dispatch_async(dispatch_get_main_queue(), ^{
//        CGFloat tableHeight = self.contentSize.height;
//        NSLog(@"TableView 高度: %f", tableHeight);
//        [self mas_updateConstraints:^(MASConstraintMaker *make) {
//            make.height.mas_equalTo(tableHeight);
//        }];
//    });
}

#pragma mark - UITableViewDataSource

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.dataArray.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    MRKDailyTaskCell *cell = [tableView dequeueReusableCellWithIdentifier:@"MRKDailyTaskCell"];
    if (!cell) {
        cell = [[MRKDailyTaskCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"MRKDailyTaskCell"];
    }
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    // 设置cell数据
    MRKDailyScheduleItemModel *model = [self.dataArray objectAtIndex:indexPath.row];
    [cell setItemModel:model
             dietModel:self.dietModel
              calendar:self.calendar
              overview:self.overviewModel
                   day:self.trainingDay
                target:self.targetModel
             indexPath:indexPath];
    return cell;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    {
        MRKDailyScheduleItemModel *model = [self.dataArray objectAtIndex:indexPath.row];
        [[MRKAIPlanLogic shared] jumpToAIPlanTaskTrain:model andTimeReference:self.calendar.timeReference];
    }
}


@end
