//
//  MRKDailyAIPlanCell.h
//  Student_IOS
//
//  Created by merit on 2025/2/27.
//

#import <UIKit/UIKit.h>
#import "MRKDailyScheduleModel.h"
#import "CalendarModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface MRKDailyAIPlanCell : UITableViewCell

- (void)setItem:(MRKDailyScheduleModel *)model
      dietModel:(DailyDietModel *)dietModel
       calendar:(CalendarModel *)calendar
       overview:(MRKPlanOverViewModel *)overview
         target:(DailyTargetModel *)target;

@property (nonatomic, copy) void (^updateHeightBlock)(CGFloat height);
@end



@interface MRKDailyAIPlanTableView : UITableView
@property (nonatomic, copy) void (^contentSizeChangedBlock)(CGFloat height);

- (void)setItem:(MRKDailyScheduleModel *)model
      dietModel:(DailyDietModel *)dietModel
       calendar:(CalendarModel *)calendar
       overview:(MRKPlanOverViewModel *)overview
         target:(DailyTargetModel *)target;
@end

NS_ASSUME_NONNULL_END
