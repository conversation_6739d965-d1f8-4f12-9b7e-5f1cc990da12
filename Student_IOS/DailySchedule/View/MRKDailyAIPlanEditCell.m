//
//  MRKDailyAIPlanEditCell.m
//  Student_IOS
//
//  Created by <PERSON><PERSON> on 2025/6/25.
//

#import "MRKDailyAIPlanEditCell.h"
#import "MrkGradientView.h"
#import "NSString+StringSize.h"
#import "MRKAIPlanLogic.h"



/// 按钮点击类型枚举
typedef NS_ENUM(NSInteger, MRKAIPlanEditCellActionType) {
    MRKAIPlanEditCellActionTypeReformulation,    ///< 重新生成
    MRKAIPlanEditCellActionTypePlanExtension,    ///< 动态延长
    MRKAIPlanEditCellActionTypeUnlockWeek,       ///< 解锁本周
    MRKAIPlanEditCellActionTypeViewWeeklyReport, ///< 查看周报
    MRKAIPlanEditCellActionTypePlanDetail        ///< 计划详情
};


@interface MRKDailyAIPlanEditCell()
@property (nonatomic, strong) MrkGradientView *backGradientView; ///< 背景颜色
@property (nonatomic, strong) UIImageView *iconImageView;        ///< mia 图标
@property (nonatomic, strong) GradientLayerLabel *titleLabel;    ///< 标题
@property (nonatomic, strong) UILabel *dayLabel;                 ///< 天数
@property (nonatomic, strong) UIView *backContentView;
/// 标题
@property (nonatomic, strong) GradientLayerLabel *remindLabel;
@property (nonatomic, strong) UILabel *remarkLabel;
@property (nonatomic, strong) UIButton *reformulationBtn;
@property (nonatomic, strong) UIButton *planExtensionBtn;
@property (nonatomic, strong) MRKDailyScheduleModel *dayModel;
@end

@implementation MRKDailyAIPlanEditCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        [self addAllSubviews];
        [self addBackGradientViewTapGesture];
    }
    return self;
}

/// 增加backGradientView点击事件
- (void)addBackGradientViewTapGesture {
    self.backGradientView.userInteractionEnabled = YES;
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(backGradientViewTapped)];
    [self.backGradientView addGestureRecognizer:tap];
}

- (void)backGradientViewTapped {
    [self aiPlanMoreActionWithType:MRKAIPlanEditCellActionTypePlanDetail];
}

- (void)aiPlanMoreAction:(id)sender {
    MRKAIPlanEditCellActionType actionType = MRKAIPlanEditCellActionTypePlanDetail;
    if (sender == self.reformulationBtn) {
        // 判断按钮当前 title，决定是“重新生成”还是“查看周报”
        NSString *title = self.reformulationBtn.currentTitle ?: @"";
        if ([title containsString:@"重新生成"] || [title containsString:@"重新制定"]) {
            actionType = MRKAIPlanEditCellActionTypeReformulation;
        } else if ([title containsString:@"查看上周报告"] || [title containsString:@"查看周报"]) {
            actionType = MRKAIPlanEditCellActionTypeViewWeeklyReport;
        }
    } else if (sender == self.planExtensionBtn) {
        // 判断按钮当前 title，决定是“动态延长”还是“解锁本周”
        NSString *title = self.planExtensionBtn.currentTitle ?: @"";
        if ([title containsString:@"动态延长"]) {
            actionType = MRKAIPlanEditCellActionTypePlanExtension;
        } else if ([title containsString:@"解锁本周"]) {
            actionType = MRKAIPlanEditCellActionTypeUnlockWeek;
        }
    } else if ([sender isKindOfClass:[UITapGestureRecognizer class]]) {
        actionType = MRKAIPlanEditCellActionTypePlanDetail;
    }

    [self aiPlanMoreActionWithType:actionType];
}

- (void)aiPlanMoreActionWithType:(MRKAIPlanEditCellActionType)type {
    
    if (!self.model.currentPlan) return;
    
    switch (type) {
        case MRKAIPlanEditCellActionTypeReformulation:
            NSLog(@"点击了重新生成");
            // 处理重新生成逻辑
            [[MRKAIPlanLogic shared] jumpToAIPlanResetPage:self.model.currentPlan.planId];
            break;
        case MRKAIPlanEditCellActionTypePlanExtension:
            NSLog(@"点击了动态延长");
            // 处理动态延长逻辑
            [[MRKAIPlanLogic shared] startAndExtendCurrentWeek];
            break;
        case MRKAIPlanEditCellActionTypeUnlockWeek:
            NSLog(@"点击了解锁本周");
            // 处理解锁本周逻辑
            [[MRKAIPlanLogic shared] dynamicStartCurrentWeek];
            break;
        case MRKAIPlanEditCellActionTypeViewWeeklyReport:
            NSLog(@"点击了查看周报");
            // 处理查看周报逻辑
            [[MRKAIPlanLogic shared] jumpToAIPlan];
            break;
        case MRKAIPlanEditCellActionTypePlanDetail:
            NSLog(@"点击了背景区域");
            // 处理背景点击逻辑
            [[MRKAIPlanLogic shared] jumpToAIPlan];
            break;
        default:
            break;
    }
}

- (void)setModel:(MRKPlanOverViewModel *)model {
    _model = model;
    
    self.titleLabel.text = model.currentPlan.title;
    self.dayLabel.text = [NSString stringWithFormat:@" | 第%ld天", model.currentPlan.progressDays];
   
    /// 底部按钮逻辑
    /// AI计划动态延长（需要校验是否有最近24小时内的体重记录，无则走体重更新流程。）
    /// 解锁本周计划（需要校验是否有最近24小时内的体重记录，无则走体重更新流程。）
    if (!model.currentPlan.currentPeriod.isExecuted) {
        [self.reformulationBtn setTitle:@"重新制定" forState:UIControlStateNormal];
        [self.planExtensionBtn setTitle:@"AI计划动态延长" forState:UIControlStateNormal];
        
        // 按钮1：重新生成
        // 按钮2：AI动态延长计划（需要校验是否有最近24小时内的体重记录，无则走体重更新流程。）
        
    } else {
        [self.reformulationBtn setTitle:@"查看上周报告>" forState:UIControlStateNormal];
        [self.planExtensionBtn setTitle:@"解锁本周计划" forState:UIControlStateNormal];
        
        // 按钮1：查看上周报告（跳转周报告页面）
        // 按钮2：AI动态解锁本周计划（需要校验是否有最近24小时内的体重记录，无则走体重更新流程。）
        
        if (model.currentPlan.type != 1) {
            User *user = [Login curLoginUser];
            if (user.healthInfo.bmi) {
                double bmi = user.healthInfo.bmi.doubleValue;
                if (bmi >= 28) {
                    // 按钮1：重新生成
                    // 按钮2：AI动态解锁本周计划（需要校验是否有最近24小时内的体重记录，无则走体重更新流程。）
                    
                    [self.reformulationBtn setTitle:@"重新制定" forState:UIControlStateNormal];
                    [self.planExtensionBtn setTitle:@"解锁本周计划" forState:UIControlStateNormal];
                }
            }
        }
    }
    
    
    MRKPlanCurrentPeriodModel *currentPeriod = model.currentPlan.currentPeriod;
    
    /// 文案逻辑
    if (currentPeriod.unStartDays > 0) {
        // 主文案：你已经unStartDays天没有开启计划啦。
        // 副文案：剩余30 - unStartDays天将自动结束，现在行动还来得及哦！
        
        self.remindLabel.text = [NSString stringWithFormat:@"你已经%ld天没有开启计划啦", currentPeriod.unStartDays];
        self.remarkLabel.text = [NSString stringWithFormat:@"剩余%ld天将自动结束，现在行动还来得及哦！", 30 - currentPeriod.unStartDays];;
        return;
    }
    
    
    ///计划类型 1-减脂减重 100-其他
    if (model.currentPlan.type == 1) {
        ///< 该周期的体重减少量（kg）
        double weightLoss = currentPeriod.weightLoss.doubleValue;
        ///< 计划减重（kg）
        double targetWeightLoss = currentPeriod.targetWeightLoss.doubleValue;
        
        
        if (!model.currentPlan.currentPeriod.isExecuted) {
            // 主文案：本周没有执行计划哦～，减重Nkg。
            // 副文案：离制定的目标已越来越远，现在回归训练
            
            self.remindLabel.text = [NSString stringWithFormat:@"第%ld周AI计划没有执行哦～，减重%@kg", currentPeriod.seq, currentPeriod.weightLoss];
            self.remarkLabel.text = @"离制定的目标已越来越远，现在回归训练";
            return;
        }
        
        if (weightLoss < 0) {
            // 减重负数 且有完成过任务：
            // 主文案：恭喜完成第N周AI计划，减重-Nkg，
            // 副文案：体重波动是身体的正常反应，别放弃继续努力~
            
            self.remindLabel.text = [NSString stringWithFormat:@"恭喜完成第%ld周AI计划，减重%@kg", currentPeriod.seq, currentPeriod.weightLoss];
            self.remarkLabel.text = @"体重波动是身体的正常反应，别放弃继续努力~";
            
        } else if (weightLoss == 0 ) {
            // 减重 0 且有完成过任务：
            // 主文案：恭喜完成第N周AI计划
            // 副文案：体重无变化，别急科学减重常有波动！
            
            self.remindLabel.text = [NSString stringWithFormat:@"恭喜完成第%ld周AI计划", currentPeriod.seq];
            self.remarkLabel.text = @"体重无变化，别急科学减重常有波动！";
            
        } else if (weightLoss > 0 && weightLoss < targetWeightLoss) {
            // 减重未达标（0 < 差值 < 目标值）且有完成过任务：
            // 主文案：恭喜完成第N周AI计划，减重Nkg
            // 副文案：科学减重常有波动，下周我们重新出发！
            
            self.remindLabel.text = [NSString stringWithFormat:@"恭喜完成第%ld周AI计划，减重%@kg", currentPeriod.seq, currentPeriod.weightLoss];
            self.remarkLabel.text = @"科学减重常有波动，下周我们重新出发！";
            
        } else if (weightLoss > targetWeightLoss + 1) {
            // 减重超标（>1kg）且有完成过任务：
            // 主文案：恭喜完成第N周AI计划，减重Nkg，
            // 副文案：速度超出健康范围，建议调整！
            
            self.remindLabel.text = [NSString stringWithFormat:@"恭喜完成第%ld周AI计划，减重%@kg", currentPeriod.seq, currentPeriod.weightLoss];
            self.remarkLabel.text = @"速度超出健康范围，建议调整！";
            
        } else {
            // 减重达标
            // 主文案：恭喜完成第N周AI计划，减重Nkg，
            // 副文案：
            
            self.remindLabel.text = [NSString stringWithFormat:@"恭喜完成第%ld周AI计划，减重%@kg", currentPeriod.seq, currentPeriod.weightLoss];
            self.remarkLabel.text = @"";
        }
        
    } else {
        
        User *user = [Login curLoginUser];
        if (user.healthInfo.bmi) {
            double bmi = user.healthInfo.bmi.doubleValue;
            if (bmi >= 28) {
                self.remindLabel.text = @"你的体重好像有点超标了";
                self.remarkLabel.text = @"体重偏高，建议更改运动目标重新制定计";
                
            } else if (bmi < 18.5) {
                self.remindLabel.text = @"你的体重偏低，不能再瘦啦～";
                self.remarkLabel.text = @"体重不足，建议适当降低运动强度和训练频率";
                
            } else {
                self.remindLabel.text = @"你的体重保持得非常棒";
                self.remarkLabel.text = @"行动的一小步是健康的一大步；";
                
            }
        } else {
            // BMI 无数据时的处理
            
            
        }
    }
}

- (void)addAllSubviews {
    [self.contentView addSubview:self.backGradientView];
    [self.backGradientView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsZero);
    }];
    ///修改渐变圆角
    MrkCornerMaskWithViewRadius(self.backGradientView, ViewRadiusMake(DHPX(60), DHPX(8), DHPX(8), DHPX(8)));
    
    [self.contentView addSubview:self.iconImageView];
    [self.iconImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentView.mas_top);
        make.left.equalTo(self.contentView.mas_left).offset(DHPX(11));
        make.size.mas_equalTo(CGSizeMake(DHPX(50), DHPX(48)));
    }];
    [self.contentView addSubview:self.titleLabel];
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.iconImageView.mas_right).offset(DHPX(10));
        make.centerY.equalTo(self.iconImageView);
    }];
    [self.contentView addSubview:self.dayLabel];
    [self.dayLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.titleLabel.mas_right);
        make.centerY.equalTo(self.titleLabel);
    }];
    
    ///
    [self.contentView addSubview:self.backContentView];
    [self.backContentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsMake(DHPX(58), DHPX(16), DHPX(8), DHPX(16)));
//        make.height.mas_equalTo(DHPX(134));
    }];
    [self.backContentView addSubview:self.remindLabel];
    [self.remindLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.backContentView.mas_top).offset(DHPX(22));
        make.centerX.equalTo(self.backContentView.mas_centerX);
    }];
    [self.backContentView addSubview:self.remarkLabel];
    [self.remarkLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.remindLabel.mas_bottom).offset(DHPX(5));
        make.centerX.equalTo(self.backContentView.mas_centerX);
        make.left.equalTo(self.backContentView.mas_left).offset(DHPX(12));
        make.right.equalTo(self.backContentView.mas_right).offset(-DHPX(12));
    }];
    
    [self.backContentView addSubview:self.reformulationBtn];
    [self.reformulationBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.remarkLabel.mas_bottom).offset(DHPX(18));
        make.right.equalTo(self.backContentView.mas_centerX).offset(-DHPX(8));
        make.size.mas_equalTo(CGSizeMake(DHPX(144), DHPX(40)));
        make.bottom.equalTo(self.backContentView.mas_bottom).offset(-DHPX(13));
    }];
    [self.backContentView addSubview:self.planExtensionBtn];
    [self.planExtensionBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.remarkLabel.mas_bottom).offset(DHPX(18));
        make.left.equalTo(self.backContentView.mas_centerX).offset(DHPX(8));
        make.size.mas_equalTo(CGSizeMake(DHPX(144), DHPX(40)));
        make.bottom.equalTo(self.backContentView.mas_bottom).offset(-DHPX(13));
    }];
}






- (MrkGradientView *)backGradientView {
    if (!_backGradientView) {
        _backGradientView = [[MrkGradientView alloc] init];
        _backGradientView.drawsGradientBackground = YES;
        _backGradientView.gradientStartPoint = CGPointMake(1, 0);
        _backGradientView.gradientEndPoint = CGPointMake(0, 1);
        _backGradientView.gradientLocations = @[@0, @1];
        _backGradientView.gradientLayerColors = @[(__bridge id)[UIColorHex(#FFD9FF) CGColor],
                                                  (__bridge id)[UIColorHex(#DDFAFF) CGColor]];
    }
    return _backGradientView;
}

- (UIImageView *)iconImageView {
    if (!_iconImageView) {
        _iconImageView = [[UIImageView alloc] init];
        _iconImageView.contentMode = UIViewContentModeScaleAspectFit;
        _iconImageView.clipsToBounds = YES;
        _iconImageView.image = [UIImage imageNamed:@"Mia_icon"];
    }
    return _iconImageView;
}

- (GradientLayerLabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[GradientLayerLabel alloc] init];
        _titleLabel.font = kSystem_Font_NoDHPX(WKDHPX(16));
        _titleLabel.gradientColors = @[
            [UIColor colorWithHexString:@"#17D2E3"],
            [UIColor colorWithHexString:@"#AA69FF"],
            [UIColor colorWithHexString:@"#FF8FB4"]
        ];
        _titleLabel.textAlignment = 0;
    }
    return _titleLabel;
}

- (UILabel *)dayLabel{
    if (!_dayLabel) {
        _dayLabel = [[UILabel alloc] init];
        _dayLabel.textColor = [UIColor colorWithHexString:@"#363A44"];
        _dayLabel.font = kSystem_Font_NoDHPX(WKDHPX(13));
    }
    return _dayLabel;
}

- (UIView *)backContentView{
    if (!_backContentView) {
        _backContentView = [[UIView alloc] init];
        _backContentView.backgroundColor = UIColor.whiteColor;
        _backContentView.layer.cornerRadius = 12.0f;
        _backContentView.layer.masksToBounds = YES;
    }
    return _backContentView;
}

- (GradientLayerLabel *)remindLabel {
    if (!_remindLabel) {
        _remindLabel = [[GradientLayerLabel alloc] init];
        _remindLabel.font = kMedium_Font_NoDHPX(WKDHPX(18));
        _remindLabel.gradientColors = @[
            [UIColor colorWithHexString:@"#17D2E3"],
            [UIColor colorWithHexString:@"#AA69FF"],
            [UIColor colorWithHexString:@"#FF8FB4"]
        ];
        _remindLabel.textAlignment = NSTextAlignmentCenter;
    }
    return _remindLabel;
}

- (UILabel *)remarkLabel{
    if (!_remarkLabel) {
        _remarkLabel = [[UILabel alloc] init];
        _remarkLabel.textColor = [UIColor colorWithHexString:@"#848A9B"];
        _remarkLabel.font = kSystem_Font_NoDHPX(WKDHPX(14));
        _remarkLabel.textAlignment = NSTextAlignmentCenter;
        _remarkLabel.numberOfLines = 0;
    }
    return _remarkLabel;
}

- (UIButton *)reformulationBtn{
    if(!_reformulationBtn){
        _reformulationBtn = [[UIButton alloc] init];
        [_reformulationBtn setTitleColor:[UIColor colorWithHexString:@"#B3B5B9"] forState:UIControlStateNormal];
        _reformulationBtn.titleLabel.font = kSystem_Font_NoDHPX(WKDHPX(14));
        _reformulationBtn.layer.cornerRadius = DHPX(40)/2;
        _reformulationBtn.layer.masksToBounds = YES;
        _reformulationBtn.layer.borderColor = UIColorHex(#B3B5B9).CGColor;
        _reformulationBtn.layer.borderWidth = 1.0f;
        [_reformulationBtn addTarget:self action:@selector(aiPlanMoreAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _reformulationBtn;
}

- (UIButton *)planExtensionBtn{
    if(!_planExtensionBtn){
        _planExtensionBtn = [[UIButton alloc] init];
        [_planExtensionBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        _planExtensionBtn.titleLabel.font = kSystem_Font_NoDHPX(WKDHPX(14));
        _planExtensionBtn.layer.cornerRadius = DHPX(40)/2;
        _planExtensionBtn.layer.masksToBounds = YES;
        [_planExtensionBtn az_setGradientBackgroundWithColors:@[UIColorHex(#17D2E3),
                                                                UIColorHex(#AA69FF),
                                                                UIColorHex(#FF8FB4),
                                                                UIColorHex(#FF8FB4)]
                                                    locations:nil
                                                   startPoint:CGPointMake(0, 0)
                                                     endPoint:CGPointMake(1, 0)];
        [_planExtensionBtn addTarget:self action:@selector(aiPlanMoreAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _planExtensionBtn;
}

@end
