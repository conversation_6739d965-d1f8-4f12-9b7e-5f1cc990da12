//
//  MRKDailyScheduleOverView.m
//  Student_IOS
//
//  Created by Jun<PERSON> on 2025/6/26.
//

#import "MRKDailyScheduleOverView.h"
#import "MRKLeastEatAlertView.h"
#import "MRKPopupManager.h"
#import "POP.h"


@interface MRKDailyScheduleOverView()
@property (nonatomic, strong) MRKDailyTrainItemView *kcalItemView;
@property (nonatomic, strong) MRKDailyTrainItemView *dietItemView;
@property (nonatomic, strong) MRKDailyTrainItemView *timeItemView;

@property (nonatomic, strong) MRKDailyTrainAboutView *statusView;
@property (nonatomic, strong) MRKDailyTrainAnalyzeView *analyzeView;

@property (nonatomic, strong) DailyTargetModel *targetModel;
@property (nonatomic, strong) CalendarModel *calendarModel;
@end

@implementation MRKDailyScheduleOverView

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        
        [self addSubview:self.kcalItemView];
        [self addSubview:self.dietItemView];
        [self addSubview:self.timeItemView];
        [self addSubview:self.statusView];
        [self addSubview:self.analyzeView];
        
        [self.kcalItemView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.mas_top).offset(DHPX(21));
            make.left.equalTo(self.mas_left).offset(DHPX(32));
            make.height.mas_equalTo(WKDHPX(30));
        }];
        
        [self.dietItemView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.kcalItemView.mas_bottom);
            make.left.equalTo(self.kcalItemView.mas_left);
            make.height.mas_equalTo(WKDHPX(30));
        }];
        
        [self.timeItemView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.dietItemView.mas_bottom);
            make.left.equalTo(self.kcalItemView.mas_left);
            make.height.mas_equalTo(WKDHPX(30));
        }];
        
        [self.statusView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.mas_top);
            make.right.equalTo(self.mas_right);
            make.size.mas_equalTo(CGSizeMake(DHPX(171), DHPX(103)));
        }];
        
        self.analyzeView.hidden = YES;
        [self.analyzeView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.statusView.mas_bottom).offset(DHPX(7));
            make.bottom.equalTo(self.mas_bottom).offset(-DHPX(8));
            make.left.equalTo(self.mas_left).offset(DHPX(16));
            make.right.equalTo(self.mas_right).offset(-DHPX(16));
            make.height.mas_equalTo(0);
        }];
    }
    return self;
}

- (void)layoutSubviews {
    [super layoutSubviews];
}


- (void)updateWithModel:(DailyTargetModel *)model current:(CalendarModel *)calendar {
    self.targetModel = model;
    self.calendarModel = calendar;
    
    ///重置状态为还可以吃
    self.statusView.overType = MRKOverViewTypeStillEdible;
    [self.statusView updateWithModel:model current:calendar];
    
    self.kcalItemView.model = model;
    self.timeItemView.model = model;
    self.dietItemView.model = model;
    
    ///显示分析逻辑 实际饮食热量 和 实际消耗卡路里
    BOOL dietCalories = model.dietCalories.doubleValue > 0;
    self.analyzeView.hidden = !dietCalories;
    [self.analyzeView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(dietCalories ? DHPX(36) : 0);
    }];
    [self setNeedsLayout];
    
    if (self.updateHeightBlock){
        self.updateHeightBlock(0);
    }
}

- (MRKDailyTrainItemView *)kcalItemView {
    if (!_kcalItemView) {
        _kcalItemView = [[MRKDailyTrainItemView alloc] init];
        _kcalItemView.trainType = MRKDailyTrainTypeKcal;
    }
    return _kcalItemView;
}
- (MRKDailyTrainItemView *)dietItemView {
    if (!_dietItemView) {
        _dietItemView = [[MRKDailyTrainItemView alloc] init];
        _dietItemView.trainType = MRKDailyTrainTypeDiet;
    }
    return _dietItemView;
}
- (MRKDailyTrainItemView *)timeItemView {
    if (!_timeItemView) {
        _timeItemView = [[MRKDailyTrainItemView alloc] init];
        _timeItemView.trainType = MRKDailyTrainTypeTime;
    }
    return _timeItemView;
}

- (MRKDailyTrainAboutView *)statusView {
    if (!_statusView) {
        _statusView = [[MRKDailyTrainAboutView alloc] init];
        _statusView.overType = MRKOverViewTypeStillEdible; ///默认还可以吃
    }
    return _statusView;
}

- (MRKDailyTrainAnalyzeView *)analyzeView {
    if (!_analyzeView) {
        _analyzeView = [[MRKDailyTrainAnalyzeView alloc] init];
        [_analyzeView addTarget:self action:@selector(analysisAction:) forControlEvents:UIControlEventTouchUpInside];
        _analyzeView.layer.cornerRadius = 8.0;
        _analyzeView.layer.masksToBounds = YES;
        [_analyzeView az_setGradientBackgroundWithColors:@[UIColorHexAlpha(#17D2E3, 0.1),
                                                           UIColorHexAlpha(#AA69FF, 0.1),
                                                           UIColorHexAlpha(#FF8FB4, 0.1)]
                                               locations:nil
                                              startPoint:CGPointMake(0, 0)
                                                endPoint:CGPointMake(1, 0)];
    }
    return _analyzeView;
}

- (void)analysisAction:(UIButton *)sender{
    if ([self.targetModel.targetDietCalories doubleValue] > 0) {
        sender.traceEventId = @"btn_sport_diet_analysis";
        [[MRKDailyLogic shared] jumpToDietSportAnalysis:self.calendarModel.date];
    } else {
        sender.traceEventId = @"btn_check_today_food_intake";
        [[MRKDailyLogic shared] targetDietCalories];
    }
}

/*
 // Only override drawRect: if you perform custom drawing.
 // An empty implementation adversely affects performance during animation.
 - (void)drawRect:(CGRect)rect {
 // Drawing code
 }
 */

@end






@interface MRKDailyTrainItemView ()
@property (nonatomic, strong) UIImageView *iconImage;
@property (nonatomic, strong) UILabel *dataLab;
@property (nonatomic, strong) UILabel *dataTargetLab;
@end

@implementation MRKDailyTrainItemView

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        
        [self addSubview:self.iconImage];
        [self addSubview:self.dataLab];
        [self addSubview:self.dataTargetLab];
    }
    return self;
}

- (void)layoutSubviews {
    [super layoutSubviews];
    
    [self.iconImage mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.mas_centerY);
        make.left.equalTo(self.mas_left);
        make.size.mas_equalTo(CGSizeMake(DHPX(20), DHPX(20)));
    }];
    [self.dataLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.mas_centerY);
        make.left.equalTo(self.iconImage.mas_right).offset(2);
    }];
    [self.dataTargetLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.mas_centerY);
        make.left.equalTo(self.dataLab.mas_right).offset(4);
        make.right.equalTo(self.mas_right);
    }];
}

- (void)setTrainType:(MRKDailyTrainType)trainType {
    _trainType = trainType;
    switch (trainType) {
        case MRKDailyTrainTypeKcal:
            self.iconImage.image = [UIImage imageNamed:@"icon_target_kcal"];
            self.dataLab.text = @"--";
            self.dataTargetLab.text = @"/--千卡";
            break;
        case MRKDailyTrainTypeDiet:
            self.iconImage.image = [UIImage imageNamed:@"icon_target_diet"];
            self.dataLab.text = @"--";
            self.dataTargetLab.text = @"/--千卡";
            break;
        case MRKDailyTrainTypeTime:
            self.iconImage.image = [UIImage imageNamed:@"icon_target_time"];
            self.dataLab.text = @"--";
            self.dataTargetLab.text = @"/--分钟";
            break;
        default:
            break;
    }
}

- (void)setModel:(DailyTargetModel *)model {
    _model = model;
    
    switch (self.trainType) {
        case MRKDailyTrainTypeKcal:{
            self.iconImage.image = [UIImage imageNamed:@"icon_target_kcal"];
            
            NSString *calorie = ([model.calorie isNotBlank] && model.calorie.floatValue > 0) ? [NSString convertDecimalNumber:model.calorie num:1] : @"--";
            NSString *targetCalorie = ([model.targetCalorie isNotBlank] && model.targetCalorie.floatValue > 0)  ? [NSString convertDecimalNumber:model.targetCalorie num:1] : @"--";;
            self.dataLab.text = calorie;
            self.dataTargetLab.text = [NSString stringWithFormat:@"/%@千卡", targetCalorie];
        } break;
        case MRKDailyTrainTypeDiet:{
            self.iconImage.image = [UIImage imageNamed:@"icon_target_diet"];
            
            NSString *dietCalories = ([model.dietCalories isNotBlank] && model.dietCalories.floatValue > 0) ? [NSString stringWithFormat:@"%.1f", model.dietCalories.doubleValue] : @"--";
            NSString *targetDietCalories = ([model.targetDietCalories isNotBlank] && model.targetDietCalories.floatValue > 0)  ? [NSString stringWithFormat:@"%.1f", model.targetDietCalories.doubleValue] : @"--";
            self.dataLab.text = dietCalories;
            self.dataTargetLab.text = [NSString stringWithFormat:@"/%@千卡", targetDietCalories];
        } break;
        case MRKDailyTrainTypeTime:{
            self.iconImage.image = [UIImage imageNamed:@"icon_target_time"];
            
            NSString *time = ([model.sportTime isNotBlank] && model.sportTime.floatValue > 0) ? [NSString stringWithFormat:@"%.0f", floor(model.sportTime.doubleValue / 60.0)] : @"--";
            NSString *sportTargetTime = ([model.sportTargetTime isNotBlank] && model.sportTargetTime.floatValue >0) ?  [NSString stringWithFormat:@"%.0f", floor(model.sportTargetTime.doubleValue / 60.0)]: @"--";
            self.dataLab.text = time;
            self.dataTargetLab.text = [NSString stringWithFormat:@"/%@分钟", sportTargetTime];;
        } break;
        default:
            break;
    }
}


- (UIImageView *)iconImage {
    if (!_iconImage) {
        _iconImage = [[UIImageView alloc] init];
        _iconImage.contentMode = UIViewContentModeScaleAspectFit;
        _iconImage.clipsToBounds = YES;
    }
    return _iconImage;
}
- (UILabel *)dataLab {
    if (!_dataLab) {
        _dataLab = [UILabel new];
        _dataLab.font = BebasFont_Bold_NoDHPX(WKDHPX(20));
        _dataLab.textColor = UIColorHex(#363A44);
        _dataLab.text = @"--";
    }
    return _dataLab;
}
- (UILabel *)dataTargetLab {
    if (!_dataTargetLab) {
        _dataTargetLab = [UILabel new];
        _dataTargetLab.font = BebasFont_Bold_NoDHPX(WKDHPX(12));
        _dataTargetLab.textColor = UIColorHex(#848A9B);
        _dataTargetLab.text = @"/--";
    }
    return _dataTargetLab;
}
@end



@interface MRKDailyTrainAboutView ()
@property (nonatomic, strong) UIImageView *progressImageView;   // 进度图
@property (nonatomic, strong) UIImageView *lockImageView; // 锁图标

@property (nonatomic, strong) UIButton *tipsLab; // 提示语
@property (nonatomic, strong) UILabel *dataLab; // 数据主值
@property (nonatomic, strong) UILabel *dataTargetLab;  // 目标值

@property (nonatomic, strong) UIButton *switchBtn;    ///< 切换按钮
@property (nonatomic, strong) UIButton *checkBtn;     ///< 查看按钮
///<
///<
@property (nonatomic, strong) DailyTargetModel *model;
@property (nonatomic, strong) CalendarModel *calendar;
@end

@implementation MRKDailyTrainAboutView

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        
        [self initUI];
    }
    return self;
}

- (void)initUI {
    self.switchBtn.hidden = YES;
    self.lockImageView.hidden = YES;
    self.checkBtn.hidden = YES;
    
    [self addSubview:self.progressImageView];
    [self addSubview:self.switchBtn];
    [self addSubview:self.tipsLab];
    [self addSubview:self.dataLab];
    [self addSubview:self.dataTargetLab];
    [self addSubview:self.lockImageView];
    [self addSubview:self.checkBtn];
}

- (void)layoutSubviews {
    [super layoutSubviews];
    
    [self.progressImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsMake(DHPX(15), 0, DHPX(10), DHPX(34)));
    }];
    
    [self.switchBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.mas_top).offset(DHPX(12));
        make.right.equalTo(self.mas_right).offset(-DHPX(16));
        make.size.mas_equalTo(CGSizeMake(DHPX(40), DHPX(20)));
    }];
    
    [self.tipsLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.progressImageView.mas_top).offset(DHPX(25));
        make.centerX.equalTo(self.progressImageView.mas_centerX);
    }];
    
    [self.dataLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.tipsLab.mas_bottom);
        make.centerX.equalTo(self.progressImageView.mas_centerX);
    }];
    
    [self.dataTargetLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.dataLab.mas_bottom);
        make.centerX.equalTo(self.progressImageView.mas_centerX);
    }];
    
    [self.lockImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.progressImageView.mas_bottom).offset(-DHPX(2));
        make.centerX.equalTo(self.progressImageView.mas_centerX);
        make.size.mas_equalTo(CGSizeMake(DHPX(30), DHPX(30)));
    }];
    
    [self.checkBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.progressImageView.mas_bottom);
        make.centerX.equalTo(self.progressImageView.mas_centerX);
        make.size.mas_equalTo(CGSizeMake(DHPX(68), DHPX(26)));
    }];
}

- (void)setLabelAnimation:(UILabel *)label andToValue:(NSNumber *)toValue{
    NSString *textStr = label.text;
    if (!toValue.stringValue){
        label.text = @"--";
        return;
    }
    
    ///
    if ([textStr isEqualToString:@"--"]){
        textStr = @"0";
    }

    POPBasicAnimation *anim = [POPBasicAnimation animation];
    anim.duration = 0.5;
    anim.timingFunction = [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionEaseInEaseOut];
    anim.property = [POPAnimatableProperty propertyWithName:@"com.merit.number" initializer:^(POPMutableAnimatableProperty *prop) {
        prop.readBlock = ^(id obj, CGFloat values[]) {
            values[0] = [[obj description] floatValue];
        };
        prop.writeBlock = ^(id obj, const CGFloat values[]) {
            if ([obj isKindOfClass:[UILabel class]]) {
                UILabel *label = (UILabel *)obj;
                NSString *text = text = [NSString stringWithFormat:@"%.1f", values[0]];;
                label.text = text;
            }
        };
        prop.threshold = 0.1;
    }];
    anim.fromValue = [NSNumber numberWithString:textStr];
    anim.toValue = toValue;
    [label pop_addAnimation:anim forKey:@"counting"];
}

- (void)updateWithModel:(DailyTargetModel *)model current:(CalendarModel *)calendar {
    self.model = model;
    self.calendar = calendar;

    // /1. 判断是否设置过饮食目标
    BOOL hasDietTarget = ([model.targetDietCalories doubleValue] > 0);

    ///< 目标热量缺口
    BOOL targetCalorieDeficit = ([model.targetCalorieDeficit doubleValue] > 0);
    self.switchBtn.hidden = !targetCalorieDeficit;
    
    // 2. UI重置
    self.tipsLab.hidden = NO;
    self.dataLab.hidden = !hasDietTarget;
    self.dataTargetLab.hidden = !hasDietTarget;
    self.lockImageView.hidden = YES;
    self.checkBtn.hidden = YES;

    // 3. 未设置饮食目标时的处理
    if (!hasDietTarget) {
        self.progressImageView.image = [UIImage imageNamed:@"daily_plan_status_gary"];
        [self.tipsLab setTitle:@"还可以吃 " forState:UIControlStateNormal];
        if (calendar.timeReference.intValue == 2) {
            /// 今日未设置目标，显示查看按钮
            self.checkBtn.hidden = NO;
        } else {
            /// 过去未设置目标，显示锁
            self.lockImageView.hidden = NO;
        }
        return;
    }

    // 4. 已设置饮食目标，显示主内容
    self.tipsLab.hidden = NO;
    self.dataLab.hidden = NO;
    self.dataTargetLab.hidden = NO;
    self.lockImageView.hidden = YES;
    self.checkBtn.hidden = YES;

    // 5. 根据 overType 展示不同内容
    switch (self.overType) {
        case MRKOverViewTypeStillEdible: {
            // 还可以吃
            self.dataTargetLab.text = [NSString stringWithFormat:@"预算%.1f千卡", model.targetDietCalories.floatValue];
            double leastEatCalorie = model.targetDietCalories.floatValue - model.dietCalories.floatValue + model.calorie.floatValue;
            [self setLabelAnimation:self.dataLab andToValue:@(leastEatCalorie)];
            if (leastEatCalorie >= [[MRKDailyLogic shared] leastEatCalorieSepYellow]) {
                // 蓝色，正常可吃
                self.progressImageView.image = [UIImage imageNamed:@"daily_plan_status_blue"];
                [self.tipsLab setTitle:@"还可以吃 " forState:UIControlStateNormal];
//                self.dataLab.text = [NSString stringWithFormat:@"%.1f", leastEatCalorie];
            } else if (leastEatCalorie < [[MRKDailyLogic shared] leastEatCalorieSepRed]) {
                // 红色，超标
                self.progressImageView.image = [UIImage imageNamed:@"daily_plan_status_red"];
                [self.tipsLab setTitle:@"多吃了 " forState:UIControlStateNormal];
//                self.dataLab.text = [NSString stringWithFormat:@"%.1f", -leastEatCalorie];
            } else {
                // 黄色，轻微超标
                self.progressImageView.image = [UIImage imageNamed:@"daily_plan_status_yellow"];
                [self.tipsLab setTitle:@"多吃了 " forState:UIControlStateNormal];
//                self.dataLab.text = [NSString stringWithFormat:@"%.1f", -leastEatCalorie];
            }
        }
            break;
        case MRKOverViewTypeCalorieDeficit: {
            // 热量缺口
            [self.tipsLab setTitle:@"热量缺口 " forState:UIControlStateNormal];
//            self.dataLab.text = [NSString stringWithFormat:@"%.1f", model.calorieDeficit.floatValue];
            [self setLabelAnimation:self.dataLab andToValue:[NSNumber numberWithString:model.calorieDeficit]];
            
            self.dataTargetLab.text = [NSString stringWithFormat:@"目标%.1f千卡", model.targetCalorieDeficit.floatValue];
            double leastEatCalorie = model.calorieDeficit.floatValue;
            if (leastEatCalorie < 0) {
                ///当前热量缺口小于0，圈为红色；（变胖）
                self.progressImageView.image = [UIImage imageNamed:@"daily_plan_status_red"];
            } else if (leastEatCalorie <= model.targetCalorieDeficit.floatValue) {
                ///当前缺口等于小于目标热量缺口，圈为黄色。（保持）
                self.progressImageView.image = [UIImage imageNamed:@"daily_plan_status_yellow"];
            } else {
                ///当前缺口等于大于目标热量缺口，圈为蓝色
                self.progressImageView.image = [UIImage imageNamed:@"daily_plan_status_blue"];
            }
        }
            break;
        default:
            break;
    }
}

#pragma mark - 懒加载
- (UIImageView *)progressImageView {
    if (!_progressImageView) {
        _progressImageView = [[UIImageView alloc] init];
        _progressImageView.contentMode = UIViewContentModeScaleAspectFit;
        _progressImageView.clipsToBounds = YES;
        _progressImageView.image = [UIImage imageNamed:@"daily_plan_status_red"];
    }
    return _progressImageView;
}

- (UIButton *)tipsLab {
    if (!_tipsLab) {
        _tipsLab = [[UIButton alloc] init];
        [_tipsLab setTitle:@"还可以吃 " forState:UIControlStateNormal];
        _tipsLab.titleLabel.font = kSystem_Font_NoDHPX(WKDHPX(11));
        [_tipsLab setTitleColor:[UIColor colorWithHexString:@"#B3B5B9"] forState:UIControlStateNormal];
        [_tipsLab setImage:[UIImage imageNamed:@"icon_target_more"] forState:UIControlStateNormal];
        _tipsLab.semanticContentAttribute = UISemanticContentAttributeForceRightToLeft;
        [_tipsLab addTarget:self action:@selector(dietAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _tipsLab;
}

- (void)dietAction:(UIButton *)sender{
    sender.traceEventId = @"btn_check_today_food_intake";
    ///MARK:—— 计算热量规则弹窗
    dispatch_async(dispatch_get_main_queue(), ^{
        MRKLeastEatAlertView *alert = [[MRKLeastEatAlertView alloc] initWithAnimationStyle:MRKActionAlertViewTransitionStyleFade];
        alert.opaquess = 0.6;
        if (self.overType ==  MRKOverViewTypeStillEdible) {
            alert.alertType = MRKOverViewAlertTypeStillEdible;
        }
        if (self.overType ==  MRKOverViewTypeCalorieDeficit) {
            alert.alertType = MRKOverViewAlertTypeCalorieDeficit;
        }
        [[MRKPopupManager sharedInstance] showAlertView:alert level:MRKPopupViewLevelHeight callback:nil];
    });
}

- (UILabel *)dataLab {
    if (!_dataLab) {
        _dataLab = [UILabel new];
        _dataLab.font = BebasFont_Bold_NoDHPX(WKDHPX(30));
        _dataLab.textColor = UIColorHex(#363A44);
        _dataLab.text = @"300.0";
    }
    return _dataLab;
}
- (UILabel *)dataTargetLab {
    if (!_dataTargetLab) {
        _dataTargetLab = [UILabel new];
        _dataTargetLab.font = BebasFont_Bold_NoDHPX(WKDHPX(12));
        _dataTargetLab.textColor = UIColorHex(#848A9B);
        _dataTargetLab.text = @"目标400.0千卡";
    }
    return _dataTargetLab;
}
- (UIImageView *)lockImageView {
    if (!_lockImageView) {
        _lockImageView = [[UIImageView alloc] init];
        _lockImageView.contentMode = UIViewContentModeScaleAspectFit;
        _lockImageView.clipsToBounds = YES;
        _lockImageView.image = [UIImage imageNamed:@"daily_diet_lock_icon@2x"];
    }
    return _lockImageView;
}
- (UIButton *)switchBtn {
    if (!_switchBtn) {
        _switchBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_switchBtn setImage:[UIImage imageNamed:@"daily_exchange_icon"] forState:UIControlStateNormal];
        _switchBtn.backgroundColor = UIColorHex(#F3F5F9);
        _switchBtn.layer.cornerRadius = DHPX(20)/2;
        _switchBtn.layer.masksToBounds = YES;
        [_switchBtn addTarget:self action:@selector(switchOverViewType:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _switchBtn;
}
- (UIButton *)checkBtn {
    if (!_checkBtn) {
        _checkBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_checkBtn setTitle:@"点击查看" forState:UIControlStateNormal];
        _checkBtn.titleLabel.font = kSystem_Font_NoDHPX(WKDHPX(13));
        [_checkBtn setTitleColor:UIColorHex(#16D2E3) forState:UIControlStateNormal];
        _checkBtn.backgroundColor = UIColorHex(#E7FAFC);
        _checkBtn.layer.cornerRadius = DHPX(26)/2;
        _checkBtn.layer.masksToBounds = YES;
        [_checkBtn addTarget:self action:@selector(setupAIPlanMessage:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _checkBtn;
}

///切换
- (void)switchOverViewType:(id)sender {
    if (self.overType ==  MRKOverViewTypeStillEdible) {
        self.overType = MRKOverViewTypeCalorieDeficit;
    } else {
        self.overType = MRKOverViewTypeStillEdible;
    }
    ///刷新数据
    [self updateWithModel:self.model current:self.calendar];
}

- (void)setupAIPlanMessage:(UIButton *)sender {
    sender.traceEventId = @"btn_diet_intake";
    // 预算热量
    [[MRKDailyLogic shared] targetDietCalories];
}

@end






@interface MRKDailyTrainAnalyzeView ()
@property (nonatomic, strong) UIImageView *iconImageView;
@property (nonatomic, strong) GradientLayerLabel *titleLabel;
@property (nonatomic, strong) UIImageView *arrowImageView;
@end

@implementation MRKDailyTrainAnalyzeView

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        
        [self addSubview:self.iconImageView];
        [self addSubview:self.titleLabel];
        [self addSubview:self.arrowImageView];
    }
    return self;
}

- (void)layoutSubviews {
    [super layoutSubviews];
    [self.iconImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.mas_centerY);
        make.left.equalTo(self.mas_left).offset(DHPX(16));
        make.size.mas_equalTo(CGSizeMake(DHPX(16), DHPX(16)));
    }];
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.mas_centerY);
        make.left.equalTo(self.iconImageView.mas_right).offset(DHPX(4));
    }];
    [self.arrowImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.mas_centerY);
        make.right.equalTo(self.mas_right).offset(-DHPX(16));
        make.size.mas_equalTo(CGSizeMake(DHPX(16), DHPX(16)));
    }];
}

- (UIImageView *)iconImageView {
    if (!_iconImageView) {
        _iconImageView = [[UIImageView alloc] init];
        _iconImageView.contentMode = UIViewContentModeScaleAspectFit;
        _iconImageView.clipsToBounds = YES;
        _iconImageView.image = [UIImage imageNamed:@"daily_plan_analyze_icon"];
    }
    return _iconImageView;
}
- (GradientLayerLabel *)titleLabel {
    if (_titleLabel) return _titleLabel;
    _titleLabel = [[GradientLayerLabel alloc] init];
    _titleLabel.font = kMedium_Font_NoDHPX(WKDHPX(14));
    _titleLabel.gradientColors = @[
        [UIColor colorWithHexString:@"#17D2E3"],
        [UIColor colorWithHexString:@"#AA69FF"],
        [UIColor colorWithHexString:@"#FF8FB4"]
    ];
    _titleLabel.textAlignment = 0;
    _titleLabel.text = @"AI运动饮食分析";
    return _titleLabel;
}
- (UIImageView *)arrowImageView {
    if (!_arrowImageView) {
        _arrowImageView = [[UIImageView alloc] init];
        _arrowImageView.contentMode = UIViewContentModeScaleAspectFit;
        _arrowImageView.clipsToBounds = YES;
        _arrowImageView.image = [UIImage imageNamed:@"daily_plan_analyze_arrow_icon"];
    }
    return _arrowImageView;
}
@end



