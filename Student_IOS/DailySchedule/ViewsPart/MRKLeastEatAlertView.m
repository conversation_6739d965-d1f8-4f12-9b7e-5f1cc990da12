//
//  MRKLeastEatAlertView.m
//  Student_IOS
//
//  Created by merit on 2025/4/23.
//

#import "MRKLeastEatAlertView.h"
#import "MRKPopupManager.h"

@interface MRKLeastEatAlertView()<MRKActionAlertViewDelegate>
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *contentLabel;
@property (nonatomic, strong) UIView *lineView;
@property (nonatomic, strong) UILabel *tipLabel;
@property (nonatomic, strong) UIButton *confirmBtn;
@end

@implementation MRKLeastEatAlertView

#pragma mark - 布局
- (void)layoutContainerView{
    self.isAutoHidden = NO;
    [self.containerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_equalTo(self.containerView.superview.mas_centerY);
        make.centerX.mas_equalTo(self.containerView.superview.mas_centerX);
        make.width.mas_equalTo(DHPX(305));
    }];
}

- (void)actionAlertViewDidSelectBackGroundView{
    @weakify(self)
    [self dismissAnimated:YES complete:^{
        @strongify(self);
        [[MRKPopupManager sharedInstance] dismissAlertView:self callback:nil];
    }];
}

- (void)setupContainerViewAttributes{
    self.containerView.cornerRadius = WKDHPX(8);
}

- (void)setupContainerSubViews{
    [self.containerView addSubview:self.titleLabel];
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.containerView).offset(WKDHPX(20));
        make.left.equalTo(self.containerView).offset(WKDHPX(18));
        make.right.equalTo(self.containerView).offset(WKDHPX(-18));
    }];
    
    [self.containerView addSubview:self.contentLabel];
    [self.contentLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.titleLabel.mas_bottom).offset(WKDHPX(24));
        make.left.equalTo(self.containerView).offset(WKDHPX(18));
        make.right.equalTo(self.containerView).offset(WKDHPX(-18));
    }];

    [self.containerView addSubview:self.lineView];
    [self.lineView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentLabel.mas_bottom).offset(WKDHPX(16));
        make.left.equalTo(self.containerView).offset(WKDHPX(22));
        make.right.equalTo(self.containerView).offset(WKDHPX(-22));
        make.height.mas_equalTo(0.5);
    }];
    
    [self.containerView addSubview:self.tipLabel];
    [self.tipLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.lineView.mas_bottom).offset(WKDHPX(16));
        make.left.equalTo(self.containerView).offset(WKDHPX(18));
        make.right.equalTo(self.containerView).offset(WKDHPX(-18));
    }];
    
    [self.containerView addSubview:self.confirmBtn];
    [self.confirmBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.tipLabel.mas_bottom).offset(WKDHPX(8));
        make.left.equalTo(self.containerView).offset(WKDHPX(20));
        make.right.equalTo(self.containerView).offset(WKDHPX(-20));
        make.height.mas_equalTo(WKDHPX(44));
        make.bottom.equalTo(self.containerView).offset(WKDHPX(-16));
    }];
    self.delegate = self;
}

#pragma mark - 内容根据alertType区分

- (void)setAlertType:(MRKOverViewAlertType)alertType {
    _alertType = alertType;
    switch (alertType) {
        case MRKOverViewAlertTypeStillEdible: {
            self.titleLabel.text = @"今日还可以吃的热量值=\n预算热量-已摄入热量＋运动消耗";
            self.contentLabel.text = @"预算热量：根据你最新体重及活动系数计算。\n\n运动消耗：根据平台运动消耗计算。\n\n已摄入热量：根据饮食记录计算。";
            self.tipLabel.text = @"温馨提示：\n1. 预算热量会实时根据体重和活动系数动态调整；\n2. {AI计划用户的预算热量更新将在下周计划开启时自动同步至训练方案}\n（当前数据仍按更改前计算）";
            break;
        }
        case MRKOverViewAlertTypeCalorieDeficit: {
            self.titleLabel.text = @"热量缺口=\n每日总能量消耗(TDEE)-实际饮食摄入+实际运动消耗";
            self.contentLabel.text = @"每日总能量消耗：根据最新的BMR和活动系数计算。\n\n实际饮食摄入：指每日由APP记录的饮食总热量。\n\n实际运动消耗：指每日在APP上产生的运动消耗数据。";
            self.tipLabel.text = @"温馨提示：\n1. 目标热量缺口会根据体重和活动系数动态调整；\n2. {AI计划用户的目标热量缺口更新将在下周计划开启时自动同步至训练方案}\n（当前数据仍按更改前计算）";
            break;
        }
        // 可根据需求继续扩展其他类型
        default: {
            break;
        }
    }
}

#pragma mark - 懒加载

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [UILabel new];
        _titleLabel.font = kMedium_Font_NoDHPX(WKDHPX(16));
        _titleLabel.textColor = [UIColor colorWithHexString:@"#363A44"];
        _titleLabel.numberOfLines = 0;
        _titleLabel.textAlignment = NSTextAlignmentCenter;
    }
    return _titleLabel;
}

- (UILabel *)contentLabel {
    if (!_contentLabel) {
        _contentLabel = [UILabel new];
        _contentLabel.font = kSystem_Font_NoDHPX(WKDHPX(13));
        _contentLabel.textColor = [UIColor colorWithHexString:@"#363A44"];
        _contentLabel.numberOfLines = 0;
    }
    return _contentLabel;
}

- (UIView *)lineView {
    if (!_lineView) {
        _lineView = [[UIView alloc] init];
        _lineView.backgroundColor = [UIColor colorWithHexString:@"#D8D8D8"];
    }
    return _lineView;
}

- (UILabel *)tipLabel {
    if (!_tipLabel) {
        _tipLabel = [UILabel new];
        _tipLabel.font = kSystem_Font_NoDHPX(WKDHPX(12));
        _tipLabel.textColor = [UIColor colorWithHexString:@"#848A9B"];
        _tipLabel.numberOfLines = 0;
    }
    return _tipLabel;
}

- (UIButton *)confirmBtn {
    if (!_confirmBtn) {
        _confirmBtn = [[UIButton alloc] init];
        [_confirmBtn setBackgroundColor:[UIColor colorWithHexString:@"#17D2E3"]];
        [_confirmBtn setTitle:@"好的" forState:UIControlStateNormal];
        [_confirmBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        _confirmBtn.titleLabel.font = kMedium_Font_NoDHPX(WKDHPX(14));
        _confirmBtn.cornerRadius = WKDHPX(44)/2;
        [_confirmBtn addTarget:self action:@selector(ensureAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _confirmBtn;
}

#pragma mark - 按钮事件

- (void)ensureAction:(UIButton *)button{
    [self actionAlertViewDidSelectBackGroundView];
}

@end
