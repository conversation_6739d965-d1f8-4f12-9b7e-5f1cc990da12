//
//  MRKDailyLogic.h
//  Student_IOS
//
//  Created by merit on 2025/4/22.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface MRKDailyLogic : NSObject

+ (nullable instancetype)shared;

///还可以吃分割黄色 0
- (int)leastEatCalorieSepYellow;

///还可以吃分割红色 -800
- (int)leastEatCalorieSepRed;

///喝水最少可选 100
- (int)drinkSelectLeast;

///喝水最多可选 4000
- (int)drinkSelectMost;

/// 权益领取
//- (void)benefitDistribution;

/// 设置目标饮食热量
- (void)targetDietCalories;

/// 进入活动类型选择页
- (void)jumpSelectActivityTypeCodePage;

/// 设置目标饮水量
- (void)targetWaterIntake:(NSString *)amount;

/// 进入运动饮食分析
- (void)jumpToDietSportAnalysis:(NSString *)date;

- (void)showVipAlert:(int)source normalPageId:(NSString *)normalPageId vipPageId:(NSString *)vipPageId;

/// 跳转到h5的饮食运动分析页面
- (void)toDietSportAnalysis:(BOOL)firstIntoPlanReport date:(NSString *)date;

/// 运动饮食分析——重新分析
- (void)jumpToDietSportReAnalysis:(NSString *)date;

/// 进入饮食记录
- (void)jumpToDietRecord:(NSString *)date index:(NSInteger)index;

/// 进入图片识别
- (void)jumpToImageRecognition:(NSString *)date;

/// 进入文本识别
- (void)jumpToTextRecognition:(NSString *)date;

/// 进入饮水记录
- (void)jumpToDrinkRecord:(NSString *)date;

/// 添加一次喝水记录（默认 200ml）
- (void)addPerDrinkRecord:(NSString *)date;

/// 添加喝水记录
- (void)addDrinkRecord:(int)amount date:(NSString *)date;

@end

NS_ASSUME_NONNULL_END
