//
//  MRKDeviceURLRequest.m
//  Student_IOS
//
//  Created by MacPro on 2023/3/29.
//

#import "MRKDeviceURLRequest.h"
#import "MRKNetworkCache.h"

@implementation MRKDeviceURLRequest

///设备连接/绑定
+ (void)requestDeviceConnect:(NSDictionary *)parameters success:(successData)success fail:(failedData)fail{
    NSString *url = @"/app/device-user-rel/connectionDevice";
    [MRKBaseRequest mrkRequestType:YTKRequestMethodPOST
                               url:url
                           andParm:parameters
                      notShowError:YES
          completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"%@:response:%@" , url,request.responseObject);
        success(request.responseObject);
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        fail(request.error);
        NSLog(@"%@:error:%@" , url,request.error.localizedDescription);
    }];
}

///设备解绑
+ (void)requestDeviceDisConnect:(NSDictionary *)parameters success:(successData)success fail:(failedData)fail{
    NSString *url = @"/app/device-user-rel/disconnectionDevice";
    [MRKBaseRequest mrkRequestType:YTKRequestMethodPUT
                               url:url
                           andParm:parameters
          completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"%@:response:%@", url, request.responseObject);
        id data = [request.responseObject valueForKeyPath:@"data"];
        if([data boolValue]) {
            success(data);
        } else {
            fail(data);
        }
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        fail(request.error);
        NSLog(@"%@:error:%@" , url,request.error.localizedDescription);
    }];
}

///搜索设备
+ (void)requestSearchDeviceInfo:(NSDictionary *)parameters success:(successData)success fail:(failedData)fail{
    NSLog(@"requestSearchDeviceInfo /app/product-model/searchProductModel para:%@ ", parameters);
    [MRKBaseRequest mrkRequestType:YTKRequestMethodPOST
                               url:@"/app/product-model/searchProductModel"
                           andParm:parameters
                      notShowError:YES
          completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"para:%@ :response:%@" ,parameters, request.responseObject);
        NSDictionary *data = request.responseObject;
        if(![[data allKeys] containsObject:@"data"]) {
            fail(data);
            return;
        }
        
        if([data objectForKey:@"data"]) {
            success(data);
        } else {
            fail(data);
        }
        
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        fail(request.error);
        NSLog(@"error:%@",request.error.localizedDescription);
    } ];
}

///主动重连设备获取
+ (void)requestAutoConnectDevice:(NSDictionary *)parameters success:(successData)success fail:(failedData)fail{
    NSString *url = @"/app/device-user-rel/listFinalConnectionDevices";
    [MRKBaseRequest mrkRequestType:YTKRequestMethodPOST
                               url:url
                           andParm:parameters
          completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"%@:response:%@" ,url ,request.responseObject);
        id data = [request.responseObject valueForKeyPath:@"data"];
        NSArray *array = [NSArray modelArrayWithClass:[MRKDeviceModel class] json:data];
        success(array);
        
        ///缓存数据
//        [MRKNetworkCache setHttpCache:data URL:AutoConnectDeviceInfoKey parameters:nil];
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        fail(request.error);
        NSLog(@"%@:error:%@" , url,request.error.localizedDescription);
    }];
}

///获取我的设备列表
+ (void)requestMyDevice:(NSDictionary *)parameters success:(successData)success fail:(failedData)fail{
    NSString *url = @"/app/device-user-rel/pageMyDevices";
    [MRKBaseRequest mrkRequestType:YTKRequestMethodPOST
                               url:url
                           andParm:parameters
          completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"%@:response:%@" , url,request.responseObject);
        id records = [request.responseObject valueForKeyPath:@"data.records"];
        NSArray *array = [NSArray modelArrayWithClass:[MRKDeviceModel class] json:records];
        success(array);
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        fail(request.error);
        NSLog(@"%@:error:%@" , url,request.error.localizedDescription);
    }];
}

///切换设备
+ (void)requestSwitchDevice:(NSDictionary *)parameters success:(successData)success fail:(failedData)fail {
    NSString *url = @"/app/product-model/switchProduct";
    [MRKBaseRequest mrkRequestType:YTKRequestMethodGET
                               url:url
                           andParm:parameters
          completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"%@:response:%@" , url,request.responseObject);
        NSDictionary *data = request.responseObject;
        success(data);
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        fail(request.error);
        NSLog(@"%@:error:%@" , url,request.error.localizedDescription);
    }];
}

///更新设备别名/备注
+ (void)requestUpdateDeviceAlias:(NSDictionary *)parameters success:(successData)success fail:(failedData)fail {
    NSString *url = @"/app/device-user-rel/updateDeviceAlias";
    [MRKBaseRequest mrkRequestType:YTKRequestMethodPUT
                               url:url
                           andParm:parameters
          completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"%@:response:%@" , url,request.responseObject);
        NSDictionary * data = request.responseObject;
        if([[data objectForKey:@"data"] boolValue]) {
            success(data);
        } else {
            fail(data);
        }
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        fail(request.error);
        NSLog(@"%@:error:%@" , url,request.error.localizedDescription);
    }];
}

///检查Ota弹窗
+(void)requestDeviceOTA:(NSDictionary *)parameters success:(successData)success fail:(failedData)fail {
    NSString *url = @"/app/product-model-firmware/getLatestFirmwareVersion";
    [MRKBaseRequest mrkRequestType:YTKRequestMethodGET
                               url:url
                           andParm:parameters
          completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"%@:response:%@" , url,request.responseObject);
        NSDictionary * data = request.responseObject;
        success(data);
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        fail(request.error);
        NSLog(@"%@:error:%@" , url,request.error.localizedDescription);
    }];
}

///记录Ota弹窗弹出
+(void)requestDeviceOTAAlert:(NSDictionary *)parameters success:(successData)success fail:(failedData)fail {
    NSString *url = @"/app/product-model-firmware/setLog";
    [MRKBaseRequest mrkRequestType:YTKRequestMethodPOST
                               url:url
                           andParm:parameters
          completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"%@:response:%@" , url,request.responseObject);
        NSDictionary *data = request.responseObject;
        success(data);
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        fail(request.error);
        NSLog(@"%@:error:%@" , url,request.error.localizedDescription);
    }];
}

///我的设备列表，并筛选四大件设备
+ (void)requestMyBigDeviceSuccess:(successData)success fail:(failedData)fail {
    NSString *url = @"/app/device-user-rel/pageMyDevices";
    [MRKBaseRequest mrkRequestType:YTKRequestMethodPOST
                               url:url
                           andParm:@{}
          completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"%@:response:%@" , url,request.responseObject);
        
        id data = [request.responseObject valueForKeyPath:@"data.records"];
        NSArray *array = [NSArray modelArrayWithClass:[MRKDeviceModel class] json:data];
        
        NSMutableArray *tmpArr = [NSMutableArray array];
        [array enumerateObjectsUsingBlock:^(MRKDeviceModel *obj, NSUInteger idx, BOOL * _Nonnull stop) {
            switch (obj.productId.intValue) {
                case BicycleEquipment:
                case TreadmillEquipment:
                case BoatEquipment:
                case EllipticalEquipment:
                    [tmpArr addObject:obj];
                    break;
                default:break;
            }
        }];
        success(tmpArr);
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        fail(request.error);
        NSLog(@"%@:error:%@" , url,request.error.localizedDescription);
    }];
}

///根据deviceId查找当前设备下所有的离线训练数据
+(void)requestDeviceLeaveData:(NSDictionary *)parameters success:(successData)success fail:(failedData)fail {
    NSString *url = @"/user/iot-sport/query-offline-training-data";
    [MRKBaseRequest mrkRequestType:YTKRequestMethodGET
                               url:url
                           andParm:parameters
          completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"%@:response:%@" , url,request.responseObject);
        NSArray *arr = (NSArray *)[request.responseObject valueForKeyPath:@"data"];
        success(arr);
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        fail(request.error);
        NSLog(@"%@:error:%@" , url,request.error.localizedDescription);
    }];
}

@end
