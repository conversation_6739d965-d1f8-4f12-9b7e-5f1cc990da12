//
//  MRKSearchViewModel.m
//  Student_IOS
//
//  Created by MacPro on 2023/3/21.
//

#import "MRKSearchViewModel.h"
#import "MRKDeviceModel.h"
#import "MRKDeviceURLRequest.h"
#import "MRKHealthViewModel.h"

@interface MRKSearchViewModel()
@property (nonatomic, strong) NSMutableArray *tmpDataArray;

@end
@implementation MRKSearchViewModel

- (instancetype)init {
    self = [super init];
    if(self) {
        self.bannerArray = [NSArray array];
        self.sections = [NSMutableArray array];
        self.dataArray = [NSMutableArray array];
        self.deviceArray = [NSMutableArray array];
        self.healthArray = [NSMutableArray array];
        self.homeDataSignal = [RACSubject subject];
        self.tmpDataArray = [NSMutableArray array];
    }
    return self;
}

- (void)requestAllData:(NSNumber *)type showOther:(NSNumber *)showOther {
    RACSignal *deviceSignal = [self getDeviceData:type showOther:showOther];
    RACSignal *healthSignal = [self getHealthData];
    RACSignal *bannaerSignal = [self getDeviceBannerData];
    // 同步刷新
    @weakify(self);
    [[RACSignal combineLatest:@[deviceSignal, healthSignal, bannaerSignal]] subscribeNext:^(id x) {
        @strongify(self);
        if (self.healthArray.count > 0) {
            [self.tmpDataArray addObject:self.healthArray];
            [self.sections addObject:@"第三方应用与服务"];
        }
        self.dataArray = self.tmpDataArray.mutableCopy;
        self.searchView.viewModel = self;
    }];
}

- (RACSignal *)getDeviceData:(NSNumber *)type showOther:(NSNumber *)showOther {
    return [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        
        NSDictionary *parms = @{
            @"current"     : @"1",
            @"size"        : @"99",
            @"level"       : @"0" ,
            @"isShowOther" : showOther,
            @"type"        : type ?: @1
        };
        [MRKRequestServiceData requestEquipmentTypeList:parms success:^(id data) {
            [self.tmpDataArray removeAllObjects];
            
            id json = [data valueForKeyPath:@"records"];
            NSArray *dataArr = [NSArray modelArrayWithClass:[MRKTypeModel class] json:json];
            NSArray *sports =  [dataArr filteredArrayUsingPredicate:[NSPredicate predicateWithFormat:[NSString stringWithFormat:@"SELF.type = '1'"]]];
            NSArray *health =  [dataArr filteredArrayUsingPredicate:[NSPredicate predicateWithFormat:[NSString stringWithFormat:@"SELF.type = '3'"]]];
            if (sports.count > 0) {
                [self.tmpDataArray addObject:sports];
                [self.sections addObject:@"运动设备"];
            }
            
            if (health.count > 0) {
                [self.tmpDataArray addObject:health];
                [self.sections addObject:@"健康设备"];
            }
            
            [subscriber sendNext:@1];
            [subscriber sendCompleted];
            
        } failure:^(id data) {
            //无网络占位图 2021-12-07 --wk
            [subscriber sendNext:@0];
            [subscriber sendCompleted];
        }];
        
        return nil;
    }];
}

- (RACSignal *)getHealthData {
    return [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        [MRKHealthViewModel requestHealthAuthList:@{} success:^(NSArray<MRKHealthServiceModel *> * _Nonnull data) {
            [self.healthArray removeAllObjects];
            
            for (MRKHealthServiceModel *m in data) {
                MRKTypeModel *model = [[MRKTypeModel alloc] init];
                model.typeName = m.name;
                model.bigIconImages = m.icon;
                model.type = [NSString stringWithFormat:@"%d",m.type];
                model.productID = @"-001";
                [self.healthArray addObject:model];
            }
            
            [subscriber sendNext:@1];
            [subscriber sendCompleted];
        } fail:^{
            [subscriber sendNext:@0];
            [subscriber sendCompleted];
        }];
        return nil;
    }];
}

- (RACSignal *)getDeviceBannerData {
    return [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        
        [MRKAdvertManager mrkRequestPositionCode:MRKEquitmentAddBannerCode
                        completeBlockWithSuccess:^(MRKAdvertDataModel * _Nullable model) {
            NSLog(@"model == %@" , model);
            self.bannerArray = model.adverts.mutableCopy;
            [subscriber sendNext:@1];
            [subscriber sendCompleted];
        }];
        
        return nil;
    }];
}

- (void)requestDeviceWithBModel:(BluetoothModel *)bmodel pageType:(NSNumber *)type{
    NSMutableDictionary *parms = @{
        @"bluetoothName"        : bmodel.localName ,
        @"productId"            : self.productID ? : @"",
        @"notShowProductIdList" : self.productID ? @[] : @[@(HeartEquipment), @(FatScaleEquipment)],
        @"modelId"              : @"",
        @"mac"                  : @""
    }.mutableCopy;
    
    if (type.intValue == 4){///过滤4大件 和俩小件
        [parms setObject:@[@(BicycleEquipment), @(TreadmillEquipment), @(BoatEquipment), @(EllipticalEquipment), @(HeartEquipment), @(FatScaleEquipment)] forKey:@"notShowProductIdList"];
    }
    
    @weakify(self);
    [MRKDeviceURLRequest requestSearchDeviceInfo:parms success:^(id data) {
        @strongify(self);
        id json = [data valueForKeyPath:@"data"];
        NSLog(@"searchDevice ======== %@", json);
        
        if (json) {
            MRKDeviceModel *infom = [MRKDeviceModel modelWithJSON:json];
            // 后端让前端手动过滤小彩屏
            if ([[MRKDeviceManager shareManager] isJudgeBlufiDevice:infom.communicationType]) {
                return;
            }
            bmodel.equipmentModel = infom;
            if (self.modelIdList.count && [self.modelIdList containsObject:infom.modelId]) {
                ///如果需要筛选指定型号 则筛选
                [self.deviceArray addObject:bmodel];
            }  else {
                ///否则直接添加到数据源
                [self.deviceArray addObject:bmodel];
            }
            self.typeSearchView.deviceArray = self.deviceArray.copy;
            self.searchView.deviceArray = self.deviceArray.copy;
        }
    } fail:^(id data) {
        NSString *log = [NSString stringWithFormat:@"&&发现设备%@,调用接口返回失败结果&：%@", parms, data];
        MLog(log);
    }];
}


- (void)requestDeviceWithArray:(NSArray *)bmodels success:(successData)success {
    
    NSArray *array = [bmodels valueForKeyPath:@"localName"];
    NSString *str  = [array componentsJoinedByString:@","];
    NSDictionary * paramDict = @{
        @"equipName":str ,
//        @"oneLevelTypeId" : @"10"
    };
    
    [MRKRequestServiceData searchDevice:paramDict success:^(id data) {
        NSArray *arr = [NSArray modelArrayWithClass:[EquipmentInfoModel class] json:data];
        [self.deviceArray addObjectsFromArray:arr];
        
        self.typeSearchView.deviceArray = self.deviceArray.copy;
        self.searchView.deviceArray = self.deviceArray.copy;
        success(@1);
    } failure:^(id data) {
        success(@0);
    }];
}


- (void)setSearchStatus:(SearchTopStatus)searchStatus {
    _searchStatus = searchStatus;
    if(self.searchView){
        self.searchView.searchStatus = searchStatus;
    }
    if(self.typeSearchView) {
        self.typeSearchView.searchStatus = searchStatus;
    }
}

@end


@implementation MRKTypeModel

+ (NSDictionary *)modelCustomPropertyMapper {
    return @{@"productID":@"id"};
}

@end
