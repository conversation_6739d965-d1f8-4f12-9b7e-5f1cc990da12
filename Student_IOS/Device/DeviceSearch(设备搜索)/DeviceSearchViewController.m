//
//  DeviceSearchViewController.m
//  Student_IOS
//
//  Created by MacPro on 2021/3/31.
//

#import "DeviceSearchViewController.h"
#import "DeviceFilterViewController.h"
#import "MRKSearchView.h"
#import "NewConnectManager.h"
#import "MRKSearchViewModel.h"
#import "MRKTimerManager.h"
#import "MRKSearchHelpAlertView.h"
#import "MRKAutoConnectManager.h"
#import "MRKDeviceConnectAlertView.h"
#import "BlueAuthAlertView.h"
#import "MRKHealthAuthorizationController.h"
#import "MyDeviceListViewController.h"
#import "MRKFirstConnectController.h"

#define kSearchFirstTimeout 5

@interface DeviceSearchViewController ()<MRKSearchViewDelegate , NewConnectManagerDelegate> {
    NSNumber * _isShowOther; //1显示其他; 0不显示
}
@property (nonatomic, strong) NewConnectManager *connectManager;
@property (nonatomic, strong) MRKSearchViewModel *viewModel;
@property (nonatomic, strong) dispatch_source_t timer;
@property (nonatomic, nullable, strong) MRKAlertView *bleAuthView;
@property (nonatomic, strong) UIButton *scanBtn;
@end

@implementation DeviceSearchViewController

- (void)viewDidLoad {
    self.tracePageId = @"page_add_equipment";
    [super viewDidLoad];
    
    if([self.productID isNotEmpty]) {
        NSString *name = [MRKEquipmentTypeData nameFromProductId:self.productID];
        self.navTitle = [NSString stringWithFormat:@"添加%@" , self.typeModel.typeName ? : name];
    } else {
        self.navTitle = @"添加设备";
    }
    
    self.viewModel = [MRKSearchViewModel new];
    self.connectManager = [NewConnectManager new];
    
    _isShowOther = @0;
    
    [self UI];
    
    ///显示连接帮助引导 防止其他UI未加载出来
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self showHelpGuide];
    });
    
    @weakify(self);
    [[[RACObserve([NewBluePeripheralManager sharedInstance], state) distinctUntilChanged] takeUntil:[self rac_willDeallocSignal]] subscribeNext:^(NSNumber * x) {
        @strongify(self);
        [self checkBlueState:x];
    }];
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    
    if(![self.productID isNotEmpty]) {
        ///一级搜索页面 获取数据
        [self.viewModel requestAllData:self.type showOther:_isShowOther];
    } else {
        ///二级页面
        [self showRightView:NO];
    }
    
    ///开始搜索
    [self startSearch];
}

- (void)viewDidDisappear:(BOOL)animated {
    [super viewDidDisappear:animated];
    
    ///移除连接帮助指引
    if([self.view viewWithTag:0x888]) {
        [[self.view viewWithTag:0x888] removeFromSuperview];
    }
    
    ///进入二级搜索页面，该页面不再监听搜索数据
    self.connectManager.delegate = nil;
    
    ///停止搜索
    [self stopSearch];
}

///开始搜索
- (void)startSearch {
    
    if([NewBluePeripheralManager sharedInstance].state != CBManagerStatePoweredOn) {
        return;
    }
    
    if(!self.connectManager.delegate) {
        NSLog(@"还未把当前类设置为connectManager代理");
        ///设置搜索代理
        self.connectManager.delegate = self;
    }
    
    if(self.viewModel.searchStatus == SearchTopSearching) {
        NSLog(@"正在搜索中,请勿重复搜索......");
        return;
    }
    
    @weakify(self);
    self.timer = TimerGCDWithInterval(1, kSearchTimeout, ^(NSInteger count) {
        dispatch_async(dispatch_get_main_queue(), ^{
            @strongify(self);
            ///二级搜索页面
            NSLog(@"count====%@" , @(count));
            if([self.productID isNotEmpty]) {
                [self.viewModel.typeSearchView searchProgress:((count > 0 ? count - 1 : 0) / 10.0)];
            }
        });
    }, ^{
        @strongify(self);
        [self.viewModel.typeSearchView searchProgress:1.0];
        [self stopSearch];
    });
    
    if(![self.productID isNotEmpty]) {
        /// 一级搜索页面 倒计时 5s 显示未搜索到设备
        dispatch_async(dispatch_get_main_queue(), ^{
            NSLog(@"一级搜索页面 倒计时 5s 显示未搜索到设备==%@" ,self);
            [self performSelector:@selector(firstFailSearch) withObject:nil afterDelay:kSearchFirstTimeout ];
        });
    }
    
    self.viewModel.searchStatus = SearchTopSearching;
    [self.connectManager startScan];
    
    NSString *log = [NSString stringWithFormat:@"&&开始搜索&&：%@",@{@"type":self.productID ? : @""}];
    MLog(log);
}

///一级页面 5s 未搜索到设备
- (void)firstFailSearch {
    [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(firstFailSearch) object:nil];
    NSLog(@"firstFailSearch___self.viewModel.deviceArray===%@",[self.viewModel.deviceArray valueForKeyPath:@"localName"]);
  
    if (self.viewModel.deviceArray.count == 0) {
        [self.viewModel.searchView firstFailSearch];
    }
}

///停止搜索
- (void)stopSearch {
    NSLog(@"%@____stopSearch" , self);
    ///取消定时
    [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(firstFailSearch) object:nil];
  
    cancelTimer(self.timer);
    NSString *log = [NSString stringWithFormat:@"&&停止搜索，搜索结果&&：%@",[self.viewModel.deviceArray valueForKeyPath:@"localName"]];
    MLog(log);
    
    [self.connectManager stopScan];
    self.viewModel.searchStatus = SearchTopSearchEnd;
}

- (void)UI{
    self.mrkContentView.backgroundColor = [UIColor whiteColor];
    if([self.productID isNotBlank]) {
        ///二级带设备大类搜索页面
        self.viewModel.typeSearchView = [[MRKTypeSearchView alloc] init];
        self.viewModel.typeSearchView.delegate = self;
        self.viewModel.typeSearchView.productID = self.productID;
        self.viewModel.productID = self.productID;
        self.viewModel.typeSearchView.typeModel = self.typeModel;
        [self.mrkContentView addSubview:self.viewModel.typeSearchView];
        [self.viewModel.typeSearchView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.mrkContentView.mas_top).offset(kNavBarHeight);
            make.left.right.bottom.equalTo(@(0));
        }];
        
    } else {
        
        ///一级搜索页面
        self.viewModel.searchView = [[MRKSearchView alloc] init];
        self.viewModel.searchView.delegate = self;
        [self.mrkContentView addSubview:self.viewModel.searchView];
        [self.viewModel.searchView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.mrkContentView.mas_top).offset(kNavBarHeight);
            make.left.right.bottom.equalTo(@(0));
        }];
    }
}


#pragma mark - NewConnectManagerDelegate
- (void)discoverBluetoothModel:(BluetoothModel *)bModel {
    ///二级页面，有一级页面已经显示的设备过滤
    if([[self.devices valueForKeyPath:@"localName"] containsObject:bModel.localName]){
        return;
    }
 
    ///请求该设备信息
    [self.viewModel requestDeviceWithBModel:bModel pageType:self.type];
    
    NSString *log = [NSString stringWithFormat:@"&&发现设备,准备调用接口&：%@",bModel.localName];
    MLog(log);
}

#pragma mark - view delegate
///选择某个设备进行连接绑定
- (void)didSelectDevice:(BluetoothModel *)device {
    NSLog(@"绑定==%@" , device);
    [self stopSearch];
    
    __block loadingView * loadview = [[loadingView alloc] initWithFrame:self.view.frame text:@"连接中..."];
    loadview.tag = 0x101010;
    [self.view addSubview:loadview];
    
    
    ///手动连接
    self.connectManager.connectMode = ManualDeviceConnectMode;
    @weakify(self);
    self.connectManager.connectStatusBlock = ^(id data) {
        @strongify(self);
        [loadview removeFromSuperview];
        if (self.connectSuccessBlock) {
            self.connectSuccessBlock(data);
        }
        
        if([data boolValue]) {
            ///绑定成功 通知首页刷新获取显示大类，根据返回数据判断是否需要刷新首页其他接口
            NSLog(@"绑定成功，通知首页刷新获取显示大类，根据返回数据判断是否需要刷新首页其他接口");
            /// 判断是否有链路数据需要跳转
            NSString *resourceUrl = [self.pageRouterData valueForKeyPath:@"CONNECT_SUCCESS.resourceUrl"];
            if ([resourceUrl isNotBlank]){
                /// 四大件走
                if ([MRKDeviceModel isFourBigDevice:device.type]){
                    [[MRKLinkRouterManager sharedInstance] routerResourcePath:resourceUrl
                                                             handleParameters:@{@"productId":device.type?:@""}
                                                            completionHandler:nil];
                    return;
                }
                MRKFirstConnectController *vc= [[MRKFirstConnectController alloc] init];
                vc.productId = device.type;
                [self.navigationController pushViewController:vc animated:NO];
            }else{
                ///判断是否要进入设备详情页面
                [self jumpAction:device];
            }
        } else {
            NSLog(@"连接失败，提示绑定失败");
        }
    };
    
    [self.connectManager connectDevice:[BluetoothModel cModelFromBModel:device]];
}

- (void)updateRightView:(BOOL)showScan {
    [self showRightView:showScan];
}

- (void)reStartSearch {
    [self startSearch];
}

- (void)openMeritServer{
    WebViewViewController *vc = [WebViewViewController new];
    vc.isHiddenNav = YES;
    vc.htmlURL = MRKAppH5LinkCombine(MRKUserHelpAndFeedback);
    [self.navigationController pushViewController:vc animated:YES];
}

///选择设备类型
- (void)didSelectedCell:(NSIndexPath *)indexPath {
    MRKTypeModel *tmodel = self.viewModel.dataArray[indexPath.section][indexPath.row];
    
    ///心率带/体脂秤
    if (tmodel.productID.intValue == HeartEquipment ||
        tmodel.productID.intValue == FatScaleEquipment ) {
        
        ///已经绑定 ->提示解绑
        if (tmodel.typeNum.intValue) {
            [MRKDeviceConnectAlertView alertHadBindDeviceView:tmodel.productID action:^(id data) {
                if([data intValue] == 1) {
                    MyDeviceListViewController *vc = [[MyDeviceListViewController alloc]init];
                    vc.productType = @3;
                    [self.navigationController pushViewController:vc animated:YES];
                }
            }];
            return;
        }
        
        ///跳转到搜索列表 选择型号 22-09-06
        DeviceFilterViewController *vc = [DeviceFilterViewController new];
        vc.equipmentType = tmodel.productID;
        [self.navigationController pushViewController:vc animated:YES];
        return;
    }
    
    ///判断选择的大类时否有设备在连接
    if([BlueDataStorageManager isConnectDeviceWithProductID:tmodel.productID]) {
        [self.connectManager alertHadConnected:tmodel.productID];
        return;
    }
    
    //进入二级搜索页面
    DeviceSearchViewController *vc = [DeviceSearchViewController new];
    vc.productID = tmodel.productID;
    vc.typeModel = tmodel;
    vc.pageRelationPath = self.pageRelationPath;
    [self.navigationController pushViewController:vc animated:YES];
}

/// 选择三方健康服务
- (void)didSelectedHealth:(int)type {
    if (type == 20){
        if (UI_USER_INTERFACE_IDIOM() == UIUserInterfaceIdiomPhone) {
            MRKHealthAuthorizationController *vc = [[MRKHealthAuthorizationController alloc] init];
            vc.type = type;
            [self.navigationController pushViewController:vc animated:YES];
            
            ///去掉WatchItem上的New标签
            NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
            [userDefaults setBool:YES forKey:@"AppleWatchNewTag"];
            [userDefaults synchronize];
        }else{
            [MBProgressHUD showMessage:@"ipad暂不支持"];
        }
        return;
    }
    
    MRKHealthAuthorizationController *vc = [[MRKHealthAuthorizationController alloc] init];
    vc.type = type;
    [self.navigationController pushViewController:vc animated:YES];
}

#pragma mark - self method
- (void)jumpAction:(BluetoothModel *)bModel {
    /// 需要进入，通知详情页面 调用用户勋章弹窗
    /// 不需要，则不需要调用用户勋章
    /// 课程详情，实景视频列表，web页面，视频播放页面，心率控速（跑步机），能力测评
    /// 进入搜索绑定成功，不需要进入设备详情，直接返回上述页面
    NSArray *array = [[self.navigationController.childViewControllers reverseObjectEnumerator] allObjects];
    
    UIViewController *targetVC = [MRKDeviceConnectAlertView isNeedAutoBack:array];
    if(targetVC) {
        [self.navigationController popToViewController:targetVC animated:YES];
    } else {
        ///进入详情 并通知详情页面刷新勋章
        [[RouteManager sharedInstance] jumpToDeviceDetail:bModel.equipmentModel];
    }
}

///扫描——二维码
- (void)qrcodeAction:(UIButton *)sender{
    [[RouteManager sharedInstance] jumpToScanQrcodeVC: nil];
}

///自动搜索
- (void)scanAction:(UIButton *)sender{
    sender.traceEventId = @"btn_search_again";
    [self.viewModel.searchView scanAction];
}

- (void)moreAction:(id)sender{
    NSLog(@"帮助文档/常见问题");
    WebViewViewController *vc = [[WebViewViewController alloc] init];
    vc.htmlURL = MRKAppH5LinkCombine(MRKLinkDeviceConnectHelp);
    [self.navigationController pushViewController:vc animated:YES];
}


///显示搜索按钮
- (void)showRightView:(BOOL)showScan {
    ///搜索一级页面 且 需要显示
    if(![self.productID isNotEmpty] && showScan && [NewBluePeripheralManager sharedInstance].state == CBManagerStatePoweredOn) {
        self.scanBtn.hidden = NO;
        [self.scanBtn mas_updateConstraints:^(MASConstraintMaker *make) {
            make.width.equalTo(@32);
        }];
    }
}

///判断蓝牙状态显示弹窗
- (void)checkBlueState:(NSNumber *)x {
    NSLog(@"%@__checkBlueState==%@" , self,x);
    if (x.intValue != CBManagerStatePoweredOn) {
        [self stopSearch];
        [self addBlueAuthView];
        return;
    }
    [self removeAuthView];
    NSLog(@"打开蓝牙了");
    if(self.bleAuthView){
        @weakify(self);
        [self.bleAuthView dismissAnimated:YES complete:^{
            @strongify(self);
            self.bleAuthView = nil;
            [self startSearch];
        }];
    } else {
        [self startSearch];
    }
}


- (void)removeAuthView{
    if([self.view viewWithTag:0x999]) {
        [[self.view viewWithTag:0x999] removeFromSuperview];
    }
}

///蓝牙未开启页面
- (void)addBlueAuthView {
    if([self.view viewWithTag:0x999]) {
        [[self.view viewWithTag:0x999] removeFromSuperview];
    }
    
    MRKSearchBlueAuthView *authView = [[MRKSearchBlueAuthView alloc]initWithFrame:self.view.bounds];
    authView.tag = 0x999;
    @weakify(self);
    authView.openBlock = ^(id data) {
        @strongify(self);
        [self openBlueAction];
    };
    [self.view addSubview:authView];
    [authView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.mrk_navgationBar.mas_bottom).offset(0);
        make.left.right.bottom.equalTo(@0);
    }];
}

- (void)openBlueAction {
    CBManagerState x = [NewBluePeripheralManager sharedInstance].state;
    if (x == CBManagerStateUnauthorized ||
        x == CBManagerStateUnsupported || 
        x == CBManagerStateResetting ) {
        BlueAuthAlertView *bleAlertView = [[BlueAuthAlertView alloc]initWithAnimationStyle:MRKActionAlertViewTransitionStyleSlideFromBottom];
        [bleAlertView show];
    } else {
        BlueOpenAlertView *bleAlertView = [[BlueOpenAlertView alloc]initWithAnimationStyle:MRKActionAlertViewTransitionStyleSlideFromBottom];
        [bleAlertView show];
        self.bleAuthView = bleAlertView;
    }
}

- (void)showHelpGuide {
    ///只引导一次
    NSUserDefaults *user = [NSUserDefaults standardUserDefaults];
    BOOL res = [user objectForKey:@"guide_pop_ljbz"];
    if(res) {
        return;
    }
    [user setBool:YES forKey:@"guide_pop_ljbz"];
    [user synchronize];
    
    UIImage *img = [UIImage imageNamed:@"pop_ljbz"];
    UIImageView *guideImgV = [[UIImageView alloc]initWithImage:img];
    [self.view addSubview:guideImgV];
    [guideImgV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(@(WKDHPX(-10)));
        make.top.equalTo(self.mrk_navgationBar.mas_bottom).offset((WKDHPX(0)));
        make.size.equalTo(@(CGSizeMake(WKDHPX(210), WKDHPX(60))));
    }];
    guideImgV.tag = 0x888;
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        if(guideImgV.superview) {
            [guideImgV removeFromSuperview];
        }
    });
}

#pragma mark ---------Delegate -----------

- (BOOL)viewControllerIsNeedNavBar:(MRKBaseController *)viewController {
    return YES;
}

- (UIColor *)mrkNavigationBarBackgroundColor:(MRKNavigationBar *)navigationBar {
    return [UIColor clearColor];
}

- (UIImage *)mrkNavigationBarLeftButtonImage:(UIButton *)leftButton navigationBar:(MRKNavigationBar *)navigationBar {
    return [UIImage imageNamed:[self.productID isNotEmpty] ? @"icon_back-3" : @"icon_back"];
}

- (UIView *)mrkNavigationBarRightView:(MRKNavigationBar *)navigationBar{
    UIView *v = [[UIView alloc] init];
    v.size = CGSizeMake(32*3, 44);
    
    UIButton *moreBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    moreBtn.frame = CGRectMake(0, 0, 32, 44);
    [moreBtn setImage:[UIImage imageNamed:@"search_icon_more"] forState:UIControlStateNormal];
    [moreBtn addTarget:self action:@selector(moreAction:) forControlEvents:UIControlEventTouchUpInside];
    [v addSubview:moreBtn];
    [moreBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.top.bottom.equalTo(v);
        make.width.equalTo(@32);
    }];
    
    UIButton *scanBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    scanBtn.frame = CGRectMake(0, 0, 32, 44);
    [scanBtn setImage:[UIImage imageNamed:@"icon_scan"] forState:UIControlStateNormal];
    [scanBtn addTarget:self action:@selector(scanAction:) forControlEvents:UIControlEventTouchUpInside];
    [v addSubview:scanBtn];
    [scanBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(moreBtn.mas_left);
        make.top.bottom.equalTo(v);
        make.width.equalTo(@0);
    }];
    self.scanBtn = scanBtn;
    self.scanBtn.hidden = YES;
    
    UIButton *qrcodeBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    qrcodeBtn.frame = CGRectMake(0, 0, 32, 44);
    [qrcodeBtn setImage:[UIImage imageNamed:@"qrcode_black_add"] forState:UIControlStateNormal];
    [qrcodeBtn addTarget:self action:@selector(qrcodeAction:) forControlEvents:UIControlEventTouchUpInside];
    [v addSubview:qrcodeBtn];
    [qrcodeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(scanBtn.mas_left);
        make.top.bottom.equalTo(v);
        make.width.equalTo(@32);
    }];

    return v;
}

- (void)dealloc {
    ///离开该页面 重新获取接口开启自动重连
    ///&& 不是进入二级页面
//    [[MRKAutoConnectManager sharedInstance] manualStartAutoConnect];
    NSLog(@"离开该页面 重新获取接口开启自动重连");
}

@end






@interface AllCatogoryCollectionViewCell ()
@property (nonatomic, strong) UIImageView *imgView;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *tagLabel;
@end

@implementation AllCatogoryCollectionViewCell

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    UIView *bgView = [UIView new];
    bgView.backgroundColor = UIColorHex(#FAFAFA);
    bgView.layer.cornerRadius = 8;
    bgView.layer.masksToBounds = YES;
    bgView.clipsToBounds = YES;
    [self.contentView addSubview:bgView];
    [bgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsMake(12, 0, 0, 0));
    }];
    
    [bgView addSubview:self.imgView];
    [_imgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(bgView.mas_centerY);
        make.left.equalTo(@(WKDHPX(8)));
        make.width.height.equalTo(@(DHPX(48)));
    }];
    
    [bgView addSubview:self.titleLabel];
    [_titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(bgView.mas_centerY);
        make.left.equalTo(self.imgView.mas_right).offset(8);
    }];
    
    self.tagLabel.hidden = YES;
    [bgView addSubview:self.tagLabel];
    [_tagLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(bgView.mas_top);
        make.right.equalTo(bgView.mas_right);
        make.size.mas_equalTo(CGSizeMake(WKDHPX(44), WKDHPX(20)));
    }];
    MrkCornerMaskWithViewRadius(self.tagLabel, ViewRadiusMake(0, 0, 8, 0));
}

- (void)configWithitem:(MRKTypeModel *)item {
    if (item) {
        if ([item.iconImages isEqual:[NSNull null]] || item.bigIconImages == nil) {
            self.imgView.image = placeImage;
        }else{
            [self.imgView sd_setImageWithURL:[NSURL URLWithString:item.bigIconImages] placeholderImage:placeImage];
        }
        
        self.titleLabel.text = item.typeName;
        
        if (item.type.intValue == 20 && UI_USER_INTERFACE_IDIOM() == UIUserInterfaceIdiomPhone) {
            NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
            BOOL RegulateTip = [userDefaults boolForKey:@"AppleWatchNewTag"];
            self.tagLabel.hidden = RegulateTip;
        }else{
            self.tagLabel.hidden = YES;
        }
    }
}
- (void)configWithHealth:(MRKHealthServiceModel *)model {
    self.titleLabel.text = model.name;
    [self.imgView sd_setImageWithURL:[NSURL URLWithString:model.icon] placeholderImage:nil];
    
    if (model.type == 20 && UI_USER_INTERFACE_IDIOM() == UIUserInterfaceIdiomPhone) {
        NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
        BOOL RegulateTip = [userDefaults boolForKey:@"AppleWatchNewTag"];
        self.tagLabel.hidden = RegulateTip;
    }else{
        self.tagLabel.hidden = YES;
    }
}

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        UILabel *l = [UILabel new];
        l.textColor = [UIColor colorWithHexString:@"#4C5362"];
        l.font = [UIFont fontWithName:fontNameMeDium size:(14.0)];
        _titleLabel = l;
    }
    return _titleLabel;
}

- (UIImageView *)imgView {
    if (!_imgView) {
        _imgView = [UIImageView new];
        _imgView.contentMode = UIViewContentModeScaleAspectFit;
    }
    return _imgView;
}

- (UILabel *)tagLabel {
    if (!_tagLabel) {
        UILabel *l = [UILabel new];
        l.backgroundColor = [UIColor colorWithHexString:@"#FF2451"];
        l.text = @"NEW";
        l.textColor = [UIColor whiteColor];
        l.font = [UIFont systemFontOfSize:12 weight:UIFontWeightHeavy];
        l.textAlignment = 1;
        _tagLabel = l;
    }
    return _tagLabel;
}
@end
