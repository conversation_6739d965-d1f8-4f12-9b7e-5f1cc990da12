//
//  DeviceSearchViewController.h
//  Student_IOS
//
//  Created by MacPro on 2021/3/31.
//  app首页改版 一级/二级搜索页面

#import <UIKit/UIKit.h>
#import "MRKHealthViewModel.h"




NS_ASSUME_NONNULL_BEGIN

@class MRKTypeModel;
@interface DeviceSearchViewController : MRKBaseController
@property (nonatomic , strong) NSNumber *type;             ///1运动设备 (展示体脂秤)，2运动设备 (不展示体脂秤)，3健康设备, 4过滤四大件
@property (nonatomic , strong) NSString *productID;        ///产品大类 二级搜索页面需要
///一级搜索页面已经显示的设备 二级页面不需要再次显示 [BluetoothModel]
///23-04-19 删除这个功能，首页搜出来的设备，这个页面也要显示，所以不再过滤
@property (nonatomic , strong) NSArray *devices;
@property (nonatomic , strong) NSArray *modelIdList;///活动页面 传入绑定指定型号设备
@property (nonatomic , copy) successData connectSuccessBlock;
@property (nonatomic , strong) MRKTypeModel *typeModel;

//@property (nonatomic , copy) NSString *source;
@end




@interface AllCatogoryCollectionViewCell:UICollectionViewCell
- (void)configWithitem:(id)item;
- (void)configWithHealth:(MRKHealthServiceModel *)model;
@end

NS_ASSUME_NONNULL_END
