//
//  MRKDeviceModel.m
//  Student_IOS
//
//  Created by MacPro on 2023/3/29.
//

#import "MRKDeviceModel.h"
//#import <PPScaleSDK/PPScaleManager.h>

@implementation MRKDeviceModel

+ (NSDictionary *)modelContainerPropertyGenericClass {
    return @{
        @"uniqueModelIdentify" : MRKDeviceIdentifyModel.class,
        @"featureDescription" : MRKDeviceFeatureModel.class,
    };
}

+ (NSDictionary *)modelCustomPropertyMapper {
    return @{@"deviceDescription":@"description"};
}

- (BOOL)modelCustomTransformFromDictionary:(NSDictionary *)dic {
    _isXEnjoyDevice = (_brandType.intValue == 2);
    return YES;
}

- (NSString *)deviceAlias {
    return [_deviceAlias isNotEmpty] ? _deviceAlias : self.bluetoothName;
}

+ (MRKDeviceModel *)currentHeartInfo {
    NSArray *array = [MRKNetworkCache httpCacheForURL:MyAllDeviceInfoKey parameters:nil];
    for (MRKDeviceModel *model in array) {
        if(model.productId.intValue == HeartEquipment) {
            return model;
        }
    }
    return nil;
}

+ (MRKDeviceModel *)currentFatScalInfo {
    NSArray *array = [MRKNetworkCache httpCacheForURL:MyAllDeviceInfoKey parameters:nil];
    for (MRKDeviceModel *model in array) {
        if (model.productId.intValue == FatScaleEquipment) {
            return model;
        }
    }
    return nil;
}


///**解绑后 自动连接数据缓存清理**/
//+ (void)removeDeviceModelInCache:(MRKDeviceModel *)model {
//    id data = [MRKNetworkCache httpCacheForURL:/*AutoConnectDeviceInfoKey*/ parameters:nil];
//    NSMutableArray *models = [NSArray modelArrayWithClass:[MRKDeviceModel class] json:data].mutableCopy;
//    for (MRKDeviceModel *mod in models.copy) {
//        if (mod.productId.intValue == model.productId.intValue) {
//            [models removeObject:mod];
//        }
//    }
//    
//    ///储存修改后的数据
//    if (models.count > 0){
//        id newData = [models modelToJSONObject];
//        [MRKNetworkCache setHttpCache:newData URL:AutoConnectDeviceInfoKey parameters:nil];
//    }
//}




- (NSNumber *)isConnect {
    return @([BlueDataStorageManager isConnectDeviceWithProductID:self.productId name:self.name]);
}

- (NSString *)name {
    return self.productId.intValue == FatScaleEquipment ? self.mac :  self.bluetoothName;
}

///（四大件）
- (BOOL)isFourBigDevice {
    BOOL show = NO;
    switch (_productId.intValue) {
        case BicycleEquipment:
        case TreadmillEquipment:
        case BoatEquipment:
        case EllipticalEquipment:
            show = YES;
            break;
        default:
            break;
    }
    return show;
}


/// 是否四大
+ (BOOL)isFourBigDevice:(NSString *)productID {
    BOOL show = NO;
    switch (productID.intValue) {
        case BicycleEquipment:
        case TreadmillEquipment:
        case BoatEquipment:
        case EllipticalEquipment:
            show = YES;
            break;
        default:
            break;
    }
    return show;
}

@end



@implementation MRKDeviceIdentifyModel
@end



@implementation MRKDeviceFeatureModel
@end



@implementation OTAModel
@end

