//
//  MRKSearchView.m
//  Student_IOS
//
//  Created by MacPro on 2023/3/20.
//

#import "MRKSearchView.h"
#import "MRKAddDeviceCollectionReusableView.h"
#import "DeviceSearchViewController.h"
#import "MRKSearchViewModel.h"

@interface MRKSearchView ()<UICollectionViewDelegate, UICollectionViewDataSource, UIScrollViewDelegate>
@property (nonatomic , strong) MRKSearchTopStatusView *topStatusView;///搜索状态view
@property (nonatomic , strong) UIScrollView *scView;
@property (nonatomic , strong) UIView *contentView;
@property (nonatomic , strong) MRKSearchDeviceReusableView *searchTopView;///搜索头view（包括搜索到设备列表）
@property (nonatomic , strong) UICollectionView *collectionView;              ///供选择的设备大类列表
@end

@implementation MRKSearchView

- (void)dealloc {
     NSLog(@"😊😊😊-----dealloc-----😊😊😊%@" , NSStringFromClass([self class]));
}

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if(self) {
        
        [self addSubview:self.topStatusView];
        [self addSubview:self.scView];
        [self.scView addSubview:self.contentView];
        [self.contentView addSubview:self.searchTopView];
        [self.contentView addSubview:self.collectionView];
    }
    return self;
}

- (void)layoutSubviews {
    [super layoutSubviews];
    
    [self.topStatusView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(@(0));
        make.top.equalTo(self.mas_top).offset(0);
        make.height.equalTo(@(WKDHPX(50)));
    }];
    
    [self.scView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.topStatusView.mas_bottom).offset(WKDHPX(0));
        make.left.right.bottom.equalTo(@0);
    }];
    
    [self.contentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.right.bottom.equalTo(@0);
    }];
    
    [self.searchTopView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.scView.mas_top).offset(WKDHPX(0));
        make.width.equalTo(@(RealScreenWidth));
        make.left.right.equalTo(@0);
    }];
    
    [self.collectionView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.searchTopView.mas_bottom).offset(WKDHPX(0));
        make.left.equalTo(@(WKDHPX(16)));
        make.right.equalTo(@(WKDHPX(-16)));
    }];
}

- (void)setDeviceArray:(NSArray *)deviceArray {
    _deviceArray = deviceArray;
    if (deviceArray.count) {
        self.searchTopView.dataArray = deviceArray;
        [self.searchTopView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.scView.mas_top).offset(WKDHPX(0));
            make.width.equalTo(@(RealScreenWidth));
            make.left.right.equalTo(@0);
            make.height.equalTo(@(WKDHPX(140 * deviceArray.count) + WKDHPX(80)));
        }];
    }
}

- (void)setViewModel:(MRKSearchViewModel *)viewModel {
    _viewModel = viewModel;
    
    CGFloat h = (viewModel.dataArray.count * WKDHPX(22+16)) + (viewModel.bannerArray.count ? WKDHPX(112) + WKDHPX(20) : WKDHPX(0));
    for (NSArray *arr in viewModel.dataArray) {
        h = h + (arr.count / 2 + (arr.count % 2 ? 1 : 0)) * WKDHPX(80) ;
    }
    CGFloat scH = (MainHeight - MRKNavBarHeight() - self.topStatusView.height);
    
    CGFloat bottom = (scH - h) > 0 ?  (scH - h) : kSafeArea_Bottom;
    [self.collectionView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(@(h));
        make.top.equalTo(self.searchTopView.mas_bottom).offset(WKDHPX(0));
        make.left.equalTo(@(WKDHPX(16)));
        make.right.equalTo(@(WKDHPX(-16)));
        make.bottom.equalTo(@(-bottom));
    }];
    [self.collectionView reloadData];
}

///5s搜索失败 提示文案
- (void)firstFailSearch {
    [self.searchTopView showFirstFail];
}

///小雷达主动搜索
- (void)scanAction {
    if(self.viewModel.deviceArray.count == 0) {
        ///未搜索到设备 自动滑动到上方
        [self.scView setContentOffset:CGPointMake(0, -1)];
    }else {
        ///直接开始重新搜索
        if (self.delegate && [self.delegate respondsToSelector:@selector(reStartSearch)]) {
            [self.delegate reStartSearch];
        }
    }
}

- (void)setSearchStatus:(SearchTopStatus)searchStatus {
    _searchStatus = searchStatus;
    [self updateStatus:YES];
}

///更新相关状态
- (void)updateStatus:(BOOL)isSet {
    CGFloat offset = self.scView.contentOffset.y;
    ///1.优先判断搜索状态
    if(self.searchStatus == SearchTopSearching) {
        ///1.1 搜索中 不管滑动到哪 都是搜索中
        self.topStatusView.status = SearchTopSearching;
        if (self.viewModel.deviceArray.count == 0) {
            ///执行搜索动画
            [self.searchTopView startSearchAnimation];
        }
        
        ///搜索中 小雷达不显示
        if (self.delegate && [self.delegate respondsToSelector:@selector(updateRightView:)]) {
            [self.delegate updateRightView:NO];
        }
        
    }else if(self.searchStatus == SearchTopSearchEnd) {
        ///搜索结束 无论是否搜索到设备  小雷达显示
        if (self.delegate && [self.delegate respondsToSelector:@selector(updateRightView:)]) {
            [self.delegate updateRightView:YES];
        }
        
        ///1.2 搜索完成
        ///2.是否搜索到设备
        if(self.viewModel.deviceArray.count) {
            ///2.1.1 搜索到设备   无滑动 ：为你搜到以下设备
            if(offset < self.searchTopView.height) {
                self.topStatusView.status = SearchTopSearcedList;
            } else {
                ///2.1.2 搜索到设备 有滑动 ：选择以下设备，手动添加
                self.topStatusView.status = SearchTopSearcedListBottom;
            }
            
        } else {
            ///2.2 未搜索到设备 自动偏移 ：未发现设备，请手动添加
            if(offset == 0) {
                ///自动偏移
                [self.scView setContentOffset:CGPointMake(0, self.searchTopView.height)];
                
            } else if(offset < self.searchTopView.height) {
                if(isSet) {
                    ///手动设置搜索结束 自动偏移
                    [self.scView setContentOffset:CGPointMake(0, self.searchTopView.height)];
                } else {
                    ///开始搜索
                    ///未搜索到设备 重新开始搜索 动画执行
                    [self.searchTopView startSearchAnimation];
                    if(self.delegate && [self.delegate respondsToSelector:@selector(reStartSearch)]) {
                        [self.delegate reStartSearch];
                    }
                }
                
            } else {
                
                self.topStatusView.status = SearchTopSearchFail;
                [self.searchTopView searchFail];
                
            }
        }
        
    }
}
#pragma mark - scrollerview delegate
- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    [self updateStatus:NO];
}

#pragma mark - collectionview delegate
- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    NSArray *arr = self.viewModel.dataArray[section] ;
    return arr.count;
}

- (NSInteger)numberOfSectionsInCollectionView:(UICollectionView *)collectionView {
    return self.viewModel.dataArray.count;
}

- (UICollectionReusableView *)collectionView:(UICollectionView *)collectionView viewForSupplementaryElementOfKind:(NSString *)kind atIndexPath:(NSIndexPath *)indexPath{
    UICollectionReusableView *reusableView = nil;
    if (kind ==UICollectionElementKindSectionFooter){
        if (indexPath.section == self.viewModel.dataArray.count - 1){
            MRKAddDeviceCollectionReusableView *footerV = (MRKAddDeviceCollectionReusableView *)[collectionView dequeueReusableSupplementaryViewOfKind:UICollectionElementKindSectionFooter withReuseIdentifier:MRKAddDeviceReuseIdentifier forIndexPath:indexPath];
            footerV.dataArray = self.viewModel.bannerArray.mutableCopy;
            reusableView = footerV;
        } else {
            UICollectionReusableView *footerV = [collectionView dequeueReusableSupplementaryViewOfKind:UICollectionElementKindSectionFooter withReuseIdentifier:@"FooterView" forIndexPath:indexPath];
            reusableView = footerV;
        }
    } else if (kind == UICollectionElementKindSectionHeader) {
        MRKAddDeviceCollectionHeaderReusableView *header = (MRKAddDeviceCollectionHeaderReusableView *)[collectionView dequeueReusableSupplementaryViewOfKind:UICollectionElementKindSectionHeader withReuseIdentifier:NSStringFromClass([MRKAddDeviceCollectionHeaderReusableView class]) forIndexPath:indexPath];
        header.title = self.viewModel.sections[indexPath.section];
        reusableView = header;
    }
    return reusableView;
}

- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout referenceSizeForHeaderInSection:(NSInteger)section {
    return CGSizeMake(RealScreenWidth, WKDHPX(22+16) );
}

- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout referenceSizeForFooterInSection:(NSInteger)section{
    if(self.viewModel.bannerArray.count && section == self.viewModel.dataArray.count - 1) {
        return CGSizeMake(RealScreenWidth, WKDHPX(112) + WKDHPX(20));
    }
    return CGSizeMake(RealScreenWidth, 0.1);
}

- (__kindof UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    AllCatogoryCollectionViewCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:NSStringFromClass([AllCatogoryCollectionViewCell class]) forIndexPath:indexPath];
    NSDictionary * showDict = self.viewModel.dataArray[indexPath.section][indexPath.row];
    [cell configWithitem:showDict];
    return cell;
}

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
    [collectionView deselectItemAtIndexPath:indexPath animated:YES];
    MRKTypeModel *model = self.viewModel.dataArray[indexPath.section][indexPath.row];
    if ([model.productID isEqualToString:@"-001"]) { // 第三方运动健康
        if(self.delegate && [self.delegate respondsToSelector:@selector(didSelectedHealth:)]) {
            [self.delegate didSelectedHealth:model.type.intValue];
        }
        return;
    }
    
    NSLog(@"选择类型");
    MRKTypeModel * tmodel = self.viewModel.dataArray[indexPath.section][indexPath.row];
    collectionView.traceEventId = tmodel.type.intValue == 1 ? @"btn_page_add_sports_equipment" : (tmodel.type.intValue == 3 ? @"btn_page_add_health_equipment" : @"");
    collectionView.tracePara = @{@"product_id" : tmodel.productID};
    if(self.delegate && [self.delegate respondsToSelector:@selector(didSelectedCell:)]) {
        [self.delegate didSelectedCell:indexPath];
    }
}


#pragma mark - lazy
- (UICollectionView *)collectionView {
    if(!_collectionView) {
        UICollectionViewFlowLayout *layout = [[UICollectionViewFlowLayout alloc] init];
        CGFloat space = WKDHPX(9);
        CGFloat itemW = (RealScreenWidth - 2 * WKDHPX(16) - space) / 2.0;
        CGFloat itemH = WKDHPX(80);
        layout.itemSize = CGSizeMake(itemW, itemH);
        layout.sectionInset = UIEdgeInsetsMake(0, 0, 0, 0);
        layout.minimumLineSpacing = 0;
        layout.minimumInteritemSpacing = space;
        
        _collectionView = [[UICollectionView alloc] initWithFrame:CGRectZero collectionViewLayout:layout];
        _collectionView.backgroundColor = [UIColor whiteColor];
        _collectionView.delegate = self;
        _collectionView.dataSource = self;
        _collectionView.showsVerticalScrollIndicator = NO;
        _collectionView.scrollEnabled = NO;
        [_collectionView registerClass:[AllCatogoryCollectionViewCell class]
            forCellWithReuseIdentifier:NSStringFromClass([AllCatogoryCollectionViewCell class])];
        [_collectionView registerClass:[MRKAddDeviceCollectionReusableView class]
            forSupplementaryViewOfKind:UICollectionElementKindSectionFooter
                   withReuseIdentifier:MRKAddDeviceReuseIdentifier];
        [_collectionView registerClass:[UICollectionReusableView class]
            forSupplementaryViewOfKind:UICollectionElementKindSectionFooter
                   withReuseIdentifier:@"FooterView"];
        [_collectionView registerClass:[MRKAddDeviceCollectionHeaderReusableView class]
            forSupplementaryViewOfKind:UICollectionElementKindSectionHeader
                   withReuseIdentifier:NSStringFromClass([MRKAddDeviceCollectionHeaderReusableView class])];
    }
    return _collectionView;
}

- (UIView *)contentView {
    if(!_contentView) {
        _contentView = [[UIView alloc] init];
    }
    return _contentView;
}

- (MRKSearchDeviceReusableView *)searchTopView {
    if(!_searchTopView) {
        _searchTopView = [MRKSearchDeviceReusableView new];
        @weakify(self);
        _searchTopView.selectDeviceBlock = ^(id data) {
            @strongify(self);
            if (self.delegate && [self.delegate respondsToSelector:@selector(didSelectDevice:)]) {
                [self.delegate didSelectDevice:data];
            }
        };
    }
    return _searchTopView;
}

- (MRKSearchTopStatusView *)topStatusView {
    if(!_topStatusView) {
        _topStatusView = [[MRKSearchTopStatusView alloc] init];
        _topStatusView.status = SearchTopSearcedList;
    }
    return _topStatusView;
}

- (UIScrollView *)scView {
    if(!_scView) {
        _scView = [[UIScrollView alloc] init];
        _scView.delegate = self;
    }
    return _scView;
}

@end







#import "NoBlindingTableViewCell.h"
@interface MRKTypeSearchView ()<UITableViewDelegate , UITableViewDataSource>
@property (nonatomic , strong) MRKSearchTopStatusView *topStatusView;
@property (nonatomic , strong) UITableView *tableView;
@property (nonatomic , strong) MRKSearchAnimationView *animationView; ///搜索页面
@property (nonatomic , strong) MRKTypeSearchFailView *searchFailView;
@property (nonatomic , strong) UIView *searchBottomView;
@property (nonatomic , strong) UIButton *reSearchBtn;
@property (nonatomic , strong) YYLabel *searchTipLab;
@end

@implementation MRKTypeSearchView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if(self) {
        
        [self addSubview:self.topStatusView];
        [self addSubview:self.animationView];
        
        [self addSubview:self.tableView];
        [self addSubview:self.searchFailView];
        
        [self addSubview:self.searchBottomView];
        [self.searchBottomView addSubview:self.reSearchBtn];
        [self.searchBottomView addSubview:self.searchTipLab];
        
        self.deviceArray = [NSArray array];
    }
    return self;
}

- (void)layoutSubviews {
    [super layoutSubviews];
    
    [self.topStatusView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.right.equalTo(@(WKDHPX(0)));
        make.height.equalTo(@(WKDHPX(50)));
    }];
    
    [self.animationView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsMake(WKDHPX(50), 0, 0, 0));
    }];
    
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsMake(WKDHPX(50), 0, 0, 0));
    }];
    
    [self.searchFailView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsMake(0, 0, 0, 0));
    }];
    
    
    [self.searchBottomView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.mas_left);
        make.right.equalTo(self.mas_right);
        make.bottom.equalTo(self.mas_bottom);
        make.height.mas_equalTo(WKDHPX(110) + kSafeArea_Bottom);
    }];
    [self.reSearchBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.searchBottomView.mas_top).offset(WKDHPX(10));
        make.centerX.equalTo(self.mas_centerX);
        make.width.equalTo(@(RealScreenWidth - WKDHPX(100)));
        make.height.equalTo(@(WKDHPX(50)));
    }];
    [self.searchTipLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.reSearchBtn.mas_bottom).offset(WKDHPX(15));
        make.centerX.equalTo(self.mas_centerX);
        make.height.equalTo(@(WKDHPX(20)));
    }];
}

- (void)setDeviceArray:(NSArray *)deviceArray {
    _deviceArray = deviceArray;
    if(deviceArray.count) {
        self.animationView.hidden = YES;
        self.tableView.hidden = NO;
    } else {
        self.animationView.hidden = NO;
        self.tableView.hidden = YES;
    }
    
    [self.tableView reloadData];
}

- (void)setProductID:(NSString *)productID {
    _productID = productID;
    self.animationView.productID = productID;
}


- (void)startSearch {
    [self.animationView startSearch];
}

- (void)searchFail {
    [self.animationView searchFail];
}

- (void)searchProgress:(CGFloat)progress {
    [self.animationView searchProgress:progress];
}

- (void)searchAction:(UIButton *)sender {
    self.searchFailView.hidden = YES;
    self.searchBottomView.hidden = YES;
    
    sender.traceEventId = @"btn_search_again";
    sender.tracePara = @{@"product_id": _productID};
    
    if(self.delegate && [self.delegate respondsToSelector:@selector(reStartSearch)]) {
        [self.delegate reStartSearch];
    }
}

- (void)setSearchStatus:(SearchTopStatus)searchStatus {
    _searchStatus = searchStatus;
    
    switch (searchStatus) {
        case SearchTopSearching: {
            self.topStatusView.status = SearchTopTypeSearching;
            [self startSearch];
            
        }  break;
            
        case SearchTopSearchEnd: {
            self.searchBottomView.hidden = NO;
            if (self.deviceArray.count) {
                self.topStatusView.status = SearchTopSearcedList;
                self.searchFailView.hidden = YES;
            } else {
                self.topStatusView.status = SearchTopTypeSearchFail;
                self.searchFailView.hidden = NO;
                [self searchFail];
                {
                    ReportMrkLogParms(1, @"未搜索到设备", @"page_nodevice_equipment", @"", nil, 0, nil);
                }
            }
            
        }  break;
            
        default:
            break;
    }
}


#pragma mark - delegate
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.deviceArray.count;
}
- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return WKDHPX(140);
}
- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    NoBlindingTableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:NSStringFromClass([NoBlindingTableViewCell class]) forIndexPath:indexPath];
    BluetoothModel *bModel = self.deviceArray[indexPath.row];
    [cell configWithItem:bModel];
    cell.tag = indexPath.row;
    @weakify(self , cell);
    cell.bindActionBlock = ^(UIButton * sender) {
        @strongify(self , cell);
        MRKDeviceModel *item = (MRKDeviceModel *)(bModel.equipmentModel);
        __block RACDisposable *dispose = [[[[MRKConnectStatusManager sharedInstance] connectStatusWithProductID:item.productId name:item.bluetoothName] distinctUntilChanged] subscribeNext:^(NSNumber * x) {
            @strongify(cell);
            cell.status = x.intValue;
            if(x.intValue == DeviceConnected || x.intValue == DeviceDisconnect){
                [dispose dispose];
            }
        }];
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(didSelectDevice:)]) {
            [self.delegate didSelectDevice:self.deviceArray[indexPath.row]];
        }
    };
    
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    return cell;
}




#pragma mark - lazy
- (UITableView *)tableView {
    if(!_tableView) {
        UITableView *tab = [[UITableView alloc]initWithFrame:self.bounds style:UITableViewStylePlain];
        [tab registerClass:[NoBlindingTableViewCell class] forCellReuseIdentifier:NSStringFromClass([NoBlindingTableViewCell class])];
        tab.delegate = self;
        tab.dataSource = self;
        tab.tableHeaderView = [UIView new];
        tab.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView = tab;
    }
    return _tableView;
}

- (MRKSearchTopStatusView *)topStatusView {
    if(!_topStatusView) {
        _topStatusView = [MRKSearchTopStatusView new];
        _topStatusView.status = SearchTopTypeSearching;
    }
    return _topStatusView;
}

- (MRKSearchAnimationView *)animationView{
    if (!_animationView){
        _animationView = [[MRKSearchAnimationView alloc] init];
    }
    return _animationView;
}

- (MRKTypeSearchFailView *)searchFailView{
    if (!_searchFailView){
        _searchFailView = [[MRKTypeSearchFailView alloc] init];
        _searchFailView.hidden = YES;
    }
    return _searchFailView;
}

- (UIView *)searchBottomView{
    if (!_searchBottomView){
        _searchBottomView = [[UIView alloc] init];
        _searchBottomView.backgroundColor = UIColor.whiteColor;
        _searchBottomView.hidden = YES;
    }
    return _searchBottomView;
}
- (YYLabel *)searchTipLab {
    if(!_searchTipLab) {
        _searchTipLab = [YYLabel new];
        _searchTipLab.textAlignment = NSTextAlignmentCenter;
        @weakify(self);
        NSMutableAttributedString *text = [[NSMutableAttributedString alloc] initWithString:@"搜索不到设备，"];
        text.font = [UIFont systemFontOfSize:WKDHPX(13.0)];
        text.color = UIColorHex(#666666);
        NSMutableAttributedString *text1 = [[NSMutableAttributedString alloc] initWithString:@"联系客服反馈问题"];
        text1.font = [UIFont systemFontOfSize:WKDHPX(13.0)];
        text1.color = UIColorHex(#16D2E3);
        text1.underlineStyle = NSUnderlineStyleSingle;
        [text1 setTextHighlightRange:text1.rangeOfAll
                               color:UIColorHex(#16D2E3)
                     backgroundColor:UIColorHex(#F3F5F9)
                           tapAction:^(UIView *containerView, NSAttributedString *text, NSRange range, CGRect rect) {
            NSLog(@"Tap: %@", [text.string substringWithRange:range]);
            @strongify(self);
            if (self.delegate && [self.delegate respondsToSelector:@selector(openMeritServer)]) {
                [self.delegate openMeritServer];
            }
            ReportMrkLogParms(2, @"未搜索到设备", @"page_nodevice_equipment", @"btn_problem_feedback", nil, 0, nil);
        }];
        [text appendAttributedString:text1];
        _searchTipLab.attributedText = text;
    }
    return _searchTipLab;
}
- (UIButton *)reSearchBtn {
    if(!_reSearchBtn) {
        UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
        btn.traceEventId = @"btn_search_again";
        [btn setTitle:@"重新搜索" forState:UIControlStateNormal];
        [btn setTitleColor:UIColorHex(ffffff) forState:UIControlStateNormal];
        btn.titleLabel.font = [UIFont fontWithName:fontNameMeDium size:WKDHPX(17)];
        btn.backgroundColor = UIColorHex(#16D2E3);
        btn.layer.cornerRadius = WKDHPX(50)/2;
        [btn addTarget:self action:@selector(searchAction:) forControlEvents:UIControlEventTouchUpInside];
        _reSearchBtn = btn;
    }
    return _reSearchBtn;
}
@end











#import "LOTAnimationView.h"
@interface MRKSearchAnimationView ()
@property (nonatomic , strong) UIImageView *searchImageView;  ///搜索图片
@property (nonatomic , strong) YYLabel *searchLabel;          ///搜索文案
@property (nonatomic , strong) UIProgressView *progressView;  ///搜索时间进度条
@end

@implementation MRKSearchAnimationView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if(self) {
        self.backgroundColor = UIColor.whiteColor;
        
        [self addSubview:self.searchImageView];
        [self addSubview:self.progressView];
        [self addSubview:self.searchLabel];
    }
    return self;
}

- (void)layoutSubviews {
    [super layoutSubviews];
    
    [self.searchImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.mas_centerX);
        make.top.equalTo(self.mas_top).offset(WKDHPX(65));
        make.size.equalTo(@(CGSizeMake(WKDHPX(290), WKDHPX(290))));
    }];
    [self.searchLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.searchImageView.mas_bottom).offset(WKDHPX(-25));
        make.centerX.equalTo(self.searchImageView.mas_centerX);
        make.left.equalTo(self.searchImageView.mas_left).offset(WKDHPX(10));
        make.right.equalTo(self.searchImageView.mas_right).offset(WKDHPX(-10));
    }];
    [self.progressView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.searchLabel.mas_top).offset(WKDHPX(-15));
        make.centerX.equalTo(self.searchImageView.mas_centerX);
        make.width.equalTo(@(WKDHPX(240)));
    }];
}

- (void)setProductID:(NSString *)productID {
    _productID = productID;
}

- (void)startSearch {
    self.searchImageView.image = [UIImage imageNamed:@""];
    
    LOTAnimationView *img = [LOTAnimationView new];
    [img setAnimationNamed:@"ScanBack.json"];
    img.loopAnimation = YES;
    [self.searchImageView addSubview:img];
    [img mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.right.bottom.equalTo(@0);
    }];
    img.tag = 1000;
    [img play];
    
    LOTAnimationView *img2 = [LOTAnimationView new];
    switch (self.productID.intValue) {
        case BicycleEquipment:
            [img2 setAnimationNamed:@"ScanBicycle.json"];
            break;
        case BoatEquipment:
            [img2 setAnimationNamed:@"ScanBoat.json"];
            break;
        case EllipticalEquipment:
            [img2 setAnimationNamed:@"ScanElliptical.json"];
            break;
        case TreadmillEquipment:
            [img2 setAnimationNamed:@"ScanTreadmill.json"];
            break;
        default:
            [img2 setAnimationNamed:@"ScanUniversal.json"];
            break;
    }
    img2.loopAnimation = YES;
    [self.searchImageView addSubview:img2];
    [img2 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.right.bottom.equalTo(@0);
    }];
    img2.tag = 1001;
    [img2 play];
    
    [self updateSearchText];
}

- (void)updateSearchText {
    self.searchLabel.textAlignment = NSTextAlignmentCenter;

    NSString *string = @"请确认设备在手机附近";
    switch (self.productID.intValue) {
        case BicycleEquipment: {
           string = @"保持设备处于通电状态\n请踩踏激活蓝牙后再搜索设备\n请确认设备在手机附近";
        }  break;
        case BoatEquipment:{
            string = @"保持设备处于通电状态\n请拉动激活蓝牙后再搜索设备\n请确认设备在手机附近";
        }  break;
        case EllipticalEquipment: {
            string = @"保持设备处于通电状态\n请踩踏激活蓝牙后再搜索设备\n请确认设备在手机附近";
        }  break;
        case TreadmillEquipment:{
            string = @"保持设备处于通电状态\n请确认设备在手机附近";
        }  break;
        default:  break;
    }
    
    NSMutableAttributedString *text = [[NSMutableAttributedString alloc] initWithString:string];
    text.font = [UIFont systemFontOfSize:WKDHPX(13.0) weight:UIFontWeightMedium];
    text.color = UIColorHex(#363A44);
    text.lineSpacing = 5;
    self.searchLabel.attributedText = text;
    self.searchLabel.textAlignment = NSTextAlignmentCenter;
}

- (void)searchFail {
    if([self.searchImageView viewWithTag:1000]) {
        [[self.searchImageView viewWithTag:1000] removeFromSuperview];
    }
    
    if([self.searchImageView viewWithTag:1001]) {
        [[self.searchImageView viewWithTag:1001] removeFromSuperview];
    }
    
    
    NSString *imageName = @"search_fail";
    switch (self.productID.intValue) {
        case BicycleEquipment: {
            imageName = @"pic_s_fail";
        }  break;
        case BoatEquipment:{
            imageName = @"pic_r_fail";
        }  break;
        case EllipticalEquipment: {
            imageName = @"pic_e_fail";
        }  break;
        case TreadmillEquipment:{
            imageName = @"pic_x_fail";
        }  break;
        default:  break;
    }
    self.searchImageView.image = [UIImage imageNamed:imageName];
    
    NSString *name = [MRKEquipmentTypeData nameFromProductId:self.productID];
    NSString *string = [NSString stringWithFormat:@"未搜索到%@,请尝试以下几种方法：\n1.请确认设备已开机并在蓝牙连接范围内;\n2.请确认设备未被他人连接;\n3.若仍搜索不到，建议断电重启或手机蓝牙取消配对后重新搜索。", name];
    NSMutableAttributedString *text = [[NSMutableAttributedString alloc] initWithString:string];
    text.font = [UIFont systemFontOfSize:WKDHPX(13.0) weight:UIFontWeightMedium];
    text.color = UIColorHex(#363A44);
    text.lineSpacing = 5;
    self.searchLabel.attributedText = text;
    self.searchLabel.textAlignment = NSTextAlignmentLeft;
}

- (void)searchProgress:(CGFloat)progress {
    if(progress == 1.0) {
        self.progressView.progressTintColor = UIColorHex(#F89E18);
    }else {
        self.progressView.progressTintColor = UIColorHex(#00DDF1);
    }
    self.progressView.progress = progress;
}

- (UIProgressView *)progressView {
    if(!_progressView) {
        UIProgressView *pv = [UIProgressView new];
        pv.progressTintColor = UIColorHex(#00DDF1);
        pv.trackTintColor = UIColorHex(#F3F5F9);
        _progressView = pv;
    }
    return _progressView;
}

- (YYLabel *)searchLabel {
    if(!_searchLabel) {
        YYLabel *l = [YYLabel new];
        l.textColor = UIColorHex(#363A44);
        l.font = [UIFont systemFontOfSize:WKDHPX(13.0) weight:UIFontWeightMedium];
        l.numberOfLines = 0;
        l.textAlignment = NSTextAlignmentCenter;
        l.preferredMaxLayoutWidth = WKDHPX(290) - 2*WKDHPX(10);
        NSMutableAttributedString *text = [[NSMutableAttributedString alloc] initWithString:@"确认设备在手机附近\n保持设备处于通电状态"];
        text.font = [UIFont systemFontOfSize:WKDHPX(13.0) weight:UIFontWeightMedium];
        text.color = UIColorHex(#363A44);
        text.lineSpacing = 5;
        l.attributedText = text;
        _searchLabel = l;
    }
    return _searchLabel;
}

- (UIImageView *)searchImageView {
    if(!_searchImageView) {
        UIImageView *img = [UIImageView new];
        img.image = [UIImage imageNamed:@""];
        _searchImageView = img;
    }
    return _searchImageView;
}
@end














#import "MRKConnectHelpView.h"

@interface MRKTypeSearchFailView ()
@property (nonatomic, strong) UIScrollView *pageScrollerView;          ///搜索文案
@property (nonatomic, strong) YYLabel *searchFailLab;                  ///搜索文案
@property (nonatomic, strong) MRKConnectHelpView *helpView;            ///搜索图片
@end

@implementation MRKTypeSearchFailView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if(self) {
        self.backgroundColor = [UIColor whiteColor];
        
        [self addSubview:self.pageScrollerView];
        [self.pageScrollerView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.mas_equalTo(UIEdgeInsetsZero);
        }];
        [self layoutSubviewsUI];
    }
    return self;
}

- (void)layoutSubviewsUI {
    [self.pageScrollerView addSubview:self.searchFailLab];
    [self.searchFailLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.pageScrollerView.mas_top).offset(WKDHPX(50));
        make.centerX.equalTo(self.pageScrollerView.mas_centerX);
        make.height.equalTo(@(WKDHPX(35)));
    }];
    
    [self.pageScrollerView addSubview:self.helpView];
    [self.helpView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.searchFailLab.mas_bottom).offset(WKDHPX(50));
        make.centerX.equalTo(self.mas_centerX);
        make.width.equalTo(@(RealScreenWidth));
        make.bottom.equalTo(self.pageScrollerView.mas_bottom);
    }];
}

- (UIScrollView *)pageScrollerView{
    if (!_pageScrollerView) {
        _pageScrollerView = [[UIScrollView alloc] init];
        _pageScrollerView.pagingEnabled = NO;
        _pageScrollerView.backgroundColor = [UIColor clearColor];
        _pageScrollerView.showsVerticalScrollIndicator = NO;
    }
    return _pageScrollerView;
}


- (YYLabel *)searchFailLab {
    if(!_searchFailLab) {
        YYLabel *l = [YYLabel new];
        l.textAlignment = NSTextAlignmentCenter;
        l.attributedText = ({
            NSMutableAttributedString *text = [[NSMutableAttributedString alloc] initWithString:@"未搜索到设备"];
            text.font = [UIFont systemFontOfSize:WKDHPX(22) weight:UIFontWeightMedium];
            text.color = UIColorHex(#FF6A2A);
            
            UIImage *image = [UIImage imageNamed:@"device_search_fail"];
            NSMutableAttributedString *ImageText = [NSMutableAttributedString attachmentStringWithContent:image
                                                                                              contentMode:UIViewContentModeCenter
                                                                                           attachmentSize:image.size
                                                                                              alignToFont:[UIFont systemFontOfSize:WKDHPX(22) weight:UIFontWeightMedium]
                                                                                                alignment:YYTextVerticalAlignmentCenter];
            ImageText.baseWritingDirection = NSWritingDirectionLeftToRight;
            ImageText.writingDirection = @[@(NSWritingDirectionLeftToRight | NSWritingDirectionOverride)];
            [text insertAttributedString:ImageText atIndex:0];
            text;
        });
        _searchFailLab = l;
    }
    return _searchFailLab;
}

- (MRKConnectHelpView *)helpView {
    if(!_helpView) {
        _helpView = [MRKConnectHelpView new];
        _helpView.backgroundColor = UIColor.whiteColor;
        
        _helpView.layer.shadowColor = [UIColor.blackColor colorWithAlphaComponent:0.7].CGColor;
        _helpView.layer.shadowOpacity = .1;
        _helpView.layer.shadowRadius = 5;
        _helpView.layer.shadowOffset = CGSizeMake(0, -3);
        // MrkCornerMaskWithViewRadius(_helpView, ViewRadiusMake(10, 10, 0, 0));
    }
    return _helpView;
}

@end







@implementation MRKSearchBlueAuthView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if(self) {
        [self addSubviews];
    }
    return self;
}

- (void)addSubviews {
    self.backgroundColor = [UIColor whiteColor];
    
    UIImageView *imgv = [[UIImageView alloc] init];
    imgv.image = [UIImage imageNamed:@"pic_turn_on_bluetooth"];
    [self addSubview:imgv];
    [imgv mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.mas_centerX);
        make.centerY.equalTo(self.mas_centerY).offset(WKDHPX(-70));
        make.size.equalTo(@(CGSizeMake(WKDHPX(251), WKDHPX(128))));
    }];
    
    UILabel *l = [[UILabel alloc] init];
    l.text = @"请开启蓝牙，以便查找附近蓝牙设备";
    l.textColor = UIColorHex(#363A44);
    l.font = kSystem_Font_NoDHPX(15);
    l.textAlignment = NSTextAlignmentCenter;
    [self addSubview:l];
    [l mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(imgv.mas_bottom).offset(WKDHPX(24));
        make.centerX.equalTo(imgv.mas_centerX);
    }];
    
    
    UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
    [btn setTitle:@"立即开启" forState:UIControlStateNormal];
    [btn setTitleColor:UIColorHex(ffffff) forState:UIControlStateNormal];
    btn.titleLabel.font = [UIFont fontWithName:fontNameMeDium size:WKDHPX(17.0)];
    btn.backgroundColor = UIColorHex(#16D2E3);
    btn.layer.cornerRadius = WKDHPX(50)/2;
    [btn addTarget:self action:@selector(openBlueAction) forControlEvents:UIControlEventTouchUpInside];
    [self addSubview:btn];
    [btn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.mas_bottom).offset(-(kSafeArea_Bottom + WKDHPX(20)));
        make.centerX.equalTo(self.mas_centerX);
        make.left.equalTo(@(WKDHPX(50)));
        make.right.equalTo(@(WKDHPX(-50)));
        make.height.equalTo(@(WKDHPX(50)));
    }];
}

- (void)openBlueAction {
    if(self.openBlock){
        self.openBlock(@1);
    }
}

@end
