//
//  MRKDeviceDetailController.m
//  Student_IOS
//
//  Created by <PERSON><PERSON> on 2023/8/4.
//

#import "MRKDeviceDetailController.h"
#import "DFUManager.h"
#import "BlueCheckOTAManager.h"
#import "MRKInputAlertView.h"
#import "MrkMeritHRDealManager.h"
#import "MRKRateWarnModel.h"
#import "MRKDeviceURLRequest.h"
#import "NSString+StringSize.h"
#import "NSAttributedString+SJMake.h"
#import "MRKDeviceConnectAlertView.h"

#import "MRKHeartWarnViewController.h"
#import "IntroducePeopleWebView.h"
#import "MRKOTAUpdateViewController.h"
#import "MRKChangeAccountViewController.h"
#import "MRKTrainingSetController.h"
#import "BlueAuthAlertView.h"

#import "BlufiManager.h"
#import "MRKDeviceManager.h"
#import "MRKGCDTimer.h"
//#import "WeightDeviceManager.h"

#import "MrkGeneralAlertView.h"
#import "Login.h"

@interface MRKDeviceDetailController ()<UITableViewDelegate,UITableViewDataSource> {
    NSString *_productType;
}
@property (nonatomic, strong) UITableView *detailTableView;
@property (nonatomic, strong) DeviceDetailHeaderView *tableHeaderView;
@property (nonatomic, strong) UIView *tableFooterView;
@property (nonatomic, strong) UILabel *updateOtaLabel;
@property (nonatomic, strong) DeviceHeartView *heartRateView;
@property (nonatomic, strong) UILabel *deviceGears;
@property (nonatomic, strong) UIButton *unBindBtn;
@property (nonatomic, strong) UIButton *weightInBtn; ///体脂称称量体重

@property (nonatomic, strong) OTAModel *otaModel;
@property (nonatomic, strong) NSMutableArray *dataSourceArr;
@property (nonatomic, strong) NewConnectManager *nConnectManager;///新的连接类
@property (nonatomic, assign) BOOL autoDisconnect; ///是否是主动断开连接
@property (nonatomic, assign) BOOL switchOnStatsu;

@property (nonatomic, strong) UIView *claimView; /// 认领
@property (nonatomic, strong) UILabel *claimLabel; /// 认领内容
///
@property (nonatomic, assign) BOOL wifiStatus;
@property (nonatomic, strong) MRKGCDTimer *wifiTimer; /// 刷新排行榜，10s
@property (nonatomic, strong) dispatch_queue_t wifiQueue;

@property (nonatomic, assign) BOOL entryClaimPage; // 进入认领返回需要主动给屏端推送数据

///
@end

static const char *BackgroundWifiTimerQueueContext = "BackgroundWifiTimerQueueContext";

@implementation MRKDeviceDetailController


#pragma mark - lazy
- (NewConnectManager *)nConnectManager {
    if (!_nConnectManager) {
        _nConnectManager = [[NewConnectManager alloc]init];
        if (self.model.productId.intValue == HeartEquipment) {
            _nConnectManager = [MrkMeritHRDealManager shareManager].connectManager;
        }
    }
    return _nConnectManager;
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    [self judgeBlufiDevice];
    [self refreshOpenAutoResistance];
}

- (void)viewDidDisappear:(BOOL)animated {
    [super viewDidDisappear:animated];
    [self removeWifiTimer];
}

- (void)dealloc{
    [self removeWifiTimer];
}

- (void)removeWifiTimer{
    if (self.wifiTimer) {
        [self.wifiTimer invalidate];
        self.wifiTimer = nil;
    }
}

#pragma mark - 添加实景挑战模式排行榜定时器
- (void)addRequestWifiTimer {
    if (self.wifiTimer) {
        [self.wifiTimer invalidate];
        self.wifiTimer = nil;
    }
    self.wifiQueue = dispatch_queue_create("com.merit.backQueue", DISPATCH_QUEUE_CONCURRENT);
    dispatch_queue_set_specific(self.wifiQueue, (__bridge const void *)(self), (void *)BackgroundWifiTimerQueueContext, NULL);
    self.wifiTimer = [MRKGCDTimer timerWithTimeInterval:2.0
                                             target:self
                                           selector:@selector(wifiTimerCallback:)
                                           userInfo:nil
                                            repeats:YES
                                      dispatchQueue:self.wifiQueue];
    [self.wifiTimer fire];
}

- (void)wifiTimerCallback:(MRKGCDTimer *)timer {
    [[BlufiManager shareManager] getDeviceStatus];
}

/// 判断是不是小彩屏设备  flutter 运动记录认领
- (void)judgeBlufiDevice{
    
    if (self.entryClaimPage) { // 发送通知，更新屏端数据
        self.entryClaimPage = NO;
        [[NSNotificationCenter defaultCenter] postNotificationName:@"kUpdateHomeSportDataNotification" object:@{@"productId": self.model.productId}];
    }
    
    if ([[MRKDeviceManager shareManager] isJudgeBlufiDevice: self.model.communicationType]) {
        /// 请求认领列表
        [self requestClaimList];
       
        @weakify(self);
        [BlufiManager shareManager].wifiStateBlock = ^(BOOL status, NSString * _Nullable name) {
            @strongify(self);
            if (status != self.wifiStatus) {
                self.wifiStatus = status;
                [self.detailTableView reloadData];
            }
        };
        
        /// 处理wifi连接的显示
        if([BlueDataStorageManager isConnectDeviceWithProductID:self.model.productId name:self.model.bluetoothName]) {
            //获取状态
            [[BlufiManager shareManager] getDeviceStatus];
            
            [self addRequestWifiTimer]; // 添加定时器
           
        }
    } else {
        if (self.claimView) {
            self.claimView.hidden = YES;
        }
    }
}

- (void)requestClaimList{
    @weakify(self);
    [MRKDeviceURLRequest requestDeviceLeaveData:@{ @"snCode": self.model.snCode } success:^(NSArray *list) {
        @strongify(self);
        NSInteger sum = 0;
        for (int i = 0; i < list.count; i ++) {
            sum += (((NSArray *)list[i][@"items"]).count ?: 0);
        }
        if (sum > 0) {
            if (self.claimView) {
                self.claimView.hidden = NO;
            } else {
                [self addClaimSubView];
            }
            self.claimLabel.text = [NSString stringWithFormat:@"有%ld条离线的运动记录", sum];
        } else {
            if (self.claimView) {
                self.claimView.hidden = YES;
            }
        }
    } fail:^(id data) {
        NSString *log = [NSString stringWithFormat:@"&&查找当前设备下所有的离线训练数据,调用接口返回失败结果&：%@", data];
        MLog(log);
        
        if (self.claimView) {
            self.claimView.hidden = NO;
        } else {
            [self addClaimSubView];
        }
        
    }];
    
}

- (void)addClaimSubView{
    UIView *claimView = [[UIView alloc] init];
    claimView.cornerRadius = WKDHPX(8);
    claimView.backgroundColor = [UIColor whiteColor];
    [self.mrkContentView addSubview:claimView];
    [claimView bringSubviewToFront:self.mrkContentView];
    [claimView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.mrkContentView.mas_top).offset(kNavBarHeight + WKDHPX(16));
        make.left.equalTo(self.mrkContentView.mas_left).offset(WKDHPX(16));
        make.right.equalTo(self.mrkContentView.mas_right).offset(-WKDHPX(16));
        make.height.equalTo(@(WKDHPX(36)));
    }];
    
    UILabel *claimLabel = [[UILabel alloc] init];
    claimLabel.text = @"有--条离线的运动记录";
    claimLabel.textColor = [UIColor colorWithHexString:@"#363A44"];
    claimLabel.font = kSystem_Font_NoDHPX(WKDHPX(15));
    [claimView addSubview:claimLabel];
    [claimLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(claimView.mas_left).offset(WKDHPX(30));
        make.right.equalTo(claimView.mas_right).offset(-WKDHPX(60));
        make.centerY.equalTo(claimView);
    }];
    
    UIButton *closeClaimBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [closeClaimBtn setImage:[UIImage imageNamed:@"icon_close-3"] forState:UIControlStateNormal];
    [closeClaimBtn addTarget:self action:@selector(closeClaimAction:) forControlEvents:UIControlEventTouchUpInside];
    [claimView addSubview:closeClaimBtn];
    [closeClaimBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.bottom.equalTo(claimView);
        make.left.equalTo(@(WKDHPX(6)));
        make.width.equalTo(@(WKDHPX(24)));
    }];
    
    UIButton *claimBtn = [UIButton buttonWithType:UIButtonTypeSystem];
    [claimBtn setTitle:@"去认领" forState:UIControlStateNormal];
    [claimBtn setTitleColor:[UIColor colorWithHexString:@"#16D2E3"] forState:UIControlStateNormal];
    claimBtn.titleLabel.font = kSystem_Font_NoDHPX(WKDHPX(15));
    [claimBtn addTarget:self action:@selector(claimAction:) forControlEvents:UIControlEventTouchUpInside];
    [claimView addSubview:claimBtn];
    [claimBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.right.bottom.equalTo(claimView);
        make.width.equalTo(@(WKDHPX(60)));
    }];
    
    self.claimView = claimView;
    self.claimLabel = claimLabel;
}

/// 去认领 flutter 认领页面
- (void)claimAction:(UIButton *)sender{
    self.entryClaimPage = YES;
    SJRouteRequest *router = [[SJRouteRequest alloc] initWithPath:@"router_claim" parameters:@{@"snCode": self.model.snCode }];
    [SJRouter.shared handleRequest:router completionHandler:nil];
}

/// 关闭认领view
- (void)closeClaimAction:(UIButton *)sender{
    self.claimView.hidden = YES;
}

- (void)viewDidLoad {
    self.tracePageId = @"page_equipment_detail";
    self.tracePara = @{@"product_id": self.model.productId,@"model_id": self.model.modelId, @"device_id":self.model.deviceId};
    [super viewDidLoad];
    self.navTitle = @"设备管理";
    self.mrkContentView.backgroundColor = [UIColor colorWithHexString:@"#F3F5F9"];
    _productType = self.model.productId;
    
    [self UIDraw];
    [self loadModel];
    
    
//    //监听首页刷新成功返回
//    NSArray *array = self.navigationController.childViewControllers ;
//    array = [[array reverseObjectEnumerator] allObjects];
//    if([array.lastObject isKindOfClass:NSClassFromString(@"MRKHomePageController")]) {
//        [[NSNotificationCenter defaultCenter] addObserver:self
//                                                 selector:@selector(homeRefreshNotification:)
//                                                     name:@"kMRKHomePageController_refresh_Notification"
//                                                   object:nil];
//    }
}


- (void)loadModel {
    
    @weakify(self);
    if (_productType.intValue == FatScaleEquipment) {
        
        ///体脂秤
    } else {
        
        [[[[[MRKConnectStatusManager sharedInstance] connectStatusWithProductID:self.model.productId name:self.model.name] distinctUntilChanged] takeUntil:[self rac_willDeallocSignal]] subscribeNext:^(NSNumber * x) {
            @strongify(self);
            self.connectStatus = x;
            if (x.intValue == DeviceDisconnect) {
                if (self.autoDisconnect) {
                    [AppDelegate errorView:@"已断开连接"];
                    self.autoDisconnect = NO;
                }
                if ([[MRKDeviceManager shareManager] isJudgeBlufiDevice: self.model.communicationType]) { // 小彩屏
                    [self removeWifiTimer]; // 移除wifi定时器
                    if (self.wifiStatus) {
                        self.wifiStatus = NO;
                        [self.detailTableView reloadData];
                    }
                }
            } else if(x.intValue == DeviceConnected){
                if ([[MRKDeviceManager shareManager] isJudgeBlufiDevice: self.model.communicationType]) { // 小彩屏
                    @weakify(self);
                    [BlufiManager shareManager].wifiStateBlock = ^(BOOL status, NSString * _Nullable name) {
                        @strongify(self);
                        if (status != self.wifiStatus) {
                            self.wifiStatus = status;
                            [self.detailTableView reloadData];
                        }
                    };
                    [self addRequestWifiTimer]; // 添加定时器
                }
            }
        }];
    }
    
    
    [[RACObserve(self, connectStatus) distinctUntilChanged] subscribeNext:^(NSNumber *x) {
        @strongify(self);
        self.tableHeaderView.connectStatus = x.intValue;
        if (x.intValue == DeviceConnected) {
            ///检查ota 刷新更新小角标
            [self checkOTAInfo];
        }
    }];
    
    
    ///心率带监听
    if (_productType.intValue == HeartEquipment) {
        @weakify(self);
        [RACObserve([MrkMeritHRDealManager shareManager], deviceBattery) subscribeNext:^(NSNumber *x) {
            @strongify(self);
            [self.tableHeaderView setDevicePower:x];
        }];
        
        [RACObserve([MrkMeritHRDealManager shareManager], heartRate) subscribeNext:^(NSNumber * x) {
            @strongify(self);
            [self.heartRateView updateHeartRate:x];
        }];
    }
}

- (void)checkOTAInfo {
    if (self.model.deviceUserRelId && self.model.isConnect.boolValue) {
        NSLog(@"~~~~~~已经连接,检测ota，刷新小角标~~~~~~~");
        @weakify(self);
        [BlueCheckOTAManager checkDeviceOTA:self.model success:^(id data) {
            @strongify(self);
            self.otaModel = data;
            [self.detailTableView reloadData];
        } failure:^(id data) {
            
        }];
    }
}


- (void)UIDraw {
    
    if (!self.model.bluetoothName) return;
    
    self.detailTableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
    self.detailTableView.separatorStyle = UITableViewCellSeparatorStyleSingleLine;
    self.detailTableView.backgroundColor = [UIColor clearColor];
    self.detailTableView.separatorColor = LineColor;
    self.detailTableView.delegate = self;
    self.detailTableView.dataSource= self;
    self.detailTableView.showsVerticalScrollIndicator = NO;
    [self.mrkContentView addSubview:self.detailTableView];
    [self.detailTableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsMake(kNavBarHeight, DHPX(16), 0, DHPX(16)));
    }];
    
    ///设置header
    self.tableHeaderView.model = self.model;
    self.detailTableView.tableHeaderView = self.tableHeaderView;
    [self.tableHeaderView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(self.tableHeaderView.superview);
    }];
    
    [self.detailTableView.tableHeaderView layoutIfNeeded];
    self.detailTableView.tableHeaderView = self.tableHeaderView;
    
    
    ///设置footer
    UIView *tableFooterView = [[UIView alloc] init];
    tableFooterView.backgroundColor = [UIColor colorWithHexString:@"#F3F5F9"];
    [self.mrkContentView addSubview:tableFooterView];
    [tableFooterView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.mrkContentView.mas_centerX);
        make.bottom.equalTo(self.mrkContentView.mas_bottom);
        make.left.equalTo(self.mrkContentView.mas_left);
        make.right.equalTo(self.mrkContentView.mas_right);
    }];
    
    ///体脂称添加测量按钮
    if(_productType.intValue == FatScaleEquipment) {
        [tableFooterView addSubview:self.unBindBtn];
        [self.unBindBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(tableFooterView.mas_bottom).offset(-(kSafeArea_Bottom +WKDHPX(20)));
            make.centerX.equalTo(tableFooterView.mas_centerX);
            make.width.mas_equalTo(WKDHPX(280));
            make.height.mas_equalTo(WKDHPX(50));
        }];
        
        [tableFooterView addSubview:self.weightInBtn];
        [self.weightInBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(tableFooterView.mas_top).offset(DHPX(16));
            make.bottom.equalTo(self.unBindBtn.mas_top).offset(-WKDHPX(20));
            make.centerX.equalTo(tableFooterView.mas_centerX);
            make.width.mas_equalTo(WKDHPX(280));
            make.height.mas_equalTo(WKDHPX(50));
        }];
    } else {
        [tableFooterView addSubview:self.unBindBtn];
        [self.unBindBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(tableFooterView.mas_top).offset(DHPX(16));
            make.bottom.equalTo(tableFooterView.mas_bottom).offset(-(kSafeArea_Bottom +WKDHPX(20)));
            make.centerX.equalTo(tableFooterView.mas_centerX);
            make.width.mas_equalTo(WKDHPX(280));
            make.height.mas_equalTo(WKDHPX(50));
        }];
    }
    
    
    
    NSMutableArray *array = [NSMutableArray array];
    switch (_productType.intValue) {
        case FatScaleEquipment:{
            
            [array addObject:@"成员管理"];
            [array addObject:[NSString stringWithFormat:@"%@操作指南", self.model.productName]];
            [array addObject:@"固件更新"];
            if ([self.model.productManual isNotBlank]) {
                [array addObject:@"产品说明"];
            }
            if ([self.model.helpCenter isNotBlank]) {
                [array addObject:@"帮助中心"];
            }
            [self.dataSourceArr addObject:array];
            
        }   break;
            
        case HeartEquipment: {
            [self.dataSourceArr addObject:@[@"实时心率"]];
            
            [array addObject:@"心率预警"];
            [array addObject:@"心率百科"];
            [array addObject:@"固件更新"];
            if ([self.model.productManual isNotBlank]) {
                [array addObject:@"产品说明"];
            }
            if ([self.model.helpCenter isNotBlank]) {
                [array addObject:@"帮助中心"];
            }
            
            [self.dataSourceArr addObject:array];
        }   break;
            
        default:{
            
            if (self.model.isMerit.boolValue) {
                if (_productType.intValue == BicycleEquipment ||
                    _productType.intValue == BoatEquipment ||
                    _productType.intValue == EllipticalEquipment ) {
                    [self.dataSourceArr addObject:@[@"匹配调阻"]];
                }
            }
            
            if ([self.model.commandName isNotEmpty] && [self.model.commandUrl isNotEmpty]) {
                [array addObject:self.model.commandName];
            }
            
            if ([[MRKDeviceManager shareManager] isJudgeBlufiDevice: self.model.communicationType]) {
                [array addObject:@"WIFI连接"];
            }
            
            if (_productType.intValue != PowerEquipment && _productType.intValue != KettleBellEquipment) {
                [array addObject:[NSString stringWithFormat:@"%@操作指南", self.model.productName]];
            }
            
            if ([[MRKDeviceManager shareManager] isJudgeBlufiDevice: self.model.communicationType]) {
                [array addObject:@"固件版本号"];
            } else {
                [array addObject:@"固件更新"];
            }
            
            if ([self.model.productManual isNotEmpty]) {
                [array addObject:@"产品说明"];
            }
            
            if ([self.model.helpCenter isNotEmpty]) {
                [array addObject:@"帮助中心"];
            }
            
            
            ///屏蔽MRK-P25A爬楼机显示
            if (_productType.intValue == StairClimbEquiment && [self.model.bluetoothName hasPrefix:@"MRK-P25A"]) {
                
            }else {
                [self.dataSourceArr addObject:array];
            }
        } break;
    }
    
    [self.detailTableView reloadData];
    dispatch_async(dispatch_get_main_queue(), ^{
        self.detailTableView.contentInset = UIEdgeInsetsMake(0, 0, tableFooterView.height, 0);
    });
    
    ///查询当前设备大类匹配调阻状态
    [self refreshOpenAutoResistance];
}



- (void)refreshOpenAutoResistance {
    if (!self.model.isMerit.boolValue) return;
    
    if (_productType.intValue == BicycleEquipment ||
        _productType.intValue == BoatEquipment ||
        _productType.intValue == EllipticalEquipment) {
        ///匹配调阻开关状态
        [MRKBaseRequest mrkGetRequestUrl:@"/user/user-setting/get"
                                 andParm:@{@"type":@"2"}
                completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
            id data = [request.responseObject valueForKeyPath:@"data"];
            self.switchOnStatsu = [data boolValue];
            [self.detailTableView reloadData];
        } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
            
        }];
    }
}


//连接/断开链接
- (void)connectAction {
    [self connectDeviceAction];
    
    if (_connectBlock) {
        _connectBlock(self.model);
    }
}


- (void)editAction {
    NSLog(@"edit_name__%@" , self.model.deviceAlias);
    @weakify(self);
    MRKInputAlertView *input = [[MRKInputAlertView alloc] initWithTitle:@"添加设备备注" unit:@"" placeString:[self.model.deviceAlias isNotEmpty] ? self.model.deviceAlias : @"请输入1～15字符名称"];
    input.isCheckValid = NO;
    input.confirmTitle = @"确认" ;
    input.confimButtonBlock = ^(NSString *alias) {
        @strongify(self);
        [self updateDeviceAlias:alias];
    };
    [input show];
    
    input.confimButton.traceEventId = @"btn_equipment_detail_rename_confirm";
    input.cancelButton.traceEventId = @"btn_equipment_detail_rename_cancel";
}


- (void)updateDeviceAlias:(NSString *)alias {
    [MBProgressHUD showLodingWithMessage:@"" view:self.view];
    NSDictionary *para = @{
        @"deviceUserRelId":self.model.deviceUserRelId,
        @"deviceAlias":alias?:@""
    };
    [MRKDeviceURLRequest requestUpdateDeviceAlias:para success:^(id data) {
        [MBProgressHUD hideHUDForView:self.view];
        self.model.deviceAlias = alias;
        self.tableHeaderView.model = self.model;
        ///通知更新
        [[NSNotificationCenter defaultCenter] postNotificationName:@"updateDeviceName" object:self.model];
    } fail:^(id data) {
        [MBProgressHUD hideHUDForView:self.view];
    }];
}

#pragma mark - 测量体重
- (void)startMessure{
    [[RouteManager sharedInstance] jumpToWeightMeasureVC:self.model];
}

- (void)openBlueAction {
    CBManagerState x = [NewBluePeripheralManager sharedInstance].state;
    if (x == CBManagerStateUnauthorized ||
        x == CBManagerStateUnsupported ||
        x == CBManagerStateResetting ) {
        BlueAuthAlertView *bleAlertView = [[BlueAuthAlertView alloc]initWithAnimationStyle:MRKActionAlertViewTransitionStyleSlideFromBottom];
        [bleAlertView show];
    } else {
        BlueOpenAlertView *bleAlertView = [[BlueOpenAlertView alloc]initWithAnimationStyle:MRKActionAlertViewTransitionStyleSlideFromBottom];
        [bleAlertView show];
    }
}

#pragma mark - 连接/断开 设备
- (void)connectDeviceAction {
    
    ///体脂秤单独处理
    if (_productType.intValue == FatScaleEquipment) {
        return;
    }
    
    if (!self.model.isConnect.boolValue) {
        CBManagerState x = [NewBluePeripheralManager sharedInstance].state;
        if (x != CBManagerStatePoweredOn) {
            [self openBlueAction];
            return;
        }
        
        if (self.model.productId.intValue == FatScaleEquipment) {
            [self startMessure];
            return;
        }
        
        ///去连接
        [MBProgressHUD showLodingWithMessage:@"连接中..." view:self.view];
        @weakify(self);
        self.nConnectManager.connectStatusBlock = ^(NSNumber * data) {
            @strongify(self);
            [MBProgressHUD hideHUDForView:self.view];
        };
        [self.nConnectManager connectDeviceModel:self.model];
        return;
    }
    
    ///主动断连
    @weakify(self);
    [MRKDeviceConnectAlertView alertDisconnectDeviceView:^(id data) {
        @strongify(self);
        if([data intValue] == 1) {
            self.autoDisconnect = YES;
            [self.nConnectManager cancelConnectDeviceModel:self.model];
        }
    }];
}

#pragma mark - 解除绑定
- (void)unBindAction {
    @weakify(self);
    [MRKDeviceConnectAlertView alertUnBindDeviceView:_productType action:^(NSNumber * data) {
        @strongify(self);
        if ([data intValue] == 1) {
            [UIViewController currentViewController].traceEventId = @"btn_equipment_detail_unbind_confirm";
            [self unBindDeviceAction];
        } else {
            [UIViewController currentViewController].traceEventId = @"btn_equipment_detail_unbind_cancel";
        }
    }];
}


- (void)unBindDeviceAction {
    ///体脂秤单独处理
    if (_productType.intValue == FatScaleEquipment) {
        [MBProgressHUD showLodingWithMessage:@"" view:self.view];
        @weakify(self);
        [[WeightDeviceManager shared] unBindScale:self.model andBlock:^(BOOL success) {
            @strongify(self);
            [MBProgressHUD hideHUDForView:self.view];
            if(success) {
                [self backWithCondition];
            }
        }];
        return;
    }
    
    
    [MBProgressHUD showLodingWithMessage:@"" view:self.view];
    @weakify(self);
    self.nConnectManager.unbindStatusBlock = ^(id data) {
        @strongify(self);
        [MBProgressHUD hideHUDForView:self.view];
        if([data boolValue]) {
            [self backWithCondition];
        }
    };
    [self.nConnectManager unbindDeviceModel:self.model];
}


- (void)backWithCondition{
    ///解绑成功
    if (self.unbindBlock) {
        self.unbindBlock(@YES);
    }
    
//    NSArray *array = self.navigationController.childViewControllers ;
//    array = [[array reverseObjectEnumerator] allObjects];
//    if([array.lastObject isKindOfClass:NSClassFromString(@"MRKHomePageController")]) {
//        [MBProgressHUD showLodingWithMessage:@"" view:self.view];
//        [[NSNotificationCenter defaultCenter] postNotificationName:@"kMRKDeviceDetailController_unbind_Notification" object:nil];
//    }else {
//        [self back];
//    }
    
    [self back];
}

///
- (void)homeRefreshNotification:(id)notification {
    [MBProgressHUD hideHUDForView:self.view];
    [self back];
}


#pragma mark - 返回
- (void)back {
    ///体脂秤的返回全部返回到首页
    if(_productType.intValue == FatScaleEquipment) {
        [self.navigationController popToRootViewControllerAnimated:YES];
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [[NSNotificationCenter defaultCenter] postNotificationName:@"SetTabBarItemIndex" object:@0];
        });
        return;
    }
    
    NSArray *array = self.navigationController.childViewControllers;
    array = [[array reverseObjectEnumerator] allObjects];
    if([array.lastObject isKindOfClass:NSClassFromString(@"MyViewController")]
       || [array.lastObject isKindOfClass:NSClassFromString(@"MRKMainPageController")]) {
        
        ///我的->设备列表->...->卡片式设备详情，返回到设备列表
        UIViewController *targetVC;
        for (UIViewController *vc in array) {
            if ([vc isKindOfClass:NSClassFromString(@"MyDeviceListViewController")]) {
                targetVC = vc;
                break;
            }
        }
        
        if(targetVC) {
            [self.navigationController popToViewController:targetVC animated:YES];
        } else {
            ///我的-体脂秤-体脂秤详情
            [self.navigationController popToRootViewControllerAnimated:YES];
        }
        return;
    }
    
    BOOL isRoot = NO;
    for (UIViewController *vc in array) {
        ///只要进入过搜索 默认返回一级页面
        if ([vc isKindOfClass:NSClassFromString(@"DeviceSearchViewController")]) {
            isRoot = YES;
            break;
        }
    }
    
    if ([self.scene isEqualToString:@"1"]) { //扫一扫默认返回到一级页面
        [self handleQrcodeEnterBack];
        return;
    }
    
    if (isRoot) {
        [self.navigationController popToRootViewControllerAnimated:YES];
        return;
    }
    
    [self.navigationController popViewControllerAnimated:YES];
}

#pragma mark - UITableView -
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView{
    return self.dataSourceArr.count;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    NSArray *array = [self.dataSourceArr objectAtIndex:section];
    return array.count;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return 60;
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section{
    return DHPX(12);
}
- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    return [UIView new];
}

- (UITableViewCell * )tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    static NSString *identifier = @"dentifier";
    DeviceDetailTableCell *cell = [tableView dequeueReusableCellWithIdentifier:identifier];
    if (!cell) {
        cell = [[DeviceDetailTableCell alloc] initWithStyle:UITableViewCellStyleValue1 reuseIdentifier:identifier];
        cell.selectionStyle = UITableViewCellSelectionStyleNone;
        cell.accessoryType = UITableViewCellAccessoryDisclosureIndicator;
        cell.contentView.backgroundColor = [UIColor clearColor];
        cell.backgroundColor = [UIColor clearColor];
    }
    cell.textLabel.font = [UIFont systemFontOfSize:15 weight:UIFontWeightMedium];
    cell.textLabel.textColor = UIColorHex(#363A44);
    NSArray *array = [self.dataSourceArr objectAtIndex:indexPath.section];
    NSString *title = array[indexPath.row];
    cell.textLabel.text = title;
    
    cell.detailTextLabel.font = [UIFont systemFontOfSize:13];
    cell.detailTextLabel.textColor = UIColorHex(#363A44);
    cell.detailTextLabel.text = @"";
    cell.accessoryView = nil;
    
//    UILabel *descripLab = [[UILabel alloc] init];
//    descripLab.frame = CGRectMake(0, 0, 100, 30);
//    descripLab.textAlignment = 2;
//    descripLab.font = [UIFont systemFontOfSize:13];
//    descripLab.textColor = UIColorHex(#363A44);
//    descripLab.text = @"";
//    cell.accessoryView = descripLab;
    
    ///切角
    cell.roundCorners = ({
        UIRectCorner corners = 0;//初始化为0，任何数跟0进行或运算等于它本身。
        BOOL isFirstRow = (indexPath.row == 0);
        BOOL isLastRow = (indexPath.row == array.count - 1);
        if (isFirstRow) {///添加左上、右上圆角
            corners = corners | UIRectCornerTopLeft | UIRectCornerTopRight;
        }
        if (isLastRow) {///添加左下、右下圆角
            corners = corners | UIRectCornerBottomLeft | UIRectCornerBottomRight;
        }
        corners;
    });
    
    if([title isEqualToString:@"实时心率"]) {
        cell.accessoryView = self.heartRateView;
    }
    
    if([title isEqualToString:@"心率预警"]) {
        [self reloadHeartRateWarninginfo:cell];
    }
    
    if([title isEqualToString:@"匹配调阻"]) {
        [self reloadDeviceResistanceinfo:cell];
    }
    
    if ([title isEqualToString:@"固件更新"]) {
        [self reloadCellOTAinfo:cell];
    }
    
    if ([title isEqualToString:@"固件版本号"]) { // 彩屏单车固件不能更新
        [self reloadCellBlufiOTAinfo:cell];
    }
    
    if ([title isEqualToString:@"WIFI连接"]) {
        [self reloadCellWifi:cell];
    }
    return cell;
}

- (void)reloadCellWifi:(DeviceDetailTableCell *)cell {
    cell.detailTextLabel.text = self.wifiStatus ? @"已连接":@"未连接";
}

- (void)reloadHeartRateWarninginfo:(DeviceDetailTableCell *)cell {
    MRKRateWarnModel *warnModel = [MRKRateWarnModel currentHeartWarnModel];
    if (warnModel.isOpen.boolValue) { ///已经开启 显示预警值
        cell.detailTextLabel.attributedText = [NSAttributedString sj_UIKitText:^(id<SJUIKitTextMakerProtocol>  _Nonnull make) {
            make.append(warnModel.threshold.stringValue?:@"--");
            make.textColor(UIColorHex(666666));
            make.font([UIFont systemFontOfSize:14 weight:UIFontWeightMedium]);
            make.appendText([NSAttributedString sj_UIKitText:^(id<SJUIKitTextMakerProtocol>  _Nonnull make) {
                make.append(@"次/分");
                make.textColor(UIColorHex(999999));
                make.font([UIFont systemFontOfSize:10]);
            }]);
        }];
    } else {
        cell.detailTextLabel.text = @"未开启";
    }
}

- (void)reloadDeviceResistanceinfo:(DeviceDetailTableCell *)cell {
    cell.detailTextLabel.text = self.switchOnStatsu ? @"已开启":@"未开启";
    if ([self.model.maxResistance isNotBlank]) {
        //最大档位提醒
        self.deviceGears.text = [NSString stringWithFormat:@"此设备最大阻力为%@档", self.model.maxResistance];
        [cell.contentView addSubview:self.deviceGears];
        [self.deviceGears mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(cell.textLabel.mas_centerY);
            make.left.equalTo(cell.textLabel.mas_right).offset(DHPX(5));
            make.height.mas_equalTo(20);
        }];
    }
}

- (void)reloadCellBlufiOTAinfo:(DeviceDetailTableCell *)cell {
    NSString *version = self.model.firmwareVersion;
    //根据设备类型返回连接设备名称
    NSString *deviceName = [BluePeripheral connectDeviceNameOfType:self.model.productId];
    if ([self.model.bluetoothName isEqualToString:deviceName]) {
        version = [BluePeripheral peripheralSoftVersionFromType:self.model.productId eigenValue:self.model.versionEigenValue];
    }
    cell.detailTextLabel.text = version;
    cell.accessoryType = UITableViewCellAccessoryNone;
}

- (void)reloadCellOTAinfo:(DeviceDetailTableCell *)cell {
    NSString *version = self.model.firmwareVersion;
    //根据设备类型返回连接设备名称
    NSString *deviceName = [BluePeripheral connectDeviceNameOfType:self.model.productId];
    if ([self.model.bluetoothName isEqualToString:deviceName]) {
        version = [BluePeripheral peripheralSoftVersionFromType:self.model.productId eigenValue:self.model.versionEigenValue];
    }
    cell.detailTextLabel.text = version;
    
    //是否包含ota协议
    if (self.otaModel.isOta.boolValue && self.otaModel.isLastVersion.boolValue == 0) {
        [cell.contentView addSubview:self.updateOtaLabel];
        [self.updateOtaLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(cell.detailTextLabel.mas_centerY);
            make.right.equalTo(cell.detailTextLabel.mas_left).offset(DHPX(-5));
            make.size.mas_equalTo(CGSizeMake(48, 20));
        }];
    }
}


- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    
    NSArray *array = [self.dataSourceArr objectAtIndex:indexPath.section];
    NSString *title = array[indexPath.row];
    
    if ([title isEqualToString:@"成员管理"]) {
        
        MRKChangeAccountViewController *vc = [MRKChangeAccountViewController new];
        vc.electrodeType = self.model.electrodeType.integerValue;
        vc.listType = @(MembersManagerType);
        [self.navigationController pushViewController:vc animated:YES];
        
    } else if([title isEqualToString:@"固件更新"]) {
        if (!self.model.isOta.boolValue) {   ///不支持ota
            [AppDelegate errorView:@"当前已是最新版本"];
            return;
        }
        
        if (![BlueDataStorageManager isConnectDeviceWithProductID:self.model.productId name:self.model.bluetoothName]) {
            [AppDelegate errorView:@"请先连接设备"];
            return;
        }
        
        if (self.otaModel.isLastVersion.boolValue) {
            [AppDelegate errorView:@"当前已是最新版本"];
            return;
        }
        
        MRKOTAUpdateViewController *vc = [MRKOTAUpdateViewController new];
        vc.fromType = @1;
        vc.model = self.model;
        vc.otaM = self.otaModel;
        [self.navigationController pushViewController:vc animated:YES];
        
    }else if ([self.model.commandName isNotEmpty] && [title isEqualToString:self.model.commandName]) {
        if (![BlueDataStorageManager isConnectDeviceWithProductID:self.model.productId name:self.model.bluetoothName]) {
            [AppDelegate errorView:@"请先连接设备"];
            return;
        }
        WebViewViewController *vc = [WebViewViewController new];
        vc.titleString = self.model.commandName;
        vc.htmlURL = self.model.commandUrl;
        vc.isAddControlDeviceObs = YES;
        [self.navigationController pushViewController:vc animated:YES];
        
    }else if ([title isEqualToString:@"产品说明"]) {//帮助中心
        
        WebViewViewController *vc = [WebViewViewController new];
        vc.titleString = @"产品说明";
        vc.htmlURL = self.model.productManual;
        [self.navigationController pushViewController:vc animated:YES];
        
    }else if ([title isEqualToString:@"帮助中心"]) {
        
        WebViewViewController *vc = [WebViewViewController new];
        vc.titleString = @"帮助中心";
        vc.htmlURL = self.model.helpCenter;
        [self.navigationController pushViewController:vc animated:YES];
        
    } else if ([title isEqualToString:@"心率百科"]) {
        tableView.traceEventId = @"btn_equipment_detail_introduction";
        
        WebViewViewController *vc = [WebViewViewController new];
        vc.titleString = @"心率百科";
        vc.htmlURL = MRKAppH5LinkCombine(MRKHeartRateEncyclopedia);
        vc.isHiddenNav = YES;
        [self.navigationController pushViewController:vc animated:YES];
        
    } else if ([title hasSuffix:@"操作指南"]){
        
        IntroducePeopleWebView *introView = [[IntroducePeopleWebView alloc]init];
        introView.H5Dict = self.model.modelToJSONObject;
        introView.showInsideType = @"fromExercise";
        [self.navigationController pushViewController:introView animated:YES];
        
    } else if([title isEqualToString:@"心率预警"]) {
        tableView.traceEventId = @"btn_equipment_detail_heartrate_alert";
        
        MRKHeartWarnViewController *vc = [MRKHeartWarnViewController new];
        [self.navigationController pushViewController:vc animated:YES];
        
    } else if([title isEqualToString:@"实时心率"]) {
        NSLog(@"实时心率");
        //刷新数据
    } else if([title isEqualToString:@"匹配调阻"]) {
        MRKTrainingSetController *vc = [MRKTrainingSetController new];
        [self.navigationController pushViewController:vc animated:YES];
    } else if ([title isEqualToString:@"WIFI连接"]) {
        if ([BlueDataStorageManager isConnectDeviceWithProductID:self.model.productId name:self.model.bluetoothName]){
            [self openWifiFlutter];
        } else {
            // 先走连接
            NSString *name = [MRKEquipmentTypeData nameFromProductId:self.model.productId];
            
            NSMutableAttributedString *message = [[NSMutableAttributedString alloc] initWithString:@"请先通过蓝牙连接单车设备\n随后即可操作Wi-Fi连接！"];
            message.color = UIColorHex(#363A44);
            message.font = [UIFont fontWithName:fontNameMeDium size:18.0];
            message.alignment = NSTextAlignmentCenter;
  
            [MrkAlertManager showAttriMessage:message
                                cancel:@"暂不连接"
                                ensure:[NSString stringWithFormat:@"连接%@",  name]
                           handleIndex:^(NSInteger index) {
                if (index == 1) {
                    [self connectDeviceAction];
                }
            }];
        }
    }
}

// 打开wifi配网页面
- (void)openWifiFlutter{
    SJRouteRequest *router = [[SJRouteRequest alloc] initWithPath:@"router_wifi" parameters:@{@"cover":self.model.cover, @"snCode": self.model.snCode, @"isConnect": @(self.wifiStatus)}];
    [SJRouter.shared handleRequest:router completionHandler:nil];
}

- (NSMutableArray *)dataSourceArr {
    if (!_dataSourceArr) {
        _dataSourceArr = [NSMutableArray array];
    }
    return _dataSourceArr;
}

- (DeviceDetailHeaderView *)tableHeaderView{
    if (!_tableHeaderView) {
        DeviceDetailHeaderView *view = [[DeviceDetailHeaderView alloc] init];
        @weakify(self);
        view.connectBlock = ^(id  _Nonnull response) {
            [self_weak_ connectAction];
        };
        view.editBlcok = ^{
            [self_weak_ editAction];
        };
        _tableHeaderView = view;
    }
    return _tableHeaderView;
}

- (UIButton *)unBindBtn {
    if (!_unBindBtn) {
        _unBindBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_unBindBtn setTitle:@"解除绑定" forState:UIControlStateNormal];
        [_unBindBtn setTitleColor:UIColorHex(#848A9B) forState:UIControlStateNormal];
        _unBindBtn.titleLabel.font = [UIFont systemFontOfSize:16];
        [_unBindBtn addTarget:self action:@selector(unBindDeviceAction:) forControlEvents:UIControlEventTouchUpInside];
        _unBindBtn.layer.cornerRadius = WKDHPX(50)/2;
        _unBindBtn.layer.masksToBounds = YES;
        _unBindBtn.layer.borderColor = UIColorHex(#848A9B).CGColor;
        _unBindBtn.layer.borderWidth = CGFloatFromPixel(1.0);
    }
    return _unBindBtn;
}
- (void)unBindDeviceAction:(UIButton *)sender {
    sender.traceEventId = @"btn_equipment_detail_unbind";
    [self unBindAction];
}

- (UIButton *)weightInBtn {
    if (!_weightInBtn) {
        _weightInBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        _weightInBtn.backgroundColor = UIColorHex(#16D2E3);
        [_weightInBtn setTitle:@"测量体重" forState:UIControlStateNormal];
        [_weightInBtn setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
        _weightInBtn.titleLabel.font = [UIFont systemFontOfSize:16];
        [_weightInBtn addTarget:self action:@selector(weightInAction:) forControlEvents:UIControlEventTouchUpInside];
        _weightInBtn.layer.cornerRadius = WKDHPX(50)/2;
        _weightInBtn.layer.masksToBounds = YES;
    }
    return _weightInBtn;
}
- (void)weightInAction:(UIButton *)sender {
    [self startMessure];
}

- (UILabel *)updateOtaLabel{
    if (!_updateOtaLabel) {
        UILabel *updateV = [UILabel new];
        updateV.backgroundColor = [UIColor colorWithHexString:@"#FF5363" alpha:0.06];
        updateV.font = [UIFont systemFontOfSize:12.0];
        updateV.textColor = [UIColor colorWithHexString:@"#FF5363"];
        updateV.text = @"升级";
        updateV.textAlignment = NSTextAlignmentCenter;
        updateV.layer.cornerRadius = 4.f;
        updateV.layer.masksToBounds = YES;
        _updateOtaLabel = updateV;
    }
    return _updateOtaLabel;
}

- (DeviceHeartView *)heartRateView{
    if (!_heartRateView) {
        DeviceHeartView *view = [[DeviceHeartView alloc] init];
        view.size = CGSizeMake(100, 30);
        _heartRateView = view;
    }
    return _heartRateView;
}

- (UILabel *)deviceGears{
    if (!_deviceGears) {
        UILabel *updateV = [UILabel new];
        updateV.font = [UIFont systemFontOfSize:13.0];
        updateV.textColor = [UIColor colorWithHexString:@"#848A9B"];
        updateV.text = @"此设备最大阻力为16档";
        updateV.textAlignment = NSTextAlignmentCenter;
        _deviceGears = updateV;
    }
    return _deviceGears;
}


- (BOOL)viewControllerIsNeedNavBar:(MRKBaseController *)viewController{
    return YES;
}

- (void)leftButtonEvent:(UIButton *)sender navigationBar:(MRKNavigationBar *)navigationBar {
    if ([self.scene isEqualToString:@"1"]) { //扫一扫默认返回到一级页面
        [self handleQrcodeEnterBack];
        return;
    }
    [self.navigationController popViewControllerAnimated:YES];
//    [self back];
}

- (void)handleQrcodeEnterBack{
    UIWindow *window = [[UIApplication sharedApplication] delegate].window;
    if (window != nil && [[window rootViewController] isKindOfClass:[UITabBarController class]]) {
        [self.navigationController popToRootViewControllerAnimated:YES];
        return;
    }
    if ([Login isLogin]) {
        [[NSNotificationCenter defaultCenter] postNotificationName:@"user_login" object:UserInfo.userId userInfo:nil];
        return;
    }
    [self.navigationController popViewControllerAnimated:YES];
}


/*
 #pragma mark - Navigation
 
 // In a storyboard-based application, you will often want to do a little preparation before navigation
 - (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
 // Get the new view controller using [segue destinationViewController].
 // Pass the selected object to the new view controller.
 }
 */

@end



#import "MRKDeviceBatteryView.h"

@interface DeviceDetailHeaderView ()
@property (nonatomic, strong) UIView *contentView;

@property (nonatomic, strong) UIImageView *deviceImageView;      ///设备主图
@property (nonatomic, strong) UIImageView *meritIconView;        ///设备Meirt图标
@property (nonatomic, strong) DeviceConnectView *connectControl; ///设备连接按钮
///
@property (nonatomic, strong) UILabel *deviceUserName;           ///设备自定义名称
@property (nonatomic, strong) UIButton *editNameBtn;             ///设备自定义名称编辑按钮
@property (nonatomic, strong) UILabel *deviceBluetoothName;      ///设备蓝牙名称
@property (nonatomic, strong) UILabel *deviceType;               ///设备型号
@property (nonatomic, strong) UILabel *deviceCharacteristic;     ///设备特点
@property (nonatomic, strong) DevicekwhView *devicekwhView;      ///设备电量
@end

@implementation DeviceDetailHeaderView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        
        self.contentView = [[UIView alloc] init];
        self.contentView.backgroundColor = [UIColor whiteColor];
        self.contentView.layer.cornerRadius = 8.0;
        self.contentView.layer.masksToBounds = YES;
        [self addSubview:self.contentView];
        [self.contentView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.mas_equalTo(UIEdgeInsetsMake(DHPX(73), 0, 0, 0));
        }];
        
        ///主图
        [self addSubview:self.deviceImageView];
        [self.deviceImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.mas_top).offset(DHPX(16));
            make.centerX.equalTo(self.mas_centerX);
            make.height.mas_equalTo(DHPX(140));
        }];
        
        ///Merti图表
        [self addSubview:self.meritIconView];
        [self.meritIconView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.contentView.mas_top).offset(DHPX(30));
            make.right.equalTo(self.contentView.mas_right);
            make.size.mas_equalTo(CGSizeMake(100, 22));
        }];
        
        [self.contentView addSubview:self.connectControl];
        [self.connectControl mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.deviceImageView.mas_bottom).offset(DHPX(10));
            make.centerX.equalTo(self.mas_centerX);
            make.height.mas_equalTo(DHPX(26));
        }];
        
        [self.contentView addSubview:self.deviceUserName];
        [self.deviceUserName mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.connectControl.mas_bottom).offset(DHPX(16));
            make.left.equalTo(@(DHPX(12)));
            make.right.mas_lessThanOrEqualTo(-DHPX(42));
            make.height.mas_equalTo(DHPX(26));
        }];
        
        [self.contentView addSubview:self.editNameBtn];
        [self.editNameBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(self.deviceUserName.mas_centerY);
            make.left.equalTo(self.deviceUserName.mas_right).offset(4);
            make.size.mas_equalTo(CGSizeMake(DHPX(25), DHPX(25)));
        }];
        
        [self.contentView addSubview:self.deviceBluetoothName];
        [self.deviceBluetoothName mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.deviceUserName.mas_bottom).offset(WKDHPX(8));
            make.left.equalTo(self.deviceUserName.mas_left);
            make.height.mas_equalTo(DHPX(18));
        }];
        
        [self.contentView addSubview:self.deviceType];
        [self.deviceType mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.deviceBluetoothName.mas_bottom).offset(WKDHPX(10));
            make.left.equalTo(self.deviceUserName.mas_left);
            make.height.mas_equalTo(DHPX(18));
        }];
        
        [self.contentView addSubview:self.deviceCharacteristic];
        [self.deviceCharacteristic mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.deviceType.mas_bottom).offset(WKDHPX(10));
            make.left.equalTo(self.deviceUserName.mas_left);
            make.height.mas_equalTo(DHPX(18));
            make.bottom.equalTo(self.mas_bottom).offset(-WKDHPX(25));
        }];
        
        ///设备电量
        [self.contentView addSubview:self.devicekwhView];
        [self.devicekwhView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.contentView.mas_top).offset(DHPX(20));
            make.right.equalTo(self.contentView.mas_right).offset(DHPX(-16));
        }];
    }
    return self;
}

- (void)setModel:(MRKDeviceModel *)model {
    _model = model;
    
    if (model.productId.intValue == FatScaleEquipment) {
        self.connectControl.hidden = YES;
        [self.connectControl mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(0);
        }];
        [self.deviceUserName mas_updateConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.deviceImageView.mas_bottom).offset(DHPX(16));
        }];
    }else{
        self.connectControl.hidden = NO;
    }
    
    [self.deviceImageView sd_setImageWithURL:[NSURL URLWithString:model.cover] placeholderImage:placeImage];
    self.meritIconView.hidden = !model.isMerit.boolValue;
    self.deviceUserName.text = model.deviceAlias ?:@"自定义名称";
    self.deviceBluetoothName.text = [NSString stringWithFormat:@"蓝牙名称: %@", model.bluetoothName];
    self.deviceType.text = [NSString stringWithFormat:@"型号: %@", model.modelName];
    
    if ([model.deviceDescription isNotBlank]) {
        self.deviceCharacteristic.hidden = NO;
        self.deviceCharacteristic.text = [NSString stringWithFormat:@"特点: %@", model.deviceDescription];
        [self.deviceCharacteristic mas_updateConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.deviceType.mas_bottom).offset(WKDHPX(10));
            make.height.mas_equalTo(DHPX(18));
        }];
    }else {
        self.deviceCharacteristic.hidden = YES;
        [self.deviceCharacteristic mas_updateConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.deviceType.mas_bottom).offset(0);
            make.height.mas_equalTo(0);
        }];
    }
}

- (void)setConnectStatus:(DEVICE_CONNECT_STATUS)connectStatus {
    _connectStatus = connectStatus;
    self.connectControl.connectStatus = connectStatus;
    if (self.model.productId.intValue == FatScaleEquipment) {
        
    }
}

- (void)setDevicePower:(NSNumber *)devicePower {
    _devicePower = devicePower;
    if (self.model.productId.intValue == FatScaleEquipment) {
        
    }else{
        self.devicekwhView.devicePower = devicePower;
    }
}

- (UIImageView *)deviceImageView{
    if (!_deviceImageView) {
        _deviceImageView = [[UIImageView alloc] init];
        _deviceImageView.contentMode = UIViewContentModeScaleAspectFit;
        _deviceImageView.backgroundColor = [UIColor clearColor];
    }
    return _deviceImageView;
}

- (UIImageView *)meritIconView{
    if (!_meritIconView) {
        _meritIconView = [[UIImageView alloc] init];
        _meritIconView.contentMode = UIViewContentModeScaleAspectFill;
        _meritIconView.image = [UIImage imageNamed:@"icon_merit_device"];
    }
    return _meritIconView;
}

- (DeviceConnectView *)connectControl {
    if (!_connectControl) {
        DeviceConnectView *connectControl = [[DeviceConnectView alloc] init];
        [connectControl addTarget:self action:@selector(medalButtonAction:) forControlEvents:UIControlEventTouchUpInside];
        connectControl.layer.cornerRadius = DHPX(26)/2;
        connectControl.layer.masksToBounds = YES;
        _connectControl = connectControl;
    }
    return _connectControl;
}

- (void)medalButtonAction:(UIButton *)sender {
    if (self.model.isConnect.intValue == 1) {
        sender.traceEventId = @"btn_equipment_detail_bluetoothunbind";
    } else {
        sender.traceEventId = @"btn_equipment_detaili_bluetoothbind";
    }
    
    if (self.connectBlock) {
        self.connectBlock(self.model);
    }
}

- (UILabel *)deviceUserName{
    if (!_deviceUserName) {
        _deviceUserName = [[UILabel alloc] init];
        _deviceUserName.textColor = [UIColor colorWithHexString:@"#363A44"];
        _deviceUserName.font = [UIFont systemFontOfSize:18 weight:UIFontWeightMedium];
        _deviceUserName.adjustsFontSizeToFitWidth = YES;
    }
    return _deviceUserName;
}

- (UIButton *)editNameBtn {
    if (!_editNameBtn) {
        UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
        [button setImage:[UIImage imageNamed:@"icon_edit"] forState:UIControlStateNormal];
        [button addTarget:self action:@selector(editAction:) forControlEvents:UIControlEventTouchUpInside];
        _editNameBtn = button;
    }
    return _editNameBtn;
}

- (void)editAction:(UIButton *)sender{
    sender.traceEventId = @"btn_equipment_detail_rename";
    if (self.editBlcok){
        self.editBlcok();
    }
}

- (UILabel *)deviceBluetoothName{
    if (!_deviceBluetoothName) {
        _deviceBluetoothName = [[UILabel alloc] init];
        _deviceBluetoothName.textColor = [UIColor colorWithHexString:@"#848A9B"];
        _deviceBluetoothName.font = [UIFont systemFontOfSize:13];
        _deviceBluetoothName.adjustsFontSizeToFitWidth = YES;
    }
    return _deviceBluetoothName;
}

- (UILabel *)deviceType{
    if (!_deviceType) {
        _deviceType = [[UILabel alloc] init];
        _deviceType.textColor = [UIColor colorWithHexString:@"#848A9B"];
        _deviceType.font = [UIFont systemFontOfSize:13];
        _deviceType.adjustsFontSizeToFitWidth = YES;
    }
    return _deviceType;
}

- (UILabel *)deviceCharacteristic{
    if (!_deviceCharacteristic) {
        _deviceCharacteristic = [[UILabel alloc] init];
        _deviceCharacteristic.textColor = [UIColor colorWithHexString:@"#848A9B"];
        _deviceCharacteristic.font = [UIFont systemFontOfSize:13];
        _deviceCharacteristic.adjustsFontSizeToFitWidth = YES;
        _deviceCharacteristic.numberOfLines = 0;
    }
    return _deviceCharacteristic;
}

- (DevicekwhView *)devicekwhView {
    if (!_devicekwhView) {
        _devicekwhView = [[DevicekwhView alloc] init];
        _devicekwhView.hidden = YES;
    }
    return _devicekwhView;
}

@end





@interface DeviceConnectView ()
@property (nonatomic, strong) UIView *centerView;
@property (nonatomic, strong) UIImageView *deviceIcon;
@property (nonatomic, strong) UILabel *titleLab;
@property (nonatomic, strong) UILabel *statusLab;
@property (nonatomic, strong) UIActivityIndicatorView *activityIndicator;
@end


@implementation DeviceConnectView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = UIColorHex(#16D2E3);
        [self setUI];
    }
    return self;
}

- (void)setUI {
    if (!self.centerView){
        self.centerView = [[UIView alloc] init];
    }
    [self addSubview:self.centerView];
    [self.centerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.mas_centerY);
        make.left.equalTo(@(DHPX(10)));
        make.right.equalTo(@(-DHPX(14)));
    }];
    
    if (!self.deviceIcon) {
        self.deviceIcon = [UIImageView new];
        self.deviceIcon.contentMode = UIViewContentModeScaleAspectFit;
        self.deviceIcon.backgroundColor = [UIColor clearColor];
        self.deviceIcon.clipsToBounds = YES;
        self.deviceIcon.image = [UIImage imageNamed:@"icon_detial_disconnect_w"];
    }
    [self.centerView addSubview:self.deviceIcon];
    [self.deviceIcon mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(self.centerView.mas_left).offset(0);
        make.centerY.mas_equalTo(self.centerView);
        make.size.equalTo(@(CGSizeMake(20, 20)));
    }];
    
    if (!self.statusLab) {
        self.statusLab = [UILabel new];
        self.statusLab.font = [UIFont systemFontOfSize:13 weight:UIFontWeightMedium];
        self.statusLab.textColor = UIColorHex(#848A9B);
        self.statusLab.textAlignment = NSTextAlignmentCenter;
        self.statusLab.text = @"未连接";
    }
    [self.centerView addSubview:self.statusLab];
    [self.statusLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(self.deviceIcon.mas_right).offset(3);
        make.centerY.mas_equalTo(self.centerView);
        make.height.mas_equalTo(20);
        make.right.mas_equalTo(self.centerView.mas_right).offset(0);
    }];
    
    if(!self.activityIndicator) {
        self.activityIndicator = [[UIActivityIndicatorView alloc] initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleGray];
        self.activityIndicator.hidesWhenStopped = YES;
        self.activityIndicator.color = UIColorHex(#16D2E3);
        self.activityIndicator.transform = CGAffineTransformMakeScale(.85f, .85f);
    }
    [self addSubview:self.activityIndicator];
    [self.activityIndicator mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.mas_equalTo(self.deviceIcon);
        make.centerY.mas_equalTo(self.deviceIcon);
        make.size.mas_equalTo(CGSizeMake(20, 20));
    }];
    
    
    @weakify(self);
    [[[RACObserve(self, connectStatus) ignore:NULL] distinctUntilChanged] subscribeNext:^(id _Nullable x) {
        @strongify(self);
        if ([x isKindOfClass:[NSNumber class]]){
            DEVICE_CONNECT_STATUS status = [x intValue];
            if (status == DeviceConnecting || status == DeviceScaning) {
                
                dispatch_async(dispatch_get_main_queue(), ^{
                    self.backgroundColor = UIColorHex(#F8F9FC);
                    //连接中
                    [self.deviceIcon setHidden:YES];
                    [self.activityIndicator startAnimating];
                    
                    [self.statusLab setText:@"连接中..."];
                    [self.statusLab setTextColor:UIColorHex(#16D2E3)];
                });
               
            } else if (status == DeviceConnected) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    self.backgroundColor = UIColorHex(#F8F9FC);
                    //连接成功
                    [self.activityIndicator stopAnimating];
                    [self.deviceIcon setHidden:NO];
                    [self.deviceIcon setImage:[UIImage imageNamed:@"icon_detial_connect"]];
                    
                    [self.statusLab setText:@"断开连接"];
                    [self.statusLab setTextColor:UIColorHex(#16D2E3)];
                });
              
            } else if (status == DeviceDisconnect) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    self.backgroundColor = UIColorHex(#16D2E3);
                    //断连成功
                    [self.activityIndicator stopAnimating];
                    [self.deviceIcon setHidden:NO];
                    [self.deviceIcon setImage:[UIImage imageNamed:@"icon_detial_disconnect_w"]];
                    
                    [self.statusLab setText:@"连接设备"];
                    [self.statusLab setTextColor:UIColorHex(#FFFFFF)];
                });
            }
        }
    }];
}

@end







@interface DevicekwhView ()
@property (nonatomic, strong) UIImageView *elecImageView;
@property (nonatomic, strong) UILabel *elecPercent;
@property (nonatomic, strong) UIView *elecProgress;
@end

@implementation DevicekwhView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        
        if (!self.elecImageView) {
            self.elecImageView = [[UIImageView alloc] init];
            self.elecImageView.contentMode = UIViewContentModeScaleAspectFit;
            self.elecImageView.backgroundColor = [UIColor clearColor];
            self.elecImageView.clipsToBounds = YES;
            self.elecImageView.image = [UIImage imageNamed:@"icon_battery"];
        }
        [self addSubview:self.elecImageView];
        [self.elecImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(self.mas_right).offset(0);
            make.width.equalTo(@(18));
            make.centerY.equalTo(self.mas_centerY);
        }];
        
        if (!self.elecProgress) {
            self.elecProgress = [[UIView alloc] init];
            self.elecProgress.backgroundColor = UIColorHex(666666);
            self.elecProgress.layer.cornerRadius = 1;
            self.elecProgress.layer.masksToBounds = YES;
        }
        [self.elecImageView addSubview:self.elecProgress];
        [self.elecProgress mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.elecImageView.mas_left).offset(1.5);
            make.height.equalTo(@(7));
            make.centerY.equalTo(self.elecImageView.mas_centerY);
        }];
        
        
        if (!self.elecPercent) {
            self.elecPercent = [[UILabel alloc] init];
            self.elecPercent.font = [UIFont fontWithName:fontNamePing size:12.0];
            self.elecPercent.textColor = UIColorHex(666666);
            self.elecPercent.text = @"--";
        }
        [self addSubview:self.elecPercent];
        [self.elecPercent mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.mas_left);
            make.right.equalTo(self.elecImageView.mas_left).offset(-DHPX(5));
            make.centerY.equalTo(self.mas_centerY);
        }];
    }
    return self;
}

- (void)setDevicePower:(NSNumber *)devicePower {
    _devicePower = devicePower;
    
    self.hidden = ![devicePower intValue];
    if ([devicePower intValue]) {
        self.elecPercent.text = [NSString stringWithFormat:@"%@%%", devicePower];
        
        float width = (devicePower.doubleValue/100.0) *13;
        [self.elecProgress mas_updateConstraints:^(MASConstraintMaker *make) {
            make.width.mas_equalTo(width);
        }];
    }
}

@end






@interface DeviceHeartView ()
@property (nonatomic, strong) UIImageView *heartImageView;
@property (nonatomic, strong) UILabel *heartCountLab;
@end

@implementation DeviceHeartView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        
        [self addSubview:self.heartCountLab];
        [self.heartCountLab mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(self.mas_right).offset(0);
            make.height.equalTo(@(30));
            make.centerY.equalTo(self.mas_centerY);
        }];
        
        [self addSubview:self.heartImageView];
        [self.heartImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(self.heartCountLab.mas_left).offset(-3);
            make.centerY.equalTo(self.mas_centerY);
            make.width.equalTo(@(20));
            make.height.equalTo(@(20));
        }];
    }
    return self;
}

- (void)updateHeartRate:(NSNumber *)rate {
    NSString *rateStr = [NSString stringWithFormat:@"%@", rate.intValue > 0 ? rate : @"--"];
    NSMutableAttributedString *string = [[NSMutableAttributedString alloc] initWithString:rateStr attributes:@{
        NSFontAttributeName : [UIFont systemFontOfSize:14 weight:UIFontWeightMedium],
        NSForegroundColorAttributeName : UIColorHex(666666)
    }];
    
    NSMutableAttributedString *unit = [[NSMutableAttributedString alloc]initWithString:@"次/分" attributes:@{
        NSFontAttributeName : [UIFont systemFontOfSize:10],
        NSForegroundColorAttributeName : UIColorHex(999999)
    }];
    
    [string appendAttributedString:unit];
    self.heartCountLab.attributedText = string;
    
    NSString *imageName = [BluetoothManager isConnectEquipmentType:@(HeartEquipment).stringValue] ? @"icon_heart_rate_my" : @"icon_heart_rate_ash";
    self.heartImageView.image = [UIImage imageNamed:imageName];
}

- (UIImageView *)heartImageView{
    if (!_heartImageView) {
        _heartImageView = [[UIImageView alloc] init];
        _heartImageView.contentMode = UIViewContentModeScaleAspectFit;
        _heartImageView.backgroundColor = [UIColor clearColor];
        _heartImageView.clipsToBounds = YES;
        _heartImageView.image = [UIImage imageNamed:@"icon_heart_rate_ash"];
    }
    return _heartImageView;
}

- (UILabel *)heartCountLab{
    if (!_heartCountLab) {
        _heartCountLab = [[UILabel alloc] init];
        _heartCountLab.font = [UIFont systemFontOfSize:10];
        _heartCountLab.textColor = UIColorHex(666666);
        _heartCountLab.text = @"--";
    }
    return _heartCountLab;
}

@end





@interface DeviceDetailTableCell()
@property (nonatomic, strong) UIView *backCornerView;
@end

@implementation DeviceDetailTableCell
- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier{
    if (self =[super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        [self.contentView addSubview:self.backCornerView];
    }
    return self;
}

- (void)layoutSubviews {
    [super layoutSubviews];
    
    [self.backCornerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentView.mas_top).offset(0);
        make.left.equalTo(self.contentView.mas_left).offset(0);
        make.size.mas_equalTo(CGSizeMake(kScreenWidth - DHPX(16)*2, 60));
    }];
}

- (UIView *)backCornerView {
    if (!_backCornerView) {
        _backCornerView = [[UIView alloc] init];
        _backCornerView.backgroundColor = [UIColor whiteColor];
    }
    return _backCornerView;
}

//roundCorners可以自由指定左上、左下、右上、右下圆角的任意组合
- (void)setRoundCorners:(UIRectCorner)roundCorners {
    _roundCorners = roundCorners;
    if (roundCorners == 0) {
        self.backCornerView.layer.mask = nil;//无圆角
    } else {
        UIBezierPath *maskPath = [UIBezierPath bezierPathWithRoundedRect:self.cellRect byRoundingCorners:roundCorners cornerRadii:CGSizeMake(8, 8)];
        CAShapeLayer *maskLayer = [[CAShapeLayer alloc] init];
        maskLayer.frame = self.cellRect;
        maskLayer.path = maskPath.CGPath;
        self.backCornerView.layer.mask = maskLayer;
    }
}

- (CGRect)cellRect {
    return CGRectMake(0, 0, kScreenWidth - DHPX(16)*2, 60);
}

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];
    
    // Configure the view for the selected state
}

@end
