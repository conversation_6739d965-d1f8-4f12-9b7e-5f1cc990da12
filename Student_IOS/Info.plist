<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>AlivcLicenseFile</key>
	<string>license.crt</string>
	<key>AlivcLicenseKey</key>
	<string>zoEXfatan1a4kukXUe9f9462bb4cd4dfba68cb2351edcc79b</string>
	<key>CFBundleGetInfoString</key>
	<string></string>
	<key>CFBundleIcons</key>
	<dict/>
	<key>CFBundleIcons~ipad</key>
	<dict/>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>tencent1111447758</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>weixin</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>wx5268e9b7f55c368b</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>QQ</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>QQ423f58ce</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>MeritApp</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>yuDongAPP</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>weibo</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>wb982091331</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>activity/addDevice</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>yudongapp</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>dynamicisland</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>MeritDynamicIsland</string>
			</array>
		</dict>
	</array>
	<key>GT_MinimumOSVersion</key>
	<integer>11</integer>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>zhihu</string>
		<string>youpin</string>
		<string>xhsdiscover</string>
		<string>weibosdk3.3</string>
		<string>sinaweibo</string>
		<string>fb685742891599488</string>
		<string>fbapi20130214</string>
		<string>WeChat</string>
		<string>sinaweibo</string>
		<string>fbapi</string>
		<string>sinaweibohd</string>
		<string>kakaokompassauth</string>
		<string>storykompassauth</string>
		<string>kakao48d3f524e4a636b08d81b3ceb50f1003</string>
		<string>kakaolink</string>
		<string>instagram</string>
		<string>pinit</string>
		<string>pocket-oauth-v1</string>
		<string>fbauth2</string>
		<string>rm226427com.mob.demoShareSDK</string>
		<string>mqzone</string>
		<string>mqq</string>
		<string>mqqapi</string>
		<string>wtloginmqq2</string>
		<string>mqqopensdkapiV3</string>
		<string>mqqopensdkapiV2</string>
		<string>mqqOpensdkSSoLogin</string>
		<string>mqzoneopensdkapiV2</string>
		<string>mqzoneopensdkapi19</string>
		<string>mqzoneopensdkapi</string>
		<string>mqzoneopensdk</string>
		<string>alipayshare</string>
		<string>alipay</string>
		<string>wechat</string>
		<string>weixin</string>
		<string>weixinULAPI</string>
		<string>weixinURLParamsAPI</string>
		<string>tencentweiboSdkv2</string>
		<string>TencentWeibo</string>
		<string>weibosdk2.5</string>
		<string>weibosdk</string>
		<string>sinaweibohdsso</string>
		<string>sinaweibosso</string>
		<string>hasgplus4</string>
		<string>storylink</string>
		<string>alipayshare</string>
		<string>mqqwpa</string>
		<string>line</string>
		<string>whatsapp</string>
		<string>openapp.jdmobile</string>
		<string>tbopen</string>
	</array>
	<key>MOBAppSecret</key>
	<string>db695fb8795496cae4a95718b202907d</string>
	<key>MOBAppkey</key>
	<string>32ab612c674e3</string>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
		<key>NSAllowsArbitraryLoadsForMedia</key>
		<true/>
		<key>NSAllowsArbitraryLoadsInWebContent</key>
		<true/>
		<key>NSAllowsLocalNetworking</key>
		<true/>
	</dict>
	<key>NSUserActivityTypes</key>
	<array>
		<string>com.merit.app.openFromDynamicIsland</string>
	</array>
	<key>RealBundleVersonString</key>
	<string>*******</string>
	<key>UIAppFonts</key>
	<array>
		<string>douyu22.ttf</string>
		<string>BEBAS___.TTF</string>
		<string>iconfont.ttf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>audio</string>
		<string>bluetooth-central</string>
		<string>bluetooth-peripheral</string>
		<string>fetch</string>
		<string>remote-notification</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<true/>
	<key>User Interface Style</key>
	<string>Light</string>
</dict>
</plist>
