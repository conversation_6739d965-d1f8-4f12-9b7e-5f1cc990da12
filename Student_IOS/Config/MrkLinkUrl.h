//
//  MrkLinkUrl.h
//  Student_IOS
//
//  Created by merit on 2022/5/30.
//

#import <Foundation/Foundation.h>


UIKIT_EXTERN NSString *const MRKLinkUserProtocol;                         ///用户协议
UIKIT_EXTERN NSString *const MRKLinkPrivacyProtocol;                      ///隐私协议
UIKIT_EXTERN NSString *const MRKLinkTrainingReport;                       ///训练报告
UIKIT_EXTERN NSString *const MRKLinkAutoRenewalProtocol;                  ///自动续费协议
UIKIT_EXTERN NSString *const MRKLinkActivityReport;                       ///挑战赛报告
UIKIT_EXTERN NSString *const MRKLinkPlanReport;                           ///训练计划报告
UIKIT_EXTERN NSString *const MRKLinkCoachDetail;                          ///教练详情
UIKIT_EXTERN NSString *const MRKLinkDeviceConnectHelp;                    ///设备搜索帮助
UIKIT_EXTERN NSString *const MRKLinkCourseDetail;                         ///课程详情
UIKIT_EXTERN NSString *const MRKLinkThemeDetail;                          ///主题详情
UIKIT_EXTERN NSString *const MRKLinkCoursePlanDetail;                     ///计划详情
UIKIT_EXTERN NSString *const MRKLinkNewWebActivity;                       ///(新）配置后的新活动页
UIKIT_EXTERN NSString *const MRKLinkGuideNewUser;                         ///新手指引
UIKIT_EXTERN NSString *const MRKLinkVideoTV;                              ///投屏
UIKIT_EXTERN NSString *const MRKLinkProductEncyclopedia;                  ///产品百科
UIKIT_EXTERN NSString *const MRKLinkMeritActivityQualification;           ///超燃脂挑战赛获得参赛资格
UIKIT_EXTERN NSString *const MRKLinkNoviceActivities;                     ///新手大礼包
UIKIT_EXTERN NSString *const MRKLinkNewPersonAlertImageUrl;               ///新手大礼包弹窗图Url
UIKIT_EXTERN NSString *const MRKLinkNewPersonAlertTaskUrl;                ///新燃优专享任务链接
UIKIT_EXTERN NSString *const MRKLinkAbilityTestUrl;                       ///能力动态测评
UIKIT_EXTERN NSString *const MRKUltraThemeShareUrl;                       ///超燃脂主题分享链接
UIKIT_EXTERN NSString *const MRKUltraCourseShareUrl;                      ///超燃脂课程分享链接
UIKIT_EXTERN NSString *const MRKUserHelpAndFeedback;                      ///七鱼帮助与反馈
UIKIT_EXTERN NSString *const MRKKingCourseUrl;                            ///王牌课详情
UIKIT_EXTERN NSString *const MRKHomePageTrumpCourse;                      ///新版王牌课
UIKIT_EXTERN NSString *const MRKHomePageRecommendSubject;                 ///推荐专题
UIKIT_EXTERN NSString *const MRKHomePageRecommendListSubject;             ///推荐专题列表
UIKIT_EXTERN NSString *const MRKHomePagePopularityRank;                   ///超燃人气榜
UIKIT_EXTERN NSString *const MRKHomePageXenjoyCourseMore;                 ///绝影更多课
UIKIT_EXTERN NSString *const MRKVipPageXenjoyInterest;                    ///绝影Vip权益
UIKIT_EXTERN NSString *const MRKHeartRateEncyclopedia;                    ///心率百科
UIKIT_EXTERN NSString *const MRKUserLevelRule;                            ///等级规则
UIKIT_EXTERN NSString *const MRKWebConnectLink;                           ///新用户连接设备
UIKIT_EXTERN NSString *const MRKHealthReport;                             ///八点极健康报告页面
UIKIT_EXTERN NSString *const MRKActiveExercise;                           ///活动赛事
UIKIT_EXTERN NSString *const MRKTrainPart;                                ///活动赛事
UIKIT_EXTERN NSString *const MRKMeritShop;                                ///商城
UIKIT_EXTERN NSString *const MRKNewUserAction;                            ///新用户活动
UIKIT_EXTERN NSString *const MRKHeartConnectQuestion;                     ///心率问题
UIKIT_EXTERN NSString *const MRKTrainDataQuestion;                        ///训练数据问题
UIKIT_EXTERN NSString *const MRKAIPlanEntrance;                           ///ai计划入口页面
UIKIT_EXTERN NSString *const MRKAIChat;                                   ///ai聊天入口页面
UIKIT_EXTERN NSString *const MRKAIPlanReport;                             ///ai计划报告入口页面
UIKIT_EXTERN NSString *const MRKAIPlanReportV2;                           ///ai计划报告入口页面V2
UIKIT_EXTERN NSString *const MRKAIUsageGuide;                             ///ai计划规则说明
UIKIT_EXTERN NSString *const MRKAIUsageGuideV2;                           ///ai计划规则说明V2
UIKIT_EXTERN NSString *const MRKAISportDietAnalysis;                      ///ai饮食分析
UIKIT_EXTERN NSString *const MRKAIDietOpenVip;                            ///ai饮食开通会员中间页
UIKIT_EXTERN NSString *const MRKAIPlanOpenVip;                            ///ai计划——AI私教购买会员页面路由地址 中间页
UIKIT_EXTERN NSString *const MRKAISportDietAnalysis;                      ///ai饮食食谱推荐页面
UIKIT_EXTERN NSString *const MRKAIPlanDietRecommend;

