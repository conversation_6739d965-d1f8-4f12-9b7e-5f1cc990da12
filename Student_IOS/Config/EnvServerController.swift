//
//  EnvServerController.swift
//  Student_IOS
//
//  Created by merit on 2025/6/26.
//

import UIKit
import Combine

class EnvServerController: UIViewController, UITableViewDelegate, UITableViewDataSource {
    
    private let tableView = UITableView()
    private var environmentService: EnvironmentService!
    private var environments: [EnvironmentItem] = []
    private var activityIndicator: UIActivityIndicatorView!
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupEnvironmentService()
        setupUI()
        fetchEnvironments()
    }
    
    // 初始化环境服务
    private func setupEnvironmentService() {
        environmentService = EnvironmentService.shared
    }
    
    // UI设置
    private func setupUI() {
        view.backgroundColor = .systemBackground
        title = "选择环境"
        
        // 导航栏加载指示器
        activityIndicator = UIActivityIndicatorView(style: .medium)
        activityIndicator.startAnimating()
        navigationItem.titleView = activityIndicator
        
        
        // 表格视图配置
        tableView.delegate = self
        tableView.dataSource = self
        tableView.register(UITableViewCell.self, forCellReuseIdentifier: "EnvironmentCell")
        tableView.separatorStyle = .singleLine
        tableView.translatesAutoresizingMaskIntoConstraints = false
        
        view.addSubview(tableView)
        
        // 布局约束
        NSLayoutConstraint.activate([
            tableView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            tableView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            tableView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            tableView.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor)
        ])
    }
    
    // 加载环境数据
    private func fetchEnvironments() {
        showLoading()
        environmentService.fetchEnvironments { [weak self] data, error in
            guard let self = self else { return }
            self.hideLoading()
            
            if let error = error {
                self.showAlert(message: "加载环境失败: \(error.localizedDescription)")
                return
            }
            
            self.environments = data ?? []
            self.tableView.reloadData()
        }
    }
    
    @objc private func refreshData() {
        fetchEnvironments()
    }
    
    // MARK: - UITableViewDataSource
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return environments.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
//        let cell = tableView.dequeueReusableCell(withIdentifier: "EnvironmentCell", for: indexPath)
        var cell = tableView.dequeueReusableCell(withIdentifier: "cell")
        if cell == nil {
            cell = UITableViewCell(style: .value1, reuseIdentifier: "cell")
        }
        let env = environments[indexPath.row]
        
        cell!.textLabel?.text = env.name
        cell!.textLabel?.font = .systemFont(ofSize: 16)
        cell!.detailTextLabel?.text = ""
        // 设置选中状态
        if let selectedEnv = environmentService.selectedEnvironment, selectedEnv.cid == env.cid {
            cell!.detailTextLabel?.text = "✅"
        }
        return cell!
    }
    
    // MARK: - UITableViewDelegate
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        let env = environments[indexPath.row]
        environmentService.selectEnvironment(env)
        tableView.reloadData()
        
        // 选中后返回（示例）
        if let navController = navigationController {
            navController.popViewController(animated: true)
        }
        MRKGTManager.shared().requestPushTokenRefreshStatus(0)
        NotificationCenter.default.post(name: NSNotification.Name("user_logout") , object: nil)
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            exit(0)
        }
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 50
    }
    
    // 辅助方法
    private func showLoading() {
        activityIndicator.startAnimating()
    }
    
    internal override func hideLoading() {
        activityIndicator.stopAnimating()
        navigationItem.titleView = nil
    }
    
    private func showAlert(message: String) {
        let alert = UIAlertController(title: "提示", message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
    
    deinit {
        environmentService.cleanup()
    }
    
}

// 环境URL模型（结构体，仅属性）
@objc(EnvironmentURLs)
class EnvironmentURLs: MRKBaseModel {
    @objc var http: String?
    @objc var aiHttp: String?
    @objc var aiVoiceUri: String?
    @objc var socket: String?
    @objc var webPath: String?
    @objc var web: String?
    @objc var aiWeb: String?
    @objc var aiConfig: String?
    @objc var isTra: String?
    @objc var env: String?
    
    @objc static func modelCustomPropertyMapper() -> Dictionary<String, Any>? {
        return [
            "http":"backend",
            "aiHttp":"aiBackend",
            "aiVoiceUri":"aiVoiceBackend",
            "webPath":"h5",
            "web":"oldH5",
            "aiWeb":"aiH5",
            "env":"mrkEnv",
        ]
    }
}

// 环境项模型（结构体，仅属性
@objc(EnvironmentItem)
class EnvironmentItem: MRKBaseModel {
    @objc var cid: Int = 0
    @objc var name: String?
    @objc var defaultEnv: Bool = false
    @objc var urls: EnvironmentURLs?
}

@objc(EnvironmentService)
class EnvironmentService: NSObject {
    @objc static let shared = EnvironmentService()
    private let userDefaults = UserDefaults.standard
    private let selectedEnvKey = "merit_selectedEnvironmentData"
    private var subscriptions = Set<AnyCancellable>()
    
    var cnEnvironments: [EnvironmentItem] = []
    @objc var selectedEnvironment: EnvironmentItem? {
        didSet {
            saveSelectedEnvironment()
        }
    }
    
    // 初始化方法
    @objc override init() {
        super.init()
        loadSelectedEnvironment()
    }
    // 析构函数
    deinit {
        cleanup()
    }
    // 清理资源
    @objc func cleanup() {
        subscriptions.removeAll()
    }
    
    // 从网络获取环境列表
    func fetchEnvironments(completion: @escaping ([EnvironmentItem]?, Error?) -> Void) {
        guard let url = URL(string: "https://merit-app-test.merach.com/env.json") else {
            completion(nil, NSError(domain: "InvalidURL", code: -1, userInfo: nil))
            return
        }
        
        URLSession.shared.dataTaskPublisher(for: url)
            .map(\.data)
            .receive(on: DispatchQueue.main)
            .sink { state in
                switch state {
                case .finished: break
                case .failure(let error):
                    completion(nil, error)
                }
            } receiveValue: { [weak self] data in
                let items = self?.parseEnvironmentData(data)
                completion(items, nil)
            }
            .store(in: &self.subscriptions)
    }
    // 解析JSON数据为环境列表
    @objc func parseEnvironmentData(_ data: Data) -> [EnvironmentItem]? {
        do {
            // 解析根数组
            let json = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any]
            guard let environmentsDict = json?["environments"] as? [String: Any],
                  let cnArray = environmentsDict["cn"] as? [[String: Any]] else {
                return nil
            }
            let items = NSArray.modelArray(with: EnvironmentItem.self, json: cnArray) as? [EnvironmentItem]
            return items
            
        } catch {
            print("解析环境数据失败: \(error)")
            return nil
        }
    }
    
    
    // 选择环境
    func selectEnvironment(_ environment: EnvironmentItem) {
        selectedEnvironment = environment
    }
    
    // 保存选中的环境（仅保存当前选中项）
    private func saveSelectedEnvironment() {
        guard let env = selectedEnvironment else {
            userDefaults.removeObject(forKey: selectedEnvKey)
            return
        }
        
        // 将对象归档为Data
        let data = env.modelToJSONString()
        // 存入UserDefaults
        userDefaults.set(data, forKey: selectedEnvKey)
        userDefaults.synchronize()
        print("单个环境配置已保存到UserDefaults \(String(describing: data))")
        
    }
    
    // 加载选中的环境
    func loadSelectedEnvironment() {
        guard let data = userDefaults.value(forKey: selectedEnvKey) else { return }
        // 从Data反归档为对象
        selectedEnvironment = EnvironmentItem.model(withJSON: data)
    }
    
}
