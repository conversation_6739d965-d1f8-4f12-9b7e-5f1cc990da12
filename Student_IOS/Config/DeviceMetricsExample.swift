//
//  DeviceMetricsExample.swift
//  Student_IOS
//
//  Created by merit on 2025/8/29.
//  DeviceMetrics 使用示例和测试
//

import UIKit

/// DeviceMetrics 使用示例控制器
class DeviceMetricsExampleViewController: UIViewController {
    
    private let scrollView = UIScrollView()
    private let stackView = UIStackView()
    private let refreshButton = UIButton(type: .system)
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        updateDeviceInfo()
    }
    
    override func viewWillTransition(to size: CGSize, with coordinator: UIViewControllerTransitionCoordinator) {
        super.viewWillTransition(to: size, with: coordinator)
        
        coordinator.animate(alongsideTransition: { _ in
            // 旋转时更新设备信息
            self.updateDeviceInfo()
        }, completion: nil)
    }
    
    private func setupUI() {
        title = "DeviceMetrics 示例"
        view.backgroundColor = .systemBackground
        
        // 刷新按钮
        refreshButton.setTitle("刷新设备信息", for: .normal)
        refreshButton.backgroundColor = .systemBlue
        refreshButton.setTitleColor(.white, for: .normal)
        refreshButton.layer.cornerRadius = 8
        refreshButton.addTarget(self, action: #selector(refreshTapped), for: .touchUpInside)
        
        // 滚动视图
        scrollView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(scrollView)
        
        // 堆栈视图
        stackView.axis = .vertical
        stackView.spacing = 16
        stackView.translatesAutoresizingMaskIntoConstraints = false
        scrollView.addSubview(stackView)
        
        // 设置约束
        NSLayoutConstraint.activate([
            scrollView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            scrollView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            scrollView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            scrollView.bottomAnchor.constraint(equalTo: view.bottomAnchor),
            
            stackView.topAnchor.constraint(equalTo: scrollView.topAnchor, constant: 20),
            stackView.leadingAnchor.constraint(equalTo: scrollView.leadingAnchor, constant: 20),
            stackView.trailingAnchor.constraint(equalTo: scrollView.trailingAnchor, constant: -20),
            stackView.bottomAnchor.constraint(equalTo: scrollView.bottomAnchor, constant: -20),
            stackView.widthAnchor.constraint(equalTo: scrollView.widthAnchor, constant: -40)
        ])
    }
    
    @objc private func refreshTapped() {
        updateDeviceInfo()
        
        // 添加刷新动画
        refreshButton.transform = CGAffineTransform(scaleX: 0.95, y: 0.95)
        UIView.animate(withDuration: 0.1) {
            self.refreshButton.transform = .identity
        }
    }
    
    private func updateDeviceInfo() {
        // 清空现有内容
        stackView.arrangedSubviews.forEach { $0.removeFromSuperview() }
        
        // 添加刷新按钮
        stackView.addArrangedSubview(refreshButton)
        refreshButton.heightAnchor.constraint(equalToConstant: 50).isActive = true
        
        // 屏幕信息
        addSection("📱 屏幕信息", items: [
            ("屏幕尺寸", "\(DeviceMetrics.screenWidth) × \(DeviceMetrics.screenHeight) pt"),
            ("屏幕分辨率", "\(Int(DeviceMetrics.screenResolution.width)) × \(Int(DeviceMetrics.screenResolution.height)) px"),
            ("缩放比例", "\(DeviceMetrics.screenScale)x"),
            ("界面方向", orientationString(DeviceMetrics.interfaceOrientation))
        ])
        
        // 安全区域信息
        let safeArea = DeviceMetrics.safeAreaInsets
        addSection("🛡️ 安全区域", items: [
            ("顶部安全区域", "\(safeArea.top) pt"),
            ("底部安全区域", "\(safeArea.bottom) pt"),
            ("左侧安全区域", "\(safeArea.left) pt"),
            ("右侧安全区域", "\(safeArea.right) pt"),
            ("状态栏高度", "\(DeviceMetrics.statusBarHeight) pt"),
            ("导航栏高度", "\(DeviceMetrics.navigationBarHeight) pt"),
            ("顶部栏总高度", "\(DeviceMetrics.topBarHeight) pt"),
            ("标签栏总高度", "\(DeviceMetrics.tabBarHeight) pt")
        ])
        
        // 设备特性
        addSection("🔍 设备特性", items: [
            ("设备类型", DeviceMetrics.isPad ? "iPad" : "iPhone"),
            ("设备型号", DeviceMetrics.deviceModelIdentifier),
            ("系统版本", "iOS \(DeviceMetrics.systemVersion)"),
            ("是否刘海屏", DeviceMetrics.isNotchedScreen ? "是" : "否"),
            ("是否灵动岛", DeviceMetrics.isDynamicIslandDevice ? "是" : "否"),
            ("是否横屏", DeviceMetrics.isLandscape ? "是" : "否"),
            ("是否竖屏", DeviceMetrics.isPortrait ? "是" : "否"),
            ("支持多任务", DeviceMetrics.supportsMultitasking ? "是" : "否")
        ])
        
        // 应用信息
        addSection("📦 应用信息", items: [
            ("应用版本", DeviceMetrics.appVersion),
            ("构建号", DeviceMetrics.appBuildNumber),
            ("Bundle ID", DeviceMetrics.appBundleIdentifier),
            ("当前时间戳", "\(DeviceMetrics.currentTimestamp)")
        ])
        
        // 控制器信息
        let currentVC = DeviceMetrics.currentViewController
        let rootVC = DeviceMetrics.rootViewController
        let navVC = DeviceMetrics.currentNavigationController
        let tabVC = DeviceMetrics.currentTabBarController
        
        addSection("🎮 控制器信息", items: [
            ("当前控制器", String(describing: type(of: currentVC))),
            ("根控制器", String(describing: type(of: rootVC))),
            ("导航控制器", navVC != nil ? String(describing: type(of: navVC!)) : "无"),
            ("标签控制器", tabVC != nil ? String(describing: type(of: tabVC!)) : "无")
        ])
        
        // 窗口信息
        let keyWindow = DeviceMetrics.keyWindow
        let allWindows = DeviceMetrics.allWindows
        
        addSection("🪟 窗口信息", items: [
            ("关键窗口", keyWindow != nil ? "存在" : "不存在"),
            ("窗口数量", "\(allWindows.count)"),
            ("关键窗口尺寸", keyWindow != nil ? "\(keyWindow!.bounds.size)" : "N/A")
        ])
    }
    
    private func addSection(_ title: String, items: [(String, String)]) {
        // 添加标题
        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = UIFont.boldSystemFont(ofSize: 18)
        titleLabel.textColor = .label
        stackView.addArrangedSubview(titleLabel)
        
        // 添加内容容器
        let containerView = UIView()
        containerView.backgroundColor = .secondarySystemBackground
        containerView.layer.cornerRadius = 12
        containerView.layer.masksToBounds = true
        
        let contentStackView = UIStackView()
        contentStackView.axis = .vertical
        contentStackView.spacing = 8
        contentStackView.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(contentStackView)
        
        NSLayoutConstraint.activate([
            contentStackView.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 16),
            contentStackView.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 16),
            contentStackView.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -16),
            contentStackView.bottomAnchor.constraint(equalTo: containerView.bottomAnchor, constant: -16)
        ])
        
        // 添加每一项
        for (key, value) in items {
            let itemView = createItemView(key: key, value: value)
            contentStackView.addArrangedSubview(itemView)
        }
        
        stackView.addArrangedSubview(containerView)
    }
    
    private func createItemView(key: String, value: String) -> UIView {
        let containerView = UIView()
        
        let keyLabel = UILabel()
        keyLabel.text = key
        keyLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        keyLabel.textColor = .label
        keyLabel.setContentHuggingPriority(.defaultHigh, for: .horizontal)
        
        let valueLabel = UILabel()
        valueLabel.text = value
        valueLabel.font = UIFont.systemFont(ofSize: 16)
        valueLabel.textColor = .secondaryLabel
        valueLabel.textAlignment = .right
        valueLabel.numberOfLines = 0
        
        keyLabel.translatesAutoresizingMaskIntoConstraints = false
        valueLabel.translatesAutoresizingMaskIntoConstraints = false
        
        containerView.addSubview(keyLabel)
        containerView.addSubview(valueLabel)
        
        NSLayoutConstraint.activate([
            keyLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor),
            keyLabel.centerYAnchor.constraint(equalTo: containerView.centerYAnchor),
            keyLabel.topAnchor.constraint(greaterThanOrEqualTo: containerView.topAnchor),
            keyLabel.bottomAnchor.constraint(lessThanOrEqualTo: containerView.bottomAnchor),
            
            valueLabel.leadingAnchor.constraint(greaterThanOrEqualTo: keyLabel.trailingAnchor, constant: 16),
            valueLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor),
            valueLabel.centerYAnchor.constraint(equalTo: containerView.centerYAnchor),
            valueLabel.topAnchor.constraint(greaterThanOrEqualTo: containerView.topAnchor),
            valueLabel.bottomAnchor.constraint(lessThanOrEqualTo: containerView.bottomAnchor),
            
            containerView.heightAnchor.constraint(greaterThanOrEqualToConstant: 32)
        ])
        
        return containerView
    }
    
    private func orientationString(_ orientation: UIInterfaceOrientation) -> String {
        switch orientation {
        case .portrait:
            return "竖屏"
        case .portraitUpsideDown:
            return "倒竖屏"
        case .landscapeLeft:
            return "左横屏"
        case .landscapeRight:
            return "右横屏"
        case .unknown:
            return "未知"
        @unknown default:
            return "未知"
        }
    }
}

// MARK: - 使用示例

extension DeviceMetricsExampleViewController {
    
    /// 演示如何使用 DeviceMetrics 进行布局适配
    func demonstrateLayoutAdaptation() {
        // 根据设备类型调整布局
        if DeviceMetrics.isPad {
            // iPad 布局
            print("使用 iPad 布局")
        } else {
            // iPhone 布局
            print("使用 iPhone 布局")
        }
        
        // 根据屏幕方向调整布局
        if DeviceMetrics.isLandscape {
            print("横屏布局")
        } else {
            print("竖屏布局")
        }
        
        // 根据安全区域调整约束
        let safeArea = DeviceMetrics.safeAreaInsets
        print("安全区域边距: \(safeArea)")
        
        // 根据刘海屏特性调整UI
        if DeviceMetrics.isNotchedScreen {
            print("刘海屏设备，需要特殊处理")
        }
    }
    
    /// 演示如何使用控制器查找功能
    func demonstrateViewControllerFinding() {
        // 获取当前顶层控制器
        if let currentVC = DeviceMetrics.currentViewController {
            print("当前控制器: \(currentVC)")
        }
        
        // 获取导航控制器
        if let navVC = DeviceMetrics.currentNavigationController {
            print("当前导航控制器: \(navVC)")
        }
        
        // 在主线程执行操作
        DeviceMetrics.executeOnMainThread {
            print("在主线程执行")
        }
        
        // 延迟执行
        DeviceMetrics.executeOnMainThread(after: 1.0) {
            print("延迟1秒执行")
        }
    }
}
