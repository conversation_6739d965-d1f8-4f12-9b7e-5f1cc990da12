//
//  DeviceMetrics.swift
//  Student_IOS
//
//  Created by Junq on 2025/8/8.
//

import Foundation
import UIKit

@objcMembers
final class DeviceMetrics: NSObject {
    
    // MARK: - 1. 屏幕尺寸与安全区

    class var screenWidth: CGFloat {
        UIScreen.main.bounds.width
    }

    class var screenHeight: CGFloat {
        UIScreen.main.bounds.height
    }

    class var screenResolution: CGSize {
        let scale = UIScreen.main.scale
        return CGSize(width: screenWidth * scale, height: screenHeight * scale)
    }

    class var statusBarHeight: CGFloat {
        UIApplication.shared.connectedScenes
            .compactMap { $0 as? UIWindowScene }
            .first?.statusBarManager?.statusBarFrame.height ?? 0
    }

    class var navigationBarHeight: CGFloat {
        44.0
    }

    class var topBarHeight: CGFloat {
        statusBarHeight + navigationBarHeight
    }

    class var bottomSafeAreaHeight: CGFloat {
        keyWindow?.safeAreaInsets.bottom ?? 0
    }

    class var topSafeAreaHeight: CGFloat {
        keyWindow?.safeAreaInsets.top ?? 0
    }

    class var tabBarHeight: CGFloat {
        49.0 + bottomSafeAreaHeight
    }

    // MARK: - 2. 设备特性判断

    /// 是否是刘海屏（根据 bottom 安全区判断）
    class var isNotchedScreen: Bool {
        bottomSafeAreaHeight > 0
    }

    /// 是否是灵动岛设备（topSafeAreaHeight > 51）
    class var isDynamicIslandDevice: Bool {
        topSafeAreaHeight > 51
    }

    /// 当前是否是横屏
    class var isLandscape: Bool {
        interfaceOrientation.isLandscape
    }

    /// 当前是否是 iPad
    class var isPad: Bool {
        UIDevice.current.userInterfaceIdiom == .pad
    }

    /// 当前界面方向
    class var interfaceOrientation: UIInterfaceOrientation {
        UIApplication.shared.connectedScenes
            .compactMap { $0 as? UIWindowScene }
            .first?.interfaceOrientation ?? .unknown
    }

    /// 当前方向 rawValue（OC 中调用）
    class var interfaceOrientationRaw: Int {
        interfaceOrientation.rawValue
    }

    // MARK: - 3. 控制器相关

    /// 当前顶层 ViewController（用于弹窗等）
    class var currentViewController: UIViewController? {
        guard let rootVC = keyWindow?.rootViewController else { return nil }
        return findTopViewController(from: rootVC)
    }

    /// Root ViewController（入口 VC）
    class var rootViewController: UIViewController? {
        keyWindow?.rootViewController
    }

    private class func findTopViewController(from vc: UIViewController) -> UIViewController {
        if let presented = vc.presentedViewController {
            return findTopViewController(from: presented)
        }
        if let nav = vc as? UINavigationController {
            return findTopViewController(from: nav.visibleViewController ?? nav)
        }
        if let tab = vc as? UITabBarController {
            return findTopViewController(from: tab.selectedViewController ?? tab)
        }
        return vc
    }

    // MARK: - 4. UIWindow & Scene

    class var keyWindow: UIWindow? {
//        UIApplication.shared.connectedScenes
//            .compactMap { $0 as? UIWindowScene }
//            .flatMap { $0.windows }
//            .first { $0.isKeyWindow }
//        
        UIApplication.shared.connectedScenes
            .filter { $0.activationState == .foregroundActive }
            .compactMap { $0 as? UIWindowScene }.first?.windows
            .filter { $0.isKeyWindow }.first
    }
}
