//
//  MrkLinkUrl.m
//  Student_IOS
//
//  Created by merit on 2022/5/30.
//

#import "MrkLinkUrl.h"


NSString *const MRKLinkUserProtocol = @"user-agreement";
NSString *const MRKLinkPrivacyProtocol = @"privacy-clause";
NSString *const MRKLinkTrainingReport = @"new-training-report";
NSString *const MRKLinkAutoRenewalProtocol = @"auto-renew-agreement";
NSString *const MRKLinkActivityReport = @"banner/activity-report";
NSString *const MRKLinkPlanReport = @"plan-report";
NSString *const MRKLinkCoachDetail = @"coach-page";
NSString *const MRKLinkDeviceConnectHelp = @"device-connection";
NSString *const MRKLinkCourseDetail = @"course-detail";
NSString *const MRKLinkThemeDetail = @"theme-detail";
NSString *const MRKLinkCoursePlanDetail = @"course-plan";
NSString *const MRKLinkNewWebActivity = @"banner/config-activity";
NSString *const MRKLinkGuideNewUser = @"novice-guide";
NSString *const MRKLinkVideoTV = @"projection-screen";
NSString *const MRKLinkProductEncyclopedia = @"product-encyclopedia";
NSString *const MRKLinkMeritActivityQualification = @"banner/activity-qualification";
NSString *const MRKLinkNoviceActivities = @"banner/novice-activities";
NSString *const MRKLinkNewPersonAlertImageUrl = @"https://static.merach.com/other/20220811/lQLPJxaUkl3_us_NBEHNA3WwBWf0n1BdSIoC9Hx7BAAzAA_885_1089.png";
NSString *const MRKLinkNewPersonAlertTaskUrl = @"special-for-new-users";
NSString *const MRKLinkAbilityTestUrl = @"animation-game/#/dynamic-evaluation"; ///暂时不要动
NSString *const MRKUltraThemeShareUrl = @"free-training-theme";
NSString *const MRKUltraCourseShareUrl = @"free-training-detail";
NSString *const MRKUserHelpAndFeedback = @"customer-service";
NSString *const MRKKingCourseUrl = @"trump-course-new";
NSString *const MRKHomePageTrumpCourse = @"trump-course-list";
NSString *const MRKHomePageRecommendSubject = @"theme-course-list";
NSString *const MRKHomePageRecommendListSubject = @"theme-course";
NSString *const MRKHomePagePopularityRank = @"popularity-rank";
NSString *const MRKHomePageXenjoyCourseMore = @"exclusive-authority";
NSString *const MRKVipPageXenjoyInterest = @"equity-details";
NSString *const MRKHeartRateEncyclopedia = @"heart-rate-encyclopedia";
NSString *const MRKUserLevelRule = @"level-rule";
NSString *const MRKWebConnectLink = @"connect-link-v2";
NSString *const MRKHealthReport = @"health-report";
NSString *const MRKActiveExercise = @"active-exercise";
NSString *const MRKTrainPart = @"many-page/#/burning-friends-community?share=1";
NSString *const MRKMeritShop = @"https://shop115090580.m.youzan.com/wscshop/showcase/homepage?kdt_id=114898412&reft=1684576612046&spm=f.110042348&is_silence_auth=1";
NSString *const MRKNewUserAction = @"good-gift-v2";
NSString *const MRKHeartConnectQuestion = @"heart-connection";
NSString *const MRKTrainDataQuestion = @"data-common-problem";

NSString *const MRKAIChat = @"chat";
NSString *const MRKAIPlanEntrance = @"aiEntrance";
NSString *const MRKAIPlanReport = @"aiPlanReport";
NSString *const MRKAIPlanReportV2 = @"aiPlanReportV2";
NSString *const MRKAIUsageGuide = @"aiUsageGuide";
NSString *const MRKAIUsageGuideV2 = @"aiUsageGuideV2";
NSString *const MRKAISportDietAnalysis = @"analysisExerciseDiet";
NSString *const MRKAIDietOpenVip = @"diet-transfer";
NSString *const MRKAIPlanOpenVip = @"purchase-plan-introduce";
NSString *const MRKAIPlanDietRecommend = @"dietRecommend";
