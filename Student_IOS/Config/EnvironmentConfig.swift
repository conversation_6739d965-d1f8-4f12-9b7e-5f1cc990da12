//
//  EnvironmentConfig.swift
//  Student_IOS
//
//  Created by Junq on 2025/4/21.
//

import Foundation

// MARK: - Environment Types
enum AppEnvironment: String, CaseIterable {
    case dev8000 = "dev-8000"
    case pre = "pre"
    case production = "production"
    case pure8000 = "pure-8000"
    case pure5000 = "pure-5000"
    
    var displayName: String {
        switch self {
        case .dev8000: return "dev-8000[外网]"
        case .pre: return "预发"
        case .production: return "正式"
        case .pure8000: return "pure-8000"
        case .pure5000: return "pure-5000"
        }
    }
}

// MARK: - Environment Configuration Protocol
protocol EnvironmentConfig {
    var title: String { get }
    var baseURL: URL { get }
    var socketURL: URL { get }
    var web: URL { get }
    var webPath: URL { get }
    var aiHttp: URL { get }
    var aiWeb: URL { get }
    var aiVoiceUri: URL { get }
    var aiConfig: String { get }
    var isTra: Int { get }
    var env: Int { get }
    var envCode: Int { get }
    var isProduction: Bool { get }
    var isDebugMode: Bool { get }
}

// MARK: - Environment Manager
class EnvironmentManager {
    static let shared = EnvironmentManager()
    
    private var _currentEnvironment: AppEnvironment = .pure8000
    private var _currentConfig: EnvironmentConfig?
    
    var currentEnvironment: AppEnvironment {
        get { return _currentEnvironment }
        set {
            _currentEnvironment = newValue
            _currentConfig = nil // Reset config cache
            NotificationCenter.default.post(name: .environmentDidChange, object: newValue)
        }
    }
    
    var currentConfig: EnvironmentConfig {
        if let config = _currentConfig {
            return config
        }
        
        let config = createConfig(for: currentEnvironment)
        _currentConfig = config
        return config
    }
    
    private init() {
        loadEnvironmentFromUserDefaults()
    }
    
    private func createConfig(for environment: AppEnvironment) -> EnvironmentConfig {
        switch environment {
        case .dev8000: return Dev8000Config()
        case .pre: return PreConfig()
        case .production: return ProductionConfig()
        case .pure8000: return Pure8000Config()
        case .pure5000: return Pure5000Config()
        }
    }
    
    func switchEnvironment(to environment: AppEnvironment) {
        currentEnvironment = environment
        saveEnvironmentToUserDefaults()
    }
    
    private func saveEnvironmentToUserDefaults() {
        UserDefaults.standard.set(currentEnvironment.rawValue, forKey: "SelectedEnvironment")
    }
    
    private func loadEnvironmentFromUserDefaults() {
        if let savedEnv = UserDefaults.standard.string(forKey: "SelectedEnvironment"),
           let environment = AppEnvironment(rawValue: savedEnv) {
            _currentEnvironment = environment
        }
    }
}

// MARK: - Notification Extension
extension Notification.Name {
    static let environmentDidChange = Notification.Name("EnvironmentDidChange")
}

// MARK: - Environment Configurations
struct Dev8000Config: EnvironmentConfig {
    let title = "dev-8000[外网]"
    let baseURL = URL(string: "https://8000.merach.com")!
    let socketURL = URL(string: "ws://39.170.15.145:8000")!
    let web = URL(string: "http://testconsole.merach.com/webview/")!
    let webPath = URL(string: "http://192.168.9.40:30200/")!
    let aiHttp = URL(string: "http://39.170.15.145:18000")!
    let aiWeb = URL(string: "https://testmia.merach.com/")!
    let aiVoiceUri = URL(string: "ws://39.170.15.145:18000")!
    let aiConfig = "https://merit-app-test.merach.com/ai/ai_config.json"
    let isTra = 0
    let env = 101
    let envCode = 8000
    let isProduction = false
    let isDebugMode = true
}

struct PreConfig: EnvironmentConfig {
    let title = "预发"
    let baseURL = URL(string: "https://preapi.merach.com")!
    let socketURL = URL(string: "wss://preapi.merach.com")!
    let web = URL(string: "https://stagingconsole.merach.com/webview/")!
    let webPath = URL(string: "https://h5-pre.merach.com/")!
    let aiHttp = URL(string: "https://premia.merach.com/api")!
    let aiWeb = URL(string: "https://premia.merach.com/")!
    let aiVoiceUri = URL(string: "wss://pre-wss-ai.merach.com")!
    let aiConfig = "https://merit-app-pre.merach.com/ai/ai_config.json"
    let isTra = 1
    let env = 201
    let envCode = 201
    let isProduction = false
    let isDebugMode = true
}

struct ProductionConfig: EnvironmentConfig {
    let title = "正式"
    let baseURL = URL(string: "https://api.merach.com")!
    let socketURL = URL(string: "wss://api.merach.com")!
    let web = URL(string: "https://console.merach.com/webview/")!
    let webPath = URL(string: "https://h5.merach.com/")!
    let aiHttp = URL(string: "https://mia.merach.com/api")!
    let aiWeb = URL(string: "https://mia.merach.com/")!
    let aiVoiceUri = URL(string: "wss://wss-ai.merach.com")!
    let aiConfig = "https://static.merach.com/ai/ai_config.json"
    let isTra = 0
    let env = 301
    let envCode = 301
    let isProduction = true
    let isDebugMode = false
}

struct Pure8000Config: EnvironmentConfig {
    let title = "pure-8000"
    let baseURL = URL(string: "https://8000.mrkw.cn:2443")!
    let socketURL = URL(string: "wss://8000.mrkw.cn")!
    let web = URL(string: "http://testconsole.merach.com/webview/")!
    let webPath = URL(string: "https://8000-h5.mrkw.cn:2443/")!
    let aiHttp = URL(string: "https://test-wss-ai.mrkw.cn:2443")!
    let aiWeb = URL(string: "https://testmia.mrkw.cn:2443/")!
    let aiVoiceUri = URL(string: "wss://test-wss-ai.mrkw.cn:2443")!
    let aiConfig = "https://merit-app-test.merach.com/ai/ai_config.json"
    let isTra = 0
    let env = 101
    let envCode = 11000
    let isProduction = false
    let isDebugMode = true
}

struct Pure5000Config: EnvironmentConfig {
    let title = "pure-5000"
    let baseURL = URL(string: "https://5000.mrkw.cn:2443")!
    let socketURL = URL(string: "wss://5000.mrkw.cn")!
    let web = URL(string: "http://testconsole.merach.com/webview/")!
    let webPath = URL(string: "https://5000-h5.mrkw.cn:2443/")!
    let aiHttp = URL(string: "https://test-wss-ai.mrkw.cn:2443")!
    let aiWeb = URL(string: "https://testmia.mrkw.cn:2443/")!
    let aiVoiceUri = URL(string: "wss://test-wss-ai.mrkw.cn:2443")!
    let aiConfig = "https://merit-app-test.merach.com/ai/ai_config.json"
    let isTra = 0
    let env = 101
    let envCode = 10000
    let isProduction = false
    let isDebugMode = true
}

// MARK: - 便捷访问扩展
extension EnvironmentManager {
    // MARK: - 配置值访问（实例 & 静态）
    
    /// 当前环境标题
    var title: String {
        return currentConfig.title
    }
    /// 静态访问：当前环境标题
    static var title: String {
        return shared.title
    }
    
    /// 基础API URL
    var baseURL: URL {
        return currentConfig.baseURL
    }
    /// 静态访问：基础API URL
    static var baseURL: URL {
        return shared.baseURL
    }
    
    /// WebSocket连接URL
    var socketURL: URL {
        return currentConfig.socketURL
    }
    /// 静态访问：WebSocket连接URL
    static var socketURL: URL {
        return shared.socketURL
    }
    
    /// Web页面URL
    var web: URL {
        return currentConfig.web
    }
    /// 静态访问：Web页面URL
    static var web: URL {
        return shared.web
    }
    
    /// Web路径URL
    var webPath: URL {
        return currentConfig.webPath
    }
    /// 静态访问：Web路径URL
    static var webPath: URL {
        return shared.webPath
    }
    
    /// AI HTTP服务URL
    var aiHttp: URL {
        return currentConfig.aiHttp
    }
    /// 静态访问：AI HTTP服务URL
    static var aiHttp: URL {
        return shared.aiHttp
    }
    
    /// AI Web页面URL
    var aiWeb: URL {
        return currentConfig.aiWeb
    }
    /// 静态访问：AI Web页面URL
    static var aiWeb: URL {
        return shared.aiWeb
    }
    
    /// AI语音服务URI
    var aiVoiceUri: URL {
        return currentConfig.aiVoiceUri
    }
    /// 静态访问：AI语音服务URI
    static var aiVoiceUri: URL {
        return shared.aiVoiceUri
    }
    
    /// AI配置文件URL
    var aiConfig: String {
        return currentConfig.aiConfig
    }
    /// 静态访问：AI配置文件URL
    static var aiConfig: String {
        return shared.aiConfig
    }
    
    /// 是否为测试环境标识
    var isTra: Int {
        return currentConfig.isTra
    }
    /// 静态访问：是否为测试环境标识
    static var isTra: Int {
        return shared.isTra
    }
    
    /// 环境标识
    var env: Int {
        return currentConfig.env
    }
    /// 静态访问：环境标识
    static var env: Int {
        return shared.env
    }
    
    /// 环境代码
    var envCode: Int {
        return currentConfig.envCode
    }
    /// 静态访问：环境代码
    static var envCode: Int {
        return shared.envCode
    }
    
    // MARK: - 环境状态访问（实例 & 静态）
    
    /// 当前环境显示名称
    var currentEnvironmentDisplayName: String {
        return currentEnvironment.displayName
    }
    /// 静态访问：当前环境显示名称
    static var currentEnvironmentDisplayName: String {
        return shared.currentEnvironmentDisplayName
    }
    
    /// 是否为生产环境
    var isProductionEnvironment: Bool {
        return currentConfig.isProduction
    }
    /// 静态访问：是否为生产环境
    static var isProductionEnvironment: Bool {
        return shared.isProductionEnvironment
    }
    
    /// 是否为调试模式
    var isDebugMode: Bool {
        return currentConfig.isDebugMode
    }
    /// 静态访问：是否为调试模式
    static var isDebugMode: Bool {
        return shared.isDebugMode
    }
    
    /// 所有可用环境
    var availableEnvironments: [AppEnvironment] {
        return AppEnvironment.allCases
    }
    /// 静态访问：所有可用环境
    static var availableEnvironments: [AppEnvironment] {
        return shared.availableEnvironments
    }
    
    /// 当前环境
    static var currentEnvironment: AppEnvironment {
        get { return shared.currentEnvironment }
        set { shared.currentEnvironment = newValue }
    }
    
    /// 当前配置
    static var currentConfig: EnvironmentConfig {
        return shared.currentConfig
    }
    
    
    // MARK: - 静态方法
    /// 静态方法：切换环境
    static func switchEnvironment(to environment: AppEnvironment) {
        shared.switchEnvironment(to: environment)
    }
}

// MARK: - 调试工具
#if DEBUG
extension EnvironmentManager {
    /// 打印当前环境配置信息（仅在Debug模式下可用）
    func printCurrentConfiguration() {
        let config = currentConfig
        print("=== 当前环境配置 ===")
        print("环境名称: \(config.title)")
        print("基础URL: \(config.baseURL)")
        print("Socket URL: \(config.socketURL)")
        print("Web URL: \(config.web)")
        print("Web Path: \(config.webPath)")
        print("AI HTTP: \(config.aiHttp)")
        print("AI Web: \(config.aiWeb)")
        print("AI Voice URI: \(config.aiVoiceUri)")
        print("AI Config: \(config.aiConfig)")
        print("是否为生产环境: \(config.isProduction)")
        print("环境代码: \(config.envCode)")
        print("=============EnvironmentManager")
    }
}
#endif


// MARK: - 环境切换UI辅助
extension EnvironmentManager {
    /// 获取环境选择器数据源
    func getEnvironmentPickerData() -> [(title: String, environment: AppEnvironment)] {
        return AppEnvironment.allCases.map { env in
            (title: env.displayName, environment: env)
        }
    }
    
    /// 创建环境切换ActionSheet
    func createEnvironmentSwitchActionSheet(
        presentingViewController: UIViewController,
        completion: ((AppEnvironment) -> Void)? = nil
    ) {
        let alertController = UIAlertController(
            title: "选择环境",
            message: "当前环境：\(currentEnvironmentDisplayName)",
            preferredStyle: .actionSheet
        )
        
        for environment in AppEnvironment.allCases {
            let action = UIAlertAction(
                title: environment.displayName,
                style: .default
            ) { [weak self] _ in
                self?.switchEnvironment(to: environment)
                completion?(environment)
            }
            
            // 标记当前选中的环境
            if environment == currentEnvironment {
                action.setValue(true, forKey: "checked")
            }
            
            alertController.addAction(action)
        }
        
        let cancelAction = UIAlertAction(title: "取消", style: .cancel)
        alertController.addAction(cancelAction)
        
        // iPad适配
        if let popover = alertController.popoverPresentationController {
            popover.sourceView = presentingViewController.view
            popover.sourceRect = CGRect(
                x: presentingViewController.view.bounds.midX,
                y: presentingViewController.view.bounds.midY,
                width: 0,
                height: 0
            )
        }
        
        presentingViewController.present(alertController, animated: true)
    }
}

// MARK: - 网络配置辅助
extension EnvironmentConfig {
    /// 获取完整的API URL
    func apiURL(path: String) -> URL? {
        return URL(string: path, relativeTo: baseURL)
    }
    
    /// 获取完整的Web URL
    func webURL(path: String) -> URL? {
        return URL(string: path, relativeTo: web)
    }
    
    /// 获取完整的AI API URL
    func aiAPIURL(path: String) -> URL? {
        return URL(string: path, relativeTo: aiHttp)
    }
    
    /// 获取WebSocket连接URL
    func socketURL(path: String) -> URL? {
        return URL(string: path, relativeTo: socketURL)
    }
}

// MARK: - 环境信息导出
extension EnvironmentManager {
    /// 导出当前环境配置为字典
    func exportCurrentConfiguration() -> [String: Any] {
        let config = currentConfig
        return [
            "title": config.title,
            "baseURL": config.baseURL.absoluteString,
            "socketURL": config.socketURL.absoluteString,
            "web": config.web.absoluteString,
            "webPath": config.webPath.absoluteString,
            "aiHttp": config.aiHttp.absoluteString,
            "aiWeb": config.aiWeb.absoluteString,
            "aiVoiceUri": config.aiVoiceUri.absoluteString,
            "aiConfig": config.aiConfig,
            "isTra": config.isTra,
            "env": config.env,
            "envCode": config.envCode,
            "isProduction": config.isProduction,
            "isDebugMode": config.isDebugMode
        ]
    }
    
    /// 导出所有环境配置
    func exportAllConfigurations() -> [[String: Any]] {
        return AppEnvironment.allCases.map { env in
            let config = createConfig(for: env)
            return [
                "environment": env.rawValue,
                "title": config.title,
                "baseURL": config.baseURL.absoluteString,
                "socketURL": config.socketURL.absoluteString,
                "web": config.web.absoluteString,
                "webPath": config.webPath.absoluteString,
                "aiHttp": config.aiHttp.absoluteString,
                "aiWeb": config.aiWeb.absoluteString,
                "aiVoiceUri": config.aiVoiceUri.absoluteString,
                "aiConfig": config.aiConfig,
                "isTra": config.isTra,
                "env": config.env,
                "envCode": config.envCode,
                "isProduction": config.isProduction
            ]
        }
    }
}

// MARK: - 环境验证
extension EnvironmentConfig {
    /// 验证当前环境配置是否有效
    func validateConfiguration() -> (isValid: Bool, errors: [String]) {
        var errors: [String] = []
        
        // 验证URL格式
        let urls = [
            ("baseURL", baseURL),
            ("socketURL", socketURL),
            ("web", web),
            ("webPath", webPath),
            ("aiHttp", aiHttp),
            ("aiWeb", aiWeb),
            ("aiVoiceUri", aiVoiceUri)
        ]
        
        for (name, url) in urls {
            if url.absoluteString.isEmpty {
                errors.append("\(name) 不能为空")
            }
        }
        
        // 验证aiConfig URL格式
        if aiConfig.isEmpty {
            errors.append("aiConfig 不能为空")
        } else if URL(string: aiConfig) == nil {
            errors.append("aiConfig 格式无效")
        }
        
        // 验证环境代码
        if envCode <= 0 {
            errors.append("envCode 必须大于0")
        }
        
        return (errors.isEmpty, errors)
    }
}

// MARK: - 使用示例和文档
/*
 使用示例：
 
 1. 获取当前环境配置：
 let config = EnvironmentManager.shared.currentConfig
 let baseURL = config.baseURL
 
 2. 切换环境：
 EnvironmentManager.shared.switchEnvironment(to: .production)
 
 3. 监听环境变化：
 NotificationCenter.default.addObserver(
     forName: .environmentDidChange,
     object: nil,
     queue: .main
 ) { notification in
     if let newEnvironment = notification.object as? AppEnvironment {
         print("环境已切换到: \(newEnvironment.displayName)")
     }
 }
 
 4. 创建环境切换界面：
 EnvironmentManager.shared.createEnvironmentSwitchActionSheet(
     presentingViewController: self
 ) { newEnvironment in
     print("用户选择了环境: \(newEnvironment.displayName)")
 }
 
 5. 构建API URL：
 let apiURL = config.apiURL(path: "/api/v1/users")
 
 6. 调试模式下打印配置：
 #if DEBUG
 EnvironmentManager.shared.printCurrentConfiguration()
 #endif
 
 7. 验证配置：
 let validation = config.validateConfiguration()
 if !validation.isValid {
     print("配置错误: \(validation.errors)")
 }
 */
