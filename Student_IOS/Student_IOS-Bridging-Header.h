//
//  Student_IOS-Bridging-Header.h
//  Student_IOS
//
//  Created by 盼张 on 2022/11/21.
//

#ifndef Student_IOS_Bridging_Header_h
#define Student_IOS_Bridging_Header_h


#import "TGApiManager.h"
#import "MRKDeviceModel.h"
#import "EquipmentModel.h"
#import "BlueConfig.h"
#import "MRKCGUtilities.h"
#import "MRKCustomShareContentView.h"
#import "MRKPushManager.h"
#import "MRKFlutterViewController.h"
#import "SJRouter.h"
#import "MRKNavigationController.h"
#import "MRKLinkRouterManager.h"
#import "NSObject+helper.h"
#import "UIViewController+Trace.h"
#import "UINavigationController+FDFullscreenPopGesture.h"
#import "MRKBaseController.h"
#import "WebViewViewController.h"
#import "MRKSwiftUseOCHelp.h"
#import "MrkLinkUrl.h"
#import "MRKShareManager.h"
#import "MBProgressHUD+MRK.h"
#import "YTKNetwork.h"
#import "MRKBaseRequest.h"
#import "UserInfo.h"
#import "RouteManager.h"
#import "RouteManager+Course.h"
#import "NSObject+YYModel.h"
#import "Login.h"
#import "User.h"
#import "MRKTraceManager.h"
#import "UIViewController+Addons.h"
#import "Masonry.h"
#import "UIView+Effects.h"
#import "UIView+AZGradient.h"
#import "UIView+MJExtension.h"

#import "ScaleBaseManager.h"
#import "WeightDeviceManager.h"
#import "HealthPersonModel.h"

#import <WatchConnectivity/WatchConnectivity.h>
#import "BlufiManager.h"
#import "MRKDeviceManager.h"
#import "MRKSportDataModel.h"
#import "BaseEquipData.h"
#import <CoreBluetooth/CBAttribute.h>
#import "MRKEquipmentTypeData.h"
#import "JXCategoryListContainerView.h"

#import "Masonry.h"

#import "MRKAIPlanLogic.h"
#import "MRKCourseModel.h"
#import "MRKDynamicIslandManager.h"
#import "MRKDailyLogic.h"
#import "MRKHealthManager.h"
#import "MrkAlertManager.h"
#import "UIApplication+Add.h"
#import "UIImage+Helper.h"
#import "MRKGTManager.h"
#import "MRKWebJSHandler.h"
#import "MRKCompleteEvaluationCourseController.h"
#import "MRKPlanOverViewModel.h"

#import "MRKBaseModel.h"
#import "MRKSVipViews.h"
#import "MRKVipCardModel.h"
#import "DYFStoreManager.h"
#import "MRKLoginTipAlert.h"
#import "MRKPurchaseManager.h"
#import "MRKPurchaseCouponModel.h"


#endif /* Student_IOS_Bridging_Header_h */
