//
//  MRKMainPageController.m
//  Student_IOS
//
//  Created by <PERSON><PERSON> on 2024/6/11.
//

#import "MRKMainPageController.h"
#import "MRKMainNavigationBar.h"
#import "MainPageTableView.h"
#import "MRKMainPageViewModel.h"

#import "MRKDeviceURLRequest.h"
#import "MRKAdjustTargetController.h"
#import "MRKSwitchDeviceController.h"
#import "MrkGradientView.h"
#import "MRKDeviceListManager.h"
#import "DeviceSearchViewController.h"
#import "MRKDeviceDetailController.h"
#import "MRKCategoryCourseController.h"
#import "MRKAllCourseViewController.h"
#import "AllLivingCourseController.h"
#import "MRKHomeAlertManager.h"
#import "MRKVersionCheck.h"

#import "OSSManager.h"
#import "MRKLogManager.h"

#import "MRKAlertController.h"
#import "MRKPopupManager.h"
#import "MRKHomeGuideViewController.h"
#import "MRKTabBarController.h"
#import "MRKAIPlanLogic.h"
#import "MRKMainGuideManager.h"

#import "MRKCategoryCourseController.h"
#import "MRKCoursePlanController.h"


@interface MRKMainPageController ()<MainPageTableViewDelegate>
@property (nonatomic, strong) MRKMainPageViewModel *pageModel;
@property (nonatomic, strong) MRKMainNavigationBar *navBar;
@property (nonatomic, strong) MrkGradientView *backGradientView;
@property (nonatomic, strong) MainPageTableView *pageTableView;
@property (nonatomic, assign) BOOL isRefresh;
@property (nonatomic, strong) MRKMiaIconView *miaView; //mia语音
@property (nonatomic, strong) MRKFlutterAiVoiceController *aiVoiceController; //获取语音页面
@property (nonatomic, assign) BOOL isPresenting; // 标记是否正在弹出
@property (nonatomic, assign) BOOL needDismiss; // 标记是否需要在弹出完成后关闭
@property (nonatomic, strong) MRKHomeGuideViewController *miaGuideView;  //mia 引导
@end

@implementation MRKMainPageController

- (void)dealloc {
    NSLog(@"============================MRKMainPageController==========================");
}

- (MRKMainPageViewModel *)pageModel{
    if (!_pageModel){
        _pageModel = [[MRKMainPageViewModel alloc] init];
    }
    return _pageModel;
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    
    //有些操作会从首页自动跳转到二级也买呢
    //如果首页消失时，正在弹mia引导，并且mia引导没有手动关闭，那么这次引导关闭，下次首页出现时，在弹出来
    if (self.miaGuideView) {
        [MRKMainGuideManager reset];
        [self.miaGuideView removeGuideView];
        self.miaGuideView = nil;
    }
}

- (void)viewDidDisappear:(BOOL)animated {
    [super viewDidDisappear:animated];
    [self.pageTableView viewDidDisappear:animated];
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    [self.pageTableView viewDidAppear:animated];
    
    [self addMiaview];
    
    ///
    if (![MRKMainGuideManager showGuide]) {
        /// 首页弹窗检查
        [[MRKHomeAlertManager shareManager] addTarget:self checkAlertCode:MRKRecommendPopupCode];
        
        /// 检查勋章弹窗
        [MRKGrowAlertManager postLevelAndMedalRequest];
    }
    
    ///走接口请求
    if (self.isRefresh) {
        [self.pageModel refreshPagePartData];
    }
    
    ///日志
    [self judgeAndUploadLog];
}


/// 判断是否上传日志
- (void)judgeAndUploadLog {
    NSDictionary *dict = @{@"type": @3}; // 1-空练设置开关，2-阻力映射设置开关， 3-用户日志上传开关
    @weakify(self);
    [MRKBaseRequest mrkRequestType:YTKRequestMethodGET
                               url:@"/user/user-setting/get"
                           andParm:dict
                      notShowError:YES
          completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        @strongify(self);
        BOOL status = [[request.responseObject valueForKeyPath:@"data"] boolValue];
        if (status) {
            [self uploadLogFile];
        }
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        
    }];
}

/// 上传日志文件，先压缩成zip
- (void)uploadLogFile{
    // 压缩log
    [MRKLogManager zipLog:^(NSString * _Nonnull path) {
        OSSUploadFileModel *model = [[OSSUploadFileModel alloc] initWithLocalPath:path
                                                                         fileName:[MRKLogManager uploadLogName]
                                                             businessResourceType:UploadBusinessResourceType_Log
                                                                         fileType:UploadFileType_Zip];
        [[OSSManager sharedManager] uploadFile:model success:^{
            [MRKLogManager deleteZipLog]; // 删除本地压缩文件
        } failure:^(NSString * _Nonnull errorStr) {
            [MRKLogManager deleteZipLog]; // 删除本地压缩文件
        }];
    }];
}



- (void)viewDidLoad {
    self.tracePageId = @"new_home";
    [super viewDidLoad];
    
    // Do any additional setup after loading the view.
    self.mrkContentView.lee_theme.LeeConfigBackgroundColor(@"main_background_color");
    [self.mrkContentView addSubview:self.backGradientView];
    [self.backGradientView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.right.mas_equalTo(0);
        make.height.mas_equalTo(WKDHPX(164)+kNavBarHeight);
    }];
    
    @weakify(self);
    self.lee_theme.LeeThemeChangingBlock(^(NSString * _Nonnull tag, UIView * _Nonnull item) {
        @strongify(self);
        if ([tag isEqualToString:THEME_XENJOY]) {
            self.statusBarStyle = UIStatusBarStyleLightContent;
            
            self.backGradientView.gradientStartPoint = CGPointMake(0, 0);
            self.backGradientView.gradientEndPoint = CGPointMake(1, 1);
            self.backGradientView.gradientLocations = @[@0, @0.5, @1];
            self.backGradientView.gradientLayerColors = @[(__bridge id)[UIColorHex(#0B1C41) CGColor],
                                                          (__bridge id)[UIColorHex(#071224) CGColor],
                                                          (__bridge id)[UIColorHex(#071326) CGColor]];
        }else {
            self.statusBarStyle = UIStatusBarStyleDefault;
            
            self.backGradientView.gradientStartPoint = CGPointMake(0, 0);
            self.backGradientView.gradientEndPoint = CGPointMake(0.12, 1);
            self.backGradientView.gradientLocations = @[@0, @0.4, @1];
            self.backGradientView.gradientLayerColors = @[(__bridge id)[UIColorHex(#BEF7FC) CGColor],
                                                          (__bridge id)[UIColorHex(#EAFDFF) CGColor],
                                                          (__bridge id)[UIColorHex(#FFFFFF) CGColor]];
        }
    });
    
    
    [self.mrkContentView addSubview:self.navBar];
    [self.navBar mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.right.mas_equalTo(0);
        make.height.mas_equalTo(kNavBarHeight);
    }];
    
    [self.mrkContentView addSubview:self.pageTableView];
    [self.pageTableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(kNavBarHeight);
        make.left.right.bottom.mas_equalTo(0);
    }];
    
    
    MrkEmptyView *emptyView = [MrkEmptyView emptyViewWithImage:[UIImage imageNamed:@"icon_nonetwork_holder"]
                                                      titleStr:@""
                                                     detailStr:@"网络异常"
                                                   btnTitleStr:@"刷新重试"
                                                 btnClickBlock:^{
        [self_weak_.pageModel refreshDeviceData];
    }];
    self.view.pageEmptyView = emptyView;
    
    [self.view beginLoading];
    [self requestPageData];
    
    /// app启动—补偿结算
    [[NSNotificationCenter defaultCenter] postNotificationName:@"RepairTrainingData" object:nil];
}

/// 首页mia入口
- (void)tapGestureRecognized:(id)tapGestureRecognizer {
    [MRKAIPlanLogic.shared jumpToAIChat];
}

/// 请求数据
- (void)requestPageData {
    @weakify(self);
    [self.pageModel.updateDetailSignal subscribeNext:^(id x) {
        @strongify(self);
        [self.view endLoading];
        [self.view hiddenEmptyView:YES];
        [self.pageTableView endRefresh];
        
        [self.navBar refreshPageData];
        [self.pageTableView refreshPageData];
        
        // 第一次数据请求
        if (!self.isRefresh) {
            self.isRefresh = YES;
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [self checkUserGuide];
            });
        }
    }];
    
    [self.pageModel.requestErrorSignal subscribeNext:^(id x) {
        @strongify(self);
        [self.view endLoading];
        [self.pageTableView endRefresh];
        
        BOOL noData = [self.pageModel pageNoData];
        [self.view hiddenEmptyView:!noData];
    }];
    
    ///第一次请求数据，要求先刷设备列表，再刷页面数据
    [self.pageModel refreshDeviceData];
}

///检查是否弹新手引导
- (void)checkUserGuide {
    if (![MRKVersionCheck shareManager].isDisplay) {
        
        ///新手3天会员
        if (![MRKMainGuideManager showGuide]) {
            [self requestAlertView];
        }
        
        ///引导
        [self homeMiaGuideView];
    }
}

- (void)requestAlertView{
    ///推荐页面弹窗
    [[MRKHomeAlertManager shareManager] addTarget:self requestAlertData:MRKRecommendPopupCode];
    
    ///VIP会员当日到期
    if ([self vipTimeJudge:self.pageModel.vipInfomodel]){
        [self vipAlertController:1];
    }
    
    ///VIP会员过期一天
    if ([self vipExpireJudge:self.pageModel.vipInfomodel]){
        [self vipAlertController:2];
    }
}

///type = 1 VIP会员当日到期/type = 2 VIP会员过期一天
- (void)vipAlertController:(NSInteger) type{
    ReportMrkLogParms(1, type == 1 ? @"会员到期当日提醒弹窗" : @"会员过期1日提醒弹窗", @"page_new", @"btn_home_popup_id_listing", nil, 0, @{@"type":@(type)});
    dispatch_async(dispatch_get_main_queue(), ^{
        NSString *imageName = type == 1 ? @"user_vip_expire" : @"user_vip_expire1";
        MRKAlertController *pageVC = [MRKAlertController controllerWithImage:[UIImage imageNamed:imageName]];
        pageVC.view.alpha = 0;
        pageVC.alertRouterHandle = ^{
            [[RouteManager sharedInstance] skipVIP];
            ReportMrkLogParms(2, @"会员到期当日提醒弹窗去续费", @"page_new", @"btn_home_popup_id_click", nil, 0, @{@"type":@(type)});
        };
        pageVC.alertDismissHandle = ^{
            ReportMrkLogParms(2, @"会员到期当日提醒弹窗关闭", @"page_new", @"btn_home_popup_id_clickclose", nil, 0, @{@"type":@(type)});
        };
        [[MRKPopupManager sharedInstance] showAlertView:pageVC level:MRKPopupViewLevelDefault callback:^{
            [UIView animateWithDuration:0.3  animations:^{
                pageVC.view.alpha = 1;
            }];
        }];
    });
}


- (BOOL)vipTimeJudge:(MemberInfoDataDTO *)model{
    if (model.isAutoRenewal) {
        return NO;
    }
    
    NSString *nowTime = [MRKTimeManager getNowTimeDate];
    NSString *timeskey = [NSString stringWithFormat:@"VipExpireTime_%@", UserInfo.userId];
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    NSString *userDefaultsTime = [userDefaults valueForKey:timeskey];
    if ([nowTime isEqualToString:userDefaultsTime]){
        return NO;
    }
    BOOL canAlert = model.manualPurchase || model.isOpened;///触达人群:手动续费用户和兑换用户
    BOOL todayVip = model.isMember && model.days == 0;///触达人群:手动续费用户和兑换用户
    ///
    NSString *key = [NSString stringWithFormat:@"VipExpireTip_%@", UserInfo.userId];
    BOOL RegulateTip = [userDefaults boolForKey:key];
    if (!RegulateTip && todayVip && canAlert){
        [userDefaults setBool:YES forKey:key];
        [userDefaults setObject:nowTime forKey:timeskey];
        [userDefaults synchronize];
        
        return YES;
    }else{
        [userDefaults removeObjectForKey:key];
        [userDefaults removeObjectForKey:timeskey];
        [userDefaults synchronize];
        
        return NO;
    }
}

- (BOOL)vipExpireJudge:(MemberInfoDataDTO *)model{
    if (model.isAutoRenewal) {
        return NO;
    }
    
    NSString *nowTime = [MRKTimeManager getNowTimeDate];
    NSString *timeskey = [NSString stringWithFormat:@"VipExpireTime1_%@", UserInfo.userId];
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    NSString *userDefaultsTime = [userDefaults valueForKey:timeskey];
    if ([nowTime isEqualToString:userDefaultsTime]){
        return NO;
    }
    
    BOOL canAlert = model.manualPurchase || model.isOpened;///触达人群:手动续费用户和兑换用户
    BOOL noVip = !model.isMember && model.expireDays == -1;///
    ///
    NSString *key = [NSString stringWithFormat:@"VipExpireTip1_%@", UserInfo.userId];
    BOOL RegulateTip = [userDefaults boolForKey:key];
    if (!RegulateTip && noVip && canAlert){
        [userDefaults setBool:YES forKey:key];
        [userDefaults setObject:nowTime forKey:timeskey];
        [userDefaults synchronize];
        
        return YES;
    }else{
        [userDefaults removeObjectForKey:key];
        [userDefaults removeObjectForKey:timeskey];
        [userDefaults synchronize];
        
        return NO;
    }
}


#pragma mark - lazy
- (MRKMainNavigationBar *)navBar {
    if (!_navBar) {
        _navBar = [[MRKMainNavigationBar alloc] init];
        _navBar.backgroundColor = UIColor.clearColor;
        @weakify(self);
        _navBar.deviceDetailBlock = ^(MRKDeviceModel * _Nonnull model) {
            @strongify(self);
            ///打开设备详情
            MRKDeviceDetailController *vc = [[MRKDeviceDetailController alloc] init];
            vc.model = model;
            [self.navigationController pushViewController:vc animated:YES];
        };
        _navBar.switchDeviceBlock = ^{
            @strongify(self);
            MRKDeviceListManager *manager = [MRKDeviceListManager shareManager];
            int count = [manager trainingDeviceCount];
            if (count <= 1) {
                ///新用户链路拦截处理一下[默认是hasBind数据]
                [[MRKLinkRouterManager sharedInstance] newUserBuildComplete:^(BOOL hasBind) {
                    @strongify(self);
                    if (hasBind){
                        DeviceSearchViewController *vc = [DeviceSearchViewController new];
                        [self.navigationController pushViewController:vc animated:YES];
                    }
                }];
            } else {
                ///设备切换
                MRKSwitchDeviceController *vc = [[MRKSwitchDeviceController alloc] init];
                [self.navigationController pushViewController:vc animated:YES];
            }
        };
    }
    return _navBar;
}

- (MainPageTableView *)pageTableView {
    if (!_pageTableView) {
        _pageTableView = [[MainPageTableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
        _pageTableView.pageModel = self.pageModel;
        _pageTableView.MDelegate = self;
        @weakify(self);
        _pageTableView.pageRefreshBlock = ^{
            @strongify(self);
            [self.pageModel refreshHeaderData];
        };
        _pageTableView.tableViewDidScroll = ^(CGFloat contentOffsetY) {
         
        };
    }
    return _pageTableView;
}

- (MrkGradientView *)backGradientView {
    if (!_backGradientView) {
        _backGradientView = [[MrkGradientView alloc] init];
        _backGradientView.drawsGradientBackground = YES;
        _backGradientView.gradientStartPoint = CGPointMake(0, 0);
        _backGradientView.gradientEndPoint = CGPointMake(0.12, 1);
        _backGradientView.gradientLocations = @[@0, @0.4, @1];
    }
    return _backGradientView;
}

// 首页miay引导
- (void)homeMiaGuideView{
    // 判断展示的不是当前页面
    if (![[UIViewController currentViewController] isKindOfClass:[MRKMainPageController class]]) {
        return;
    }
    
    if ([MRKMainGuideManager showGuide]) {
        [MRKMainGuideManager record];
        
        NSMutableArray *array = [NSMutableArray array];
        UIView *trainView = [self.pageTableView guideTrainView];
        if (trainView) {
            [array addObject:({
                MRKGuideModel *model = [[MRKGuideModel alloc] init];
                model.targetView = trainView;
                model.titleText = @"自由训练移到这, 随心开练";
                model.buttonTitleText = @"下一步";
                model.contentSize = CGSizeMake(262, 104);
                model;
            })];
        }
        
        UIView *weightView = [self.pageTableView guideWeightView];
        if (weightView) {
            [array addObject:({
                MRKGuideModel *model = [[MRKGuideModel alloc] init];
                model.targetView = weightView;
                model.titleText = @"体重管理, 从这里开始";
                model.buttonTitleText = @"下一步";
                model.contentSize = CGSizeMake(230, 104);
                model;
            })];
        }
        
        UIView *aiPlanEnterView = [self.pageTableView guideAIPlanEnterView];
        if (aiPlanEnterView) {
            [array addObject:({
                MRKGuideModel *model = [[MRKGuideModel alloc] init];
                model.targetView = aiPlanEnterView;
                model.titleText = @"开始你的AI智能运动饮食计划";
                model.buttonTitleText = @"知道了";
                model.contentSize = CGSizeMake(280, 104);
                model;
            })];
        }
        
        if (array.count == 0) {
            return;
        }
        
        MRKHomeGuideViewController *pageVC = [[MRKHomeGuideViewController alloc] init];
        pageVC.guideViews = array;
        pageVC.view.alpha = 0;
        @weakify(self);
        pageVC.dismissBlock = ^{
            self_weak_.miaGuideView = nil;
            [self_weak_ requestAlertView];
        };
        self.miaGuideView = pageVC;
        [[MRKPopupManager sharedInstance] showAlertView:pageVC level:MRKPopupViewLevelDefault callback:^{
            [UIView animateWithDuration:0.3  animations:^{
                pageVC.view.alpha = 1;
            }];
        }];
    }
}




#pragma mark - MainPageTableViewDelegate

///**
// 调整目标
// */
//- (void)editTrainTarget {
//    MRKAdjustTargetController *vc = [[MRKAdjustTargetController alloc] init];
//    vc.targerModel = self.pageModel.targerModel;
//    [self.navigationController pushViewController:vc animated:YES];
//}

/// 点击金刚区
/// - Parameter model
- (void)kingKongAdvertModel:(AdvertModel *)model {
    [[NSNotificationCenter defaultCenter] postNotificationName:AdvertSkipToPageNotification
                                                        object:@{@"model":model,@"position":model.bannerType?:@""}];
}

///新用户vip
- (void)newUserVipCharge {
    [[RouteManager sharedInstance] skipWeb: MRKAppH5LinkCombine(MRKNewUserAction) hiddenNav:YES];
    ReportMrkLogParms(2, @"新人活动专区入口", @"page_new", @"btn_home_xractivity_click", nil, 0, nil);
}

/**
 体重隐藏显示
 */
//- (void)weigtDataSecret:(UIButton *)sender{
//    NSString *key = [NSString stringWithFormat:@"TrainingRegulateSecret_%@", UserInfo.userId];
//    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
//    BOOL RegulateSecret = [userDefaults boolForKey:key];
//    [userDefaults setBool:!RegulateSecret forKey:key];
//    [userDefaults synchronize];
//    [self.pageTableView reloadSection:MRKRecordWeightRow withRowAnimation:UITableViewRowAnimationNone];
//}

///**
// 记录体重
// */
//- (void)recordWeigtData{
//    
//    [[RouteManager sharedInstance] requestFatScaleInfo:^(MRKDeviceModel * _Nonnull model) {
//        [[RouteManager sharedInstance] jumpToWeightCenterVC:model];
//    } failure:^{
//        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
//            [[NSNotificationCenter defaultCenter] postNotificationName:@"SetTabBarItemIndex" object:@2];
//        });
//    }];
//}

/**
 课程详情
 @Param courseId 课程id
 */
- (void)courseDetail:(NSString *)courseId{
    [[RouteManager sharedInstance] jumpToCourseDetailWithId:courseId];
}

/**
 主题课包
 @Param model 主题di
 */
- (void)themeCourseList:(MRKThemeListModel *)model{
    NSDictionary *dic = @{
        @"themeId"   : model.cid?:@"",
        @"name"      : model.name?:@"",
        @"introduce" : model.introduce?:@"",
        @"cover"     : model.cover?:@""
    };
    NSString *body = dic.dicForUrlArgument;
    NSString *Url = [NSString stringWithFormat:@"%@?%@", MRKHomePageRecommendListSubject, body];
    NSString *link = MRKAppH5LinkCombine(Url);
    [[RouteManager sharedInstance] skipWeb:link hiddenNav:YES];
}

///更多主题课包
- (void)themeCourseListMore {
    NSString *Url = [NSString stringWithFormat:@"%@?equipmentId=%@", MRKHomePageRecommendSubject, self.pageModel.equimentTypeId?:@""];
    [[RouteManager sharedInstance] skipWeb: MRKAppH5LinkCombine(Url) hiddenNav:YES];
    ReportMrkLogParms(2, @"用户点击甄选课程入口", @"new_home", @"btn_home_zxclass_click", nil, 0, nil);
}

- (void)selectCourseCondition:(MRKCourseConditionModel*)model Tag:(MRKConditionTagModel *)tag{
    MRKAllCourseViewController *vc = [[MRKAllCourseViewController alloc] init];
    vc.conditionModel = model;
    vc.tagModel = tag;
    [self.navigationController pushViewController:vc animated:YES];
}

///设置运动目标
- (void)setTrainingTarget{
    MRKAdjustTargetController *vc = [[MRKAdjustTargetController alloc] init];
    vc.alertTrainTarget = YES;
    [self.navigationController pushViewController:vc animated:YES];
    ReportMrkLogParms(2, @"用户点击完成甄选课程偏好设定", @"page_setgoals", @"btn_home_zxclass_finish_click", nil, 0, nil);
}

///更多绝影课
- (void)xenjoyCourseMore{
    NSString *Url = [NSString stringWithFormat:@"%@?equipmentId=%@", MRKHomePageXenjoyCourseMore, self.pageModel.equimentTypeId?:@""];
    [[RouteManager sharedInstance] skipWeb: MRKAppH5LinkCombine(Url) hiddenNav:YES];
}

///王牌课
- (void)kingCourseMore:(NSString *)i pageId:(NSString *)pageId {
    ///详情
    //http://testconsole.merach.com/webview/trump-course/index.html?themeId=1&id=39&equipmentId=2
    NSString *url;
    if ([i isNotBlank]) {
        ///进入课包详情
        NSString *Url = [NSString stringWithFormat:@"%@?themeId=%@&id=%@&equipmentId=%@", MRKKingCourseUrl, pageId, i, self.pageModel.equimentTypeId?:@""];
        url = MRKAppH5LinkCombine(Url);
    }else {
        ///进入课包列表
        NSString *Url = [NSString stringWithFormat:@"%@?themeId=%@&equipmentId=%@", MRKHomePageTrumpCourse, pageId, self.pageModel.equimentTypeId?:@""];
        url = MRKAppH5LinkCombine(Url);
    }
    [[RouteManager sharedInstance] skipWeb:url hiddenNav:YES];
}

/**
 banner
 */
- (void)bannerAdvertModel:(AdvertModel *)model{
    [[NSNotificationCenter defaultCenter] postNotificationName:AdvertSkipToPageNotification
                                                        object:@{@"model":model,
                                                                 @"position":model.bannerType}];
}

/**
 人气好课周榜更多
 */
- (void)topCoursesRankMore{
    NSString *Url = [NSString stringWithFormat:@"%@?equipmentId=%@", MRKHomePagePopularityRank, self.pageModel.equimentTypeId?:@""];
    [[RouteManager sharedInstance] skipWeb:MRKAppH5LinkCombine(Url) hiddenNav:YES];
}

/**
 更多实景视频
 */
- (void)realLiveCourseMore{
    MRKCategoryCourseController *courseVC  = [[MRKCategoryCourseController alloc] init];
    courseVC.type = MRKCategoryCourseTypeLive;
    courseVC.productID = self.pageModel.equimentTypeId;
    [self.navigationController pushViewController:courseVC animated:YES];
}

/// 实景视频
/// - Parameter model: 实景
- (void)realLiveCourseClick:(LiveModel *)model{
    [[RouteManager sharedInstance] realVideoOpenModel:model];
}

///预约直播
- (void)makeAnappointmentLiveCourse:(MRKCourseModel *)model{
    if (model.courseStatus.intValue == 30) {
        @weakify(self);
        [MRKRequestData makeButtonClick:model scene:@"home" atController:self succeed:^{
            @strongify(self);
            [self.pageTableView.pageModel requestLiveCoursesData:^{
                @strongify(self);
                dispatch_async(dispatch_get_main_queue(), ^{
                    [self.pageTableView reloadData];
                });
            }];
        }];
        return;
    }
    
    ///课程详情
    [[RouteManager sharedInstance] jumpToCourseDetailWithModel:model];
}

/// 点击直播课
- (void)clickLiveCourse:(MRKCourseModel *)model{
    [[RouteManager sharedInstance] jumpToCourseDetailWithModel:model];
}

///更多直播视频
- (void)liveCourseMore{
    /// 直播课全部课程
    AllLivingCourseController *vc = [[AllLivingCourseController alloc]init];
    [self.navigationController pushViewController:vc animated:YES];
}

/**
 点击推荐计划
 */
- (void)clickRecommendPlan:(MRKCoursePlanModel *)model{
    MRKCoursePlanController *vc = [[MRKCoursePlanController alloc] init];
    vc.model = model;
    [self.navigationController pushViewController:vc animated:YES];
}
/**
 点击推荐计划更多
 */
- (void)clickRecommendPlanMore{
    MRKCategoryCourseController *vc = [[MRKCategoryCourseController alloc] init];
    vc.type = MRKCategoryCourseTypePlan;
    [self.navigationController pushViewController:vc animated:YES];
}

/*
 #pragma mark - Navigation
 
 // In a storyboard-based application, you will often want to do a little preparation before navigation
 - (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
 // Get the new view controller using [segue destinationViewController].
 // Pass the selected object to the new view controller.
 }
 */



#pragma mark - mia 相关
- (void)mrkNavigationController:(UINavigationController *)navigationController willShowViewController:(UIViewController *)viewController animated:(BOOL)animated {
    BOOL isRoot = (navigationController.viewControllers.count == 1);
    self.miaView.hidden = !isRoot;
}

- (void)initAiVoiceController {
    _aiVoiceController = [[MRKFlutterAiVoiceController alloc] init];
    _aiVoiceController.modalPresentationStyle = UIModalPresentationOverCurrentContext;
    @weakify(self);
    _aiVoiceController.voiceText = ^(NSString * text) {
        [self_weak_ removeAiVoiceViewController:^{
            if (text.isNotBlank) {
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    [MRKAIPlanLogic.shared jumpToAIChatWithVoiceText:text];
                });
            }
        }];
    };
}

- (MRKMiaIconView *)miaView {
    if (!_miaView) {
        @weakify(self);
        _miaView = [[MRKMiaIconView alloc] initWithFrame:CGRectMake(0, 0, 52, 52)];
        _miaView.tapAction = ^{
            [MRKAIPlanLogic.shared jumpToAIChat];
        };
        _miaView.longPressBegan = ^{
            [[HapticManager shared] trigger:HapticTypeImpactMedium];
            [self_weak_ userMiaVoice];
        };
        _miaView.moveAction = ^(BOOL up) {
            [[HapticManager shared] trigger:HapticTypeImpactMedium];
            [self_weak_.aiVoiceController moveVoiceWithUp:up];
        };
        _miaView.longPressSend = ^{
            [self_weak_.aiVoiceController getVoiceToText];
        };
        _miaView.longPressCancelled = ^{
            [self_weak_.aiVoiceController cancelVoice];
            [self_weak_ removeAiVoiceViewController:nil];
        };
    }
    return _miaView;
}

// 获取麦克风权限
- (void)userMiaVoice {
    ReportMrkLogParms(2, @"长按 Mia 图标", @"page_home", @"long_press_mia_icon", nil, 0, nil);
    
    [[SystemPermissionManager shared] requestMicrophonePermissionWithUndeterminedControl:false onGranted:^{
        NSLog(@"onGranted");
        [self addAiVoiceViewController];
    } onDenied:^{
    
    }];
}

// 添加mia
- (void)addMiaview {
    [self.view addSubview:self.miaView];
    [self.miaView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_equalTo(-16);
        make.bottom.mas_equalTo(-14);
        make.size.mas_equalTo(CGSizeMake(52, 52));
    }];
}

//弹出mia语音获取页面
- (void)addAiVoiceViewController {
    NSLog(@"addAiVoiceViewController");
    if (self.isPresenting) return; // 防止重复弹出
    self.isPresenting = YES;
    [self initAiVoiceController];
    NSLog(@"initAiVoiceController");
    @weakify(self);
    [kAppDelegate.window.rootViewController presentViewController:self.aiVoiceController animated:NO completion:^{
        @strongify(self);
        [self.aiVoiceController.view addSubview:self.miaView];
        self.isPresenting = NO;
        if (self.needDismiss) { // 处理松手时未完成的关闭请求
            self.needDismiss = NO;
            [self removeAiVoiceViewController:nil];
        }
    }];
}

// 关闭mia语音获取页面
- (void)removeAiVoiceViewController:(void (^ __nullable)(void))completion  {
    NSLog(@"removeAiVoiceViewController");
    if (!self.isPresenting) {
        [self addMiaview];
        @weakify(self);
        [self.aiVoiceController dismissViewControllerAnimated:YES completion:^{
            !completion?:completion();
            [self_weak_.aiVoiceController cancelVoice];
            self_weak_.aiVoiceController = nil;
        }];
    } else {
        self.needDismiss = YES; // 若正在弹出，等待完成后关闭
    }
}


@end
