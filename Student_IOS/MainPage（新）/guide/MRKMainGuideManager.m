//
//  MRKMainGuideManager.m
//  Student_IOS
//
//  Created by merit on 2025/5/17.
//

#import "MRKMainGuideManager.h"
#import "ZWMGuideView.h"


#define MineGuide_Local_55 @"MineGuide_Local_5.5.0.0"

@interface MRKMainGuideManager()<ZWMGuideViewDataSource, ZWMGuideViewLayoutDelegate>
@property (nonatomic, strong) ZWMGuideView *guideView;
@property (nonatomic, assign) CGRect guideFrame;
@property (nonatomic, strong) MRKMainGuideCustomView *customView;
@end

@implementation MRKMainGuideManager

- (void)dealloc {
    NSLog(@"\n😊😊😊-----dealloc-----😊😊😊%@\n",[NSString stringWithUTF8String:object_getClassName(self)]);
}

- (instancetype)init{
    if (self = [super init]) {
        
    }
    return self;
}

- (void)showGuide:(UIView *)view {
    _guideFrame = CGRectMake(0, 0, <PERSON>Width, MainHeight);
    [self.guideView show];
}

- (void)dismiss {
    [self.guideView hide];
}

- (ZWMGuideView *)guideView {
    if (_guideView == nil) {
        _guideView = [[ZWMGuideView alloc] initWithFrame:self.guideFrame];
        _guideView.dataSource = self;
        _guideView.delegate = self;
        _guideView.type = ZWMGuideMaskItemTypeCustom;
    }
    return _guideView;
}

- (MRKMainGuideCustomView *)customView {
    if (!_customView) {
        _customView = [[MRKMainGuideCustomView alloc] init];
    }
    return _customView;
}

#pragma mark -- ZWMGuideViewDataSource（必须实现的数据源方法）
- (NSInteger)numberOfItemsInGuideMaskView:(ZWMGuideView *)guideMaskView{
    return self.guideViews.count;
}

- (UIView *)guideMaskView:(ZWMGuideView *)guideMaskView viewForItemAtIndex:(NSInteger)index{
    MRKGuideModel *model = self.guideViews[index];
    return model.targetView;
}

- (void)guideMaskView:(ZWMGuideView *)guideMaskView currentIndex:(NSInteger)index {
    MRKGuideModel *model = self.guideViews[index];
    
    NSString *buttonTitleText = model.buttonTitleText;
    if (index == self.guideViews.count - 1){
        buttonTitleText = @"知道了";
    }
    [self.customView setText:model.titleText buttonTitle:buttonTitleText];
}

#pragma mark -- ZWMGuideViewLayoutDelegate
- (CGFloat)guideMaskView:(ZWMGuideView *)guideMaskView cornerRadiusForItemAtIndex:(NSInteger)index {
    return index < 2 ? 14 : 16;
}
- (UIEdgeInsets)guideMaskView:(ZWMGuideView *)guideMaskView insetsForItemAtIndex:(NSInteger)index{
    return UIEdgeInsetsMake(0, 0, 0, 0);
}

- (CGFloat)guideMaskView:(ZWMGuideView *)guideMaskView bottomForItemAtIndex:(NSInteger)index {
    return 0;
}

- (CGFloat)guideMaskView:(ZWMGuideView *)guideMaskView spaceForSubviewsAtIndex:(NSInteger)index {
    return 0;
}

- (CGFloat)guideMaskView:(ZWMGuideView *)guideMaskView horizontalSpaceForDescriptionLabelAtIndex:(NSInteger)index {
    return 16;
}

- (UIView *)guideMaskView:(ZWMGuideView *)guideMaskView guideViewForIndex:(NSInteger)index {
    MRKGuideModel *model = self.guideViews[index];
    self.customView.mj_size = model.contentSize;
    return self.customView;
}

- (void)guideMaskViewHidden:(ZWMGuideView *)guideMaskView {
    !_completionBlock?:_completionBlock();
}


#pragma mark - 记录是否展示过
// 记录引导展示过
+ (void)record {
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    [userDefaults setBool:YES forKey:MineGuide_Local_55];
    [userDefaults synchronize];
}

// 产看是否展示过引导
+ (BOOL)showGuide {
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    BOOL guide = [userDefaults boolForKey:MineGuide_Local_55];
    return !guide;
}
// 清空记录
+ (void)reset {
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    [userDefaults removeObjectForKey:MineGuide_Local_55];
    [userDefaults synchronize];
}
@end







@interface MRKMainGuideCustomView()
@property (nonatomic, strong) UIButton *button;
@property (nonatomic, strong) UIView *contentView;
@property (nonatomic, strong) UIImageView *iconImageView;
@property (nonatomic, strong) UILabel *textLabel;
@end

@implementation MRKMainGuideCustomView


- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        [self addSubview:self.contentView];
        [self.contentView addSubview:self.iconImageView];
        [self.contentView addSubview:self.textLabel];
        
        [self addSubview:self.button];
    }
    return self;
}

- (void)layoutSubviews {
    [super layoutSubviews];
    
    [self.contentView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.mas_top);
        make.left.mas_equalTo(self.mas_left);
        make.right.mas_equalTo(self.mas_right);
        make.height.mas_equalTo(56);
    }];
    [self.iconImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView.mas_left).offset(6);
        make.top.bottom.mas_equalTo(0);
        make.width.mas_equalTo(52);
    }];
    [self.textLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.iconImageView.mas_right).offset(2);
        make.right.equalTo(self.contentView.mas_right).offset(-12);
        make.centerY.equalTo(self.iconImageView.mas_centerY);
    }];
    [self.button mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentView.mas_bottom).offset(16);
        make.right.equalTo(self.contentView.mas_right);
        make.size.mas_equalTo(CGSizeMake(88, 32));
        make.bottom.mas_equalTo(self.mas_bottom);
    }];
}

- (void)setText:(NSString *)text buttonTitle:(NSString *)buttonTitle {
    self.textLabel.text = text;
    [self.button setTitle:buttonTitle forState:UIControlStateNormal];
}

- (UIButton *)button{
    if (!_button) {
        _button = [UIButton buttonWithType:UIButtonTypeCustom];
        [_button setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        _button.titleLabel.font = kMedium_Font_NoDHPX(14);
        [_button setBorderColor:[UIColor whiteColor]];
        [_button setBorderWidth:1];
        _button.cornerRadius = 16;
        _button.enabled = NO;
    }
    return _button;
}

- (UIView *)contentView {
    if (!_contentView) {
        _contentView = [[UIView alloc] init];
        [_contentView az_setGradientBackgroundWithColors:@[
            [UIColor colorWithHexString:@"#17D2E3"],
            [UIColor colorWithHexString:@"#AA69FF"],
            [UIColor colorWithHexString:@"#FF8FB4"]
        ] locations:@[@0, @0.5, @1] startPoint:CGPointMake(0, 0) endPoint:CGPointMake(1, 0)];
        _contentView.cornerRadius = 16;
    }
    return _contentView;
}

- (UIImageView *)iconImageView {
    if (!_iconImageView) {
        _iconImageView = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"icon_mia_emoji1"]];
        _iconImageView.contentMode = UIViewContentModeScaleAspectFit;
    }
    return _iconImageView;
}
- (UILabel *)textLabel {
    if (!_textLabel) {
        _textLabel = [[UILabel alloc] init];
        _textLabel.font = kMedium_Font_NoDHPX(16);
        _textLabel.textColor = UIColor.whiteColor;
        _textLabel.numberOfLines = 0;
    }
    return _textLabel;
}
@end
