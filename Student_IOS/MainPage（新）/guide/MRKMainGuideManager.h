//
//  MRKMainGuideManager.h
//  Student_IOS
//
//  Created by merit on 2025/5/17.
//

#import <Foundation/Foundation.h>
#import "MRKGuideModel.h"
NS_ASSUME_NONNULL_BEGIN

@interface MRKMainGuideManager : NSObject

@property (nonatomic, strong) NSArray <MRKGuideModel *> *guideViews;

@property (nonatomic, weak) UIView *targetView;

@property (nonatomic, copy) void(^completionBlock)(void);

- (void)showGuide:(UIView *)view;

- (void)dismiss;

// 记录引导展示过
+ (void)record;

// 产看是否展示过引导
+ (BOOL)showGuide;

// 清空记录
+ (void)reset;

@end


@interface MRKMainGuideCustomView : UIView
- (void)setText:(NSString *)text buttonTitle:(NSString *)buttonTitle;
@end

NS_ASSUME_NONNULL_END
