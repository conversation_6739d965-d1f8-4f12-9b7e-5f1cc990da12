//
//  MRKNewUserVipCell.m
//  Student_IOS
//
//  Created by Junq on 2024/9/10.
//

#import "MRKNewUserVipCell.h"
#import "MrkGradientView.h"
#import "LOTAnimationView.h"



@interface MRKNewUserVipCell ()
@property (nonatomic, strong) UIView *alphalView;
@property (nonatomic, strong) UIImageView *backGradientView;
@property (nonatomic, strong) LOTAnimationView *receiveImageView;
@end

@implementation MRKNewUserVipCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        ReportMrkLogParms(1, @"新人活动专区", @"page_new", @"btn_home_xractivity_listing", nil, 0, nil);
        self.backgroundColor = [UIColor clearColor];
        self.contentView.backgroundColor = [UIColor clearColor];

        if (!self.alphalView) {
            self.alphalView = [[UIView alloc] init];
            self.alphalView.lee_theme.LeeConfigBackgroundColor(@"main_view_backColor");
            self.alphalView.alpha = 0;
        }
        [self.contentView addSubview:self.alphalView];
        [self.alphalView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.mas_equalTo(UIEdgeInsetsZero);
        }];
        
        ///
        [self.contentView addSubview:self.backGradientView];
        [self.backGradientView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.mas_equalTo(UIEdgeInsetsZero);
            make.height.mas_equalTo(self.backGradientView.mas_width).multipliedBy(47.0/375.0);
        }];
        
        [self.backGradientView addSubview:self.receiveImageView];
        [self.receiveImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(self.backGradientView.mas_right).offset(-WKDHPX(16));
            make.centerY.mas_equalTo(self.backGradientView.mas_centerY).offset(WKDHPX(6));
            make.size.mas_equalTo(CGSizeMake(WKDHPX(80), WKDHPX(80)));
        }];
    }
    return self;
}

- (void)layoutSubviews{
    [super layoutSubviews];
}

- (void)setBackGroundAlpha:(float)backGroundAlpha {
    _backGroundAlpha = backGroundAlpha;
    self.alphalView.alpha = backGroundAlpha;
}

- (void)reloadAnimation{
    if(!self.receiveImageView.isAnimationPlaying) {
        [self.receiveImageView setAnimationNamed:@"new_user_data.json"];
        self.receiveImageView.loopAnimation = YES;
        [self.receiveImageView play];
    }
}

- (UIImageView *)backGradientView {
    if (!_backGradientView) {
        UIImageView *imagev = [[UIImageView alloc] init];
        imagev.backgroundColor = [UIColor clearColor];
        imagev.contentMode = UIViewContentModeScaleAspectFill;
        imagev.image = [UIImage imageNamed:@"new_user_vip_bg"];
        imagev.clipsToBounds = YES;
        _backGradientView = imagev;
    }
    return _backGradientView;
}

- (LOTAnimationView *)receiveImageView {
    if(!_receiveImageView) {
        _receiveImageView = [LOTAnimationView new];
    }
    return _receiveImageView;
}

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}

@end
