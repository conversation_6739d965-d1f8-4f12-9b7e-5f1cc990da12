import UIKit
import SnapKit

/// 主视图点击类型
private enum AIPlanEnterMainTapType {
    /// 去定制计划
    case customizePlan
    /// 开启计划
    case startPlan
    /// 查看报告
    case viewReport
    /// 查看计划
    case viewPlan
    /// 查看周报
    case viewWeeklyReport
}

@objcMembers
public class AIPlanEnterView: UIView {
    private var tapType: AIPlanEnterMainTapType?
    
    /// 支持背景贴图
    public var backgroundImage: UIImage? {
        didSet {
            bgImageView.image = backgroundImage
        }
    }
    
    /// AI概览模型
    public var overViewModel: MRKPlanOverViewModel? {
        didSet {
            ///刷新视图 [主线程刷新]
            updateViewStatus()
        }
    }
    
   /**
    刷新主视图状态，根据当前计划模型的不同状态，动态调整各子视图的显示、内容和布局。
    1. 若无计划，显示去定制按钮，隐藏其它内容。
    2. 若有计划，根据计划状态（未开始、进行中、请假、完成）分别处理：
    - 未开始：显示计划标题、描述，隐藏进度和各功能item。
    - 进行中：根据是否到训练时间、周期是否结束、训练日/休息日等细分，动态显示/隐藏各item和进度。
    - 请假：显示请假信息，隐藏其它item。
    - 完成：显示完成信息，隐藏其它item。
    */
    func updateViewStatus() {
        ///< 先全部隐藏，避免状态残留
        creatImageView.hidden(true)
        actionImageView.hidden(true)
        titleView.hidden(true)
        daysLabel.hidden(true)
        sportItemView.hidden(true)
        foodItemView.hidden(true)
        leaveItemView.hidden(true)
        tapType = nil
        
        ///< 1. 无计划，显示去定制, 并且不在进行中
        guard let viewModel = overViewModel,
              let plan = viewModel.currentPlan,
              !viewModel.isProcess
        else {
            showNoPlanStatus()
            return
        }
        
        
        ///< 2. 有计划，根据状态分支处理
        ///< 计划状态 训练状态：1-未开始；2-进行中；3-请假中；4-已完成；5-中途结束；6-未完成；99-废弃
        switch plan.status {
        case 1:
            ///< 计划未开启
            showNotStartedStatus(plan)
        case 2:
            ///< 计划进行中
            showInProgressStatus(plan)
        case 3:
            ///< 请假中
            ///< 请假过程中，如果currentTrainingDay非null，依然展示正常任务
            if let _ = plan.currentTrainingDay {
                showInProgressStatus(plan)
            } else {
                showLeaveStatus(plan)
            }
        case 4, 5, 6:
            ///< 计划已完成
            showFinishedStatus(plan)
        default:
            ///< 其它未知状态，显示去定制
            showNoPlanStatus()
        }
    }
    

    // MARK: - 状态分支方法
    private func showNoPlanStatus() {
        self.snp.updateConstraints { make in
            make.height.equalTo(WKDHPX_S(80))
        }
        
        let image = UIImage(named: "aiPlan_enter_bg14")
        backgroundImage = image?.stretchableImage(left: 16, right: 16)
        
        tapType = .customizePlan
        creatImageView.hidden(false)

        actionImageView.hidden(false)
        actionImageView.image = UIImage(named: "aiplan_go_customize_icon")
    }

    private func showNotStartedStatus(_ plan: MRKCurrentAIPlanModel) {
        self.snp.updateConstraints { make in
            make.height.equalTo(WKDHPX_S(80))
        }
        
        let image = UserInfo.isXEnjoy ? UIImage(named: "aiPlan_enter_bg14") : UIImage(named: "aiPlan_enter_bg13")
        backgroundImage = image?.stretchableImage(left: 16, right: 16)
        
        tapType = .startPlan
        actionImageView.hidden(false)
        actionImageView.image = UIImage(named: "aiplan_start_planning_icon")
        
        titleView.hidden(false)
        titleView.snp.remakeConstraints { make in
            make.centerY.equalToSuperview()
            make.left.equalToSuperview().offset(WKDHPX_S(12))
            make.right.lessThanOrEqualTo(actionImageView.snp.left).offset(-WKDHPX_S(12))
        }
        titleView.titleLabel.text = plan.title
        titleView.descripLabel.text = "吃练结合 智能健身 科学有效"
        titleView.isXEnjoyMode = UserInfo.isXEnjoy
    }

    private func showInProgressStatus(_ plan: MRKCurrentAIPlanModel) {
        ///< 未到训练时间 [展示查看计划]
        if !plan.isStart {
            self.snp.updateConstraints { make in
                make.height.equalTo(WKDHPX_S(80))
            }
            
            let image = UserInfo.isXEnjoy ? UIImage(named: "aiPlan_enter_bg14") : UIImage(named: "aiPlan_enter_bg13")
            backgroundImage = image?.stretchableImage(left: 16, right: 16)
            
            tapType = .viewPlan
            actionImageView.hidden(false)
            actionImageView.image = UIImage(named: "aiplan_view_plan_icon")
            
            titleView.hidden(false)
            titleView.snp.remakeConstraints { make in
                make.centerY.equalToSuperview()
                make.left.equalToSuperview().offset(WKDHPX_S(12))
                make.right.lessThanOrEqualTo(actionImageView.snp.left).offset(-WKDHPX_S(12))
            }
            titleView.titleLabel.text = plan.title
            titleView.descripLabel.text = "计划明天开始，记得来训练哦"
            titleView.isXEnjoyMode = UserInfo.isXEnjoy
            return
        }
        
        ///< 当前周期已结束 [展示查看周报]
        if plan.currentPeriod?.status == 2 {
            self.snp.updateConstraints { make in
                make.height.equalTo(WKDHPX_S(80))
            }
            
            let image = UserInfo.isXEnjoy ? UIImage(named: "aiPlan_enter_bg14") : UIImage(named: "aiPlan_enter_bg13")
            backgroundImage = image?.stretchableImage(left: 16, right: 16)
            
            tapType = .viewWeeklyReport
            actionImageView.hidden(false)
            actionImageView.image = UIImage(named: "aiplan_view_week_report_icon")
            
            titleView.hidden(false)
            titleView.snp.remakeConstraints { make in
                make.centerY.equalToSuperview()
                make.left.equalToSuperview().offset(WKDHPX_S(12))
                make.right.lessThanOrEqualTo(actionImageView.snp.left).offset(-WKDHPX_S(12))
            }
            titleView.titleLabel.text = plan.title
            titleView.descripLabel.text = "🎉 上个运动周计划已完成！"
            titleView.isXEnjoyMode = UserInfo.isXEnjoy
            return
        }
        
        ///
        self.snp.updateConstraints { make in
            make.height.equalTo(WKDHPX_S(144))
        }
        
        
        let image =  UserInfo.isXEnjoy ?  UIImage(named: "aiPlan_enter_bg10") : UIImage(named: "aiPlan_enter_bg9")
        backgroundImage = image?.stretchableImage(left: 16, right: 110)
        
        titleView.titleLabel.text = plan.title
        titleView.hidden(false)
        titleView.snp.remakeConstraints { make in
            make.top.equalToSuperview().offset(WKDHPX_S(12))
            make.left.equalToSuperview().offset(WKDHPX_S(12))
            make.right.equalToSuperview().offset(-WKDHPX_S(120))
        }
        titleView.isXEnjoyMode = UserInfo.isXEnjoy
        
        daysLabel.hidden(false)
        daysLabel.text = "第\(plan.progressDays)天"
        daysLabel.textColor =  UserInfo.isXEnjoy ? (UIColor(hexString: "#363A44") ?? .white ).withAlphaComponent(0.9)  : UIColor.color(hexString: "#0D7E88")
        
        ///< 训练日/休息日
        guard let trainingDay = plan.currentTrainingDay else { return }
        if trainingDay.trainingDayType == 2 {
            ///休息日

            ///判断多少天未执行
            var suffixText = ""
            if (plan.consecutiveMissedDays > 0) {
                suffixText = "已经\(plan.consecutiveMissedDays)天没执行啦，要加油哦"
            } else {
                suffixText = "运动休息日，控制饮食摄入"
            }
            let attrString = NSMutableAttributedString(string: suffixText)
            attrString.color = UserInfo.isXEnjoy ? UIColor.white.withAlphaComponent(0.6) : (UIColor(hexString: "#1E1E20") ?? .white ).withAlphaComponent(0.7)
            titleView.descripLabel.attributedText = attrString
          
            if plan.version == 1 {
                
                ///计划1.0版本
                foodItemView.hidden(false)
                foodItemView.sizeStyle = .big
                foodItemView.titleLabel.text = "今日饮食"
                foodItemView.descripLabel.text = "健康从记录每一餐开始"
                foodItemView.actionImageView.image = UIImage(named: "aiplan_diet_record_icon")
                foodItemView.snp.remakeConstraints { make in
                    make.left.equalToSuperview().offset(WKDHPX_S(12))
                    make.right.equalToSuperview().offset(-WKDHPX_S(12))
                    make.bottom.equalToSuperview().offset(-WKDHPX_S(12))
                    make.height.equalTo(WKDHPX_S(56))
                }
                
            } else {
                
                foodItemView.hidden(false)
                foodItemView.sizeStyle = .big
                foodItemView.titleLabel.text = "今日饮食"
                if trainingDay.finishedDietTaskCount == trainingDay.dietTaskCount {
                    foodItemView.descripLabel.text = "今日任务已完成"
                } else {
                    let days = trainingDay.dietTaskCount - trainingDay.finishedDietTaskCount
                    
                    let prefixText = "还需记录 "
                    let daysText = "\(days)"
                    let suffixText = " 次"
                    let fullText = prefixText + daysText + suffixText
                    let attrString = NSMutableAttributedString(string: fullText)
                    // 普通部分颜色
                    attrString.addAttribute(.foregroundColor, value: UIColor(hexString: "#848A9B") ?? .lightGray, range: NSRange(location: 0, length: prefixText.count))
                    attrString.addAttribute(.foregroundColor, value: UIColor(hexString: "#848A9B") ?? .lightGray, range: NSRange(location: prefixText.count + daysText.count, length: suffixText.count))
                    // days部分颜色和字体
                    attrString.addAttribute(.foregroundColor, value: UIColor(hexString: "#26A15F") ?? .systemTeal, range: NSRange(location: prefixText.count, length: daysText.count))
                    attrString.addAttribute(.font, value: UIFont.systemFont(ofSize: WKDHPX_S(14), weight: .semibold), range: NSRange(location: prefixText.count, length: daysText.count))
                    foodItemView.descripLabel.attributedText = attrString
                }
                foodItemView.actionImageView.image = UIImage(named: "aiplan_diet_record_icon")
                foodItemView.snp.remakeConstraints { make in
                    make.left.equalToSuperview().offset(WKDHPX_S(12))
                    make.right.equalToSuperview().offset(-WKDHPX_S(12))
                    make.bottom.equalToSuperview().offset(-WKDHPX_S(12))
                    make.height.equalTo(WKDHPX_S(56))
                }
            }
            
            
        } else {
            ///训练日
            
            
            ///判断多少天未执行
            var suffixText = ""
            if (plan.consecutiveMissedDays > 0) {
                suffixText = "已经\(plan.consecutiveMissedDays)天没执行啦，要加油哦"
            } else {
                if plan.version == 1 {
                    suffixText = "运动训练日，保持运动节奏"
                } else {
                    suffixText = "运动训练日，吃练结合"
                }
            }
         
            let attrString = NSMutableAttributedString(string: suffixText)
            attrString.color = UserInfo.isXEnjoy ? UIColor.white.withAlphaComponent(0.6) : (UIColor(hexString: "#1E1E20") ?? .white ).withAlphaComponent(0.7)
            titleView.descripLabel.attributedText = attrString
            
            if plan.version == 1 {
                ///计划1.0版本
                sportItemView.hidden(false)
                sportItemView.sizeStyle = .big
                sportItemView.titleLabel.text = "今日运动"
                if trainingDay.finishTrainingTaskCount == trainingDay.trainingTaskCount {
                    sportItemView.descripLabel.text = "今日任务已完成"
                    sportItemView.actionImageView.image = UIImage(named: "aiplan_task_done_icon")
                } else {
                    let days = trainingDay.trainingTaskCount - trainingDay.finishTrainingTaskCount
                 
                    let prefixText = "还剩 "
                    let daysText = "\(days)"
                    let suffixText = " 项任务"
                    let fullText = prefixText + daysText + suffixText

                    let attrString = NSMutableAttributedString(string: fullText)
                    // 普通部分颜色
                    attrString.addAttribute(.foregroundColor, value: UIColor(hexString: "#848A9B") ?? .lightGray, range: NSRange(location: 0, length: prefixText.count))
                    attrString.addAttribute(.foregroundColor, value: UIColor(hexString: "#848A9B") ?? .lightGray, range: NSRange(location: prefixText.count + daysText.count, length: suffixText.count))
                    // days部分颜色和字体
                    attrString.addAttribute(.foregroundColor, value: UIColor(hexString: "#244E98") ?? .systemTeal, range: NSRange(location: prefixText.count, length: daysText.count))
                    attrString.addAttribute(.font, value: UIFont.systemFont(ofSize: WKDHPX_S(14), weight: .semibold), range: NSRange(location: prefixText.count, length: daysText.count))
                    sportItemView.descripLabel.attributedText = attrString
                    
                    sportItemView.actionImageView.image = UIImage(named: "aiplan_train_icon")
                }
                sportItemView.snp.remakeConstraints { make in
                    make.left.equalToSuperview().offset(WKDHPX_S(12))
                    make.bottom.equalToSuperview().offset(-WKDHPX_S(12))
                    make.right.equalToSuperview().offset(-WKDHPX_S(12))
                    make.height.equalTo(WKDHPX_S(56))
                }
                
            } else {
                ///计划2.0版本
                sportItemView.hidden(false)
                sportItemView.sizeStyle = .small
                sportItemView.snp.remakeConstraints { make in
                    make.left.equalToSuperview().offset(WKDHPX_S(12))
                    make.bottom.equalToSuperview().offset(-WKDHPX_S(12))
                    make.right.equalTo(self.snp.centerX).offset(-WKDHPX_S(6))
                    make.height.equalTo(WKDHPX_S(56))
                }
                sportItemView.titleLabel.text = "今日运动"
                
                
                if let targetSportCalories = trainingDay.targetSportCalories, targetSportCalories.decimalValue != .zero {
                    // 有值，直接用 targetSportCalories
                    
                    // 判断 target 是否大于 actual
                    if targetSportCalories.compareCalories(to: trainingDay.sportCalories ?? "0") == .orderedDescending {
                        print("✅ 目标热量大于实际热量")

                        sportItemView.titleLabel.text = "还需消耗"
                        
                        let differenceCalories = targetSportCalories.differenceCalories(with: trainingDay.sportCalories ?? "0")
                        let calories = "\(differenceCalories)"
                        let suffixText = " 千卡"
                        let fullText = calories + suffixText
                        let attrString = NSMutableAttributedString(string: fullText)
                        attrString.addAttribute(.foregroundColor, value: UIColor(hexString: "#363A44") ?? .black, range: NSRange(location: 0, length: calories.count))
                        attrString.addAttribute(.font, value: UIFont.systemFont(ofSize: WKDHPX_S(14), weight: .semibold), range:NSRange(location: 0, length: calories.count))
                        attrString.addAttribute(.foregroundColor, value: UIColor(hexString: "#848A9B") ?? .lightGray, range: NSRange(location: calories.count, length: suffixText.count))
                        sportItemView.descripLabel.attributedText = attrString
                        sportItemView.actionImageView.image = UIImage(named: "aiplan_train_icon")
                    } else {
                        print("🔹 目标热量小于等于实际热量")
                        
                        sportItemView.titleLabel.text = "消耗目标达成"
                        
                        let calories = "\(trainingDay.sportCalories ?? "0")"
                        let suffixText = " 千卡"
                        let fullText = calories + suffixText
                        let attrString = NSMutableAttributedString(string: fullText)
                        attrString.addAttribute(.foregroundColor, value: UIColor(hexString: "#363A44") ?? .black, range: NSRange(location: 0, length: calories.count))
                        attrString.addAttribute(.font, value: UIFont.systemFont(ofSize: WKDHPX_S(14), weight: .semibold), range:NSRange(location: 0, length: calories.count))
                        attrString.addAttribute(.foregroundColor, value: UIColor(hexString: "#848A9B") ?? .lightGray, range: NSRange(location: calories.count, length: suffixText.count))
                        sportItemView.descripLabel.attributedText = attrString
                        sportItemView.actionImageView.image = UIImage(named: "aiplan_task_done_icon")
                    }
                    
                } else {
                    // nil 情况
                    
                    sportItemView.titleLabel.text = "还需消耗"
                    sportItemView.descripLabel.text = "-- 千卡"
                    sportItemView.actionImageView.image = UIImage(named: "aiplan_train_icon")
                }
               
                
                foodItemView.hidden(false)
                foodItemView.sizeStyle = .small
                foodItemView.snp.remakeConstraints { make in
                    make.right.equalToSuperview().offset(-WKDHPX_S(12))
                    make.bottom.equalToSuperview().offset(-WKDHPX_S(12))
                    make.left.equalTo(self.snp.centerX).offset(WKDHPX_S(6))
                    make.height.equalTo(WKDHPX_S(56))
                }
                foodItemView.titleLabel.text = "今日饮食"
                foodItemView.actionImageView.image = UIImage(named: "aiplan_diet_record_icon")
                if trainingDay.finishedDietTaskCount == trainingDay.dietTaskCount {
                    foodItemView.descripLabel.text = "今日任务已完成"
                } else {
                    let days = trainingDay.dietTaskCount - trainingDay.finishedDietTaskCount
                    
                    let prefixText = "还需记录 "
                    let daysText = "\(days)"
                    let suffixText = " 次"
                    let fullText = prefixText + daysText + suffixText

                    let attrString = NSMutableAttributedString(string: fullText)
                    // 普通部分颜色
                    attrString.addAttribute(.foregroundColor, value: UIColor(hexString: "#848A9B") ?? .lightGray, range: NSRange(location: 0, length: prefixText.count))
                    attrString.addAttribute(.foregroundColor, value: UIColor(hexString: "#848A9B") ?? .lightGray, range: NSRange(location: prefixText.count + daysText.count, length: suffixText.count))
                    // days部分颜色和字体
                    attrString.addAttribute(.foregroundColor, value: UIColor(hexString: "#26A15F") ?? .systemTeal, range: NSRange(location: prefixText.count, length: daysText.count))
                    attrString.addAttribute(.font, value: UIFont.systemFont(ofSize: WKDHPX_S(14), weight: .semibold), range: NSRange(location: prefixText.count, length: daysText.count))
                    foodItemView.descripLabel.attributedText = attrString
                }
            }
        }
    }

   
    private func showLeaveStatus(_ plan: MRKCurrentAIPlanModel) {
        self.snp.updateConstraints { make in
            make.height.equalTo(WKDHPX_S(144))
        }
        
        let image = UserInfo.isXEnjoy ? UIImage(named: "aiPlan_enter_bg11") : UIImage(named: "aiPlan_enter_bg12")
        backgroundImage = image?.stretchableImage(left: 16, right: 16)
        
        titleView.hidden(false)
        titleView.snp.remakeConstraints { make in
            make.top.equalToSuperview().offset(WKDHPX_S(12))
            make.left.equalToSuperview().offset(WKDHPX_S(12))
            make.right.equalToSuperview().offset(-WKDHPX_S(12))
        }
        titleView.titleLabel.text = plan.title
        titleView.descripLabel.text = "☕️ 不运动的日子要好好休息，调整好状态哦"
        titleView.isXEnjoyMode = UserInfo.isXEnjoy
        
        
        leaveItemView.hidden(false)
        ///< 请假信息
        if let leaveInfo = plan.leaveInfo {
            leaveItemView.titleLabel.text = "请假\(leaveInfo.leaveDays)天"
            leaveItemView.descripLabel.text = "\(leaveInfo.startDate) - \(leaveInfo.endDate)"
        } else {
            leaveItemView.titleLabel.text = "请假--天"
            leaveItemView.descripLabel.text = "开始时间 - 结束时间"
        }
        leaveItemView.actionImageView.image = UIImage(named: "aiplan_cancel_leave_icon")
    }

    private func showFinishedStatus(_ plan: MRKCurrentAIPlanModel) {
        self.snp.updateConstraints { make in
            make.height.equalTo(WKDHPX_S(80))
        }

        let image = UserInfo.isXEnjoy ? UIImage(named: "aiPlan_enter_bg14") : UIImage(named: "aiPlan_enter_bg13")
        backgroundImage = image?.stretchableImage(left: 16, right: 16)
        
        tapType = .viewReport
        
        actionImageView.hidden(false)
        actionImageView.image = UIImage(named: "aiplan_view_report_icon")
        
        titleView.hidden(false)
        titleView.snp.remakeConstraints { make in
            make.centerY.equalToSuperview()
            make.left.equalToSuperview().offset(WKDHPX_S(12))
            make.right.lessThanOrEqualTo(actionImageView.snp.left).offset(-WKDHPX_S(12))
        }
        titleView.titleLabel.text = plan.title
        titleView.descripLabel.text = "本次AI计划已结束"
        titleView.isXEnjoyMode = UserInfo.isXEnjoy
    }
    
    
    // MARK: - 懒加载控件
    
    public lazy var bgImageView: UIImageView = {
        let imgView = UIImageView()
        imgView.image = UIImage(named: "aiPlan_enter_bg12")
        imgView.contentMode = .scaleToFill
        imgView.clipsToBounds = true
        return imgView
    }()
    
    public lazy var creatImageView: UIImageView = {
        let imgView = UIImageView()
        imgView.image = UIImage(named: "aiplan_start_order_icon")
        imgView.contentMode = .scaleAspectFit
        imgView.clipsToBounds = true
        return imgView
    }()
    
    public lazy var actionImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "aiplan_go_customize_icon")
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    lazy var daysLabel: UILabel = {
        let label = UILabel()
        label.font = kMedium_PingFangSC(s: 13)
        label.textColor = UIColor.secondaryLabel
        label.textAlignment = .left
        label.text = "第4天"
        return label
    }()
    
    public lazy var titleView: AIPlanEnterTitleView = {
        let view = AIPlanEnterTitleView()
        view.titleLabel.text = "120天瘦身减脂计划"
        view.descripLabel.text = "吃练结合 智能健身 科学有效"
        return view
    }()
    
    public lazy var sportItemView: AIPlanEnterItemView = {
        let view = AIPlanEnterItemView()
        view.sizeStyle = .small
        view.titleLabel.text = "今日运动"
        view.descripLabel.text = "今日任务已完成"
        view.actionImageView.image = UIImage(named: "aiplan_train_icon")
        view.gradientColors = [
            UIColor.white.cgColor,
            UIColor.white.cgColor,
            UIColor(hexString: "#E8F2FD")!.cgColor
        ]
        view.layer.cornerRadius = 8.0
        view.layer.masksToBounds = true
        view.addTarget(self, action: #selector(trainListTarget), for: .touchUpInside)
        return view
    }()
    
    public lazy var foodItemView: AIPlanEnterItemView = {
        let view = AIPlanEnterItemView()
        view.sizeStyle = .small
        view.titleLabel.text = "今日饮食"
        view.descripLabel.text = "还需记录一次"
        view.actionImageView.image = UIImage(named: "aiplan_diet_record_icon")
        view.gradientColors = [
            UIColor.white.cgColor,
            UIColor.white.cgColor,
            UIColor(hexString: "#E8FDEC")!.cgColor
        ]
        view.layer.cornerRadius = 8.0
        view.layer.masksToBounds = true
//        view.addTarget(self, action: #selector(dietTarget), for: .touchUpInside)
        return view
    }()
    
    public lazy var leaveItemView: AIPlanEnterItemView = {
        let view = AIPlanEnterItemView()
        view.sizeStyle = .big
        view.titleLabel.text = "请假申请"
        view.descripLabel.text = "本月剩余2次"
        view.actionImageView.image = UIImage(named: "aiplan_cancel_leave_icon")
        view.gradientColors = [
            UIColor.white.cgColor,
            UIColor.white.cgColor,
            UIColor(hexString: "#E8F2FD")!.cgColor
        ]
        view.layer.cornerRadius = 8.0
        view.layer.masksToBounds = true
//        view.addTarget(self, action: #selector(cancelTrainLeave), for: .touchUpInside)
        return view
    }()
    
    
    
    ///< 去运动列表
    @objc func trainListTarget() {
        MRKAIPlanLogic.shared()?.jumpToAIPlan()
    }
    
    ///< 去运动
    @objc func trainTarget() {
        MRKAIPlanLogic.shared()?.goToTaskTrain()
        ReportMrkLogParms(2, "首页 - AI 计划 - 去运动点击", "page_home", "btn_ai_plan_go_exercise", "", 0, nil)
    }
    
    ///< 去饮食
    @objc func dietTarget() {
        MRKAIPlanLogic.shared()?.recordDiet(true)
        ReportMrkLogParms(2, "首页 - AI 计划 - 去记录点击", "page_home", "btn_ai_plan_go_record", "", 0, nil)
    }
    
    ///< 销假
    @objc func cancelTrainLeave() {
        MRKAIPlanLogic.shared()?.cancelLeave()
    }
    
    ///< actionImageView 点击回调
    @objc func actionImageViewTapped() {
        switch tapType {
        case .customizePlan: /// 去定制计划
            MRKAIPlanLogic.shared()?.jumpToAIPlan()
            ReportMrkLogParms(2, "首页 - AI 计划 - 去制定点击", "page_home", "btn_ai_plan_go_create", "", 0, nil)
        case .startPlan: /// 开启计划
            MRKAIPlanLogic.shared()?.jumpToAIPlan()
            ReportMrkLogParms(2, "首页 - AI 计划 - 开启计划点击", "page_home", "btn_ai_plan_start", "", 0, nil)
        case .viewReport: /// 查看报告
            ///
            MRKAIPlanLogic.shared()?.aiPlanReport()
            ReportMrkLogParms(2, "首页 - AI 计划 - 查看报告点击", "page_home", "btn_ai_plan_view_report", "", 0, nil)
        case .viewPlan: /// 查看计划
            MRKAIPlanLogic.shared()?.jumpToAIPlan()
            ReportMrkLogParms(2, "首页 - AI 计划 - 查看计划点击", "page_home", "btn_ai_plan_unstart", "", 0, nil)
        case .viewWeeklyReport: /// 查看周报
            MRKAIPlanLogic.shared()?.jumpToAIPlan()
            ReportMrkLogParms(2, "首页 - AI 计划 - 查看周报点击", "page_home", "btn_ai_plan_view_weekly", "", 0, nil)
        default: break
        
        }
    }

    
    // MARK: - 初始化
    public override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        addActionImageViewTap()
    }

    public required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
        addActionImageViewTap()
    }

    /// 给 actionImageView 添加点击事件
    private func addActionImageViewTap() {
        actionImageView.isUserInteractionEnabled = true
        let tap = UITapGestureRecognizer(target: self, action: #selector(actionImageViewTapped))
        actionImageView.addGestureRecognizer(tap)
    }
       
    // MARK: - UI布局
    
    private func setupUI() {
        backgroundColor = .clear
        
        addSubview(bgImageView)
        bgImageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        addSubview(titleView)
        titleView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(WKDHPX_S(12))
            make.left.equalToSuperview().offset(WKDHPX_S(12))
            make.right.equalToSuperview().offset(-WKDHPX_S(12))
        }
        
        addSubview(daysLabel)
        daysLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(WKDHPX_S(12))
            make.right.equalToSuperview().offset(-WKDHPX_S(22))
        }
        
        addSubview(creatImageView)
        creatImageView.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.left.equalToSuperview().offset(WKDHPX_S(12))
            make.height.equalTo(WKDHPX_S(50))
        }
        
        addSubview(actionImageView)
        actionImageView.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.right.equalToSuperview().offset(0)
            make.height.equalTo(WKDHPX_S(58))
        }
        
        sportItemView.actionImageSelectBlock = { [weak self] in
            self?.trainTarget()
        }
        foodItemView.actionImageSelectBlock = { [weak self] in
            self?.dietTarget()
        }
        leaveItemView.actionImageSelectBlock = { [weak self] in
            self?.cancelTrainLeave()
        }
        addSubview(sportItemView)
        addSubview(foodItemView)
        addSubview(leaveItemView)
        ///< 运动item
        sportItemView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(WKDHPX_S(12))
            make.bottom.equalToSuperview().offset(-WKDHPX_S(12))
            make.right.equalTo(self.snp.centerX).offset(-WKDHPX_S(6))
            make.height.equalTo(WKDHPX_S(56))
        }
        ///< 饮食item
        foodItemView.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-WKDHPX_S(12))
            make.bottom.equalToSuperview().offset(-WKDHPX_S(12))
            make.left.equalTo(self.snp.centerX).offset(WKDHPX_S(6))
            make.height.equalTo(WKDHPX_S(56))
        }
        ///< 请假item（big）
        leaveItemView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(WKDHPX_S(12))
            make.right.equalToSuperview().offset(-WKDHPX_S(12))
            make.bottom.equalToSuperview().offset(-WKDHPX_S(12))
            make.height.equalTo(WKDHPX_S(56))
        }
    }
}






public class AIPlanEnterTitleView: UIView {
    
    var isXEnjoyMode: Bool = false {
        didSet {
            updateAppearanceForColor(isXEnjoyMode)
        }
    }
    
    private func updateAppearanceForColor(_ mode: Bool) {
        if mode {
            titleLabel.textColor = .white
            descripLabel.textColor = .white.withAlphaComponent(0.6)
            arrowImageView.tintColor = .white
        } else {
            titleLabel.textColor = UIColor(hexString: "#1E1E20")
            descripLabel.textColor = (UIColor(hexString: "#1E1E20") ?? .white ).withAlphaComponent(0.7)
            arrowImageView.tintColor = UIColor(hexString: "#1E1E20")
        }
    }
    
    
    // MARK: - 懒加载控件
    lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.boldSystemFont(ofSize: WKDHPX_S(18))
        label.textColor = UIColor(hexString: "#1E1E20")
        label.textAlignment = .left
        label.text = "120天瘦身减脂计划"
        return label
    }()
    
    lazy var descripLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: WKDHPX_S(13))
        label.textColor = UIColor(hexString: "#848A9B")
        label.textAlignment = .left
        label.text = "吃练结合 智能健身 科学有效"
        return label
    }()
    
    lazy var arrowImageView: UIImageView = {
        let imageView = UIImageView()
        let image = UIImage(named: "icon_cell_right_arrow")?.withRenderingMode(.alwaysTemplate)
        imageView.image = image
        imageView.tintColor = UIColor(hexString: "#1E1E20")
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    // MARK: - 初始化
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }
    
    // MARK: - UI布局
    private func setupUI() {
        addSubview(titleLabel)
        addSubview(arrowImageView)
        addSubview(descripLabel)
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.left.equalToSuperview()
        }
        arrowImageView.snp.makeConstraints { make in
            make.centerY.equalTo(titleLabel.snp.centerY)
            make.left.equalTo(titleLabel.snp.right).offset(2)
            make.width.equalTo(WKDHPX_S(16))
            make.height.equalTo(WKDHPX_S(16))
        }
        
        descripLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(WKDHPX_S(4))
            make.left.equalToSuperview()
            make.bottom.equalToSuperview()
        }
    }
    
    public override func layoutSubviews() {
        super.layoutSubviews()
        
    }
}




/**
 ///< 获取未完成任务列表
 guard let unfinishTaskList = overViewModel?.currentPlan?.currentTrainingDay?.unfinishTaskList, !unfinishTaskList.isEmpty else {
     print("暂无未完成运动任务")
     return
 }

 ///< 查找第一个未完成的任务（taskStatus == 0）
 if let firstUnfinishedTask = unfinishTaskList.first(where: { $0.taskStatus == 0 }) {
     /// 跳转到相应任务去
     print("跳转到未完成任务：\(firstUnfinishedTask)")
     MRKAIPlanLogic.shared()?.jump(toTaskTrain: firstUnfinishedTask)
 } else {
     /// 所有任务都已完成，跳转到全部任务页面或提示
     print("所有运动任务已完成，跳转到全部任务页")

     MRKAIPlanLogic.shared()?.jumpToAIPlan()
 }
 */


//110. 16



// MARK: - 绘制外边框和阴影

/**
 public override func layoutSubviews() {
     super.layoutSubviews()
     
         // 移除旧的边框
         layer.sublayers?.filter { $0.name == "aiPlanBorderLayer" }.forEach { $0.removeFromSuperlayer() }
 
         // 创建遮罩层
         let maskLayer = CAShapeLayer()
         maskLayer.frame = bounds
 
         switch enterBorderStyle {
         case .cornerRadiusStyle:
             layer.cornerRadius = 16
             layer.masksToBounds = false
 
             let path = UIBezierPath(roundedRect: bounds, cornerRadius: 16)
             maskLayer.path = path.cgPath
 
             let borderLayer = CAShapeLayer()
             borderLayer.name = "aiPlanBorderLayer"
             borderLayer.path = path.cgPath
             borderLayer.fillColor = UIColor.clear.cgColor
             borderLayer.strokeColor = UIColor.white.cgColor
             borderLayer.lineWidth = 2
             borderLayer.frame = bounds
             layer.addSublayer(borderLayer)
 
         case .beizerPathStyle:
             layer.cornerRadius = 0
             layer.masksToBounds = false
 
             let cornerRadius: CGFloat = 16
             let offsetY: CGFloat = WKDHPX_S(10)
             let width = bounds.width
             let height = bounds.height
 
             let path = UIBezierPath()
             path.move(to: CGPoint(x: 0, y: cornerRadius + offsetY))
             path.addArc(withCenter: CGPoint(x: cornerRadius, y: cornerRadius + offsetY),
                         radius: cornerRadius,
                         startAngle: .pi,
                         endAngle: -.pi / 2,
                         clockwise: true)
             path.addLine(to: CGPoint(x: width - WKDHPX_S(90), y: offsetY))
             path.addCurve(to: CGPoint(x: width - WKDHPX_S(30), y: offsetY),
                           controlPoint1: CGPoint(x: width - WKDHPX_S(90), y: offsetY),
                           controlPoint2: CGPoint(x: width - WKDHPX_S(60), y: -offsetY))
             path.addLine(to: CGPoint(x: width - cornerRadius, y: offsetY))
             path.addArc(withCenter: CGPoint(x: width - cornerRadius, y: cornerRadius + offsetY),
                         radius: cornerRadius,
                         startAngle: -.pi / 2,
                         endAngle: 0,
                         clockwise: true)
             path.addLine(to: CGPoint(x: width, y: height - cornerRadius))
             path.addArc(withCenter: CGPoint(x: width - cornerRadius, y: height - cornerRadius),
                         radius: cornerRadius,
                         startAngle: 0,
                         endAngle: .pi / 2,
                         clockwise: true)
             path.addLine(to: CGPoint(x: cornerRadius, y: height))
             path.addArc(withCenter: CGPoint(x: cornerRadius, y: height - cornerRadius),
                         radius: cornerRadius,
                         startAngle: .pi / 2,
                         endAngle: .pi,
                         clockwise: true)
             path.close()
 
             maskLayer.path = path.cgPath
 
             let borderLayer = CAShapeLayer()
             borderLayer.name = "aiPlanBorderLayer"
             borderLayer.path = path.cgPath
             borderLayer.fillColor = UIColor.clear.cgColor
             borderLayer.strokeColor = UIColor.white.cgColor
             borderLayer.lineWidth = 2
             borderLayer.frame = bounds
             layer.addSublayer(borderLayer)
         }
 
         // 设置遮罩，裁剪掉外部多余UI
         self.layer.mask = maskLayer
 }
 */
