//
//  AIPlanEnterItemView.swift
//  Student_IOS
//
//  Created by <PERSON><PERSON> on 2025/6/30.
//

import UIKit
import SnapKit

enum AIPlanEnterItemSizeStyle {
    case big
    case small
}

public class AIPlanEnterItemView: UIControl {

    // MARK: - 渐变色属性
    /// 外部可控的渐变色数组，赋值后自动刷新背景
    var gradientColors: [CGColor]? {
        didSet {
            setNeedsLayout()
        }
    }

    // 尺寸风格
    var sizeStyle: AIPlanEnterItemSizeStyle = .small {
        didSet {
            updateLayoutForSizeStyle()
        }
    }
    
    public var actionImageSelectBlock : (() -> Void)?

    // MARK: - 懒加载控件
    lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.font = kSemibold_PingFangSC(s: 13)
        label.textColor = UIColor.black
        label.textAlignment = .left
        label.text = "今日饮食"
        return label
    }()

    lazy var descripLabel: UILabel = {
        let label = UILabel()
        label.font = kRegular_PingFangSC(s: 11)
        label.textColor = UIColor.secondaryLabel
        label.textAlignment = .left
        label.text = "本月完成率"
        return label
    }()

    lazy var actionImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage.init(named: "aiplan_diet_record_icon")
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()

    // MARK: - 初始化

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        addActionImageViewTap()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
        addActionImageViewTap()
    }

    /// 给 actionImageView 添加点击事件
    private func addActionImageViewTap() {
        actionImageView.isUserInteractionEnabled = true
        let tap = UITapGestureRecognizer(target: self, action: #selector(actionImageViewTapped))
        actionImageView.addGestureRecognizer(tap)
    }
    
    ///< actionImageView 点击回调
    @objc func actionImageViewTapped() {
        actionImageSelectBlock?()
    }

    // MARK: - UI布局

    private func setupUI() {
        backgroundColor = .clear
        layer.cornerRadius = 16
        clipsToBounds = true

        addSubview(titleLabel)
        addSubview(descripLabel)
        addSubview(actionImageView)

        titleLabel.snp.makeConstraints { make in
            make.bottom.equalTo(self.snp.centerY).offset(-2)
            make.left.equalToSuperview().offset(8)
        }
        descripLabel.snp.makeConstraints { make in
            make.top.equalTo(self.snp.centerY).offset(2) 
            make.left.equalToSuperview().offset(8)
        }
        actionImageView.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.right.equalToSuperview().offset(-8)
        }
        
        updateLayoutForSizeStyle()
    }

    private func updateLayoutForSizeStyle() {
        switch sizeStyle {
        case .big:

            actionImageView.snp.updateConstraints { make in
                make.right.equalToSuperview().offset(-12)
            }
        case .small:
            
            actionImageView.snp.updateConstraints { make in
                make.right.equalToSuperview().offset(-8)
            }
        }
    }

    // MARK: - 渐变背景

    public override func layoutSubviews() {
        super.layoutSubviews()
        applyGradientBackground()
    }

    private func applyGradientBackground() {
        // 移除旧的渐变层
        layer.sublayers?.filter { $0.name == "aiPlanGradientLayer" }.forEach { $0.removeFromSuperlayer() }

        let gradientLayer = CAGradientLayer()
        gradientLayer.name = "aiPlanGradientLayer"
        gradientLayer.frame = bounds
        if let colors = gradientColors, !colors.isEmpty {
            gradientLayer.colors = colors
        } else {
            gradientLayer.colors = [
                UIColor.systemBlue.withAlphaComponent(0.18).cgColor,
                UIColor.systemPurple.withAlphaComponent(0.18).cgColor
            ]
        }
        gradientLayer.startPoint = CGPoint(x: 0.5, y: 0)
        gradientLayer.endPoint = CGPoint(x: 0.5, y: 1)
        layer.insertSublayer(gradientLayer, at: 0)
    }
}


