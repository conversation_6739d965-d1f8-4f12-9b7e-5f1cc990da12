//
//  MRKRecommendPlanCell.m
//  Student_IOS
//
//  Created by <PERSON><PERSON> on 2025/6/24.
//

#import "MRKRecommendPlanCell.h"

@interface MRKRecommendPlanCell ()
@property (nonatomic, strong) UILabel *courseTitle;
@property (nonatomic, strong) UILabel *courseDescrip;
@property (nonatomic, strong) UIButton *moreBtn;
@property (nonatomic, strong) MRKRecommendTableView *courseTableView;
@end

@implementation MRKRecommendPlanCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier{
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.contentView.lee_theme.LeeConfigBackgroundColor(@"main_view_backColor");
        
        [self.contentView addSubview:self.courseTitle];
        [self.courseTitle mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.contentView.mas_top).offset(WKDHPX(28));
            make.left.equalTo(self.contentView.mas_left).offset(WKDHPX(16));
            make.height.mas_equalTo(WKDHPX(22));
        }];

        [self.contentView addSubview:self.moreBtn];
        [self.moreBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(self.courseTitle.mas_centerY);
            make.right.equalTo(self.contentView.mas_right).offset(-WKDHPX(16));
        }];
        
        [self.contentView addSubview:self.courseTableView];
        [self.courseTableView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.courseTitle.mas_bottom).offset(WKDHPX(12));
            make.bottom.equalTo(self.contentView.mas_bottom);
            make.left.equalTo(self.contentView.mas_left).offset(WKDHPX(16));
            make.right.equalTo(self.contentView.mas_right).offset(-WKDHPX(16));
        }];
        
        @weakify(self);
        self.lee_theme.LeeThemeChangingBlock(^(NSString * _Nonnull tag, UIView * _Nonnull item) {
            @strongify(self);
            if ([tag isEqualToString:THEME_XENJOY]) {
                self.courseTitle.textColor = UIColorHex(#FCECE1);
            } else {
                self.courseTitle.textColor = UIColorHex(#363A44);
            }
        });
    }
    return self;
}

- (void)layoutSubviews{
    [super layoutSubviews];
}

- (void)setDataArray:(NSArray<MRKCoursePlanModel *> *)dataArray{
    _dataArray = dataArray;
    self.courseTableView.dataSource = dataArray;
}

- (UIButton *)moreBtn {
    if (!_moreBtn){
        _moreBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_moreBtn setTitle:@"查看更多" forState:UIControlStateNormal];
        [_moreBtn setTitleColor:UIColorHex(#848A9B) forState:UIControlStateNormal];
        _moreBtn.titleLabel.font = kSystem_Font_NoDHPX((12));
        [_moreBtn setImage:[UIImage imageNamed:@"icon_arrow_12"] forState:UIControlStateNormal];
        [_moreBtn addTarget:self action:@selector(showTargetAction:) forControlEvents:UIControlEventTouchUpInside];
        [_moreBtn setSemanticContentAttribute:UISemanticContentAttributeForceRightToLeft];
    }
    return _moreBtn;
}

///更多主题课
- (void)showTargetAction:(id)sender {
    if (self.morePlanBlock) {
        self.morePlanBlock();
    }
}

- (UILabel *)courseTitle {
    if (!_courseTitle) {
        UILabel *l = [UILabel new];
        l.font = kMedium_Font_NoDHPX(WKDHPX(17));
        l.textColor = UIColorHex(#363A44);
        l.text = @"为你推荐";
        _courseTitle = l;
    }
    return _courseTitle;
}

- (MRKRecommendTableView *)courseTableView{
    if (!_courseTableView) {
        _courseTableView = [[MRKRecommendTableView alloc] init];
        @weakify(self);
        _courseTableView.selectPlanBlock = ^(MRKCoursePlanModel * _Nonnull model) {
            @strongify(self);
            if (self.topPlanBlock) {
                self.topPlanBlock(model);
            }
        };
    }
    return _courseTableView;
}
@end









@interface MRKRecommendTableView ()<UITableViewDataSource, UITableViewDelegate>
@property (nonatomic, strong) UITableView *tableView;
@end

@implementation MRKRecommendTableView
- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        
        [self.tableView registerClass:[MRKRecommendPlanListCell class] forCellReuseIdentifier:@"MRKRecommendPlanListCell"];
        [self addSubview:self.tableView];
        [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.mas_top);
            make.bottom.equalTo(self.mas_bottom);
            make.left.mas_equalTo(self.mas_left);
            make.right.mas_equalTo(self.mas_right);
        }];
        
        ///监听高度
        @weakify(self);
        [[RACObserve(self.tableView, contentSize) ignore:NULL] subscribeNext:^(id  _Nullable x) {
            @strongify(self);
            CGFloat height = self.tableView.contentSize.height;
            self.height = height;
            [self.tableView mas_updateConstraints:^(MASConstraintMaker *make) {
                make.height.mas_equalTo(height);
            }];
        }];
    }
    return self;
}

- (void)layoutSubviews {
    [super layoutSubviews];
}


- (void)setDataSource:(NSArray<MRKCoursePlanModel *> *)dataSource{
    _dataSource = dataSource;
    [self.tableView reloadData];
}

#pragma mark Table M comments
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section{
    return self.dataSource.count;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    return WKDHPX(162);
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
    static NSString *identifier = @"MRKRecommendPlanListCell";
    MRKRecommendPlanListCell *cell = [tableView dequeueReusableCellWithIdentifier:identifier];
    if (!cell) {
        cell = [[MRKRecommendPlanListCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:identifier];
    }
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    cell.backgroundColor = UIColor.clearColor;
    cell.planModel = [self.dataSource objectAtIndex:(indexPath.row)];
    return cell;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    
    MRKCoursePlanModel *model = [self.dataSource objectAtIndex:(indexPath.row)];
    if (self.selectPlanBlock) {
        self.selectPlanBlock(model);
    }
}

- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
        _tableView.backgroundColor = UIColor.clearColor;
        _tableView.backgroundView.backgroundColor = UIColor.clearColor;
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.scrollEnabled = NO;
        _tableView.dataSource = self;
        _tableView.delegate = self;
    }
    return _tableView;
}
@end



@interface MRKRecommendPlanListCell ()
@property (nonatomic, strong) UIImageView *mainImageView;
@property (nonatomic, strong) UIImageView *shadowView;
@property (nonatomic, strong) UIImageView *tagImageView;
@property (nonatomic, strong) MRKPlanLabel *tipLabel;
@property (nonatomic, strong) UILabel *titleLab;
@end

@implementation MRKRecommendPlanListCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier{
    if (self =[super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.contentView.backgroundColor = UIColor.clearColor;
        self.backgroundColor = UIColor.clearColor;
        
        [self.contentView addSubview:self.mainImageView];
        [self.mainImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.mas_equalTo(UIEdgeInsetsMake(0, 0, WKDHPX(12), 0));
        }];
        
        [self.mainImageView addSubview:self.shadowView];
        [self.mainImageView addSubview:self.tagImageView];
        [self.mainImageView addSubview:self.titleLab];
        [self.mainImageView addSubview:self.tipLabel];
    }
    return self;
}

- (void)layoutSubviews {
    [super layoutSubviews];
    
    [self.shadowView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.bottom.right.mas_equalTo(0);
        make.top.equalTo(self.mainImageView.mas_centerY);
    }];
    
    [self.tagImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.equalTo(@(WKDHPX(10)));
        make.height.mas_equalTo(WKDHPX(18));
    }];
    
    [self.titleLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(WKDHPX(12));
        make.right.bottom.mas_equalTo(-WKDHPX(12));
    }];
    
    [self.tipLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(WKDHPX(12));
        make.height.mas_equalTo(WKDHPX(22));
        make.bottom.equalTo(self.titleLab.mas_top).offset(-WKDHPX(8));
    }];
}

- (void)setPlanModel:(MRKCoursePlanModel *)planModel{
    _planModel = planModel;
    
    NSString *middleURl = [NSString imageUrlClip:planModel.mainFigure
                                         andSize:CGSizeMake(MainWidth - PageMargin_16*2, WKDHPX(150))];
    [self.mainImageView setImageWithURL:[NSURL URLWithString:middleURl]
                            placeholder:[UIImage imageNamed:@"pic_1"]
                                options:YYWebImageOptionProgressiveBlur|YYWebImageOptionSetImageWithFadeAnimation
                             completion:nil];
    
    self.titleLab.text = planModel.title;
    self.tipLabel.cycletime = [NSString stringWithFormat:@"%@周", planModel.planningCycle?:@"--"];
    self.tipLabel.describe = [NSString stringWithFormat:@"%@｜%@", planModel.difficulty?:@"", planModel.equipmentTypeName?:@""];
    
    if (!planModel.hiddenTagIcon) {
        self.tagImageView.hidden = YES;
        NSString *imageName = @"";
        NSInteger viptype = planModel.vipType.intValue;
        switch (viptype) {
            case 10:
                imageName = @"courseTag_vip";
                break;
            case 30:
                imageName = @"courseTag_enjoyvip";
                break;
            default:
                break;
        }
        if (imageName.length > 0) {
            self.tagImageView.hidden = NO;
            self.tagImageView.image = [UIImage imageNamed:imageName];
        }
    }
}

- (UIImageView *)mainImageView {
    if (!_mainImageView) {
        UIImageView *imagev = [[UIImageView alloc] init];
        imagev.contentMode = UIViewContentModeScaleAspectFill;
        imagev.backgroundColor = [UIColor whiteColor];
        imagev.clipsToBounds = YES;
        imagev.layer.cornerRadius = 8.0;
        imagev.layer.masksToBounds = YES;
        _mainImageView = imagev;
    }
    return _mainImageView;
}

- (UIImageView *)shadowView{
    if (!_shadowView) {
        _shadowView = [[UIImageView alloc] init];
        [_shadowView az_setGradientBackgroundWithColors:@[[UIColor clearColor],
                                                          [UIColor colorWithWhite:0 alpha:0.4]]
                                              locations:nil
                                             startPoint:CGPointMake(0, 0)
                                               endPoint:CGPointMake(0, 1)];
        
    }
    return _shadowView;
}

/**图片标签**/
- (UIImageView *)tagImageView{
    if (!_tagImageView) {
        _tagImageView = [[UIImageView alloc] init];
        _tagImageView.contentMode = UIViewContentModeScaleAspectFit;
    }
    return _tagImageView;
}

- (UILabel *)titleLab {
    if (_titleLab) return _titleLab;
    UILabel *label = [[UILabel alloc] init];
    label.font = kMedium_Font_NoDHPX(WKDHPX(20));
    label.textColor = [UIColor whiteColor];
    _titleLab = label;
    return _titleLab;
}

- (MRKPlanLabel *)tipLabel{
    if (_tipLabel) return _tipLabel;
    _tipLabel = [[MRKPlanLabel alloc] init];
    [_tipLabel planUILayout];
    return _tipLabel;
}

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}
@end






