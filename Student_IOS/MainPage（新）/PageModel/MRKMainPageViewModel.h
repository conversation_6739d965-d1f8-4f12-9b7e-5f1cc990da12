//
//  MRKMainPageViewModel.h
//  Student_IOS
//
//  Created by Junq on 2024/6/14.
//

#import <Foundation/Foundation.h>
#import "MRKCourseApi.h"
#import "MRKHomePageModels.h"
#import "MRKHealthMineModel.h"
#import "MRKDailyScheduleModel.h"
#import "MRKCourseFilterModels.h"
#import "MRKCoursePlanModel.h"
#import "MRKPurchaseDiscountModel.h"


NS_ASSUME_NONNULL_BEGIN

@interface MRKMainPageViewModel : NSObject
@property (nonatomic, readonly) RACSignal *updateDetailSignal;
@property (nonatomic, readonly) RACSignal *requestErrorSignal;

@property (nonatomic, assign, readonly) BOOL hasScaleDevice;                               ///是否有体脂秤

@property (nonatomic, copy, nullable, readonly) NSString *equimentTypeId;                  ///设备大类
@property (nonatomic, strong, nullable, readonly) MRKDeviceModel *deviceModel;             ///首页当前设备
@property (nonatomic, strong, nullable, readonly) NSArray<MRKDeviceModel *> *deviceArray;  ///全部设备

@property (nonatomic, strong, readonly) MemberInfoDataDTO *vipInfomodel;                   ///会员信息
@property (nonatomic, strong, readonly) HomeTargetModel *targerModel;                      ///今日目标
@property (nonatomic, assign, readonly) BOOL newUser;                                      ///新人专区状态接口
@property (nonatomic, strong, readonly) NSArray<AdvertModel *> *resourceFirstDatas;        ///资源位1区
@property (nonatomic, strong, readonly) NSArray<AdvertModel *> *resourceSecondDatas;       ///资源位2区
@property (nonatomic, strong, readonly) NSArray<HomeKingPackageModel *> *kingCoursePages;  ///王牌课 二级课包
@property (nonatomic, strong, readonly) NSArray<MRKCourseModel *> *livingCourses;          ///直播课
@property (nonatomic, strong, readonly) NSArray<LiveModel *> *liveCourseList;              ///实景视频
@property (nonatomic, strong, readonly) NSArray<MRKCourseModel *> *xenjoyDatas;            ///绝影会员课
@property (nonatomic, strong, readonly) NSArray<MRKThemeListModel *> *themeCourseList;     ///主题列表
@property (nonatomic, strong, readonly) NSArray<AdvertModel *> *bannerList;                ///banner列表
@property (nonatomic, strong, readonly) NSArray<HomeTopCourseModel *> *topCourse;          ///燃友都在练
@property (nonatomic, strong, readonly) NSArray<MRKCoursePlanModel *> *topPlanArray;       ///推荐计划数据
@property (nonatomic, strong, readonly) MRKPlanOverViewModel *planOverViewModel;           ///AI计划概览

///
@property (nonatomic, assign, readonly) BOOL loaded; ///接口加载过



///
///
///
///
/// 刷新首页设备，并刷新全部数据
- (void)refreshDeviceData;

/// 请求全部数据
- (void)refreshPageData;


- (void)refreshHeaderData;


/// 请求部分数据【用于回到首页，刷新运动数据等】
- (void)refreshPagePartData;


- (BOOL)pageNoData;

/// 直播课程 刷新直播
- (void)requestLiveCoursesData:(void(^)(void))completion;

/// 获取用户优惠弹窗
- (void)fetchUserPurchaseDiscount:(void(^)(MRKPurchaseDiscountModel*))completion;
/// 确认获取用户优惠弹窗
- (void)confirmPurchaseDiscount:(NSInteger)sceneCode;

/// VIP会员当日到期
- (BOOL)vipTimeJudge:(MemberInfoDataDTO *)model;

/// VIP会员过期一天
- (BOOL)vipExpireJudge:(MemberInfoDataDTO *)model;
@end


NS_ASSUME_NONNULL_END
