//
//  MRKMainPageViewModel.m
//  Student_IOS
//
//  Created by Jun<PERSON> on 2024/6/14.
//

#import "MRKMainPageViewModel.h"
#import "MRKDeviceListManager.h"
#import "MRKDeviceURLRequest.h"
#import "MRKTrainingDataAPIClient.h"
#import "MRKMineDataModel.h"
#import "MRKCourseApi.h"
#import "MRKPlanOverViewModel.h"
#import "MRKAIPlanAPIClient.h"

@interface MRKMainPageViewModel()
@property (nonatomic, strong) RACSubject *updateDetailSignal;
@property (nonatomic, strong) RACSubject *requestErrorSignal;

@property (nonatomic, copy, nullable) NSString *equimentTypeId;                  ///设备大类
@property (nonatomic, strong, nullable) MRKDeviceModel *deviceModel;             ///首页当前设备
@property (nonatomic, strong, nullable) NSArray<MRKDeviceModel *> *deviceArray;  ///全部设备

@property (nonatomic, strong) MemberInfoDataDTO *vipInfomodel;                   ///会员信息
@property (nonatomic, strong) HomeTargetModel *targerModel;                      ///今日目标
@property (nonatomic, strong) NSArray<AdvertModel *> *resourceFirstDatas;        ///资源位1区
@property (nonatomic, strong) NSArray<AdvertModel *> *resourceSecondDatas;       ///资源位2区
@property (nonatomic, strong) NSArray<HomeKingPackageModel *> *kingCoursePages;  ///王牌课 二级课包
@property (nonatomic, strong) NSArray<MRKCourseModel *> *livingCourses;          ///直播课
@property (nonatomic, strong) NSArray<LiveModel *> *liveCourseList;              ///实景视频
@property (nonatomic, assign) BOOL newUser;                                      ///新人专区状态接口
@property (nonatomic, strong) NSArray<MRKCourseModel *> *xenjoyDatas;            ///绝影会员课
@property (nonatomic, strong) NSArray<MRKThemeListModel *> *themeCourseList;     ///主题列表
@property (nonatomic, strong) NSArray<AdvertModel *> *bannerList;                ///banner列表
@property (nonatomic, strong) NSArray<HomeTopCourseModel *> *topCourse;          ///燃友都在练
@property (nonatomic, strong) NSArray<MRKCoursePlanModel *> *topPlanArray;       ///推荐计划数据
@property (nonatomic, strong) MRKPlanOverViewModel *planOverViewModel;           ///AI计划概览
///
@property (nonatomic, assign) BOOL loaded; //接口加载过
@end





@implementation MRKMainPageViewModel

- (void)dealloc {
     NSLog(@"😊😊😊-----dealloc-----😊😊😊%@" , NSStringFromClass([self class]));
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (instancetype)init {
    if (self = [super init]) {
        self.updateDetailSignal = [[RACSubject subject] setNameWithFormat:@"MRKMainPageViewModel"];
        self.requestErrorSignal = [[RACSubject subject] setNameWithFormat:@"MRKMainPageViewModelError"];
        
        ///通知刷新首页数据
        [[NSNotificationCenter defaultCenter] addObserver:self
                                                 selector:@selector(refreshPageData)
                                                     name:@"kRefreshMainPageNotification"
                                                   object:nil];
    }
    return self;
}



#pragma mark - 数据请求
/// 刷新设备数据
- (void)refreshDeviceData {
    MRKDeviceModel *model = [MRKDeviceListManager shareManager].deviceModel;
    if (model != nil){
        [self refreshPageData];
    }else{
        [[MRKDeviceListManager shareManager] requestAllDeviceRefresh:NO];
    }
}


- (void)refreshHeaderData {
    ///请求报错
    if ([MRKDeviceListManager shareManager].requestError){
        [self refreshDeviceData];
    }else{
        [self refreshPageData];
    }
}

/// 请求所有数据
- (void)refreshPageData {
    NSLog(@"收到通知，刷新数据");
    
    ///请求报错
    if ([MRKDeviceListManager shareManager].requestError){
        [(RACSubject *)self.requestErrorSignal sendNext:@"error"];
        return;
    }
    
    self.deviceArray = [MRKDeviceListManager shareManager].deviceArray;
    self.equimentTypeId = [MRKDeviceListManager shareManager].equimentTypeId;
    self.deviceModel = [MRKDeviceListManager shareManager].deviceModel;
    
    @weakify(self);
    RACSignal *userSignal = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self getUserInfo:^{
            [subscriber sendNext:nil];
        }];
        return nil;
    }];
    
    //今日目标
    RACSignal *targetAction = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self requestTargetData:^{
            [subscriber sendNext:nil];
        }];
        return nil;
    }];
    
    // 金刚区
    RACSignal *kingkongAction = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self requestKingKongFirstData:^{
            [subscriber sendNext:nil];
        }];
        return nil;
    }];
    
    //  AI计划概览
    RACSignal *planOverViewAction = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self requestPlanOverViewData:^{
            [subscriber sendNext:nil];
        }];
        return nil;
    }];
    
    // 王牌课
    RACSignal *kingAction = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self requestKingCourseData:^{
            [subscriber sendNext:nil];
        }];
        return nil;
    }];
    
    // 直播课
    RACSignal *liveAction = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self requestLiveCoursesData:^{
            [subscriber sendNext:nil];
        }];
        return nil;
    }];
    
    // 实景
    RACSignal *realAction = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self requestLiveCourseDatas:^{
            [subscriber sendNext:nil];
        }];
        return nil;
    }];
    
    // memberInfo
    RACSignal *memberAction = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self getNewUserRequest:^{
            [subscriber sendNext:nil];
        }];
        return nil;
    }];
    
    // 绝影会员
    RACSignal *xenjoyAction = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self requestXenjoyData:^{
            [subscriber sendNext:nil];
        }];
        return nil;
    }];
    
    // 主题课程
    RACSignal *themeAction = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self requestThemeCourseData:^{
            [subscriber sendNext:nil];
        }];
        return nil;
    }];

    // banner
    RACSignal *bannerAction = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self requestBannerData:^{
            [subscriber sendNext:nil];
        }];
        return nil;
    }];
    
    // 排行榜
    RACSignal *topAction = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self requestHomeTop:^{
            [subscriber sendNext:nil];
        }];
        return nil;
    }];
    
    ///推荐计划
    RACSignal *topPlanAction = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self requestTopPlanData:^{
            [subscriber sendNext:nil];
        }];
        return nil;
    }];
    
    [[RACSignal combineLatest:@[userSignal, targetAction, kingkongAction, planOverViewAction, kingAction, liveAction, realAction, memberAction, xenjoyAction, themeAction, bannerAction, topAction, topPlanAction]] subscribeNext:^(id x) {
        @strongify(self);
        [(RACSubject *)self.updateDetailSignal sendNext:@"endRefresh"];
    }];
}


/// 刷新数据所有数据
- (void)refreshPagePartData {
    
    @weakify(self);
    RACSignal *userSignal = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self getUserInfo:^{
            [subscriber sendNext:nil];
        }];
        return nil;
    }];
    
    ///今日目标
    RACSignal *targetAction = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self requestTargetData:^{
            [subscriber sendNext:nil];
        }];
        return nil;
    }];
    
    //  AI计划概览
    RACSignal *planOverViewAction = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self requestPlanOverViewData:^{
            [subscriber sendNext:nil];
        }];
        return nil;
    }];
    
    ///memberInfo
    RACSignal *memberAction = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self getNewUserRequest:^{
            [subscriber sendNext:nil];
        }];
        return nil;
    }];
    
    /// 王牌课
    RACSignal *kingAction = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self requestKingCourseData:^{
            [subscriber sendNext:nil];
        }];
        return nil;
    }];
    
    /// 直播课
    RACSignal *liveAction = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self requestLiveCoursesData:^{
            [subscriber sendNext:nil];
        }];
        return nil;
    }];
    
    /// 实景
    RACSignal *realAction = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self requestLiveCourseDatas:^{
            [subscriber sendNext:nil];
        }];
        return nil;
    }];
    
    /// 主题课程
    RACSignal *themeAction = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self requestThemeCourseData:^{
            [subscriber sendNext:nil];
        }];
        return nil;
    }];

    [[RACSignal combineLatest:@[userSignal, targetAction, planOverViewAction, kingAction, liveAction, realAction, memberAction, themeAction]] subscribeNext:^(id x) {
        @strongify(self);
        [(RACSubject *)self.updateDetailSignal sendNext:@"endRefresh"];
    }];
}



- (BOOL)hasScaleDevice {
    return [[MRKDeviceListManager shareManager] hasScaleDevice];
}

#pragma mark - 接口请求

- (void)getUserInfo:(void(^)(void))completion {
    [MRKBaseRequest mrkRequestType:YTKRequestMethodGET
                               url:@"/user/user-member"
                           andParm:nil
                      notShowError:YES
          completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        id data = [request.responseObject valueForKeyPath:@"data"];
        self.vipInfomodel = [MemberInfoDataDTO modelWithJSON:data];
        completion();
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        completion();
    }];
}

/// 今日运动数据
- (void)requestTargetData:(void(^)(void))completion {
    [MRKTrainingDataAPIClient requestTodayTargetSuccess:^(id data) {
        self.targerModel = [HomeTargetModel modelWithJSON:data];
        completion();
    } failure:^(id data) {
        completion();
    }];
}

/// 实景课程 全部实景
- (void)requestLiveCourseDatas:(void(^)(void))completion {
    [MRKCourseApi liveVideoCourse:1 equimentID:self.equimentTypeId topRankCount:4 success:^(id  _Nonnull data) {
        NSArray *liveVideo = [NSArray modelArrayWithClass:[LiveModel class] json:[data valueForKeyPath:@"records"]].mutableCopy;
        self.liveCourseList = liveVideo;
        completion();
    } failure:^(NSError * _Nonnull error) {
        completion();
    }];
}

/// 直播课程
- (void)requestLiveCoursesData:(void(^)(void))completion {
    NSDictionary *parms = @{
        @"productId":self.equimentTypeId?:@""
    }.mutableCopy;
    [MRKBaseRequest mrkGetRequestUrl:@"/course/getTop3LiveCourse"
                             andParm:parms
            completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        id data = [request.responseObject valueForKeyPath:@"data"];\
        NSArray *array = [NSArray modelArrayWithClass:[MRKCourseModel class] json:data];\
        self.livingCourses = array;
        completion();
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        completion();
    }];
}

/// 王牌课
- (void)requestKingCourseData:(void(^)(void))completion {
    [MRKBaseRequest mrkGetRequestUrl:@"/course/series/search"
                             andParm:nil
            completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        id data = [request.responseObject objectForKey:@"data"];
        NSArray *array = [NSArray modelArrayWithClass:[HomeKingCourseModel class] json:data];
        
        NSMutableArray *tmp = [NSMutableArray array];
        for (HomeKingCourseModel *page in array) {
            //每个一级标题下对应的 二级课包
            [page.packages enumerateObjectsUsingBlock:^(HomeKingPackageModel * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
                obj.pageId = page.cid;
                
                HomeKingContentCourseModel *m = obj.allCourse.firstObject;
                obj.cover = m.cover;
                obj.nums = [NSString stringWithFormat:@"%ld", obj.allCourse.count];
                obj.isNew = m.isNew;
                obj.products = @[];
                obj.allCourse = @[];
            }];
            [tmp addObjectsFromArray:page.packages];
        }
        self.kingCoursePages = tmp;
        completion();
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        completion();
    }];
}

/// 全部课程等金刚区
- (void)requestKingKongFirstData:(void(^)(void))completion {
    NSString *PositionCode = UserInfo.isXEnjoyVip ? MRKXRecommendResourceFirstCode : MRKRecommendResourceFirstCode;
    [MRKAdvertManager mrkRequestPositionCode:PositionCode
                                   productId:self.equimentTypeId.isNotEmpty ? self.equimentTypeId :@"0"
                    completeBlockWithSuccess:^(MRKAdvertDataModel * _Nullable model) {
        self.resourceFirstDatas = model.adverts.mutableCopy;
        completion();
    }];
}

/// 计划概览
- (void)requestPlanOverViewData:(void(^)(void))completion {
    [MRKAIPlanAPIClient aiplanOverviewSuccess:^(id data) {
        self.planOverViewModel = (MRKPlanOverViewModel *)data;
        completion();
    } failure:^(id data) {
        completion();
    }];
}

///新人专区状态接口
- (void)getNewUserRequest:(void(^)(void))completion  {
    [MRKBaseRequest mrkRequestType:YTKRequestMethodGET
                               url:@"/user/newcomer-zone/v2"
                           andParm:nil
          completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        id data = [request.responseObject valueForKeyPath:@"data.display"];
        self.newUser = [data boolValue];
        completion();
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        completion();
    }];
}

///// 体脂秤数据
//- (void)requestScaleData:(void(^)(void))completion {
//    [MRKBaseRequest mrkGetRequestUrl:@"/equip/record-user-association/targetWeight"
//                             andParm:nil
//            completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
//        id data = [request.responseObject objectForKey:@"data"];
//        self.scaleDataModel = [HomeScaleDataModel modelWithJSON:data];
//        completion();
//    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
//        completion();
//    }];
//}

/// 绝影会员 || !UserInfo.isXEnjoy
- (void)requestXenjoyData:(void(^)(void))completion {
    if (!UserInfo.isXEnjoy) {
        self.xenjoyDatas = @[];
        completion();
        return;
    }
    [MRKCourseApi requestXenjoyCourse:self.equimentTypeId
                           completion:^(NSArray<MRKRecommendCourseModel *> * _Nonnull array) {
        self.xenjoyDatas = array.mutableCopy;
        completion();
    } failure:^(NSError * _Nonnull error) {
        completion();
    }];
}

/// 主题课程 合集
- (void)requestThemeCourseData:(void(^)(void))completion {
    [MRKCourseApi requestHomeThemeCourse:self.equimentTypeId completion:^(MRKThemeModel * _Nonnull model) {
        NSMutableArray *array = model.records.mutableCopy;
        if (model.records.count > 3) {
            array = [model.records subarrayWithRange:NSMakeRange(0, 3)].mutableCopy;
        }
        self.themeCourseList = array;
        completion();
    } failure:^(NSError * _Nonnull error) {
        completion();
    }];
}

/// banner
- (void)requestBannerData:(void(^)(void))completion {
    NSString *code = @"";
    if (![self.equimentTypeId isNotBlank])  {
        code = MRKRecommendBannerCode;
    }else{
        code = [MRKAdvertManager deviceBannerPositionCode:@(self.equimentTypeId.integerValue)];
    }
    [MRKAdvertManager mrkRequestPositionCode:code productId:self.equimentTypeId.isNotEmpty ? self.equimentTypeId :@"0"  completeBlockWithSuccess:^(MRKAdvertDataModel * _Nullable model) {
        [model.adverts enumerateObjectsUsingBlock:^(AdvertModel * obj, NSUInteger idx, BOOL * _Nonnull stop) {
            obj.bannerType = @(BannerPage);
        }];
        self.bannerList = model.adverts.mutableCopy;
        completion();
    }];
}

/// 排行榜
- (void)requestHomeTop:(void(^)(void))completion {
    if (self.deviceModel.isXEnjoyDevice) { ///需求要求绝影设备下隐藏榜单
        self.topCourse = @[];
        completion();
        return;
    }
    
    // 只显示三节课
    [MRKCourseApi topCourseEquimentID:self.equimentTypeId
                              success:^(NSArray<HomeTopCourseModel *> * _Nonnull data) {
        if (data.count > 3) {
            data = [data subarrayWithRange:NSMakeRange(0, 3)].mutableCopy;
        }
        self.topCourse = data;
        completion();
    } failure:^(NSError * _Nonnull error) {
        completion();
    }];
}

///推荐计划
- (void)requestTopPlanData:(void(^)(void))completion {
    [MRKBaseRequest mrkGetRequestUrl:@"/course/coursePlanUserAssociatedController/top-plan"
                             andParm:nil
            completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"requestCoursePlanInfoData ==== %@",request.responseObject);
        id data = [request.responseObject valueForKeyPath:@"data"];
        NSArray *array = [NSArray modelArrayWithClass:[MRKCoursePlanModel class] json:data];
        if (array.count > 4){
            array = [array subarrayWithRange:NSMakeRange(0, 4)];
        }
        self.topPlanArray = array;
        completion();
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        completion();
    }];
}

- (BOOL)pageNoData {
//    return self.topCourse.count == 0 && self.kingkongDatas.count == 0 && self.targerModel == nil;
    return NO;
}

/// 获取用户优惠弹窗
- (void)fetchUserPurchaseDiscount:(void(^)(MRKPurchaseDiscountModel*))completion  {
    [MRKBaseRequest mrkRequestType:YTKRequestMethodGET
                               url:@"/user/member-product/promotion/popup"
                           andParm:nil
          completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        id data = [request.responseObject valueForKeyPath:@"data"];
        MRKPurchaseDiscountModel *model = [MRKPurchaseDiscountModel modelWithJSON:data];
        completion(model);
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        
    }];
}

/// 确认获取用户优惠弹窗
- (void)confirmPurchaseDiscount:(NSInteger)sceneCode {
    [MRKBaseRequest mrkRequestType:YTKRequestMethodPOST
                               url:@"/user/member-product/promotion/popup/confirm"
                           andParm:@{@"sceneCode":@(sceneCode)}
          completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        
    }];
}

/// VIP会员当日到期
- (BOOL)vipTimeJudge:(MemberInfoDataDTO *)model{
    if (model.isAutoRenewal) {
        return NO;
    }
    
    NSString *nowTime = [MRKTimeManager getNowTimeDate];
    NSString *timeskey = [NSString stringWithFormat:@"VipExpireTime_%@", UserInfo.userId];
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    NSString *userDefaultsTime = [userDefaults valueForKey:timeskey];
    if ([nowTime isEqualToString:userDefaultsTime]){
        return NO;
    }
    BOOL canAlert = model.manualPurchase || model.isOpened;///触达人群:手动续费用户和兑换用户
    BOOL todayVip = model.isMember && model.days == 0;///触达人群:手动续费用户和兑换用户
    ///
    NSString *key = [NSString stringWithFormat:@"VipExpireTip_%@", UserInfo.userId];
    BOOL RegulateTip = [userDefaults boolForKey:key];
    if (!RegulateTip && todayVip && canAlert){
        [userDefaults setBool:YES forKey:key];
        [userDefaults setObject:nowTime forKey:timeskey];
        [userDefaults synchronize];
        
        return YES;
    }else{
        [userDefaults removeObjectForKey:key];
        [userDefaults removeObjectForKey:timeskey];
        [userDefaults synchronize];
        
        return NO;
    }
}

/// VIP会员过期一天
- (BOOL)vipExpireJudge:(MemberInfoDataDTO *)model{
    if (model.isAutoRenewal) {
        return NO;
    }
    
    NSString *nowTime = [MRKTimeManager getNowTimeDate];
    NSString *timeskey = [NSString stringWithFormat:@"VipExpireTime1_%@", UserInfo.userId];
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    NSString *userDefaultsTime = [userDefaults valueForKey:timeskey];
    if ([nowTime isEqualToString:userDefaultsTime]){
        return NO;
    }
    
    BOOL canAlert = model.manualPurchase || model.isOpened;///触达人群:手动续费用户和兑换用户
    BOOL noVip = !model.isMember && model.expireDays == -1;///
    ///
    NSString *key = [NSString stringWithFormat:@"VipExpireTip1_%@", UserInfo.userId];
    BOOL RegulateTip = [userDefaults boolForKey:key];
    if (!RegulateTip && noVip && canAlert){
        [userDefaults setBool:YES forKey:key];
        [userDefaults setObject:nowTime forKey:timeskey];
        [userDefaults synchronize];
        
        return YES;
    }else{
        [userDefaults removeObjectForKey:key];
        [userDefaults removeObjectForKey:timeskey];
        [userDefaults synchronize];
        
        return NO;
    }
}


@end


