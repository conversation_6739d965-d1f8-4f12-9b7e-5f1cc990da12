//
//  MrkGameEdgeControlLayer.m
//  Student_IOS
//
//  Created by merit on 2022/8/12.
//

#import "MrkGameEdgeControlLayer.h"

#import "NSAttributedString+SJMake.h"
#import "Masonry.h"
#import "SJBaseVideoPlayer.h"
#import "SJTimerControl.h"

#import "SJVideoPlayerURLAsset+SJControlAdd.h"
#import "SJDraggingProgressPopupView.h"
#import "UIView+SJAnimationAdded.h"
#import "SJVideoPlayerConfigurations.h"
#import "SJProgressSlider.h"
#import "SJLoadingView.h"
#import "SJDraggingObservation.h"
#import "SJScrollingTextMarqueeView.h"
#import "SJFullscreenModeStatusBar.h"
#import "SJSpeedupPlaybackPopupView.h"
#import <objc/message.h>

#import "TrainDataItemView.h"
#import "MRKVideoCoachView.h"
#import "MRKDMListView.h"
#import "MRKVideoStepView.h"
#import "MRKRaceRankView.h"
#import "MRKRenderModel.h"
#import "MRKVideoStepProgressView.h"
#import "MRKGameUserView.h"


#define MRKDanmuViewWidth     185

#pragma mark - Top

@interface MrkGameEdgeControlLayer ()<SJProgressSliderDelegate> {
    CGSize _previousSize;
}
@property (nonatomic, weak, nullable) SJBaseVideoPlayer *videoPlayer;
@property (nonatomic, strong, readonly) SJProgressSlider *bottomProgressIndicator;

// 固定左上角的返回按钮. 设置`fixesBackItem`后显示
@property (nonatomic, strong, readonly) UIButton *fixedBackButton;
@property (nonatomic, strong, readonly) SJEdgeControlButtonItem *backItem;
@property (nonatomic, strong, nullable) id<SJReachabilityObserver> reachabilityObserver;
@property (nonatomic, strong, readonly) SJTimerControl *dateTimerControl API_AVAILABLE(ios(11.0)); // refresh date for custom status bar

@property (nonatomic, assign) BOOL hasToastAlert; ///有无弹过指令窗
@property (nonatomic, strong) MRKDMListView *dmListView; ///弹幕列表 view
@property (nonatomic, strong) MRKVideoStepProgressView *progressView; ///环节进度
@property (nonatomic, strong) MRKRaceRankView *rollView; ///排行榜
@property (nonatomic, assign) BOOL canEditData; ///标识是否完成，要不要刷新数据，
@property (nonatomic, assign) NSInteger raceStatus;
@property (nonatomic, assign) BOOL changeStatus; ///
///
@property (nonatomic, strong) MRKGameUserView *userView;
@property (nonatomic, strong) CourseLinkModel *currentlinkModel;/**播放器当前时间对应的小节model**/
@property (nonatomic, strong) TrainingShowData *tyModel;
@end

@implementation MrkGameEdgeControlLayer
@synthesize restarted = _restarted;
@synthesize draggingProgressPopupView = _draggingProgressPopupView;
@synthesize draggingObserver = _draggingObserver;


- (MRKRaceRankView *)rollView{
    if (!_rollView) {
        _rollView = [[MRKRaceRankView alloc] init];
    }
    return _rollView;
}

- (MRKVideoStepProgressView *)progressView {
    if (!_progressView) {
        _progressView = [[MRKVideoStepProgressView alloc] init];
    }
    return _progressView;
}

- (MRKDMListView *)dmListView {
    if (!_dmListView) {
        _dmListView = [[MRKDMListView alloc] init];
        @weakify(self);
        _dmListView.sendMsgBlock = ^(NSInteger type) {
            @strongify(self);
            if (type == 3) {
                ///点击上弹幕按钮 直接发送当前占位字符
                NSString *holderStr = [self.dmListView getSendViewHolderStr];
                if (self.delegate && [self.delegate respondsToSelector:@selector(keyboardInputWasTappedForControlLayer:sendMsg:)]) {
                    [self.delegate keyboardInputWasTappedForControlLayer:self sendMsg:holderStr];
                }
                return;
            }
            
            if (self.delegate && [self.delegate respondsToSelector:@selector(keyboardInputWasTappedForControlLayer:andType:)]) {
                [self.delegate keyboardInputWasTappedForControlLayer:self andType:type];
            }
        };
    }
    return _dmListView;
}

- (MRKGameUserView *)userView{
    if (!_userView) {
        _userView = [[MRKGameUserView alloc] init];
        _userView.hidden = YES;
    }
    return _userView;
}



- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if ( !self ) return nil;
    _bottomProgressIndicatorHeight = 1;
    _automaticallyPerformRotationOrFitOnScreen = YES;
    
   
    self.rightAdapterLayoutFromTopDirection = YES;
    self.autoAdjustTopSpacing = YES;
    self.hiddenBottomProgressIndicator = YES;
    
    self.rightMargin = 0;
    CGFloat padding = IS_IPHONEX_SURE ? 30 : 10;
    self.rightWidth = MRKDanmuViewWidth +padding;
    
    [self _setupView];
    ///添加超然脂率
    [self addSubview:self.userView];
    [self.userView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.mas_top).offset(50);
        make.left.equalTo(self.mas_left).offset(kScreenPadding);
        make.size.mas_equalTo(CGSizeMake(120, 32));
    }];
    
    return self;
}

- (void)dealloc {
    [NSNotificationCenter.defaultCenter removeObserver:self];
}

///
/// controlLayerDidAppear
///
- (void)controlLayerDidAppear{
    
}

///
/// controlLayerDidDisappear
///
- (void)controlLayerDidDisappear{
    
}

- (void)layoutSubviews {
    [super layoutSubviews];
    
    if ( !CGSizeEqualToSize(_previousSize, self.bounds.size) ) {
        if (@available(iOS 11.0, *)) {
            [self _updateAppearStateForCustomStatusBar];
        }
        [self _updateLayoutForBottomProgressIndicator];
    }
    _previousSize = self.bounds.size;
}






#pragma mark -

///
/// 切换器(player.switcher)重启该控制层
///
- (void)restartControlLayer {
    _restarted = YES;
    sj_view_makeAppear(self.controlView, YES);
    
    [self _showOrHiddenLoadingView];
    [self _updateAppearStateForContainerViews];
    [self _reloadAdaptersIfNeeded];
}

///
/// 控制层退场
///
- (void)exitControlLayer {
    _restarted = NO;
    
    sj_view_makeDisappear(self.controlView, YES, ^{
        if ( !self->_restarted ) [self.controlView removeFromSuperview];
    });
    
    sj_view_makeDisappear(_topContainerView, YES);
    sj_view_makeDisappear(_leftContainerView, YES);
    sj_view_makeDisappear(_bottomContainerView, YES);
    sj_view_makeDisappear(_rightContainerView, YES);
    sj_view_makeDisappear(_draggingProgressPopupView, YES);
    sj_view_makeDisappear(_centerContainerView, YES);
    
    sj_view_makeDisappear(_topDataView, YES);
    sj_view_makeDisappear(_bottomDataView, YES);
}






#pragma mark - item actions

- (void)_fixedBackButtonWasTapped {
    [self.backItem performActions];
}

- (void)_backItemWasTapped {
    if ( [self.delegate respondsToSelector:@selector(backItemWasTappedForControlLayer:)] ) {
        [self.delegate backItemWasTappedForControlLayer:self];
    }
}

- (void)_lockItemWasTapped {
//    self.videoPlayer.lockedScreen = !self.videoPlayer.isLockedScreen;
}

- (void)_playItemWasTapped {
    _videoPlayer.isPaused ? [self.videoPlayer play] : [self.videoPlayer pauseForUser];
}

- (void)_fullItemWasTapped {
    if ( _onlyUsedFitOnScreen ) {
        [_videoPlayer setFitOnScreen:!_videoPlayer.isFitOnScreen];
        return;
    }
    
    if ( _usesFitOnScreenFirst && !_videoPlayer.isFitOnScreen ) {
        [_videoPlayer setFitOnScreen:YES];
        return;
    }
    
    [_videoPlayer rotate];
}

- (void)_replayItemWasTapped {
    [_videoPlayer replay];
}

- (void)_autoItemWasTapped {
    self.videoPlayer.useLessonPlanControl = !self.videoPlayer.useLessonPlanControl;
    
    NSString *tipStr = _videoPlayer.isUseLessonPlanControl ? @"开启AI调节" : @"关闭AI调节";
    [self.videoPlayer.prompt show:[NSAttributedString sj_UIKitText:^(id<SJUIKitTextMakerProtocol>  _Nonnull make) {
        make.append(tipStr);
        make.font([UIFont systemFontOfSize:14 weight:UIFontWeightMedium]);
        make.textColor([UIColor whiteColor]);
    }] duration:3];
}

- (void)_deviceItemWasTapped {
    if ( [self.delegate respondsToSelector:@selector(connectDeviceWasTappedForControlLayer:)] ) {
        [self.delegate connectDeviceWasTappedForControlLayer:self];
    }
}

/// 弹幕按钮点击
- (void)_danmuItemWasTapped {
    self.videoPlayer.closeBarrageControl = !self.videoPlayer.closeBarrageControl;
    // 判断弹幕功能是否开启
    [self.dmListView isHiddenDanmuView:!self.dmListView.hidden];
}

- (void)_tvItemWasTapped {
    if ( [self.delegate respondsToSelector:@selector(linkTVItemWasTappedForControlLayer:)] ) {
        [self.delegate linkTVItemWasTappedForControlLayer:self];
    }
}

- (void)_shareItemWasTapped {
    if ( [self.delegate respondsToSelector:@selector(shareItemWasTappedForControlLayer:)] ) {
        [self.delegate shareItemWasTappedForControlLayer:self];
    }
}











#pragma mark - slider delegate methods

- (void)sliderWillBeginDragging:(SJProgressSlider *)slider {
    if ( _videoPlayer.assetStatus != SJAssetStatusReadyToPlay ) {
        [slider cancelDragging];
        return;
    }
    else if ( _videoPlayer.canSeekToTime && !_videoPlayer.canSeekToTime(_videoPlayer) ) {
        [slider cancelDragging];
        return;
    }
    
    [self _willBeginDragging];
}

- (void)slider:(SJProgressSlider *)slider valueDidChange:(CGFloat)value {
    if ( slider.isDragging ) [self _didMove:value];
}

- (void)sliderDidEndDragging:(SJProgressSlider *)slider {
    [self _endDragging];
}





#pragma mark - player delegate methods

- (UIView *)controlView {
    return self;
}

- (void)installedControlViewToVideoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer {
    _videoPlayer = videoPlayer;
    SJNSLog(@"");

    sj_view_makeDisappear(_topContainerView, NO);
    sj_view_makeDisappear(_leftContainerView, NO);
    sj_view_makeDisappear(_bottomContainerView, NO);
    sj_view_makeDisappear(_rightContainerView, NO);
    sj_view_makeDisappear(_centerContainerView, NO);
    
    sj_view_makeDisappear(_topDataView, NO);
    sj_view_makeDisappear(_bottomDataView, NO);
    
    [self _reloadSizeForBottomTimeLabel];
    [self _updateContentForBottomCurrentTimeItemIfNeeded];
    [self _updateContentForBottomDurationItemIfNeeded];
    
    _reachabilityObserver = [videoPlayer.reachability getObserver];
    __weak typeof(self) _self = self;
    _reachabilityObserver.networkSpeedDidChangeExeBlock = ^(id<SJReachability> r) {
        __strong typeof(_self) self = _self;
        if ( !self ) return;
        [self _updateNetworkSpeedStrForLoadingView];
    };
}

///
/// 当播放器尝试自动隐藏控制层之前 将会调用这个方法
///
- (BOOL)controlLayerOfVideoPlayerCanAutomaticallyDisappear:(__kindof SJBaseVideoPlayer *)videoPlayer {
    return YES;
}




///视图切换
- (void)controlLayerNeedAppear:(__kindof SJBaseVideoPlayer *)videoPlayer {
    if ( videoPlayer.isLockedScreen )
        return;
    
    SJNSLog(@"");
    [self _updateAppearStateForResidentBackButtonIfNeeded];
    [self _updateAppearStateForContainerViews];
    [self _reloadAdaptersIfNeeded];
    
    [self _updateContentForBottomCurrentTimeItemIfNeeded];
    [self _updateAppearStateForBottomProgressIndicatorIfNeeded];
    if (@available(iOS 11.0, *)) {
        [self _reloadCustomStatusBarIfNeeded];
    }
}

- (void)controlLayerNeedDisappear:(__kindof SJBaseVideoPlayer *)videoPlayer {
    if ( videoPlayer.isLockedScreen )
        return;
    
    SJNSLog(@"");
    [self _updateAppearStateForResidentBackButtonIfNeeded];
    [self _updateAppearStateForContainerViews];
    [self _updateAppearStateForBottomProgressIndicatorIfNeeded];
}


- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer prepareToPlay:(SJVideoPlayerURLAsset *)asset {
    SJNSLog(@"");
    
    [self _reloadSizeForBottomTimeLabel];
    [self _updateContentForBottomDurationItemIfNeeded];
    [self _updateContentForBottomCurrentTimeItemIfNeeded];
    
    [self _updateContentForBottomProgressIndicatorIfNeeded];
    [self _updateAppearStateForResidentBackButtonIfNeeded];
    [self _updateAppearStateForBottomProgressIndicatorIfNeeded];
    
    [self _reloadAdaptersIfNeeded];
    [self _showOrHiddenLoadingView];
}

- (void)videoPlayerPlaybackStatusDidChange:(__kindof SJBaseVideoPlayer *)videoPlayer {
    SJNSLog(@"");
    
    [self _reloadAdaptersIfNeeded];
    [self _showOrHiddenLoadingView];
    [self _updateContentForBottomCurrentTimeItemIfNeeded];
    [self _updateContentForBottomDurationItemIfNeeded];
    [self _updateContentForBottomProgressIndicatorIfNeeded];
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer pictureInPictureStatusDidChange:(SJPictureInPictureStatus)status API_AVAILABLE(ios(14.0)) {
    SJNSLog(@"");
    [self _updateContentForPictureInPictureItem];
    [self.topAdapter reload];
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer currentTimeDidChange:(NSTimeInterval)currentTime {
    [self _updateContentForBottomCurrentTimeItemIfNeeded];
    [self _updateContentForBottomProgressIndicatorIfNeeded];
    [self _updateCurrentTimeForDraggingProgressPopupViewIfNeeded];
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer durationDidChange:(NSTimeInterval)duration {
    [self _reloadSizeForBottomTimeLabel];
    [self _updateContentForBottomDurationItemIfNeeded];
    [self _updateContentForBottomProgressIndicatorIfNeeded];
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer playableDurationDidChange:(NSTimeInterval)duration {

}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer playbackTypeDidChange:(SJPlaybackType)playbackType {
    SJNSLog(@"");
    
    switch ( playbackType ) {
        case SJPlaybackTypeLIVE: {
            
            [self.bottomAdapter removeAllItems];
            [self.bottomAdapter reload];
        }
            break;
        case SJPlaybackTypeUnknown:
        case SJPlaybackTypeVOD:
        case SJPlaybackTypeFILE: {
            
        }
            break;
        case SJPlaybackTypeREAL:{
          
            SJEdgeControlButtonItem *modelItem = [_topAdapter itemForTag:SJEdgeControlLayerTopItem_Model];
            modelItem.hidden = YES;
            
            SJEdgeControlButtonItem *danmuItem = [_topAdapter itemForTag:SJEdgeControlLayerTopItem_Danmu];
            danmuItem.hidden = YES;
            
            SJEdgeControlButtonItem *shareItem = [_topAdapter itemForTag:SJEdgeControlLayerTopItem_Share];
            shareItem.hidden = YES;
            
            [self.topAdapter reload];
        }
            break;
    }
  
    [self _showOrRemoveBottomProgressIndicator];
}

- (BOOL)canTriggerRotationOfVideoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer {
    SJNSLog(@"");
    if ( _onlyUsedFitOnScreen )
        return NO;
    if ( _usesFitOnScreenFirst )
        return videoPlayer.isFitOnScreen;
    
    return YES;
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer willRotateView:(BOOL)isFull {
    SJNSLog(@"");
    [self _updateAppearStateForResidentBackButtonIfNeeded];
    [self _updateAppearStateForContainerViews];
    [self _reloadAdaptersIfNeeded];
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer willFitOnScreen:(BOOL)isFitOnScreen {
    SJNSLog(@"");
    [self _updateAppearStateForResidentBackButtonIfNeeded];
    [self _updateAppearStateForContainerViews];
    [self _reloadAdaptersIfNeeded];
}

/// 是否可以触发播放器的手势
- (BOOL)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer gestureRecognizerShouldTrigger:(SJPlayerGestureType)type location:(CGPoint)location {
    SJEdgeControlButtonItemAdapter *adapter = nil;
    BOOL(^_locationInTheView)(UIView *) = ^BOOL(UIView *container) {
        return CGRectContainsPoint(container.frame, location) && !sj_view_isDisappeared(container);
    };
    
    if ( _locationInTheView(_topContainerView) ) {
        adapter = _topAdapter;
    }
    else if ( _locationInTheView(_topDataView) ) {
        adapter = nil;
    }
    else if ( _locationInTheView(_bottomContainerView) ) {
        adapter = _bottomAdapter;
    }
    else if ( _locationInTheView(_leftContainerView) ) {
        adapter = _leftAdapter;
    }
    else if ( _locationInTheView(_rightContainerView) ) {
        adapter = _rightAdapter;
    }
    else if ( _locationInTheView(_centerContainerView) ) {
        adapter = _centerAdapter;
    }
    if ( !adapter ) return YES;
    
    CGPoint point = [self.controlView convertPoint:location toView:adapter.view];
    if ( !CGRectContainsPoint(adapter.view.frame, point) ) return YES;
    
    SJEdgeControlButtonItem *_Nullable item = [adapter itemAtPoint:point];
    return item != nil ? (item.actions.count == 0)  : YES;
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer panGestureTriggeredInTheHorizontalDirection:(SJPanGestureRecognizerState)state progressTime:(NSTimeInterval)progressTime {
//    switch ( state ) {
//        case SJPanGestureRecognizerStateBegan:
//            [self _willBeginDragging];
//            break;
//        case SJPanGestureRecognizerStateChanged:
//            [self _didMove:progressTime];
//            break;
//        case SJPanGestureRecognizerStateEnded:
//            [self _endDragging];
//            break;
//    }
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer longPressGestureStateDidChange:(SJLongPressGestureRecognizerState)state {
    if ( [(id)self.speedupPlaybackPopupView respondsToSelector:@selector(layoutInRect:gestureState:playbackRate:)] ) {
        if ( state == SJLongPressGestureRecognizerStateBegan ) {
            if ( self.speedupPlaybackPopupView.superview != self ) {
                [self insertSubview:self.speedupPlaybackPopupView atIndex:0];
            }
        }
        [self.speedupPlaybackPopupView layoutInRect:self.frame gestureState:state playbackRate:videoPlayer.rate];
    }
    else {
        switch ( state ) {
            case SJLongPressGestureRecognizerStateChanged: break;
            case SJLongPressGestureRecognizerStateBegan: {
                if ( self.speedupPlaybackPopupView.superview != self ) {
                    [self insertSubview:self.speedupPlaybackPopupView atIndex:0];
                    [self.speedupPlaybackPopupView mas_makeConstraints:^(MASConstraintMaker *make) {
                        make.center.equalTo(self.topAdapter);
                    }];
                }
                self.speedupPlaybackPopupView.rate = videoPlayer.rateWhenLongPressGestureTriggered;
                [self.speedupPlaybackPopupView show];
            }
                break;
            case SJLongPressGestureRecognizerStateEnded: {
                [self.speedupPlaybackPopupView hidden];
            }
                break;
        }
    }
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer presentationSizeDidChange:(CGSize)size {
    if ( _automaticallyPerformRotationOrFitOnScreen && !videoPlayer.isFullscreen && !videoPlayer.isFitOnScreen ) {
        _onlyUsedFitOnScreen = size.width < size.height;
    }
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer andCommandModel:(id)model{
    [self sendInstructionsWithData:model];
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer SEIData:(id)model {
    [self sendInstructionsWithData:model];
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer useLessonPlanControl:(BOOL)useLessonPlanControl{
    [self _reloadDataAdapterIfNeeded];
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer closeBarrageControl:(BOOL)closeBarrageControl{
    [self _reloadTopAdapterIfNeeded];
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer receiveDanmu:(id)model{
    if (self.dmListView.hidden) {return;}
    
    if([model isKindOfClass:[NSArray class]]) { // 录播弹幕
    
        for (MRKRenderModel *data in model) {
            data.userId = data.isSelf ? data.userId : @"";
            data.msg_type = 1;
            [self.dmListView receiveMsg:data];
        }
        
    } else if([model isKindOfClass:[NSDictionary class]]) { //直播弹幕
        id data = [model valueForKeyPath:@"bullet_message"];
        
        MRKRenderModel *renderModel = [MRKRenderModel modelWithJSON:data];
        renderModel.isLivingMsg = YES;
        [self.dmListView receiveMsg:renderModel];
    }
}

///下一小节数据
- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer nextCourseLinkPOS:(id)model{
    if (![model isKindOfClass:[CourseLinkModel class]]) {  return; }
    CourseLinkModel *linkModel = (CourseLinkModel *)model;
    self.currentlinkModel = linkModel;
    
    ///刷新当前文本提示窗
    [self  refreshNoteTips];
}


- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer withGameLIVERankData:(id)model{
    [self.rollView rankData:model withType:RaceDataTypeLIVERank];
    
    ///记录排行数据rankmodel
    if ([model isKindOfClass:[MRKRankDataModel class]]) {
        MRKRankDataModel *data = (MRKRankDataModel *)model;
        self.rankModel = data;
    }
}

- (void)videoPlayerRankRequestError:(__kindof SJBaseVideoPlayer *)videoPlayer type:(NSInteger)type{
    [self.rollView rankDataRequestError];
}


- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer fatBurningModel:(id)model{
  
    
}


///设备数据
- (void)videoPlayerBluetooth:(__kindof SJBaseVideoPlayer *)videoPlayer andTymodel:(id)model;
{
    ///设备蓝牙数据处理
    [self dataAdapterUpdateWithModel:model];
}
///心率数据
- (void)videoPlayerBluetooth:(__kindof SJBaseVideoPlayer *)videoPlayer andRdmodel:(id)model{
    
}
///设备连接状态
- (void)videoPlayerBluetooth:(__kindof SJBaseVideoPlayer *)videoPlayer connectDevice:(DEVICE_CONNECT_STATUS)status{
    
    [self _updateConnectForTopDeviceItemIfNeeded];
    [self _reloadDataAdapterIfNeeded];
}

///蓝牙断连/**设备断连弹窗*/
- (void)videoPlayerDisconnectBluetooth:(__kindof SJBaseVideoPlayer *)videoPlayer{

}








/// 这是一个只有在播放器锁屏状态下, 才会回调的方法
/// 当播放器锁屏后, 用户每次点击都会回调这个方法
- (void)tappedPlayerOnTheLockedState:(__kindof SJBaseVideoPlayer *)videoPlayer {
//    if ( sj_view_isDisappeared(_leftContainerView) ) {
//        sj_view_makeAppear(_leftContainerView, YES);
//        [self.lockStateTappedTimerControl start];
//    }
//    else {
//        sj_view_makeDisappear(_leftContainerView, YES);
//        [self.lockStateTappedTimerControl clear];
//    }
}

- (void)lockedVideoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer {
//    [self _updateAppearStateForResidentBackButtonIfNeeded];
//    [self _updateAppearStateForBottomProgressIndicatorIfNeeded];
//    [self _updateAppearStateForContainerViews];
//    [self _reloadAdaptersIfNeeded];
//    [self.lockStateTappedTimerControl start];
}

- (void)unlockedVideoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer {
//    [self _updateAppearStateForBottomProgressIndicatorIfNeeded];
//    [self.lockStateTappedTimerControl clear];
//    [videoPlayer controlLayerNeedAppear];
}

- (void)videoPlayer:(SJBaseVideoPlayer *)videoPlayer reachabilityChanged:(SJNetworkStatus)status {
    if (@available(iOS 11.0, *)) {
        [self _reloadCustomStatusBarIfNeeded];
    }
    if ( _disabledPromptWhenNetworkStatusChanges ) return;
    if ( [self.videoPlayer.URLAsset.mediaURL isFileURL] ) return; // return when is local video.
    
    switch ( status ) {
        case SJNetworkStatus_NotReachable: {
            
            ///无网络提示
            [_videoPlayer.prompt show:[NSAttributedString sj_UIKitText:^(id<SJUIKitTextMakerProtocol>  _Nonnull make) {
                make.append(SJVideoPlayerConfigurations.shared.localizedStrings.unstableNetworkPrompt);
                make.font([UIFont systemFontOfSize:14 weight:UIFontWeightMedium]);
                make.textColor(UIColor.whiteColor);
            }] duration:3];
        }
            break;
        case SJNetworkStatus_ReachableViaWWAN: {
            
            ///4G流量提示
            [_videoPlayer.prompt show:[NSAttributedString sj_UIKitText:^(id<SJUIKitTextMakerProtocol>  _Nonnull make) {
                make.append(SJVideoPlayerConfigurations.shared.localizedStrings.cellularNetworkPrompt);
                make.font([UIFont systemFontOfSize:14 weight:UIFontWeightMedium]);
                make.textColor(UIColor.whiteColor);
            }] duration:3];
        }
            break;
        case SJNetworkStatus_ReachableViaWiFi: {}
            break;
    }
}

#pragma mark -

- (NSString *)stringForSeconds:(NSInteger)secs {
    return _videoPlayer ? [_videoPlayer stringForSeconds:secs] : @"";
}

#pragma mark -

- (void)setFixesBackItem:(BOOL)fixesBackItem {
    if ( fixesBackItem == _fixesBackItem )
        return;
    _fixesBackItem = fixesBackItem;
    dispatch_async(dispatch_get_main_queue(), ^{
        if ( self->_fixesBackItem ) {
            [self.controlView addSubview:self.fixedBackButton];
            [self->_fixedBackButton mas_makeConstraints:^(MASConstraintMaker *make) {
                make.top.left.bottom.equalTo(self.topAdapter.view);
                make.width.equalTo(self.topAdapter.view.mas_height);
            }];
            
            [self _updateAppearStateForResidentBackButtonIfNeeded];
            [self _reloadTopAdapterIfNeeded];
        }
        else {
            if ( self->_fixedBackButton ) {
                [self->_fixedBackButton removeFromSuperview];
                self->_fixedBackButton = nil;
                
                // back item
                [self _reloadTopAdapterIfNeeded];
            }
        }
    });
}

- (void)setHiddenBottomProgressIndicator:(BOOL)hiddenBottomProgressIndicator {
    if ( hiddenBottomProgressIndicator != _hiddenBottomProgressIndicator ) {
        _hiddenBottomProgressIndicator = hiddenBottomProgressIndicator;
        dispatch_async(dispatch_get_main_queue(), ^{
            [self _showOrRemoveBottomProgressIndicator];
        });
    }
}

- (void)setBottomProgressIndicatorHeight:(CGFloat)bottomProgressIndicatorHeight {
    if ( bottomProgressIndicatorHeight != _bottomProgressIndicatorHeight ) {
        _bottomProgressIndicatorHeight = bottomProgressIndicatorHeight;
        dispatch_async(dispatch_get_main_queue(), ^{
            [self _updateLayoutForBottomProgressIndicator];
        });
    }
}

- (void)setLoadingView:(nullable UIView<SJLoadingView> *)loadingView {
    if ( loadingView != _loadingView ) {
        [_loadingView removeFromSuperview];
        _loadingView = loadingView;
        if ( loadingView != nil ) {
            [self.controlView addSubview:loadingView];
            [loadingView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.center.offset(0);
            }];
        }
    }
}

- (void)setDraggingProgressPopupView:(nullable __kindof UIView<SJDraggingProgressPopupView> *)draggingProgressPopupView {
    _draggingProgressPopupView = draggingProgressPopupView;
    [self _updateForDraggingProgressPopupView];
}

- (void)setTitleView:(nullable __kindof UIView<SJScrollingTextMarqueeView> *)titleView {
    _titleView = titleView;
    [self _reloadTopAdapterIfNeeded];
}

- (void)setCustomStatusBar:(UIView<SJFullscreenModeStatusBar> *)customStatusBar NS_AVAILABLE_IOS(11.0) {
    if ( customStatusBar != _customStatusBar ) {
        [_customStatusBar removeFromSuperview];
        _customStatusBar = customStatusBar;
        [self _reloadCustomStatusBarIfNeeded];
    }
}

- (void)setShouldShowCustomStatusBar:(BOOL (^)(MrkGameEdgeControlLayer * _Nonnull))shouldShowCustomStatusBar NS_AVAILABLE_IOS(11.0) {
    _shouldShowCustomStatusBar = shouldShowCustomStatusBar;
    [self _updateAppearStateForCustomStatusBar];
}

- (void)setspeedupPlaybackPopupView:(UIView<SJSpeedupPlaybackPopupView> *)speedupPlaybackPopupView {
    if ( _speedupPlaybackPopupView != speedupPlaybackPopupView ) {
        [_speedupPlaybackPopupView removeFromSuperview];
        _speedupPlaybackPopupView = speedupPlaybackPopupView;
    }
}

- (void)setOnlyUsedFitOnScreen:(BOOL)onlyUsedFitOnScreen {
    if ( onlyUsedFitOnScreen != _onlyUsedFitOnScreen ) {
        _onlyUsedFitOnScreen = onlyUsedFitOnScreen;
        if ( _onlyUsedFitOnScreen ) {
            _automaticallyPerformRotationOrFitOnScreen = NO;
        }
    }
}

#pragma mark - setup view

- (void)_setupView {
    [self _addItemsToTopAdapter];
    [self _addItemsToLeftAdapter];
    [self _addItemsToBottomAdapter];
    [self _addItemsToRightAdapter];
    [self _addItemsToCenterAdapter];
    
    [self _addItemsToDataAdapter];   ///添加进度展示层
    [self _addItemsBottomDataAdapter];   ///添加数据展示层
    
    
    self.topContainerView.sjv_disappearDirection = SJViewDisappearAnimation_Top;
    self.leftContainerView.sjv_disappearDirection = SJViewDisappearAnimation_Left;
    self.bottomContainerView.sjv_disappearDirection = SJViewDisappearAnimation_Bottom;
    self.rightContainerView.sjv_disappearDirection = SJViewDisappearAnimation_Right;
    self.centerContainerView.sjv_disappearDirection = SJViewDisappearAnimation_None;
    
    
    self.topDataView.sjv_disappearDirection = SJViewDisappearAnimation_Top;
    self.bottomDataView.sjv_disappearDirection = SJViewDisappearAnimation_Bottom;
    
    sj_view_initializes(@[self.topContainerView,
                          self.leftContainerView,
                          self.bottomContainerView,
                          self.rightContainerView,
                          self.topDataView,
                          self.bottomDataView
                        ]);
    
    [NSNotificationCenter.defaultCenter addObserver:self
                                           selector:@selector(_resetControlLayerAppearIntervalForItemIfNeeded:)
                                               name:SJEdgeControlButtonItemPerformedActionNotification
                                             object:nil];
}

@synthesize fixedBackButton = _fixedBackButton;
- (UIButton *)fixedBackButton {
    if ( _fixedBackButton ) return _fixedBackButton;
    _fixedBackButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [_fixedBackButton setImage:SJVideoPlayerConfigurations.shared.resources.backImage forState:UIControlStateNormal];
    [_fixedBackButton addTarget:self action:@selector(_fixedBackButtonWasTapped) forControlEvents:UIControlEventTouchUpInside];
    return _fixedBackButton;
}

@synthesize bottomProgressIndicator = _bottomProgressIndicator;
- (SJProgressSlider *)bottomProgressIndicator {
    if ( _bottomProgressIndicator ) return _bottomProgressIndicator;
    _bottomProgressIndicator = [SJProgressSlider new];
    _bottomProgressIndicator.pan.enabled = NO;
    _bottomProgressIndicator.trackHeight = _bottomProgressIndicatorHeight;
    _bottomProgressIndicator.round = NO;
    id<SJVideoPlayerControlLayerResources> sources = SJVideoPlayerConfigurations.shared.resources;
    UIColor *traceColor = sources.bottomIndicatorTraceColor ?: sources.progressTraceColor;
    UIColor *trackColor = sources.bottomIndicatorTrackColor ?: sources.progressTrackColor;
    _bottomProgressIndicator.traceImageView.backgroundColor = traceColor;
    _bottomProgressIndicator.trackImageView.backgroundColor = trackColor;
    return _bottomProgressIndicator;
}

@synthesize loadingView = _loadingView;
- (UIView<SJLoadingView> *)loadingView {
    if ( _loadingView == nil ) {
        [self setLoadingView:[SJLoadingView.alloc initWithFrame:CGRectZero]];
    }
    return _loadingView;
}

- (__kindof UIView<SJDraggingProgressPopupView> *)draggingProgressPopupView {
    if ( _draggingProgressPopupView == nil ) {
        [self setDraggingProgressPopupView:[SJDraggingProgressPopupView.alloc initWithFrame:CGRectZero]];
    }
    return _draggingProgressPopupView;
}

- (id<SJDraggingObservation>)draggingObserver {
    if ( _draggingObserver == nil ) {
        _draggingObserver = [SJDraggingObservation new];
    }
    return _draggingObserver;
}

@synthesize titleView = _titleView;
- (UIView<SJScrollingTextMarqueeView> *)titleView {
    if ( _titleView == nil ) {
        [self setTitleView:[SJScrollingTextMarqueeView.alloc initWithFrame:CGRectZero]];
    }
    return _titleView;
}

@synthesize speedupPlaybackPopupView = _speedupPlaybackPopupView;
- (UIView<SJSpeedupPlaybackPopupView> *)speedupPlaybackPopupView {
    if ( _speedupPlaybackPopupView == nil ) {
        _speedupPlaybackPopupView = [SJSpeedupPlaybackPopupView.alloc initWithFrame:CGRectZero];
    }
    return _speedupPlaybackPopupView;
}

@synthesize customStatusBar = _customStatusBar;
- (UIView<SJFullscreenModeStatusBar> *)customStatusBar {
    if ( _customStatusBar == nil ) {
        [self setCustomStatusBar:[SJFullscreenModeStatusBar.alloc initWithFrame:CGRectZero]];
    }
    return _customStatusBar;
}

@synthesize shouldShowCustomStatusBar = _shouldShowCustomStatusBar;
- (BOOL (^)(MrkGameEdgeControlLayer * _Nonnull))shouldShowCustomStatusBar {
    if ( _shouldShowCustomStatusBar == nil ) {
        BOOL is_iPhoneX = _screen.is_iPhoneX;
        [self setShouldShowCustomStatusBar:^BOOL(MrkGameEdgeControlLayer * _Nonnull controlLayer) {
            if ( controlLayer.videoPlayer.isFitOnScreen ) return NO;
            
            BOOL isFullscreen = controlLayer.videoPlayer.isFullscreen;
            if ( isFullscreen == NO ) {
                CGRect bounds = UIScreen.mainScreen.bounds;
                if ( bounds.size.width > bounds.size.height )
                    isFullscreen = CGRectEqualToRect(controlLayer.bounds, bounds);
            }
            
            BOOL shouldShow = NO;
            if ( isFullscreen ) {
                ///
                /// 13 以后, 全屏后显示自定义状态栏
                ///
                if ( @available(iOS 13.0, *) ) {
                    shouldShow = YES;
                }
                ///
                /// 11 仅 iPhone X 显示自定义状态栏
                ///
                else if ( @available(iOS 11.0, *) ) {
                    shouldShow = is_iPhoneX;
                }
            }
            return shouldShow;
        }];
    }
    return _shouldShowCustomStatusBar;
}

@synthesize dateTimerControl = _dateTimerControl;
- (SJTimerControl *)dateTimerControl {
    if ( _dateTimerControl == nil ) {
        _dateTimerControl = SJTimerControl.alloc.init;
        _dateTimerControl.interval = 1;
        __weak typeof(self) _self = self;
        _dateTimerControl.exeBlock = ^(SJTimerControl * _Nonnull control) {
            __strong typeof(_self) self = _self;
            if ( !self ) return;
            self.customStatusBar.isHidden ? [control interrupt] : [self _reloadCustomStatusBarIfNeeded];
        };
    }
    return _dateTimerControl;
}



- (void)_addItemsToTopAdapter {
    SJEdgeControlButtonItem *backItem = [SJEdgeControlButtonItem placeholderWithType:SJButtonItemPlaceholderType_49x49 tag:SJEdgeControlLayerTopItem_Back];
    backItem.resetsAppearIntervalWhenPerformingItemAction = NO;
    [backItem addAction:[SJEdgeControlButtonItemAction actionWithTarget:self action:@selector(_backItemWasTapped)]];
    [self.topAdapter addItem:backItem];
    _backItem = backItem;

    SJEdgeControlButtonItem *titleItem = [SJEdgeControlButtonItem placeholderWithType:SJButtonItemPlaceholderType_49xFill tag:SJEdgeControlLayerTopItem_Title];
    [self.topAdapter addItem:titleItem];
    
    MRKVideoCoachView *view = [[MRKVideoCoachView alloc] initWithFrame:CGRectMake(0, 0, 130, 49)];
    SJEdgeControlButtonItem *coachitem = [[SJEdgeControlButtonItem alloc] initWithCustomView:view tag:SJEdgeControlLayerTopItem_Model];
    [self.topAdapter addItem:coachitem];
    
    SJEdgeControlButtonItem *deviceItem = [SJEdgeControlButtonItem placeholderWithType:SJButtonItemPlaceholderType_49x49 tag:SJEdgeControlLayerTopItem_Device];
    [deviceItem addAction:[SJEdgeControlButtonItemAction actionWithTarget:self action:@selector(_deviceItemWasTapped)]];
    [self.topAdapter addItem:deviceItem];
    
    SJEdgeControlButtonItem *danmuItem = [SJEdgeControlButtonItem placeholderWithType:SJButtonItemPlaceholderType_49x49 tag:SJEdgeControlLayerTopItem_Danmu];
    [danmuItem addAction:[SJEdgeControlButtonItemAction actionWithTarget:self action:@selector(_danmuItemWasTapped)]];
    [self.topAdapter addItem:danmuItem];
    
    SJEdgeControlButtonItem *tvItem = [SJEdgeControlButtonItem placeholderWithType:SJButtonItemPlaceholderType_49x49 tag:SJEdgeControlLayerTopItem_TV];
    [tvItem addAction:[SJEdgeControlButtonItemAction actionWithTarget:self action:@selector(_tvItemWasTapped)]];
    [self.topAdapter addItem:tvItem];
    
    SJEdgeControlButtonItem *shareItem = [SJEdgeControlButtonItem placeholderWithType:SJButtonItemPlaceholderType_49x49 tag:SJEdgeControlLayerTopItem_Share];
    [shareItem addAction:[SJEdgeControlButtonItemAction actionWithTarget:self action:@selector(_shareItemWasTapped)]];
    [self.topAdapter addItem:shareItem];
    
    [self.topAdapter reload];
}

- (void)_addItemsToLeftAdapter {
    self.rollView.frame = CGRectMake(0, 0, 220, DHPX(230));
    SJEdgeControlButtonItem *rollitem = [[SJEdgeControlButtonItem alloc] initWithCustomView:self.rollView tag:SJEdgeControlLayerLeftItem_Rank];
    [self.leftAdapter addItem:rollitem];
    
    [self.leftAdapter reload];
}

- (void)_addItemsToBottomAdapter {

    SJEdgeControlButtonItem *titleItem = [SJEdgeControlButtonItem placeholderWithType:SJButtonItemPlaceholderType_49xFill tag:SJEdgeControlLayerBottomItem_Holder];
    [self.bottomAdapter addItem:titleItem];
    
    // 当前时间
    SJEdgeControlButtonItem *currentTimeItem = [SJEdgeControlButtonItem placeholderWithSize:8 tag:SJEdgeControlLayerBottomItem_CurrentTime];
    [self.bottomAdapter addItem:currentTimeItem];
    
    // 时间分隔符
    SJEdgeControlButtonItem *separatorItem = [[SJEdgeControlButtonItem alloc] initWithTitle:[NSAttributedString sj_UIKitText:^(id<SJUIKitTextMakerProtocol>  _Nonnull make) {
        make.append(@"/");
        make.font([UIFont fontWithName:fontNamePing size:16]);
        make.textColor([UIColor whiteColor]);
        make.alignment(NSTextAlignmentCenter);
    }] target:nil action:NULL tag:SJEdgeControlLayerBottomItem_Separator];
    [self.bottomAdapter addItem:separatorItem];
 
    // 全部时长
    SJEdgeControlButtonItem *durationTimeItem = [SJEdgeControlButtonItem placeholderWithSize:8 tag:SJEdgeControlLayerBottomItem_DurationTime];
    [self.bottomAdapter addItem:durationTimeItem];
    
    // 播放按钮
    SJEdgeControlButtonItem *playItem = [SJEdgeControlButtonItem placeholderWithType:SJButtonItemPlaceholderType_49x49 tag:SJEdgeControlLayerBottomItem_Play];
    [playItem addAction:[SJEdgeControlButtonItemAction actionWithTarget:self action:@selector(_playItemWasTapped)]];
    [self.bottomAdapter addItem:playItem];

    [self.bottomAdapter reload];
}

- (void)_addItemsToDataAdapter{
    [self.topDataView addSubview:self.progressView];
    [self.progressView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_offset(UIEdgeInsetsZero);
    }];
}

- (void)_addItemsBottomDataAdapter {
    SJEdgeControlButtonItem *item1 = [SJEdgeControlButtonItem placeholderWithSize:DHPX(90) tag:SJEdgeControlLayerCenterItem_TrainTime];
    item1.dataStr = @"00.00";
    item1.holdStr = @"比赛时长";
    item1.showRice = YES;
    [self.dataAdapter addItem:item1];
    
    SJEdgeControlButtonItem *item2 = [SJEdgeControlButtonItem placeholderWithSize:DHPX(90) tag:SJEdgeControlLayerCenterItem_Kcal];
    item2.dataStr = @"--";
    item2.holdStr = @"消耗 (千卡)";
    [self.dataAdapter addItem:item2];
    
    SJEdgeControlButtonItem *item3 = [SJEdgeControlButtonItem placeholderWithSize:DHPX(90) tag:SJEdgeControlLayerCenterItem_RateOfStroke];
    item3.dataStr = @"--";
    item3.holdStr = @"桨频 (spm)";
    [self.dataAdapter addItem:item3];
    
    SJEdgeControlButtonItem *item4 = [SJEdgeControlButtonItem placeholderWithSize:DHPX(90) tag:SJEdgeControlLayerCenterItem_Resistance];
    item4.dataStr = @"--";
    item4.holdStr = @"阻力 (lv)";
    [self.dataAdapter addItem:item4];
    
    SJEdgeControlButtonItem *item5 = [SJEdgeControlButtonItem placeholderWithSize:DHPX(90) tag:SJEdgeControlLayerCenterItem_Distance];
    item5.dataStr = @"--";
    item5.holdStr = @"距离 (公里)";
    [self.dataAdapter addItem:item5];
    
    [self.dataAdapter reload];
}


- (void)_addItemsToRightAdapter {
    CGFloat padding = IS_IPHONEX_SURE ? 30 : 10;
    
    UIView *v = [[UIView alloc] initWithFrame:CGRectMake(0, 0, MRKDanmuViewWidth+padding, 50)];
    SJEdgeControlButtonItem *heartItem = [[SJEdgeControlButtonItem alloc] initWithCustomView:v tag:12001];
    [self.rightAdapter addItem:heartItem];
    
    CGFloat height = kScreenWidth - MAX(DHPX(45), 65) - 100 - 50;
    self.dmListView.frame = CGRectMake(0, 0, MRKDanmuViewWidth+padding, height);
    SJEdgeControlButtonItem *coachitem = [[SJEdgeControlButtonItem alloc] initWithCustomView:self.dmListView tag:12000];
    [self.rightAdapter addItem:coachitem];
    
    [self.rightAdapter reload];
}

- (void)_addItemsToCenterAdapter {
    ///重播按钮
    UILabel *replayLabel = [UILabel new];
    replayLabel.numberOfLines = 0;
    SJEdgeControlButtonItem *replayItem = [SJEdgeControlButtonItem frameLayoutWithCustomView:replayLabel tag:SJEdgeControlLayerCenterItem_Replay];
    [replayItem addAction:[SJEdgeControlButtonItemAction actionWithTarget:self action:@selector(_replayItemWasTapped)]];
    [self.centerAdapter addItem:replayItem];
    
    // 播放按钮
    SJEdgeControlButtonItem *playItem = [SJEdgeControlButtonItem placeholderWithType:SJButtonItemPlaceholderType_49x49 tag:SJEdgeControlLayerBottomItem_Play];
    [playItem addAction:[SJEdgeControlButtonItemAction actionWithTarget:self action:@selector(_playItemWasTapped)]];
    [self.centerAdapter addItem:playItem];
    
    [self.centerAdapter reload];
}


- (void)dataAdapterUpdateWithModel:(TrainingShowData *)model{
    @weakify(self);
    dispatch_async(dispatch_get_main_queue(), ^{
        @strongify(self);
        TrainingShowData *m = model;
        self.tyModel = model;
        if ( self.canEditData ) {
            ///桨频
            SJEdgeControlButtonItem *item = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_RateOfStroke];
            item.dataStr = m.spm;
            [self.dataAdapter updateContentForItemWithTag:SJEdgeControlLayerCenterItem_RateOfStroke];
            
            ///阻力
            SJEdgeControlButtonItem *item1 = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_Resistance];
            item1.dataStr = m.resistance;
            [self.dataAdapter updateContentForItemWithTag:SJEdgeControlLayerCenterItem_Resistance];
        } else {
            ///桨频
            SJEdgeControlButtonItem *item = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_RateOfStroke];
            item.dataStr = @"--";
            [self.dataAdapter updateContentForItemWithTag:SJEdgeControlLayerCenterItem_RateOfStroke];
            
            ///阻力
            SJEdgeControlButtonItem *item1 = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_Resistance];
            item1.dataStr = @"--";
            [self.dataAdapter updateContentForItemWithTag:SJEdgeControlLayerCenterItem_Resistance];
        }
    });
}



            
///
/// 数据交互栏处理数据
///
- (void)gameDataAdapterUpdateWithModel:(MRKRaceDataModel *)model{
    
    self.dataModel = model;
    self.raceStatus = model.raceStatus;
    
    if (model.raceStatus == 10) {
        ///如果比赛完成后禁掉数据
        if (model.isFinish) {
            self.canEditData = NO;
        } else {
            if (!self.canEditData) {
                self.canEditData = YES;
            }
        }
        
        self.userView.hidden = YES;
        
        ///比赛用时
        SJEdgeControlButtonItem *item2 = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_TrainTime];
        NSString *currentTimeStr = [MRKToolKit MSTimeStrFromSecond:(int)model.raceTime];
        item2.dataStr = currentTimeStr;
        [self.dataAdapter updateContentForItemWithTag:SJEdgeControlLayerCenterItem_TrainTime];
        
        ///消耗
        SJEdgeControlButtonItem *item3 = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_Kcal];
        item3.dataStr = model.kcal ? [NSString convertDecimalNumber:model.kcal num:1] : @"--";
        // model.kcal ? [NSString stringWithFormat:@"%.1f", model.kcal.floatValue] : @"--";
        [self.dataAdapter updateContentForItemWithTag:SJEdgeControlLayerCenterItem_Kcal];
        
        ///距离
        SJEdgeControlButtonItem *item4 = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_Distance];
        item4.dataStr = [NSString convertDecimalNumber:[NSString stringWithFormat:@"%ld",(long)model.distance] num:2];
        // [NSString stringWithFormat: @"%.2f", model.distance/1000.0];
        [self.dataAdapter updateContentForItemWithTag:SJEdgeControlLayerCenterItem_Distance];
        
        self.progressView.statusStr = @"比赛中...";
        self.progressView.distance = model.distance;
        self.progressView.userTime = model.userRaceTime;
        
        [self changeRaceLayerStatus];
        
    } else if (model.raceStatus == 20 ){
        
        self.userView.hidden = YES;
        
        if (self.canEditData) {
            self.canEditData = NO;
        }
        
        [self changeRaceLayerStatus];
        
        self.progressView.statusStr = @"比赛结束";
        self.progressView.distance = model.distance;
        self.progressView.userTime = model.userRaceTime;
        
        ///比赛用时
        SJEdgeControlButtonItem *item2 = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_TrainTime];
        NSString *currentTimeStr = [MRKToolKit MSTimeStrFromSecond:(int)model.raceTime];
        item2.dataStr = currentTimeStr;
        [self.dataAdapter updateContentForItemWithTag:SJEdgeControlLayerCenterItem_TrainTime];
        
        ///消耗
        SJEdgeControlButtonItem *item3 = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_Kcal];
        item3.dataStr = model.kcal ? [NSString convertDecimalNumber:model.kcal num:1] : @"--";
        //model.kcal ? [NSString stringWithFormat:@"%.1f", model.kcal.floatValue] : @"--";
        [self.dataAdapter updateContentForItemWithTag:SJEdgeControlLayerCenterItem_Kcal];
        
        ///距离
        SJEdgeControlButtonItem *item4 = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_Distance];
        item4.dataStr = [NSString convertDecimalNumber:[NSString stringWithFormat:@"%ld",(long)model.distance] num:2];
        // [NSString stringWithFormat: @"%.2f", model.distance/1000.0];
        [self.dataAdapter updateContentForItemWithTag:SJEdgeControlLayerCenterItem_Distance];
        
    } else {
        
        self.userView.hidden = NO;
        
        if (self.canEditData) {
            self.canEditData = NO;
        }
        
    }
}






- (void)changeRaceLayerStatus{
    if (!self.changeStatus && self.raceStatus != 0) {
        self.changeStatus = YES;
        ///如果是数据展示状态
        if (!_videoPlayer.isControlLayerAppeared) {
            if (self.dataModel.raceStatus == 20 && self.dataModel.userRaceTime <= 0)  {
                sj_view_makeDisappear(_topDataView, YES);
            } else {
                sj_view_makeAppear(_topDataView, YES);
            }
            sj_view_makeAppear(_leftContainerView, YES);
        } else {
            [self _updateAppearStateForContainerViews];
        }
    }
}




#pragma mark - appear state

- (void)_updateAppearStateForContainerViews {
    [self _updateAppearStateForTopContainerView];
   
    [self _updateAppearStateForBottomContainerView];
    [self _updateAppearStateForRightContainerView];
    [self _updateAppearStateForCenterContainerView];
    [self _updateAppearStateForBottomDataContainerView];
    
    [self _updateAppearStateForLeftContainerView];
    [self _updateAppearStateForTopDataContainerView];
    
    if (@available(iOS 11.0, *)) {
        [self _updateAppearStateForCustomStatusBar];
    }
}

- (void)_updateAppearStateForTopContainerView {
    if ( 0 == _topAdapter.numberOfItems ) {
        sj_view_makeDisappear(_topContainerView, YES);
        return;
    }
    
    /// 锁屏状态下, 使隐藏
    if ( _videoPlayer.isLockedScreen ) {
        sj_view_makeDisappear(_topContainerView, YES);
        return;
    }
    
    /// 是否显示
    if ( _videoPlayer.isControlLayerAppeared ) {
        sj_view_makeAppear(_topContainerView, YES);
    } else {
        sj_view_makeDisappear(_topContainerView, YES);
    }
}


- (void)_updateAppearStateForTopDataContainerView {
    /// 是否显示
    if ( _videoPlayer.isControlLayerAppeared || self.raceStatus == 0) {
        sj_view_makeDisappear(_topDataView, YES);
    } else {
        
        if (!self.dataModel) {
            sj_view_makeAppear(_topDataView, YES);
        } else {
            if (self.dataModel.raceStatus == 20 && self.dataModel.userRaceTime <= 0)  {
                sj_view_makeDisappear(_topDataView, YES);
            } else {
                sj_view_makeAppear(_topDataView, YES);
            }
        }
    }
}

- (void)_updateAppearStateForLeftContainerView {
    if ( 0 == _leftAdapter.numberOfItems ) {
        sj_view_makeDisappear(_leftContainerView, YES);
        return;
    }
    
    if (self.raceStatus == 0) {
        sj_view_makeDisappear(_leftContainerView, YES);
    } else {
        /// 是否显示
        sj_view_makeAppear(_leftContainerView, YES);
    }
}

/// 更新显示状态
- (void)_updateAppearStateForBottomContainerView {
    if ( 0 == _bottomAdapter.numberOfItems ) {
        sj_view_makeDisappear(_bottomContainerView, YES);
        return;
    }
    
    /// 是否显示
    if ( _videoPlayer.isControlLayerAppeared ) {
        sj_view_makeAppear(_bottomContainerView, YES);
    } else {
        sj_view_makeDisappear(_bottomContainerView, YES);
    }
}


- (void)_updateAppearStateForBottomDataContainerView {
    if ( 0 == _dataAdapter.numberOfItems ) {
        sj_view_makeDisappear(_bottomDataView, YES);
        return;
    }
    
    /// 是否显示
    if ( _videoPlayer.isControlLayerAppeared ) {
        sj_view_makeDisappear(_bottomDataView, YES);
    } else {
        sj_view_makeAppear(_bottomDataView, YES);
    }
}

/// 更新显示状态
- (void)_updateAppearStateForRightContainerView {
    if ( 0 == _rightAdapter.numberOfItems ) {
        sj_view_makeDisappear(_rightContainerView, YES);
        return;
    }
    
    /// 锁屏状态下, 使隐藏
    if ( _videoPlayer.isLockedScreen ) {
        sj_view_makeDisappear(_rightContainerView, YES);
        return;
    }
    
    /// 是否显示
    sj_view_makeAppear(_rightContainerView, YES);
}

- (void)_updateAppearStateForCenterContainerView {
    if ( 0 == _centerAdapter.numberOfItems ) {
        sj_view_makeDisappear(_centerContainerView, YES);
        return;
    }
    
    sj_view_makeAppear(_centerContainerView, YES);
}

- (void)_updateAppearStateForBottomProgressIndicatorIfNeeded {
    if ( _bottomProgressIndicator == nil )
        return;
    
    _videoPlayer.isControlLayerAppeared && !_videoPlayer.isLockedScreen ?
            sj_view_makeDisappear(_bottomProgressIndicator, YES) :
            sj_view_makeAppear(_bottomProgressIndicator, YES);
}

- (void)_updateAppearStateForCustomStatusBar NS_AVAILABLE_IOS(11.0) {
    BOOL shouldShow = self.shouldShowCustomStatusBar(self);
    if ( shouldShow ) {
        if ( self.customStatusBar.superview == nil ) {
            static dispatch_once_t onceToken;
            dispatch_once(&onceToken, ^{
                UIDevice.currentDevice.batteryMonitoringEnabled = YES;
            });
            
            [NSNotificationCenter.defaultCenter addObserver:self selector:@selector(_reloadCustomStatusBarIfNeeded) name:UIDeviceBatteryLevelDidChangeNotification object:nil];
            [NSNotificationCenter.defaultCenter addObserver:self selector:@selector(_reloadCustomStatusBarIfNeeded) name:UIDeviceBatteryStateDidChangeNotification object:nil];
            
            [self.topContainerView addSubview:self.customStatusBar];
            [self.customStatusBar mas_makeConstraints:^(MASConstraintMaker *make) {
                make.top.offset(0);
                make.left.right.equalTo(self.topAdapter);
                make.height.offset(20);
            }];
        }
    }
    
    _customStatusBar.hidden = !shouldShow;
    _customStatusBar.isHidden ? [self.dateTimerControl interrupt] : [self.dateTimerControl resume];
}


/// 暂不实现
- (void)_updateContentForPictureInPictureItem API_AVAILABLE(ios(14.0)) {
    
    
}

#pragma mark - update items

- (void)_reloadAdaptersIfNeeded {
    [self _reloadTopAdapterIfNeeded];
    [self _reloadLeftAdapterIfNeeded];
    [self _reloadBottomAdapterIfNeeded];
    [self _reloadRightAdapterIfNeeded];
    [self _reloadCenterAdapterIfNeeded];
    
    [self _reloadDataAdapterIfNeeded];
}

- (void)_reloadTopAdapterIfNeeded {
    if ( sj_view_isDisappeared(_topContainerView) ) return;
    
    id<SJVideoPlayerControlLayerResources> sources = SJVideoPlayerConfigurations.shared.resources;
    BOOL isFullscreen = _videoPlayer.isFullscreen;
    BOOL isFitOnScreen = _videoPlayer.isFitOnScreen;
    BOOL isPlayOnScrollView = _videoPlayer.isPlayOnScrollView;
    BOOL isSmallscreen = !isFullscreen && !isFitOnScreen;

    // back item
    {
        SJEdgeControlButtonItem *backItem = [self.topAdapter itemForTag:SJEdgeControlLayerTopItem_Back];
        if ( backItem != nil ) {
            if ( _fixesBackItem ) {
                backItem.alpha = 0;
                backItem.image = nil;
            }
            else {
                if ( isFullscreen || isFitOnScreen )
                    backItem.hidden = NO;
                else if ( _hiddenBackButtonWhenOrientationIsPortrait )
                    backItem.hidden = YES;
                else
                    backItem.hidden = isPlayOnScrollView;
                
                if ( backItem.hidden == NO )
                    backItem.image = sources.backImage;
            }
        }
    }
    
    // title item
    {
        SJEdgeControlButtonItem *titleItem = [self.topAdapter itemForTag:SJEdgeControlLayerTopItem_Title];
        if ( titleItem != nil ) {
            if ( self.isHiddenTitleItemWhenOrientationIsPortrait && isSmallscreen ) {
                titleItem.hidden = YES;
            } else {
                if ( titleItem.customView != self.titleView )
                    titleItem.customView = self.titleView;
                SJVideoPlayerURLAsset *asset = _videoPlayer.URLAsset;
                NSAttributedString *_Nullable attributedTitle = asset.attributedTitle;
                self.titleView.attributedText = attributedTitle;
                titleItem.hidden = (attributedTitle.length == 0);
            }

            if ( titleItem.hidden == NO ) {
                // margin
                NSInteger atIndex = [_topAdapter indexOfItemForTag:SJEdgeControlLayerTopItem_Title];
                CGFloat left  = [_topAdapter isHiddenWithRange:NSMakeRange(0, atIndex)] ? 16 : 0;
                CGFloat right = [_topAdapter isHiddenWithRange:NSMakeRange(atIndex, _topAdapter.numberOfItems)] ? 16 : 0;
                titleItem.insets = SJEdgeInsetsMake(left, right);
            }
        }
    }
    
    // Coach item
    {
        SJEdgeControlButtonItem *coachItem = [self.topAdapter itemForTag:SJEdgeControlLayerTopItem_Model];
        if ( coachItem != nil ) {
            if ( coachItem.hidden == NO ) {
                // margin
                MRKVideoCoachView *view2 = (MRKVideoCoachView *)coachItem.customView;
                view2.model = _videoPlayer.videoPlayData.coachModel;
            }
        }
    }
    
    // Device item
    {
        SJEdgeControlButtonItem *deviceItem = [self.topAdapter itemForTag:SJEdgeControlLayerTopItem_Device];
        if ( deviceItem != nil ) {
            deviceItem.hidden = !_videoPlayer.courseModel.isSupportConnection;
            if ( deviceItem.hidden == NO ) {
                // margin
                deviceItem.image = _videoPlayer.isConnectDevice ? sources.device_LImage : sources.device_DImage;
            }
        }
    }
    
    // Danmu item
    {
        SJEdgeControlButtonItem *danmuItem = [self.topAdapter itemForTag:SJEdgeControlLayerTopItem_Danmu];
        if ( danmuItem != nil ) {
            danmuItem.hidden = !_videoPlayer.courseModel.isShowRankControl;
            if ( danmuItem.hidden == NO ) {
                // margin
                danmuItem.image = _videoPlayer.closeBarrageControl ? sources.danmu_DImage: sources.danmu_LImage;
            }
        }
    }
    
    // tv item
    {
        SJEdgeControlButtonItem *tvItem = [self.topAdapter itemForTag:SJEdgeControlLayerTopItem_TV];
        if ( tvItem != nil ) {
            if ( tvItem.hidden == NO ) {
                // margin
                tvItem.image = sources.tvImage;
            }
        }
    }
    
    // share item
    {
        SJEdgeControlButtonItem *shareItem = [self.topAdapter itemForTag:SJEdgeControlLayerTopItem_Share];
        if ( shareItem != nil ) {
            if ( shareItem.hidden == NO ) {
                // margin
                shareItem.image = sources.shareImage;
            }
        }
    }
    
    [_topAdapter reload];
}

- (void)_reloadLeftAdapterIfNeeded {
    if ( sj_view_isDisappeared(_leftContainerView) ) return;
    
    SJEdgeControlButtonItem *rankItem = [self.leftAdapter itemForTag:SJEdgeControlLayerLeftItem_Rank];
    if ( rankItem != nil ) {
        BOOL isShowRoll = _videoPlayer.videoPlayData.liveModel.isShowRankControl;
        rankItem.hidden = !isShowRoll;
    }
    [_leftAdapter reload];
}

- (void)_reloadBottomAdapterIfNeeded {
    if ( sj_view_isDisappeared(_bottomContainerView) ) return;
    
    id<SJVideoPlayerControlLayerResources> sources = SJVideoPlayerConfigurations.shared.resources;
    // play item
    {
        SJEdgeControlButtonItem *playItem = [self.bottomAdapter itemForTag:SJEdgeControlLayerBottomItem_Play];
        if ( playItem != nil && playItem.hidden == NO ) {
            playItem.image = _videoPlayer.isPaused ? sources.playImage : sources.pauseImage;
        }
    }
    
    [_bottomAdapter reload];
}


- (void)reloadDataAdapter{
    MRKVideoPrepareDataInfo *videoPlayData = _videoPlayer.videoPlayData;
    ///添加蓝牙展示数据
//    [self _addItemsBottomDataAdapter];
    self.rollView.titleStr = [NSString stringWithFormat:@"%.0fKM竞速赛",(float)_videoPlayer.courseModel.raceTarget/1000];
    self.progressView.raceTarget = _videoPlayer.courseModel.raceTarget;
    
    [self.dmListView isHiddenDanmuView:!_videoPlayer.courseModel.isShowRankControl];
    if (self.dmListView.hidden == NO) {
        [self.dmListView welcomeToRoom:@"欢迎来到MERIT课程，和数以万计的燃友们一起冲击燃脂！我们互相鼓励，互相监督，共同进步；课中良好氛围需要大家一起维护，发布违规言论我们将对账号进行封禁处理。"];
        [self.dmListView changeHolderStr:@"说说比赛感受吧"];
    }
    
    [self _reloadDataAdapterIfNeeded];
}

- (void)_reloadDataAdapterIfNeeded {
//    if ( sj_view_isDisappeared(_bottomDataView) ) return;
    
//    id<SJVideoPlayerControlLayerResources> sources = SJVideoPlayerConfigurations.shared.resources;
//    // auto item
//    {
//        self.usePlanCourseControl.hidden =  !_videoPlayer.videoPlayData.isShowAutoControl;
//        if ( self.usePlanCourseControl.hidden == NO ) {
//            // margin
//            UIImage *image = _videoPlayer.isUseLessonPlanControl ? sources.autoImage : sources.noAutoImage;
//            [self.usePlanCourseControl setImage:image forState:UIControlStateNormal];
//        }
//    }
//
//
//    MRKVideoPrepareDataInfo *videoPlayData = _videoPlayer.videoPlayData;
//    int equipmentId = videoPlayData.equipmentId.intValue;
//    switch (equipmentId) {
//        case BoatEquipment: case BicycleEquipment: case EllipticalEquipment: {
//
//            // 阻力 item
//            SJEdgeControlButtonItem *item4 = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_Resistance];
//            if ( item4 != nil) {
//                if (_videoPlayer.videoPlayData.isShowAutoControl) {
//                    item4.showControl = _videoPlayer.useLessonPlanControl;
//                    item4.holdStr = _videoPlayer.useLessonPlanControl?@"自动阻力(lv)":@"阻力(lv)";
//                }else{
//                    item4.showControl = NO;
//                    item4.holdStr = @"阻力(lv)";
//                }
//            }
//
//        }break;
//        case TreadmillEquipment: {
//            // 速度 item
//            SJEdgeControlButtonItem *item8 = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_Speed];
//            if ( item8 != nil) {
//                if (_videoPlayer.videoPlayData.isShowAutoControl) {
//                    item8.showControl = _videoPlayer.useLessonPlanControl;
//                    item8.holdStr = _videoPlayer.useLessonPlanControl?@"自动速度(km/h)":@"速度(km/h)";
//                }else{
//                    item8.showControl = NO;
//                    item8.holdStr = @"速度(km/h)";
//                }
//            }
//
//            // 坡度 item
//            SJEdgeControlButtonItem *item7 = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_Slope];
//            if ( item7 != nil) {
//                if (_videoPlayer.videoPlayData.isShowAutoControl) {
//                    item7.showControl = _videoPlayer.useLessonPlanControl;
//                    item7.holdStr = _videoPlayer.useLessonPlanControl?@"自动坡度":@"坡度";
//                }else{
//                    item7.showControl = NO;
//                    item7.holdStr = @"坡度";
//                }
//            }
//        }break;
//        default: break;
//    }
//
//    [_dataAdapter reload];
}


- (void)_reloadRightAdapterIfNeeded {
    if ( sj_view_isDisappeared(_rightContainerView) ) return;
    
    SJEdgeControlButtonItem *danmuItem = [self.rightAdapter itemForTag:12000];
    if ( danmuItem != nil ) {
        BOOL isRealVideoCourse = _videoPlayer.videoPlayData.liveModel.isRealVideoCourse;
        danmuItem.hidden = isRealVideoCourse;
    }
    [self.rightAdapter reload];
}

- (void)_reloadCenterAdapterIfNeeded {
    if ( sj_view_isDisappeared(_centerContainerView) ) return;
    
    SJEdgeControlButtonItem *replayItem = [self.centerAdapter itemForTag:SJEdgeControlLayerCenterItem_Replay];
    if ( replayItem != nil ) {
        replayItem.hidden = !_videoPlayer.isPlaybackFinished;
        if ( replayItem.hidden == NO && replayItem.title == nil ) {
            id<SJVideoPlayerControlLayerResources> resources = SJVideoPlayerConfigurations.shared.resources;
            id<SJVideoPlayerLocalizedStrings> strings = SJVideoPlayerConfigurations.shared.localizedStrings;
            UILabel *textLabel = replayItem.customView;
            textLabel.attributedText = [NSAttributedString sj_UIKitText:^(id<SJUIKitTextMakerProtocol>  _Nonnull make) {
                make.alignment(NSTextAlignmentCenter).lineSpacing(6);
                make.font(resources.replayTitleFont);
                make.textColor(resources.replayTitleColor);
                if ( resources.replayImage != nil ) {
                    make.appendImage(^(id<SJUTImageAttachment>  _Nonnull make) {
                        make.image = resources.replayImage;
                    });
                }
                if ( strings.replay.length != 0 ) {
                    if ( resources.replayImage != nil ) make.append(@"\n");
                    make.append(strings.replay);
                }
            }];
            textLabel.bounds = (CGRect){CGPointZero, [textLabel.attributedText sj_textSize]};
        }
    }
    
    SJEdgeControlButtonItem *playItem = [self.centerAdapter itemForTag:SJEdgeControlLayerBottomItem_Play];
    if ( playItem != nil ) {
        BOOL isPaused = _videoPlayer.isPaused && !_videoPlayer.isPlaybackFinished;
        playItem.hidden = !isPaused;
        if ( playItem.hidden == NO ) {
            playItem.image = [AlivcImage imageNamed:@"icon_big_play"];
        }
    }
    
    [_centerAdapter reload];
}

- (void)_updateConnectForTopDeviceItemIfNeeded {
    if ( sj_view_isDisappeared(_topContainerView) )
        return;
    id<SJVideoPlayerControlLayerResources> sources = SJVideoPlayerConfigurations.shared.resources;
    SJEdgeControlButtonItem *deviceItem = [self.topAdapter itemForTag:SJEdgeControlLayerTopItem_Device];
    if ( deviceItem != nil && deviceItem.hidden == NO ) {
        // margin
        deviceItem.image = _videoPlayer.isConnectDevice ? sources.device_LImage : sources.device_DImage;
    }
}


- (void)_updateContentForBottomCurrentTimeItemIfNeeded {
    if ( sj_view_isDisappeared(_bottomContainerView) )
        return;
    NSString *currentTimeStr = [_videoPlayer stringForSeconds:_videoPlayer.currentTime];
    SJEdgeControlButtonItem *currentTimeItem = [_bottomAdapter itemForTag:SJEdgeControlLayerBottomItem_CurrentTime];
    if ( currentTimeItem != nil && currentTimeItem.isHidden == NO ) {
        currentTimeItem.title = [self _textForTimeString:currentTimeStr];
        [_bottomAdapter updateContentForItemWithTag:SJEdgeControlLayerBottomItem_CurrentTime];
    }
}

- (void)_updateContentForBottomDurationItemIfNeeded {
    SJEdgeControlButtonItem *durationTimeItem = [_bottomAdapter itemForTag:SJEdgeControlLayerBottomItem_DurationTime];
    if ( durationTimeItem != nil && durationTimeItem.isHidden == NO ) {
        durationTimeItem.title = [self _textForTimeString:[_videoPlayer stringForSeconds:_videoPlayer.duration]];
        [_bottomAdapter updateContentForItemWithTag:SJEdgeControlLayerBottomItem_DurationTime];
    }
}

- (void)_reloadSizeForBottomTimeLabel {
    // 00:00
    // 00:00:00
    NSString *ms = @"00:00";
    NSString *hms = @"00:00:00";
    NSString *durationTimeStr = [_videoPlayer stringForSeconds:_videoPlayer.duration];
    NSString *format = (durationTimeStr.length == ms.length)?ms:hms;
    CGSize formatSize = [[self _textForTimeString:format] sj_textSize];
    
    SJEdgeControlButtonItem *currentTimeItem = [_bottomAdapter itemForTag:SJEdgeControlLayerBottomItem_CurrentTime];
    SJEdgeControlButtonItem *durationTimeItem = [_bottomAdapter itemForTag:SJEdgeControlLayerBottomItem_DurationTime];
    
    if ( !durationTimeItem && !currentTimeItem ) return;
    currentTimeItem.size = formatSize.width;
    durationTimeItem.size = formatSize.width;
    [_bottomAdapter reload];
}

- (void)_updateContentForBottomProgressIndicatorIfNeeded {
    if ( _bottomProgressIndicator != nil && !sj_view_isDisappeared(_bottomProgressIndicator) ) {
        _bottomProgressIndicator.value = _videoPlayer.currentTime;
        _bottomProgressIndicator.maxValue = _videoPlayer.duration ? : 1;
    }
}

- (void)_updateCurrentTimeForDraggingProgressPopupViewIfNeeded {
    if ( !sj_view_isDisappeared(_draggingProgressPopupView) )
        _draggingProgressPopupView.currentTime = _videoPlayer.currentTime;
}

- (void)_updateAppearStateForResidentBackButtonIfNeeded {
    if ( !_fixesBackItem )
        return;
    BOOL isFitOnScreen = _videoPlayer.isFitOnScreen;
    BOOL isFull = _videoPlayer.isFullscreen;
    BOOL isLockedScreen = _videoPlayer.isLockedScreen;
    if ( isLockedScreen ) {
        _fixedBackButton.hidden = YES;
    }
    else {
        BOOL isPlayOnScrollView = _videoPlayer.isPlayOnScrollView;
        _fixedBackButton.hidden = isPlayOnScrollView && !isFitOnScreen && !isFull;
    }
}

- (void)_updateNetworkSpeedStrForLoadingView {
    if ( !_videoPlayer || !self.loadingView.isAnimating )
        return;
    
    if ( self.loadingView.showsNetworkSpeed && ![_videoPlayer.URLAsset.mediaURL isFileURL] ) {
        self.loadingView.networkSpeedStr = [NSAttributedString sj_UIKitText:^(id<SJUIKitTextMakerProtocol>  _Nonnull make) {
            id<SJVideoPlayerControlLayerResources> resources = SJVideoPlayerConfigurations.shared.resources;
            make.font(resources.loadingNetworkSpeedTextFont);
            make.textColor(resources.loadingNetworkSpeedTextColor);
            make.alignment(NSTextAlignmentCenter);
            make.append(self.videoPlayer.reachability.networkSpeedStr);
        }];
    }
    else {
        self.loadingView.networkSpeedStr = nil;
    }
}

- (void)_reloadCustomStatusBarIfNeeded NS_AVAILABLE_IOS(11.0) {
    if ( sj_view_isDisappeared(_customStatusBar) )
        return;
    _customStatusBar.networkStatus = _videoPlayer.reachability.networkStatus;
    _customStatusBar.date = NSDate.date;
    _customStatusBar.batteryState = UIDevice.currentDevice.batteryState;
    _customStatusBar.batteryLevel = UIDevice.currentDevice.batteryLevel;
}

#pragma mark  ---------------- Progres---------------

- (void)_updateForDraggingProgressPopupView {
    SJDraggingProgressPopupViewStyle style = SJDraggingProgressPopupViewStyleNormal;
    if ( !_videoPlayer.URLAsset.isM3u8 &&
         [_videoPlayer.playbackController respondsToSelector:@selector(screenshotWithTime:size:completion:)] ) {
        if ( _videoPlayer.isFullscreen ) {
            style = SJDraggingProgressPopupViewStyleFullscreen;
        }
        else if ( _videoPlayer.isFitOnScreen ) {
            style = SJDraggingProgressPopupViewStyleFitOnScreen;
        }
    }
    _draggingProgressPopupView.style = style;
    _draggingProgressPopupView.duration = _videoPlayer.duration ?: 1;
    _draggingProgressPopupView.currentTime = _videoPlayer.currentTime;
    _draggingProgressPopupView.dragTime = _videoPlayer.currentTime;
}

- (nullable NSAttributedString *)_textForTimeString:(NSString *)timeStr {
    id<SJVideoPlayerControlLayerResources> resources = SJVideoPlayerConfigurations.shared.resources;
    return [NSAttributedString sj_UIKitText:^(id<SJUIKitTextMakerProtocol>  _Nonnull make) {
        make.append(timeStr).font(resources.timeLabelFont).textColor(resources.timeLabelColor).alignment(NSTextAlignmentCenter);
    }];
}

/// 此处为重置控制层的隐藏间隔.(如果点击到当前控制层上的item, 则重置控制层的隐藏间隔)
- (void)_resetControlLayerAppearIntervalForItemIfNeeded:(NSNotification *)note {
    SJEdgeControlButtonItem *item = note.object;
    if ( item.resetsAppearIntervalWhenPerformingItemAction ) {
        if ( [_topAdapter containsItem:item] ||
             [_leftAdapter containsItem:item] ||
             [_bottomAdapter containsItem:item] ||
             [_rightAdapter containsItem:item])
            
            [_videoPlayer controlLayerNeedAppear];
    }
}

- (void)_showOrRemoveBottomProgressIndicator {
    if ( _hiddenBottomProgressIndicator || _videoPlayer.playbackType == SJPlaybackTypeLIVE ) {
        if ( _bottomProgressIndicator ) {
            [_bottomProgressIndicator removeFromSuperview];
            _bottomProgressIndicator = nil;
        }
    } else {
        if ( !_bottomProgressIndicator ) {
            [self.controlView addSubview:self.bottomProgressIndicator];
            [self _updateLayoutForBottomProgressIndicator];
        }
    }
}

- (void)_updateLayoutForBottomProgressIndicator {
    if ( _bottomProgressIndicator == nil ) return;
    _bottomProgressIndicator.trackHeight = _bottomProgressIndicatorHeight;
    if (_screen.is_iPhoneX) {
        _bottomProgressIndicator.frame = (CGRect){kScreenPadding+20, self.bounds.size.height - _bottomProgressIndicatorHeight, self.bounds.size.width - kScreenPadding*2 - 20*2, _bottomProgressIndicatorHeight};
    } else {
        _bottomProgressIndicator.frame = (CGRect){0, self.bounds.size.height - _bottomProgressIndicatorHeight, self.bounds.size.width, _bottomProgressIndicatorHeight};
    }
}

- (void)_showOrHiddenLoadingView {
    if ( _videoPlayer == nil || _videoPlayer.URLAsset == nil ) {
        [self.loadingView stop];
        return;
    }
    
    if ( _videoPlayer.isPaused ) {
        [self.loadingView stop];
    }
    else if ( _videoPlayer.assetStatus == SJAssetStatusPreparing ) {
        [self.loadingView start];
    }
    else if ( _videoPlayer.assetStatus == SJAssetStatusFailed ) {
        [self.loadingView stop];
    }
    else if ( _videoPlayer.assetStatus == SJAssetStatusReadyToPlay ) {
        self.videoPlayer.reasonForWaitingToPlay == SJWaitingToMinimizeStallsReason ? [self.loadingView start] : [self.loadingView stop];
    }
}

- (void)_willBeginDragging {
    [self.controlView addSubview:self.draggingProgressPopupView];
    [self _updateForDraggingProgressPopupView];
    [_draggingProgressPopupView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.offset(0);
    }];
    
    sj_view_initializes(_draggingProgressPopupView);
    sj_view_makeAppear(_draggingProgressPopupView, NO);
    
    if ( _draggingObserver.willBeginDraggingExeBlock )
        _draggingObserver.willBeginDraggingExeBlock(_draggingProgressPopupView.dragTime);
}

- (void)_didMove:(NSTimeInterval)progressTime {
    _draggingProgressPopupView.dragTime = progressTime;
//    // 是否生成预览图
//    if ( _draggingProgressPopupView.isPreviewImageHidden == NO ) {
//        __weak typeof(self) _self = self;
//        [_videoPlayer screenshotWithTime:progressTime
//                                    size:CGSizeMake(_draggingProgressPopupView.frame.size.width, _draggingProgressPopupView.frame.size.height)
//                              completion:^(SJBaseVideoPlayer * _Nonnull videoPlayer, UIImage * _Nullable image, NSError * _Nullable error) {
//            __strong typeof(_self) self = _self;
//            if ( !self ) return;
//            [self.draggingProgressPopupView setPreviewImage:image];
//        }];
//    }
    
    if ( _draggingObserver.didMoveExeBlock )
        _draggingObserver.didMoveExeBlock(_draggingProgressPopupView.dragTime);
}

- (void)_endDragging {
    NSTimeInterval time = _draggingProgressPopupView.dragTime;
    if ( _draggingObserver.willEndDraggingExeBlock )
        _draggingObserver.willEndDraggingExeBlock(time);
    
    [_videoPlayer seekToTime:time completionHandler:nil];

    sj_view_makeDisappear(_draggingProgressPopupView, YES, ^{
        if ( sj_view_isDisappeared(self->_draggingProgressPopupView) ) {
            [self->_draggingProgressPopupView removeFromSuperview];
        }
    });
    
    if ( _draggingObserver.didEndDraggingExeBlock )
        _draggingObserver.didEndDraggingExeBlock(time);
}


/*
#pragma mark  ---------------- 课程指令设置 ---------------
///接收到指令信息
- (void)sendInstructionsWithData:(CourseLinkModel *)model{
    if (!model) return;
    
    // 判断是Merit课程 和 是电磁控设备 才发指令
    if (_videoPlayer.videoPlayData.isMeritControl) {
        [self instructionsOperationData:model];
    }
}

///指令操作
- (void)instructionsOperationData:(CourseLinkModel *)model{
    
    NSString *typeId = _videoPlayer.videoPlayData.equipmentId;
    if (typeId.intValue == TreadmillEquipment) {
        ///跑步机
        if (![_videoPlayer.trainManager isNeedInstructionWithModel:model]) {
            return;
        }
        
        NSString *tip = @"";///拼接提示
        ///
        SJEdgeControlButtonItem *item8 = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_Speed];
        if ( item8 == nil ) { return; }
        UIView *vi = item8.partView; ///锚点
        ///
        ///
        ///
        CGFloat speed = model.minNum.floatValue/10; ////跑步机速度提示要除以10
        if (model.adviseNum.intValue == 0) {
            tip = [NSString stringWithFormat:@"速度将调至%.1f", speed];
        }
        
        if (model.minNum.intValue > 0 && model.adviseNum.intValue > 0) {
            tip = [NSString stringWithFormat:@"速度将调至%.1f, 坡度将调至%@", speed, model.adviseNum];
        }

        [self setResistanceSlopeSpeedNotification:@{Speed:model.minNum,
                                                    Slope:model.adviseNum,
                                                    BlueDeviceType:typeId,
                                                    @"status":@(_videoPlayer.trainManager.treamillStatus)}
                                     andPointView:vi
                                        andTipStr:tip];
    }
    
    if (typeId.intValue == BoatEquipment ||
        typeId.intValue == BicycleEquipment ||
        typeId.intValue == EllipticalEquipment ||
        typeId.intValue == PowerEquipment) {
        //划船机,单车,椭圆机
        if (model.adviseNum.intValue == 0) {return;}
  
        @weakify(self);
        [_videoPlayer.videoPlayData mappingResistance:model completion:^(CourseLinkModel * _Nonnull mod) {
            @strongify(self);
            ///锚点\阻力Item加提示
            SJEdgeControlButtonItem *item8 = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_Resistance];
            UIView *vi = item8.partView; ///锚点
            NSString *tip = [NSString stringWithFormat:@"阻力将调至%@", model.adviseNum];
            
            NSNumber *slope = @0;
            if (mod.isChangeSlope) {
                /// 需要调节坡度
                slope = mod.slopeNum ?:@0;
            } else {
                /// 不调节坡度的时候默认设备自己的坡度
                NSString *gradient = self.tyModel.gradient;
                if (![gradient isEqualToString:@"--"]){
                    slope = @(gradient.intValue);
                }
            }
            NSDictionary *orderParms = @{
                Resistance:mod.adviseNum?:@0,
                Slope:slope,
                BlueDeviceType:typeId
            };
            [self setResistanceSlopeSpeedNotification:orderParms andPointView:vi andTipStr:tip];
        }];
    }
}
 
///设备指令通知
- (void)setResistanceSlopeSpeedNotification:(NSDictionary *)data andPointView:(UIView *)view andTipStr:(NSString *)tip{
    
    if (!self.hasToastAlert) {
        self.hasToastAlert = YES;
        
        @weakify(self);
        void(^sendNotificationBlock)(void) = ^{
            @strongify(self);
            if (!self) return;
            self.hasToastAlert = NO;
            ///是否使用
            if (self.videoPlayer.useLessonPlanControl){
                [[NSNotificationCenter defaultCenter] postNotificationName:SetResistanceSlopeSpeedNotification object:data];
            }
        };
        
        [MBProgressHUD showMiaToast:tip toView:self afterDelay:5.0];
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(5.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            sendNotificationBlock();
        });
    }
}

*/












#pragma mark  ---------------- 课程指令设置 ---------------
///接收到指令信息
- (void)sendInstructionsWithData:(CourseLinkModel *)model{
    if (!model) return;
    
    // 判断是Merit课程 和 是电磁控设备 才发指令
    if (_videoPlayer.videoPlayData.isMeritControl) {
        [self instructionsOperationData:model];
    }
}

///指令操作
- (void)instructionsOperationData:(CourseLinkModel *)model{
    ///教案关闭
    if (!self.videoPlayer.isUseLessonPlanControl) { return; }
    
    NSString *typeId = _videoPlayer.videoPlayData.equipmentId;
    EquipmentDetialModel *eqModel = _videoPlayer.videoPlayData.eqModel;
    
    ///跑步机
    if (typeId.intValue == TreadmillEquipment) {
        ///跑步机是否有坡度下发
        BOOL treadmillHasSlopeControl = _videoPlayer.videoPlayData.planModel.treadmillHasSlopeControl;
        
        ///跑步机是否需要下发指令
        if (![_videoPlayer.trainManager isNeedInstructionWithModel:model]) {
            return;
        }
        
        NSDictionary *orderParms = nil;                                                 ///指令数据
        NSMutableString *tipStr = [[NSMutableString alloc] init];                       ///提示字段
        
        NSNumber *speedOrderNum = model.minNum;
        NSNumber *slopeOrderNum = model.adviseNum;
        double speed = 0.0;
        if (speedOrderNum.doubleValue > 0) {
            speed = model.minNum.doubleValue/10;
            if (speed > eqModel.maxSpeed.intValue && eqModel.maxSpeed.intValue != 0) {
                speed = eqModel.maxSpeed.intValue;
            }
        }
        NSString *speedTipStr =[NSString stringWithFormat:@"%.1f", speed];
        /// 跑步机调节速度
        SJEdgeControlButtonItem *speeditem = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_Speed];
        if (speeditem != nil) {
            speedOrderNum = @(speed*10);
            
            ///速度Item加提示
            TrainDataItemView *speedItemView = speeditem.partView;
            [speedItemView reloadNumTip:@(speed)];
            
            [tipStr appendString:CombineString(@"速度将调至", speedTipStr)];
        }
        
        /// 跑步机是否可调节坡度
        /// @note 有负坡度显示
        SJEdgeControlButtonItem *slopeitem = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_Slope];
        if (slopeitem != nil && eqModel.showSlope.boolValue && treadmillHasSlopeControl) {
            int slopeNum = model.adviseNum.intValue;
            if (slopeNum > eqModel.maxSlope.intValue) {
                slopeNum = eqModel.maxSlope.intValue;
            }
            if (slopeNum < eqModel.minSlope.intValue) {
                slopeNum = eqModel.minSlope.intValue;
            }
            
            slopeOrderNum = @(slopeNum);
            ///坡度Item加提示
            TrainDataItemView *slopeItemView = slopeitem.partView;
            [slopeItemView reloadNumTip:@(slopeNum)];
            
            NSString *slopeStr = [NSString stringWithFormat:@"%d", slopeNum];
            NSString *slopeTipStr = [NSString stringWithFormat:@"%@坡度将调至", tipStr.length > 0 ? @"，" : @""];
            [tipStr appendString:CombineString(slopeTipStr, slopeStr)];
        }
        
        if ([tipStr isEmpty]) return;
        ///
        orderParms = @{
            Speed : speedOrderNum ?:@0,
            Slope : slopeOrderNum ?:@0,
            BlueDeviceType : typeId,
            @"status" : @(_videoPlayer.trainManager.treamillStatus)
        };
        [self setOrderNotificationData:orderParms
                             andTipStr:tipStr
                        andControlType:@"3"
                      andControlNumber:speedTipStr];
    }
    
    ///划船机,单车,力量站 椭圆机 可支持坡度调节
    if(typeId.intValue == BoatEquipment ||
       typeId.intValue == BicycleEquipment ||
       typeId.intValue == PowerEquipment ||
       typeId.intValue == EllipticalEquipment) {
        
        SJEdgeControlButtonItem *slopeitem = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_Slope];
        SJEdgeControlButtonItem *item = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_Resistance]; ///
   
        ///没有坡度，阻力为0
        if (slopeitem == nil && model.adviseNum.intValue == 0) {
            return;
        }
        
        @weakify(self);
        [_videoPlayer.videoPlayData mappingResistance:model completion:^(CourseLinkModel * _Nonnull mod) {
            @strongify(self);
            NSMutableDictionary *orderParms = [NSMutableDictionary dictionaryWithDictionary:@{
                BlueDeviceType:typeId
            }];
            
            NSMutableString *tipStr = [[NSMutableString alloc] init];    ///提示字段
            ///锚点/阻力Item加提示
            if (item != nil && model.adviseNum.intValue > 0) {
                [orderParms setObject:mod.adviseNum?:@0 forKey:Resistance];
                
                TrainDataItemView *partView = item.partView;
                [partView reloadNumTip:mod.adviseNum];
                
                [tipStr appendString:CombineString(@"阻力将调至", mod.adviseNum.stringValue)];
            }
            
            ///坡度Item加提示
            if (slopeitem != nil && eqModel.showSlope.boolValue && mod.isChangeSlope) {
                [orderParms setObject:mod.slopeNum?:@0 forKey:Slope];
                
                TrainDataItemView *slopePartView = slopeitem.partView;
                [slopePartView reloadNumTip:mod.slopeNum];
                
                NSString *firTipStr = [NSString stringWithFormat:@"%@坡度将调至", model.adviseNum.intValue > 0 ? @"，" : @"" ];
                [tipStr appendString:CombineString(firTipStr, mod.slopeNum.stringValue)];
            }
            
            if ([tipStr isNotBlank]) {
                [self setOrderNotificationData:orderParms
                                     andTipStr:tipStr
                                andControlType:model.adviseNum.intValue > 0 ? @"1" : @"2"
                              andControlNumber:[NSString stringWithFormat:@"%@", model.adviseNum.intValue > 0 ? model.adviseNum : mod.slopeNum]];
            }
        }];
    }
}


NS_INLINE NSString *CombineString(NSString *str1, NSString *str2) {
    return [NSString stringWithFormat:@"%@%@", str1?:@"", str2?:@""];
}

///设备指令通知
- (void)setOrderNotificationData:(NSDictionary *)data
                       andTipStr:(NSString *)tip
                  andControlType:(NSString *)controlType
                andControlNumber:(NSString *)controlNumber{
    
    if (!self.hasToastAlert) {
        self.hasToastAlert = YES;
        
        @weakify(self);
        void(^sendNotificationBlock)(void) = ^{
            @strongify(self);
            if (!self) return;
            self.hasToastAlert = NO;
            ///是否使用
            if (self.videoPlayer.useLessonPlanControl){
                [[NSNotificationCenter defaultCenter] postNotificationName:SetResistanceSlopeSpeedNotification object:data];
            }
        };
        
        [MBProgressHUD showMiaToast:tip toView:self afterDelay:5.0];
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(5.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            sendNotificationBlock();
        });
        MLog(@"_setOrderNotificationData下发指令 ==== %@", data);
    }
}




/**
 刷新建议提示, 不判断设备
 */
- (void)refreshNoteTips{
    /// 判断设备有无连接设备
    if (!_videoPlayer.videoPlayData.isConnectMachine){
        return;
    }
    
    /// 当前无小节model
    if (!self.currentlinkModel) {
        return;
    }
    
    /// isMeritControl && 开启AI调控
    if (_videoPlayer.videoPlayData.isMeritControl && self.videoPlayer.isUseLessonPlanControl) {
        return;
    }
    
    /**
     @note 考虑到会有[速度,坡度] [阻力, 坡度]同时存在的情况
     # minNum;           速度 [麦瑞克跑步机均为Merit设备, 暂不考虑]
     # adviseNum;        阻力
     # slopeNum;         坡度
     */
    CourseLinkModel *model = self.currentlinkModel;
    ///
    NSString *typeId = _videoPlayer.videoPlayData.equipmentId;
    EquipmentDetialModel *eqModel = _videoPlayer.videoPlayData.eqModel;
    
    ///跑步机
    if (typeId.intValue == TreadmillEquipment) {
        NSMutableString *tipStr = [[NSMutableString alloc] init];///提示字段
        
        /// 速度
        NSNumber *speedOrderNum = model.minNum;
        double speed = 0.0;
        if (speedOrderNum.doubleValue > 0) {
            speed =speedOrderNum.doubleValue/10;
            if (speed > eqModel.maxSpeed.intValue && eqModel.maxSpeed.intValue != 0) {
                speed = eqModel.maxSpeed.intValue;
            }
        }
        if (speed > 0) {
            NSString *speedTipStr =[NSString stringWithFormat:@"%.1f", speed];
            [tipStr appendString:CombineString(@"建议速度", speedTipStr)];
        }
        
        /// 坡度
        int slopeNum = model.adviseNum.intValue;
        if (slopeNum > eqModel.maxSlope.intValue) {
            slopeNum = eqModel.maxSlope.intValue;
        }
        if (slopeNum < eqModel.minSlope.intValue) {
            slopeNum = eqModel.minSlope.intValue;
        }
        if (slopeNum > 0) {
            NSString *slopeStr = [NSString stringWithFormat:@"%d", slopeNum];
            NSString *slopeTipStr = [NSString stringWithFormat:@"%@坡度", tipStr.length > 0 ? @"，" : @"建议"];
            [tipStr appendString:CombineString(slopeTipStr, slopeStr)];
        }
      
        if ([tipStr isEmpty]) return;
        [MBProgressHUD showMiaToast:tipStr toView:self];
        return;
    }
    
    if (typeId.intValue == BoatEquipment ||
       typeId.intValue == BicycleEquipment ||
       typeId.intValue == PowerEquipment ||
       typeId.intValue == EllipticalEquipment) {

        @weakify(self);
        [_videoPlayer.videoPlayData mappingResistance:model completion:^(CourseLinkModel * _Nonnull mod) {
            @strongify(self);
            NSMutableString *tipStr = [[NSMutableString alloc] init];///提示字段
            ///
            ///[阻力]
            if (mod.adviseNum.intValue > 0){
                [tipStr appendString:[NSString stringWithFormat:@"建议阻力%@", mod.adviseNum]];
            }
            
            ///[坡度]
            if (mod.slopeNum.intValue > 0) {
                NSString *slopeStr = [NSString stringWithFormat:@"%@", mod.slopeNum];
                NSString *slopeTipStr = [NSString stringWithFormat:@"%@坡度", tipStr.length > 0 ? @"，" : @"建议"];
                [tipStr appendString:CombineString(slopeTipStr, slopeStr)];
            }
            
            if ([tipStr isEmpty]) return;
            [MBProgressHUD showMiaToast:tipStr toView:self];
        }];
    }
}


@end


@implementation SJEdgeControlButtonItem (MrkGameEdgeControlLayerExtended)
- (void)setResetsAppearIntervalWhenPerformingItemAction:(BOOL)resetsAppearIntervalWhenPerformingItemAction {
    objc_setAssociatedObject(self, @selector(resetsAppearIntervalWhenPerformingItemAction), @(resetsAppearIntervalWhenPerformingItemAction), OBJC_ASSOCIATION_RETAIN_NONATOMIC);
}
- (BOOL)resetsAppearIntervalWhenPerformingItemAction {
    id result = objc_getAssociatedObject(self, _cmd);
    return result == nil ? YES : [result boolValue];
}
@end

