//
//  MRKVideoStepProgressView.m
//  Student_IOS
//
//  Created by merit on 2021/9/16.
//

#import "MRKVideoStepProgressView.h"
#import "MrkProgressPopView.h"


@interface MRKVideoStepProgressView ()
@property (nonatomic, strong) UILabel *timeLab;
@property (nonatomic, strong) MrkProgressPopView *progressSlider;
@end

@implementation MRKVideoStepProgressView

- (UILabel *)timeLab{
    if (!_timeLab) {
        UILabel *lab = [[UILabel alloc] init];
        lab.font = [UIFont fontWithName:Bebas_Font size:20];
        lab.textAlignment = 1;
        lab.textColor = [UIColor colorWithWhite:1 alpha:0.8];
        lab.adjustsFontSizeToFitWidth = YES;
        lab.text = @"00:00";
        _timeLab  = lab;
    }
    return _timeLab;
}

- (MrkProgressPopView *)progressSlider{
    if (!_progressSlider) {
        _progressSlider= [[MrkProgressPopView alloc] init];
        _progressSlider.image = [AlivcImage r_imageNamed:@"pic_race_rowing"];
        _progressSlider.trackTintColor = [UIColor colorWithWhite:1 alpha:0.2];
        _progressSlider.popUpViewAnimatedColors = @[RGB(137, 193, 243),RGB(198, 228, 255),RGB(250, 252, 255)];
        _progressSlider.progress = 0.0;
        [_progressSlider showPopUpViewAnimated:YES];
    }
    return _progressSlider;
}

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        
        [self addSubview:self.timeLab];
        [self addSubview:self.progressSlider];
    }
    return self;
}

- (void)layoutSubviews{
    [super layoutSubviews];
    
    self.timeLab.frame = CGRectMake(kScreenPadding, 0, 50, DHPX(45));
    self.progressSlider.frame = CGRectMake(kScreenPadding +55, (DHPX(45) - 5)/2, MainWidth -kScreenPadding*2 -55*2, 5);
}

- (void)setEquipmentId:(NSString *)equipmentId{
    _equipmentId = equipmentId;
    
    switch (equipmentId.intValue) {
        case BoatEquipment: { ///划船机
            self.progressSlider.image = [AlivcImage r_imageNamed:@"icon_rowing_machine_white"];
        }break;
            
        case BicycleEquipment:{///单车
            self.progressSlider.image = [AlivcImage r_imageNamed:@"icon_live_spoken car"];
        }break;
            
        case EllipticalEquipment:{///椭圆机
            self.progressSlider.image = [AlivcImage r_imageNamed:@"Icon_elliptical machine"];
        }break;
            
        case TreadmillEquipment: {///跑步机
            self.progressSlider.image = [AlivcImage r_imageNamed:@"icon_run"];
        }break;
        default: break;
    }
}

- (void)setProgress:(float)progress{
    _progress = progress;
    [self.progressSlider setProgress:progress animated:NO];
}

- (void)setDistance:(NSInteger)distance{
    _distance = distance;
    self.progress = (float)distance/self.raceTarget;
}

- (void)setUserTime:(NSTimeInterval)userTime{
    _userTime = userTime;
    
    NSString *timeStr = [MRKToolKit MSTimeStrFromSecond:(int)userTime];
    self.timeLab.text = timeStr;
}

- (void)dealloc {
    NSLog(@"😊😊😊-----dealloc-----😊😊😊%@" , NSStringFromClass([self class]));
}

/*
 // Only override drawRect: if you perform custom drawing.
 // An empty implementation adversely affects performance during animation.
 - (void)drawRect:(CGRect)rect {
 // Drawing code
 }
 */

@end
