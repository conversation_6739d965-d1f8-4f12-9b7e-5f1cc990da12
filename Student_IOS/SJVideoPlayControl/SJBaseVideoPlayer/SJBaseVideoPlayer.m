//
//  SJBaseVideoPlayer.m
//  SJBaseVideoPlayerProject
//
//  Created by 畅三江 on 2018/2/2.
//  Copyright © 2018年 changsanjiang. All rights reserved.
//

#import "SJBaseVideoPlayer.h"
#import <objc/message.h>
#import "SJRotationManager.h"
#import "SJDeviceVolumeAndBrightnessController.h"
#import "SJDeviceVolumeAndBrightnessTargetViewContext.h"

#import "SJVideoPlayerRegistrar.h"
#import "SJVideoPlayerPresentView.h"
#import "SJPlayModelPropertiesObserver.h"
#import "SJTimerControl.h"
#import "UIScrollView+ListViewAutoplaySJAdd.h"
#import "SJAliMediaPlaybackController.h"
#import "SJReachability.h"
#import "SJControlLayerAppearStateManager.h"
#import "SJFitOnScreenManager.h"

#import "SJPlayerView.h"
#import "SJSmallViewFloatingController.h"
#import "SJVideoDefinitionSwitchingInfo+Private.h"
#import "SJPromptPopupController.h"
#import "SJPrompt.h"
#import "SJBaseVideoPlayerConst.h"
#import "SJBaseVideoPlayer+TestLog.h"
#import "SJVideoPlayerURLAsset+SJSubtitlesAdd.h"

#import "SJViewControllerManager.h"
#import "UIView+SJBaseVideoPlayerExtended.h"
#import "NSString+SJBaseVideoPlayerExtended.h"
#import "SJPlayerViewInternal.h"
#import "Masonry.h"
#import "FeedbackGeneratorUtil.h"
#import "SportListModel.h"


NS_ASSUME_NONNULL_BEGIN
typedef struct _SJPlayerControlInfo {
    struct {
        CGFloat factor;
        NSTimeInterval offsetTime; ///< pan手势触发过程中的偏移量(secs)
    } pan;
    
    struct {
        CGFloat initialRate;
    } longPress;
    
    struct {
        SJPlayerGestureTypeMask disabledGestures;
        CGFloat rateWhenLongPressGestureTriggered;
        BOOL allowHorizontalTriggeringOfPanGesturesInCells;
    } gestureController;
    
    struct {
        BOOL automaticallyHides;
        NSTimeInterval delayHidden;
    } placeholder;
    
    struct {
        BOOL isScrollAppeared;
        BOOL pausedWhenScrollDisappeared;
        BOOL hiddenPlayerViewWhenScrollDisappeared;
        BOOL resumePlaybackWhenScrollAppeared;
    } scrollControl;
    
    struct {
        BOOL disableBrightnessSetting;
        BOOL disableVolumeSetting;
    } deviceVolumeAndBrightness;
    
    struct {
        BOOL accurateSeeking;
        BOOL autoplayWhenSetNewAsset;
        BOOL resumePlaybackWhenAppDidEnterForeground;
        BOOL resumePlaybackWhenPlayerHasFinishedSeeking;
        BOOL isUserPaused;
    } playbackControl;
    
    struct {
        BOOL pausedToKeepAppearState;
    } controlLayer;
    
    struct {
        BOOL isEnabled;
    } audioSessionControl;
    
    struct {
        BOOL isAppeared;
        BOOL hiddenFloatSmallViewWhenPlaybackFinished;
    } floatSmallViewControl;
    
} _SJPlayerControlInfo;

@interface SJBaseVideoPlayer ()<SJVideoPlayerPresentViewDelegate, SJPlayerViewDelegate>
@property (nonatomic) _SJPlayerControlInfo *controlInfo;

/// - 管理对象: 监听 App在前台, 后台, 耳机插拔, 来电等的通知
@property (nonatomic, strong, readonly) SJVideoPlayerRegistrar *registrar;

/// - observe视图的滚动
@property (nonatomic, strong, nullable) SJPlayModelPropertiesObserver *playModelObserver;
@property (nonatomic, strong) SJViewControllerManager *viewControllerManager;
@property (nonatomic, copy) NSString *linkid;///当前小节id
@property (nonatomic, assign) float videoRate; ///播放器实景中调节播放倍速标识
@end

@implementation SJBaseVideoPlayer {
    SJPlayerView *_view;
    
    ///
    /// 视频画面的呈现层
    ///
    SJVideoPlayerPresentView *_presentView;
    
    SJVideoPlayerRegistrar *_registrar;
    
    /// 当前资源是否播放过
    /// mpc => Media Playback Controller
    id<SJVideoPlayerURLAssetObserver> _Nullable _mpc_assetObserver;
    
    /// device volume And brightness manager
    id<SJDeviceVolumeAndBrightnessController> _deviceVolumeAndBrightnessController;
    SJDeviceVolumeAndBrightnessTargetViewContext *_deviceVolumeAndBrightnessTargetViewContext;
    id<SJDeviceVolumeAndBrightnessControllerObserver> _deviceVolumeAndBrightnessControllerObserver;
    
    /// playback controller
    NSError *_Nullable _error;
    id<SJVideoPlayerPlaybackController> _playbackController;
    SJVideoPlayerURLAsset *_URLAsset;
    
    /// control layer appear manager
    id<SJControlLayerAppearManager> _controlLayerAppearManager;
    id<SJControlLayerAppearManagerObserver> _controlLayerAppearManagerObserver;
    
    /// rotation manager
    id<SJRotationManager> _rotationManager;
    id<SJRotationManagerObserver> _rotationManagerObserver;
    
    /// Fit on screen manager
    id<SJFitOnScreenManager> _fitOnScreenManager;
    id<SJFitOnScreenManagerObserver> _fitOnScreenManagerObserver;
    
    /// Network status
    id<SJReachability> _reachability;
    id<SJReachabilityObserver> _reachabilityObserver;
    
    /// Scroll
    id<SJSmallViewFloatingController> _Nullable _smallViewFloatingController;
    id<SJSmallViewFloatingControllerObserverProtocol> _Nullable _smallViewFloatingControllerObserver;
    
    SocketRocketManager *_socketManager;
    MRKSportListDataManager *_rankDataManager;
    MRKVideoTeachingPlan *_teachPlan;
    MRKVideoTrainManager *_trainManager;
    SuperBurningRateManager *_burningRate;
    
    AVAudioSessionCategory _mCategory;
    AVAudioSessionCategoryOptions _mCategoryOptions;
    AVAudioSessionSetActiveOptions _mSetActiveOptions;
}

+ (instancetype)player {
    return [[self alloc] init];
}

+ (NSString *)version {
    return @"v3.7.2";
}

- (void)setVideoGravity:(SJVideoGravity)videoGravity {
    ///
    self.adaptScreenSizeOn = (videoGravity == AVLayerVideoGravityResizeAspectFill);
    self.playbackController.videoGravity = videoGravity;
}

- (SJVideoGravity)videoGravity {
    return self.playbackController.videoGravity;
}

- (nullable __kindof UIViewController *)atViewController {
    return [_presentView lookupResponderForClass:UIViewController.class];
}

- (instancetype)init {
    self = [super init];
    if ( !self ) return nil;
    
    _controlInfo = (_SJPlayerControlInfo *)calloc(1, sizeof(struct _SJPlayerControlInfo));
    _controlInfo->placeholder.automaticallyHides = YES;
    _controlInfo->placeholder.delayHidden = 0.5;
    _controlInfo->scrollControl.pausedWhenScrollDisappeared = YES;
    _controlInfo->scrollControl.hiddenPlayerViewWhenScrollDisappeared = YES;
    _controlInfo->scrollControl.resumePlaybackWhenScrollAppeared = YES;
    _controlInfo->playbackControl.autoplayWhenSetNewAsset = YES;
    _controlInfo->playbackControl.resumePlaybackWhenPlayerHasFinishedSeeking = YES;
    _controlInfo->floatSmallViewControl.hiddenFloatSmallViewWhenPlaybackFinished = YES;
    _controlInfo->gestureController.rateWhenLongPressGestureTriggered = 2.0;
    _controlInfo->audioSessionControl.isEnabled = YES;
    _controlInfo->pan.factor = 667;
    
    _mCategory = AVAudioSessionCategoryPlayback;
    _mSetActiveOptions = AVAudioSessionSetActiveOptionNotifyOthersOnDeactivation;
    
    [self _setupViews];
    [self performSelectorOnMainThread:@selector(_prepare) withObject:nil waitUntilDone:NO];
    return self;
}

- (void)_prepare {
    [self fitOnScreenManager];
    if ( !self.onlyFitOnScreen ) [self rotationManager];
    
    [self controlLayerAppearManager];
    [self deviceVolumeAndBrightnessController];
    [self registrar];
    [self reachability];
    [self gestureController];
    [self _setupViewControllerManager];
    [self _showOrHiddenPlaceholderImageViewIfNeeded];
    
    _deviceVolumeAndBrightnessTargetViewContext = [SJDeviceVolumeAndBrightnessTargetViewContext.alloc init];
    _deviceVolumeAndBrightnessTargetViewContext.isFullscreen = _rotationManager.isFullscreen;
    _deviceVolumeAndBrightnessTargetViewContext.isFitOnScreen = _fitOnScreenManager.isFitOnScreen;
    _deviceVolumeAndBrightnessTargetViewContext.isPlayOnScrollView = self.isPlayOnScrollView;
    _deviceVolumeAndBrightnessTargetViewContext.isScrollAppeared = self.isScrollAppeared;
    _deviceVolumeAndBrightnessTargetViewContext.isFloatingMode = _smallViewFloatingController.isAppeared;
    _deviceVolumeAndBrightnessController.targetViewContext = _deviceVolumeAndBrightnessTargetViewContext;
    [_deviceVolumeAndBrightnessController onTargetViewContextUpdated];
}

- (void)dealloc {
#ifdef DEBUG
    NSLog(@"%d \t %s", (int)__LINE__, __func__);
#endif
    [NSNotificationCenter.defaultCenter postNotificationName:SJVideoPlayerPlaybackControllerWillDeallocateNotification object:_playbackController];
    [_presentView performSelectorOnMainThread:@selector(removeFromSuperview) withObject:nil waitUntilDone:YES];
    [_view performSelectorOnMainThread:@selector(removeFromSuperview) withObject:nil waitUntilDone:YES];
    free(_controlInfo);
}

- (void)playerViewWillMoveToWindow:(SJPlayerView *)playerView {
    [self.playModelObserver refreshAppearState];
}

///
/// 此处拦截父视图的Tap手势
///
- (nullable UIView *)playerView:(SJPlayerView *)playerView hitTestForView:(nullable __kindof UIView *)view {
    if ( playerView.hidden || playerView.alpha < 0.01 || !playerView.isUserInteractionEnabled ) return nil;
    
    for ( UIGestureRecognizer *gesture in playerView.superview.gestureRecognizers ) {
        if ( [gesture isKindOfClass:UITapGestureRecognizer.class] && gesture.isEnabled ) {
            gesture.enabled = NO;
            dispatch_async(dispatch_get_main_queue(), ^{
                gesture.enabled = YES;
            });
        }
    }
    return view;
}

- (void)presentViewDidLayoutSubviews:(SJVideoPlayerPresentView *)presentView {

}

- (void)presentViewDidMoveToWindow:(SJVideoPlayerPresentView *)presentView {
    if ( _deviceVolumeAndBrightnessController != nil ) [_deviceVolumeAndBrightnessController onTargetViewMoveToWindow];
}



#pragma mark -

- (void)_handleSingleTap:(CGPoint)location {
    if ( self.controlInfo->floatSmallViewControl.isAppeared ) {
        if ( self.smallViewFloatingController.onSingleTapped ) {
            self.smallViewFloatingController.onSingleTapped(self.smallViewFloatingController);
        }
        return;
    }
    
    if ( self.isLockedScreen ) {
        if ( [self.controlLayerDelegate respondsToSelector:@selector(tappedPlayerOnTheLockedState:)] ) {
            [self.controlLayerDelegate tappedPlayerOnTheLockedState:self];
        }
    }
    else {
        [self.controlLayerAppearManager switchAppearState];
    }
}

- (void)_handleDoubleTap:(CGPoint)location {
    if ( self.controlInfo->floatSmallViewControl.isAppeared ) {
        if ( self.smallViewFloatingController.onDoubleTapped ) {
            self.smallViewFloatingController.onDoubleTapped(self.smallViewFloatingController);
        }
        return;
    }
    
    self.isPaused ? [self play] : [self pauseForUser];
}

- (void)_handlePan:(SJPanGestureTriggeredPosition)position direction:(SJPanGestureMovingDirection)direction state:(SJPanGestureRecognizerState)state translate:(CGPoint)translate {
    switch ( state ) {
        case SJPanGestureRecognizerStateBegan:
        {
            switch ( direction )
            {
                    /// 水平
                case SJPanGestureMovingDirection_H: {
                    if ( self.duration == 0 ) {
                        [_presentView cancelGesture:SJPlayerGestureType_Pan];
                        return;
                    }
                    self.controlInfo->pan.offsetTime = self.currentTime;
                } break;
                    
                    /// 垂直
                case SJPanGestureMovingDirection_V: {
                    
                }break;
            }
        }break;
            
        case SJPanGestureRecognizerStateChanged: {
            switch ( direction ) {
                    /// 水平
                case SJPanGestureMovingDirection_H: {
                    NSTimeInterval duration = self.duration;
                    NSTimeInterval previous = self.controlInfo->pan.offsetTime;
                    CGFloat tlt = translate.x;
                    CGFloat add = tlt / self.controlInfo->pan.factor * self.duration;
                    CGFloat offsetTime = previous + add;
                    if ( offsetTime > duration ) offsetTime = duration;
                    else if ( offsetTime < 0 ) offsetTime = 0;
                    self.controlInfo->pan.offsetTime = offsetTime;
                }
                    break;
                    /// 垂直
                case SJPanGestureMovingDirection_V: {
                    CGFloat value = translate.y * 0.005;
                    switch ( position ) {
                            /// brightness
                        case SJPanGestureTriggeredPosition_Left: {
                            float old = self.deviceVolumeAndBrightnessController.brightness;
                            float new = old - value;
                            NSLog(@"brightness.set: old: %lf, new: %lf", old, new);
                            self.deviceVolumeAndBrightnessController.brightness = new;
                        }
                            break;
                            /// volume
                        case SJPanGestureTriggeredPosition_Right: {
                            self.deviceVolumeAndBrightnessController.volume -= value;
                        }
                            break;
                    }
                }
                    break;
            }
        }
            break;
        case SJPanGestureRecognizerStateEnded: {
            switch ( direction ) {
                case SJPanGestureMovingDirection_H: { }
                    break;
                case SJPanGestureMovingDirection_V: { }
                    break;
            }
        }
            break;
    }
    
    if ( direction == SJPanGestureMovingDirection_H ) {
        if ( [self.controlLayerDelegate respondsToSelector:@selector(videoPlayer:panGestureTriggeredInTheHorizontalDirection:progressTime:)] ) {
            [self.controlLayerDelegate videoPlayer:self panGestureTriggeredInTheHorizontalDirection:state progressTime:self.controlInfo->pan.offsetTime];
        }
    }
}

- (void)_handlePinch:(CGFloat)scale {
    self.videoGravity = scale > 1 ?AVLayerVideoGravityResizeAspectFill:AVLayerVideoGravityResizeAspect;
}

- (void)_handleLongPress:(SJLongPressGestureRecognizerState)state {
    switch ( state ) {
        case SJLongPressGestureRecognizerStateBegan:
            self.controlInfo->longPress.initialRate = self.rate;
        case SJLongPressGestureRecognizerStateChanged:
            self.rate = self.rateWhenLongPressGestureTriggered;
            break;
        case SJLongPressGestureRecognizerStateEnded:
            self.rate = self.controlInfo->longPress.initialRate;
            break;
    }
    
    if ( [self.controlLayerDelegate respondsToSelector:@selector(videoPlayer:longPressGestureStateDidChange:)] ) {
        [self.controlLayerDelegate videoPlayer:self longPressGestureStateDidChange:state];
    }
}

#pragma mark -

- (void)setControlLayerDataSource:(nullable id<SJVideoPlayerControlLayerDataSource>)controlLayerDataSource {
    if ( controlLayerDataSource == _controlLayerDataSource ) return;
    _controlLayerDataSource = controlLayerDataSource;
    if ( !controlLayerDataSource ) return;
    
    _controlLayerDataSource.controlView.clipsToBounds = YES;
    
    // install
    UIView *controlView = _controlLayerDataSource.controlView;
    controlView.layer.zPosition = SJPlayerZIndexes.shared.controlLayerViewZIndex;
    controlView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
    controlView.frame = self.presentView.bounds;
    [self.presentView addSubview:controlView];
    
    if ( [self.controlLayerDataSource respondsToSelector:@selector(installedControlViewToVideoPlayer:)] ) {
        [self.controlLayerDataSource installedControlViewToVideoPlayer:self];
    }
}


#pragma mark -

- (void)_setupRotationManager:(id<SJRotationManager>)rotationManager {
    _rotationManager = rotationManager;
    _rotationManagerObserver = nil;
    
    if ( rotationManager == nil || self.onlyFitOnScreen )
        return;
    
    self.viewControllerManager.rotationManager = rotationManager;
    
    rotationManager.superview = self.view;
    rotationManager.target = self.presentView;
    __weak typeof(self) _self = self;
    rotationManager.shouldTriggerRotation = ^BOOL(id<SJRotationManager>  _Nonnull mgr) {
        __strong typeof(_self) self = _self;
        if ( !self ) return NO;
        if ( mgr.isFullscreen == NO ) {
            if ( self.playModelObserver.isScrolling ) return NO;
            if ( !self.view.superview ) return NO;
//            UIWindow *_Nullable window = self.view.window;
//            if ( window && !window.isKeyWindow ) return NO;
            if ( self.isPlayOnScrollView && !(self.isScrollAppeared || self.controlInfo->floatSmallViewControl.isAppeared) ) return NO;
            if ( self.touchedOnTheScrollView ) return NO;
        }
        if ( self.isLockedScreen ) return NO;
        
        if ( self.isFitOnScreen )
            return self.allowsRotationInFitOnScreen;
        
        if ( self.viewControllerManager.isViewDisappeared ) return NO;
        if ( [self.controlLayerDelegate respondsToSelector:@selector(canTriggerRotationOfVideoPlayer:)] ) {
            if ( ![self.controlLayerDelegate canTriggerRotationOfVideoPlayer:self] )
                return NO;
        }
        if ( self.atViewController.presentedViewController ) return NO;
        if ( self.shouldTriggerRotation && !self.shouldTriggerRotation(self) ) return NO;
        return YES;
    };
    
    _rotationManagerObserver = [rotationManager getObserver];
    _rotationManagerObserver.onRotatingChanged = ^(id<SJRotationManager>  _Nonnull mgr, BOOL isRotating) {
        __strong typeof(_self) self = _self;
        if ( !self ) return ;
        
        self->_deviceVolumeAndBrightnessTargetViewContext.isFullscreen = mgr.isFullscreen;
        [self->_deviceVolumeAndBrightnessController onTargetViewContextUpdated];
        
        if ( isRotating ) {
            if ( [self.controlLayerDelegate respondsToSelector:@selector(videoPlayer:willRotateView:)] ) {
                [self.controlLayerDelegate videoPlayer:self willRotateView:mgr.isFullscreen];
            }
            
            [self controlLayerNeedDisappear];
        }
        else {
            [self.playModelObserver refreshAppearState];
            if ( [self.controlLayerDelegate respondsToSelector:@selector(videoPlayer:didEndRotation:)] ) {
                [self.controlLayerDelegate videoPlayer:self didEndRotation:mgr.isFullscreen];
            }
            
            if ( mgr.isFullscreen ) {
                [self.viewControllerManager setNeedsStatusBarAppearanceUpdate];
            }
            else {
                [UIView animateWithDuration:0.25 animations:^{
                    [self.viewControllerManager setNeedsStatusBarAppearanceUpdate];
                }];
            }
        }
    };
    
    _rotationManagerObserver.onTransitioningChanged = ^(id<SJRotationManager>  _Nonnull mgr, BOOL isTransitioning) {
        __strong typeof(_self) self = _self;
        if ( !self ) return ;
        if ( [self.controlLayerDelegate respondsToSelector:@selector(videoPlayer:onRotationTransitioningChanged:)] ) {
            [self.controlLayerDelegate videoPlayer:self onRotationTransitioningChanged:isTransitioning];
        }
    };
}

- (void)_clearRotationManager {
    _viewControllerManager.rotationManager = nil;
    _rotationManagerObserver = nil;
    _rotationManager = nil;
}

#pragma mark -

- (void)_setupFitOnScreenManager:(id<SJFitOnScreenManager>)fitOnScreenManager {
    _fitOnScreenManager = fitOnScreenManager;
    _fitOnScreenManagerObserver = nil;
    
    if ( fitOnScreenManager == nil ) return;
    
    self.viewControllerManager.fitOnScreenManager = fitOnScreenManager;
    
    _fitOnScreenManagerObserver = [fitOnScreenManager getObserver];
    __weak typeof(self) _self = self;
    _fitOnScreenManagerObserver.fitOnScreenWillBeginExeBlock = ^(id<SJFitOnScreenManager> mgr) {
        __strong typeof(_self) self = _self;
        if ( !self ) return;
        self->_deviceVolumeAndBrightnessTargetViewContext.isFitOnScreen = mgr.isFitOnScreen;
        [self->_deviceVolumeAndBrightnessController onTargetViewContextUpdated];
        
        if ( self->_rotationManager != nil ) {
            self->_rotationManager.superview = mgr.isFitOnScreen ? self.fitOnScreenManager.superviewInFitOnScreen : self.view;
        }
        if ( self->_smallViewFloatingController != nil ) {
            self->_smallViewFloatingController.targetSuperview = mgr.isFitOnScreen ? self.fitOnScreenManager.superviewInFitOnScreen : self.view;
        }
        
        [self controlLayerNeedDisappear];
        
        if ( [self.controlLayerDelegate respondsToSelector:@selector(videoPlayer:willFitOnScreen:)] ) {
            [self.controlLayerDelegate videoPlayer:self willFitOnScreen:mgr.isFitOnScreen];
        }
    };
    
    _fitOnScreenManagerObserver.fitOnScreenDidEndExeBlock = ^(id<SJFitOnScreenManager> mgr) {
        __strong typeof(_self) self = _self;
        if ( !self ) return;
        
        if ( [self.controlLayerDelegate respondsToSelector:@selector(videoPlayer:didCompleteFitOnScreen:)] ) {
            [self.controlLayerDelegate videoPlayer:self didCompleteFitOnScreen:mgr.isFitOnScreen];
        }
        
        [self.viewControllerManager setNeedsStatusBarAppearanceUpdate];
    };
}


#pragma mark -

- (void)_setupControlLayerAppearManager:(id<SJControlLayerAppearManager>)controlLayerAppearManager {
    _controlLayerAppearManager = controlLayerAppearManager;
    _controlLayerAppearManagerObserver = nil;
    
    if ( controlLayerAppearManager == nil ) return;
    self.viewControllerManager.controlLayerAppearManager = controlLayerAppearManager;
    
    __weak typeof(self) _self = self;
    _controlLayerAppearManager.canAutomaticallyDisappear = ^BOOL(id<SJControlLayerAppearManager>  _Nonnull mgr) {
        __strong typeof(_self) self = _self;
        if ( !self ) return NO;

        if ( [self.controlLayerDelegate respondsToSelector:@selector(controlLayerOfVideoPlayerCanAutomaticallyDisappear:)] ) {
            if ( ![self.controlLayerDelegate controlLayerOfVideoPlayerCanAutomaticallyDisappear:self] ) {
                return NO;
            }
        }
        
        if ( self.canAutomaticallyDisappear && !self.canAutomaticallyDisappear(self) ) {
            return NO;
        }
        return YES;
    };
    
    _controlLayerAppearManagerObserver = [_controlLayerAppearManager getObserver];
    _controlLayerAppearManagerObserver.onAppearChanged = ^(id<SJControlLayerAppearManager> mgr) {
        __strong typeof(_self) self = _self;
        if ( !self ) return;
        
        if ( mgr.isAppeared ) {
            if ( [self.controlLayerDelegate respondsToSelector:@selector(controlLayerNeedAppear:)] ) {
                [self.controlLayerDelegate controlLayerNeedAppear:self];
            }
        } else {
            if ( [self.controlLayerDelegate respondsToSelector:@selector(controlLayerNeedDisappear:)] ) {
                [self.controlLayerDelegate controlLayerNeedDisappear:self];
            }
        }
        
        if ( !self.isFullscreen || self.isRotating ) {
            [UIView animateWithDuration:0 animations:^{
            } completion:^(BOOL finished) {
                [self.viewControllerManager setNeedsStatusBarAppearanceUpdate];
            }];
        }
        else {
            [UIView animateWithDuration:0.25 animations:^{
                [self.viewControllerManager setNeedsStatusBarAppearanceUpdate];
            }];
        }
    };
}


#pragma mark -

- (void)_setupSmallViewFloatingController:(id<SJSmallViewFloatingController>)smallViewFloatingController {
    _smallViewFloatingController = smallViewFloatingController;
    _smallViewFloatingControllerObserver = nil;
    
    if ( smallViewFloatingController == nil ) return;
    
    smallViewFloatingController.targetSuperview = self.view;
    smallViewFloatingController.target = self.presentView;
    
    __weak typeof(self) _self = self;
    _smallViewFloatingControllerObserver = [smallViewFloatingController getObserver];
    _smallViewFloatingControllerObserver.onAppearChanged = ^(id<SJSmallViewFloatingController>  _Nonnull controller) {
        __strong typeof(_self) self = _self;
        if ( !self ) return ;
        BOOL isAppeared = controller.isAppeared;
        self->_deviceVolumeAndBrightnessTargetViewContext.isFloatingMode = isAppeared;
        [self->_deviceVolumeAndBrightnessController onTargetViewContextUpdated];
        
        self.controlInfo->floatSmallViewControl.isAppeared = isAppeared;
        self.rotationManager.superview = isAppeared ? controller.floatingView : self.view;
    };
}




#pragma mark -

- (SJVideoPlayerRegistrar *)registrar {
    if ( _registrar ) return _registrar;
    _registrar = [SJVideoPlayerRegistrar new];
    
    __weak typeof(self) _self = self;
    _registrar.willTerminate = ^(SJVideoPlayerRegistrar * _Nonnull registrar) {
        __strong typeof(_self) self = _self;
        if ( !self ) return;
        [self _postNotification:SJVideoPlayerApplicationWillTerminateNotification];
    };
    return _registrar;
}

#pragma mark -

- (void)_setupViews {
    _view = [SJPlayerView new];
    _view.tag = SJPlayerViewTag;
    _view.delegate = self;
    _view.backgroundColor = [UIColor blackColor];
    
    _presentView = [SJVideoPlayerPresentView new];
    _presentView.tag = SJPresentViewTag;
    _presentView.frame = _view.bounds;
    _presentView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
    _presentView.placeholderImageView.layer.zPosition = SJPlayerZIndexes.shared.placeholderImageViewZIndex;
    _presentView.delegate = self;
    [self _configGestureController:_presentView];
    [_view addSubview:_presentView];
    _view.presentView = _presentView;
}

- (void)_setupViewControllerManager {
    if ( _viewControllerManager == nil ) {
        _viewControllerManager = SJViewControllerManager.alloc.init;
    }
    _viewControllerManager.fitOnScreenManager = _fitOnScreenManager;
    _viewControllerManager.rotationManager = _rotationManager;
    _viewControllerManager.controlLayerAppearManager = _controlLayerAppearManager;
    _viewControllerManager.presentView = self.presentView;
    _viewControllerManager.lockedScreen = self.isLockedScreen;
    
    if ( [_rotationManager isKindOfClass:SJRotationManager.class] ) {
        SJRotationManager *mgr = _rotationManager;
        mgr.actionForwarder = _viewControllerManager;
    }
}

- (void)_postNotification:(NSNotificationName)name {
    [self _postNotification:name userInfo:nil];
}

- (void)_postNotification:(NSNotificationName)name userInfo:(nullable NSDictionary *)userInfo {
    [NSNotificationCenter.defaultCenter postNotificationName:name object:self userInfo:userInfo];
}

- (void)_showOrHiddenPlaceholderImageViewIfNeeded {
    if ( _playbackController.isReadyForDisplay ) {
        if ( _controlInfo->placeholder.automaticallyHides ) {
            NSTimeInterval delay = _URLAsset.original != nil ? 0 : _controlInfo->placeholder.delayHidden;
            BOOL animated = _URLAsset.original == nil;
            [self.presentView hidePlaceholderImageViewAnimated:animated delay:delay];
        }
    }
    else {
        [self.presentView setPlaceholderImageViewHidden:NO animated:NO];
    }
}

- (void)_configGestureController:(id<SJGestureController>)gestureController {
    __weak typeof(self) _self = self;
    gestureController.gestureRecognizerShouldTrigger = ^BOOL(id<SJGestureController>  _Nonnull control, SJPlayerGestureType type, CGPoint location) {
        __strong typeof(_self) self = _self;
        if ( !self ) return NO;
        
        if ( self.isRotating )
            return NO;
        
        if ( type != SJPlayerGestureType_SingleTap && self.isLockedScreen )
            return NO;
        
        if ( SJPlayerGestureType_Pan == type ) {
            switch ( control.movingDirection ) {
                case SJPanGestureMovingDirection_H: {
                    if ( self.playbackType == SJPlaybackTypeLIVE )
                        return NO;
                    
                    if ( self.duration <= 0 )
                        return NO;
                    
                    if ( self.canSeekToTime != nil && !self.canSeekToTime(self) )
                        return NO;
                    
                    if ( self.isPlayOnScrollView ) {
                        if ( !self.controlInfo->gestureController.allowHorizontalTriggeringOfPanGesturesInCells ) {
                            if ( !self.isFitOnScreen && !self.isRotating )
                                return NO;
                        }
                    }
                }
                    break;
                case SJPanGestureMovingDirection_V: {
                    if ( self.isPlayOnScrollView ) {
                        if ( !self.isFitOnScreen && !self.isRotating )
                            return NO;
                    }
                    switch ( control.triggeredPosition ) {
                            /// Brightness
                        case SJPanGestureTriggeredPosition_Left: {
                            if ( self.controlInfo->deviceVolumeAndBrightness.disableBrightnessSetting )
                                return NO;
                        }
                            break;
                            /// Volume
                        case SJPanGestureTriggeredPosition_Right: {
                            if ( self.controlInfo->deviceVolumeAndBrightness.disableVolumeSetting || self.isMuted )
                                return NO;
                        }
                            break;
                    }
                }
            }
        }
        
        if ( type == SJPlayerGestureType_LongPress ) {
            if ( self.assetStatus != SJAssetStatusReadyToPlay || self.isPaused )
                return NO;
        }
        
        if ( [self.controlLayerDelegate respondsToSelector:@selector(videoPlayer:gestureRecognizerShouldTrigger:location:)] ) {
            if ( ![self.controlLayerDelegate videoPlayer:self gestureRecognizerShouldTrigger:type location:location] )
                return NO;
        }
        
        if ( self.gestureRecognizerShouldTrigger && !self.gestureRecognizerShouldTrigger(self, type, location) ) {
            return NO;
        }
        return YES;
    };
    
    gestureController.singleTapHandler = ^(id<SJGestureController>  _Nonnull control, CGPoint location) {
        __strong typeof(_self) self = _self;
        if ( !self ) return ;
        [self _handleSingleTap:location];
    };
    
    gestureController.doubleTapHandler = ^(id<SJGestureController>  _Nonnull control, CGPoint location) {
        __strong typeof(_self) self = _self;
        if ( !self ) return ;
        [self _handleDoubleTap:location];
    };
    
    gestureController.panHandler = ^(id<SJGestureController>  _Nonnull control, SJPanGestureTriggeredPosition position, SJPanGestureMovingDirection direction, SJPanGestureRecognizerState state, CGPoint translate) {
        __strong typeof(_self) self = _self;
        if ( !self ) return ;
        [self _handlePan:position direction:direction state:state translate:translate];
    };
    
    gestureController.pinchHandler = ^(id<SJGestureController>  _Nonnull control, CGFloat scale) {
        __strong typeof(_self) self = _self;
        if ( !self ) return ;
        [self _handlePinch:scale];
    };
    
    gestureController.longPressHandler = ^(id<SJGestureController>  _Nonnull control, SJLongPressGestureRecognizerState state) {
        __strong typeof(_self) self = _self;
        if ( !self ) return;
        [self _handleLongPress:state];
    };
}

- (void)_updateCurrentPlayingIndexPathIfNeeded:(SJPlayModel *)playModel {
    if ( !playModel )
        return;
    
    // 维护当前播放的indexPath
    UIScrollView *scrollView = playModel.inScrollView;
    if ( scrollView.sj_enabledAutoplay ) {
        scrollView.sj_currentPlayingIndexPath = playModel.indexPath;
    }
}

/// - 当用户触摸到TableView或者ScrollView时, 这个值为YES.
/// - 这个值用于旋转的条件之一, 如果用户触摸在TableView或者ScrollView上时, 将不会自动旋转.
- (BOOL)touchedOnTheScrollView {
    return _playModelObserver.isTouched;
}

- (void)switchControlLayerWithIdentifier:(SJControlLayerIdentifier)identifier{
    
}

@end






@implementation SJBaseVideoPlayer (AudioSession)

- (void)setAudioSessionControlEnabled:(BOOL)audioSessionControlEnabled {
    _controlInfo->audioSessionControl.isEnabled = audioSessionControlEnabled;
}

- (BOOL)isAudioSessionControlEnabled {
    return _controlInfo->audioSessionControl.isEnabled;
}

- (void)setCategory:(AVAudioSessionCategory)category withOptions:(AVAudioSessionCategoryOptions)options {
    _mCategory = category;
    _mCategoryOptions = options;
    
    NSError *error = nil;
    if ( ![AVAudioSession.sharedInstance setCategory:_mCategory withOptions:_mCategoryOptions error:&error] ) {
#ifdef DEBUG
        NSLog(@"%@", error);
#endif
    }
}

- (void)setActiveOptions:(AVAudioSessionSetActiveOptions)options {
    _mSetActiveOptions = options;
    NSError *error = nil;
    if ( ![AVAudioSession.sharedInstance setActive:YES withOptions:_mSetActiveOptions error:&error] ) {
#ifdef DEBUG
        NSLog(@"%@", error);
#endif
    }
}
@end





@implementation SJBaseVideoPlayer (Placeholder)
- (UIView<SJVideoPlayerPresentView> *)presentView {
    return _presentView;
}

- (void)setAutomaticallyHidesPlaceholderImageView:(BOOL)isHidden {
    _controlInfo->placeholder.automaticallyHides = isHidden;
}
- (BOOL)automaticallyHidesPlaceholderImageView {
    return _controlInfo->placeholder.automaticallyHides;
}

- (void)setDelayInSecondsForHiddenPlaceholderImageView:(NSTimeInterval)delayHidden {
    _controlInfo->placeholder.delayHidden = delayHidden;
}
- (NSTimeInterval)delayInSecondsForHiddenPlaceholderImageView {
    return _controlInfo->placeholder.delayHidden;
}
@end







#pragma mark - 控制
@implementation SJBaseVideoPlayer (PlayControl)
- (void)setPlaybackController:(nullable __kindof id<SJVideoPlayerPlaybackController>)playbackController {
    if ( _playbackController != nil ) {
        [_playbackController.playerView removeFromSuperview];
        [NSNotificationCenter.defaultCenter postNotificationName:SJVideoPlayerPlaybackControllerWillDeallocateNotification object:_playbackController];
    }
    _playbackController = playbackController;
    [self _playbackControllerDidChange];
}

- (__kindof id<SJVideoPlayerPlaybackController>)playbackController {
    if ( _playbackController ) return _playbackController;
    _playbackController = [SJAliMediaPlaybackController new];
    [self _playbackControllerDidChange];
    return _playbackController;
}

- (void)_playbackControllerDidChange {
    if ( !_playbackController )
        return;
    
    _playbackController.delegate = self;
    
    if ( _playbackController.playerView.superview != self.presentView ) {
        _playbackController.playerView.frame = self.presentView.bounds;
        _playbackController.playerView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
        _playbackController.playerView.layer.zPosition = SJPlayerZIndexes.shared.playbackViewZIndex;
        [_presentView addSubview:_playbackController.playerView];
    }
}

- (SJPlaybackObservation *)playbackObserver {
    SJPlaybackObservation *obs = objc_getAssociatedObject(self, _cmd);
    if ( obs == nil ) {
        obs = [[SJPlaybackObservation alloc] initWithPlayer:self];
        objc_setAssociatedObject(self, _cmd, obs, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    }
    return obs;
}

- (void)switchVideoDefinition:(SJVideoPlayerURLAsset *)URLAsset {
    self.definitionSwitchingInfo.switchingAsset = URLAsset;
    [self.playbackController switchVideoDefinition:URLAsset];
}

- (SJVideoDefinitionSwitchingInfo *)definitionSwitchingInfo {
    SJVideoDefinitionSwitchingInfo *_Nullable definitionSwitchingInfo = objc_getAssociatedObject(self, _cmd);
    if ( definitionSwitchingInfo == nil ) {
        definitionSwitchingInfo = [SJVideoDefinitionSwitchingInfo new];
        objc_setAssociatedObject(self, @selector(definitionSwitchingInfo), definitionSwitchingInfo, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    }
    return definitionSwitchingInfo;
}

- (void)_resetDefinitionSwitchingInfo {
    SJVideoDefinitionSwitchingInfo *info = self.definitionSwitchingInfo;
    info.currentPlayingAsset = nil;
    info.switchingAsset = nil;
    info.status = SJDefinitionSwitchStatusUnknown;
}

- (SJPlaybackType)playbackType {
    return _playbackController.playbackType;
}

- (NSString *)tracePlaybackType {
    switch (self.playbackType) {
        case SJPlaybackTypeLIVE:
            return @"living";
            break;
        case SJPlaybackTypeVOD:
            return @"recorded";
            break;
        case SJPlaybackTypeREAL:
            return @"liveVideo";
            break;//实景
        default:
            return @"local";
            break;
    }
}

#pragma mark -

- (NSError *_Nullable)error {
    return _playbackController.error;
}

- (SJAssetStatus)assetStatus {
    return _playbackController.assetStatus;
}

- (SJPlaybackTimeControlStatus)timeControlStatus {
    return _playbackController.timeControlStatus;
}

- (BOOL)isPaused {
    return self.timeControlStatus == SJPlaybackTimeControlStatusPaused;
}

- (BOOL)isPlaying {
    return self.timeControlStatus == SJPlaybackTimeControlStatusPlaying;
}

- (BOOL)isBuffering {
    return self.timeControlStatus == SJPlaybackTimeControlStatusWaitingToPlay && self.reasonForWaitingToPlay == SJWaitingToMinimizeStallsReason;
}

- (BOOL)isEvaluating {
    return self.timeControlStatus == SJPlaybackTimeControlStatusWaitingToPlay && self.reasonForWaitingToPlay == SJWaitingWhileEvaluatingBufferingRateReason;
}

- (BOOL)isNoAssetToPlay {
    return self.timeControlStatus == SJPlaybackTimeControlStatusWaitingToPlay && self.reasonForWaitingToPlay == SJWaitingWithNoAssetToPlayReason;
}

- (BOOL)isPlaybackFailed {
    return self.assetStatus == SJAssetStatusFailed;
}

- (nullable SJWaitingReason)reasonForWaitingToPlay {
    return _playbackController.reasonForWaitingToPlay;
}

- (BOOL)isPlaybackFinished {
    return _playbackController.isPlaybackFinished;
}

- (nullable SJFinishedReason)finishedReason {
    return _playbackController.finishedReason;
}

- (BOOL)isPlayed {
    return _playbackController.isPlayed;
}

- (BOOL)isReplayed {
    return _playbackController.isReplayed;
}

- (BOOL)isUserPaused {
    return _controlInfo->playbackControl.isUserPaused;
}

- (NSTimeInterval)currentTime {
    return self.playbackController.currentTime;
}

- (NSTimeInterval)duration {
    return self.playbackController.duration;
}

- (NSTimeInterval)playableDuration {
    return self.playbackController.playableDuration;
}

- (NSTimeInterval)durationWatched {
    return self.playbackController.durationWatched;
}

- (NSString *)stringForSeconds:(NSInteger)secs {
    return [NSString stringWithCurrentTime:secs duration:self.duration];
}

#pragma mark -
// 1.
- (void)setURLAsset:(nullable SJVideoPlayerURLAsset *)URLAsset {
    
    [self _resetDefinitionSwitchingInfo];
    
    {
        [self _postNotification:SJVideoPlayerURLAssetWillChangeNotification];
        _URLAsset = URLAsset;
        [self _postNotification:SJVideoPlayerURLAssetDidChangeNotification];
    }
    
    //
    // prepareToPlay
    //
    self.playbackController.media = URLAsset;
    self.definitionSwitchingInfo.currentPlayingAsset = URLAsset;
    
    [self _updateAssetObservers];
    [self _showOrHiddenPlaceholderImageViewIfNeeded];
    
    if ( [self.controlLayerDelegate respondsToSelector:@selector(videoPlayer:prepareToPlay:)] ) {
        [self.controlLayerDelegate videoPlayer:self prepareToPlay:URLAsset];
    }
    
    if ( URLAsset == nil ) {
        [self stop];
        return;
    }
    
    [(SJMediaPlaybackController *)self.playbackController prepareToPlay];
    
    ///隐藏主动播放
    [self _tryToPlayIfNeeded];
    
    ///设置初始状态
    [self controlLayerNeedAppear];
}

- (nullable SJVideoPlayerURLAsset *)URLAsset {
    return _URLAsset;
}

/**
 逐步废弃了 在setURLAsset里使用
 */
- (void)singlePlay{
    [self _tryToPlayIfNeeded];
}

- (void)_tryToPlayIfNeeded {
    if ( self.registrar.state == SJVideoPlayerAppState_Background && self.isPausedInBackground )
        return;
    
    if ( _controlInfo->playbackControl.autoplayWhenSetNewAsset == NO )
        return;
    
    if ( self.isPlayOnScrollView && self.isScrollAppeared == NO && self.pausedWhenScrollDisappeared )
        return;
    
    [self play];
}

- (void)_updateAssetObservers {
    
    [self _updateCurrentPlayingIndexPathIfNeeded:_URLAsset.playModel];
    [self _updatePlayModelObserver:_URLAsset.playModel];
    
    _mpc_assetObserver = [_URLAsset getObserver];
    __weak typeof(self) _self = self;
    _mpc_assetObserver.playModelDidChangeExeBlock = ^(SJVideoPlayerURLAsset * _Nonnull asset) {
        __strong typeof(_self) self = _self;
        if ( !self ) return;
        [self _updateCurrentPlayingIndexPathIfNeeded:asset.playModel];
        [self _updatePlayModelObserver:asset.playModel];
    };
    
    _deviceVolumeAndBrightnessTargetViewContext.isPlayOnScrollView = self.isPlayOnScrollView;
    _deviceVolumeAndBrightnessTargetViewContext.isScrollAppeared = self.isScrollAppeared;
    [_deviceVolumeAndBrightnessController onTargetViewContextUpdated];
}

- (void)refresh {
    if ( !self.URLAsset ) return;
    [self _postNotification:SJVideoPlayerPlaybackWillRefreshNotification];
    [_playbackController refresh];
    [self play];
    [self _postNotification:SJVideoPlayerPlaybackDidRefreshNotification];
}

- (void)setPlayerVolume:(float)playerVolume {
    self.playbackController.volume = playerVolume;
}
- (float)playerVolume {
    return self.playbackController.volume;
}

- (void)setMuted:(BOOL)muted {
    self.playbackController.muted = muted;
}
- (BOOL)isMuted {
    return self.playbackController.muted;
}

- (void)setAutoplayWhenSetNewAsset:(BOOL)autoplayWhenSetNewAsset {
    _controlInfo->playbackControl.autoplayWhenSetNewAsset = autoplayWhenSetNewAsset;
}
- (BOOL)autoplayWhenSetNewAsset {
    return _controlInfo->playbackControl.autoplayWhenSetNewAsset;
}

- (void)setPausedInBackground:(BOOL)pausedInBackground {
    self.playbackController.pauseWhenAppDidEnterBackground = pausedInBackground;
}
- (BOOL)isPausedInBackground {
    return self.playbackController.pauseWhenAppDidEnterBackground;
}

- (void)setResumePlaybackWhenAppDidEnterForeground:(BOOL)resumePlaybackWhenAppDidEnterForeground {
    _controlInfo->playbackControl.resumePlaybackWhenAppDidEnterForeground = resumePlaybackWhenAppDidEnterForeground;
}
- (BOOL)resumePlaybackWhenAppDidEnterForeground {
    return _controlInfo->playbackControl.resumePlaybackWhenAppDidEnterForeground;
}

- (void)setCanPlayAnAsset:(nullable BOOL (^)(__kindof SJBaseVideoPlayer * _Nonnull))canPlayAnAsset {
    objc_setAssociatedObject(self, @selector(canPlayAnAsset), canPlayAnAsset, OBJC_ASSOCIATION_COPY_NONATOMIC);
}
- (nullable BOOL (^)(__kindof SJBaseVideoPlayer * _Nonnull))canPlayAnAsset {
    return objc_getAssociatedObject(self, _cmd);
}

- (void)setResumePlaybackWhenPlayerHasFinishedSeeking:(BOOL)resumePlaybackWhenPlayerHasFinishedSeeking {
    _controlInfo->playbackControl.resumePlaybackWhenPlayerHasFinishedSeeking = resumePlaybackWhenPlayerHasFinishedSeeking;
}
- (BOOL)resumePlaybackWhenPlayerHasFinishedSeeking {
    return _controlInfo->playbackControl.resumePlaybackWhenPlayerHasFinishedSeeking;
}

- (void)play {
    if ( [self.controlLayerDelegate respondsToSelector:@selector(canPerformPlayForVideoPlayer:)] ) {
        if ( ![self.controlLayerDelegate canPerformPlayForVideoPlayer:self] )
            return;
    }
    
    if ( self.canPlayAnAsset && !self.canPlayAnAsset(self) ){
        return;
    }
      
    if ( self.registrar.state == SJVideoPlayerAppState_Background && self.isPausedInBackground ){
        return;
    }
    
    _controlInfo->playbackControl.isUserPaused = NO;
    
    if ( self.assetStatus == SJAssetStatusFailed ) {
        [self refresh];
        return;
    }
    
    ///
    if (_controlInfo->audioSessionControl.isEnabled) {
        NSError *error = nil;
        if ( ![AVAudioSession.sharedInstance setCategory:_mCategory withOptions:_mCategoryOptions error:&error] ) {
#ifdef DEBUG
            NSLog(@"%@", error);
#endif
        }
        
        if ( ![AVAudioSession.sharedInstance setActive:YES withOptions:_mSetActiveOptions error:&error] ) {
#ifdef DEBUG
            NSLog(@"%@", error);
#endif
        }
    }
    
    [_playbackController play];
    
    ///
    [self.controlLayerAppearManager resume];
    
    ///判断是否开启跑步机
    [self.trainManager checkTreamillNeedStart];
}

- (void)pause {
    if ( [self.controlLayerDelegate respondsToSelector:@selector(canPerformPauseForVideoPlayer:)] ) {
        if ( ![self.controlLayerDelegate canPerformPauseForVideoPlayer:self] )
            return;
    }
    
    [_playbackController pause];
    
    ///播放结束跳转其他页面不触发跑步机暂停
//    if (![_playbackController isPlaybackFinished]) {
//        [_blueManager treamillPause];
//    }
}

///用户手动暂停视频
- (void)pauseForUser {
    _controlInfo->playbackControl.isUserPaused = YES;
    [self pause];
    
    ///用户手动暂停视频， 给跑步机发送暂停
    [self.trainManager pauseTreamill];
}

///设备暂停->视频暂停
- (void)devicePauseVideo {
    _controlInfo->playbackControl.isUserPaused = YES;
    [self pause];
}

- (void)pauseForAlert{
    _controlInfo->playbackControl.isUserPaused = YES;
    
    if ( [self.controlLayerDelegate respondsToSelector:@selector(canPerformPauseForVideoPlayer:)] ) {
        if ( ![self.controlLayerDelegate canPerformPauseForVideoPlayer:self] )
            return;
    }
    
    [_playbackController pause];
}

- (void)stop {
    if ( [self.controlLayerDelegate respondsToSelector:@selector(canPerformStopForVideoPlayer:)] ) {
        if ( ![self.controlLayerDelegate canPerformStopForVideoPlayer:self] )
            return;
    }
    
    [self _postNotification:SJVideoPlayerPlaybackWillStopNotification];
    
    {
        _controlInfo->playbackControl.isUserPaused = NO;
        _playModelObserver = nil;
        _URLAsset = nil;
        [_playbackController stop];
        
        [self _resetDefinitionSwitchingInfo];
        [self _showOrHiddenPlaceholderImageViewIfNeeded];
    }
    
    [self _postNotification:SJVideoPlayerPlaybackDidStopNotification];
}

- (void)replay {
    if ( !self.URLAsset ) return;
    if ( self.assetStatus == SJAssetStatusFailed ) {
        [self refresh];
        return;
    }
    
    _controlInfo->playbackControl.isUserPaused = NO;
    [_playbackController replay];
}

- (void)setCanSeekToTime:(BOOL (^_Nullable)(__kindof SJBaseVideoPlayer * _Nonnull))canSeekToTime {
    objc_setAssociatedObject(self, @selector(canSeekToTime), canSeekToTime, OBJC_ASSOCIATION_COPY_NONATOMIC);
}
- (BOOL (^_Nullable)(__kindof SJBaseVideoPlayer * _Nonnull))canSeekToTime {
    return objc_getAssociatedObject(self, _cmd);
}

- (void)setAccurateSeeking:(BOOL)accurateSeeking {
    _controlInfo->playbackControl.accurateSeeking = accurateSeeking;
}
- (BOOL)accurateSeeking {
    return _controlInfo->playbackControl.accurateSeeking;
}

- (void)seekToTime:(NSTimeInterval)secs completionHandler:(void (^ __nullable)(BOOL finished))completionHandler {
    if ( isnan(secs) ) { return; }
    
    if ( secs > self.playbackController.duration ) {
        secs = self.playbackController.duration * 0.98;
    }else if ( secs < 0 ) {
        secs = 0;
    }
    
    [self seekToTime:CMTimeMakeWithSeconds(secs, NSEC_PER_SEC)
     toleranceBefore:self.accurateSeeking ? kCMTimeZero : kCMTimePositiveInfinity
      toleranceAfter:self.accurateSeeking ? kCMTimeZero : kCMTimePositiveInfinity
   completionHandler:completionHandler];
}

- (void)seekToTime:(CMTime)time toleranceBefore:(CMTime)toleranceBefore toleranceAfter:(CMTime)toleranceAfter completionHandler:(void (^ _Nullable)(BOOL))completionHandler {
    if ( self.canSeekToTime && !self.canSeekToTime(self) ) {
        return;
    }
    
    if ( self.canPlayAnAsset && !self.canPlayAnAsset(self) ) {
        return;
    }
    
    if ( self.assetStatus != SJAssetStatusReadyToPlay ) {
        if ( completionHandler ) completionHandler(NO);
        return;
    }
    
    __weak typeof(self) _self = self;
    [self.playbackController seekToTime:time toleranceBefore:toleranceBefore toleranceAfter:toleranceAfter completionHandler:^(BOOL finished) {
        __strong typeof(_self) self = _self;
        if ( !self ) return;
        if ( finished && self.controlInfo->playbackControl.resumePlaybackWhenPlayerHasFinishedSeeking ) {
            [self play];
        }
        if ( completionHandler ) completionHandler(finished);
    }];
}

- (void)setRate:(float)rate {
    if ( self.canPlayAnAsset && !self.canPlayAnAsset(self) ) {
        return;
    }
    
    if ( _playbackController.rate == rate )
        return;
    
    self.playbackController.rate = rate;
}

- (float)rate {
    return self.playbackController.rate;
}

- (void)_updatePlayModelObserver:(SJPlayModel *)playModel {
    // clean
    _playModelObserver = nil;
    _controlInfo->scrollControl.isScrollAppeared = NO;
    
    if ( playModel == nil || [playModel isMemberOfClass:SJPlayModel.class] )
        return;
    
    // update playModel
    self.playModelObserver = [[SJPlayModelPropertiesObserver alloc] initWithPlayModel:playModel];
    self.playModelObserver.delegate = (id)self;
    [self.playModelObserver refreshAppearState];
}






#pragma mark - SJVideoPlayerPlaybackControllerDelegate

- (void)playbackController:(id<SJVideoPlayerPlaybackController>)controller assetStatusDidChange:(SJAssetStatus)status {
    if ( [self.controlLayerDelegate respondsToSelector:@selector(videoPlayerPlaybackStatusDidChange:)] ) {
        [self.controlLayerDelegate videoPlayerPlaybackStatusDidChange:self];
    }
    
    [self _postNotification:SJVideoPlayerAssetStatusDidChangeNotification];
#ifdef DEBUG
    [self showLog_AssetStatus];
#endif
}

- (void)playbackController:(id<SJVideoPlayerPlaybackController>)controller timeControlStatusDidChange:(SJPlaybackTimeControlStatus)status {
    BOOL isBuffering = self.isBuffering || self.assetStatus == SJAssetStatusPreparing;
    isBuffering ? [self.reachability startRefresh] : [self.reachability stopRefresh];
    
    if ( [self.controlLayerDelegate respondsToSelector:@selector(videoPlayerPlaybackStatusDidChange:)] ) {
        [self.controlLayerDelegate videoPlayerPlaybackStatusDidChange:self];
    }
    
    [self _postNotification:SJVideoPlayerPlaybackTimeControlStatusDidChangeNotification];
    
    if ( status == SJPlaybackTimeControlStatusPaused && self.pausedToKeepAppearState ) {
        [self.controlLayerAppearManager keepAppearState];
    }
    
#ifdef DEBUG
    [self showLog_TimeControlStatus];
#endif
}

- (void)playbackController:(id<SJVideoPlayerPlaybackController>)controller volumeDidChange:(float)volume {
    [self _postNotification:SJVideoPlayerVolumeDidChangeNotification];
}

- (void)playbackController:(id<SJVideoPlayerPlaybackController>)controller rateDidChange:(float)rate {
    if ( [self.controlLayerDelegate respondsToSelector:@selector(videoPlayer:rateChanged:)] ) {
        [self.controlLayerDelegate videoPlayer:self rateChanged:rate];
    }
    
    [self _postNotification:SJVideoPlayerRateDidChangeNotification];
}

- (void)playbackController:(id<SJVideoPlayerPlaybackController>)controller mutedDidChange:(BOOL)isMuted {
    if ( [self.controlLayerDelegate respondsToSelector:@selector(videoPlayer:muteChanged:)] ) {
        [self.controlLayerDelegate videoPlayer:self muteChanged:isMuted];
    }
    [self _postNotification:SJVideoPlayerMutedDidChangeNotification];
}

- (void)playbackController:(id<SJVideoPlayerPlaybackController>)controller pictureInPictureStatusDidChange:(SJPictureInPictureStatus)status API_AVAILABLE(ios(14.0)) {
    if ( [self.controlLayerDelegate respondsToSelector:@selector(videoPlayer:pictureInPictureStatusDidChange:)] ) {
        [self.controlLayerDelegate videoPlayer:self pictureInPictureStatusDidChange:status];
    }
    
    _deviceVolumeAndBrightnessTargetViewContext.isPictureInPictureMode = (status == SJPictureInPictureStatusRunning);
    [_deviceVolumeAndBrightnessController onTargetViewContextUpdated];
    
    [self _postNotification:SJVideoPlayerPictureInPictureStatusDidChangeNotification];
}

- (void)playbackController:(id<SJVideoPlayerPlaybackController>)controller durationDidChange:(NSTimeInterval)duration {
    if ( [self.controlLayerDelegate respondsToSelector:@selector(videoPlayer:durationDidChange:)] ) {
        [self.controlLayerDelegate videoPlayer:self durationDidChange:duration];
    }
    
    [self _postNotification:SJVideoPlayerDurationDidChangeNotification];
}

- (void)playbackController:(id<SJVideoPlayerPlaybackController>)controller currentTimeDidChange:(NSTimeInterval)currentTime {
    ///
    self.teachPlan.currentTime = currentTime;
    
    if ( [self.controlLayerDelegate respondsToSelector:@selector(videoPlayer:currentTimeDidChange:)] ) {
        [self.controlLayerDelegate videoPlayer:self currentTimeDidChange:currentTime];
    }
    
    [self _postNotification:SJVideoPlayerCurrentTimeDidChangeNotification];
}

- (void)playbackController:(id<SJVideoPlayerPlaybackController>)controller playbackDidFinish:(SJFinishedReason)reason {
    if ( _smallViewFloatingController.isAppeared && self.hiddenFloatSmallViewWhenPlaybackFinished ) {
        [_smallViewFloatingController dismiss];
    }
    
    if ( [self.controlLayerDelegate respondsToSelector:@selector(videoPlayerPlaybackStatusDidChange:)] ) {
        [self.controlLayerDelegate videoPlayerPlaybackStatusDidChange:self];
    }
    
    [self _postNotification:SJVideoPlayerPlaybackDidFinishNotification];
}

- (void)playbackController:(id<SJVideoPlayerPlaybackController>)controller presentationSizeDidChange:(CGSize)presentationSize {
    if ( [self.controlLayerDelegate respondsToSelector:@selector(videoPlayer:presentationSizeDidChange:)] ) {
        [self.controlLayerDelegate videoPlayer:self presentationSizeDidChange:presentationSize];
    }
    
    [self _postNotification:SJVideoPlayerPresentationSizeDidChangeNotification];
}

- (void)playbackController:(id<SJVideoPlayerPlaybackController>)controller playbackTypeDidChange:(SJPlaybackType)playbackType {
    self.teachPlan.playbackType = playbackType;
    if ( [self.controlLayerDelegate respondsToSelector:@selector(videoPlayer:playbackTypeDidChange:)] ) {
        [self.controlLayerDelegate videoPlayer:self playbackTypeDidChange:playbackType];
    }
    
    [self _postNotification:SJVideoPlayerPlaybackTypeDidChangeNotification];
}

- (void)playbackController:(id<SJVideoPlayerPlaybackController>)controller playableDurationDidChange:(NSTimeInterval)playableDuration {
    if ( controller.duration == 0 ) return;
    if ( [self.controlLayerDelegate respondsToSelector:@selector(videoPlayer:playableDurationDidChange:)] ) {
        [self.controlLayerDelegate videoPlayer:self playableDurationDidChange:playableDuration];
    }
    
    [self _postNotification:SJVideoPlayerPlayableDurationDidChangeNotification];
}

/**
准备播放
 */
- (void)playbackControllerIsReadyForDisplay:(id<SJVideoPlayerPlaybackController>)controller {
    [self _showOrHiddenPlaceholderImageViewIfNeeded];
    [self _postNotification:SJVideoPlayerReadyForDisplayNotification];
}

/**
 seek跳转
 */
- (void)playbackController:(id<SJVideoPlayerPlaybackController>)controller willSeekToTime:(CMTime)time {
    [self _postNotification:SJVideoPlayerPlaybackWillSeekNotification userInfo:@{
        SJVideoPlayerNotificationUserInfoKeySeekTime : [NSValue valueWithCMTime:time]
    }];
}

- (void)playbackController:(id<SJVideoPlayerPlaybackController>)controller didSeekToTime:(CMTime)time {
    [self _postNotification:SJVideoPlayerPlaybackDidSeekNotification userInfo:@{
        SJVideoPlayerNotificationUserInfoKeySeekTime : [NSValue valueWithCMTime:time]
    }];
}


/**
 切换清晰度
 */
- (void)playbackController:(id<SJVideoPlayerPlaybackController>)controller switchingDefinitionStatusDidChange:(SJDefinitionSwitchStatus)status media:(id<SJMediaModelProtocol>)media {
    if ( status == SJDefinitionSwitchStatusFinished ) {
        _URLAsset = (id)media;
        self.definitionSwitchingInfo.currentPlayingAsset = _URLAsset;
        [self _updateAssetObservers];
    }
    
    self.definitionSwitchingInfo.status = status;
    
    if ( [self.controlLayerDelegate respondsToSelector:@selector(videoPlayer:switchingDefinitionStatusDidChange:media:)] ) {
        [self.controlLayerDelegate videoPlayer:self switchingDefinitionStatusDidChange:status media:media];
    }
    
    [self _postNotification:SJVideoPlayerDefinitionSwitchStatusDidChangeNotification];
}

/**
 重播
 */
- (void)playbackController:(id<SJVideoPlayerPlaybackController>)controller didReplay:(id<SJMediaModelProtocol>)media {
    [self _postNotification:SJVideoPlayerPlaybackDidReplayNotification];
}

/**
 直播SEI 消息
 */
- (void)playbackController:(id<SJVideoPlayerPlaybackController>)controller SEIData:(id)data{
    ///判断在使用控制
    if (!self.useLessonPlanControl || self.playbackType != SJPlaybackTypeLIVE) { return; }
    ///判断设备是否连接
    if (![self.videoPlayData isConnectMachine]) { return; }
 
    ///直播指令
    CourseLinkModel *model = [CourseLinkModel modelWithDictionary:data];
    @weakify(self);
    void(^SEIDataBlock)(CourseLinkModel *switchModel) = ^(CourseLinkModel *switchModel){
        @strongify(self);
        if ( [self.controlLayerDelegate respondsToSelector:@selector(videoPlayer:SEIData:)] ) {
            [self.controlLayerDelegate videoPlayer:self SEIData:switchModel];
        }
    };
    NSArray *array = self.videoPlayData.planModel.courseLinkPOS.mutableCopy;
    [array enumerateObjectsUsingBlock:^(id  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        CourseLinkModel *switchModel = (CourseLinkModel *)obj;
        if ([switchModel.cid isEqualToString:model.cid]){
            SEIDataBlock(switchModel);
            *stop = YES;
        }
    }];
}

/**
 直播降级
 */
- (void)playbackControllerAliPlayerLiveDemotion:(id<SJVideoPlayerPlaybackController>)controller{
    
    SJVideoPlayerURLAsset *media = self.URLAsset;
    if (!media.isPlayRtsUrl){ return; } ///已经调用过降级
    
    AVPUrlSource *source = (AVPUrlSource *)media.source;
    if ([media.playUrl isNotBlank] && ![source.playerUrl.absoluteString isEqualToString:media.playUrl]){
        media.isPlayRtsUrl = NO;
        source.playerUrl = [NSURL URLWithString:media.playUrl];
    }
    
    [self.playbackController switchVideoDefinition:media];
}

- (void)applicationDidBecomeActiveWithPlaybackController:(id<SJVideoPlayerPlaybackController>)controller {
    
    BOOL canPlay = self.URLAsset != nil &&
                  !self.showPalyDoneLayer &&
                   self.isPaused &&
                   self.controlInfo->playbackControl.resumePlaybackWhenAppDidEnterForeground &&
                  !self.vc_isDisappeared;
    
    if ( self.isPlayOnScrollView ) {
        if ( canPlay && self.isScrollAppeared ) [self play];
    } else {
        if ( canPlay ) [self play];
    }
    
    if ( [self.controlLayerDelegate respondsToSelector:@selector(applicationDidBecomeActiveWithVideoPlayer:)] ) {
        [self.controlLayerDelegate applicationDidBecomeActiveWithVideoPlayer:self];
    }
}

- (void)applicationWillResignActiveWithPlaybackController:(id<SJVideoPlayerPlaybackController>)controller {
    if ( [self.controlLayerDelegate respondsToSelector:@selector(applicationWillResignActiveWithVideoPlayer:)] ) {
        [self.controlLayerDelegate applicationWillResignActiveWithVideoPlayer:self];
    }
}

- (void)applicationWillEnterForegroundWithPlaybackController:(id<SJVideoPlayerPlaybackController>)controller {
    if ( [self.controlLayerDelegate respondsToSelector:@selector(applicationDidEnterBackgroundWithVideoPlayer:)] ) {
        [self.controlLayerDelegate applicationDidEnterBackgroundWithVideoPlayer:self];
    }
    [self _postNotification:SJVideoPlayerApplicationWillEnterForegroundNotification];
}

- (void)applicationDidEnterBackgroundWithPlaybackController:(id<SJVideoPlayerPlaybackController>)controller {
    if ( [self.controlLayerDelegate respondsToSelector:@selector(applicationDidEnterBackgroundWithVideoPlayer:)] ) {
        [self.controlLayerDelegate applicationDidEnterBackgroundWithVideoPlayer:self];
    }
    [self _postNotification:SJVideoPlayerApplicationDidEnterBackgroundNotification];
    
    
    ///退到后台语音
    if (self.connectDevice && self.trainManager.productId.intValue == TreadmillEquipment) {
        jxt_getSafeMainQueue(^{
            [FeedbackGeneratorUtil playSoundWithTrainingReminder:@"Sound_001"];
        });
    }
}
@end


#pragma mark - Network

@implementation SJBaseVideoPlayer (Network)

- (void)setReachability:(id<SJReachability> _Nullable)reachability {
    _reachability = reachability;
    [self _needUpdateReachabilityProperties];
}

- (id<SJReachability>)reachability {
    if ( _reachability )
        return _reachability;
    
    _reachability = [SJReachability shared];
    [self _needUpdateReachabilityProperties];
    return _reachability;
}

- (void)_needUpdateReachabilityProperties {
    if ( _reachability == nil ) return;
    
    _reachabilityObserver = [_reachability getObserver];
    __weak typeof(self) _self = self;
    _reachabilityObserver.networkStatusDidChangeExeBlock = ^(id<SJReachability> r) {
        __strong typeof(_self) self = _self;
        if ( !self ) return;
        if ( [self.controlLayerDelegate respondsToSelector:@selector(videoPlayer:reachabilityChanged:)] ) {
            [self.controlLayerDelegate videoPlayer:self reachabilityChanged:r.networkStatus];
        }
    };
}

- (id<SJReachabilityObserver>)reachabilityObserver {
    id<SJReachabilityObserver> observer = objc_getAssociatedObject(self, _cmd);
    if ( observer == nil ) {
        observer = [self.reachability getObserver];
        objc_setAssociatedObject(self, _cmd, observer, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    }
    return observer;
}
@end





#pragma mark - DeviceVolumeAndBrightness

@implementation SJBaseVideoPlayer (DeviceVolumeAndBrightness)

- (void)setDeviceVolumeAndBrightnessController:(id<SJDeviceVolumeAndBrightnessController> _Nullable)deviceVolumeAndBrightnessController {
    _deviceVolumeAndBrightnessController = deviceVolumeAndBrightnessController;
    [self _configDeviceVolumeAndBrightnessController:self.deviceVolumeAndBrightnessController];
}

- (id<SJDeviceVolumeAndBrightnessController>)deviceVolumeAndBrightnessController {
    if ( _deviceVolumeAndBrightnessController )
        return _deviceVolumeAndBrightnessController;
    _deviceVolumeAndBrightnessController = [SJDeviceVolumeAndBrightnessController.alloc init];
    [self _configDeviceVolumeAndBrightnessController:_deviceVolumeAndBrightnessController];
    return _deviceVolumeAndBrightnessController;
}

- (void)_configDeviceVolumeAndBrightnessController:(id<SJDeviceVolumeAndBrightnessController>)mgr {
    mgr.targetViewContext = _deviceVolumeAndBrightnessTargetViewContext;
    mgr.target = self.presentView;
    _deviceVolumeAndBrightnessControllerObserver = [mgr getObserver];
    __weak typeof(self) _self = self;
    _deviceVolumeAndBrightnessControllerObserver.volumeDidChangeExeBlock = ^(id<SJDeviceVolumeAndBrightnessController>  _Nonnull mgr, float volume) {
        __strong typeof(_self) self = _self;
        if ( !self ) return ;
        if ( [self.controlLayerDelegate respondsToSelector:@selector(videoPlayer:volumeChanged:)] ) {
            [self.controlLayerDelegate videoPlayer:self volumeChanged:volume];
        }
    };
    
    _deviceVolumeAndBrightnessControllerObserver.brightnessDidChangeExeBlock = ^(id<SJDeviceVolumeAndBrightnessController>  _Nonnull mgr, float brightness) {
        __strong typeof(_self) self = _self;
        if ( !self ) return ;
        if ( [self.controlLayerDelegate respondsToSelector:@selector(videoPlayer:brightnessChanged:)] ) {
            [self.controlLayerDelegate videoPlayer:self brightnessChanged:brightness];
        }
    };
    
    [mgr onTargetViewMoveToWindow];
    [mgr onTargetViewContextUpdated];
}

- (id<SJDeviceVolumeAndBrightnessControllerObserver>)deviceVolumeAndBrightnessObserver {
    id<SJDeviceVolumeAndBrightnessControllerObserver> observer = objc_getAssociatedObject(self, _cmd);
    if ( observer == nil ) {
        observer = [self.deviceVolumeAndBrightnessController getObserver];
        objc_setAssociatedObject(self, _cmd, observer, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    }
    return observer;
}

- (void)setDisableBrightnessSetting:(BOOL)disableBrightnessSetting {
    _controlInfo->deviceVolumeAndBrightness.disableBrightnessSetting = disableBrightnessSetting;
}
- (BOOL)disableBrightnessSetting {
    return _controlInfo->deviceVolumeAndBrightness.disableBrightnessSetting;
}

- (void)setDisableVolumeSetting:(BOOL)disableVolumeSetting {
    _controlInfo->deviceVolumeAndBrightness.disableVolumeSetting = disableVolumeSetting;
}
- (BOOL)disableVolumeSetting {
    return _controlInfo->deviceVolumeAndBrightness.disableVolumeSetting;
}

@end



#pragma mark - Life

@implementation SJBaseVideoPlayer (Life)
/// You should call it when view did appear
- (void)vc_viewDidAppear {
    [self.viewControllerManager viewDidAppear];
    [self.playModelObserver refreshAppearState];
    
    if (!([self.videoPlayData isTestPlay] && self.currentTime >= 300)) { // 除（试播并超过时间，暂停不恢复播放）
        ///设备在视频页连接上的返回 播放
        if ((self.timeControlStatus == SJPlaybackTimeControlStatusPaused) && _URLAsset != nil) {
            [self play];
        }
    }
    
    if ( [self.controlLayerDelegate respondsToSelector:@selector(controllerDidAppeared:)] ) {
        [self.controlLayerDelegate controllerDidAppeared:self];
    }
}

/// You should call it when view will disappear
- (void)vc_viewWillDisappear {
    [self.viewControllerManager viewWillDisappear];
}

- (void)vc_viewDidDisappear {
    [self.viewControllerManager viewDidDisappear];
    [self pause];
    NSLog(@"vc_viewDidDisappear__________________");
    
    if ( [self.controlLayerDelegate respondsToSelector:@selector(controllerDidDisAppeared:)] ) {
        [self.controlLayerDelegate controllerDidDisAppeared:self];
    }
}

- (BOOL)vc_prefersStatusBarHidden {
    return self.viewControllerManager.prefersStatusBarHidden;
}

- (UIStatusBarStyle)vc_preferredStatusBarStyle {
    return self.viewControllerManager.preferredStatusBarStyle;
}

- (void)setVc_isDisappeared:(BOOL)vc_isDisappeared {
    vc_isDisappeared ?  [self.viewControllerManager viewWillDisappear] :
                        [self.viewControllerManager viewDidAppear];
}

- (BOOL)vc_isDisappeared {
    return self.viewControllerManager.isViewDisappeared;
}

- (void)needShowStatusBar {
    [self.viewControllerManager showStatusBar];
}

- (void)needHiddenStatusBar {
    [self.viewControllerManager hiddenStatusBar];
}
@end



#pragma mark - Gesture

@implementation SJBaseVideoPlayer (Gesture)

- (id<SJGestureController>)gestureController {
    return _presentView;
}

- (void)setGestureRecognizerShouldTrigger:(BOOL (^_Nullable)(__kindof SJBaseVideoPlayer * _Nonnull, SJPlayerGestureType, CGPoint))gestureRecognizerShouldTrigger {
    objc_setAssociatedObject(self, @selector(gestureRecognizerShouldTrigger), gestureRecognizerShouldTrigger, OBJC_ASSOCIATION_COPY_NONATOMIC);
}
- (BOOL (^_Nullable)(__kindof SJBaseVideoPlayer * _Nonnull, SJPlayerGestureType, CGPoint))gestureRecognizerShouldTrigger {
    return objc_getAssociatedObject(self, _cmd);
}

- (void)setAllowHorizontalTriggeringOfPanGesturesInCells:(BOOL)allowHorizontalTriggeringOfPanGesturesInCells {
    _controlInfo->gestureController.allowHorizontalTriggeringOfPanGesturesInCells = allowHorizontalTriggeringOfPanGesturesInCells;
}

- (BOOL)allowHorizontalTriggeringOfPanGesturesInCells {
    return _controlInfo->gestureController.allowHorizontalTriggeringOfPanGesturesInCells;
}

- (void)setRateWhenLongPressGestureTriggered:(CGFloat)rateWhenLongPressGestureTriggered {
    _controlInfo->gestureController.rateWhenLongPressGestureTriggered = rateWhenLongPressGestureTriggered;
}
- (CGFloat)rateWhenLongPressGestureTriggered {
    return _controlInfo->gestureController.rateWhenLongPressGestureTriggered;
}

- (void)setOffsetFactorForHorizontalPanGesture:(CGFloat)offsetFactorForHorizontalPanGesture {
    NSAssert(offsetFactorForHorizontalPanGesture != 0, @"The factor can't be set to 0!");
    _controlInfo->pan.factor = offsetFactorForHorizontalPanGesture;
}
- (CGFloat)offsetFactorForHorizontalPanGesture {
    return _controlInfo->pan.factor;
}
@end


#pragma mark - 控制层

@implementation SJBaseVideoPlayer (ControlLayer)
/// 控制层需要显示
- (void)controlLayerNeedAppear {
    [self.controlLayerAppearManager needAppear];
}

/// 控制层需要隐藏
- (void)controlLayerNeedDisappear {
    [self.controlLayerAppearManager needDisappear];
}

- (void)setControlLayerAppearManager:(id<SJControlLayerAppearManager> _Nullable)controlLayerAppearManager {
    [self _setupControlLayerAppearManager:controlLayerAppearManager];
}

- (id<SJControlLayerAppearManager>)controlLayerAppearManager {
    if ( _controlLayerAppearManager == nil ) {
        [self _setupControlLayerAppearManager:SJControlLayerAppearStateManager.alloc.init];
    }
    return _controlLayerAppearManager;
}

- (id<SJControlLayerAppearManagerObserver>)controlLayerAppearObserver {
    id<SJControlLayerAppearManagerObserver> observer = objc_getAssociatedObject(self, _cmd);
    if ( observer == nil ) {
        observer = [self.controlLayerAppearManager getObserver];
        objc_setAssociatedObject(self, _cmd, observer, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    }
    return observer;
}

- (void)setCanAutomaticallyDisappear:(BOOL (^_Nullable)(__kindof SJBaseVideoPlayer * _Nonnull))canAutomaticallyDisappear {
    objc_setAssociatedObject(self, @selector(canAutomaticallyDisappear), canAutomaticallyDisappear, OBJC_ASSOCIATION_COPY_NONATOMIC);
}
- (BOOL (^_Nullable)(__kindof SJBaseVideoPlayer * _Nonnull))canAutomaticallyDisappear {
    return objc_getAssociatedObject(self, _cmd);
}

/// 控制层是否显示
- (void)setControlLayerAppeared:(BOOL)controlLayerAppeared {
    controlLayerAppeared ? [self.controlLayerAppearManager needAppear] :
                           [self.controlLayerAppearManager needDisappear];
}
- (BOOL)isControlLayerAppeared {
    return self.controlLayerAppearManager.isAppeared;
}

/// 暂停时是否保持控制层一直显示
- (void)setPausedToKeepAppearState:(BOOL)pausedToKeepAppearState {
    _controlInfo->controlLayer.pausedToKeepAppearState = pausedToKeepAppearState;
}

- (BOOL)pausedToKeepAppearState {
    return _controlInfo->controlLayer.pausedToKeepAppearState;
}
@end



#pragma mark - 充满屏幕

@implementation SJBaseVideoPlayer (FitOnScreen)
- (void)setFitOnScreenManager:(id<SJFitOnScreenManager> _Nullable)fitOnScreenManager {
    [self _setupFitOnScreenManager:fitOnScreenManager];
}

- (id<SJFitOnScreenManager>)fitOnScreenManager {
    if ( _fitOnScreenManager == nil ) {
        [self _setupFitOnScreenManager:[[SJFitOnScreenManager alloc] initWithTarget:self.presentView targetSuperview:self.view]];
    }
    return _fitOnScreenManager;
}

- (id<SJFitOnScreenManagerObserver>)fitOnScreenObserver {
    id<SJFitOnScreenManagerObserver> observer = objc_getAssociatedObject(self, _cmd);
    if ( observer == nil ) {
        observer = [self.fitOnScreenManager getObserver];
        objc_setAssociatedObject(self, _cmd, observer, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    }
    return observer;
}

- (void)setOnlyFitOnScreen:(BOOL)onlyFitOnScreen {
    objc_setAssociatedObject(self, @selector(onlyFitOnScreen), @(onlyFitOnScreen), OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    if ( onlyFitOnScreen ) {
        [self _clearRotationManager];
    }
    else {
        [self rotationManager];
    }
}

- (BOOL)onlyFitOnScreen {
    return [objc_getAssociatedObject(self, _cmd) boolValue];
}

- (BOOL)isFitOnScreen {
    return self.fitOnScreenManager.isFitOnScreen;
}
- (void)setFitOnScreen:(BOOL)fitOnScreen {
    [self setFitOnScreen:fitOnScreen animated:YES];
}
- (void)setFitOnScreen:(BOOL)fitOnScreen animated:(BOOL)animated {
    [self setFitOnScreen:fitOnScreen animated:animated completionHandler:nil];
}
- (void)setFitOnScreen:(BOOL)fitOnScreen animated:(BOOL)animated completionHandler:(nullable void(^)(__kindof SJBaseVideoPlayer *player))completionHandler {
    NSAssert(!self.isFullscreen, @"横屏全屏状态下, 无法执行竖屏全屏!");
    
    __weak typeof(self) _self = self;
    [self.fitOnScreenManager setFitOnScreen:fitOnScreen animated:animated completionHandler:^(id<SJFitOnScreenManager> mgr) {
        __strong typeof(_self) self = _self;
        if ( !self ) return;
        if ( completionHandler ) completionHandler(self);
    }];
}
@end
















#pragma mark - 屏幕旋转

@implementation SJBaseVideoPlayer (Rotation)

- (void)setRotationManager:(nullable id<SJRotationManager>)rotationManager {
    [self _setupRotationManager:rotationManager];
}

- (nullable id<SJRotationManager>)rotationManager {
    if ( _rotationManager == nil && !self.onlyFitOnScreen ) {
        SJRotationManager *defaultManager = [SJRotationManager rotationManager];
        defaultManager.actionForwarder = self.viewControllerManager;
        [self _setupRotationManager:defaultManager];
    }
    return _rotationManager;
}

- (id<SJRotationManagerObserver>)rotationObserver {
    id<SJRotationManagerObserver> observer = objc_getAssociatedObject(self, _cmd);
    if ( observer == nil ) {
        observer = [self.rotationManager getObserver];
        objc_setAssociatedObject(self, _cmd, observer, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    }
    return observer;
}

- (void)setShouldTriggerRotation:(BOOL (^_Nullable)(__kindof SJBaseVideoPlayer * _Nonnull))shouldTriggerRotation {
    objc_setAssociatedObject(self, @selector(shouldTriggerRotation), shouldTriggerRotation, OBJC_ASSOCIATION_COPY_NONATOMIC);
}
- (BOOL (^_Nullable)(__kindof SJBaseVideoPlayer * _Nonnull))shouldTriggerRotation {
    return objc_getAssociatedObject(self, _cmd);
}

- (void)setAllowsRotationInFitOnScreen:(BOOL)allowsRotationInFitOnScreen {
    objc_setAssociatedObject(self, @selector(allowsRotationInFitOnScreen), @(allowsRotationInFitOnScreen), OBJC_ASSOCIATION_RETAIN_NONATOMIC);
}
- (BOOL)allowsRotationInFitOnScreen {
    return [objc_getAssociatedObject(self, _cmd) boolValue];
}

- (void)rotate {
    [self.rotationManager rotate];
}

- (void)rotate:(SJOrientation)orientation animated:(BOOL)animated {
    [self.rotationManager rotate:orientation animated:animated];
}

- (void)rotate:(SJOrientation)orientation animated:(BOOL)animated completion:(void (^ _Nullable)(__kindof SJBaseVideoPlayer *player))block {
    __weak typeof(self) _self = self;
    [self.rotationManager rotate:orientation animated:animated completionHandler:^(id<SJRotationManager>  _Nonnull mgr) {
        __strong typeof(_self) self = _self;
        if ( !self ) return;
        if ( block ) block(self);
    }];
}

- (BOOL)isRotating {
    return _rotationManager.isRotating;
}

- (BOOL)isFullscreen {
    return _rotationManager.isFullscreen;
}

- (UIInterfaceOrientation)currentOrientation {
    return (NSInteger)_rotationManager.currentOrientation;
}

- (void)setLockedScreen:(BOOL)lockedScreen {
    if ( lockedScreen != self.isLockedScreen ) {
        self.viewControllerManager.lockedScreen = lockedScreen;
        objc_setAssociatedObject(self, @selector(isLockedScreen), @(lockedScreen), OBJC_ASSOCIATION_RETAIN_NONATOMIC);
        
        if      ( lockedScreen && [self.controlLayerDelegate respondsToSelector:@selector(lockedVideoPlayer:)] ) {
            [self.controlLayerDelegate lockedVideoPlayer:self];
        }
        else if ( !lockedScreen && [self.controlLayerDelegate respondsToSelector:@selector(unlockedVideoPlayer:)] ) {
            [self.controlLayerDelegate unlockedVideoPlayer:self];
        }
        
        [self _postNotification:SJVideoPlayerScreenLockStateDidChangeNotification];
    }
}

- (BOOL)isLockedScreen {
    return [objc_getAssociatedObject(self, _cmd) boolValue];
}
@end







#pragma mark - 在`tableView`或`collectionView`上播放

@implementation SJBaseVideoPlayer (ScrollView)
- (void)refreshAppearStateForPlayerView {
    [self.playModelObserver refreshAppearState];
}

- (void)setSmallViewFloatingController:(nullable id<SJSmallViewFloatingController>)smallViewFloatingController {
    [self _setupSmallViewFloatingController:smallViewFloatingController];
}

- (id<SJSmallViewFloatingController>)smallViewFloatingController {
    if ( _smallViewFloatingController == nil ) {
        __weak typeof(self) _self = self;
        SJSmallViewFloatingController *controller = SJSmallViewFloatingController.alloc.init;
        controller.floatingViewShouldAppear = ^BOOL(id<SJSmallViewFloatingController>  _Nonnull controller) {
            __strong typeof(_self) self = _self;
            if ( !self ) return NO;
            return self.timeControlStatus != SJPlaybackTimeControlStatusPaused && self.assetStatus != SJAssetStatusUnknown;
        };
        [self _setupSmallViewFloatingController:controller];
    }
    return _smallViewFloatingController;
}

- (void)setHiddenFloatSmallViewWhenPlaybackFinished:(BOOL)hiddenFloatSmallViewWhenPlaybackFinished {
    _controlInfo->floatSmallViewControl.hiddenFloatSmallViewWhenPlaybackFinished = hiddenFloatSmallViewWhenPlaybackFinished;
}
- (BOOL)isHiddenFloatSmallViewWhenPlaybackFinished {
    return _controlInfo->floatSmallViewControl.hiddenFloatSmallViewWhenPlaybackFinished;
}

- (void)setPausedWhenScrollDisappeared:(BOOL)pausedWhenScrollDisappeared {
    _controlInfo->scrollControl.pausedWhenScrollDisappeared = pausedWhenScrollDisappeared;
}
- (BOOL)pausedWhenScrollDisappeared {
    return _controlInfo->scrollControl.pausedWhenScrollDisappeared;
}

- (void)setHiddenViewWhenScrollDisappeared:(BOOL)hiddenViewWhenScrollDisappeared {
    _controlInfo->scrollControl.hiddenPlayerViewWhenScrollDisappeared = hiddenViewWhenScrollDisappeared;
}
- (BOOL)hiddenViewWhenScrollDisappeared {
    return _controlInfo->scrollControl.hiddenPlayerViewWhenScrollDisappeared;
}

- (void)setResumePlaybackWhenScrollAppeared:(BOOL)resumePlaybackWhenScrollAppeared {
    _controlInfo->scrollControl.resumePlaybackWhenScrollAppeared = resumePlaybackWhenScrollAppeared;
}
- (BOOL)resumePlaybackWhenScrollAppeared {
    return _controlInfo->scrollControl.resumePlaybackWhenScrollAppeared;
}

- (BOOL)isPlayOnScrollView {
    return self.playModelObserver.isPlayInScrollView;
}

- (BOOL)isScrollAppeared {
    return _controlInfo->scrollControl.isScrollAppeared;
}

- (void)setPlayerViewWillAppearExeBlock:(void (^_Nullable)(__kindof SJBaseVideoPlayer * _Nonnull))playerViewWillAppearExeBlock {
    objc_setAssociatedObject(self, @selector(playerViewWillAppearExeBlock), playerViewWillAppearExeBlock, OBJC_ASSOCIATION_COPY_NONATOMIC);
}
- (void (^_Nullable)(__kindof SJBaseVideoPlayer * _Nonnull))playerViewWillAppearExeBlock {
    return objc_getAssociatedObject(self, _cmd);
}

- (void)setPlayerViewWillDisappearExeBlock:(void (^_Nullable)(__kindof SJBaseVideoPlayer * _Nonnull))playerViewWillDisappearExeBlock {
    objc_setAssociatedObject(self, @selector(playerViewWillDisappearExeBlock), playerViewWillDisappearExeBlock, OBJC_ASSOCIATION_COPY_NONATOMIC);
}
- (void (^_Nullable)(__kindof SJBaseVideoPlayer * _Nonnull))playerViewWillDisappearExeBlock {
    return objc_getAssociatedObject(self, _cmd);
}
@end












#pragma mark - 提示层数据

@implementation SJBaseVideoPlayer (PromptControl)

- (void)setPrompt:(nullable id<SJPromptProtocol>)prompt {
    objc_setAssociatedObject(self, @selector(prompt), prompt, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    [self _setupPrompt];
}
- (id<SJPromptProtocol>)prompt {
    id<SJPromptProtocol> prompt = objc_getAssociatedObject(self, _cmd);
    if ( prompt == nil ) {
        prompt = SJPrompt.alloc.init;
        objc_setAssociatedObject(self, _cmd, prompt, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
        [self _setupPrompt];
    }
    return prompt;
}
- (void)_setupPrompt {
    id<SJPromptProtocol> prompt = objc_getAssociatedObject(self, @selector(prompt));
    prompt.target = self.presentView;
}

///======================================================================================================
- (void)setScorePrompt:(nullable SJScorePrompt *)scorePrompt {
    objc_setAssociatedObject(self, @selector(scorePrompt), scorePrompt, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    [self _setupScorePrompt];
}
- (SJScorePrompt *)scorePrompt {
    SJScorePrompt *scorePrompt = objc_getAssociatedObject(self, _cmd);
    if ( scorePrompt == nil ) {
        scorePrompt = SJScorePrompt.alloc.init;
        objc_setAssociatedObject(self, _cmd, scorePrompt, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
        [self _setupScorePrompt];
    }
    return scorePrompt;
}
- (void)_setupScorePrompt {
    SJScorePrompt *prompt = objc_getAssociatedObject(self, @selector(scorePrompt));
    prompt.target = self.presentView;
}

///======================================================================================================
- (void)setLivingPrompt:(nullable SJLivingGreetPrompt *)livingPrompt {
    objc_setAssociatedObject(self, @selector(livingPrompt), livingPrompt, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    [self _setupLivingPrompt];
}
- (SJLivingGreetPrompt *)livingPrompt {
    SJLivingGreetPrompt *livingPrompt = objc_getAssociatedObject(self, _cmd);
    if ( livingPrompt == nil ) {
        livingPrompt = SJLivingGreetPrompt.alloc.init;
        objc_setAssociatedObject(self, _cmd, livingPrompt, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
        [self _setupLivingPrompt];
    }
    return livingPrompt;
}
- (void)_setupLivingPrompt{
    SJLivingGreetPrompt *prompt = objc_getAssociatedObject(self, @selector(livingPrompt));
    prompt.target = self.presentView;
}

///======================================================================================================
- (void)setMatchingPrompt:(nullable MRKDeviceMatchingPrompt *)matchingPrompt {
    objc_setAssociatedObject(self, @selector(matchingPrompt), matchingPrompt, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    [self _setupMatchingPrompt];
}
- (MRKDeviceMatchingPrompt *)matchingPrompt {
    MRKDeviceMatchingPrompt *matchingPrompt = objc_getAssociatedObject(self, _cmd);
    if ( matchingPrompt == nil ) {
        matchingPrompt = MRKDeviceMatchingPrompt.alloc.init;
        objc_setAssociatedObject(self, _cmd, matchingPrompt, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
        [self _setupMatchingPrompt];
    }
    return matchingPrompt;
}
- (void)_setupMatchingPrompt {
    MRKDeviceMatchingPrompt *prompt = objc_getAssociatedObject(self, @selector(matchingPrompt));
    prompt.target = self.presentView;
}

///======================================================================================================
- (void)setGamePrompt:(nullable MRKGameAlertPrompt *)gamePrompt {
    objc_setAssociatedObject(self, @selector(gamePrompt), gamePrompt, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    [self _setupGamePrompt];
}
- (MRKGameAlertPrompt *)gamePrompt {
    MRKGameAlertPrompt *gamePrompt = objc_getAssociatedObject(self, _cmd);
    if ( gamePrompt == nil ) {
        gamePrompt = MRKGameAlertPrompt.alloc.init;
        objc_setAssociatedObject(self, _cmd, gamePrompt, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
        [self _setupGamePrompt];
    }
    return gamePrompt;
}
- (void)_setupGamePrompt {
    MRKGameAlertPrompt *prompt = objc_getAssociatedObject(self, @selector(gamePrompt));
    prompt.target = self.presentView;
}

///======================================================================================================
- (void)setPromptPopupController:(nullable id<SJPromptPopupController>)promptPopupController {
    objc_setAssociatedObject(self, @selector(promptPopupController), promptPopupController, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    if ( promptPopupController != nil ) {
        [self _setupPromptPopupController];
    }
}
- (id<SJPromptPopupController>)promptPopupController {
    id<SJPromptPopupController>_Nullable controller = objc_getAssociatedObject(self, _cmd);
    if ( controller == nil ) {
        controller = [SJPromptPopupController new];
        objc_setAssociatedObject(self, _cmd, controller, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
        [self _setupPromptPopupController];
    }
    return controller;
}
- (void)_setupPromptPopupController {
    id<SJPromptPopupController>_Nullable controller = objc_getAssociatedObject(self, @selector(promptPopupController));
    controller.target = self.presentView;
}

///======================================================================================================
- (void)setSuggestValuePrompt:(nullable MRKSuggestValuePrompt *)suggestValuePrompt {
    objc_setAssociatedObject(self, @selector(suggestValuePrompt), suggestValuePrompt, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    [self _setupSuggestPrompt];
}
- (MRKSuggestValuePrompt *)suggestValuePrompt {
    MRKSuggestValuePrompt *suggestValuePrompt = objc_getAssociatedObject(self, _cmd);
    if ( suggestValuePrompt == nil ) {
        suggestValuePrompt = MRKSuggestValuePrompt.alloc.init;
        objc_setAssociatedObject(self, _cmd, suggestValuePrompt, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
        [self _setupSuggestPrompt];
    }
    return suggestValuePrompt;
}
- (void)_setupSuggestPrompt {
    MRKSuggestValuePrompt *prompt = objc_getAssociatedObject(self, @selector(suggestValuePrompt));
    prompt.target = self.presentView;
}

@end





@implementation SJBaseVideoPlayer (PlayData)

- (void)setUseLessonPlanControl:(BOOL)useLessonPlanControl{
    if ( useLessonPlanControl != self.useLessonPlanControl ) {
        objc_setAssociatedObject(self, @selector(isUseLessonPlanControl), @(useLessonPlanControl), OBJC_ASSOCIATION_RETAIN_NONATOMIC);
        ///
        self.teachPlan.useLessonPlanControl = useLessonPlanControl;
        
        if ( [self.controlLayerDelegate respondsToSelector:@selector(videoPlayer:useLessonPlanControl:)] ) {
            [self.controlLayerDelegate videoPlayer:self useLessonPlanControl:useLessonPlanControl];
        }
    }
}
- (BOOL)isUseLessonPlanControl {
    return [objc_getAssociatedObject(self, _cmd) boolValue];
}


- (void)setHasAlertWarnContrlLayer:(BOOL)hasAlertWarnContrlLayer{
    if ( hasAlertWarnContrlLayer != self.hasAlertWarnContrlLayer ) {
        objc_setAssociatedObject(self, @selector(isHasAlertWarnContrlLayer), @(hasAlertWarnContrlLayer), OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    }
}
- (BOOL)isHasAlertWarnContrlLayer {
    return [objc_getAssociatedObject(self, _cmd) boolValue];
}


- (void)setCloseBarrageControl:(BOOL)closeBarrageControl{
    if ( closeBarrageControl != self.closeBarrageControl ) {
        objc_setAssociatedObject(self, @selector(isCloseBarrageControl), @(closeBarrageControl), OBJC_ASSOCIATION_RETAIN_NONATOMIC);
        if ( [self.controlLayerDelegate respondsToSelector:@selector(videoPlayer:closeBarrageControl:)] ) {
            [self.controlLayerDelegate videoPlayer:self closeBarrageControl:closeBarrageControl];
        }
    }
}
- (BOOL)isCloseBarrageControl {
    return [objc_getAssociatedObject(self, _cmd) boolValue];
}


- (void)setAdaptScreenSizeOn:(BOOL)adaptScreenSizeOn{
    if ( adaptScreenSizeOn != self.adaptScreenSizeOn ) {
        objc_setAssociatedObject(self, @selector(isAdaptScreenSizeOn), @(adaptScreenSizeOn), OBJC_ASSOCIATION_RETAIN_NONATOMIC);
        ///修改时对应的缓存出处理
        self.videoPlayData.videoAdaptScreen = adaptScreenSizeOn;
    }
}
- (BOOL)isAdaptScreenSizeOn {
    return [objc_getAssociatedObject(self, _cmd) boolValue];
}


- (void)setConnectDevice:(BOOL)connectDevice{
    if ( connectDevice != self.connectDevice ) {
        objc_setAssociatedObject(self, @selector(isConnectDevice), @(connectDevice), OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    }
}
- (BOOL)isConnectDevice {
    return [objc_getAssociatedObject(self, _cmd) boolValue];
}


- (void)setCourseModel:(nullable MRKCourseModel *)courseModel{
    objc_setAssociatedObject(self, @selector(courseModel), courseModel, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    if ( courseModel != nil ) {
        self.teachPlan.courseModel = courseModel;
    }
}
- (nullable MRKCourseModel *)courseModel{
    return objc_getAssociatedObject(self, _cmd);
}


- (void)setVideoPlayData:(nullable MRKVideoPrepareDataInfo *)videoPlayData {
    objc_setAssociatedObject(self, @selector(videoPlayData), videoPlayData, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    if ( videoPlayData != nil ) {
        ///
        self.trainManager.playbackType = videoPlayData.playbackType;
        
//        self.blueManager.playbackType = videoPlayData.playbackType;
//        self.blueManager.eqModel = videoPlayData.eqModel;
//        self.blueManager.useRateKcal = videoPlayData.useRateKcal;
        
        self.burningRate.eqModel = videoPlayData.eqModel;
        self.burningRate.planModel = videoPlayData.planModel;
        
        self.teachPlan.videoPlayData = videoPlayData;
        self.teachPlan.playbackType = videoPlayData.playbackType;
        
        ///竖屏的暂时屏蔽排行榜数据
        if (videoPlayData.liveModel.videoMediaType.integerValue != 2) {
            ///排行榜请求类型
            self.rankDataManager.requestType = videoPlayData.isMeritControl ? 1 : 2;
        }
        
        ///初始设备链接状态
        DEVICE_CONNECT_STATUS status = videoPlayData.isConnectMachine ?  DeviceConnected : DeviceDisconnect;
        [self updateDeviceStatus:status];
    }
}
- (nullable MRKVideoPrepareDataInfo *)videoPlayData {
    return objc_getAssociatedObject(self, _cmd);
}

@end





@implementation SJBaseVideoPlayer (Socket)
- (void)setSocketManager:(nullable SocketRocketManager*)socketManager {
    if ( _socketManager != nil ) _socketManager = nil;
    
    _socketManager = socketManager;
    if ( socketManager != nil ) {
        //        [socketManager mrk_opensocket]; 放外部启动
        @weakify(self);
        [socketManager setReceiveBlock:^(SRWebSocket *socket, id data) {
            [self_weak_ webSocket:socket didReceiveMessage:data];
        }];
    }
}

- (nullable SocketRocketManager *)socketManager{
    SocketRocketManager *socketManager = _socketManager;
    if ( socketManager == nil ) {
        socketManager = [SocketRocketManager.alloc init];
        socketManager.liveModel = self.courseModel;
        [self setSocketManager:socketManager];
    }
    return socketManager;
}

- (void)webSocket:(SRWebSocket *)webSocket didReceiveMessage:(id)message{
    NSDictionary *parm = [MRKToolKit dictORArrayFromJsonString:message];
    
    
    NSLog(@"showDict === %@",parm);
    NSInteger type = [[parm objectForKey:@"com_type"] integerValue];
    switch (type) {
        case 2:{///弹幕接收
            ///
            if ( [self.controlLayerDelegate respondsToSelector:@selector(videoPlayer:receiveDanmu:)] ) {
                [self.controlLayerDelegate videoPlayer:self receiveDanmu:parm];
            }
        }break;
        case 5:{///拉取运动排名

        }break;
        case 6:{///拉取在线情况
  
        }break;
        case 7:{///推流结束
            NSLog(@"webSocket didReceiveMessage 直播推流结束");
        }break;
        case 8:{///直播开始
            ///
            NSLog(@"webSocket didReceiveMessage 直播开始");
        }break;
        default: break;
    }
    
    
    NSInteger bizType = [[parm objectForKey:@"bizType"] integerValue];
    switch (bizType) {
        case 4: {///直播场景
            NSString *message = [parm objectForKey:@"message"];
            if ([message isNotBlank]) {
                NSDictionary *data = [message rts_toDictionary];
                ///messageType 直播场景类型/ 3 标识为商品
                NSInteger messageType = [[data objectForKey:@"messageType"] integerValue];
                if (messageType == 3) {
                    id content = [data objectForKey:@"content"];
                    MRKCourseGoodsModel *model = [MRKCourseGoodsModel modelWithJSON:content];
                    model.isLivingCourse = YES;
                    if ( [self.controlLayerDelegate respondsToSelector:@selector(videoPlayer:courseGoods:)] ) {
                        [self.controlLayerDelegate videoPlayer:self courseGoods:model];
                    }
                }
            }
        } break;
        default: break;
    }
}

@end







@implementation SJBaseVideoPlayer (SportRankDataManager)

- (nullable MRKSportListDataManager *)rankDataManager{
    MRKSportListDataManager *rankDataManager = _rankDataManager;
    if ( rankDataManager == nil ) {
        rankDataManager = [MRKSportListDataManager.alloc init];
        rankDataManager.liveModel = self.courseModel;
        rankDataManager.delegate = self;
        [self setRankDataManager:rankDataManager];
    }
    return rankDataManager;
}

- (void)setRankDataManager:(nullable MRKSportListDataManager*)rankDataManager {
    if ( _rankDataManager != nil ){
        _rankDataManager = nil;
    }
    _rankDataManager = rankDataManager;
    if ( rankDataManager != nil ) {
        rankDataManager.delegate = self;
    }
}

/**设备id */
- (NSString *)equipmentIdd{
    return self.videoPlayData.eqModel.idd;
}

/**当前小节id */
- (NSString *)linkIdd{
    return self.linkid;
}

/**直播排行榜数据 */
- (void)manager:(MRKSportListDataManager *)manager type:(NSInteger)type withLIVERankData:(MRKRankDataModel *)model{
    if ( [self.controlLayerDelegate respondsToSelector:@selector(videoPlayer:type:LIVERankData:)] ) {
        [self.controlLayerDelegate videoPlayer:self type:type LIVERankData:model];
    }
}

/**录播排行榜数据 */
- (void)manager:(MRKSportListDataManager *)manager type:(NSInteger)type withVODRankData:(MRKRankDataModel *)model{
    if ( [self.controlLayerDelegate respondsToSelector:@selector(videoPlayer:type:VODRankData:)] ) {
        [self.controlLayerDelegate videoPlayer:self type:type VODRankData:model];
    }
}

/**录播增量数据 */
- (void)manager:(MRKSportListDataManager *)manager type:(NSInteger)type withCourseKcalIncremenData:(SportListModel *)model{
    ///[1-燃脂榜单，2-消耗榜单]
    if (type == 1){
        model.data = [self.burningRate.totalBurningRate stringValue];
    }else{
        model.data = self.burningRate.displayModel.totalKcal;
    }
    if ( [self.controlLayerDelegate respondsToSelector:@selector(videoPlayer:type:kcalIncremenData:)] ) {
        [self.controlLayerDelegate videoPlayer:self type:type kcalIncremenData:model];
    }
}

/** 竞赛增量数据 */
- (void)manager:(MRKSportListDataManager *)manager withGameLIVERankData:(SportListModel *)model{
    if ( [self.controlLayerDelegate respondsToSelector:@selector(videoPlayer:withGameLIVERankData:)] ) {
        [self.controlLayerDelegate videoPlayer:self withGameLIVERankData:model];
    }
}

/**请求错误 */
- (void)managerRequestError:(MRKSportListDataManager *)manager type:(NSInteger)type{
    if ( [self.controlLayerDelegate respondsToSelector:@selector(videoPlayerRankRequestError:type:)] ) {
        [self.controlLayerDelegate videoPlayerRankRequestError:self type:type];
    }
}

@end






@implementation SJBaseVideoPlayer (TeachPlan)

- (void)setTeachPlan:(nullable MRKVideoTeachingPlan *)teachPlan{
    if (_teachPlan != nil) {
        _teachPlan = nil;
    }
    
    _teachPlan = teachPlan;
    if (teachPlan != nil) {
        teachPlan.delegate = self;
        teachPlan.dataSource = self;
    }
}

- (nullable MRKVideoTeachingPlan *)teachPlan{
    MRKVideoTeachingPlan *teachPlan = _teachPlan;
    if (teachPlan == nil) {
        teachPlan = [MRKVideoTeachingPlan.alloc init];
        teachPlan.delegate = self;
        teachPlan.dataSource = self;
        [self setTeachPlan:teachPlan];
    }
    return teachPlan;
}

/** 播放时长 */
- (NSTimeInterval)videoPlayerPlayWatchTime{
    return _playbackController.durationWatched;
}

/** 录播弹幕数据 */
- (void)VODVideoReceiveDanmuData:(id)data{
    if ( [self.controlLayerDelegate respondsToSelector:@selector(videoPlayer:receiveDanmu:)] ) {
        [self.controlLayerDelegate videoPlayer:self receiveDanmu:data];
    }
}

/** 录播试看完回调 */
- (void)VODVideoTryAndSeePlayDone{
    [self.playbackController pause];
    ///log
    ReportMrkLogParms(1, @"试看5分钟结束", @"page_vip_open", @"", nil, 0, @{@"courseId": self.videoPlayData.liveModel.courseId?:@"", @"course_state": self.tracePlaybackType});
    [self switchControlLayerWithIdentifier:SJControlLayer_VideoVipHolder];
}

/** 录播接收到指令 */
- (void)VODVideoReceiveElectromagneticControlOrder:(id)data{
    if ( [self.controlLayerDelegate respondsToSelector:@selector(videoPlayer:andCommandModel:)] ) {
        [self.controlLayerDelegate videoPlayer:self andCommandModel:data];
    }
}

/** 小件消耗数据返回 */
- (void)smallEquipmentKcalData:(BaseEquipDataModel *)obj{
    ///发送小件数据
   [[NSNotificationCenter defaultCenter] postNotificationName:@"VideoNotSupportConnectData" object:obj];
}

/**下一小节的数据 */
- (void)nextCourseLinkPOS:(CourseLinkModel *)obj{
    ///超燃脂率切换小节
    [self.burningRate nextCourseLinkPOS:obj];
    
    ///厉跨小节后设置YES
    self.trainManager.changeIndex = YES;
    self.trainManager.couseLinkModel = obj;
    
    ///当前小节id
    self.linkid = obj.cid;
    if ([self.controlLayerDelegate respondsToSelector:@selector(videoPlayer:nextCourseLinkPOS:)] ) {
        [self.controlLayerDelegate videoPlayer:self nextCourseLinkPOS:obj];
    }
}

/** 下一大节的数据 */
- (void)nextCourseCataloguePOS:(CourseCataloguePOSModel *)obj{
    if ([self.controlLayerDelegate respondsToSelector:@selector(videoPlayer:nextCourseCataloguePOS:)] ) {
        [self.controlLayerDelegate videoPlayer:self nextCourseCataloguePOS:obj];
    }
}

/** 播放时长 */
- (void)videoPlayerPlayTime:(NSTimeInterval)playTime{
    self.trainManager.duration = self.duration;
    self.trainManager.playtime = playTime;
    
}

/**每秒超燃脂数据*/
- (void)superFatBurningRateSeconds:(CourseLinkModel *)obj time:(NSTimeInterval)currentTime{
    [self.burningRate preBurningRate:currentTime modleId:obj.cid];
}

/** 超燃脂率数据 */
- (void)superFatBurningRate:(CourseLinkModel *)obj{
    self.burningRate.trainingRecordId = self.trainManager.trainingRecordId;
    @weakify(self);
    [self.burningRate fatBurningRequestWith:obj.cid sucess:^(MRKFatBurningModel * _Nullable model) {
        @strongify(self);
        if ( [self.controlLayerDelegate respondsToSelector:@selector(videoPlayer:fatBurningModel:)] ) {
            [self.controlLayerDelegate videoPlayer:self fatBurningModel:model];
        }
    }];
}

/**商品显示逻辑*/
- (void)VODVideoCourseGoods:(MRKCourseGoodsModel *)model{
    if ( [self.controlLayerDelegate respondsToSelector:@selector(videoPlayer:courseGoods:)] ) {
        [self.controlLayerDelegate videoPlayer:self courseGoods:model];
    }
}

@end





@implementation SJBaseVideoPlayer (TrainData)

- (MRKVideoTrainManager *)trainManager {
    MRKVideoTrainManager *trainManager = _trainManager;
    if ( trainManager == nil ) {
        trainManager = [[MRKVideoTrainManager alloc] initWithModel:self.courseModel];
        trainManager.delegate = self;
        [self setTrainManager:trainManager];
    }
    return trainManager;
}

- (void)setTrainManager:(nullable MRKVideoTrainManager *)trainManager {
    if (_trainManager != nil) {
        _trainManager = nil;
    }
    
    _trainManager = trainManager;
    if ( trainManager != nil ) {
        _trainManager.delegate = self;
    }
}

#pragma mark - MRKVideoTrainManagerDelegate
///视频暂停
- (void)videoPause {
    dispatch_async(dispatch_get_main_queue(), ^{
        [self devicePauseVideo];
    });
}

///视频播放
- (void)videoResume {
    dispatch_async(dispatch_get_main_queue(), ^{
        [self play];
    });
}

///更新设备UI
- (void)updateDeviceUI:(TrainingShowData *)model {
    if (self.playbackType == SJPlaybackTypeREAL) {  ///实景视频调速
        ///
        float videoSpeed = 0;
        NSString *equipTypeId = self.courseModel.equipmentId;
        switch (equipTypeId.intValue) {
            case BoatEquipment:{
                ///划船机
                ///（最小倍速0.5，最大倍速2 正常22-26，取22桨频，倍数1，30 1.25 39、40时对应倍数1.5 60 2倍速）     y = k/（1+a*e^(-bx)）     k = 2.5 a =4 b = 1/21.640  x =桨频   y=倍速
                double speed = model.spm.doubleValue;
                videoSpeed = MIN(2.5/(1 + 4 * pow(M_E, - speed / 21.640)), 2);
            } break;
            case StairClimbEquipment:{
                /// 爬楼机
                ///（最小倍速0.5，最大倍速2 正常40-50，取40踏频，倍数1，50 1.25 64 1.5倍速 80时对应倍数1.75 100 2.0）   y = k/（1+a*e^(-bx)）     k = 2.5 a =4 b = 1/35.778 x =踏频 y=倍速
                double speed = model.spm.doubleValue;
                videoSpeed = MIN(2.5/(1 + 4 * pow(M_E, - speed / 35.778)), 2);
            } break;
            case EllipticalEquipment:{
                /// 椭圆机
                ///（最小倍速0.5，最大倍速2 正常50-60，取50踏频，倍数1，62 1.25 80时对应倍数1.5 125 2.00）   y = k/（1+a*e^(-bx)）     k = 2.5 a =4 b = 1/44.723 x =踏频 y=倍速
                double speed = model.spm.doubleValue;
                videoSpeed = MIN(2.5/(1 + 4 * pow(M_E, - speed / 44.723)), 2);
            } break;
            case BicycleEquipment: {
                ///动感单车
                ///（最小倍速0.5，最大倍速2 正常65-75，取65踏频，倍数1.1，56倍数1 78时 1.25 100时对应倍数1.49 156 2.00）  y = k/（1+a*e^(-bx)）     k = 2.5 a =4 b = 1/56.265  x =踏频 y=倍速
                double speed = model.spm.doubleValue;
                videoSpeed = MIN(2.5/(1 + 4 * pow(M_E, - speed / 56.265)), 2);
            } break;
            case TreadmillEquipment:{
                ///跑步机
                ///（最小倍速0.5，最大倍速2 正常5-7，取5km/h，倍数1，7时1.25 9的时候1.5 10时对应倍数1.6 14 2倍速）   y = k/（1+a*e^(-bx)）    k = 2.5 a =4 b = 1/5.049   x =速度 y=倍速
                double speed = model.speed.doubleValue;
                videoSpeed = MIN(2.5/(1 + 4 * pow(M_E, - speed / 5.049)), 2);
            } break;
            default: break;
        }
        
        if (videoSpeed != 0){
            if (self.videoRate != videoSpeed) {
                self.videoRate = videoSpeed;
                self.playbackController.rate = videoSpeed; ///播放倍速
            }
        }
    }
    
    ///超燃脂率计算更新数据
    self.burningRate.displayModel = model;
    
    dispatch_async(dispatch_get_main_queue(), ^{
        if ( [self.controlLayerDelegate respondsToSelector:@selector(videoPlayerBluetooth:andTymodel:)] ) {
           [self.controlLayerDelegate videoPlayerBluetooth:self andTymodel:model];
        }
    });
}

///更新心率设备UI
- (void)updateRateDeviceUI:(BaseEquipDataModel *)model {
//    dispatch_async(dispatch_get_main_queue(), ^{
//        if ( [self.controlLayerDelegate respondsToSelector:@selector(videoPlayerBluetooth:andRdmodel:)] ) {
//            [self.controlLayerDelegate videoPlayerBluetooth:self andRdmodel:model];
//        }
//    });
}

///设备中途设备断开连接
- (void)disconnectAlert {
    if ( [self.controlLayerDelegate respondsToSelector:@selector(videoPlayerDisconnectBluetooth:)] ) {
        [self.controlLayerDelegate videoPlayerDisconnectBluetooth:self];
    }
}

//更新设备连接状态
- (void)updateDeviceStatus:(DEVICE_CONNECT_STATUS)status {
    self.connectDevice = (status == DeviceConnected);
    
    ///设备断连[清除设备数据]
    if (status == DeviceDisconnect) {
        self.videoPlayData.eqModel = nil;
    }
    
    ///连接上设备后. 隐藏提示弹窗
    if (status == DeviceConnected) {
        if (self.matchingPrompt != nil){
            [self.matchingPrompt hiddenContentView];
        }
    }
    
    dispatch_async(dispatch_get_main_queue(), ^{
        if ( [self.controlLayerDelegate respondsToSelector:@selector(videoPlayerBluetooth:connectDevice:)] ) {
            [self.controlLayerDelegate videoPlayerBluetooth:self connectDevice:status];
        }
    });
}

/**
 设备长按飞梭结束
 */
- (void)deviceExit {
    dispatch_async(dispatch_get_main_queue(), ^{
        [self.prompt show:[[NSAttributedString alloc] initWithString:@"运动已结束"] duration:kMBPHudShowTime];
        
        ///主动结束按中途退出算
        MLog(@"飞梭结束deviceExit_______");
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wundeclared-selector"
        if ([self.controllerVC respondsToSelector:@selector(showReasonAlert)]) {
            [self.controllerVC performSelector:@selector(showReasonAlert)];
        }
#pragma clang diagnostic pop
    });
}

//在视频播放页面，连接设备成功
- (void)connectDeviceSuccess {
    @weakify(self);
    [self.videoPlayData getEquipmentInfo:^{
        @strongify(self);

//        self.blueManager.eqModel = self.videoPlayData.eqModel;
//        self.blueManager.useRateKcal = self.videoPlayData.useRateKcal;
        
        ///接口请求成功后刷新链接状态【再次刷新】
        if (self.videoPlayData.eqModel != nil){
            self.burningRate.eqModel = self.videoPlayData.eqModel;
            [self updateDeviceStatus:DeviceConnected];
        }
    }];
}

@end








@implementation SJBaseVideoPlayer (burningRate)

- (SuperBurningRateManager *)burningRate {
    SuperBurningRateManager *burningRate = _burningRate;
    if ( burningRate == nil ) {
        burningRate = [[SuperBurningRateManager alloc] init];
        [self setBurningRate:burningRate];
    }
    return burningRate;
}

- (void)setBurningRate:(nullable SuperBurningRateManager *)burningRate {
    if (_burningRate != nil) {
        _burningRate = nil;
    }
    _burningRate = burningRate;
    
    ///每秒计算回调刷新的总超燃脂率
    @weakify(self);
    _burningRate.updateTotalBurningRateExeBlock = ^(__kindof NSNumber * _Nonnull totalBurningRate, __kindof NSNumber * _Nonnull burningRate) {
        @strongify(self);
        self.trainManager.totalMeritRate = [totalBurningRate stringValue];
        self.trainManager.preMeritRate = [burningRate stringValue];
    };
}

@end








#pragma mark -

@interface SJBaseVideoPlayer (SJPlayModelPropertiesObserverDelegate)<SJPlayModelPropertiesObserverDelegate>
@end

@implementation SJBaseVideoPlayer (SJPlayModelPropertiesObserverDelegate)
- (void)observer:(nonnull SJPlayModelPropertiesObserver *)observer userTouchedCollectionView:(BOOL)touched { /* nothing */ }
- (void)observer:(nonnull SJPlayModelPropertiesObserver *)observer userTouchedTableView:(BOOL)touched { /* nothing */ }

- (void)playerWillAppearForObserver:(nonnull SJPlayModelPropertiesObserver *)observer superview:(nonnull UIView *)superview {
    if ( _controlInfo->scrollControl.isScrollAppeared ) { return; }
    
    _controlInfo->scrollControl.isScrollAppeared = YES;
    _deviceVolumeAndBrightnessTargetViewContext.isScrollAppeared = YES;
    [_deviceVolumeAndBrightnessController onTargetViewContextUpdated];
    
    if ( _controlInfo->scrollControl.hiddenPlayerViewWhenScrollDisappeared ) {
        _view.hidden = NO;
    }
    
    if ( _playbackController.isPlayed ) {
        if ( !self.viewControllerManager.isViewDisappeared ) {
            if ( self.isPlayOnScrollView ) {
                if ( _controlInfo->scrollControl.resumePlaybackWhenScrollAppeared ) {
                    [self play];
                }
            }
        }
    }
    
    if ( superview && self.view.superview != superview ) {
        [superview addSubview:self.view];
        [self.view mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.edges.equalTo(superview);
        }];
    }
    
    if ( _smallViewFloatingController.isAppeared ) {
        [_smallViewFloatingController dismiss];
    }
    
    if ( [self.controlLayerDelegate respondsToSelector:@selector(videoPlayerWillAppearInScrollView:)] ) {
        [self.controlLayerDelegate videoPlayerWillAppearInScrollView:self];
    }
    
    if ( self.playerViewWillAppearExeBlock )
        self.playerViewWillAppearExeBlock(self);
}

- (void)playerWillDisappearForObserver:(nonnull SJPlayModelPropertiesObserver *)observer {
    if ( _controlInfo->scrollControl.isScrollAppeared == NO ) { return; }
    
    if ( _rotationManager.isRotating ) { return; }
    
    _controlInfo->scrollControl.isScrollAppeared = NO;
    _deviceVolumeAndBrightnessTargetViewContext.isScrollAppeared = NO;
    [_deviceVolumeAndBrightnessController onTargetViewContextUpdated];
    
    _view.hidden = _controlInfo->scrollControl.hiddenPlayerViewWhenScrollDisappeared;
    
    if ( _smallViewFloatingController.isEnabled ) {
        [_smallViewFloatingController show];
    } else if ( _controlInfo->scrollControl.pausedWhenScrollDisappeared ) {
        [self pause];
    }
    
    if ( [self.controlLayerDelegate respondsToSelector:@selector(videoPlayerWillDisappearInScrollView:)] ) {
        [self.controlLayerDelegate videoPlayerWillDisappearInScrollView:self];
    }
    
    if ( self.playerViewWillDisappearExeBlock )
        self.playerViewWillDisappearExeBlock(self);
}

@end


NS_ASSUME_NONNULL_END
