//
//  MRKUserExperienceControlLayer.m
//  Student_IOS
//
//  Created by <PERSON><PERSON> on 2023/12/27.
//

#import "MRKUserExperienceControlLayer.h"
#import "NSAttributedString+SJMake.h"
#import <objc/message.h>
#import "Masonry.h"
#import "SJBaseVideoPlayer.h"
#import "SJVideoPlayer.h"

#import "SJTimerControl.h"
#import "SJVideoPlayerURLAsset+SJControlAdd.h"
#import "UIView+SJAnimationAdded.h"
#import "SJVideoPlayerConfigurations.h"
#import "SJProgressSlider.h"
#import "SJLoadingView.h"
#import "SJDraggingObservation.h"
#import "SJScrollingTextMarqueeView.h"
#import "SJFullscreenModeStatusBar.h"
#import "SJSpeedupPlaybackPopupView.h"

#import "TrainDataItemView.h"
#import "MRKVideoDefinitionView.h"
#import "MRKRenderModel.h"
#import "MRKFatBurningModel.h"
#import "MRKVideoBTConnectView.h"
#import "SJPopAdviseDefinitionView.h"
#import "MRKSignActivity.h"
#import "MRKTraceManager.h"
#import "SpeechSynthesizerManager.h"

@interface MRKUserExperienceControlLayer (){
    CGSize _previousSize;
}

@property (nonatomic, weak, nullable) SJBaseVideoPlayer *videoPlayer;
@property (nonatomic, weak, nullable) SJVideoPlayer *currentVideoPlayer;
@property (nonatomic, strong, readonly) SJProgressSlider *bottomProgressIndicator;


// 固定左上角的返回按钮. 设置`fixesBackItem`后显示
@property (nonatomic, strong, readonly) UIButton *fixedBackButton;
@property (nonatomic, strong, readonly) SJEdgeControlButtonItem *backItem;
@property (nonatomic, strong, nullable) id<SJReachabilityObserver> reachabilityObserver;
@property (nonatomic, strong, readonly) SJTimerControl *dateTimerControl API_AVAILABLE(ios(11.0)); // refresh date for custom status bar
@property (nonatomic, strong) MRKVideoBTConnectView *BTConnectView; ///蓝牙断连弹窗

/**有无弹过指令窗**/
@property (nonatomic, assign) BOOL hasToastAlert;
/**录播课程结束前10S提示**/
@property (nonatomic, assign) BOOL hasToast10SecondsAlert;
/**播放器当前时间对应的小节model**/
@property (nonatomic, strong) CourseLinkModel *currentlinkModel;
@property (nonatomic, strong) TrainingShowData *tyModel;
@end

@implementation MRKUserExperienceControlLayer

@synthesize restarted = _restarted;


- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if ( !self ) return nil;
    
    self.bottomProgressIndicatorHeight = 1;
    self.hiddenBottomProgressIndicator = YES;
    self.automaticallyPerformRotationOrFitOnScreen = YES;
    self.rightAdapterLayoutFromTopDirection = YES;
    self.autoAdjustTopSpacing = YES;
  
    [self _setupView];
    return self;
}

- (void)layoutSubviews {
    [super layoutSubviews];
    
    if ( !CGSizeEqualToSize(_previousSize, self.bounds.size) ) {
        if (@available(iOS 11.0, *)) {
            [self _updateAppearStateForCustomStatusBar];
        }
        [self _updateLayoutForBottomProgressIndicator];
    }
    _previousSize = self.bounds.size;
}


- (void)dealloc {
    NSLog(@"😊😊😊-----dealloc-----😊😊😊%@" , NSStringFromClass([self class]));
    [NSNotificationCenter.defaultCenter removeObserver:self];
    [SpeechSynthesizerManager.shared stopSpeaking];
}




#pragma mark - videoTipString

- (void)videoTipString:(NSString *)tipStr {
    if (![tipStr isNotBlank]) return;
    [self.videoPlayer.prompt show:[NSAttributedString sj_UIKitText:^(id<SJUIKitTextMakerProtocol>  _Nonnull make) {
        make.append(tipStr);
        make.font([UIFont systemFontOfSize:14 weight:UIFontWeightMedium]);
        make.textColor([UIColor whiteColor]);
    }] duration:3];
}

#pragma mark - item actions

- (void)_fixedBackButtonWasTapped {
    [self.backItem performActions];
}

/**返回*/
- (void)_backItemWasTapped {
    if ( [self.delegate respondsToSelector:@selector(backItemWasTappedForControlLayer:)] ) {
        [self.delegate backItemWasTappedForControlLayer:self];
    }
}

/**锁屏点击*/
- (void)_lockItemWasTapped {
    //    self.videoPlayer.lockedScreen = !self.videoPlayer.isLockedScreen;
}

/**播放按钮点击*/
- (void)_playItemWasTapped {
    _videoPlayer.isPaused ? [self.videoPlayer play] : [self.videoPlayer pauseForUser];
}

/**
 播放器全屏按钮点击
 @note 暂时未使用此功能
 */
- (void)_fullItemWasTapped {
    if ( _onlyUsedFitOnScreen ) {
        [_videoPlayer setFitOnScreen:!_videoPlayer.isFitOnScreen];
        return;
    }
    
    if ( _usesFitOnScreenFirst && !_videoPlayer.isFitOnScreen ) {
        [_videoPlayer setFitOnScreen:YES];
        return;
    }
    [_videoPlayer rotate];
}

/**播放器重播*/
- (void)_replayItemWasTapped {
    [_videoPlayer replay];
}


/**设备连接*/
- (void)_deviceItemWasTapped {
    
    if ( [self.delegate respondsToSelector:@selector(connectDeviceWasTappedForControlLayer:)] ) {
        [self.delegate connectDeviceWasTappedForControlLayer:self];
    }
    
    ///log
    ReportMrkLogParms(2, @"连接设备", @"page_play", @"btn_play_connect_equipment", nil, 0, @{@"courseId": self.videoPlayer.courseModel.courseId?:@"", @"course_state": self.videoPlayer.tracePlaybackType});
}


/**静音按钮点击*/
- (void)_mutedItemWasTapped {
    self.videoPlayer.muted = !self.videoPlayer.muted;
    
    /// 刷新静音显示
    [self _updateMutedItemIfNeeded];
    
    NSString *tipStr = _videoPlayer.muted ? @"声音已关闭":@"声音已开启";
    [self videoTipString:tipStr];
    
}


#pragma mark - restartControlLayer/exitControlLayer

///
/// 切换器(player.switcher)重启该控制层
///
- (void)restartControlLayer {
    _restarted = YES;
    sj_view_makeAppear(self.controlView, YES);
    
    [self _showOrHiddenLoadingView];
    [self _updateAppearStateForContainerViews];
    [self _reloadAdaptersIfNeeded];
}

///
/// 控制层退场
///
- (void)exitControlLayer {
    _restarted = NO;
    
    sj_view_makeDisappear(self.controlView, YES, ^{
        if ( !self->_restarted ) [self.controlView removeFromSuperview];
    });
    
    sj_view_makeDisappear(_topContainerView, YES);
    sj_view_makeDisappear(_leftContainerView, YES);
    sj_view_makeDisappear(_bottomContainerView, YES);
    sj_view_makeDisappear(_rightContainerView, YES);
    sj_view_makeDisappear(_centerContainerView, YES);
    
    sj_view_makeDisappear(_topDataView, YES);
    sj_view_makeDisappear(_bottomDataView, YES);
}

#pragma mark - player delegate methods

- (UIView *)controlView {
    return self;
}

- (void)installedControlViewToVideoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer {
    _videoPlayer = videoPlayer;
    
    if ([videoPlayer isKindOfClass:[SJVideoPlayer class]]){
        _currentVideoPlayer = videoPlayer;
    }
    
    SJNSLog(@"installedControlViewToVideoPlayer");
    
    sj_view_makeDisappear(_topContainerView, NO);
    sj_view_makeDisappear(_leftContainerView, NO);
    sj_view_makeDisappear(_bottomContainerView, NO);
    sj_view_makeDisappear(_rightContainerView, NO);
    sj_view_makeDisappear(_centerContainerView, NO);
    
    sj_view_makeDisappear(_topDataView, NO);
    sj_view_makeDisappear(_bottomDataView, NO);
    
    [self _reloadSizeForBottomTimeLabel];
    [self _updateContentForBottomCurrentTimeItemIfNeeded];
    [self _updateContentForBottomDurationItemIfNeeded];
    
    @weakify(self);
    _reachabilityObserver = [videoPlayer.reachability getObserver];
    _reachabilityObserver.networkSpeedDidChangeExeBlock = ^(id<SJReachability> r) {
        @strongify(self);
        if ( !self ) return;
        [self _updateNetworkSpeedStrForLoadingView];
    };
}

///
/// 当播放器尝试自动隐藏控制层之前 将会调用这个方法
///
- (BOOL)controlLayerOfVideoPlayerCanAutomaticallyDisappear:(__kindof SJBaseVideoPlayer *)videoPlayer {
    return YES;
}





- (void)controllerDidAppeared:(__kindof SJBaseVideoPlayer *)videoPlayer{
    NSLog(@"⛄⛄⛄⛄⛄⛄⛄⛄⛄⛄controlLayerDidAppeared");
    ///
    [_videoPlayer.videoPlayData getEquipmentInfo:^{
       
    }];
}

- (void)controllerDidDisAppeared:(__kindof SJBaseVideoPlayer *)videoPlayer {
    
}


///
///视图切换
///
- (void)controlLayerNeedAppear:(__kindof SJBaseVideoPlayer *)videoPlayer {
    if ( videoPlayer.isLockedScreen )
        return;
    
    SJNSLog(@"");
    [self _updateAppearStateForResidentBackButtonIfNeeded];
    [self _updateAppearStateForContainerViews];
    [self _reloadAdaptersIfNeeded];
    [self _updateContentForBottomCurrentTimeItemIfNeeded];
    [self _updateAppearStateForBottomProgressIndicatorIfNeeded];
    if (@available(iOS 11.0, *)) {
        [self _reloadCustomStatusBarIfNeeded];
    }
}

- (void)controlLayerNeedDisappear:(__kindof SJBaseVideoPlayer *)videoPlayer {
    if ( videoPlayer.isLockedScreen )
        return;
    
    SJNSLog(@"");
    [self _updateAppearStateForResidentBackButtonIfNeeded];
    [self _updateAppearStateForContainerViews];
    [self _updateAppearStateForBottomProgressIndicatorIfNeeded];
}


- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer prepareToPlay:(SJVideoPlayerURLAsset *)asset {
    SJNSLog(@"");
    
    [self _reloadSizeForBottomTimeLabel];
    [self _updateContentForBottomDurationItemIfNeeded];
    [self _updateContentForBottomCurrentTimeItemIfNeeded];
    
    [self _updateContentForBottomProgressIndicatorIfNeeded];
    [self _updateAppearStateForResidentBackButtonIfNeeded];
    [self _updateAppearStateForBottomProgressIndicatorIfNeeded];
    
    [self _reloadAdaptersIfNeeded];
    [self _showOrHiddenLoadingView];
}

- (void)videoPlayerPlaybackStatusDidChange:(__kindof SJBaseVideoPlayer *)videoPlayer {
    SJNSLog(@"");
    
    [self _reloadAdaptersIfNeeded];
    [self _showOrHiddenLoadingView];
    [self _updateContentForBottomCurrentTimeItemIfNeeded];
    [self _updateContentForBottomDurationItemIfNeeded];
    [self _updateContentForBottomProgressIndicatorIfNeeded];
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer pictureInPictureStatusDidChange:(SJPictureInPictureStatus)status API_AVAILABLE(ios(14.0)) {
    SJNSLog(@"");
    
    [self _updateContentForPictureInPictureItem];
    [self.topAdapter reload];
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer currentTimeDidChange:(NSTimeInterval)currentTime {
    /**更新底部当前时长*/
    [self _updateContentForBottomCurrentTimeItemIfNeeded];
    /**更新底部进度条*/
    [self _updateContentForBottomProgressIndicatorIfNeeded];
    ///录播最后10s时提示
    [self _updateVODPlayerLast10SecondsTipIfNeeded:currentTime];
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer durationDidChange:(NSTimeInterval)duration {
    
    [self _reloadSizeForBottomTimeLabel];
    [self _updateContentForBottomDurationItemIfNeeded];
    [self _updateContentForBottomProgressIndicatorIfNeeded];
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer playableDurationDidChange:(NSTimeInterval)duration {
    
}


#pragma mark --- playbackTypeDidChange ---
- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer playbackTypeDidChange:(SJPlaybackType)playbackType {
    SJNSLog(@"");
    
    switch ( playbackType ) {
        case SJPlaybackTypeLIVE: {
            
            [self.bottomAdapter removeAllItems];
            [self.bottomAdapter reload];
            
            ///更新底部隐藏
            [self _updateAppearStateForBottomContainerView];
        }
            break;
        case SJPlaybackTypeUnknown:
        case SJPlaybackTypeVOD:
        case SJPlaybackTypeFILE: {
            
        }
            break;
        case SJPlaybackTypeREAL:{
            
            SJEdgeControlButtonItem *modelItem = [_topAdapter itemForTag:SJEdgeControlLayerTopItem_Model];
            if (modelItem != nil){
                modelItem.hidden = YES;
            }
            
            SJEdgeControlButtonItem *moreItem = [_topAdapter itemForTag:SJEdgeControlLayerTopItem_More];
            if (moreItem != nil){
                moreItem.hidden = YES;
            }
            
            [self.topAdapter reload];
            
        }
            break;
    }
    
    [self _showOrRemoveBottomProgressIndicator];
}




- (BOOL)canTriggerRotationOfVideoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer {
    SJNSLog(@"");
    if ( _onlyUsedFitOnScreen )
        return NO;
    if ( _usesFitOnScreenFirst )
        return videoPlayer.isFitOnScreen;
    
    return YES;
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer willRotateView:(BOOL)isFull {
    SJNSLog(@"");
    [self _updateAppearStateForResidentBackButtonIfNeeded];
    [self _updateAppearStateForContainerViews];
    [self _reloadAdaptersIfNeeded];
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer willFitOnScreen:(BOOL)isFitOnScreen {
    SJNSLog(@"");
    [self _updateAppearStateForResidentBackButtonIfNeeded];
    [self _updateAppearStateForContainerViews];
    [self _reloadAdaptersIfNeeded];
}

/// 是否可以触发播放器的手势
- (BOOL)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer gestureRecognizerShouldTrigger:(SJPlayerGestureType)type location:(CGPoint)location {
    SJEdgeControlButtonItemAdapter *adapter = nil;
    BOOL(^_locationInTheView)(UIView *) = ^BOOL(UIView *container) {
        return CGRectContainsPoint(container.frame, location) && !sj_view_isDisappeared(container);
    };
    
    if ( _locationInTheView(_topContainerView) ) {
        adapter = _topAdapter;
    }
    else if ( _locationInTheView(_topDataView) ) {
        adapter = nil;
    }
    else if ( _locationInTheView(_bottomContainerView) ) {
        adapter = _bottomAdapter;
    }
    else if ( _locationInTheView(_leftContainerView) ) {
        adapter = _leftAdapter;
    }
    else if ( _locationInTheView(_rightContainerView) ) {
        adapter = _rightAdapter;
    }
    else if ( _locationInTheView(_centerContainerView) ) {
        adapter = _centerAdapter;
    }
    if ( !adapter ) return YES;
    
    CGPoint point = [self.controlView convertPoint:location toView:adapter.view];
    if ( !CGRectContainsPoint(adapter.view.frame, point) ) return YES;
    
    SJEdgeControlButtonItem *_Nullable item = [adapter itemAtPoint:point];
    return item != nil ? (item.actions.count == 0)  : YES;
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer panGestureTriggeredInTheHorizontalDirection:(SJPanGestureRecognizerState)state progressTime:(NSTimeInterval)progressTime {
    
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer longPressGestureStateDidChange:(SJLongPressGestureRecognizerState)state {
    
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer presentationSizeDidChange:(CGSize)size {
    if ( _automaticallyPerformRotationOrFitOnScreen && !videoPlayer.isFullscreen && !videoPlayer.isFitOnScreen ) {
        _onlyUsedFitOnScreen = size.width < size.height;
    }
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer SEIData:(id)model {
    [self sendInstructionsWithData:model];
}




#pragma mark ---- SJAppActivityBluetoothControlDelegate ----

- (void)videoPlayerBluetooth:(__kindof SJBaseVideoPlayer *)videoPlayer andTymodel:(id)model{
    ///设备蓝牙数据处理
    [self dataAdapterUpdateWithModel:model];
    if(self.delegate && [self.delegate respondsToSelector:@selector(updateBlueData:)]) {
        [self.delegate updateBlueData:model];
    }
}

- (void)videoPlayerBluetooth:(__kindof SJBaseVideoPlayer *)videoPlayer andRdmodel:(id)model{
    ///心率蓝牙数据处理
  
}

- (void)videoPlayerBluetooth:(__kindof SJBaseVideoPlayer *)videoPlayer connectDevice:(DEVICE_CONNECT_STATUS)status{
    ///刷新设备状态
    [self _updateConnectForTopDeviceItemIfNeeded];
    
    ///刷新连接状态
    [self _reloadDataAdapterIfNeeded];
    
    ///隐藏连接弹窗
    if (status == DeviceConnected || status == DeviceAutoConnecting){
        if (self.BTConnectView) {
            [self.BTConnectView hide];
            self.BTConnectView = nil;
        }
    }
    
    ///隐藏阻力回显
    if (status == DeviceConnected){
        [self _reloadItemsBottomDataAdapter];
    }
}

///蓝牙断连/**设备断连弹窗*/
- (void)videoPlayerDisconnectBluetooth:(__kindof SJBaseVideoPlayer *)videoPlayer{
    if (self.BTConnectView) {
        [self.BTConnectView hide];
        self.BTConnectView = nil;
    }
    
    @weakify(self);
    self.BTConnectView = [MRKVideoBTConnectView build];
    self.BTConnectView.selectBlock = ^(__kindof AlertBaseView *alertView, NSInteger index) {
        @strongify(self);
        [alertView hide];
        if (index == 1) {
            [self _deviceItemWasTapped];
        }
    };
    [self.BTConnectView showIn:self];
    
//    ///断连清数据
//    [self _clearDataAdapterIfNeeded];
}



#pragma mark ---- SJControlLayerControlDelegate ----

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer andCommandModel:(id)model{
    [self sendInstructionsWithData:model];
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer useLessonPlanControl:(BOOL)useLessonPlanControl{

}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer closeBarrageControl:(BOOL)closeBarrageControl{
  
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer receiveDanmu:(id)model{

}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer type:(NSInteger)type LIVERankData:(id)model{

}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer type:(NSInteger)type VODRankData:(id)model{
 
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer type:(NSInteger)type kcalIncremenData:(id)model{
 
}

- (void)videoPlayerRankRequestError:(__kindof SJBaseVideoPlayer *)videoPlayer type:(NSInteger)type{
  
}


- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer fatBurningModel:(id)model{

}

///下一小节数据
- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer nextCourseLinkPOS:(id)model{
    if (![model isKindOfClass:[CourseLinkModel class]]) {  return; }
    CourseLinkModel *linkModel = (CourseLinkModel *)model;
    self.currentlinkModel = linkModel;
    
    MRKVideoPrepareDataInfo *videoPlayData = videoPlayer.videoPlayData;
    int equipmentId = videoPlayData.equipmentId.intValue;
    switch (equipmentId) {
        case BicycleEquipment: case EllipticalEquipment: {
            /// 踏频(rpm) item
            SJEdgeControlButtonItem *treadItem = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_RateOfTread];
            if ( treadItem != nil) {
                if (treadItem.partView != nil) {
                    [treadItem.partView reloadExerciseDialView:linkModel];
                }
            }
        }break;
        case BoatEquipment: {
            /// 桨频(spm) item
            SJEdgeControlButtonItem *strokeItem = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_RateOfStroke];
            if ( strokeItem != nil) {
                if (strokeItem.partView != nil) {
                    [strokeItem.partView reloadExerciseDialView:linkModel];
                }
            }
        }break;
        default: break;
    }
    
    ///刷新当前文本提示窗
    [self  refreshNoteTips];
}

///下一大节数据
- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer nextCourseCataloguePOS:(id)model{
 
 
}

///显示商品窗
- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer courseGoods:(id)model {

}

- (void)_showCoachCourseGoods:(id)model{
    [self videoPlayer:_videoPlayer courseGoods:model];
}













/// 这是一个只有在播放器锁屏状态下, 才会回调的方法
/// 当播放器锁屏后, 用户每次点击都会回调这个方法
- (void)tappedPlayerOnTheLockedState:(__kindof SJBaseVideoPlayer *)videoPlayer {
    
}

- (void)lockedVideoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer {
    
}

- (void)unlockedVideoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer {
    
}

- (void)videoPlayer:(SJBaseVideoPlayer *)videoPlayer reachabilityChanged:(SJNetworkStatus)status {
    if (@available(iOS 11.0, *)) {
        [self _reloadCustomStatusBarIfNeeded];
    }
    if ( _disabledPromptWhenNetworkStatusChanges ) return;
    if ( [self.videoPlayer.URLAsset.mediaURL isFileURL] ) return; // return when is local video.
    
    switch ( status ) {
        case SJNetworkStatus_NotReachable: {
            
            ///无网络提示
            NSString *tipStr = SJVideoPlayerConfigurations.shared.localizedStrings.unstableNetworkPrompt;
            [self videoTipString:tipStr];
        }
            break;
        case SJNetworkStatus_ReachableViaWWAN: {
            
            ///4G流量提示
            NSString *tipStr = SJVideoPlayerConfigurations.shared.localizedStrings.cellularNetworkPrompt;
            [self videoTipString:tipStr];
        }
            break;
        case SJNetworkStatus_ReachableViaWiFi: {}
            break;
    }
}










#pragma mark -

- (NSString *)stringForSeconds:(NSInteger)secs {
    return _videoPlayer ? [_videoPlayer stringForSeconds:secs] : @"";
}

#pragma mark -

- (void)setFixesBackItem:(BOOL)fixesBackItem {
    if ( fixesBackItem == _fixesBackItem )
        return;
    _fixesBackItem = fixesBackItem;
    dispatch_async(dispatch_get_main_queue(), ^{
        if ( self->_fixesBackItem ) {
            [self.controlView addSubview:self.fixedBackButton];
            [self->_fixedBackButton mas_makeConstraints:^(MASConstraintMaker *make) {
                make.top.left.bottom.equalTo(self.topAdapter.view);
                make.width.equalTo(self.topAdapter.view.mas_height);
            }];
            
            [self _updateAppearStateForResidentBackButtonIfNeeded];
            [self _reloadTopAdapterIfNeeded];
        }
        else {
            if ( self->_fixedBackButton ) {
                [self->_fixedBackButton removeFromSuperview];
                self->_fixedBackButton = nil;
                
                // back item
                [self _reloadTopAdapterIfNeeded];
            }
        }
    });
}

- (void)setHiddenBottomProgressIndicator:(BOOL)hiddenBottomProgressIndicator {
    if ( hiddenBottomProgressIndicator != _hiddenBottomProgressIndicator ) {
        _hiddenBottomProgressIndicator = hiddenBottomProgressIndicator;
        dispatch_async(dispatch_get_main_queue(), ^{
            [self _showOrRemoveBottomProgressIndicator];
        });
    }
}

- (void)setBottomProgressIndicatorHeight:(CGFloat)bottomProgressIndicatorHeight {
    if ( bottomProgressIndicatorHeight != _bottomProgressIndicatorHeight ) {
        _bottomProgressIndicatorHeight = bottomProgressIndicatorHeight;
        dispatch_async(dispatch_get_main_queue(), ^{
            [self _updateLayoutForBottomProgressIndicator];
        });
    }
}

- (void)setLoadingView:(nullable UIView<SJLoadingView> *)loadingView {
    if ( loadingView != _loadingView ) {
        [_loadingView removeFromSuperview];
        _loadingView = loadingView;
        if ( loadingView != nil ) {
            ///单次loading时长
            //            loadingView.loadingSecondsBlock = ^(NSTimer * _Nonnull timer, NSTimeInterval time) {
            //                if (time > 5.0) {
            //                    ///判断是否提示, 查询有无可提示的清晰度
            //                    if (self.showAdviseDefinition && [self _checkPlayAdviseDefinition]){
            //                        self.showAdviseDefinition = NO;
            //                        [self _playAdviseDefinition];
            //
            //                        ///销毁本次timer
            //                        if ([timer isValid]) {
            //                            [timer invalidate];
            //                            timer = nil;
            //                        }
            //                    }
            //                }
            //            };
            [self.controlView addSubview:loadingView];
            [loadingView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.center.offset(0);
            }];
        }
    }
}

- (void)setTitleView:(nullable __kindof UIView<SJScrollingTextMarqueeView> *)titleView {
    _titleView = titleView;
    [self _reloadTopAdapterIfNeeded];
}

- (void)setCustomStatusBar:(UIView<SJFullscreenModeStatusBar> *)customStatusBar NS_AVAILABLE_IOS(11.0) {
    if ( customStatusBar != _customStatusBar ) {
        [_customStatusBar removeFromSuperview];
        _customStatusBar = customStatusBar;
        [self _reloadCustomStatusBarIfNeeded];
    }
}

- (void)setShouldShowCustomStatusBar:(BOOL (^)(MRKUserExperienceControlLayer * _Nonnull))shouldShowCustomStatusBar NS_AVAILABLE_IOS(11.0) {
    _shouldShowCustomStatusBar = shouldShowCustomStatusBar;
    [self _updateAppearStateForCustomStatusBar];
}

- (void)setOnlyUsedFitOnScreen:(BOOL)onlyUsedFitOnScreen {
    if ( onlyUsedFitOnScreen != _onlyUsedFitOnScreen ) {
        _onlyUsedFitOnScreen = onlyUsedFitOnScreen;
        if ( _onlyUsedFitOnScreen ) {
            _automaticallyPerformRotationOrFitOnScreen = NO;
        }
    }
}








#pragma mark - setup view

- (void)_setupView {
    [self _addItemsToTopAdapter];
    [self _addItemsToBottomAdapter];
    [self _addItemsToCenterAdapter];
    [self _addItemsBottomDataAdapter];   ///添加数据展示层
    
    
    self.topContainerView.sjv_disappearDirection = SJViewDisappearAnimation_None;
    self.bottomContainerView.sjv_disappearDirection = SJViewDisappearAnimation_None;
    self.centerContainerView.sjv_disappearDirection = SJViewDisappearAnimation_None;
    self.bottomDataView.sjv_disappearDirection = SJViewDisappearAnimation_Bottom;
    
    sj_view_initializes(@[self.topContainerView,
                          self.bottomContainerView,
                          self.bottomDataView]);
    
    [NSNotificationCenter.defaultCenter addObserver:self
                                           selector:@selector(_resetControlLayerAppearIntervalForItemIfNeeded:)
                                               name:SJEdgeControlButtonItemPerformedActionNotification
                                             object:nil];
}

@synthesize fixedBackButton = _fixedBackButton;
- (UIButton *)fixedBackButton {
    if ( _fixedBackButton ) return _fixedBackButton;
    _fixedBackButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [_fixedBackButton setImage:SJVideoPlayerConfigurations.shared.resources.backImage forState:UIControlStateNormal];
    [_fixedBackButton addTarget:self action:@selector(_fixedBackButtonWasTapped) forControlEvents:UIControlEventTouchUpInside];
    return _fixedBackButton;
}

@synthesize bottomProgressIndicator = _bottomProgressIndicator;
- (SJProgressSlider *)bottomProgressIndicator {
    if ( _bottomProgressIndicator ) return _bottomProgressIndicator;
    _bottomProgressIndicator = [SJProgressSlider new];
    _bottomProgressIndicator.pan.enabled = NO;
    _bottomProgressIndicator.trackHeight = _bottomProgressIndicatorHeight;
    _bottomProgressIndicator.round = NO;
    id<SJVideoPlayerControlLayerResources> sources = SJVideoPlayerConfigurations.shared.resources;
    UIColor *traceColor = sources.bottomIndicatorTraceColor ?: sources.progressTraceColor;
    UIColor *trackColor = sources.bottomIndicatorTrackColor ?: sources.progressTrackColor;
    _bottomProgressIndicator.traceImageView.backgroundColor = traceColor;
    _bottomProgressIndicator.trackImageView.backgroundColor = trackColor;
    return _bottomProgressIndicator;
}

@synthesize loadingView = _loadingView;
- (UIView<SJLoadingView> *)loadingView {
    if ( _loadingView == nil ) {
        [self setLoadingView:[SJLoadingView.alloc initWithFrame:CGRectZero]];
    }
    return _loadingView;
}

@synthesize titleView = _titleView;
- (UIView<SJScrollingTextMarqueeView> *)titleView {
    if ( _titleView == nil ) {
        [self setTitleView:[SJScrollingTextMarqueeView.alloc initWithFrame:CGRectZero]];
    }
    return _titleView;
}

@synthesize customStatusBar = _customStatusBar;
- (UIView<SJFullscreenModeStatusBar> *)customStatusBar {
    if ( _customStatusBar == nil ) {
        [self setCustomStatusBar:[SJFullscreenModeStatusBar.alloc initWithFrame:CGRectZero]];
    }
    return _customStatusBar;
}

@synthesize shouldShowCustomStatusBar = _shouldShowCustomStatusBar;
- (BOOL (^)(MRKUserExperienceControlLayer * _Nonnull))shouldShowCustomStatusBar {
    if ( _shouldShowCustomStatusBar == nil ) {
        BOOL is_iPhoneX = _screen.is_iPhoneX;
        [self setShouldShowCustomStatusBar:^BOOL(MRKUserExperienceControlLayer * _Nonnull controlLayer) {
            if ( controlLayer.videoPlayer.isFitOnScreen ) return NO;
            BOOL isFullscreen = controlLayer.videoPlayer.isFullscreen;
            if ( isFullscreen == NO ) {
                CGRect bounds = UIScreen.mainScreen.bounds;
                if ( bounds.size.width > bounds.size.height )
                    isFullscreen = CGRectEqualToRect(controlLayer.bounds, bounds);
            }
            
            BOOL shouldShow = NO;
            if ( isFullscreen ) {
                ///
                /// 13 以后, 全屏后显示自定义状态栏
                ///
                if ( @available(iOS 13.0, *) ) {
                    shouldShow = YES;
                }
                ///
                /// 11 仅 iPhone X 显示自定义状态栏
                ///
                else if ( @available(iOS 11.0, *) ) {
                    shouldShow = is_iPhoneX;
                }
            }
            return shouldShow;
        }];
    }
    return _shouldShowCustomStatusBar;
}

@synthesize dateTimerControl = _dateTimerControl;
- (SJTimerControl *)dateTimerControl {
    if ( _dateTimerControl == nil ) {
        _dateTimerControl = SJTimerControl.alloc.init;
        _dateTimerControl.interval = 1;
        __weak typeof(self) _self = self;
        _dateTimerControl.exeBlock = ^(SJTimerControl * _Nonnull control) {
            __strong typeof(_self) self = _self;
            if ( !self ) return;
            self.customStatusBar.isHidden ? [control interrupt] : [self _reloadCustomStatusBarIfNeeded];
        };
    }
    return _dateTimerControl;
}



#pragma Mark --- _addItemsToTopAdapter ---
- (void)_addItemsToTopAdapter {
    
    ///返回按钮
    SJEdgeControlButtonItem *backItem = [SJEdgeControlButtonItem placeholderWithType:SJButtonItemPlaceholderType_49x49 tag:SJEdgeControlLayerTopItem_Back];
    backItem.resetsAppearIntervalWhenPerformingItemAction = NO;
    [backItem addAction:[SJEdgeControlButtonItemAction actionWithTarget:self action:@selector(_backItemWasTapped)]];
    [self.topAdapter addItem:backItem];
    _backItem = backItem;
    
    ///title
    SJEdgeControlButtonItem *titleItem = [SJEdgeControlButtonItem placeholderWithType:SJButtonItemPlaceholderType_49xFill tag:SJEdgeControlLayerTopItem_Title];
    [self.topAdapter addItem:titleItem];
    
    ///连接设备
    SJEdgeControlButtonItem *deviceItem = [SJEdgeControlButtonItem placeholderWithType:SJButtonItemPlaceholderType_49x49 tag:SJEdgeControlLayerTopItem_Device];
    [deviceItem addAction:[SJEdgeControlButtonItemAction actionWithTarget:self action:@selector(_deviceItemWasTapped)]];
    [self.topAdapter addItem:deviceItem];
    
    ///静音
    SJEdgeControlButtonItem *mutedItem = [SJEdgeControlButtonItem placeholderWithType:SJButtonItemPlaceholderType_49x49 tag:SJEdgeControlLayerTopItem_Muted];
    [mutedItem addAction:[SJEdgeControlButtonItemAction actionWithTarget:self action:@selector(_mutedItemWasTapped)]];
    [self.topAdapter addItem:mutedItem];
    
    [self.topAdapter reload];
}

- (void)_addItemsToBottomAdapter {
    
    SJEdgeControlButtonItem *titleItem = [SJEdgeControlButtonItem placeholderWithType:SJButtonItemPlaceholderType_49xFill tag:SJEdgeControlLayerBottomItem_Holder];
    [self.bottomAdapter addItem:titleItem];
    
    // 当前时间
    SJEdgeControlButtonItem *currentTimeItem = [SJEdgeControlButtonItem placeholderWithSize:8 tag:SJEdgeControlLayerBottomItem_CurrentTime];
    [self.bottomAdapter addItem:currentTimeItem];
    
    // 时间分隔符
    SJEdgeControlButtonItem *separatorItem = [[SJEdgeControlButtonItem alloc] initWithTitle:[NSAttributedString sj_UIKitText:^(id<SJUIKitTextMakerProtocol>  _Nonnull make) {
        make.append(@"/");
        make.font([UIFont fontWithName:fontNamePing size:16]);
        make.textColor([UIColor whiteColor]);
        make.alignment(NSTextAlignmentCenter);
    }] target:nil action:NULL tag:SJEdgeControlLayerBottomItem_Separator];
    [self.bottomAdapter addItem:separatorItem];
    
    // 全部时长
    SJEdgeControlButtonItem *durationTimeItem = [SJEdgeControlButtonItem placeholderWithSize:8 tag:SJEdgeControlLayerBottomItem_DurationTime];
    [self.bottomAdapter addItem:durationTimeItem];
    
    // 播放按钮
    SJEdgeControlButtonItem *playItem = [SJEdgeControlButtonItem placeholderWithType:SJButtonItemPlaceholderType_49x49 tag:SJEdgeControlLayerBottomItem_Play];
    [playItem addAction:[SJEdgeControlButtonItemAction actionWithTarget:self action:@selector(_playItemWasTapped)]];
    [self.bottomAdapter addItem:playItem];
    
    [self.bottomAdapter reload];
}


- (void)_addItemsBottomDataAdapter {
    MRKVideoPrepareDataInfo *videoPlayData = _videoPlayer.videoPlayData;
    int equipmentId = videoPlayData.equipmentId.intValue;
    for (MRKEqDisplayModel *model in videoPlayData.deviceDisplayArr) {
        SJEdgeControlButtonItemTag tag = model.displayId.integerValue + 50000;
        BOOL showPointer = NO;
        ///判断划船机 && 桨频
        if (equipmentId == BoatEquipment && tag == SJEdgeControlLayerCenterItem_RateOfStroke){
            showPointer = YES;
        }
        
        ///判断单车或者椭圆机 && 踏频
        if ( (equipmentId == BicycleEquipment || equipmentId == EllipticalEquipment || equipmentId == StairClimbEquipment) && tag == SJEdgeControlLayerCenterItem_RateOfTread){
            showPointer = YES;
        }
        
        SJEdgeControlButtonItem *item = [SJEdgeControlButtonItem placeholderWithSize:showPointer? 150 + 40 : DHPX(90) tag:tag];
        item.dataStr = model.defaultValue;
        item.holdStr = model.name;
        [self.dataAdapter addItem:item];
    }
    
    [self.dataAdapter reload];
}

///刷新BottomDataAdapter
///刷新不支持阻力回显的设备. 阻力模块剔除
- (void)_reloadItemsBottomDataAdapter{
    MRKVideoPrepareDataInfo *videoPlayData = _videoPlayer.videoPlayData;
    int equipmentType = videoPlayData.equipmentId.intValue;
    if (equipmentType == BicycleEquipment ||
        equipmentType == BoatEquipment ||
        equipmentType == EllipticalEquipment ) {
        
        if (videoPlayData.eqModel == nil ){
            return;
        }
        
        ///不支持阻力回显
        if (!videoPlayData.eqModel.isSupportResistanceEcho ){
            [self.dataAdapter removeItemForTag:SJEdgeControlLayerCenterItem_Resistance];
            [self.dataAdapter reload];
        }
    }
}

- (void)_addItemsToCenterAdapter {
    ///播放按钮
    SJEdgeControlButtonItem *playItem = [SJEdgeControlButtonItem placeholderWithType:SJButtonItemPlaceholderType_49x49 tag:SJEdgeControlLayerBottomItem_Play];
    [playItem addAction:[SJEdgeControlButtonItemAction actionWithTarget:self action:@selector(_playItemWasTapped)]];
    [self.centerAdapter addItem:playItem];
    
    [self.centerAdapter reload];
}

#pragma mark ---- 数据处理 ----
- (void)dataAdapterUpdateWithModel:(TrainingShowData *)model{
    @weakify(self);
    dispatch_async(dispatch_get_main_queue(), ^{
        @strongify(self);
        TrainingShowData *m = model;
        self.tyModel = model;
        for (SJEdgeControlButtonItem *item in self.dataAdapter.items) {
            TrainDataType tag = item.tag; //(SJEdgeControlButtonItemTag)
            ///
            switch (tag) {
                case TrainDataType_TrainTime: {///运动时间
                    item.dataStr = m.totalTime;
                }break;
                case TrainDataType_Kcal: {///消耗(kcal)
                    item.dataStr = m.totalKcal;
                }break;
                case TrainDataType_Rate: {///心率(bmp)
                 
                }break;
                case TrainDataType_RateOfTread: case TrainDataType_RateOfStroke:{///踏频(rpm)/桨频(spm)
                    item.dataStr = m.spm;
                }break;
                case TrainDataType_Resistance: {///阻力(lv)
                    item.dataStr = m.resistance;
                }break;
                case TrainDataType_Slope: {///坡度
                    item.dataStr = m.gradient;
                }break;
                case TrainDataType_Distance: {///距离(km)
                    item.dataStr = m.totalDistance;
                }break;
                case TrainDataType_Count:  case TrainDataType_QCount: {///个数/圈数
                    item.dataStr =  m.totalNum;
                }break;
                case TrainDataType_Gear: {///挡位
                    item.dataStr =  m.gear;
                }break;
                case TrainDataType_Speed: { ///速度(km/h)
                    item.dataStr =  m.speed;
                }break;
                case TrainDataType_PCount: { ///力量站次数
                    item.dataStr =  m.totalNum;
                }break;
                default: break;
            }
            [self.dataAdapter updateContentForItemWithTag:tag];
        }
    });
}











#pragma mark ---- ContainerView appear state ----

- (void)_updateAppearStateForContainerViews {
    [self _updateAppearStateForTopContainerView];
    [self _updateAppearStateForBottomContainerView];
    [self _updateAppearStateForCenterContainerView];
    
    [self _updateAppearStateForBottomDataContainerView];
    
    if (@available(iOS 11.0, *)) {
        [self _updateAppearStateForCustomStatusBar];
    }
}

- (void)_updateAppearStateForTopContainerView {
    if ( 0 == _topAdapter.numberOfItems ) {
        sj_view_makeDisappear(_topContainerView, YES);
        return;
    }
    
    /// 锁屏状态下, 使隐藏
    if ( _videoPlayer.isLockedScreen ) {
        sj_view_makeDisappear(_topContainerView, YES);
        return;
    }
    
    /// 是否显示
    if ( _videoPlayer.isControlLayerAppeared ) {
        sj_view_makeAppear(_topContainerView, YES);
    } else {
        sj_view_makeDisappear(_topContainerView, YES);
    }
}



/// 更新显示状态
- (void)_updateAppearStateForBottomContainerView {
    if ( 0 == _bottomAdapter.numberOfItems ) {
        sj_view_makeDisappear(_bottomContainerView, YES);
        return;
    }
    
    /// 是否显示
    if ( _videoPlayer.isControlLayerAppeared ) {
        sj_view_makeAppear(_bottomContainerView, YES);
    } else {
        sj_view_makeDisappear(_bottomContainerView, YES);
    }
}


- (void)_updateAppearStateForBottomDataContainerView {
    if ( 0 == _dataAdapter.numberOfItems ) {
        sj_view_makeDisappear(_bottomDataView, YES);
        return;
    }
    
    if ( _videoPlayer.isControlLayerAppeared ) {
        ///隐藏
        sj_view_makeDisappear(_bottomDataView, YES);
    } else {
        ///显示
        sj_view_makeAppear(_bottomDataView, YES, ^{
           
        });
    }
}




- (void)_updateAppearStateForCenterContainerView {
    if ( 0 == _centerAdapter.numberOfItems ) {
        sj_view_makeDisappear(_centerContainerView, YES);
        return;
    }
    
    sj_view_makeAppear(_centerContainerView, YES);
}

- (void)_updateAppearStateForBottomProgressIndicatorIfNeeded {
    if ( _bottomProgressIndicator == nil )
        return;
    
    _videoPlayer.isControlLayerAppeared && !_videoPlayer.isLockedScreen ?
    sj_view_makeDisappear(_bottomProgressIndicator, YES) :
    sj_view_makeAppear(_bottomProgressIndicator, YES);
}

- (void)_updateAppearStateForCustomStatusBar NS_AVAILABLE_IOS(11.0) {
    BOOL shouldShow = self.shouldShowCustomStatusBar(self);
    if ( shouldShow ) {
        if ( self.customStatusBar.superview == nil ) {
            static dispatch_once_t onceToken;
            dispatch_once(&onceToken, ^{
                UIDevice.currentDevice.batteryMonitoringEnabled = YES;
            });
            
            [NSNotificationCenter.defaultCenter addObserver:self selector:@selector(_reloadCustomStatusBarIfNeeded) name:UIDeviceBatteryLevelDidChangeNotification object:nil];
            [NSNotificationCenter.defaultCenter addObserver:self selector:@selector(_reloadCustomStatusBarIfNeeded) name:UIDeviceBatteryStateDidChangeNotification object:nil];
            
            [self.topContainerView addSubview:self.customStatusBar];
            [self.customStatusBar mas_makeConstraints:^(MASConstraintMaker *make) {
                make.top.offset(0);
                make.left.right.equalTo(self.topAdapter);
                make.height.offset(20);
            }];
        }
    }
    
    _customStatusBar.hidden = !shouldShow;
    _customStatusBar.isHidden ? [self.dateTimerControl interrupt] : [self.dateTimerControl resume];
}


/// 暂不实现
- (void)_updateContentForPictureInPictureItem API_AVAILABLE(ios(14.0)) {
    
    
}







#pragma mark ---- update items ----
- (void)_reloadAdaptersIfNeeded {
    [self _reloadTopAdapterIfNeeded];
    [self _reloadBottomAdapterIfNeeded];
    [self _reloadCenterAdapterIfNeeded];
    
    [self _reloadDataAdapterIfNeeded];
}

- (void)_reloadTopAdapterIfNeeded {
    if ( sj_view_isDisappeared(_topContainerView) ) return;
    
    id<SJVideoPlayerControlLayerResources> sources = SJVideoPlayerConfigurations.shared.resources;
    BOOL isFullscreen = _videoPlayer.isFullscreen;
    BOOL isFitOnScreen = _videoPlayer.isFitOnScreen;
    BOOL isPlayOnScrollView = _videoPlayer.isPlayOnScrollView;
    BOOL isSmallscreen = !isFullscreen && !isFitOnScreen;
    
    // back item
    SJEdgeControlButtonItem *backItem = [self.topAdapter itemForTag:SJEdgeControlLayerTopItem_Back];
    if ( backItem != nil ) {
        if ( _fixesBackItem ) {
            backItem.alpha = 0;
            backItem.image = nil;
        } else {
            if ( isFullscreen || isFitOnScreen )
                backItem.hidden = NO;
            else if ( _hiddenBackButtonWhenOrientationIsPortrait )
                backItem.hidden = YES;
            else
                backItem.hidden = isPlayOnScrollView;
            
            if ( backItem.hidden == NO )
                backItem.image = sources.backImage;
        }
    }
    
    // title item
    SJEdgeControlButtonItem *titleItem = [self.topAdapter itemForTag:SJEdgeControlLayerTopItem_Title];
    if ( titleItem != nil ) {
        if ( self.isHiddenTitleItemWhenOrientationIsPortrait && isSmallscreen ) {
            titleItem.hidden = YES;
        } else {
            if ( titleItem.customView != self.titleView )
                titleItem.customView = self.titleView;
            SJVideoPlayerURLAsset *asset = _videoPlayer.URLAsset;
            NSAttributedString *_Nullable attributedTitle = asset.attributedTitle;
            self.titleView.attributedText = attributedTitle;
            titleItem.hidden = (attributedTitle.length == 0);
        }
        
        if ( titleItem.hidden == NO ) {
            // margin
            NSInteger atIndex = [_topAdapter indexOfItemForTag:SJEdgeControlLayerTopItem_Title];
            CGFloat left  = [_topAdapter isHiddenWithRange:NSMakeRange(0, atIndex)] ? 16 : 0;
            CGFloat right = [_topAdapter isHiddenWithRange:NSMakeRange(atIndex, _topAdapter.numberOfItems)] ? 16 : 0;
            titleItem.insets = SJEdgeInsetsMake(left, right);
        }
    }

    // Device item
    SJEdgeControlButtonItem *deviceItem = [self.topAdapter itemForTag:SJEdgeControlLayerTopItem_Device];
    if ( deviceItem != nil ) {
        deviceItem.hidden = !_videoPlayer.courseModel.isSupportConnection;
        if ( deviceItem.hidden == NO ) {
            MRKVideoPrepareDataInfo *videoPlayData = _videoPlayer.videoPlayData;
            deviceItem.image = [AlivcImage mrkEquipmentImageWith:videoPlayData.equipmentId connect:_videoPlayer.isConnectDevice];
        }
    }
    
    // muted item
    SJEdgeControlButtonItem *mutedItem = [self.topAdapter itemForTag:SJEdgeControlLayerTopItem_Muted];
    if ( mutedItem != nil ) {
        mutedItem.hidden = NO;
        if ( mutedItem.hidden == NO ) {
            mutedItem.image = _videoPlayer.isMuted ? sources.muted_DImage : sources.muted_LImage;
        }
    }
    
    [_topAdapter reload];
}

- (void)_reloadLeftAdapterIfNeeded {
    if ( sj_view_isDisappeared(_leftContainerView) ) return;
    
    SJEdgeControlButtonItem *rankItem = [self.leftAdapter itemForTag:SJEdgeControlLayerLeftItem_Rank];
    if ( rankItem != nil ) {
        BOOL isShowRoll = _videoPlayer.videoPlayData.liveModel.isShowRankControl;
        rankItem.hidden = !isShowRoll;
    }
    [_leftAdapter reload];
}

/**
 刷新静音按钮的状态
 **/
- (void)_updateMutedItemIfNeeded {
    if ( sj_view_isDisappeared(_topContainerView) ) return;
    id<SJVideoPlayerControlLayerResources> sources = SJVideoPlayerConfigurations.shared.resources;
    
    SJEdgeControlButtonItem *mutedItem = [self.topAdapter itemForTag:SJEdgeControlLayerTopItem_Muted];
    if ( mutedItem != nil && !mutedItem.hidden) {
        mutedItem.image = _videoPlayer.isMuted ? sources.muted_DImage : sources.muted_LImage;
        [_topAdapter updateContentForItemWithTag:SJEdgeControlLayerTopItem_Muted];
    }
}



- (void)_reloadSizeAdaptItemIfNeeded {
    if ( sj_view_isDisappeared(_topContainerView) ) return;
    
    id<SJVideoPlayerControlLayerResources> sources = SJVideoPlayerConfigurations.shared.resources;
    // sizeAdaptItem item
    SJEdgeControlButtonItem *sizeAdaptItem = [self.topAdapter itemForTag:SJEdgeControlLayerTopItem_SizeAdapt];
    if ( sizeAdaptItem != nil ) {
        sizeAdaptItem.image = _videoPlayer.isAdaptScreenSizeOn ? sources.sizeAdapt_DImage: sources.sizeAdapt_LImage;
        [_topAdapter updateContentForItemWithTag:SJEdgeControlLayerTopItem_SizeAdapt];
    }
}


- (void)_reloadBottomAdapterIfNeeded {
    if ( sj_view_isDisappeared(_bottomContainerView) ) return;
    
    id<SJVideoPlayerControlLayerResources> sources = SJVideoPlayerConfigurations.shared.resources;
    // play item
    {
        SJEdgeControlButtonItem *playItem = [self.bottomAdapter itemForTag:SJEdgeControlLayerBottomItem_Play];
        if ( playItem != nil && playItem.hidden == NO ) {
            playItem.image = _videoPlayer.isPaused ? sources.playImage : sources.pauseImage;
        }
    }
    
    [_bottomAdapter reload];
}


- (void)reloadDataAdapter{
    MRKVideoPrepareDataInfo *videoPlayData = _videoPlayer.videoPlayData;
    ///添加蓝牙展示数据
    [self _addItemsBottomDataAdapter];
    
    // GCD 延时执行
    dispatch_time_t delayTime = dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC));
    dispatch_after(delayTime, dispatch_get_main_queue(), ^{
        [self _reloadDataAdapterIfNeeded];
    });
}

- (TrainDataGaugeModel *)TrainDataGaugeModel{
    MRKVideoPrepareDataInfo *videoPlayData = _videoPlayer.videoPlayData;
    TrainDataGaugeModel *model = [[TrainDataGaugeModel alloc] init];
    model.equipmentType = videoPlayData.equipmentId.intValue;
    model.planMinDataValue = videoPlayData.planModel.minNum;
    model.planMaxDataValue = videoPlayData.planModel.maxNum;
    model.minDataValue = @0;
    model.maxDataValue = ({
        int equipmentId = videoPlayData.equipmentId.intValue;
        NSInteger maxValue = 0; ///默认为0
        if (equipmentId == BoatEquipment) { maxValue = 60; }
        if (equipmentId == EllipticalEquipment) { maxValue = 150; }
        if (equipmentId == BicycleEquipment) { maxValue = 160; }
        if (equipmentId == StairClimbEquipment) { maxValue = 110; }
        @(maxValue);
    });
    return model;
}

- (void)_reloadDataAdapterIfNeeded {
//    if ( sj_view_isDisappeared(_bottomDataView) ) return;
    
    MRKVideoPrepareDataInfo *videoPlayData = _videoPlayer.videoPlayData;
    int equipmentId = videoPlayData.equipmentId.intValue;
    switch (equipmentId) {
        case BoatEquipment: case BicycleEquipment: case EllipticalEquipment: {
            
            // 阻力 item
            SJEdgeControlButtonItem *resistanceItem = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_Resistance];
            if ( resistanceItem != nil) {
                if (_videoPlayer.videoPlayData.isShowAutoControl) {
                    resistanceItem.showControl = _videoPlayer.useLessonPlanControl;
                }else{
                    resistanceItem.showControl = NO;
                }
            }
            
            // 踏频(rpm) item
            SJEdgeControlButtonItem *treadItem = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_RateOfTread];
            if ( treadItem != nil && videoPlayData.playbackType != SJPlaybackTypeUnknown) {
                if (treadItem.partView != nil) {
                    TrainDataItemView *partView = treadItem.partView;
                    partView.showPointer = YES;
                    partView.dialModel = [self TrainDataGaugeModel];
                }
            }
            
            // 桨频(spm) item
            SJEdgeControlButtonItem *strokeItem = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_RateOfStroke];
            if ( strokeItem != nil && videoPlayData.playbackType != SJPlaybackTypeUnknown) {
                if (strokeItem.partView != nil) {
                    TrainDataItemView *partView = strokeItem.partView;
                    partView.showPointer = YES;
                    partView.dialModel = [self TrainDataGaugeModel];
                }
            }
        } break;
            
        case TreadmillEquipment: {
            // 速度 item
            SJEdgeControlButtonItem *speedItem = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_Speed];
            if ( speedItem != nil) {
                if (_videoPlayer.videoPlayData.isShowAutoControl) {
                    speedItem.showControl = _videoPlayer.useLessonPlanControl;
                } else {
                    speedItem.showControl = NO;
                }
            }
            
            // 坡度 item
            SJEdgeControlButtonItem *slopeItem = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_Slope];
            if ( slopeItem != nil) {
                if (_videoPlayer.videoPlayData.isShowAutoControl) {
                    slopeItem.showControl = _videoPlayer.useLessonPlanControl;
                }else{
                    slopeItem.showControl = NO;
                }
            }
        } break;
            
        case StairClimbEquipment: {
            // 踏频(rpm) item
            SJEdgeControlButtonItem *treadItem = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_RateOfTread];
            if ( treadItem != nil && videoPlayData.playbackType != SJPlaybackTypeUnknown) {
                if (treadItem.partView != nil) {
                    TrainDataItemView *partView = treadItem.partView;
                    partView.showPointer = YES;
                    partView.dialModel = [self TrainDataGaugeModel];
                }
            }
        } break;
        default: break;
    }
    
    [self.dataAdapter reload];
}


/**
 断连清数据
 @note 断连后 踏频/桨频/速度 清空
 **/
- (void)_clearDataAdapterIfNeeded {
//    if ( sj_view_isDisappeared(_bottomDataView) ) return;
    
    MRKVideoPrepareDataInfo *videoPlayData = _videoPlayer.videoPlayData;
    int equipmentId = videoPlayData.equipmentId.intValue;
    switch (equipmentId) {
        case BoatEquipment: case BicycleEquipment: case EllipticalEquipment: {
            // 踏频(rpm) item
            SJEdgeControlButtonItem *treadItem = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_RateOfTread];
            if ( treadItem != nil ) {
                treadItem.dataStr = @"--";
                [self.dataAdapter updateContentForItemWithTag:SJEdgeControlLayerCenterItem_RateOfTread];
            }
            
            // 桨频(spm) item
            SJEdgeControlButtonItem *strokeItem = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_RateOfStroke];
            if ( strokeItem != nil ) {
                strokeItem.dataStr = @"--";
                [self.dataAdapter updateContentForItemWithTag:SJEdgeControlLayerCenterItem_RateOfStroke];
            }
        }break;
        case TreadmillEquipment: {
            // 速度 item
            SJEdgeControlButtonItem *speedItem = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_Speed];
            if ( speedItem != nil) {
                speedItem.dataStr = @"--";
                [self.dataAdapter updateContentForItemWithTag:SJEdgeControlLayerCenterItem_Speed];
            }
        }break;
        case StairClimbEquipment: {
            
            // 踏频(rpm) item
            SJEdgeControlButtonItem *treadItem = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_RateOfTread];
            if ( treadItem != nil ) {
                treadItem.dataStr = @"--";
                [self.dataAdapter updateContentForItemWithTag:SJEdgeControlLayerCenterItem_RateOfTread];
            }
            
        } break;
        default: break;
    }
}

- (void)_reloadCenterAdapterIfNeeded {
    if ( sj_view_isDisappeared(_centerContainerView) ) return;
    
    SJEdgeControlButtonItem *playItem = [self.centerAdapter itemForTag:SJEdgeControlLayerBottomItem_Play];
    if ( playItem != nil ) {
        BOOL isPaused = _videoPlayer.isPaused && !_videoPlayer.isPlaybackFinished;
        playItem.hidden = !isPaused;
        if ( playItem.hidden == NO ) {
            playItem.image = [AlivcImage imageNamed:@"icon_big_play"];
        }
    }
    
    [_centerAdapter reload];
}




/**刷新设备连接按钮的状态**/
- (void)_updateConnectForTopDeviceItemIfNeeded {
    if ( sj_view_isDisappeared(_topContainerView) ) return;
    
    SJEdgeControlButtonItem *deviceItem = [self.topAdapter itemForTag:SJEdgeControlLayerTopItem_Device];
    if ( deviceItem != nil && deviceItem.hidden == NO ) {
        MRKVideoPrepareDataInfo *videoPlayData = _videoPlayer.videoPlayData;
        deviceItem.image = [AlivcImage mrkEquipmentImageWith:videoPlayData.equipmentId connect:_videoPlayer.isConnectDevice];
        [_topAdapter updateContentForItemWithTag:SJEdgeControlLayerTopItem_Device];
    }
}

/**刷新视频进度时长**/
- (void)_updateContentForBottomCurrentTimeItemIfNeeded {
    if ( sj_view_isDisappeared(_bottomContainerView) ) return;
    
    NSString *currentTimeStr = [_videoPlayer stringForSeconds:_videoPlayer.currentTime];
    SJEdgeControlButtonItem *currentTimeItem = [_bottomAdapter itemForTag:SJEdgeControlLayerBottomItem_CurrentTime];
    if ( currentTimeItem != nil && currentTimeItem.isHidden == NO ) {
        currentTimeItem.title = [self _textForTimeString:currentTimeStr];
        [_bottomAdapter updateContentForItemWithTag:SJEdgeControlLayerBottomItem_CurrentTime];
    }
}

/**刷新视频时长**/
- (void)_updateContentForBottomDurationItemIfNeeded {
    SJEdgeControlButtonItem *durationTimeItem = [_bottomAdapter itemForTag:SJEdgeControlLayerBottomItem_DurationTime];
    if ( durationTimeItem != nil && durationTimeItem.isHidden == NO ) {
        durationTimeItem.title = [self _textForTimeString:[_videoPlayer stringForSeconds:_videoPlayer.duration]];
        [_bottomAdapter updateContentForItemWithTag:SJEdgeControlLayerBottomItem_DurationTime];
    }
}

- (void)_reloadSizeForBottomTimeLabel {
    // 00:00
    // 00:00:00
    NSString *ms = @"00:00";
    NSString *hms = @"00:00:00";
    NSString *durationTimeStr = [_videoPlayer stringForSeconds:_videoPlayer.duration];
    NSString *format = (durationTimeStr.length == ms.length) ? ms:hms;
    CGSize formatSize = [[self _textForTimeString:format] sj_textSize];
    
    SJEdgeControlButtonItem *currentTimeItem = [_bottomAdapter itemForTag:SJEdgeControlLayerBottomItem_CurrentTime];
    SJEdgeControlButtonItem *durationTimeItem = [_bottomAdapter itemForTag:SJEdgeControlLayerBottomItem_DurationTime];
    
    if ( !durationTimeItem && !currentTimeItem ) return;
    currentTimeItem.size = formatSize.width;
    durationTimeItem.size = formatSize.width;
    [_bottomAdapter reload];
}

- (void)_updateContentForBottomProgressIndicatorIfNeeded {
    if ( _bottomProgressIndicator != nil && !sj_view_isDisappeared(_bottomProgressIndicator) ) {
        _bottomProgressIndicator.value = _videoPlayer.currentTime;
        _bottomProgressIndicator.maxValue = _videoPlayer.duration ? : 1;
    }
}

/**
 *录播快结束前10s加提示
 */
- (void)_updateVODPlayerLast10SecondsTipIfNeeded:(NSTimeInterval)currentTime {
    if (_videoPlayer.playbackType != SJPlaybackTypeVOD) return;
    if ( self.hasToast10SecondsAlert) return;
    
    NSTimeInterval last10Seconds = _videoPlayer.duration - 5;
    if (last10Seconds <= 0){ return; }
    if (currentTime >= last10Seconds){
        self.hasToast10SecondsAlert = YES;
        [self videoTipString:@"课程已结束，即将退出"];
    }
}

- (void)_updateAppearStateForResidentBackButtonIfNeeded {
    if ( !_fixesBackItem )
        return;
    BOOL isFitOnScreen = _videoPlayer.isFitOnScreen;
    BOOL isFull = _videoPlayer.isFullscreen;
    BOOL isLockedScreen = _videoPlayer.isLockedScreen;
    if ( isLockedScreen ) {
        _fixedBackButton.hidden = YES;
    }
    else {
        BOOL isPlayOnScrollView = _videoPlayer.isPlayOnScrollView;
        _fixedBackButton.hidden = isPlayOnScrollView && !isFitOnScreen && !isFull;
    }
}

- (void)_updateNetworkSpeedStrForLoadingView {
    if ( !_videoPlayer || !self.loadingView.isAnimating )
        return;
    
    if ( self.loadingView.showsNetworkSpeed && ![_videoPlayer.URLAsset.mediaURL isFileURL] ) {
        self.loadingView.networkSpeedStr = [NSAttributedString sj_UIKitText:^(id<SJUIKitTextMakerProtocol>  _Nonnull make) {
            id<SJVideoPlayerControlLayerResources> resources = SJVideoPlayerConfigurations.shared.resources;
            make.font(resources.loadingNetworkSpeedTextFont);
            make.textColor(resources.loadingNetworkSpeedTextColor);
            make.alignment(NSTextAlignmentCenter);
            make.append(self.videoPlayer.reachability.networkSpeedStr);
        }];
    } else {
        self.loadingView.networkSpeedStr = nil;
    }
}

- (void)_reloadCustomStatusBarIfNeeded NS_AVAILABLE_IOS(11.0) {
    if ( sj_view_isDisappeared(_customStatusBar) )
        return;
    _customStatusBar.networkStatus = _videoPlayer.reachability.networkStatus;
    _customStatusBar.date = NSDate.date;
    _customStatusBar.batteryState = [UIDevice currentDevice].batteryState;
    _customStatusBar.batteryLevel = [UIDevice currentDevice].batteryLevel;
}

#pragma mark  ---------------- Progres---------------

- (nullable NSAttributedString *)_textForTimeString:(NSString *)timeStr {
    id<SJVideoPlayerControlLayerResources> resources = SJVideoPlayerConfigurations.shared.resources;
    NSAttributedString *string = [NSAttributedString sj_UIKitText:^(id<SJUIKitTextMakerProtocol>  _Nonnull make) {
        make.append(timeStr).font(resources.timeLabelFont).textColor(resources.timeLabelColor).alignment(NSTextAlignmentCenter);
    }];
    return string;
}

/// 此处为重置控制层的隐藏间隔.(如果点击到当前控制层上的item, 则重置控制层的隐藏间隔)
- (void)_resetControlLayerAppearIntervalForItemIfNeeded:(NSNotification *)note {
    SJEdgeControlButtonItem *item = note.object;
    if ( item.resetsAppearIntervalWhenPerformingItemAction ) {
        if ( [_topAdapter containsItem:item] ||
             [_leftAdapter containsItem:item] ||
             [_bottomAdapter containsItem:item] ||
             [_rightAdapter containsItem:item])
        {
            [_videoPlayer controlLayerNeedAppear];
        }
    }
}

- (void)_showOrRemoveBottomProgressIndicator {
    if ( _hiddenBottomProgressIndicator || _videoPlayer.playbackType == SJPlaybackTypeLIVE ) {
        if ( _bottomProgressIndicator ) {
            [_bottomProgressIndicator removeFromSuperview];
            _bottomProgressIndicator = nil;
        }
    } else {
        if ( !_bottomProgressIndicator ) {
            [self.controlView addSubview:self.bottomProgressIndicator];
            [self _updateLayoutForBottomProgressIndicator];
        }
    }
}

- (void)_updateLayoutForBottomProgressIndicator {
    if ( _bottomProgressIndicator == nil ) return;
    _bottomProgressIndicator.trackHeight = _bottomProgressIndicatorHeight;
    if (_screen.is_iPhoneX) {
        _bottomProgressIndicator.frame = (CGRect){kScreenPadding+20, self.bounds.size.height - _bottomProgressIndicatorHeight, self.bounds.size.width - kScreenPadding*2 - 20*2, _bottomProgressIndicatorHeight};
    } else {
        _bottomProgressIndicator.frame = (CGRect){0, self.bounds.size.height - _bottomProgressIndicatorHeight, self.bounds.size.width, _bottomProgressIndicatorHeight};
    }
}

- (void)_showOrHiddenLoadingView {
    if ( _videoPlayer == nil || _videoPlayer.URLAsset == nil ) {
        [self.loadingView stop];
        return;
    }
    
    if ( _videoPlayer.isPaused ) {
        [self.loadingView stop];
    }
    else if ( _videoPlayer.assetStatus == SJAssetStatusPreparing ) {
        [self.loadingView start];
    }
    else if ( _videoPlayer.assetStatus == SJAssetStatusFailed ) {
        [self.loadingView stop];
    }
    else if ( _videoPlayer.assetStatus == SJAssetStatusReadyToPlay ) {
        self.videoPlayer.reasonForWaitingToPlay == SJWaitingToMinimizeStallsReason ? [self.loadingView start] : [self.loadingView stop];
    }
}












#pragma mark  ---------------- 课程指令设置 ---------------
///接收到指令信息
- (void)sendInstructionsWithData:(CourseLinkModel *)model{
    if (!model) return;
    
    // 判断是Merit课程 和 是电磁控设备 才发指令
    if (_videoPlayer.videoPlayData.isMeritControl) {
        [self instructionsOperationData:model];
    }
}

///指令操作
- (void)instructionsOperationData:(CourseLinkModel *)model{
    ///教案关闭
    if (!self.videoPlayer.isUseLessonPlanControl) { return; }
    
    NSString *typeId = _videoPlayer.videoPlayData.equipmentId;
    EquipmentDetialModel *eqModel = _videoPlayer.videoPlayData.eqModel;
    
    ///跑步机
    if (typeId.intValue == TreadmillEquipment) {
        ///跑步机是否有坡度下发
        BOOL treadmillHasSlopeControl = _videoPlayer.videoPlayData.planModel.treadmillHasSlopeControl;
        
        ///跑步机是否需要下发指令
        if (![_videoPlayer.trainManager isNeedInstructionWithModel:model]) {
            return;
        }
        
        NSDictionary *orderParms = nil;                                                 ///指令数据
        NSMutableString *tipStr = [[NSMutableString alloc] init];                       ///提示字段
        
        NSNumber *speedOrderNum = model.minNum;
        NSNumber *slopeOrderNum = model.adviseNum;
        double speed = 0.0;
        if (speedOrderNum.doubleValue > 0) {
            speed = model.minNum.doubleValue/10;
            if (speed > eqModel.maxSpeed.intValue && eqModel.maxSpeed.intValue != 0) {
                speed = eqModel.maxSpeed.intValue;
            }
        }
        NSString *speedTipStr =[NSString stringWithFormat:@"%.1f", speed];
        /// 跑步机调节速度
        SJEdgeControlButtonItem *speeditem = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_Speed];
        if (speeditem != nil) {
            speedOrderNum = @(speed*10);
            
            ///速度Item加提示
            TrainDataItemView *speedItemView = speeditem.partView;
            [speedItemView reloadNumTip:@(speed)];
            
            [tipStr appendString:CombineString(@"速度将调至", speedTipStr)];
        }
        
        /// 跑步机是否可调节坡度
        /// @note 有负坡度显示
        SJEdgeControlButtonItem *slopeitem = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_Slope];
        if (slopeitem != nil && eqModel.showSlope.boolValue && treadmillHasSlopeControl) {
            int slopeNum = model.adviseNum.intValue;
            if (slopeNum > eqModel.maxSlope.intValue) {
                slopeNum = eqModel.maxSlope.intValue;
            }
            if (slopeNum < eqModel.minSlope.intValue) {
                slopeNum = eqModel.minSlope.intValue;
            }
          
            slopeOrderNum = @(slopeNum);
            ///坡度Item加提示
            TrainDataItemView *slopeItemView = slopeitem.partView;
            [slopeItemView reloadNumTip:@(slopeNum)];
            
            NSString *slopeStr = [NSString stringWithFormat:@"%d", slopeNum];
            NSString *slopeTipStr = [NSString stringWithFormat:@"%@坡度将调至", tipStr.length > 0 ? @"，" : @""];
            [tipStr appendString:CombineString(slopeTipStr, slopeStr)];
        }
        
        if ([tipStr isEmpty]) return;
        ///
        orderParms = @{
            Speed : speedOrderNum ?:@0,
            Slope : slopeOrderNum ?:@0,
            BlueDeviceType : typeId,
            @"status" : @(_videoPlayer.trainManager.treamillStatus)
        };
        [self setOrderNotificationData:orderParms
                             andTipStr:tipStr
                        andControlType:@"3"
                      andControlNumber:speedTipStr];
    }
    
    ///划船机,单车,力量站 椭圆机 可支持坡度调节
    if(typeId.intValue == BoatEquipment ||
       typeId.intValue == BicycleEquipment ||
       typeId.intValue == PowerEquipment ||
       typeId.intValue == EllipticalEquipment) {
        
        SJEdgeControlButtonItem *slopeitem = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_Slope];
        SJEdgeControlButtonItem *item = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_Resistance]; ///
   
        ///没有坡度，阻力为0
        if (slopeitem == nil && model.adviseNum.intValue == 0) {
            return;
        }
        
        @weakify(self);
        [_videoPlayer.videoPlayData mappingResistance:model completion:^(CourseLinkModel * _Nonnull mod) {
            @strongify(self);
            NSMutableDictionary *orderParms = [NSMutableDictionary dictionaryWithDictionary:@{
                BlueDeviceType:typeId
            }];
            
            NSMutableString *tipStr = [[NSMutableString alloc] init];    ///提示字段
            ///锚点/阻力Item加提示
            if (item != nil && model.adviseNum.intValue > 0) {
                [orderParms setObject:mod.adviseNum?:@0 forKey:Resistance];
                
                TrainDataItemView *partView = item.partView;
                [partView reloadNumTip:mod.adviseNum];
                
                [tipStr appendString:CombineString(@"阻力将调至", mod.adviseNum.stringValue)];
            }
            
            ///坡度Item加提示
            if (slopeitem != nil && eqModel.showSlope.boolValue && mod.isChangeSlope) {
                [orderParms setObject:mod.slopeNum?:@0 forKey:Slope];
                
                TrainDataItemView *slopePartView = slopeitem.partView;
                [slopePartView reloadNumTip:mod.slopeNum];
                
                NSString *firTipStr = [NSString stringWithFormat:@"%@坡度将调至", model.adviseNum.intValue > 0 ? @"，" : @"" ];
                [tipStr appendString:CombineString(firTipStr, mod.slopeNum.stringValue)];
            }
            
            if ([tipStr isNotBlank]) {
                [self setOrderNotificationData:orderParms
                                     andTipStr:tipStr
                                andControlType:model.adviseNum.intValue > 0 ? @"1" : @"2"
                              andControlNumber:[NSString stringWithFormat:@"%@", model.adviseNum.intValue > 0 ? model.adviseNum : mod.slopeNum]];
            }
        }];
    }
}

NS_INLINE NSString *CombineString(NSString *str1, NSString *str2) {
    return [NSString stringWithFormat:@"%@%@", str1?:@"", str2?:@""];
}

///设备指令通知
- (void)setOrderNotificationData:(NSDictionary *)data
                       andTipStr:(NSString *)tip
                  andControlType:(NSString *)controlType
                andControlNumber:(NSString *)controlNumber{
    
    if (!self.hasToastAlert) {
        self.hasToastAlert = YES;
        
        @weakify(self);
        void(^sendNotificationBlock)(void) = ^{
            @strongify(self);
            if (!self) return;
            self.hasToastAlert = NO;
            ///教案是否开启
            if (self.videoPlayer.isUseLessonPlanControl){
                [[NSNotificationCenter defaultCenter] postNotificationName:SetResistanceSlopeSpeedNotification object:data];
            }
            if (self.delegate && [self.delegate respondsToSelector:@selector(updateTeachPlanOperation:)]) {
                [self.delegate updateTeachPlanOperation:NO];
            }
            self.videoPlayer.trainManager.isSendControl = NO;
        };
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(updateTeachPlanOperation:)]) {
            [self.delegate updateTeachPlanOperation:YES];
        }
        
        if ([[UIApplication sharedApplication] applicationState] == UIApplicationStateBackground &&
            self.videoPlayer.courseModel.equipmentId.intValue != TreadmillEquipment &&
            tip.length > 0) {
            /// 后台且不是跑步机
            /// 播放语音
            [[SpeechSynthesizerManager shared] speak:tip];
            
            self.videoPlayer.trainManager.controlType = controlType;
            self.videoPlayer.trainManager.controlNumber = controlNumber;
            self.videoPlayer.trainManager.isSendControl = YES;
        }
        
        [MBProgressHUD showMiaToast:tip toView:self afterDelay:5.0];
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(5.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            sendNotificationBlock();
        });
        MLog(@"_setOrderNotificationData下发指令 ==== %@", data);
    }
}


/**
 刷新建议提示, 不判断设备
 */
- (void)refreshNoteTips{
    /// 判断设备有无连接设备
    if (!_videoPlayer.videoPlayData.isConnectMachine){
        return;
    }
    
    /// 当前无小节model
    if (!self.currentlinkModel) {
        return;
    }
    
    /// isMeritControl && 开启AI调控
    if (_videoPlayer.videoPlayData.isMeritControl && self.videoPlayer.isUseLessonPlanControl) {
        return;
    }
    
    /**
     @note 考虑到会有[速度,坡度] [阻力, 坡度]同时存在的情况
     # minNum;           速度 [麦瑞克跑步机均为Merit设备, 暂不考虑]
     # adviseNum;        阻力
     # slopeNum;         坡度
     */
    CourseLinkModel *model = self.currentlinkModel;
    ///
    NSString *typeId = _videoPlayer.videoPlayData.equipmentId;
    EquipmentDetialModel *eqModel = _videoPlayer.videoPlayData.eqModel;
    
    ///跑步机
    if (typeId.intValue == TreadmillEquipment) {
        NSMutableString *tipStr = [[NSMutableString alloc] init];///提示字段
        
        /// 速度
        NSNumber *speedOrderNum = model.minNum;
        double speed = 0.0;
        if (speedOrderNum.doubleValue > 0) {
            speed =speedOrderNum.doubleValue/10;
            if (speed > eqModel.maxSpeed.intValue && eqModel.maxSpeed.intValue != 0) {
                speed = eqModel.maxSpeed.intValue;
            }
        }
        if (speed > 0) {
            NSString *speedTipStr =[NSString stringWithFormat:@"%.1f", speed];
            [tipStr appendString:CombineString(@"建议速度", speedTipStr)];
        }
        
        /// 坡度
        int slopeNum = model.adviseNum.intValue;
        if (slopeNum > eqModel.maxSlope.intValue) {
            slopeNum = eqModel.maxSlope.intValue;
        }
        if (slopeNum < eqModel.minSlope.intValue) {
            slopeNum = eqModel.minSlope.intValue;
        }
        if (slopeNum > 0) {
            NSString *slopeStr = [NSString stringWithFormat:@"%d", slopeNum];
            NSString *slopeTipStr = [NSString stringWithFormat:@"%@坡度", tipStr.length > 0 ? @"，" : @"建议"];
            [tipStr appendString:CombineString(slopeTipStr, slopeStr)];
        }
      
        if ([tipStr isEmpty]) return;
        [MBProgressHUD showMiaToast:tipStr toView:self];
        return;
    }
    
    if (typeId.intValue == BoatEquipment ||
       typeId.intValue == BicycleEquipment ||
       typeId.intValue == PowerEquipment ||
       typeId.intValue == EllipticalEquipment) {

        @weakify(self);
        [_videoPlayer.videoPlayData mappingResistance:model completion:^(CourseLinkModel * _Nonnull mod) {
            @strongify(self);
            NSMutableString *tipStr = [[NSMutableString alloc] init];///提示字段
            ///
            ///[阻力]
            if (mod.adviseNum.intValue > 0){
                [tipStr appendString:[NSString stringWithFormat:@"建议阻力%@", mod.adviseNum]];
            }
            
            ///[坡度]
            if (mod.slopeNum.intValue > 0) {
                NSString *slopeStr = [NSString stringWithFormat:@"%@", mod.slopeNum];
                NSString *slopeTipStr = [NSString stringWithFormat:@"%@坡度", tipStr.length > 0 ? @"，" : @"建议"];
                [tipStr appendString:CombineString(slopeTipStr, slopeStr)];
            }
            
            if ([tipStr isEmpty]) return;
            [MBProgressHUD showMiaToast:tipStr toView:self];
        }];
    }
}



@end

