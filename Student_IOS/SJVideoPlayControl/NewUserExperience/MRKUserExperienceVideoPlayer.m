//
//  MRKUserExperienceVideoPlayer.m
//  Student_IOS
//
//  Created by <PERSON><PERSON> on 2023/12/27.
//

#import "MRKUserExperienceVideoPlayer.h"
#import "UIView+SJAnimationAdded.h"
#import <objc/message.h>
#import "SJReachability.h"
#import "SJBaseVideoPlayer.h"
#import "SJBaseVideoPlayerConst.h"
#import "UIView+SJBaseVideoPlayerExtended.h"
#import "NSTimer+SJAssetAdd.h"
#import "MRKVideoDefinitionView.h"
#import "MRKTraceManager.h"
#import "VideoListModel.h"
#import "NSAttributedString+SJMake.h"

NS_ASSUME_NONNULL_BEGIN

@interface MRKUserExperienceVideoPlayer () <SJSwitchVideoDefinitionViewDelegate,
SJNotReachableControlLayerDelegate,
MRKUserExperienceControlLayerDelegate,
MRKVideoPlayDoneLayerDelegate>

@property (nonatomic, strong, readonly) SJVideoDefinitionSwitchingInfoObserver *sj_switchingInfoObserver;
@property (nonatomic, strong, readonly) id<SJControlLayerAppearManagerObserver> sj_appearManagerObserver;
@property (nonatomic, strong, readonly) id<SJControlLayerSwitcherObserver> sj_switcherObserver;
@property (nonatomic, strong, nullable) SJEdgeControlButtonItem *definitionItem;
/// 用于断网之后(当网络恢复后使播放器自动恢复播放)
@property (nonatomic, strong, nullable) id<SJReachabilityObserver> sj_reachabilityObserver;
@property (nonatomic, strong, nullable) NSTimer *sj_timeoutTimer;
@property (nonatomic) BOOL sj_isTimeout;
@end

@implementation MRKUserExperienceVideoPlayer

- (void)dealloc {
    [NSNotificationCenter.defaultCenter removeObserver:self];
#ifdef DEBUG
    NSLog(@"%d \t %s", (int)__LINE__, __func__);
#endif
}

+ (NSString *)version {
    return @"v3.3.2";
}

+ (instancetype)player {
    return [[self alloc] init];
}

- (instancetype)init {
    self = [self _init];
    if ( !self ) return nil;
    
    [self.switcher switchControlLayerForIdentifier:SJControlLayer_Edge];   // 切换到添加的控制层
    self.defaultEdgeControlLayer.hiddenBottomProgressIndicator = NO;       // 显示底部进度条
    self.defaultEdgeControlLayer.bottomProgressIndicatorHeight = 2;
    
    self.useLessonPlanControl = YES;                                       // 默认使用教案
    self.closeBarrageControl = NO;                                         // 默认不关闭弹幕
    
    return self;
}

- (instancetype)_init {
    self = [super init];
    if ( !self ) return nil;
    
    [self _observeNotifies];
    [self _initializeSwitcher];
    [self _initializeSwitcherObserver];
    [self _initializeSettingsObserver];
    [self _initializeAppearManagerObserver];
    [self _initializeReachabilityObserver];
    [self _configurationsDidUpdate];
    
    return self;
}



///
/// 点击了切换清晰度按钮
///
- (void)_definitionItemWasTapped:(SJEdgeControlButtonItem *)definitionItem {
    self.defaultSwitchVideoDefinitionView.assets = self.definitionURLAssets;
    self.defaultSwitchVideoDefinitionView.videoPlayer = self;
    [self.defaultSwitchVideoDefinitionView showIn:self.view];
}

/**
- (void)_definitionItemWasTapped:(SJEdgeControlButtonItem *)definitionItem {
    self.defaultSwitchVideoDefinitionControlLayer.assets = self.definitionURLAssets;
    [self.switcher switchControlLayerForIdentifier:SJControlLayer_SwitchVideoDefinition];
}
*/

///
/// 点击了返回按钮
///
- (void)_backButtonWasTapped {
    
    dispatch_async(dispatch_get_main_queue(), ^{
        UIViewController *vc = [UIViewController currentViewController];
        [vc.view endEditing:YES];
        if (vc.presentingViewController ) {
            [vc dismissViewControllerAnimated:YES completion:nil];
        }else{
            [vc.navigationController popViewControllerAnimated:YES];
        }
    });
    ///log
    ReportMrkLogParms(2, @"视频返回", @"page_play", @"btn_play_return", nil, 0, @{@"courseId": self.courseModel.courseId?:@""});
}



#pragma mark - SJSwitchVideoDefinitionViewDelegate
/// 开通VIP跳转
- (void)openCourseVIPView:(MRKSwitchVideoDefinitionView *)view withData:(nullable id)data{
    [[RouteManager sharedInstance] skipVIP];
}

/// 修改清晰度
- (void)definitionView:(MRKSwitchVideoDefinitionView *)view didSelectAsset:(SJVideoPlayerURLAsset *)asset{
    [view hide];
    
    SJVideoPlayerURLAsset *selected = self.URLAsset;
    ///判断直播降级过, 切换的playerUrl给扭转回去 3/23 Junq
    ///=================================
    if (self.playbackType == SJPlaybackTypeLIVE && !selected.isPlayRtsUrl) {
        AVPUrlSource *source = (AVPUrlSource *)selected.source;
        if (![source.playerUrl.absoluteString isEqualToString:selected.rtsPlayUrl] && [selected.rtsPlayUrl isNotBlank]){
            selected.isPlayRtsUrl = YES;
            source.playerUrl = [NSURL URLWithString:selected.rtsPlayUrl];
        }
    }
    ///=================================

    SJVideoDefinitionSwitchingInfo *info = self.definitionSwitchingInfo;
    if ( info.switchingAsset != nil && info.status != SJDefinitionSwitchStatusFailed ) {
        selected = info.switchingAsset;
    }
    
    if ( asset != selected ) {
        [self sj_switchingInfoObserver];
        [self switchVideoDefinition:asset];
    }
}

- (void)controlLayerDidSelectAsset:(SJVideoPlayerURLAsset *)asset{
    SJVideoPlayerURLAsset *selected = self.URLAsset;
    SJVideoDefinitionSwitchingInfo *info = self.definitionSwitchingInfo;
    if ( info.switchingAsset != nil && info.status != SJDefinitionSwitchStatusFailed ) {
        selected = info.switchingAsset;
    }

    if ( asset != selected ) {
        [self sj_switchingInfoObserver];
        [self switchVideoDefinition:asset];
    }
}



///
/// 点击了控制层空白区域
///
- (void)tappedBlankAreaOnTheControlLayer:(id<SJControlLayer>)controlLayer {
    [self.switcher switchToPreviousControlLayer];
}

- (void)closeGuideControlLayer:(id<SJControlLayer>)controlLayer{
    [self.switcher switchControlLayerForIdentifier:SJControlLayer_Edge];
    if (self.videoWarnControlLayerHidden) {
        self.videoWarnControlLayerHidden();
    }
}

#pragma mark - MrkVideoWarnControlLayerDelegate
/// 超燃脂温馨提示页面暂不开始, 直接返回
- (void)backItemWasTappedForWarnControlLayer:(id<SJControlLayer>)controlLayer{
    [self _backButtonWasTapped];
}

/// 点击开始上课
- (void)classBeginWasTappedForControlLayer:(id<SJControlLayer>)controlLayer {
    [self.switcher switchControlLayerForIdentifier:SJControlLayer_Edge];
    if (self.videoWarnControlLayerHidden) {
        self.videoWarnControlLayerHidden();
    }
}


///
/// 点击了控制层上的返回按钮
///
- (void)backItemWasTappedForControlLayer:(id<SJControlLayer>)controlLayer {
    NSLog(@"video ===== backItemWasTappedForControlLayer");
    
    /// 直播转码层返回直接返回
    if ( self.playbackController.currentTime <= 0) {
        [self _backButtonWasTapped];
        return;
    }
    
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wundeclared-selector"
    if ([self.controllerVC respondsToSelector:@selector(controllerPopBack:)]) {
        [self.controllerVC performSelector:@selector(controllerPopBack:) withObject:controlLayer];
    }
#pragma clang diagnostic pop
}



#pragma mark - MRKVideoPlayDoneLayerDelegate
/// 返回训练报告
- (void)backItemWasTappedToReportController:(id<SJControlLayer>)controlLayer{
    [self.trainManager skipReportVC];
}



#pragma mark - SJNotReachableControlLayerDelegate
/// 点击了控制层上的刷新按钮
- (void)reloadItemWasTappedForControlLayer:(id<SJControlLayer>)controlLayer {
    [self refresh];
    [self.switcher switchControlLayerForIdentifier:SJControlLayer_Edge];
}

#pragma mark - 点击了控制层开通会员
- (void)openCourseVIPForControlLayer:(id<SJControlLayer>)controlLayer withData:(nullable id)data{
    [[RouteManager sharedInstance] skipVIP];
}

#pragma mark - 添加设备选择view
- (void)controlLayerAddDeviceListView{
    self.deviceListView.buildDeviceArr = self.videoPlayData.buildDeviceArr;
    [self.deviceListView showIn:self.view];
}






#pragma mark - 添加更多view
- (void)controlLayerMoreOperationView{
 
}


#pragma mark - 添加带货商品窗view
- (void)controlLayerWebProductView:(id)model {

}



#pragma mark - Lazy -

@synthesize defaultEdgeControlLayer = _defaultEdgeControlLayer;
- (MRKUserExperienceControlLayer *)defaultEdgeControlLayer {
    if ( !_defaultEdgeControlLayer ) {
          _defaultEdgeControlLayer = [MRKUserExperienceControlLayer new];
//        _defaultEdgeControlLayer.delegate = self;
    }
    return _defaultEdgeControlLayer;
}

@synthesize defaultLoadFailedControlLayer = _defaultLoadFailedControlLayer;
- (SJLoadFailedControlLayer *)defaultLoadFailedControlLayer {
    if ( !_defaultLoadFailedControlLayer ) {
        _defaultLoadFailedControlLayer = [SJLoadFailedControlLayer new];
        _defaultLoadFailedControlLayer.delegate = self;
    }
    return _defaultLoadFailedControlLayer;
}

@synthesize defaultNotReachableControlLayer = _defaultNotReachableControlLayer;
- (SJNotReachableControlLayer *)defaultNotReachableControlLayer {
    if ( !_defaultNotReachableControlLayer ) {
        _defaultNotReachableControlLayer = [[SJNotReachableControlLayer alloc] initWithFrame:self.view.bounds];
        _defaultNotReachableControlLayer.delegate = self;
    }
    return _defaultNotReachableControlLayer;
}

@synthesize defaultSwitchVideoDefinitionView = _defaultSwitchVideoDefinitionView;
- (MRKSwitchVideoDefinitionView *)defaultSwitchVideoDefinitionView {
    if ( _defaultSwitchVideoDefinitionView == nil ) {
        _defaultSwitchVideoDefinitionView = [MRKSwitchVideoDefinitionView build];
        _defaultSwitchVideoDefinitionView.delegate = self;
    }
    return _defaultSwitchVideoDefinitionView;
}

@synthesize videoPlayDoneLayer = _videoPlayDoneLayer;
- (MRKVideoPlayDoneLayer *)videoPlayDoneLayer {
    if ( _videoPlayDoneLayer == nil ) {
        _videoPlayDoneLayer = [[MRKVideoPlayDoneLayer alloc] initWithFrame:self.view.bounds];
        _videoPlayDoneLayer.delegate = self;
    }
    return _videoPlayDoneLayer;
}

@synthesize deviceListView = _deviceListView;
- (MRKBuildDeviceListView *)deviceListView {
    if(!_deviceListView) {
        _deviceListView = [[MRKBuildDeviceListView alloc] initWithFrame:self.view.bounds];
        @weakify(self);
        _deviceListView.selectDeviceBlock = ^(MRKDeviceModel * _Nonnull model) {
            @strongify(self);
            if (self.deviceConnectModelBlock){
                self.deviceConnectModelBlock(model);
            }
        };
    }
    return _deviceListView;
}




@synthesize sj_switchingInfoObserver = _sj_switchingInfoObserver;
- (SJVideoDefinitionSwitchingInfoObserver *)sj_switchingInfoObserver {
    if ( _sj_switchingInfoObserver == nil ) {
        _sj_switchingInfoObserver = [self.definitionSwitchingInfo getObserver];
        __weak typeof(self) _self = self;
        _sj_switchingInfoObserver.statusDidChangeExeBlock = ^(SJVideoDefinitionSwitchingInfo * _Nonnull info) {
            __strong typeof(_self) self = _self;
            if ( !self ) return ;
            if ( self.isDisabledDefinitionSwitchingPrompt ) return;
            switch ( info.status ) {
                case SJDefinitionSwitchStatusUnknown:
                    break;
                case SJDefinitionSwitchStatusSwitching: {
                    [self.prompt show:[NSAttributedString sj_UIKitText:^(id<SJUIKitTextMakerProtocol>  _Nonnull make) {
                        make.append([NSString stringWithFormat:@"%@【%@】请稍后...", SJVideoPlayerConfigurations.shared.localizedStrings.definitionSwitchingPrompt, info.switchingAsset.definition_fullName]);
                        make.textColor(UIColor.whiteColor);
                        make.font([UIFont systemFontOfSize:14]);
                    }] duration:-1];
                }
                    break;
                case SJDefinitionSwitchStatusFinished: {
                    [self.prompt hidden];
                    [self.prompt show:[NSAttributedString sj_UIKitText:^(id<SJUIKitTextMakerProtocol>  _Nonnull make) {
                        if (UserInfo.isMember) {
                            NSString *vipTip = nil;
                            UIImage *icon = nil;
                            int vipType = UserInfo.vipType;
                            switch (vipType) {
                                case 10: {
                                    vipTip = @"尊贵的趣练VIP，已为您切换至";
                                    icon = [UIImage imageNamed:@"record_vip"];
                                }  break;
                                case 30:{
                                    vipTip = @"尊贵的绝影会员，已为您切换至";
                                    icon = [UIImage imageNamed:@"record_xenjoy"];
                                }  break;
                                default:
                                    vipTip = SJVideoPlayerConfigurations.shared.localizedStrings.definitionSwitchSuccessfullyPrompt;
                                    break;
                            }
                            
                            if (icon != nil) {
                                make.appendImage(^(id<SJUTImageAttachment>  _Nonnull make) {
                                    make.image = icon;
                                    make.bounds = CGRectMake(0, -5, 23, 20);
                                });
                            }
                            make.append([NSString stringWithFormat:@"%@【%@】画质", vipTip, info.currentPlayingAsset.definition_fullName]);
                            make.textColor(UIColorHex(#F4C8A1));
                        }else{
                            make.append([NSString stringWithFormat:@"%@", SJVideoPlayerConfigurations.shared.localizedStrings.definitionSwitchSuccessfullyPrompt]);
                            make.textColor(UIColor.whiteColor);
                        }
                        make.font([UIFont systemFontOfSize:14]);
                        make.alignment(NSTextAlignmentCenter);
                    }] duration:3];
                }
                    break;
                case SJDefinitionSwitchStatusFailed: {
                    [self.prompt hidden];
                    [self.prompt show:[NSAttributedString sj_UIKitText:^(id<SJUIKitTextMakerProtocol>  _Nonnull make) {
                        make.append(SJVideoPlayerConfigurations.shared.localizedStrings.definitionSwitchFailedPrompt);
                        make.textColor(UIColor.whiteColor);
                        make.font([UIFont systemFontOfSize:14]);
                    }]];
                }
                    break;
            }
            [self _updateContentForDefinitionItemIfNeeded];
        };
    }
    return _sj_switchingInfoObserver;
}


#pragma mark -  _observeNotifies

// - initialize -
- (void)_observeNotifies {
    [NSNotificationCenter.defaultCenter addObserver:self
                                           selector:@selector(_switchControlLayerIfNeeded)
                                               name:SJVideoPlayerPlaybackTimeControlStatusDidChangeNotification
                                             object:self];
    
    [NSNotificationCenter.defaultCenter addObserver:self
                                           selector:@selector(_resumeOrStopTimeoutTimer)
                                               name:SJVideoPlayerPlaybackTimeControlStatusDidChangeNotification
                                             object:self];
    
    [NSNotificationCenter.defaultCenter addObserver:self
                                           selector:@selector(_switchControlLayerIfNeeded)
                                               name:SJVideoPlayerAssetStatusDidChangeNotification
                                             object:self];
    
    [NSNotificationCenter.defaultCenter addObserver:self
                                           selector:@selector(_switchControlLayerIfNeeded)
                                               name:SJVideoPlayerPlaybackDidFinishNotification
                                             object:self];
    
    [NSNotificationCenter.defaultCenter addObserver:self
                                           selector:@selector(_configurationsDidUpdate)
                                               name:SJVideoPlayerConfigurationsDidUpdateNotification
                                             object:nil];
}

- (void)_initializeSwitcher {
    _switcher = [[SJControlLayerSwitcher alloc] initWithPlayer:self];
    __weak typeof(self) _self = self;
    _switcher.resolveControlLayer = ^id<SJControlLayer> _Nullable(SJControlLayerIdentifier identifier) {
        __strong typeof(_self) self = _self;
        if ( !self ) return nil;
        if ( identifier == SJControlLayer_Edge )
            return self.defaultEdgeControlLayer;
        else if ( identifier == SJControlLayer_NotReachableAndPlaybackStalled )
            return self.defaultNotReachableControlLayer;
        else if ( identifier == SJControlLayer_LoadFailed )
            return self.defaultLoadFailedControlLayer;
        else if ( identifier == SJControlLayer_VideoPlayDoneHolder )
            return self.videoPlayDoneLayer;
        return nil;
    };
}

- (void)_initializeSwitcherObserver {
    _sj_switcherObserver = [_switcher getObserver];
    __weak typeof(self) _self = self;
    _sj_switcherObserver.playerWillBeginSwitchControlLayer = ^(id<SJControlLayerSwitcher>  _Nonnull switcher, id<SJControlLayer>  _Nonnull controlLayer) {
        __strong typeof(_self) self = _self;
        if ( !self ) return;
        if ( [controlLayer respondsToSelector:@selector(setHiddenBackButtonWhenOrientationIsPortrait:)] ) {
            [(MRKUserExperienceControlLayer *)controlLayer setHiddenBackButtonWhenOrientationIsPortrait:self.defaultEdgeControlLayer.isHiddenBackButtonWhenOrientationIsPortrait];
        }
    };
    ///切换层级结束后
    _sj_switcherObserver.playerDidEndSwitchControlLayer = ^(id<SJControlLayerSwitcher>  _Nonnull switcher, id<SJControlLayer>  _Nonnull controlLayer) {
        __strong typeof(_self) self = _self;
        if ( !self ) return;
        
        ///切换到MRKVideoPlayDoneLayer, 视频中止stop
        if ([controlLayer isKindOfClass:[MRKVideoPlayDoneLayer class]]) {
            self.showPalyDoneLayer = YES;
        }
    };
}

- (void)_initializeSettingsObserver {
    [NSNotificationCenter.defaultCenter addObserver:self
                                           selector:@selector(_configurationsDidUpdate)
                                               name:SJVideoPlayerConfigurationsDidUpdateNotification
                                             object:nil];
}


- (void)_configurationsDidUpdate {
    if ( self.presentView.placeholderImageView.image == nil )
        self.presentView.placeholderImageView.image = SJVideoPlayerConfigurations.shared.resources.placeholder;
    
}

// 播放器当前是否只支持一个方向
- (BOOL)_whetherToSupportOnlyOneOrientation {
    if ( self.rotationManager.autorotationSupportedOrientations == SJOrientationMaskPortrait ) return YES;
    if ( self.rotationManager.autorotationSupportedOrientations == SJOrientationMaskLandscapeLeft ) return YES;
    if ( self.rotationManager.autorotationSupportedOrientations == SJOrientationMaskLandscapeRight ) return YES;
    return NO;
}

- (void)_resumeOrStopTimeoutTimer {
    
    if ( self.isBuffering || self.isEvaluating ) {
        if ( SJReachability.shared.networkStatus == SJNetworkStatus_NotReachable && _sj_timeoutTimer == nil ) {
            __weak typeof(self) _self = self;
            _sj_timeoutTimer = [NSTimer sj_timerWithTimeInterval:3 repeats:YES usingBlock:^(NSTimer * _Nonnull timer) {
                [timer invalidate];
                __strong typeof(_self) self = _self;
                if ( !self ) return;
#ifdef DEBUG
                NSLog(@"%d \t %s \t 网络超时, 切换到无网控制层!", (int)__LINE__, __func__);
#endif
                self.sj_isTimeout = YES;
                [self _switchControlLayerIfNeeded];
            }];
            [_sj_timeoutTimer sj_fire];
            [NSRunLoop.mainRunLoop addTimer:_sj_timeoutTimer forMode:NSRunLoopCommonModes];
        }
    }
    else if ( _sj_timeoutTimer != nil ) {
        [_sj_timeoutTimer invalidate];
        _sj_timeoutTimer = nil;
        self.sj_isTimeout = NO;
    }
    
}

- (void)_switchControlLayerIfNeeded {
    
    if ( self.assetStatus == SJAssetStatusFailed ) {
        // 资源出错时
        // - 发生错误时, 切换到加载失败控制层
        //
        [self.switcher switchControlLayerForIdentifier:SJControlLayer_LoadFailed];
    } else if ( self.sj_isTimeout ) {
        // 当处于缓冲状态时
        // - 当前如果没有网络, 则切换到无网空制层
        //
        [self.switcher switchControlLayerForIdentifier:SJControlLayer_NotReachableAndPlaybackStalled];
    } else {
        
        if ( self.switcher.currentIdentifier == SJControlLayer_LoadFailed ||
            self.switcher.currentIdentifier == SJControlLayer_NotReachableAndPlaybackStalled ) {
            
            [self.switcher switchControlLayerForIdentifier:SJControlLayer_Edge];
        }
    }
}

- (void)_initializeAppearManagerObserver {
    _sj_appearManagerObserver = [self.controlLayerAppearManager getObserver];
    __weak typeof(self) _self = self;
    _sj_appearManagerObserver.onAppearChanged = ^(id<SJControlLayerAppearManager>  _Nonnull mgr) {
        __strong typeof(_self) self = _self;
        if ( !self ) return ;
        // refresh edge button items
        if ( self.switcher.currentIdentifier == SJControlLayer_Edge ) {
            [self _updateContentForDefinitionItemIfNeeded];
        }
    };
}

- (void)_initializeReachabilityObserver {
    _sj_reachabilityObserver = [self.reachability getObserver];
    __weak typeof(self) _self = self;
    _sj_reachabilityObserver.networkStatusDidChangeExeBlock = ^(id<SJReachability>  _Nonnull r) {
        __strong typeof(_self) self = _self;
        if ( !self ) return;
        if ( r.networkStatus == SJNetworkStatus_NotReachable ) {
            [self _resumeOrStopTimeoutTimer];
        }
        else if ( self.switcher.currentIdentifier == SJControlLayer_NotReachableAndPlaybackStalled ) {
#ifdef DEBUG
            NSLog(@"%d \t %s \t 网络恢复, 将刷新资源, 使播放器恢复播放!", (int)__LINE__, __func__);
#endif
            [self refresh];
        }
    };
}

- (void)_updateContentForDefinitionItemIfNeeded {
    if ( self.definitionURLAssets.count != 0 ) {
        SJVideoPlayerURLAsset *asset = self.URLAsset;
        
        UIView *view = self.definitionItem.customView;
        if (view && [view isKindOfClass:[MRKVideoDefinitionView class]]) {
            MRKVideoDefinitionView *vi  = (MRKVideoDefinitionView *)view;
            vi.definition = asset.definition_fullName;
        }
        [self.defaultEdgeControlLayer.topAdapter reload];
    }
}

- (void)switchControlLayerWithIdentifier:(SJControlLayerIdentifier)identifier {
    ///切换到DoneLayer时判断 defaultSwitchVideoDefinitionView 是否在显示
    if ( identifier == SJControlLayer_VideoPlayDoneHolder ){
        if (self.defaultSwitchVideoDefinitionView.isDisplayOnScreen){
            [self.defaultSwitchVideoDefinitionView hide];
        }
    }
    
    [self.switcher switchControlLayerForIdentifier:identifier];
}


@end


@implementation MRKUserExperienceVideoPlayer (CommonSettings)
+ (void (^)(void (^ _Nonnull)(SJVideoPlayerConfigurations * _Nonnull)))update {
    return SJVideoPlayerConfigurations.update;
}

+ (void (^)(NSBundle * _Nonnull))setLocalizedStrings {
    return ^(NSBundle *bundle) {
        SJVideoPlayerConfigurations.update(^(SJVideoPlayerConfigurations * _Nonnull configs) {
            [configs.localizedStrings setFromBundle:bundle];
        });
    };
}

+ (void (^)(void (^ _Nonnull)(id<SJVideoPlayerLocalizedStrings> _Nonnull)))updateLocalizedStrings {
    return ^(void(^block)(id<SJVideoPlayerLocalizedStrings> strings)) {
        SJVideoPlayerConfigurations.update(^(SJVideoPlayerConfigurations * _Nonnull configs) {
            block(configs.localizedStrings);
        });
    };
}

+ (void (^)(void (^ _Nonnull)(id<SJVideoPlayerControlLayerResources> _Nonnull)))updateResources {
    return ^(void(^block)(id<SJVideoPlayerControlLayerResources> resources)) {
        SJVideoPlayerConfigurations.update(^(SJVideoPlayerConfigurations * _Nonnull configs) {
            block(configs.resources);
        });
    };
}
@end



#pragma mark -
@implementation MRKUserExperienceVideoPlayer (SJExtendedSwitchVideoDefinitionControlLayer)

- (void)setDefinitionURLAssets:(nullable NSArray<SJVideoPlayerURLAsset *> *)definitionURLAssets {
    objc_setAssociatedObject(self, @selector(definitionURLAssets), definitionURLAssets, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    
    SJEdgeControlButtonItemAdapter *adapter = self.defaultEdgeControlLayer.topAdapter;
//    if ( definitionURLAssets.count > 1 ) {
//        if ( self.definitionItem == nil ) {
//            MRKVideoDefinitionView *view = [[MRKVideoDefinitionView alloc] init];
//            view.frame = CGRectMake(0, 0, 55, 30);
//            self.definitionItem = [[SJEdgeControlButtonItem alloc] initWithCustomView:view tag:SJEdgeControlLayerTopItem_Definition];
//            [self.definitionItem addAction:[SJEdgeControlButtonItemAction actionWithTarget:self action:@selector(_definitionItemWasTapped:)]];
//            [adapter insertItem:self.definitionItem rearItem:SJEdgeControlLayerTopItem_Device];
//        }
//
//        [self _updateContentForDefinitionItemIfNeeded];
//
//    } else {
        self.definitionItem = nil;
        [adapter removeItemForTag:SJEdgeControlLayerTopItem_Definition];
        [self.defaultEdgeControlLayer.topAdapter reload];
//    }
}

- (nullable NSArray<SJVideoPlayerURLAsset *> *)definitionURLAssets {
    return objc_getAssociatedObject(self, _cmd);
}

- (void)setDisabledDefinitionSwitchingPrompt:(BOOL)disabledDefinitionSwitchingPrompt {
    objc_setAssociatedObject(self, @selector(isDisabledDefinitionSwitchingPrompt), @(disabledDefinitionSwitchingPrompt), OBJC_ASSOCIATION_RETAIN_NONATOMIC);
}

- (BOOL)isDisabledDefinitionSwitchingPrompt {
    return [objc_getAssociatedObject(self, _cmd) boolValue];
}

@end




@implementation MRKUserExperienceVideoPlayer (RotationOrFitOnScreen)
- (void)setAutomaticallyPerformRotationOrFitOnScreen:(BOOL)automaticallyPerformRotationOrFitOnScreen {
    self.defaultEdgeControlLayer.automaticallyPerformRotationOrFitOnScreen = automaticallyPerformRotationOrFitOnScreen;
}
- (BOOL)automaticallyPerformRotationOrFitOnScreen {
    return self.defaultEdgeControlLayer.automaticallyPerformRotationOrFitOnScreen;
}

- (void)setOnlyUsedFitOnScreen:(BOOL)onlyUsedFitOnScreen {
    self.defaultEdgeControlLayer.onlyUsedFitOnScreen = onlyUsedFitOnScreen;
}
- (BOOL)onlyUsedFitOnScreen {
    return self.defaultEdgeControlLayer.onlyUsedFitOnScreen;
}

- (void)setUsesFitOnScreenFirst:(BOOL)usesFitOnScreenFirst {
    self.defaultEdgeControlLayer.usesFitOnScreenFirst = usesFitOnScreenFirst;
}
- (BOOL)usesFitOnScreenFirst {
    return self.defaultEdgeControlLayer.usesFitOnScreenFirst;
}
@end

NS_ASSUME_NONNULL_END
