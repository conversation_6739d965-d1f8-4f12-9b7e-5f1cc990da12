//
//  TrainDataItemView.h
//  Student_IOS
//
//  Created by merit on 2022/4/11.
//

#import <UIKit/UIKit.h>
#import "CourseLinkModel.h"

NS_ASSUME_NONNULL_BEGIN


typedef void(^TextColorHighlightBlock)(UIColor *textColor);

@class TrainDataGaugeModel;
@interface TrainDataItemView : UIView
/**数据*/
@property (nonatomic, strong) UILabel *topLab;
/**描述*/
@property (nonatomic, strong) UILabel *bottomLab;


/**Item是否显示表盘*/
@property (nonatomic, assign) BOOL showPointer;
/**Item是否显示可自动调节*/
@property (nonatomic, assign) BOOL showControl;
/**Item竞赛时长变色*/
@property (nonatomic, assign) BOOL showRice;


/**当前表盘数据*/
@property (nonatomic, strong) TrainDataGaugeModel *dialModel;

/**刷新表盘*/
- (void)reloadExerciseDialView:(CourseLinkModel *)model;

/**刷新数字提示*/
- (void)reloadNumTip:(NSNumber *)num;

@end






@interface TrainDataGaugeView : UIView

@property (nonatomic, copy) TextColorHighlightBlock  textHighlightBlock;

/**当前小节建议小值*/
@property (nonatomic, strong) NSNumber *dialMinValue;

/**当前小节建议大值*/
@property (nonatomic, strong) NSNumber *dialMaxValue;

/**当前表盘数据*/
@property (nonatomic, strong) NSNumber *pointerValue;

/**当前表盘数据*/
@property (nonatomic, strong) TrainDataGaugeModel *dialModel;

/**刷新表盘*/
- (void)reloadExerciseDialView;
@end






@interface TrainDataGaugeModel : NSObject
/**设备大类id*/
@property (nonatomic, assign) EquipmentType equipmentType;

/**当前课程教案建议值最小值*/
@property (nonatomic, strong) NSNumber *planMinDataValue;

/**当前课程教案建议值最大值*/
@property (nonatomic, strong) NSNumber *planMaxDataValue;

/**当前设备踏频/桨频最小值*/
@property (nonatomic, strong) NSNumber *minDataValue;

/** [表盘只支持 动感单车 /椭圆机 /划船机]
 设备支持的踏频/桨频 (min - max)
 跑步机：    0-16；[不使用]
 动感单车：0-160；
 椭圆机：    0-120；
 划船机：    0-60；
 爬楼机：    0-110；*/
@property (nonatomic, strong) NSNumber *maxDataValue;
@end

NS_ASSUME_NONNULL_END
