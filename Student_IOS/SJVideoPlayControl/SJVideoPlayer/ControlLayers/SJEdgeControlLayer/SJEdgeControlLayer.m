//
//  SJEdgeControlLayer.m
//  SJVideoPlayer
//
//  Created by 畅三江 on 2018/10/24.
//  Copyright © 2018 畅三江. All rights reserved.
//

#import "SJEdgeControlLayer.h"
#import "NSAttributedString+SJMake.h"
#import <objc/message.h>
#import "Masonry.h"
#import "SJBaseVideoPlayer.h"
#import "SJVideoPlayer.h"

#import "SJTimerControl.h"
#import "SJVideoPlayerURLAsset+SJControlAdd.h"
#import "UIView+SJAnimationAdded.h"
#import "SJVideoPlayerConfigurations.h"
#import "SJProgressSlider.h"
#import "SJLoadingView.h"
#import "SJDraggingObservation.h"
#import "SJScrollingTextMarqueeView.h"
#import "SJFullscreenModeStatusBar.h"
#import "SJSpeedupPlaybackPopupView.h"

#import "TrainDataItemView.h"
#import "MRKSuperBurnView.h"
#import "MRKVideoDefinitionView.h"
#import "MRKDMListView.h"
#import "MRKVideoStepView.h"
#import "MRKSportListView.h"
#import "MRKRenderModel.h"
#import "MRKFatBurningModel.h"
#import "MRKHeartItemView.h"

#import "MRKSuperBurnAboutView.h"
#import "MRKVideoBTConnectView.h"
#import "MRKLivingAnimationManager.h"
#import "MRKVideoGuideView.h"
#import "SJPopAdviseDefinitionView.h"

#import "MRKSignActivity.h"
#import "MRKTraceManager.h"
#import "MRKCoachSellView.h"
#import "SpeechSynthesizerManager.h"


#define MRKDanmuViewWidth     185

/** 氛围类型 */
typedef NS_ENUM(NSInteger, SJEdgeLayerDisplayType) {
    SJEdgeLayerDisplayTypeNone,
    SJEdgeLayerDisplayTypeAmbient,          ///氛围
    SJEdgeLayerDisplayTypeImmerse,          ///沉浸
};

@interface SJEdgeControlLayer ()<MRKLivingAnimationManagerDelegate , MRKVideoGuideViewDelegate> {
    CGSize _previousSize;
}
@property (nonatomic, weak, nullable) SJBaseVideoPlayer *videoPlayer;
@property (nonatomic, weak, nullable) SJVideoPlayer *currentVideoPlayer;

@property (nonatomic, strong) MRKLivingAnimationManager *livingAnimationManager;
@property (nonatomic, strong, readonly) SJProgressSlider *bottomProgressIndicator;
@property (nonatomic, assign) SJEdgeLayerDisplayType displayType;

/// 固定左上角的返回按钮. 设置`fixesBackItem`后显示
@property (nonatomic, strong, readonly) UIButton *fixedBackButton;
@property (nonatomic, strong, readonly) SJEdgeControlButtonItem *backItem;
@property (nonatomic, strong, nullable) id<SJReachabilityObserver> reachabilityObserver;
@property (nonatomic, strong, readonly) SJTimerControl *dateTimerControl API_AVAILABLE(ios(11.0)); // refresh date for custom status bar

@property (nonatomic, strong) MRKSuperBurnView *burnView;           ///超燃脂率
@property (nonatomic, strong) MRKDMListView *dmListView;            ///弹幕列表 view
@property (nonatomic, strong) MRKVideoStepView *stepView;           ///环节进度
@property (nonatomic, strong) MRKSportListView *rollView;           ///排行榜
@property (nonatomic, strong) MRKHeartItemView *heartItemView;      ///心率模块
@property (nonatomic, strong) MRKVideoGuideView *guideView;         ///引导view
@property (nonatomic, strong) MRKVideoBTConnectView *BTConnectView; ///蓝牙断连弹窗
@property (nonatomic, strong) MRKCoachSellView *coachSellView;      ///教练带货商品窗
///
///
@property (nonatomic, assign) BOOL hasToastAlert;/**有无弹过指令窗**/
@property (nonatomic, assign) BOOL hasToast10SecondsAlert;/**录播课程结束前10S提示**/
@property (nonatomic, strong) CourseLinkModel *currentlinkModel;/**播放器当前时间对应的小节model**/
@property (nonatomic, assign) BOOL showAdviseDefinition;/**推荐下一个清晰度阀值**/

@property (nonatomic, strong) TrainingShowData *tyModel;

@end

@implementation SJEdgeControlLayer
@synthesize restarted = _restarted;


- (MRKSuperBurnView *)burnView{
    if (!_burnView) {
        _burnView = [[MRKSuperBurnView alloc] init];
        _burnView.hidden = YES;
        @weakify(self);
        _burnView.superBurnAboutBlock = ^{
            @strongify(self);
            /**超燃脂率说明*/
            MRKSuperBurnAboutView *view = [MRKSuperBurnAboutView build];
            [view showIn:self];
        };
    }
    return _burnView;
}

- (MRKSportListView *)rollView{
    if (!_rollView) {
        _rollView = [[MRKSportListView alloc] init];
        @weakify(self);
        _rollView.mrkSportReloadBlock = ^(BOOL isSuperBurnRank) {
            ///切换排行榜请求数据的requestType
            self_weak_.videoPlayer.rankDataManager.requestType = isSuperBurnRank ? 1 : 2;
        };
        _rollView.mrkSportListChangeBlock = ^{
            ///刷新氛围显示
            [self_weak_ _updateModelForTopModelItemIfNeeded];
        };
    }
    return _rollView;
}

- (MRKVideoStepView *)stepView {
    if (!_stepView) {
        _stepView = [[MRKVideoStepView alloc] init];
        @weakify(self);
        _stepView.seekBlock = ^(NSTimeInterval seekTime) {
            @strongify(self);
            [self.videoPlayer seekToTime:seekTime/1000 completionHandler:nil];
        };
    }
    return _stepView;
}

- (MRKDMListView *)dmListView {
    if (!_dmListView) {
        _dmListView = [[MRKDMListView alloc] init];
        @weakify(self);
        _dmListView.sendMsgBlock = ^(NSInteger type) {
            @strongify(self);
            if (type == 3) {
                ///点击上弹幕按钮 直接发送当前占位字符
                NSString *holderStr = [self.dmListView getSendViewHolderStr];
                if (self.delegate && [self.delegate respondsToSelector:@selector(keyboardInputWasTappedForControlLayer:sendMsg:)]) {
                    [self.delegate keyboardInputWasTappedForControlLayer:self sendMsg:holderStr];
                }
                return;
            }
            
            if (self.delegate && [self.delegate respondsToSelector:@selector(keyboardInputWasTappedForControlLayer:andType:)]) {
                [self.delegate keyboardInputWasTappedForControlLayer:self andType:type];
            }
        };
    }
    return _dmListView;
}

- (MRKHeartItemView *)heartItemView {
    if (!_heartItemView) {
        _heartItemView = [[MRKHeartItemView alloc] init];
    }
    return _heartItemView;
}

- (MRKLivingAnimationManager *)livingAnimationManager{
    if (!_livingAnimationManager) {
        _livingAnimationManager = [[MRKLivingAnimationManager alloc] init];
    }
    return _livingAnimationManager;
}

- (MRKCoachSellView *)coachSellView{
    if (!_coachSellView) {
        _coachSellView = [[MRKCoachSellView alloc] init];
        _coachSellView.hidden = YES;
        _coachSellView.alpha = 0;
        @weakify(self);
        _coachSellView.bugOperationBlock = ^(MRKCourseGoodsModel * _Nullable model) {
            @strongify(self);
            [self->_currentVideoPlayer controlLayerWebProductView:model];
            
            ///log
            ReportMrkLogParms(2, @"教练带货", @"page_play", @"btn_play_comodity", nil, 0, @{@"courseId": self.videoPlayer.courseModel.courseId?:@"", @"course_state": self.videoPlayer.tracePlaybackType});
        };
    }
    return _coachSellView;
}

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if ( !self ) return nil;
    
    self.showAdviseDefinition = YES;
    self.displayType = SJEdgeLayerDisplayTypeAmbient; ///默认氛围模式
    
    self.bottomProgressIndicatorHeight = 1;
    self.automaticallyPerformRotationOrFitOnScreen = YES;
    self.rightAdapterLayoutFromTopDirection = YES;
    self.autoAdjustTopSpacing = YES;
    self.hiddenBottomProgressIndicator = YES;
    
    CGFloat l_padding = kStatusBarHeight;
    self.leftMargin = l_padding;
    self.leftWidth = 160;
    
    CGFloat r_padding = IS_IPHONEX_SURE ? 30 : 10;
    self.rightMargin = 0;
    self.rightWidth = MRKDanmuViewWidth +r_padding;
    
    [self _setupView];
    
    { ///添加超然脂率
        [self addSubview:self.burnView];
        float padding = IS_IPHONEX_SURE ? -20 : -10;
        [self.burnView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.mas_left).offset(kScreenPadding);
            make.bottom.mas_equalTo(self.mas_bottom).offset(padding);
            make.size.mas_equalTo(CGSizeMake(80, 80));
        }];
        [self bringSubviewToFront:self.burnView];
    }
    
    
    { ///添加带货商品窗
        [self addSubview:self.coachSellView];
        [self.coachSellView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(self.mas_right).offset(-self.rightWidth -4);
            make.bottom.mas_equalTo(self.mas_bottom).offset(-120);
            make.size.mas_equalTo(CGSizeMake(64, 70));
        }];
    }
    
    self.livingAnimationManager.delegate = self;
    return self;
}

- (void)layoutSubviews {
    [super layoutSubviews];
    
    if ( !CGSizeEqualToSize(_previousSize, self.bounds.size) ) {
        if (@available(iOS 11.0, *)) {
            [self _updateAppearStateForCustomStatusBar];
        }
        [self _updateLayoutForBottomProgressIndicator];
    }
    _previousSize = self.bounds.size;
}


- (void)dealloc {
    NSLog(@"😊😊😊-----dealloc-----😊😊😊%@" , NSStringFromClass([self class]));
    [NSNotificationCenter.defaultCenter removeObserver:self];
    [SpeechSynthesizerManager.shared stopSpeaking];
}




#pragma mark - videoTipString

- (void)videoTipString:(NSString *)tipStr {
    if (![tipStr isNotBlank]) return;
    [self.videoPlayer.prompt show:[NSAttributedString sj_UIKitText:^(id<SJUIKitTextMakerProtocol>  _Nonnull make) {
        make.append(tipStr);
        make.font([UIFont systemFontOfSize:14 weight:UIFontWeightMedium]);
        make.textColor([UIColor whiteColor]);
    }] duration:3];
}

#pragma mark - item actions

- (void)_fixedBackButtonWasTapped {
    [self.backItem performActions];
}

/**返回*/
- (void)_backItemWasTapped {
    if ( [self.delegate respondsToSelector:@selector(backItemWasTappedForControlLayer:)] ) {
        [self.delegate backItemWasTappedForControlLayer:self];
    }
    MLog(@"_backItemWasTapped====");
}

/**锁屏点击*/
- (void)_lockItemWasTapped {
    //    self.videoPlayer.lockedScreen = !self.videoPlayer.isLockedScreen;
}

/**播放按钮点击*/
- (void)_playItemWasTapped {
    _videoPlayer.isPaused ? [self.videoPlayer play] : [self.videoPlayer pauseForUser];
}

/**
 播放器全屏按钮点击
 @note 暂时未使用此功能
 */
- (void)_fullItemWasTapped {
    if ( _onlyUsedFitOnScreen ) {
        [_videoPlayer setFitOnScreen:!_videoPlayer.isFitOnScreen];
        return;
    }
    
    if ( _usesFitOnScreenFirst && !_videoPlayer.isFitOnScreen ) {
        [_videoPlayer setFitOnScreen:YES];
        return;
    }
    [_videoPlayer rotate];
}

/**播放器重播*/
- (void)_replayItemWasTapped {
    [_videoPlayer replay];
    
    MLog(@"_replayItemWasTapped ==== ");
}

/**自动调节模式切换*/
- (void)_autoItemWasTapped {
    _videoPlayer.useLessonPlanControl = !_videoPlayer.useLessonPlanControl;

    NSString *tipStr = _videoPlayer.isUseLessonPlanControl ? @"开启AI调节" : @"关闭AI调节";
    [self videoTipString:tipStr];
    
    MLog(@"_autoItemWasTapped ==== %@", tipStr);
    
    ///log
    NSString *control_result = _videoPlayer.isUseLessonPlanControl ? @"open": @"close";
    ReportMrkLogParms(2, @"超燃脂智控", @"page_play", @"btn_play_chaoranzhi_switch", nil, 0, @{@"control_result":control_result, @"courseId": self.videoPlayer.courseModel.courseId?:@"", @"course_state": self.videoPlayer.tracePlaybackType});
}

/**氛围模式切换*/
- (void)_modelItemWasTapped {
    
    NSString *tipStr = @"";
    switch ( self.displayType ) {
        case SJEdgeLayerDisplayTypeAmbient:{///氛围 -> 沉浸
            self.displayType = SJEdgeLayerDisplayTypeImmerse;
            self.videoPlayer.closeBarrageControl = YES;
            tipStr = @"已切换至「沉浸模式」";
            
            ///[隐藏排行榜 和  隐藏 弹幕;]
            [self.rollView shrinkSportListView];
            [self.dmListView isHiddenDanmuView:YES];
            
        } break;
        case SJEdgeLayerDisplayTypeImmerse:{///沉浸 -> 氛围
            self.displayType = SJEdgeLayerDisplayTypeAmbient;
            self.videoPlayer.closeBarrageControl = NO;
            tipStr = @"已切换至「氛围模式」";
            
            ///[显示排行榜 和  显示弹幕;]
            [self.rollView magnifySportListView];
            [self.dmListView isHiddenDanmuView:NO];
            
        } break;
        default:  break;
    }
    
    ///刷新按钮状态
    [self _reloadDisplayAdapterIfNeeded];
    
    [self videoTipString:tipStr];
    
    MLog(@"_modelItemWasTapped ==== %@", tipStr);
    
    ///log
    if ([tipStr isNotBlank]){
        NSString *control_result = [tipStr isEqualToString:@"已切换至「沉浸模式」"] ? @"immerse": @"atmosphere";
        ReportMrkLogParms(2, @"氛围模式切换", @"page_play", @"btn_play_atmosphere_cut", nil, 0, @{
            @"control_result":control_result,
            @"courseId": self.videoPlayer.courseModel.courseId?:@"",
            @"course_state": self.videoPlayer.tracePlaybackType});
    }
}

/**设备连接*/
- (void)_deviceItemWasTapped {
    if ( [self.delegate respondsToSelector:@selector(connectDeviceWasTappedForControlLayer:)] ) {
        [self.delegate connectDeviceWasTappedForControlLayer:self];
    }
    
    MLog(@"_deviceItemWasTapped ==== ");
    
    ///log
    ReportMrkLogParms(2, @"连接设备", @"page_play", @"btn_play_connect_equipment", nil, 0, @{@"courseId": self.videoPlayer.courseModel.courseId?:@"", @"course_state": self.videoPlayer.tracePlaybackType});
}

/**弹幕按钮点击*/
- (void)_danmuItemWasTapped {
    self.videoPlayer.closeBarrageControl = !self.videoPlayer.closeBarrageControl;
    
    /// 判断弹幕功能是否开启
    [self.dmListView isHiddenDanmuView:!self.dmListView.hidden];
    
    /// 刷新氛围显示
    [self _updateModelForTopModelItemIfNeeded];
    
    NSString *tipStr = _videoPlayer.closeBarrageControl ? @"已关闭弹幕" : @"已开启弹幕";
    [self videoTipString:tipStr];
    
    MLog(@"_danmuItemWasTapped ==== %@", tipStr);
    
    ///log
    NSString *control_result = self.videoPlayer.closeBarrageControl ? @"close": @"open";
    ReportMrkLogParms(2, @"弹幕开关", @"page_play", @"btn_play_danmu_switch", nil, 0, @{@"control_result":control_result, @"courseId": self.videoPlayer.courseModel.courseId?:@"", @"course_state": self.videoPlayer.tracePlaybackType});
}

/**TV投屏按钮点击*/
- (void)_tvItemWasTapped {
    if ( [self.delegate respondsToSelector:@selector(linkTVItemWasTappedForControlLayer:)] ) {
        [self.delegate linkTVItemWasTappedForControlLayer:self];
    }
    
    MLog(@"_tvItemWasTapped ==== ");
    
    ///log
    ReportMrkLogParms(2, @"投屏", @"page_play", @"btn_play_tv", nil, 0, @{@"courseId": self.videoPlayer.courseModel.courseId?:@"", @"course_state": self.videoPlayer.tracePlaybackType});
}




- (BOOL)useAudioPlayerMuted{
    return _videoPlayer.playbackType == SJPlaybackTypeREAL && [_videoPlayer.courseModel.musicUrl isNotBlank];
}

/**静音按钮点击*/
- (void)_mutedItemWasTapped {
#warning 实景默认静音，开启音乐播放器，逻辑注意！！！
    ///如果实景视频配置了播放音乐的url，要通过MRKAliAudioPlayer的isMuted判断
    if (self.useAudioPlayerMuted) {
        ///
        if ( [self.delegate respondsToSelector:@selector(mutedItemWasTappedForControlLayer:)] ) {
            [self.delegate mutedItemWasTappedForControlLayer:self];
        }
        
        /// 刷新静音显示
        [self _updateMutedItemIfNeeded];
        
        NSString *tipStr = self.audioPlayerMuted ? @"声音已关闭":@"声音已开启";
        [self videoTipString:tipStr];
    }else{
        self.videoPlayer.muted = !self.videoPlayer.muted;
        
        /// 刷新静音显示
        [self _updateMutedItemIfNeeded];
        
        NSString *tipStr = _videoPlayer.muted ? @"声音已关闭":@"声音已开启";
        [self videoTipString:tipStr];
    }
    
    MLog(@"_mutedItemWasTapped ==== ");
}




/**分享按钮点击*/
- (void)_shareItemWasTapped {
    
    //    MRKRenderModel *renderModel = [[MRKRenderModel alloc] init];
    //    renderModel.isLivingMsg = YES;
    //    renderModel.nickname = @"";
    //    renderModel.isEnjoyVip = YES;
    //    [[NSNotificationCenter defaultCenter] postNotificationName:mLivingAnimationNotification object:renderModel];
    //    return;
    //
    ////
    if ( [self.delegate respondsToSelector:@selector(shareItemWasTappedForControlLayer:)] ) {
        [self.delegate shareItemWasTappedForControlLayer:self];
    }
    
    MLog(@"_shareItemWasTapped ==== ");
}

/**铺满屏幕按钮点击*/
- (void)_sizeAdaptItemItemWasTapped {
    if (_videoPlayer.videoGravity == AVLayerVideoGravityResizeAspectFill) {
        _videoPlayer.videoGravity = AVLayerVideoGravityResizeAspect;
    }else {
        _videoPlayer.videoGravity = AVLayerVideoGravityResizeAspectFill;
    }
    
    ///
    [self _reloadSizeAdaptItemIfNeeded];
    
    NSString *tipStr = _videoPlayer.videoGravity == AVLayerVideoGravityResizeAspectFill ? @"已开启视频全屏" : @"已关闭视频全屏";
    [self videoTipString:tipStr];
    
    MLog(@"_sizeAdaptItemItemWasTapped ==== %@", tipStr);
    ///log
    NSString *control_result = _videoPlayer.videoGravity == AVLayerVideoGravityResizeAspectFill  ? @"open": @"close";
    ReportMrkLogParms(2, @"铺满全屏", @"page_play", @"btn_play_full_screen", nil, 0, @{@"control_result":control_result, @"courseId": self.videoPlayer.courseModel.courseId?:@"", @"course_state": self.videoPlayer.tracePlaybackType});
}

///设备映射调节
- (void)_deviceMappingRegulate:(BOOL)isOn {
    @weakify(self);
    [self.videoPlayer.videoPlayData updateMappingResistance:isOn completion:^(BOOL completion) {
        @strongify(self);
        if (completion) {
            [self videoTipString:isOn ?@"已开启匹配调阻":@"已关闭匹配调阻"];
            ///如果自动调整设备关闭, 要打开
            if (isOn && !self.videoPlayer.isUseLessonPlanControl) {
                self.videoPlayer.useLessonPlanControl = YES;
            }
            
            ///log
            NSString *control_result = isOn ? @"open": @"close";
            ReportMrkLogParms(2, @"匹配调阻", @"page_play", @"btn_play_matched_resistance", nil, 0, @{@"control_result":control_result, @"courseId": self.videoPlayer.courseModel.courseId?:@"", @"course_state": self.videoPlayer.tracePlaybackType});
            return;
        }
        
        [self videoTipString:@"调整失败, 请重试"];
    }];
}

/**更多按钮点击*/
- (void)_moreItemWasTapped {
    [_currentVideoPlayer controlLayerMoreOperationView];
    
    MLog(@"_moreItemWasTapped ==== ");
    ///log
    ReportMrkLogParms(2, @"视频更多", @"page_play", @"btn_play_more", nil, 0, @{@"courseId": self.videoPlayer.courseModel.courseId?:@"", @"course_state": self.videoPlayer.tracePlaybackType});
}

/**提示推荐下一个清晰度**/
- (void)_playAdviseDefinition {
    SJVideoPlayerURLAsset *asset = self.videoPlayer.URLAsset;
    NSArray<SJVideoPlayerURLAsset *> *definitionURLAssets = self.currentVideoPlayer.definitionURLAssets;
    
    __block NSInteger index = NSNotFound;
    [definitionURLAssets enumerateObjectsUsingBlock:^(SJVideoPlayerURLAsset * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([asset.definition_fullName isEqualToString:obj.definition_fullName]){
            index = idx;
            *stop = YES;
        }
    }];
    
    if (index == NSNotFound) { return; }
    if (index == definitionURLAssets.count -1) { return; }
    
    SJVideoPlayerURLAsset *adviseAsset = [definitionURLAssets objectAtIndex:index+1];
    
    SJPopAdviseDefinitionView *view = SJPopAdviseDefinitionView.new;
    view.definition = adviseAsset.definition_fullName;
    @weakify(self);
    view.switchButtonWasTappedExeBlock = ^(SJPopAdviseDefinitionView * _Nonnull view, NSString * _Nonnull definition) {
        @strongify(self);
        [self.currentVideoPlayer controlLayerDidSelectAsset:adviseAsset];
        
        ///移除当前提示
        [self.videoPlayer.promptPopupController remove:view];
        
        ///重置可提示阀值
        self.showAdviseDefinition = YES;
    };
    [self.videoPlayer.promptPopupController showCustomView:view duration:10];
}

/**判断时候有下一个清晰度的数据**/
- (BOOL)_checkPlayAdviseDefinition {
    SJVideoPlayerURLAsset *asset = self.videoPlayer.URLAsset;
    NSArray<SJVideoPlayerURLAsset *> *definitionURLAssets = self.currentVideoPlayer.definitionURLAssets;
    
    __block NSInteger index = NSNotFound;
    [definitionURLAssets enumerateObjectsUsingBlock:^(SJVideoPlayerURLAsset * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([asset.definition_fullName isEqualToString:obj.definition_fullName]){
            index = idx;
            *stop = YES;
        }
    }];
    if (index == NSNotFound) { return NO; }
    if (index == definitionURLAssets.count -1) { return NO; }
    return YES;
}











#pragma mark - restartControlLayer/exitControlLayer

///
/// 切换器(player.switcher)重启该控制层
///
- (void)restartControlLayer {
    _restarted = YES;
    sj_view_makeAppear(self.controlView, YES);
    
    [self _showOrHiddenLoadingView];
    [self _updateAppearStateForContainerViews];
    [self _reloadAdaptersIfNeeded];
}

///
/// 控制层退场
///
- (void)exitControlLayer {
    _restarted = NO;
    sj_view_makeDisappear(self.controlView, YES, ^{
        if ( !self->_restarted ) [self.controlView removeFromSuperview];
    });
    
    sj_view_makeDisappear(_topContainerView, YES);
    sj_view_makeDisappear(_leftContainerView, YES);
    sj_view_makeDisappear(_bottomContainerView, YES);
    sj_view_makeDisappear(_rightContainerView, YES);
    sj_view_makeDisappear(_centerContainerView, YES);
    
    sj_view_makeDisappear(_topDataView, YES);
    sj_view_makeDisappear(_bottomDataView, YES);
}


#pragma mark - player delegate methods

- (UIView *)controlView {
    return self;
}

- (void)installedControlViewToVideoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer {
    _videoPlayer = videoPlayer;
    if ([videoPlayer isKindOfClass:[SJVideoPlayer class]]){
        _currentVideoPlayer = videoPlayer;
    }
    
    SJNSLog(@"installedControlViewToVideoPlayer");
    
    sj_view_makeDisappear(_topContainerView, NO);
    sj_view_makeDisappear(_leftContainerView, NO);
    sj_view_makeDisappear(_bottomContainerView, NO);
    sj_view_makeDisappear(_rightContainerView, NO);
    sj_view_makeDisappear(_centerContainerView, NO);
    
    sj_view_makeDisappear(_topDataView, NO);
    sj_view_makeDisappear(_bottomDataView, NO);
    
    [self _reloadSizeForBottomTimeLabel];
    [self _updateContentForBottomCurrentTimeItemIfNeeded];
    [self _updateContentForBottomDurationItemIfNeeded];
    
    @weakify(self);
    _reachabilityObserver = [videoPlayer.reachability getObserver];
    _reachabilityObserver.networkSpeedDidChangeExeBlock = ^(id<SJReachability> r) {
        @strongify(self);
        if ( !self ) return;
        [self _updateNetworkSpeedStrForLoadingView];
    };
}

///
/// 当播放器尝试自动隐藏控制层之前 将会调用这个方法
///
- (BOOL)controlLayerOfVideoPlayerCanAutomaticallyDisappear:(__kindof SJBaseVideoPlayer *)videoPlayer {
    return YES;
}



- (void)controllerDidAppeared:(__kindof SJBaseVideoPlayer *)videoPlayer{
    
    self.heartItemView.heartView.isShowHeartRateAlert = YES;
    self.heartItemView.heartView.layerTipView = self;
    
    ///刷新stepView的frame
    [self _reloadTopDataAdaptersIfNeeded];
    
    ///
    @weakify(self);
    [_videoPlayer.videoPlayData getEquipmentInfo:^{
        @strongify(self);
        if (self->_videoPlayer.videoPlayData.eqModel != nil){
            [self reloadWarnTipLayer];
        }
    }];
}

- (void)controllerDidDisAppeared:(__kindof SJBaseVideoPlayer *)videoPlayer {
    
    
}


///
///视图切换
///
- (void)controlLayerNeedAppear:(__kindof SJBaseVideoPlayer *)videoPlayer {
    if ( videoPlayer.isLockedScreen )
        return;
    
    SJNSLog(@"");
    [self _updateAppearStateForResidentBackButtonIfNeeded];
    [self _updateAppearStateForContainerViews];
    [self _reloadAdaptersIfNeeded];
    [self _updateContentForBottomCurrentTimeItemIfNeeded];
    [self _updateAppearStateForBottomProgressIndicatorIfNeeded];
    if (@available(iOS 11.0, *)) {
        [self _reloadCustomStatusBarIfNeeded];
    }
}

- (void)controlLayerNeedDisappear:(__kindof SJBaseVideoPlayer *)videoPlayer {
    if ( videoPlayer.isLockedScreen )
        return;
    
    SJNSLog(@"");
    [self _updateAppearStateForResidentBackButtonIfNeeded];
    [self _updateAppearStateForContainerViews];
    [self _updateAppearStateForBottomProgressIndicatorIfNeeded];
}


- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer prepareToPlay:(SJVideoPlayerURLAsset *)asset {
    SJNSLog(@"");
    
    [self _reloadSizeForBottomTimeLabel];
    [self _updateContentForBottomDurationItemIfNeeded];
    [self _updateContentForBottomCurrentTimeItemIfNeeded];
    
    [self _updateContentForBottomProgressIndicatorIfNeeded];
    [self _updateAppearStateForResidentBackButtonIfNeeded];
    [self _updateAppearStateForBottomProgressIndicatorIfNeeded];
    
    [self _reloadAdaptersIfNeeded];
    [self _showOrHiddenLoadingView];
}

- (void)videoPlayerPlaybackStatusDidChange:(__kindof SJBaseVideoPlayer *)videoPlayer {
    SJNSLog(@"");
    
    [self _reloadAdaptersIfNeeded];
    [self _showOrHiddenLoadingView];
    [self _updateContentForBottomCurrentTimeItemIfNeeded];
    [self _updateContentForBottomDurationItemIfNeeded];
    [self _updateContentForBottomProgressIndicatorIfNeeded];
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer pictureInPictureStatusDidChange:(SJPictureInPictureStatus)status API_AVAILABLE(ios(14.0)) {
    SJNSLog(@"");
    [self _updateContentForPictureInPictureItem];
    [self.topAdapter reload];
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer currentTimeDidChange:(NSTimeInterval)currentTime {
    /**更新底部当前时长*/
    [self _updateContentForBottomCurrentTimeItemIfNeeded];
    
    /**更新底部进度条*/
    [self _updateContentForBottomProgressIndicatorIfNeeded];
    
    /**修改热力图进度*/
    [self _updateCurrentTimeForStepView];
    
    ///录播最后10s时提示
    [self _updateVODPlayerLast10SecondsTipIfNeeded:currentTime];
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer durationDidChange:(NSTimeInterval)duration {
    [self _reloadSizeForBottomTimeLabel];
    [self _updateContentForBottomDurationItemIfNeeded];
    [self _updateContentForBottomProgressIndicatorIfNeeded];
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer playableDurationDidChange:(NSTimeInterval)duration {
    
}


#pragma mark --- playbackTypeDidChange ---
- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer playbackTypeDidChange:(SJPlaybackType)playbackType {
    SJNSLog(@"");
    
    switch ( playbackType ) {
        case SJPlaybackTypeLIVE: {
            
            [self.bottomAdapter removeAllItems];
            [self.bottomAdapter reload];
            
            ///更新底部隐藏
            [self _updateAppearStateForBottomContainerView];
        }
            break;
        case SJPlaybackTypeUnknown:
        case SJPlaybackTypeVOD:
        case SJPlaybackTypeFILE: {
            
        }
            break;
        case SJPlaybackTypeREAL:{
            
            SJEdgeControlButtonItem *modelItem = [_topAdapter itemForTag:SJEdgeControlLayerTopItem_Model];
            if (modelItem != nil){
                modelItem.hidden = YES;
            }
            
            SJEdgeControlButtonItem *moreItem = [_topAdapter itemForTag:SJEdgeControlLayerTopItem_More];
            if (moreItem != nil){
                moreItem.hidden = YES;
            }
            
            [self.topAdapter reload];
            
        }
            break;
    }
    
    [self _showOrRemoveBottomProgressIndicator];
}




- (BOOL)canTriggerRotationOfVideoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer {
    SJNSLog(@"");
    if ( _onlyUsedFitOnScreen )
        return NO;
    if ( _usesFitOnScreenFirst )
        return videoPlayer.isFitOnScreen;
    
    return YES;
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer willRotateView:(BOOL)isFull {
    SJNSLog(@"");
    [self _updateAppearStateForResidentBackButtonIfNeeded];
    [self _updateAppearStateForContainerViews];
    [self _reloadAdaptersIfNeeded];
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer willFitOnScreen:(BOOL)isFitOnScreen {
    SJNSLog(@"");
    [self _updateAppearStateForResidentBackButtonIfNeeded];
    [self _updateAppearStateForContainerViews];
    [self _reloadAdaptersIfNeeded];
}

/// 是否可以触发播放器的手势
- (BOOL)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer gestureRecognizerShouldTrigger:(SJPlayerGestureType)type location:(CGPoint)location {
    SJEdgeControlButtonItemAdapter *adapter = nil;
    BOOL(^_locationInTheView)(UIView *) = ^BOOL(UIView *container) {
        return CGRectContainsPoint(container.frame, location) && !sj_view_isDisappeared(container);
    };
    
    if ( _locationInTheView(_topContainerView) ) {
        adapter = _topAdapter;
    }
    else if ( _locationInTheView(_topDataView) ) {
        adapter = nil;
    }
    else if ( _locationInTheView(_bottomContainerView) ) {
        adapter = _bottomAdapter;
    }
    else if ( _locationInTheView(_leftContainerView) ) {
        adapter = _leftAdapter;
    }
    else if ( _locationInTheView(_rightContainerView) ) {
        adapter = _rightAdapter;
    }
    else if ( _locationInTheView(_centerContainerView) ) {
        adapter = _centerAdapter;
    }
    if ( !adapter ) return YES;
    
    CGPoint point = [self.controlView convertPoint:location toView:adapter.view];
    if ( !CGRectContainsPoint(adapter.view.frame, point) ) return YES;
    
    SJEdgeControlButtonItem *_Nullable item = [adapter itemAtPoint:point];
    return item != nil ? (item.actions.count == 0)  : YES;
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer panGestureTriggeredInTheHorizontalDirection:(SJPanGestureRecognizerState)state progressTime:(NSTimeInterval)progressTime {
    
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer longPressGestureStateDidChange:(SJLongPressGestureRecognizerState)state {
    
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer presentationSizeDidChange:(CGSize)size {
    if ( _automaticallyPerformRotationOrFitOnScreen && !videoPlayer.isFullscreen && !videoPlayer.isFitOnScreen ) {
        _onlyUsedFitOnScreen = size.width < size.height;
    }
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer SEIData:(id)model {
    [self sendInstructionsWithData:model];
}




#pragma mark ---- SJAppActivityBluetoothControlDelegate ----

- (void)videoPlayerBluetooth:(__kindof SJBaseVideoPlayer *)videoPlayer andTymodel:(id)model{
    ///设备蓝牙数据处理
    [self dataAdapterUpdateWithModel:model];
    
    if(self.delegate && [self.delegate respondsToSelector:@selector(updateBlueData:)]) {
        [self.delegate updateBlueData:model];
    }
}

- (void)videoPlayerBluetooth:(__kindof SJBaseVideoPlayer *)videoPlayer andRdmodel:(id)model{
    ///心率蓝牙数据处理
    [self.heartItemView updateheartWithModel:model];
}

- (void)videoPlayerBluetooth:(__kindof SJBaseVideoPlayer *)videoPlayer connectDevice:(DEVICE_CONNECT_STATUS)status{
    
    ///刷新设备状态
    [self _updateConnectForTopDeviceItemIfNeeded];
    
    ///刷新连接状态
    [self _reloadDataAdapterIfNeeded];
    
    ///隐藏连接弹窗
    if (status == DeviceConnected || status == DeviceAutoConnecting){
        if (self.BTConnectView) {
            [self.BTConnectView hide];
            self.BTConnectView = nil;
        }
    }
    
    ///隐藏阻力回显
    if (status == DeviceConnected){
        [self _reloadItemsBottomDataAdapter];
    }
    
    MRKVideoPrepareDataInfo *info = videoPlayer.videoPlayData;
    {
        ///重置排行榜请求类型
        if (status == DeviceConnected && info.isMeritControl) {
            ///请求类型：1-燃脂榜单，2-消耗榜单
            if (videoPlayer.rankDataManager.requestType == 2 && self.rollView.type == SportSwitchTypeNomal) {
                videoPlayer.rankDataManager.requestType = 1;
            }
        }
        
        /**
         更新排行榜头部数据
         🚨🚨🚨🚨不可和上面操作替换位置,因为此操作会修改上面self.rollView.type == SportSwitchTypeNomal判断
         */
        self.rollView.isMeritCourse = info.isMeritControl;
    }
    
    NSLog(@"🌕🌕info.isMeritControl ==== %@", info.isMeritControl ? @"YES":@"NO");
    NSLog(@"🌕🌕status ==== %@", status == DeviceConnected ? @"YES":@"NO");
    NSLog(@"🌕🌕videoPlayer.rankDataManager.requestType ==== %ld", videoPlayer.rankDataManager.requestType);
    NSLog(@"🌕🌕self.rollView.type ==== %ld", self.rollView.type);
    
    ///
    /**
     力量站屏蔽超燃脂率
     */
    if (info.liveModel.equipmentId.intValue != PowerEquipment) {
        [self.burnView hiddenBurnView:info.isMeritControl];
    }
    
    ///总的小火苗，根据是否超燃脂设备来判断隐藏与否。isMeritControl
    ///self.stepView.isHiddenTotalHotView = !info.isMeritControl;
    
    [self reloadWarnTipLayer];
}

/**
 中途连接设备弹温馨提示
 */
- (void)reloadWarnTipLayer{
    BOOL isConnectMachine = _videoPlayer.isConnectDevice;
    if (!isConnectMachine) return;
    
    ///< hasAlertWarnContrlLayer return
    if (_videoPlayer.isHasAlertWarnContrlLayer) return;
    
    ///< 没有数据源 return
    if (_videoPlayer.URLAsset == nil) return;
    
    ///已经禁止过温馨提示
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    BOOL videoGuideLayer = [userDefaults boolForKey:@"VideoWarnControlTip"];
    if (videoGuideLayer) return;
    
    MRKVideoPrepareDataInfo *info = _videoPlayer.videoPlayData;
    if ([info isMeritControl] && info.isConnectMachine) {
        ///暂停视频
        [self.videoPlayer pauseForUser];
        
        @weakify(self);
        self.currentVideoPlayer.hasAlertWarnContrlLayer = YES;
        self.currentVideoPlayer.videoWarnControlLayer.equipmentId = info.equipmentId;
        self.currentVideoPlayer.videoWarnControlLayer.courseCoverUrl = info.liveModel.cover;
        [self.currentVideoPlayer.switcher switchControlLayerForIdentifier:SJControlLayer_VideoWarn];
        self.currentVideoPlayer.videoWarnControlLayerHidden = ^{
            @strongify(self);
            ///恢复播放
            [self.videoPlayer play];
            
            ///重置榜单请求 【蒙层弹出后导致榜单数据拦截，再请求一次】
            self.videoPlayer.rankDataManager.requestType = 1;
        };
    }
}

///蓝牙断连/**设备断连弹窗*/
- (void)videoPlayerDisconnectBluetooth:(__kindof SJBaseVideoPlayer *)videoPlayer{
    if (self.BTConnectView) {
        [self.BTConnectView hide];
        self.BTConnectView = nil;
    }
    
    @weakify(self);
    self.BTConnectView = [MRKVideoBTConnectView build];
    self.BTConnectView.selectBlock = ^(__kindof AlertBaseView *alertView, NSInteger index) {
        @strongify(self);
        [alertView hide];
        if (index == 1) {
            [self _deviceItemWasTapped];
        }
    };
    [self.BTConnectView showIn:self];
    
    ///断连清数据
    //    [self _clearDataAdapterIfNeeded];
}



#pragma mark ---- SJControlLayerControlDelegate ----

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer andCommandModel:(id)model{
    [self sendInstructionsWithData:model];
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer useLessonPlanControl:(BOOL)useLessonPlanControl{
    ///刷新指令开关item
    [self _updateAutoItemIfNeeded];
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer closeBarrageControl:(BOOL)closeBarrageControl{
    ///刷新弹幕开关item
    [self _updateDanmuItemIfNeeded];
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer receiveDanmu:(id)model{
    if (self.dmListView.hidden) {return;}
    
    if([model isKindOfClass:[NSArray class]]) { // 录播弹幕
        for (MRKRenderModel *data in model) {
            data.msg_type = 1;
            [self.dmListView receiveMsg:data];
        }
    } else if([model isKindOfClass:[NSDictionary class]]) { //直播弹幕
        id data = [model valueForKeyPath:@"bullet_message"];
        MRKRenderModel *renderModel = [MRKRenderModel modelWithJSON:data];
        renderModel.isLivingMsg = YES;
        if ([renderModel.userId isNotBlank]) { ///直播可以直接判断是否自己所发
            renderModel.isSelf = [renderModel.userId isEqualToString:UserInfo.userId];
        }
        NSInteger msgType = renderModel.msg_type;
        switch (msgType) {
            case 1:{  ///收到弹幕数据
                ///
                [self.dmListView receiveMsg:renderModel];
            } break;
                
            case 2: {  ///进入直播间消息
                ///
                [[NSNotificationCenter defaultCenter] postNotificationName:mLivingAnimationNotification object:renderModel];
            } break;
                
            default: break;
        }
    }
}

/// ====== 直播欢迎提示条
- (void)animationManager:(MRKLivingAnimationManager *)animationManager showModel:(id)model{
    self.livingAnimationManager.isShowAnimation = YES;
    @weakify(self);
    [self.videoPlayer.livingPrompt showGreetPromptData:model selectHandler:^{
        @strongify(self);
        self.livingAnimationManager.isShowAnimation = NO;
        ///下一个动画
        [self.livingAnimationManager addSafeAnimation];
    }];
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer type:(NSInteger)type LIVERankData:(id)model{
    ///直播燃脂率排行
    if (type == 1) {
        [self.rollView rankData:model withType:SportDataTypeLIVEFatBuringRank];
    }
    
    ///直播消耗增量排行
    if (type == 2) {
        [self.rollView rankData:model withType:SportDataTypeLIVEKcalRank];
    }
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer type:(NSInteger)type VODRankData:(id)model{
    ///录播超燃脂率排行
    if (type == 1) {
        [self.rollView rankData:model withType:SportDataTypeVODFatBuringRank];
    }
    
    ///录播消耗排行
    if (type == 2) {
        [self.rollView rankData:model withType:SportDataTypeVODKcalRank];
    }
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer type:(NSInteger)type kcalIncremenData:(id)model{
    ///录播超燃脂率增量
    if (type == 1) {
        [self.rollView rankData:model withType:SportDataTypeVODFatBuring];
    }
    
    ///录播消耗增量
    if (type == 2) {
        [self.rollView rankData:model withType:SportDataTypeVODKcal];
    }
}

- (void)videoPlayerRankRequestError:(__kindof SJBaseVideoPlayer *)videoPlayer type:(NSInteger)type{
    [self.rollView rankDataRequestError];
}


- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer fatBurningModel:(id)model{
    if (![model isKindOfClass:[MRKFatBurningModel class]]) {  return; }
    
    /**
     提示类型：1-PERFECT，2-GOOD，3-JUST，4-MISS
     make.shadowBlurRadius = 3.0;     // 模糊程度
     */
    MRKFatBurningModel *burningModel = (MRKFatBurningModel *)model;
    switch (burningModel.tipType) {
        case 1: case 2: case 3: {
            @weakify(self);
            CGPoint point = self.burnView.center;
            ///
            self.videoPlayer.scorePrompt.target = self;
            
            UIImage *image = [AlivcImage imageNamed:[NSString stringWithFormat:@"textColor_%ld",burningModel.tipType]];
            image = [image imageByResizeToSize:CGSizeMake(6, 20)];
            [self.videoPlayer.scorePrompt showAttributedText:[NSAttributedString sj_UIKitText:^(id<SJUIKitTextMakerProtocol>  _Nonnull make) {
                make.append(burningModel.tipText ?:@"");
                make.append([NSString stringWithFormat:@" +%@",burningModel.meritRate]);
                make.font([UIFont fontWithName:DOUYU_Font size:16]);
                make.textColor([UIColor colorWithPatternImage:image]);
                make.shadow(^(NSShadow * _Nonnull make) {
                    make.shadowOffset = CGSizeMake(0, 1);
                    make.shadowColor = [UIColor whiteColor];
                });
            }] numberText:[NSAttributedString sj_UIKitText:^(id<SJUIKitTextMakerProtocol>  _Nonnull make) {
                make.append([NSString stringWithFormat:@"+%@",burningModel.meritRate]);
                make.font([UIFont fontWithName:DOUYU_Font size:16]);
                make.textColor([UIColor colorWithPatternImage:image]);
                make.shadow(^(NSShadow * _Nonnull make) {
                    make.shadowOffset = CGSizeMake(0, 1);
                    make.shadowColor = [UIColor whiteColor];
                });
            }] duration:3 moveToPoint:point completionHandler:^{
                self_weak_.burnView.totalMeritRate = burningModel.totalMeritRate;
            }];
            
        } break;
        case 4:{
            @weakify(self);
            CGPoint point = self.burnView.center;
            
            ///
            self.videoPlayer.scorePrompt.target = self;
            
            UIImage *image = [AlivcImage imageNamed:[NSString stringWithFormat:@"textColor_%ld",burningModel.tipType]];
            image = [image imageByResizeToSize:CGSizeMake(6, 20)];
            [self.videoPlayer.scorePrompt showAttributedText:[NSAttributedString sj_UIKitText:^(id<SJUIKitTextMakerProtocol>  _Nonnull make) {
                make.append(burningModel.tipText?:@"");
                make.font([UIFont fontWithName:DOUYU_Font size:16]);
                make.textColor([UIColor colorWithPatternImage:image]);
                make.shadow(^(NSShadow * _Nonnull make) {
                    make.shadowOffset = CGSizeMake(0, 1);
                    make.shadowColor = [UIColor whiteColor];
                });
            }]  numberText:nil duration:3 moveToPoint:point completionHandler:^{
                self_weak_.burnView.totalMeritRate = burningModel.totalMeritRate;
            }];
        } break;
        default:{
            self.burnView.totalMeritRate = burningModel.totalMeritRate;
        } break;
    }
    
    /// 超燃脂率回调，判断小火苗是否点亮
    if (burningModel.isFinish) {
        [self.stepView fireSection:burningModel.idd flameNum:burningModel.flameNum];
    }
    
    /// 不是关闭播放器时的结算
    if (!burningModel.isCloseSettledGuide) {
        /// 如果小火苗点亮，判断是否是第一次点亮，判断是否弹出过引导
        if (burningModel.flameNum >= 1) {
            [self fireGuide];
        }
        
        /// 判断超燃脂率引导显示
        [self meritRateGuide];
    }
}

///下一小节数据
- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer nextCourseLinkPOS:(id)model{
    if (![model isKindOfClass:[CourseLinkModel class]]) {  return; }
    CourseLinkModel *linkModel = (CourseLinkModel *)model;
    self.currentlinkModel = linkModel;
    
    MRKVideoPrepareDataInfo *videoPlayData = videoPlayer.videoPlayData;
    int equipmentId = videoPlayData.equipmentId.intValue;
    switch (equipmentId) {
        case BicycleEquipment: case EllipticalEquipment: case StairClimbEquiment:{
            /// 踏频(rpm) item
            SJEdgeControlButtonItem *treadItem = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_RateOfTread];
            if ( treadItem != nil) {
                if (treadItem.partView != nil) {
                    [treadItem.partView reloadExerciseDialView:linkModel];
                }
            }
        }break;
        case BoatEquipment: {
            /// 桨频(spm) item
            SJEdgeControlButtonItem *strokeItem = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_RateOfStroke];
            if ( strokeItem != nil) {
                if (strokeItem.partView != nil) {
                    [strokeItem.partView reloadExerciseDialView:linkModel];
                }
            }
        }break;
        default: break;
    }
    
    ///刷新当前文本提示窗
    [self  refreshNoteTips];
}

///下一大节数据
- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer nextCourseCataloguePOS:(id)model{
    if (![model isKindOfClass:[CourseCataloguePOSModel class]]) {  return; }
    CourseCataloguePOSModel *linkModel = (CourseCataloguePOSModel *)model;
    self.dmListView.dataArr = linkModel.hotWords; /// 更新弹幕view里面的热词
}

///显示商品窗
- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer courseGoods:(id)model {
    if (![model isKindOfClass:[MRKCourseGoodsModel class]]) {  return; }
    
    MRKCourseGoodsModel *goodsModel = (MRKCourseGoodsModel *)model;
    self.coachSellView.model = goodsModel;
    [self.coachSellView setHiddenAnimated:goodsModel.isLivingCourse ? !goodsModel.isShow : !goodsModel.displaying];
}

- (void)_showCoachCourseGoods:(id)model{
    [self videoPlayer:_videoPlayer courseGoods:model];
}





/// 这是一个只有在播放器锁屏状态下, 才会回调的方法
/// 当播放器锁屏后, 用户每次点击都会回调这个方法
- (void)tappedPlayerOnTheLockedState:(__kindof SJBaseVideoPlayer *)videoPlayer {
    
}

- (void)lockedVideoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer {
    
}

- (void)unlockedVideoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer {
    
}

- (void)videoPlayer:(SJBaseVideoPlayer *)videoPlayer reachabilityChanged:(SJNetworkStatus)status {
    if (@available(iOS 11.0, *)) {
        [self _reloadCustomStatusBarIfNeeded];
    }
    if ( _disabledPromptWhenNetworkStatusChanges ) return;
    if ( [self.videoPlayer.URLAsset.mediaURL isFileURL] ) return; // return when is local video.
    
    switch ( status ) {
        case SJNetworkStatus_NotReachable: {
            ///无网络提示
            NSString *tipStr = SJVideoPlayerConfigurations.shared.localizedStrings.unstableNetworkPrompt;
            [self videoTipString:tipStr];
        }
            break;
        case SJNetworkStatus_ReachableViaWWAN: {
            ///4G流量提示
            NSString *tipStr = SJVideoPlayerConfigurations.shared.localizedStrings.cellularNetworkPrompt;
            [self videoTipString:tipStr];
        }
            break;
        case SJNetworkStatus_ReachableViaWiFi: {
            
        }
            break;
    }
}










#pragma mark -

- (NSString *)stringForSeconds:(NSInteger)secs {
    return _videoPlayer ? [_videoPlayer stringForSeconds:secs] : @"";
}

#pragma mark -

- (void)setFixesBackItem:(BOOL)fixesBackItem {
    if ( fixesBackItem == _fixesBackItem )
        return;
    _fixesBackItem = fixesBackItem;
    dispatch_async(dispatch_get_main_queue(), ^{
        if ( self->_fixesBackItem ) {
            [self.controlView addSubview:self.fixedBackButton];
            [self->_fixedBackButton mas_makeConstraints:^(MASConstraintMaker *make) {
                make.top.left.bottom.equalTo(self.topAdapter.view);
                make.width.equalTo(self.topAdapter.view.mas_height);
            }];
            
            [self _updateAppearStateForResidentBackButtonIfNeeded];
            [self _reloadTopAdapterIfNeeded];
        }
        else {
            if ( self->_fixedBackButton ) {
                [self->_fixedBackButton removeFromSuperview];
                self->_fixedBackButton = nil;
                
                // back item
                [self _reloadTopAdapterIfNeeded];
            }
        }
    });
}

- (void)setHiddenBottomProgressIndicator:(BOOL)hiddenBottomProgressIndicator {
    if ( hiddenBottomProgressIndicator != _hiddenBottomProgressIndicator ) {
        _hiddenBottomProgressIndicator = hiddenBottomProgressIndicator;
        dispatch_async(dispatch_get_main_queue(), ^{
            [self _showOrRemoveBottomProgressIndicator];
        });
    }
}

- (void)setBottomProgressIndicatorHeight:(CGFloat)bottomProgressIndicatorHeight {
    if ( bottomProgressIndicatorHeight != _bottomProgressIndicatorHeight ) {
        _bottomProgressIndicatorHeight = bottomProgressIndicatorHeight;
        dispatch_async(dispatch_get_main_queue(), ^{
            [self _updateLayoutForBottomProgressIndicator];
        });
    }
}

- (void)setLoadingView:(nullable UIView<SJLoadingView> *)loadingView {
    if ( loadingView != _loadingView ) {
        [_loadingView removeFromSuperview];
        _loadingView = loadingView;
        if ( loadingView != nil ) {
            ///单次loading时长
            //            loadingView.loadingSecondsBlock = ^(NSTimer * _Nonnull timer, NSTimeInterval time) {
            //                if (time > 5.0) {
            //                    ///判断是否提示, 查询有无可提示的清晰度
            //                    if (self.showAdviseDefinition && [self _checkPlayAdviseDefinition]){
            //                        self.showAdviseDefinition = NO;
            //                        [self _playAdviseDefinition];
            //
            //                        ///销毁本次timer
            //                        if ([timer isValid]) {
            //                            [timer invalidate];
            //                            timer = nil;
            //                        }
            //                    }
            //                }
            //            };
            [self.controlView addSubview:loadingView];
            [loadingView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.center.offset(0);
            }];
        }
    }
}

- (void)setTitleView:(nullable __kindof UIView<SJScrollingTextMarqueeView> *)titleView {
    _titleView = titleView;
    [self _reloadTopAdapterIfNeeded];
}

- (void)setCustomStatusBar:(UIView<SJFullscreenModeStatusBar> *)customStatusBar NS_AVAILABLE_IOS(11.0) {
    if ( customStatusBar != _customStatusBar ) {
        [_customStatusBar removeFromSuperview];
        _customStatusBar = customStatusBar;
        [self _reloadCustomStatusBarIfNeeded];
    }
}

- (void)setShouldShowCustomStatusBar:(BOOL (^)(SJEdgeControlLayer * _Nonnull))shouldShowCustomStatusBar NS_AVAILABLE_IOS(11.0) {
    _shouldShowCustomStatusBar = shouldShowCustomStatusBar;
    [self _updateAppearStateForCustomStatusBar];
}

- (void)setOnlyUsedFitOnScreen:(BOOL)onlyUsedFitOnScreen {
    if ( onlyUsedFitOnScreen != _onlyUsedFitOnScreen ) {
        _onlyUsedFitOnScreen = onlyUsedFitOnScreen;
        if ( _onlyUsedFitOnScreen ) {
            _automaticallyPerformRotationOrFitOnScreen = NO;
        }
    }
}








#pragma mark - setup view

- (void)_setupView {
    [self _addItemsToTopAdapter];
    [self _addItemsToLeftAdapter];
    [self _addItemsToBottomAdapter];
    [self _addItemsToRightAdapter];
    [self _addItemsToCenterAdapter];
    
    [self _addItemsTopDataAdapter];       ///添加进度展示层
    [self _addItemsBottomDataAdapter];   ///添加数据展示层
    
    
    self.topContainerView.sjv_disappearDirection = SJViewDisappearAnimation_None;
    self.leftContainerView.sjv_disappearDirection = SJViewDisappearAnimation_None;
    self.bottomContainerView.sjv_disappearDirection = SJViewDisappearAnimation_None;
    self.rightContainerView.sjv_disappearDirection = SJViewDisappearAnimation_None;
    self.centerContainerView.sjv_disappearDirection = SJViewDisappearAnimation_None;
    
    self.topDataView.sjv_disappearDirection = SJViewDisappearAnimation_Top;
    self.bottomDataView.sjv_disappearDirection = SJViewDisappearAnimation_Bottom;
    
    sj_view_initializes(@[self.topContainerView,
                          self.leftContainerView,
                          self.bottomContainerView,
                          self.rightContainerView,
                          self.topDataView,
                          self.bottomDataView]);
    
    [NSNotificationCenter.defaultCenter addObserver:self
                                           selector:@selector(_resetControlLayerAppearIntervalForItemIfNeeded:)
                                               name:SJEdgeControlButtonItemPerformedActionNotification
                                             object:nil];
}

@synthesize fixedBackButton = _fixedBackButton;
- (UIButton *)fixedBackButton {
    if ( _fixedBackButton ) return _fixedBackButton;
    _fixedBackButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [_fixedBackButton setImage:SJVideoPlayerConfigurations.shared.resources.backImage forState:UIControlStateNormal];
    [_fixedBackButton addTarget:self action:@selector(_fixedBackButtonWasTapped) forControlEvents:UIControlEventTouchUpInside];
    return _fixedBackButton;
}

@synthesize bottomProgressIndicator = _bottomProgressIndicator;
- (SJProgressSlider *)bottomProgressIndicator {
    if ( _bottomProgressIndicator ) return _bottomProgressIndicator;
    _bottomProgressIndicator = [SJProgressSlider new];
    _bottomProgressIndicator.pan.enabled = NO;
    _bottomProgressIndicator.trackHeight = _bottomProgressIndicatorHeight;
    _bottomProgressIndicator.round = NO;
    
    id<SJVideoPlayerControlLayerResources> sources = SJVideoPlayerConfigurations.shared.resources;
    UIColor *traceColor = sources.bottomIndicatorTraceColor ?: sources.progressTraceColor;
    UIColor *trackColor = sources.bottomIndicatorTrackColor ?: sources.progressTrackColor;
    _bottomProgressIndicator.traceImageView.backgroundColor = traceColor;
    _bottomProgressIndicator.trackImageView.backgroundColor = trackColor;
    return _bottomProgressIndicator;
}

@synthesize loadingView = _loadingView;
- (UIView<SJLoadingView> *)loadingView {
    if ( _loadingView == nil ) {
        [self setLoadingView:[SJLoadingView.alloc initWithFrame:CGRectZero]];
    }
    return _loadingView;
}

@synthesize titleView = _titleView;
- (UIView<SJScrollingTextMarqueeView> *)titleView {
    if ( _titleView == nil ) {
        [self setTitleView:[SJScrollingTextMarqueeView.alloc initWithFrame:CGRectZero]];
    }
    return _titleView;
}

@synthesize customStatusBar = _customStatusBar;
- (UIView<SJFullscreenModeStatusBar> *)customStatusBar {
    if ( _customStatusBar == nil ) {
        [self setCustomStatusBar:[SJFullscreenModeStatusBar.alloc initWithFrame:CGRectZero]];
    }
    return _customStatusBar;
}

@synthesize shouldShowCustomStatusBar = _shouldShowCustomStatusBar;
- (BOOL (^)(SJEdgeControlLayer * _Nonnull))shouldShowCustomStatusBar {
    if ( _shouldShowCustomStatusBar == nil ) {
        BOOL is_iPhoneX = _screen.is_iPhoneX;
        [self setShouldShowCustomStatusBar:^BOOL(SJEdgeControlLayer * _Nonnull controlLayer) {
            if ( controlLayer.videoPlayer.isFitOnScreen ) return NO;
            
            BOOL isFullscreen = controlLayer.videoPlayer.isFullscreen;
            if ( isFullscreen == NO ) {
                CGRect bounds = UIScreen.mainScreen.bounds;
                if ( bounds.size.width > bounds.size.height )
                    isFullscreen = CGRectEqualToRect(controlLayer.bounds, bounds);
            }
            
            BOOL shouldShow = NO;
            if ( isFullscreen ) {
                /// 13 以后, 全屏后显示自定义状态栏
                if ( @available(iOS 13.0, *) ) {
                    shouldShow = YES;
                }
                /// 11 仅 iPhone X 显示自定义状态栏
                else if ( @available(iOS 11.0, *) ) {
                    shouldShow = is_iPhoneX;
                }
            }
            return shouldShow;
        }];
    }
    return _shouldShowCustomStatusBar;
}

@synthesize dateTimerControl = _dateTimerControl;
- (SJTimerControl *)dateTimerControl {
    if ( _dateTimerControl == nil ) {
        _dateTimerControl = SJTimerControl.alloc.init;
        _dateTimerControl.interval = 1;
        __weak typeof(self) _self = self;
        _dateTimerControl.exeBlock = ^(SJTimerControl * _Nonnull control) {
            __strong typeof(_self) self = _self;
            if ( !self ) return;
            self.customStatusBar.isHidden ? [control interrupt] : [self _reloadCustomStatusBarIfNeeded];
        };
    }
    return _dateTimerControl;
}



#pragma Mark --- _addItemsToTopAdapter ---
- (void)_addItemsToTopAdapter {
    
    ///返回按钮
    SJEdgeControlButtonItem *backItem = [SJEdgeControlButtonItem placeholderWithType:SJButtonItemPlaceholderType_49x49 tag:SJEdgeControlLayerTopItem_Back];
    backItem.resetsAppearIntervalWhenPerformingItemAction = NO;
    [backItem addAction:[SJEdgeControlButtonItemAction actionWithTarget:self action:@selector(_backItemWasTapped)]];
    [self.topAdapter addItem:backItem];
    _backItem = backItem;
    
    ///title
    SJEdgeControlButtonItem *titleItem = [SJEdgeControlButtonItem placeholderWithType:SJButtonItemPlaceholderType_49xFill tag:SJEdgeControlLayerTopItem_Title];
    [self.topAdapter addItem:titleItem];
    
    ///氛围 SJEdgeControlLayerTopItem_Model
    MRKVideoDefinitionView *customView = [[MRKVideoDefinitionView alloc] initWithFrame:CGRectMake(0, 0, 55, 30)];
    SJEdgeControlButtonItem *modelItem = [[SJEdgeControlButtonItem alloc] initWithCustomView:customView tag:SJEdgeControlLayerTopItem_Model];
    [modelItem addAction:[SJEdgeControlButtonItemAction actionWithTarget:self action:@selector(_modelItemWasTapped)]];
    [self.topAdapter addItem:modelItem];
    
    ///连接设备
    SJEdgeControlButtonItem *deviceItem = [SJEdgeControlButtonItem placeholderWithType:SJButtonItemPlaceholderType_49x49 tag:SJEdgeControlLayerTopItem_Device];
    [deviceItem addAction:[SJEdgeControlButtonItemAction actionWithTarget:self action:@selector(_deviceItemWasTapped)]];
    [self.topAdapter addItem:deviceItem];
    
    ///超燃脂控制开关
    SJEdgeControlButtonItem *teachItem = [SJEdgeControlButtonItem placeholderWithType:SJButtonItemPlaceholderType_49x49 tag:SJEdgeControlLayerTopItem_Auto];
    [teachItem addAction:[SJEdgeControlButtonItemAction actionWithTarget:self action:@selector(_autoItemWasTapped)]];
    [self.topAdapter addItem:teachItem];
    
    {
        SJEdgeControlButtonItem *sizeAdaptItem = [SJEdgeControlButtonItem placeholderWithType:SJButtonItemPlaceholderType_49x49 tag:SJEdgeControlLayerTopItem_SizeAdapt];
        [sizeAdaptItem addAction:[SJEdgeControlButtonItemAction actionWithTarget:self action:@selector(_sizeAdaptItemItemWasTapped)]];
        [self.topAdapter addItem:sizeAdaptItem];
        
        SJEdgeControlButtonItem *tvItem = [SJEdgeControlButtonItem placeholderWithType:SJButtonItemPlaceholderType_49x49 tag:SJEdgeControlLayerTopItem_TV];
        [tvItem addAction:[SJEdgeControlButtonItemAction actionWithTarget:self action:@selector(_tvItemWasTapped)]];
        [self.topAdapter addItem:tvItem];
        
        SJEdgeControlButtonItem *mutedItem = [SJEdgeControlButtonItem placeholderWithType:SJButtonItemPlaceholderType_49x49 tag:SJEdgeControlLayerTopItem_Muted];
        [mutedItem addAction:[SJEdgeControlButtonItemAction actionWithTarget:self action:@selector(_mutedItemWasTapped)]];
        [self.topAdapter addItem:mutedItem];
    }
    
    SJEdgeControlButtonItem *moreItem = [SJEdgeControlButtonItem placeholderWithType:SJButtonItemPlaceholderType_49x49 tag:SJEdgeControlLayerTopItem_More];
    [moreItem addAction:[SJEdgeControlButtonItemAction actionWithTarget:self action:@selector(_moreItemWasTapped)]];
    [self.topAdapter addItem:moreItem];
    
    //    SJEdgeControlButtonItem *danmuItem = [SJEdgeControlButtonItem placeholderWithType:SJButtonItemPlaceholderType_49x49 tag:SJEdgeControlLayerTopItem_Danmu];
    //    [danmuItem addAction:[SJEdgeControlButtonItemAction actionWithTarget:self action:@selector(_danmuItemWasTapped)]];
    //    [self.topAdapter addItem:danmuItem];
    
    //    SJEdgeControlButtonItem *shareItem = [SJEdgeControlButtonItem placeholderWithType:SJButtonItemPlaceholderType_49x49 tag:SJEdgeControlLayerTopItem_Share];
    //    [shareItem addAction:[SJEdgeControlButtonItemAction actionWithTarget:self action:@selector(_shareItemWasTapped)]];
    //    [self.topAdapter addItem:shareItem];
    
    [self.topAdapter reload];
}





- (void)_addItemsToLeftAdapter {
    self.rollView.frame = CGRectMake(0, 0, 160, 240);
    SJEdgeControlButtonItem *rollitem = [[SJEdgeControlButtonItem alloc] initWithCustomView:self.rollView tag:SJEdgeControlLayerLeftItem_Rank];
    [self.leftAdapter addItem:rollitem];
    
    [self.leftAdapter reload];
}

- (void)_addItemsToBottomAdapter {
    
    SJEdgeControlButtonItem *titleItem = [SJEdgeControlButtonItem placeholderWithType:SJButtonItemPlaceholderType_49xFill tag:SJEdgeControlLayerBottomItem_Holder];
    [self.bottomAdapter addItem:titleItem];
    
    ///当前时间
    SJEdgeControlButtonItem *currentTimeItem = [SJEdgeControlButtonItem placeholderWithSize:8 tag:SJEdgeControlLayerBottomItem_CurrentTime];
    [self.bottomAdapter addItem:currentTimeItem];
    
    /// 时间分隔符
    SJEdgeControlButtonItem *separatorItem = [[SJEdgeControlButtonItem alloc] initWithTitle:[NSAttributedString sj_UIKitText:^(id<SJUIKitTextMakerProtocol>  _Nonnull make) {
        make.append(@"/");
        make.font([UIFont fontWithName:fontNamePing size:16]);
        make.textColor([UIColor whiteColor]);
        make.alignment(NSTextAlignmentCenter);
    }] target:nil action:NULL tag:SJEdgeControlLayerBottomItem_Separator];
    [self.bottomAdapter addItem:separatorItem];
    
    /// 全部时长
    SJEdgeControlButtonItem *durationTimeItem = [SJEdgeControlButtonItem placeholderWithSize:8 tag:SJEdgeControlLayerBottomItem_DurationTime];
    [self.bottomAdapter addItem:durationTimeItem];
    
    /// 播放按钮
    SJEdgeControlButtonItem *playItem = [SJEdgeControlButtonItem placeholderWithType:SJButtonItemPlaceholderType_49x49 tag:SJEdgeControlLayerBottomItem_Play];
    [playItem addAction:[SJEdgeControlButtonItemAction actionWithTarget:self action:@selector(_playItemWasTapped)]];
    [self.bottomAdapter addItem:playItem];
    
    [self.bottomAdapter reload];
}

- (void)_addItemsTopDataAdapter{
    //    self.stepView.frame = CGRectMake(0, (68-DHPX(45))/2 , MAX(MainWidth, MainHeight), DHPX(45));
    [self.topDataView addSubview:self.stepView];
    [self.stepView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_equalTo(self.topDataView.mas_centerY);
        make.centerX.mas_equalTo(self.topDataView.mas_centerX);
        make.height.offset(DHPX(45));
        make.width.offset(RealScreenHeight);
    }];
    self.stepView.hidden = YES;
}

- (void)_addItemsBottomDataAdapter {
    MRKVideoPrepareDataInfo *videoPlayData = _videoPlayer.videoPlayData;
    int equipmentId = videoPlayData.equipmentId.intValue;
    for (MRKEqDisplayModel *model in videoPlayData.deviceDisplayArr) {
        SJEdgeControlButtonItemTag tag = model.displayId.integerValue + 50000;
        BOOL showPointer = NO;
        ///判断划船机 && 桨频
        if (equipmentId == BoatEquipment && tag == SJEdgeControlLayerCenterItem_RateOfStroke){
            showPointer = YES;
        }
        
        ///判断单车或者椭圆机 && 踏频
        if ( (equipmentId == BicycleEquipment || equipmentId == EllipticalEquipment || equipmentId == StairClimbEquiment) && tag == SJEdgeControlLayerCenterItem_RateOfTread){
            showPointer = YES;
        }
        
        SJEdgeControlButtonItem *item = [SJEdgeControlButtonItem placeholderWithSize:showPointer? 150 + 40 : DHPX(90) tag:tag];
        item.dataStr = model.defaultValue;
        item.holdStr = model.name;
        [self.dataAdapter addItem:item];
    }
    
    [self.dataAdapter reload];
}

///刷新BottomDataAdapter
///刷新不支持阻力回显的设备. 阻力模块剔除
- (void)_reloadItemsBottomDataAdapter{
    MRKVideoPrepareDataInfo *videoPlayData = _videoPlayer.videoPlayData;
    int equipmentType = videoPlayData.equipmentId.intValue;
    if (equipmentType == BicycleEquipment ||
        equipmentType == BoatEquipment ||
        equipmentType == EllipticalEquipment ) {
        
        if (videoPlayData.eqModel == nil ){
            return;
        }
        
        ///不支持阻力回显
        if (!videoPlayData.eqModel.isSupportResistanceEcho ){
            [self.dataAdapter removeItemForTag:SJEdgeControlLayerCenterItem_Resistance];
            [self.dataAdapter reload];
        }
    }
}


- (void)_addItemsToRightAdapter {
    CGFloat padding = IS_IPHONEX_SURE ? 30 : 10;
    
    self.heartItemView.frame = CGRectMake(0, 0, MRKDanmuViewWidth +padding, 60);
    SJEdgeControlButtonItem *heartItem = [[SJEdgeControlButtonItem alloc] initWithCustomView:self.heartItemView tag:SJEdgeControlLayerLeftItem_HeartView];
    [self.rightAdapter addItem:heartItem];
    
    CGFloat height = kScreenWidth -DHPX(45) -20 -80 -50;
    if (isIPhoneXSeries()) {
        height -= 20; //底部减掉20状态
    }
    
    self.dmListView.frame = CGRectMake(0, 0, MRKDanmuViewWidth +padding, height);
    SJEdgeControlButtonItem *chatItem = [[SJEdgeControlButtonItem alloc] initWithCustomView:self.dmListView tag:SJEdgeControlLayerLeftItem_LiveRoom];
    [self.rightAdapter addItem:chatItem];
    
    [self.rightAdapter reload];
}

- (void)_addItemsToCenterAdapter {
    //    ///重播按钮
    //    UILabel *replayLabel = [UILabel new];
    //    replayLabel.numberOfLines = 0;
    //    SJEdgeControlButtonItem *replayItem = [SJEdgeControlButtonItem frameLayoutWithCustomView:replayLabel tag:SJEdgeControlLayerCenterItem_Replay];
    //    [replayItem addAction:[SJEdgeControlButtonItemAction actionWithTarget:self action:@selector(_replayItemWasTapped)]];
    //    [self.centerAdapter addItem:replayItem];
    
    ///播放按钮
    SJEdgeControlButtonItem *playItem = [SJEdgeControlButtonItem placeholderWithType:SJButtonItemPlaceholderType_49x49 tag:SJEdgeControlLayerBottomItem_Play];
    [playItem addAction:[SJEdgeControlButtonItemAction actionWithTarget:self action:@selector(_playItemWasTapped)]];
    [self.centerAdapter addItem:playItem];
    
    [self.centerAdapter reload];
}


//TrainDataType

#pragma mark ---- 数据处理 ----
- (void)dataAdapterUpdateWithModel:(TrainingShowData *)model{
    @weakify(self);
    dispatch_async(dispatch_get_main_queue(), ^{
        @strongify(self);
        TrainingShowData *m = model;
        self.tyModel = model;
        for (SJEdgeControlButtonItem *item in self.dataAdapter.items) {
            TrainDataType tag = item.tag; //(SJEdgeControlButtonItemTag)
            ///
            switch (tag) {
                case TrainDataType_TrainTime: {///运动时间
                    item.dataStr = m.totalTime;
                }break;
                case TrainDataType_Kcal: {///消耗(kcal)
                    item.dataStr = m.totalKcal;
                }break;
                case TrainDataType_Rate: {///心率(bmp)
                    
                } break;
                case TrainDataType_RateOfTread: case TrainDataType_RateOfStroke:{///踏频(rpm)/桨频(spm)
                    item.dataStr = m.spm;
                }break;
                case TrainDataType_Resistance: {///阻力(lv)
                    item.dataStr = m.resistance ;
                }break;
                case TrainDataType_Slope: {///坡度
                    item.dataStr = m.gradient;
                }break;
                case TrainDataType_Distance: {///距离(km)
                    item.dataStr = m.totalDistance;
                }break;
                case TrainDataType_Count:  case TrainDataType_QCount: {///个数/圈数
                    item.dataStr = m.totalNum;
                }break;
                case TrainDataType_Gear: {///挡位
                    item.dataStr = m.gear;
                }break;
                case TrainDataType_Speed: { ///速度(km/h)
                    item.dataStr = m.speed;
                }break;
                case TrainDataType_PCount: { ///力量站次数
                    item.dataStr = m.totalNum;
                }break;
                default: break;
            }
            [self.dataAdapter updateContentForItemWithTag:tag];
        }
    });
}












#pragma mark ---- ContainerView appear state ----

- (void)_updateAppearStateForContainerViews {
    [self _updateAppearStateForTopContainerView];
    [self _updateAppearStateForLeftContainerView];
    [self _updateAppearStateForBottomContainerView];
    [self _updateAppearStateForRightContainerView];
    [self _updateAppearStateForCenterContainerView];
    
    [self _updateAppearStateForTopDataContainerView];
    [self _updateAppearStateForBottomDataContainerView];
    
    if (@available(iOS 11.0, *)) {
        [self _updateAppearStateForCustomStatusBar];
    }
}

- (void)_updateAppearStateForTopContainerView {
    if ( 0 == _topAdapter.numberOfItems ) {
        sj_view_makeDisappear(_topContainerView, YES);
        return;
    }
    
    /// 锁屏状态下, 使隐藏
    if ( _videoPlayer.isLockedScreen ) {
        sj_view_makeDisappear(_topContainerView, YES);
        return;
    }
    
    /// 是否显示
    if ( _videoPlayer.isControlLayerAppeared ) {
        sj_view_makeAppear(_topContainerView, YES);
    } else {
        sj_view_makeDisappear(_topContainerView, YES);
    }
}

- (void)_updateAppearStateForLeftContainerView {
    if ( 0 == _leftAdapter.numberOfItems ) {
        sj_view_makeDisappear(_leftContainerView, YES);
        return;
    }
    
    /// 锁屏状态下显示
    if ( _videoPlayer.isLockedScreen ) {
        sj_view_makeAppear(_leftContainerView, YES);
        return;
    }
    
    /// 是否显示
    //    if ( _videoPlayer.isControlLayerAppeared ) {
    sj_view_makeAppear(_leftContainerView, YES);
    //    } else {
    //        sj_view_makeDisappear(_leftContainerView, YES);
    //    }
}

/// 更新显示状态
- (void)_updateAppearStateForBottomContainerView {
    if ( 0 == _bottomAdapter.numberOfItems ) {
        sj_view_makeDisappear(_bottomContainerView, YES);
        return;
    }
    
    /// 锁屏状态下显示
    if ( _videoPlayer.isLockedScreen ) {
        sj_view_makeAppear(_leftContainerView, YES);
        return;
    }
    
    /// 是否显示
    if ( _videoPlayer.isControlLayerAppeared ) {
        sj_view_makeAppear(_bottomContainerView, YES);
    } else {
        sj_view_makeDisappear(_bottomContainerView, YES);
    }
}

/// 更新显示状态
- (void)_updateAppearStateForRightContainerView {
    if ( 0 == _rightAdapter.numberOfItems ) {
        sj_view_makeDisappear(_rightContainerView, YES);
        return;
    }
    
    /// 锁屏状态下, 使隐藏
    if ( _videoPlayer.isLockedScreen ) {
        sj_view_makeDisappear(_rightContainerView, YES);
        return;
    }
    /// 是否显示
    //    if ( _videoPlayer.isControlLayerAppeared ) {
    sj_view_makeAppear(_rightContainerView, YES);
    //    } else {
    //        sj_view_makeDisappear(_rightContainerView, YES);
    //    }
}

- (void)_updateAppearStateForCenterContainerView {
    if ( 0 == _centerAdapter.numberOfItems ) {
        sj_view_makeDisappear(_centerContainerView, YES);
        return;
    }
    
    sj_view_makeAppear(_centerContainerView, YES);
}

- (void)_updateAppearStateForBottomProgressIndicatorIfNeeded {
    if ( _bottomProgressIndicator == nil )
        return;
    
    _videoPlayer.isControlLayerAppeared && !_videoPlayer.isLockedScreen ?
    sj_view_makeDisappear(_bottomProgressIndicator, YES) :
    sj_view_makeAppear(_bottomProgressIndicator, YES);
}

- (void)_updateAppearStateForCustomStatusBar NS_AVAILABLE_IOS(11.0) {
    BOOL shouldShow = self.shouldShowCustomStatusBar(self);
    if ( shouldShow ) {
        if ( self.customStatusBar.superview == nil ) {
            static dispatch_once_t onceToken;
            dispatch_once(&onceToken, ^{
                UIDevice.currentDevice.batteryMonitoringEnabled = YES;
            });
            
            [NSNotificationCenter.defaultCenter addObserver:self selector:@selector(_reloadCustomStatusBarIfNeeded) name:UIDeviceBatteryLevelDidChangeNotification object:nil];
            [NSNotificationCenter.defaultCenter addObserver:self selector:@selector(_reloadCustomStatusBarIfNeeded) name:UIDeviceBatteryStateDidChangeNotification object:nil];
            
            [self.topContainerView addSubview:self.customStatusBar];
            [self.customStatusBar mas_makeConstraints:^(MASConstraintMaker *make) {
                make.top.offset(0);
                make.left.right.equalTo(self.topAdapter);
                make.height.offset(20);
            }];
        }
    }
    
    _customStatusBar.hidden = !shouldShow;
    _customStatusBar.isHidden ? [self.dateTimerControl interrupt] : [self.dateTimerControl resume];
}

- (void)_updateAppearStateForTopDataContainerView {
    /// 是否显示
    if ( _videoPlayer.isControlLayerAppeared ) {
        sj_view_makeDisappear(_topDataView, YES);
        return;
    }
    
    if (_videoPlayer.playbackType == SJPlaybackTypeREAL) {
        sj_view_makeDisappear(_topDataView, YES);
        return;
    }
    
    /// 是否显示
    //    if ( _videoPlayer.isControlLayerAppeared ) {
    sj_view_makeAppear(_topDataView, YES);
    //    } else {
    //        sj_view_makeDisappear(_topDataView, YES);
    //    }
}

- (void)_updateAppearStateForBottomDataContainerView {
    if ( 0 == _dataAdapter.numberOfItems ) {
        sj_view_makeDisappear(_bottomDataView, YES);
        return;
    }
    
    if ( _videoPlayer.isControlLayerAppeared ) {
        ///隐藏
        sj_view_makeDisappear(_bottomDataView, YES);
    } else {
        ///显示
        sj_view_makeAppear(_bottomDataView, YES, ^{
           
        });
    }
}

/// 暂不实现
- (void)_updateContentForPictureInPictureItem API_AVAILABLE(ios(14.0)) {
    
    
}








#pragma mark ---- update items ----

- (void)_reloadAdaptersIfNeeded {
    [self _reloadTopAdapterIfNeeded];
    [self _reloadLeftAdapterIfNeeded];
    [self _reloadBottomAdapterIfNeeded];
    [self _reloadRightAdapterIfNeeded];
    [self _reloadCenterAdapterIfNeeded];
    
    [self _reloadDataAdapterIfNeeded];
}

- (void)_reloadTopAdapterIfNeeded {
    if ( sj_view_isDisappeared(_topContainerView) ) return;
    
    id<SJVideoPlayerControlLayerResources> sources = SJVideoPlayerConfigurations.shared.resources;
    BOOL isFullscreen = _videoPlayer.isFullscreen;
    BOOL isFitOnScreen = _videoPlayer.isFitOnScreen;
    BOOL isPlayOnScrollView = _videoPlayer.isPlayOnScrollView;
    BOOL isSmallscreen = !isFullscreen && !isFitOnScreen;
    
    // back item
    SJEdgeControlButtonItem *backItem = [self.topAdapter itemForTag:SJEdgeControlLayerTopItem_Back];
    if ( backItem != nil ) {
        if ( _fixesBackItem ) {
            backItem.alpha = 0;
            backItem.image = nil;
        } else {
            if ( isFullscreen || isFitOnScreen )
                backItem.hidden = NO;
            else if ( _hiddenBackButtonWhenOrientationIsPortrait )
                backItem.hidden = YES;
            else
                backItem.hidden = isPlayOnScrollView;
            
            if ( backItem.hidden == NO )
                backItem.image = sources.backImage;
        }
    }
    
    // title item
    SJEdgeControlButtonItem *titleItem = [self.topAdapter itemForTag:SJEdgeControlLayerTopItem_Title];
    if ( titleItem != nil ) {
        if ( self.isHiddenTitleItemWhenOrientationIsPortrait && isSmallscreen ) {
            titleItem.hidden = YES;
        } else {
            if ( titleItem.customView != self.titleView )
                titleItem.customView = self.titleView;
            SJVideoPlayerURLAsset *asset = _videoPlayer.URLAsset;
            NSAttributedString *_Nullable attributedTitle = asset.attributedTitle;
            self.titleView.attributedText = attributedTitle;
            titleItem.hidden = (attributedTitle.length == 0);
        }
        
        if ( titleItem.hidden == NO ) {
            // margin
            NSInteger atIndex = [_topAdapter indexOfItemForTag:SJEdgeControlLayerTopItem_Title];
            CGFloat left  = [_topAdapter isHiddenWithRange:NSMakeRange(0, atIndex)] ? 16 : 0;
            CGFloat right = [_topAdapter isHiddenWithRange:NSMakeRange(atIndex, _topAdapter.numberOfItems)] ? 16 : 0;
            titleItem.insets = SJEdgeInsetsMake(left, right);
        }
    }
    
    // model item
    SJEdgeControlButtonItem *modelItem = [self.topAdapter itemForTag:SJEdgeControlLayerTopItem_Model];
    if ( modelItem != nil ) {
        if (_videoPlayer.courseModel.equipmentId.intValue == PowerEquipment) {
            modelItem.hidden = NO;
        }else{
            modelItem.hidden = !_videoPlayer.courseModel.isShowRankControl;
        }
        
        if ( modelItem.hidden == NO ) {
            MRKVideoDefinitionView *customView = (MRKVideoDefinitionView *)modelItem.customView;
            switch (self.displayType) {
                case SJEdgeLayerDisplayTypeAmbient:
                    customView.definition = @"氛围";
                    break;
                case SJEdgeLayerDisplayTypeImmerse:
                    customView.definition = @"沉浸";
                    break;
                default:
                    break;
            }
        }
    }
    
    // Device item
    SJEdgeControlButtonItem *deviceItem = [self.topAdapter itemForTag:SJEdgeControlLayerTopItem_Device];
    if ( deviceItem != nil ) {
        deviceItem.hidden = !_videoPlayer.courseModel.isSupportConnection;
        if ( deviceItem.hidden == NO ) {
            MRKVideoPrepareDataInfo *videoPlayData = _videoPlayer.videoPlayData;
            deviceItem.image = [AlivcImage mrkEquipmentImageWith:videoPlayData.equipmentId connect:_videoPlayer.isConnectDevice];
        }
    }
    
    // teach item
    SJEdgeControlButtonItem *teachItem = [self.topAdapter itemForTag:SJEdgeControlLayerTopItem_Auto];
    if ( teachItem != nil ) {
        teachItem.hidden = !_videoPlayer.videoPlayData.isShowAutoControl;
        if ( teachItem.hidden == NO ) {
            teachItem.image = _videoPlayer.isUseLessonPlanControl ? sources.autoImage : sources.noAutoImage;
        }
    }
    
    {   ///实景显示
        // sizeAdaptItem item
        SJEdgeControlButtonItem *sizeAdaptItem = [self.topAdapter itemForTag:SJEdgeControlLayerTopItem_SizeAdapt];
        if ( sizeAdaptItem != nil ) {
            sizeAdaptItem.hidden = !_videoPlayer.courseModel.isRealVideoCourse;
            if ( sizeAdaptItem.hidden == NO ) {
                sizeAdaptItem.image = _videoPlayer.isAdaptScreenSizeOn ? sources.sizeAdapt_DImage: sources.sizeAdapt_LImage;
            }
        }
        // tv item
        SJEdgeControlButtonItem *tvItem = [self.topAdapter itemForTag:SJEdgeControlLayerTopItem_TV];
        if ( tvItem != nil ) {
            tvItem.hidden = !_videoPlayer.courseModel.isRealVideoCourse;
            if ( tvItem.hidden == NO ) {
                tvItem.image = sources.tvImage;
            }
        }
        // muted item
        SJEdgeControlButtonItem *mutedItem = [self.topAdapter itemForTag:SJEdgeControlLayerTopItem_Muted];
        if ( mutedItem != nil ) {
            mutedItem.hidden = !_videoPlayer.courseModel.isRealVideoCourse;
            if ( mutedItem.hidden == NO ) {
                ///如果实景视频配置了播放音乐的url，要通过MRKAliAudioPlayer的isMuted判断
                if (self.useAudioPlayerMuted) {
                    mutedItem.image = self.audioPlayerMuted ? sources.muted_DImage : sources.muted_LImage;
                }else{
                    mutedItem.image = _videoPlayer.isMuted ? sources.muted_DImage : sources.muted_LImage;
                }
            }
        }
    }
    
    // more item
    SJEdgeControlButtonItem *moreItem = [self.topAdapter itemForTag:SJEdgeControlLayerTopItem_More];
    if ( moreItem != nil ) {
        moreItem.hidden = _videoPlayer.courseModel.isRealVideoCourse;
        if ( moreItem.hidden == NO ) {
            moreItem.image = sources.moreImage;
        }
    }
    
    [_topAdapter reload];
}

- (void)_reloadLeftAdapterIfNeeded {
    if ( sj_view_isDisappeared(_leftContainerView) ) return;
    
    SJEdgeControlButtonItem *rankItem = [self.leftAdapter itemForTag:SJEdgeControlLayerLeftItem_Rank];
    if ( rankItem != nil ) {
        BOOL isShowRoll = _videoPlayer.videoPlayData.liveModel.isShowRankControl;
        rankItem.hidden = !isShowRoll;
    }
    [_leftAdapter reload];
}

- (void)_reloadDisplayAdapterIfNeeded {
    if ( sj_view_isDisappeared(_topContainerView) ) return;
    
    // model item
    SJEdgeControlButtonItem *modelItem = [self.topAdapter itemForTag:SJEdgeControlLayerTopItem_Model];
    if ( modelItem != nil && !modelItem.hidden ) {
        MRKVideoDefinitionView *item = (MRKVideoDefinitionView *)modelItem.customView;
        switch (self.displayType) {
            case SJEdgeLayerDisplayTypeAmbient:
                item.definition = @"氛围";
                break;
            case SJEdgeLayerDisplayTypeImmerse:
                item.definition = @"沉浸";
                break;
            default:
                break;
        }
        [_topAdapter updateContentForItemWithTag:SJEdgeControlLayerTopItem_Model];
    }
}

- (void)_reloadSizeAdaptItemIfNeeded {
    if ( sj_view_isDisappeared(_topContainerView) ) return;
    
    id<SJVideoPlayerControlLayerResources> sources = SJVideoPlayerConfigurations.shared.resources;
    // sizeAdaptItem item
    SJEdgeControlButtonItem *sizeAdaptItem = [self.topAdapter itemForTag:SJEdgeControlLayerTopItem_SizeAdapt];
    if ( sizeAdaptItem != nil ) {
        sizeAdaptItem.image = _videoPlayer.isAdaptScreenSizeOn ? sources.sizeAdapt_DImage: sources.sizeAdapt_LImage;
        [_topAdapter updateContentForItemWithTag:SJEdgeControlLayerTopItem_SizeAdapt];
    }
}


- (void)_reloadBottomAdapterIfNeeded {
    if ( sj_view_isDisappeared(_bottomContainerView) ) return;
    
    id<SJVideoPlayerControlLayerResources> sources = SJVideoPlayerConfigurations.shared.resources;
    // play item
    {
        SJEdgeControlButtonItem *playItem = [self.bottomAdapter itemForTag:SJEdgeControlLayerBottomItem_Play];
        if ( playItem != nil && playItem.hidden == NO ) {
            playItem.image = _videoPlayer.isPaused ? sources.playImage : sources.pauseImage;
        }
    }
    
    [_bottomAdapter reload];
}


- (void)reloadRequestData{
    MRKVideoPrepareDataInfo *videoPlayData = _videoPlayer.videoPlayData;
    NSLog(@"🌕🌕🌕reloadDataAdapter.equipmentId   ==== %@ ",videoPlayData.equipmentId);
    ///添加蓝牙展示数据
    [self _addItemsBottomDataAdapter];
    
    self.stepView.isHiddenHotView = !videoPlayData.liveModel.isShowHotProgress;  //热力图是否显示
    self.stepView.equipmentId = videoPlayData.liveModel.equipmentId;
    self.stepView.totalFireNumber = videoPlayData.planModel.totalFireNumber;
    
    // 热力进度条赋值
    if (videoPlayData.planModel.courseCataloguePOS.count > 0) {
        // 整理成所需要的环节数据
        self.stepView.hidden = NO;
        self.stepView.progressData = videoPlayData.planModel.courseCataloguePOS;
    }
    
    [self.dmListView isHiddenDanmuView:_videoPlayer.courseModel.isRealVideoCourse]; ///实景隐藏
    if (self.dmListView.hidden == NO) {
        [self.dmListView welcomeToRoom:@"欢迎开启新的超燃脂旅程\n和万千燃友一起互相监督鼓励，冲击燃脂! 请大家一起维护课中良好氛围💪"];
    }
    
    [self _reloadDataAdapterIfNeeded];
}


///展示心率消耗view
- (void)showRateKcalView:(UIView *)view withModel:(id)model{
    ///tip 心率消耗的提示弹窗
    [self.heartItemView checkHeartRateAlert:view withModel:model];
    
    // GCD 延时执行
    dispatch_time_t delayTime = dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC));
    dispatch_after(delayTime, dispatch_get_main_queue(), ^{
        [self.heartItemView showRateKcalView:model completeHandler:^{
            
        }];
    });
}


- (TrainDataGaugeModel *)TrainDataGaugeModel{
    MRKVideoPrepareDataInfo *videoPlayData = _videoPlayer.videoPlayData;
    TrainDataGaugeModel *model = [[TrainDataGaugeModel alloc] init];
    model.equipmentType = videoPlayData.equipmentId.intValue;
    model.planMinDataValue = videoPlayData.planModel.minNum;
    model.planMaxDataValue = videoPlayData.planModel.maxNum;
    model.minDataValue = @0;
    model.maxDataValue = ({
        int equipmentId = videoPlayData.equipmentId.intValue;
        NSInteger maxValue = 0; ///默认为0
        if (equipmentId == BoatEquipment) { maxValue = 60; }
        if (equipmentId == EllipticalEquipment) { maxValue = 150; }
        if (equipmentId == BicycleEquipment) { maxValue = 160; }
        if (equipmentId == StairClimbEquiment) { maxValue = 110; }
        @(maxValue);
    });
    return model;
}

- (void)_reloadDataAdapterIfNeeded {
    //    if ( sj_view_isDisappeared(_bottomDataView) ) return;
    
    MRKVideoPrepareDataInfo *videoPlayData = _videoPlayer.videoPlayData;
    int equipmentId = videoPlayData.equipmentId.intValue;
    switch (equipmentId) {
        case BoatEquipment: case BicycleEquipment: case EllipticalEquipment: {
            
            // 阻力 item
            SJEdgeControlButtonItem *resistanceItem = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_Resistance];
            if ( resistanceItem != nil) {
                if (_videoPlayer.videoPlayData.isShowAutoControl) {
                    resistanceItem.showControl = _videoPlayer.isUseLessonPlanControl;
                }else{
                    resistanceItem.showControl = NO;
                }
            }
            
            // 踏频(rpm) item
            SJEdgeControlButtonItem *treadItem = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_RateOfTread];
            if ( treadItem != nil && videoPlayData.playbackType != SJPlaybackTypeUnknown) {
                if (treadItem.partView != nil) {
                    TrainDataItemView *partView = treadItem.partView;
                    partView.showPointer = YES;
                    partView.dialModel = [self TrainDataGaugeModel];
                }
            }
            
            // 桨频(spm) item
            SJEdgeControlButtonItem *strokeItem = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_RateOfStroke];
            if ( strokeItem != nil && videoPlayData.playbackType != SJPlaybackTypeUnknown) {
                if (strokeItem.partView != nil) {
                    TrainDataItemView *partView = strokeItem.partView;
                    partView.showPointer = YES;
                    partView.dialModel = [self TrainDataGaugeModel];
                }
            }
            
        }break;
        case TreadmillEquipment: {
            
            // 速度 item
            SJEdgeControlButtonItem *speedItem = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_Speed];
            if ( speedItem != nil) {
                if (_videoPlayer.videoPlayData.isShowAutoControl) {
                    speedItem.showControl = _videoPlayer.isUseLessonPlanControl;
                } else {
                    speedItem.showControl = NO;
                }
            }
            
            // 坡度 item
            SJEdgeControlButtonItem *slopeItem = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_Slope];
            if ( slopeItem != nil) {
                if (_videoPlayer.videoPlayData.isShowAutoControl) {
                    slopeItem.showControl = _videoPlayer.isUseLessonPlanControl;
                }else{
                    slopeItem.showControl = NO;
                }
            }
            
        } break;
            
        case StairClimbEquiment: {
            
            // 踏频(rpm) item
            SJEdgeControlButtonItem *treadItem = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_RateOfTread];
            if ( treadItem != nil && videoPlayData.playbackType != SJPlaybackTypeUnknown) {
                if (treadItem.partView != nil) {
                    TrainDataItemView *partView = treadItem.partView;
                    partView.showPointer = YES;
                    partView.dialModel = [self TrainDataGaugeModel];
                }
            }
            
        } break;
            
        default: break;
    }
    
    [self.dataAdapter reload];
}


/**
 断连清数据
 @note 断连后 踏频/桨频/速度 清空
 **/
- (void)_clearDataAdapterIfNeeded {
    MRKVideoPrepareDataInfo *videoPlayData = _videoPlayer.videoPlayData;
    int equipmentId = videoPlayData.equipmentId.intValue;
    switch (equipmentId) {
        case BoatEquipment: case BicycleEquipment: case EllipticalEquipment: {
            // 踏频(rpm) item
            SJEdgeControlButtonItem *treadItem = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_RateOfTread];
            if ( treadItem != nil ) {
                treadItem.dataStr = @"--";
                [self.dataAdapter updateContentForItemWithTag:SJEdgeControlLayerCenterItem_RateOfTread];
            }
            
            // 桨频(spm) item
            SJEdgeControlButtonItem *strokeItem = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_RateOfStroke];
            if ( strokeItem != nil ) {
                strokeItem.dataStr = @"--";
                [self.dataAdapter updateContentForItemWithTag:SJEdgeControlLayerCenterItem_RateOfStroke];
            }
        } break;
            
        case TreadmillEquipment: {
            // 速度 item
            SJEdgeControlButtonItem *speedItem = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_Speed];
            if ( speedItem != nil) {
                speedItem.dataStr = @"--";
                [self.dataAdapter updateContentForItemWithTag:SJEdgeControlLayerCenterItem_Speed];
            }
        } break;
            
        case StairClimbEquiment: {
            
            // 踏频(rpm) item
            SJEdgeControlButtonItem *treadItem = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_RateOfTread];
            if ( treadItem != nil ) {
                treadItem.dataStr = @"--";
                [self.dataAdapter updateContentForItemWithTag:SJEdgeControlLayerCenterItem_RateOfTread];
            }
            
        } break;
        default: break;
    }
}


- (void)_reloadTopDataAdaptersIfNeeded {
    if ( sj_view_isDisappeared(_topDataView) ) return;
    if ( self.stepView.hidden ) return;
    
    [self.stepView resetLayoutFrame];
}

- (void)_reloadRightAdapterIfNeeded {
    if ( sj_view_isDisappeared(_rightContainerView) ) return;
    
    SJEdgeControlButtonItem *chatItem = [self.rightAdapter itemForTag:SJEdgeControlLayerLeftItem_LiveRoom];
    if ( chatItem != nil ) {
        BOOL isRealVideoCourse = _videoPlayer.videoPlayData.liveModel.isRealVideoCourse;
        chatItem.hidden = isRealVideoCourse;
    }
    [self.rightAdapter reload];
}

- (void)_reloadCenterAdapterIfNeeded {
    if ( sj_view_isDisappeared(_centerContainerView) ) return;
    
    SJEdgeControlButtonItem *playItem = [self.centerAdapter itemForTag:SJEdgeControlLayerBottomItem_Play];
    if ( playItem != nil ) {
        BOOL isPaused = _videoPlayer.isPaused && !_videoPlayer.isPlaybackFinished;
        playItem.hidden = !isPaused;
        if ( playItem.hidden == NO ) {
            playItem.image = [AlivcImage imageNamed:@"icon_big_play"];
        }
    }
    
    [_centerAdapter reload];
}




/**
 刷新氛围model按钮的状态
 @note 用来处理手动点击 弹幕开关和排行榜时刷新 氛围模式
 **/
- (void)_updateModelForTopModelItemIfNeeded {
    SJEdgeControlButtonItem *modelItem = [self.topAdapter itemForTag:SJEdgeControlLayerTopItem_Model];
    if ( modelItem != nil && !modelItem.hidden) {
        ///判断排行榜是否展示
        BOOL hiddenRank = self.rollView.rankListViewHidden;
        BOOL hiddenDanmu = self.videoPlayer.closeBarrageControl;
        ///俩都为否的话, 改为沉浸模式
        if (hiddenRank && hiddenDanmu ){
            self.displayType = SJEdgeLayerDisplayTypeImmerse;
        }
        ///俩都为是的话, 改为氛围模式
        if (!hiddenRank && !hiddenDanmu ){
            self.displayType = SJEdgeLayerDisplayTypeAmbient;
        }
        [self _reloadDisplayAdapterIfNeeded];
    }
}

/**
 刷新静音按钮的状态
 **/
- (void)_updateMutedItemIfNeeded {
    if ( sj_view_isDisappeared(_topContainerView) ) return;
    id<SJVideoPlayerControlLayerResources> sources = SJVideoPlayerConfigurations.shared.resources;
    SJEdgeControlButtonItem *mutedItem = [self.topAdapter itemForTag:SJEdgeControlLayerTopItem_Muted];
    if ( mutedItem != nil && !mutedItem.hidden) {
        ///如果实景视频配置了播放音乐的url，要通过MRKAliAudioPlayer的isMuted判断
        if (self.useAudioPlayerMuted) {
            mutedItem.image = self.audioPlayerMuted ? sources.muted_DImage : sources.muted_LImage;
        }else{
            mutedItem.image = _videoPlayer.isMuted ? sources.muted_DImage : sources.muted_LImage;
        }
        [_topAdapter updateContentForItemWithTag:SJEdgeControlLayerTopItem_Muted];
    }
}


/**刷新设备连接按钮的状态**/
- (void)_updateConnectForTopDeviceItemIfNeeded {
    if ( sj_view_isDisappeared(_topContainerView) ) return;
    SJEdgeControlButtonItem *deviceItem = [self.topAdapter itemForTag:SJEdgeControlLayerTopItem_Device];
    // connect device item
    if ( deviceItem != nil && deviceItem.hidden == NO ) {
        MRKVideoPrepareDataInfo *videoPlayData = _videoPlayer.videoPlayData;
        deviceItem.image = [AlivcImage mrkEquipmentImageWith:videoPlayData.equipmentId connect:_videoPlayer.isConnectDevice];
        [_topAdapter updateContentForItemWithTag:SJEdgeControlLayerTopItem_Device];
    }
}

/**刷新自动指令开关的状态**/
- (void)_updateAutoItemIfNeeded {
    if ( sj_view_isDisappeared(_topContainerView) ) return;
    id<SJVideoPlayerControlLayerResources> sources = SJVideoPlayerConfigurations.shared.resources;
    // teach item
    SJEdgeControlButtonItem *teachItem = [self.topAdapter itemForTag:SJEdgeControlLayerTopItem_Auto];
    if ( teachItem != nil && teachItem.hidden == NO ) {
        teachItem.image = _videoPlayer.isUseLessonPlanControl ? sources.autoImage : sources.noAutoImage;
        [_topAdapter updateContentForItemWithTag:SJEdgeControlLayerTopItem_Auto];
    }
}

/**刷新弹幕按钮的状态**/
- (void)_updateDanmuItemIfNeeded {
    if ( sj_view_isDisappeared(_topContainerView) ) return;
    id<SJVideoPlayerControlLayerResources> sources = SJVideoPlayerConfigurations.shared.resources;
    // Danmu item
    SJEdgeControlButtonItem *danmuItem = [self.topAdapter itemForTag:SJEdgeControlLayerTopItem_Danmu];
    if ( danmuItem != nil && danmuItem.hidden == NO ) {
        danmuItem.image = _videoPlayer.closeBarrageControl ? sources.danmu_DImage: sources.danmu_LImage;
        [_topAdapter updateContentForItemWithTag:SJEdgeControlLayerTopItem_Danmu];
    }
}

/**刷新视频进度时长**/
- (void)_updateContentForBottomCurrentTimeItemIfNeeded {
    if ( sj_view_isDisappeared(_bottomContainerView) ) return;
    
    NSString *currentTimeStr = [_videoPlayer stringForSeconds:_videoPlayer.currentTime];
    SJEdgeControlButtonItem *currentTimeItem = [_bottomAdapter itemForTag:SJEdgeControlLayerBottomItem_CurrentTime];
    // palyertTime item
    if ( currentTimeItem != nil && currentTimeItem.isHidden == NO ) {
        currentTimeItem.title = [self _textForTimeString:currentTimeStr];
        [_bottomAdapter updateContentForItemWithTag:SJEdgeControlLayerBottomItem_CurrentTime];
    }
}

/**刷新视频时长**/
- (void)_updateContentForBottomDurationItemIfNeeded {
    SJEdgeControlButtonItem *durationTimeItem = [_bottomAdapter itemForTag:SJEdgeControlLayerBottomItem_DurationTime];
    if ( durationTimeItem != nil && durationTimeItem.isHidden == NO ) {
        durationTimeItem.title = [self _textForTimeString:[_videoPlayer stringForSeconds:_videoPlayer.duration]];
        [_bottomAdapter updateContentForItemWithTag:SJEdgeControlLayerBottomItem_DurationTime];
    }
}

- (void)_reloadSizeForBottomTimeLabel {
    // 00:00
    // 00:00:00
    NSString *ms = @"00:00";
    NSString *hms = @"00:00:00";
    NSString *durationTimeStr = [_videoPlayer stringForSeconds:_videoPlayer.duration];
    NSString *format = (durationTimeStr.length == ms.length) ? ms:hms;
    CGSize formatSize = [[self _textForTimeString:format] sj_textSize];
    
    SJEdgeControlButtonItem *currentTimeItem = [_bottomAdapter itemForTag:SJEdgeControlLayerBottomItem_CurrentTime];
    SJEdgeControlButtonItem *durationTimeItem = [_bottomAdapter itemForTag:SJEdgeControlLayerBottomItem_DurationTime];
    
    if ( !durationTimeItem && !currentTimeItem ) return;
    currentTimeItem.size = formatSize.width;
    durationTimeItem.size = formatSize.width;
    [_bottomAdapter reload];
}

- (void)_updateContentForBottomProgressIndicatorIfNeeded {
    if ( _bottomProgressIndicator != nil && !sj_view_isDisappeared(_bottomProgressIndicator) ) {
        _bottomProgressIndicator.value = _videoPlayer.currentTime;
        _bottomProgressIndicator.maxValue = _videoPlayer.duration ? : 1;
    }
}

- (void)_updateCurrentTimeForStepView {
    if (self.stepView.hidden) { return; }
    
    ///取蓝牙数据里对应值计算热力值
    //    NSNumber *value = self.videoPlayer.blueManager.dataManager.tyModel.hotProgressValue;
    
    NSNumber *value = self.videoPlayer.trainManager.trainData.hotProgressValue;
    [self.stepView setHotProgressTime:_videoPlayer.currentTime hotValue:value];
}

/**
 *录播快结束前10s加提示
 */
- (void)_updateVODPlayerLast10SecondsTipIfNeeded:(NSTimeInterval)currentTime {
    if (_videoPlayer.playbackType != SJPlaybackTypeVOD) return;
    if ( self.hasToast10SecondsAlert) return;
    
    NSTimeInterval last10Seconds = _videoPlayer.duration - 5;
    if (last10Seconds <= 0){ return; }
    if (currentTime >= last10Seconds){
        self.hasToast10SecondsAlert = YES;
        [self videoTipString:@"课程已结束，即将退出"];
    }
}

- (void)_updateAppearStateForResidentBackButtonIfNeeded {
    if ( !_fixesBackItem )
        return;
    BOOL isFitOnScreen = _videoPlayer.isFitOnScreen;
    BOOL isFull = _videoPlayer.isFullscreen;
    BOOL isLockedScreen = _videoPlayer.isLockedScreen;
    if ( isLockedScreen ) {
        _fixedBackButton.hidden = YES;
    }
    else {
        BOOL isPlayOnScrollView = _videoPlayer.isPlayOnScrollView;
        _fixedBackButton.hidden = isPlayOnScrollView && !isFitOnScreen && !isFull;
    }
}

- (void)_updateNetworkSpeedStrForLoadingView {
    if ( !_videoPlayer || !self.loadingView.isAnimating )
        return;
    
    if ( self.loadingView.showsNetworkSpeed && ![_videoPlayer.URLAsset.mediaURL isFileURL] ) {
        self.loadingView.networkSpeedStr = [NSAttributedString sj_UIKitText:^(id<SJUIKitTextMakerProtocol>  _Nonnull make) {
            id<SJVideoPlayerControlLayerResources> resources = SJVideoPlayerConfigurations.shared.resources;
            make.font(resources.loadingNetworkSpeedTextFont);
            make.textColor(resources.loadingNetworkSpeedTextColor);
            make.alignment(NSTextAlignmentCenter);
            make.append(self.videoPlayer.reachability.networkSpeedStr);
        }];
    } else {
        self.loadingView.networkSpeedStr = nil;
    }
}

- (void)_reloadCustomStatusBarIfNeeded NS_AVAILABLE_IOS(11.0) {
    if ( sj_view_isDisappeared(_customStatusBar) )
        return;
    _customStatusBar.networkStatus = _videoPlayer.reachability.networkStatus;
    _customStatusBar.date = NSDate.date;
    _customStatusBar.batteryState = [UIDevice currentDevice].batteryState;
    _customStatusBar.batteryLevel = [UIDevice currentDevice].batteryLevel;
}

#pragma mark  ---------------- Progres---------------

- (nullable NSAttributedString *)_textForTimeString:(NSString *)timeStr {
    id<SJVideoPlayerControlLayerResources> resources = SJVideoPlayerConfigurations.shared.resources;
    NSAttributedString *string = [NSAttributedString sj_UIKitText:^(id<SJUIKitTextMakerProtocol>  _Nonnull make) {
        make.append(timeStr).font(resources.timeLabelFont).textColor(resources.timeLabelColor).alignment(NSTextAlignmentCenter);
    }];
    return string;
}

/// 此处为重置控制层的隐藏间隔.(如果点击到当前控制层上的item, 则重置控制层的隐藏间隔)
- (void)_resetControlLayerAppearIntervalForItemIfNeeded:(NSNotification *)note {
    SJEdgeControlButtonItem *item = note.object;
    if ( item.resetsAppearIntervalWhenPerformingItemAction ) {
        if ( [_topAdapter containsItem:item] ||
            [_leftAdapter containsItem:item] ||
            [_bottomAdapter containsItem:item] ||
            [_rightAdapter containsItem:item])
        {
            [_videoPlayer controlLayerNeedAppear];
        }
    }
}

- (void)_showOrRemoveBottomProgressIndicator {
    if ( _hiddenBottomProgressIndicator || _videoPlayer.playbackType == SJPlaybackTypeLIVE ) {
        if ( _bottomProgressIndicator ) {
            [_bottomProgressIndicator removeFromSuperview];
            _bottomProgressIndicator = nil;
        }
    } else {
        if ( !_bottomProgressIndicator ) {
            [self.controlView addSubview:self.bottomProgressIndicator];
            [self _updateLayoutForBottomProgressIndicator];
        }
    }
}

- (void)_updateLayoutForBottomProgressIndicator {
    if ( _bottomProgressIndicator == nil ) return;
    _bottomProgressIndicator.trackHeight = _bottomProgressIndicatorHeight;
    if (_screen.is_iPhoneX) {
        _bottomProgressIndicator.frame = (CGRect){kScreenPadding+20, self.bounds.size.height - _bottomProgressIndicatorHeight, self.bounds.size.width - kScreenPadding*2 - 20*2, _bottomProgressIndicatorHeight};
    } else {
        _bottomProgressIndicator.frame = (CGRect){0, self.bounds.size.height - _bottomProgressIndicatorHeight, self.bounds.size.width, _bottomProgressIndicatorHeight};
    }
}

- (void)_showOrHiddenLoadingView {
    if ( _videoPlayer == nil || _videoPlayer.URLAsset == nil ) {
        [self.loadingView stop];
        return;
    }
    
    if ( _videoPlayer.isPaused ) {
        [self.loadingView stop];
    }
    else if ( _videoPlayer.assetStatus == SJAssetStatusPreparing ) {
        [self.loadingView start];
    }
    else if ( _videoPlayer.assetStatus == SJAssetStatusFailed ) {
        [self.loadingView stop];
    }
    else if ( _videoPlayer.assetStatus == SJAssetStatusReadyToPlay ) {
        self.videoPlayer.reasonForWaitingToPlay == SJWaitingToMinimizeStallsReason ? [self.loadingView start] : [self.loadingView stop];
    }
}












#pragma mark  ---------------- 课程指令设置 ---------------
///接收到指令信息
- (void)sendInstructionsWithData:(CourseLinkModel *)model{
    if (!model) return;
    
    /// 判断是Merit课程 和 是电磁控设备 才发指令
    if (_videoPlayer.videoPlayData.isMeritControl) {
        [self instructionsOperationData:model];
    }
}

///指令操作
- (void)instructionsOperationData:(CourseLinkModel *)model{
    ///教案关闭
    if (!self.videoPlayer.isUseLessonPlanControl) { return; }
    
    NSString *typeId = _videoPlayer.videoPlayData.equipmentId;
    EquipmentDetialModel *eqModel = _videoPlayer.videoPlayData.eqModel;
    
    ///跑步机
    if (typeId.intValue == TreadmillEquipment) {
        ///跑步机是否有坡度下发
        BOOL treadmillHasSlopeControl = _videoPlayer.videoPlayData.planModel.treadmillHasSlopeControl;
        
        ///跑步机是否需要下发指令
        if (![_videoPlayer.trainManager isNeedInstructionWithModel:model]) {
            return;
        }
        
        NSDictionary *orderParms = nil;                                                 ///指令数据
        NSMutableString *tipStr = [[NSMutableString alloc] init];                       ///提示字段
        
        NSNumber *speedOrderNum = model.minNum;
        NSNumber *slopeOrderNum = model.adviseNum;
        double speed = 0.0;
        if (speedOrderNum.doubleValue > 0) {
            speed = speedOrderNum.doubleValue/10;
            if (speed > eqModel.maxSpeed.intValue && eqModel.maxSpeed.intValue != 0) {
                speed = eqModel.maxSpeed.intValue;
            }
        }
        NSString *speedTipStr =[NSString stringWithFormat:@"%.1f", speed];
        /// 跑步机调节速度
        SJEdgeControlButtonItem *speeditem = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_Speed];
        if (speeditem != nil) {
            speedOrderNum = @(speed*10);
            
            ///速度Item加提示
            TrainDataItemView *speedItemView = speeditem.partView;
            [speedItemView reloadNumTip:@(speed)];
            
            [tipStr appendString:CombineString(@"速度将调至", speedTipStr)];
        }
        
        /// 跑步机是否可调节坡度
        /// @note 有负坡度显示
        SJEdgeControlButtonItem *slopeitem = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_Slope];
        if (slopeitem != nil && eqModel.showSlope.boolValue && treadmillHasSlopeControl) {
            int slopeNum = model.adviseNum.intValue;
            if (slopeNum > eqModel.maxSlope.intValue) {
                slopeNum = eqModel.maxSlope.intValue;
            }
            if (slopeNum < eqModel.minSlope.intValue) {
                slopeNum = eqModel.minSlope.intValue;
            }
          
            slopeOrderNum = @(slopeNum);
            ///坡度Item加提示
            TrainDataItemView *slopeItemView = slopeitem.partView;
            [slopeItemView reloadNumTip:@(slopeNum)];
            
            NSString *slopeStr = [NSString stringWithFormat:@"%d", slopeNum];
            NSString *slopeTipStr = [NSString stringWithFormat:@"%@坡度将调至", tipStr.length > 0 ? @"，" : @""];
            [tipStr appendString:CombineString(slopeTipStr, slopeStr)];
        }
        
        if ([tipStr isEmpty]) return;
        ///
        orderParms = @{
            Speed : speedOrderNum ?:@0,
            Slope : slopeOrderNum ?:@0,
            BlueDeviceType : typeId,
            @"status" : @(_videoPlayer.trainManager.treamillStatus)
        };
        [self setOrderNotificationData:orderParms
                             andTipStr:tipStr
                        andControlType:@"3"
                      andControlNumber:speedTipStr];
    }
    
    ///划船机,单车,力量站 椭圆机 可支持坡度调节
    if(typeId.intValue == BoatEquipment ||
       typeId.intValue == BicycleEquipment ||
       typeId.intValue == PowerEquipment ||
       typeId.intValue == EllipticalEquipment) {
        
        SJEdgeControlButtonItem *slopeitem = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_Slope];
        SJEdgeControlButtonItem *item = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_Resistance]; ///
   
        ///没有坡度，阻力为0
        if (slopeitem == nil && model.adviseNum.intValue == 0) {
            return;
        }
        
        @weakify(self);
        [_videoPlayer.videoPlayData mappingResistance:model completion:^(CourseLinkModel * _Nonnull mod) {
            @strongify(self);
            NSMutableDictionary *orderParms = [NSMutableDictionary dictionaryWithDictionary:@{
                BlueDeviceType:typeId
            }];
            
            NSMutableString *tipStr = [[NSMutableString alloc] init];    ///提示字段
            ///阻力Item加提示
            if (item != nil && model.adviseNum.intValue > 0) {
                [orderParms setObject:mod.adviseNum?:@0 forKey:Resistance];
                
                TrainDataItemView *partView = item.partView;
                [partView reloadNumTip:mod.adviseNum];
                
                [tipStr appendString:CombineString(@"阻力将调至", mod.adviseNum.stringValue)];
            }
            
            ///坡度Item加提示
            if (slopeitem != nil && eqModel.showSlope.boolValue && mod.isChangeSlope) {
                [orderParms setObject:mod.slopeNum?:@0 forKey:Slope];
                
                TrainDataItemView *slopePartView = slopeitem.partView;
                [slopePartView reloadNumTip:mod.slopeNum];
                
                NSString *firTipStr = [NSString stringWithFormat:@"%@坡度将调至", model.adviseNum.intValue > 0 ? @"，" : @"" ];
                [tipStr appendString:CombineString(firTipStr, mod.slopeNum.stringValue)];
            }
            
            if ([tipStr isNotBlank]) {
                [self setOrderNotificationData:orderParms
                                     andTipStr:tipStr
                                andControlType:model.adviseNum.intValue > 0 ? @"1" : @"2"
                              andControlNumber:[NSString stringWithFormat:@"%@", model.adviseNum.intValue > 0 ? model.adviseNum : mod.slopeNum]];
            }
        }];
      
    }
}

NS_INLINE NSString *CombineString(NSString *str1, NSString *str2) {
    return [NSString stringWithFormat:@"%@%@", str1?:@"", str2?:@""];
}

///设备指令通知
- (void)setOrderNotificationData:(NSDictionary *)data
                       andTipStr:(NSString *)tip
                  andControlType:(NSString *)controlType
                andControlNumber:(NSString *)controlNumber{
    
    if (!self.hasToastAlert) {
        self.hasToastAlert = YES;
        
        @weakify(self);
        void(^sendNotificationBlock)(void) = ^{
            @strongify(self);
            if (!self) return;
            self.hasToastAlert = NO;
            ///教案是否开启
            if (self.videoPlayer.isUseLessonPlanControl){
                [[NSNotificationCenter defaultCenter] postNotificationName:SetResistanceSlopeSpeedNotification object:data];
            }
            if (self.delegate && [self.delegate respondsToSelector:@selector(updateTeachPlanOperation:)]) {
                [self.delegate updateTeachPlanOperation:NO];
            }
            self.videoPlayer.trainManager.isSendControl = NO;
        };
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(updateTeachPlanOperation:)]) {
            [self.delegate updateTeachPlanOperation:YES];
        }
        
        if ([[UIApplication sharedApplication] applicationState] == UIApplicationStateBackground &&
            self.videoPlayer.courseModel.equipmentId.intValue != TreadmillEquipment &&
            tip.length > 0) {
            /// 后台且不是跑步机
            /// 播放语音
            [[SpeechSynthesizerManager shared] speak:tip];
            
            self.videoPlayer.trainManager.controlType = controlType;
            self.videoPlayer.trainManager.controlNumber = controlNumber;
            self.videoPlayer.trainManager.isSendControl = YES;
        }
        
        [MBProgressHUD showMiaToast:tip toView:self afterDelay:5.0];
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(5.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            sendNotificationBlock();
        });
        MLog(@"_setOrderNotificationData下发指令 ==== %@", data);
    }
}

/**
 刷新建议提示, 不判断设备
 */
- (void)refreshNoteTips{
    /// 判断设备有无连接设备
    if (!_videoPlayer.videoPlayData.isConnectMachine){
        return;
    }
    
    /// 当前无小节model
    if (!self.currentlinkModel) {
        return;
    }
    
    /// isMeritControl && 开启AI调控
    if (_videoPlayer.videoPlayData.isMeritControl && self.videoPlayer.isUseLessonPlanControl) {
        return;
    }
    
    /**
     @note 考虑到会有[速度,坡度] [阻力, 坡度]同时存在的情况
     # minNum;           速度 [麦瑞克跑步机均为Merit设备, 暂不考虑]
     # adviseNum;        阻力
     # slopeNum;         坡度
     */
    CourseLinkModel *model = self.currentlinkModel;
    ///
    NSString *typeId = _videoPlayer.videoPlayData.equipmentId;
    EquipmentDetialModel *eqModel = _videoPlayer.videoPlayData.eqModel;
    
    ///跑步机
    if (typeId.intValue == TreadmillEquipment) {
        NSMutableString *tipStr = [[NSMutableString alloc] init];///提示字段
        
        /// 速度
        NSNumber *speedOrderNum = model.minNum;
        double speed = 0.0;
        if (speedOrderNum.doubleValue > 0) {
            speed =speedOrderNum.doubleValue/10;
            if (speed > eqModel.maxSpeed.intValue && eqModel.maxSpeed.intValue != 0) {
                speed = eqModel.maxSpeed.intValue;
            }
        }
        if (speed > 0) {
            NSString *speedTipStr =[NSString stringWithFormat:@"%.1f", speed];
            [tipStr appendString:CombineString(@"建议速度", speedTipStr)];
        }
        
        /// 坡度
        int slopeNum = model.adviseNum.intValue;
        if (slopeNum > eqModel.maxSlope.intValue) {
            slopeNum = eqModel.maxSlope.intValue;
        }
        if (slopeNum < eqModel.minSlope.intValue) {
            slopeNum = eqModel.minSlope.intValue;
        }
        if (slopeNum > 0) {
            NSString *slopeStr = [NSString stringWithFormat:@"%d", slopeNum];
            NSString *slopeTipStr = [NSString stringWithFormat:@"%@坡度", tipStr.length > 0 ? @"，" : @"建议"];
            [tipStr appendString:CombineString(slopeTipStr, slopeStr)];
        }
      
        if ([tipStr isEmpty]) return;
        [MBProgressHUD showMiaToast:tipStr toView:self];
        return;
    }
    
    if (typeId.intValue == BoatEquipment ||
       typeId.intValue == BicycleEquipment ||
       typeId.intValue == PowerEquipment ||
       typeId.intValue == EllipticalEquipment) {

        @weakify(self);
        [_videoPlayer.videoPlayData mappingResistance:model completion:^(CourseLinkModel * _Nonnull mod) {
            @strongify(self);
            NSMutableString *tipStr = [[NSMutableString alloc] init];///提示字段
            ///
            ///[阻力]
            if (mod.adviseNum.intValue > 0){
                [tipStr appendString:[NSString stringWithFormat:@"建议阻力%@", mod.adviseNum]];
            }
            
            ///[坡度]
            if (mod.slopeNum.intValue > 0) {
                NSString *slopeStr = [NSString stringWithFormat:@"%@", mod.slopeNum];
                NSString *slopeTipStr = [NSString stringWithFormat:@"%@坡度", tipStr.length > 0 ? @"，" : @"建议"];
                [tipStr appendString:CombineString(slopeTipStr, slopeStr)];
            }
            
            if ([tipStr isEmpty]) return;
            [MBProgressHUD showMiaToast:tipStr toView:self];
        }];
    }
}

#pragma mark  ---------------- 引导 ---------------

- (void)addUserGuide:(void(^)(void))completion{
    id<SJVideoPlayerControlLayerResources> resources = SJVideoPlayerConfigurations.shared.resources;
    ///都引导过就退出
    if (resources.isTipRestance && resources.isTipModel) {
        if (completion) {
            completion();
        }
        return;
    }
    
    MRKVideoGuideView *guidV = self.guideView;
    // auto item
    SJEdgeControlButtonItem *autoItem = [self.topAdapter itemForTag:SJEdgeControlLayerTopItem_Auto];
    if (autoItem != nil && !autoItem.hidden && !resources.isTipRestance) {
        resources.isTipRestance = YES;
        
        GuideItemView *item = [guidV guideView:autoItem];
        item.useDismissTimers = YES;
        [guidV.guideControl.itemsArray addObject:item];
    }
    
    // model item
    SJEdgeControlButtonItem *modelItem = [self.topAdapter itemForTag:SJEdgeControlLayerTopItem_Model];
    if (modelItem != nil && !modelItem.hidden && !resources.isTipModel) {
        resources.isTipModel = YES;
        
        GuideItemView *item = [guidV guideView:modelItem];
        item.useDismissTimers = YES;
        [guidV.guideControl.itemsArray addObject:item];
    }
    
    ///如果没引导的话 return
    if (guidV.guideControl.itemsArray.count == 0) {
        if (completion) {
            completion();
        }
        return;
    }
    
    ///video
    [self.videoPlayer.controlLayerAppearManager needAppear];
    [self.videoPlayer.controlLayerAppearManager keepAppearState];
    
    [guidV.guideControl start];
    @weakify(self);
    guidV.guideControl.finishBlock = ^(id _Nonnull sender) {
        @strongify(self);
        [self.videoPlayer.controlLayerAppearManager resume];
        
        if (completion) {
            completion();
        }
    };
}

- (void)fireGuide {
    id<SJVideoPlayerControlLayerResources> resources = SJVideoPlayerConfigurations.shared.resources;
    if (resources.isTipFlame) {
        return;
    }
    
    resources.isTipFlame = YES;
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.4 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        UIView *pointV = [self.stepView.fireView viewWithTag:100];
        PopTitleView *ptv = [MRKVideoGuideView fireGuideView:pointV target:self];
        [ptv showOnView:self];
        [ptv dismissAfter:5.0];
    });
}

- (void)meritRateGuide {
    id<SJVideoPlayerControlLayerResources> resources = SJVideoPlayerConfigurations.shared.resources;
    if (resources.isTipHeartRate) {
        return;
    }
    
    resources.isTipHeartRate = YES;
    UIView *pointV = self.burnView;
    PopTitleView *ptv = [MRKVideoGuideView meritRateGuideView:pointV target:self];
    [ptv showOnView:self];
    [ptv dismissAfter:5.0];
}

- (void)smartOperateGuide {
    id<SJVideoPlayerControlLayerResources> resources = SJVideoPlayerConfigurations.shared.resources;
    if (resources.isTipModel) {
        return;
    }
    
    resources.isTipModel = YES;
    // auto item
    SJEdgeControlButtonItem *autoItem = [self.topAdapter itemForTag:SJEdgeControlLayerTopItem_Auto];
    if (autoItem != nil && !autoItem.hidden) {
        UIView *pointV = autoItem.backView;
        PopTitleView *ptv = [MRKVideoGuideView smartGuideView:pointV target:self];
        [ptv showOnView:self];
        [ptv dismissAfter:5.0];
    }
}

- (MRKVideoGuideView *)guideView {
    if(!_guideView) {
        _guideView = [[MRKVideoGuideView alloc] initWithTarget:[UIViewController keywindow]];
        _guideView.delegate = self;
    }
    return _guideView;
}

@end


@implementation SJEdgeControlButtonItem (SJControlLayerExtended)
- (void)setResetsAppearIntervalWhenPerformingItemAction:(BOOL)resetsAppearIntervalWhenPerformingItemAction {
    objc_setAssociatedObject(self, @selector(resetsAppearIntervalWhenPerformingItemAction), @(resetsAppearIntervalWhenPerformingItemAction), OBJC_ASSOCIATION_RETAIN_NONATOMIC);
}
- (BOOL)resetsAppearIntervalWhenPerformingItemAction {
    id result = objc_getAssociatedObject(self, _cmd);
    return result == nil ? YES : [result boolValue];
}
@end






