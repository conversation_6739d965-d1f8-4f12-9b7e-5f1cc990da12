//
//  MRKVideoBottomDataView.m
//  Student_IOS
//
//  Created by merit on 2023/3/2.
//

#import "MRKVideoBottomDataView.h"
#import "Masonry.h"
#import "NSAttributedString+SJMake.h"
#import "SJVideoPlayerControlMaskView.h"
#import "MRKVideoPrepareDataInfo.h"
#import "SJItemTags.h"


NS_ASSUME_NONNULL_BEGIN

@interface MRKVideoBottomDataView ()
@property (nonatomic, readonly) BOOL isFitOnScreen;
@end

@implementation MRKVideoBottomDataView {
    CGRect _beforeBounds;
}

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if ( !self ) return nil;
    CGFloat screenW = UIScreen.mainScreen.bounds.size.width;
    CGFloat screenH = UIScreen.mainScreen.bounds.size.height;
    CGFloat max = MAX(screenW, screenH);
    CGFloat min = MIN(screenW, screenH);
    
    BOOL is_iPhoneX = (((float)((int)(min / max * 100))) / 100) == (((float)((int)(1125.0 / 2436 * 100))) / 100);
    _screen = (struct MRK_Screen){max, min, is_iPhoneX};
    
    _bottomDataHeight = 75;
    _bottomMargin = 0;
    _dataMargin = 0;
    
    self.autoAdjustLayoutWhenDeviceIsiPhoneX = YES;
    return self;
}

- (void)dealloc {
    
}
- (BOOL)isFitOnScreen {
    return (_screen.min == self.bounds.size.width) && (_screen.max == self.bounds.size.height);
}

- (void)layoutSubviews {
    [super layoutSubviews];
    [self _updateLayoutIfNeeded];
}

- (void)_updateLayoutIfNeeded {
    CGRect curr = self.bounds;
    if ( _screen.is_iPhoneX && _autoAdjustLayoutWhenDeviceIsiPhoneX )
    {
        if ( !CGRectEqualToRect(_beforeBounds, curr) ) {
            CGFloat viewW = curr.size.width;
            CGFloat viewH = curr.size.height;
            
            BOOL isFullscreen = (viewW == _screen.max) && (viewH == _screen.min);
            if ( isFullscreen ) {
                [self _updateLayout_isFullScreen_iPhone_X];
            }  else {
                [self _updateLayout_isNormal_iPhone_X];
            }
        }
    } else if ( !CGRectEqualToRect(_beforeBounds, curr) ) {
        
    }
    _beforeBounds = curr;
}

///竖屏
- (void)_updateLayout_isNormal_iPhone_X {
    if (@available(iOS 11.0, *)) {
        [_dataAdapter.view mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.top.offset(0);
            make.left.equalTo(self.mas_safeAreaLayoutGuideLeft);
            make.right.equalTo(self.mas_safeAreaLayoutGuideRight);
            make.bottom.equalTo(self.mas_safeAreaLayoutGuideBottom).offset(-self.bottomMargin);
            make.height.offset(self.bottomDataHeight);
        }];
    }
}

///横屏
- (void)_updateLayout_isFullScreen_iPhone_X {
    if (@available(iOS 11.0, *)) {
        CGFloat safeWidth = ceil(_screen.max - kScreenPadding *2);
        [_dataAdapter.view mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.top.offset(0);
            make.bottom.offset(-(self.dataMargin + 20));
            
            make.left.mas_greaterThanOrEqualTo(0).priorityLow();
            make.right.mas_lessThanOrEqualTo(0).priorityLow();
            make.centerX.offset(0);
            
            make.width.offset(safeWidth);
            make.height.offset(self.bottomDataHeight);
        }];
    }
}











//- (SJVideoPlayerControlMaskView *)bottomDataView {
//    if ( _bottomDataView ) return _bottomDataView;
//    _bottomDataView = [[SJVideoPlayerControlMaskView alloc] initWithStyle:SJMaskStyle_bottom];
//    _bottomDataView.colorLocations = @[@0.3, @0.7, @1.0];
//    [self addSubview:_bottomDataView];
//    [_bottomDataView mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.bottom.offset(0);
//        make.left.right.offset(0);
////        make.height.offset(self.bottomDataHeight);
//    }];
//
//    return _bottomDataView;
//}


- (SJEdgeControlButtonItemAdapter *)dataAdapter {
    if ( _dataAdapter ) return _dataAdapter;
    _dataAdapter = [[SJEdgeControlButtonItemAdapter alloc] initWithFrame:CGRectZero layoutType:SJAdapterLayoutTypeHorizontalCenterLayout];
    [self addSubview:_dataAdapter.view];
    [_dataAdapter.view mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.offset(0);
        if (@available(iOS 11.0, *)) {
            make.left.equalTo(self.mas_safeAreaLayoutGuideLeft);
            make.right.equalTo(self.mas_safeAreaLayoutGuideRight);
            make.bottom.equalTo(self.mas_safeAreaLayoutGuideBottom).offset(-self.dataMargin);
        } else {
            make.left.right.offset(0);
            make.bottom.offset(-self.dataMargin);
        }
        make.height.offset(self.bottomDataHeight);
    }];
    return _dataAdapter;
}


#pragma mark -

- (void)setBottomDataHeight:(CGFloat)bottomDataHeight{
    _bottomDataHeight = bottomDataHeight;
    
    [_dataAdapter.view mas_updateConstraints:^(MASConstraintMaker *make) {
        make.height.offset(bottomDataHeight);
    }];
}

#pragma mark -

- (void)setBottomMargin:(CGFloat)bottomMargin {
    _bottomMargin = bottomMargin;
    [_dataAdapter.view mas_updateConstraints:^(MASConstraintMaker *make) {
        if (@available(iOS 11.0, *)) {
            make.bottom.equalTo(self.mas_safeAreaLayoutGuideBottom).offset(-bottomMargin);
        } else {
            make.bottom.offset(-self.bottomMargin).offset(-bottomMargin);
        }
    }];
}




- (TrainDataGaugeModel *)TrainDataGaugeModel:(MRKVideoPrepareDataInfo *)dataInfo{
    TrainDataGaugeModel *model = [[TrainDataGaugeModel alloc] init];
    model.equipmentType = dataInfo.equipmentId.intValue;
    model.planMinDataValue = dataInfo.planModel.minNum;
    model.planMaxDataValue = dataInfo.planModel.maxNum;
    model.minDataValue = @0;
    model.maxDataValue = ({
        int equipmentId = dataInfo.equipmentId.intValue;
        NSInteger maxValue = 0; ///默认为0
        if (equipmentId == BoatEquipment) { maxValue = 60; }
        if (equipmentId == EllipticalEquipment) { maxValue = 150; }
        if (equipmentId == BicycleEquipment) { maxValue = 160; }
        if (equipmentId == StairClimbEquiment) { maxValue = 110; }
        @(maxValue);
    });
    return model;
}

/**
 更新数据栏是否显示指针模块和是否显示字段可控变色
 */
- (void)_reloadDataAdapterIfNeeded:(MRKVideoPrepareDataInfo *)dataInfo{
    int equipmentId = dataInfo.equipmentId.intValue;
    switch (equipmentId) {
        case BoatEquipment:
        case BicycleEquipment:
        case EllipticalEquipment: {
            
            // 阻力 item
            SJEdgeControlButtonItem *resistanceItem = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_Resistance];
            if ( resistanceItem != nil) {
                if (dataInfo.isShowAutoControl) {
                    resistanceItem.showControl = self.useLessonPlanControl;
                }else{
                    resistanceItem.showControl = NO;
                }
            }
            
            // 踏频(rpm) item
            SJEdgeControlButtonItem *treadItem = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_RateOfTread];
            if ( treadItem != nil && dataInfo.playbackType != SJPlaybackTypeUnknown) {
                if (treadItem.partView != nil) {
                    TrainDataItemView *partView = treadItem.partView;
                    partView.showPointer = YES;
                    partView.dialModel = [self TrainDataGaugeModel:dataInfo];
                }
            }
            
            // 桨频(spm) item
            SJEdgeControlButtonItem *strokeItem = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_RateOfStroke];
            if ( strokeItem != nil && dataInfo.playbackType != SJPlaybackTypeUnknown) {
                if (strokeItem.partView != nil) {
                    TrainDataItemView *partView = strokeItem.partView;
                    partView.showPointer = YES;
                    partView.dialModel = [self TrainDataGaugeModel:dataInfo];
                }
            }
            
        }break;
        case TreadmillEquipment: {
            // 速度 item
            SJEdgeControlButtonItem *speedItem = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_Speed];
            if ( speedItem != nil) {
                if (dataInfo.isShowAutoControl) {
                    speedItem.showControl = self.useLessonPlanControl;
                } else {
                    speedItem.showControl = NO;
                }
            }
            
            // 坡度 item
            SJEdgeControlButtonItem *slopeItem = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_Slope];
            if ( slopeItem != nil) {
                if (dataInfo.isShowAutoControl) {
                    slopeItem.showControl = self.useLessonPlanControl;
                }else{
                    slopeItem.showControl = NO;
                }
            }
            
        }break;
            
        case StairClimbEquiment: {
            
            // 踏频(rpm) item
            SJEdgeControlButtonItem *treadItem = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_RateOfTread];
            if ( treadItem != nil && dataInfo.playbackType != SJPlaybackTypeUnknown) {
                if (treadItem.partView != nil) {
                    TrainDataItemView *partView = treadItem.partView;
                    partView.showPointer = YES;
                    partView.dialModel = [self TrainDataGaugeModel:dataInfo];
                }
            }
            
        } break;
        default: break;
    }
    
    [self.dataAdapter reload];
}


/**
 刷新数据栏显示Item
 */
- (void)_addItemsBottomDataAdapter:(MRKVideoPrepareDataInfo *)dataInfo{
    int equipmentId = dataInfo.equipmentId.intValue;
    for (MRKEqDisplayModel *model in dataInfo.deviceDisplayArr) {
        SJEdgeControlButtonItemTag tag = model.displayId.integerValue + 50000;
        
        BOOL showPointer = NO;
        ///判断划船机 && 桨频
        if (equipmentId == BoatEquipment && tag == SJEdgeControlLayerCenterItem_RateOfStroke){
            showPointer = YES;
        }
        ///判断单车或者椭圆机 && 踏频
        if ( (equipmentId == BicycleEquipment || equipmentId == EllipticalEquipment) && tag == SJEdgeControlLayerCenterItem_RateOfTread){
            showPointer = YES;
        }
        
        SJEdgeControlButtonItem *item = [SJEdgeControlButtonItem placeholderWithSize:showPointer? 150 + 40 : DHPX(90) tag:tag];
        item.dataStr = model.defaultValue;
        item.holdStr = model.name;
        [self.dataAdapter addItem:item];
    }
    
    [self.dataAdapter reload];
}


///刷新不支持阻力回显的设备. 阻力模块剔除
- (void)_reloadItemsBottomDataAdapter:(MRKVideoPrepareDataInfo *)dataInfo{
    MRKVideoPrepareDataInfo *videoPlayData = dataInfo;
    int equipmentType = videoPlayData.equipmentId.intValue;
    if (equipmentType == BicycleEquipment ||
        equipmentType == BoatEquipment ||
        equipmentType == EllipticalEquipment ) {
        
        if (videoPlayData.eqModel == nil ){
            return;
        }
        
        ///不支持阻力回显
        if (!videoPlayData.eqModel.isSupportResistanceEcho ){
            [self.dataAdapter removeItemForTag:SJEdgeControlLayerCenterItem_Resistance];
            [self.dataAdapter reload];
        }
    }
}



- (void)dataAdapterUpdateWithModel:(BaseEquipDataModel *)model{
    dispatch_async(dispatch_get_main_queue(), ^{
        BaseEquipDataModel *m = model;
        for (SJEdgeControlButtonItem *item in self.dataAdapter.items) {
            NSInteger tag = (NSInteger)item.tag;
            switch (tag) {
                case 50001: {///运动时间
                    NSString *currentTimeStr = [MRKToolKit MSTimeStrFromSecond:m.totalTimeSecond.intValue];
                    item.dataStr = currentTimeStr;
                }break;
                case 50002: {///消耗(kcal)
                    item.dataStr = m.energy ? [NSString convertDecimalNumber: [NSString stringWithFormat:@"%@", m.energy] num:1] : @"--";
                    // item.dataStr = m.energy ? [NSString stringWithFormat:@"%.1f", m.energy.floatValue] : @"--";
                }break;
                case 50003: {///心率(bmp)
                    item.dataStr = m.rate ? (m.rate.intValue == 0 ? @"--" :  m.rate.stringValue) : @"--";
                }break;
                case 50004: case 50008:{///踏频(rpm)/桨频(spm)
                    item.dataStr = m.spm ? (m.spm.intValue == 0 ? @"--" :  m.spm.stringValue) : @"--";
                }break;
                case 50005: {///阻力(lv)
                    item.dataStr = m.drag ? (m.drag.intValue == 0 ? @"--" :  m.drag.stringValue) : @"--";
                }break;
                case 50006: {///坡度
                    item.dataStr = m.gradient ? (m.gradient.intValue == 0 ? @"--" : m.gradient.stringValue) : @"--";
                }break;
                case 50007: {///距离(km)
                    item.dataStr = [NSString convertDecimalNumber: [NSString stringWithFormat:@"%@", m.totalDistance] num:2 dividing: @"1000"];
                    //  item.dataStr = [NSString stringWithFormat: @"%.2f" , m.totalDistance.intValue/1000.0];
                }break;
                case 50009:  case 50010: {///个数/圈数
                    item.dataStr =  m.count ? (m.count.intValue == 0 ? @"0" :  m.count.stringValue) : @"--";
                }break;
                case 50011: {///挡位
                    item.dataStr = m.grade.stringValue;
                }break;
                case 50012: { ///速度(km/h)
                    item.dataStr = [NSString stringWithFormat:@"%.1f" , m.speed.floatValue];
                }break;
                case 50013: { ///力量站次数
                    item.dataStr =  m.count ? (m.count.intValue == 0 ? @"0" :  m.count.stringValue) : @"--";
                }break;
                default: break;
            }
            [self.dataAdapter updateContentForItemWithTag:tag];
        }
    });
}



/*
 // Only override drawRect: if you perform custom drawing.
 // An empty implementation adversely affects performance during animation.
 - (void)drawRect:(CGRect)rect {
 // Drawing code
 }
 */


@end
NS_ASSUME_NONNULL_END
