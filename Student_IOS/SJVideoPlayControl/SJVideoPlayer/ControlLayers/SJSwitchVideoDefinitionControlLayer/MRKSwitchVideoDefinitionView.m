//
//  MRKVideoDefinitionView.m
//  Student_IOS
//
//  Created by merit on 2023/2/28.
//

#import "MRKSwitchVideoDefinitionView.h"
#import "NSAttributedString+SJMake.h"
#import "SJBaseVideoPlayer.h"
#import "UIView+SJAnimationAdded.h"
#import "UIView+AZGradient.h"
#import "SJEdgeControlButtonItem.h"
#import "SJEdgeControlButtonItemAdapter.h"

#import "UIView+HyExtension.h"
#import "POP.h"
#import "MRKTraceManager.h"
#import "BaseTableView.h"
#import "MRKEdgeLabel.h"
#import "MRKTagsView.h"
#import "UIView+AZGradient.h"




NS_ASSUME_NONNULL_BEGIN
@interface MRKSwitchVideoDefinitionView ()
@property (nonatomic, strong) UIView *blackView;
@property (nonatomic, strong) UIView *messageView;

@property (nonatomic, copy, nullable) NSArray<VideoListModel *> *items;
@property (nonatomic, strong) VideoDefinitionListView *listView;
@end

@implementation MRKSwitchVideoDefinitionView

- (void)show {
    if (self.contentView) {
        [self.contentView addSubview:self];
        
        self.isDisplayOnScreen = YES;
        [self createBlackView];
        [self createMessageView];
        
        if (self.autoHiddenDelay > 0) {
            [self performSelector:@selector(hide) withObject:nil afterDelay:self.autoHiddenDelay];
        }
    }
}

- (void)hide {
    [super hide];
    
    if (self.contentView) {
        self.isDisplayOnScreen = NO;
        [self removeViews];
    }
}

- (void)createBlackView {
    self.blackView = [[UIView alloc] init];
    self.blackView.backgroundColor = [UIColor clearColor];
    [self addSubview:self.blackView];
    [self.blackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsMake(0, 0, 0, 0));
    }];
    
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] init];
    [self.blackView addGestureRecognizer:tap];
    @weakify(self);
    [tap.rac_gestureSignal subscribeNext:^(id x) {
        [self_weak_ hide];
    }];
}

- (void)createMessageView {
    // 创建信息窗体view
    self.messageView = [[UIView alloc] init];
    self.messageView.frame = CGRectMake(MainWidth, 0, DHPX(260), MainHeight);
    self.messageView.backgroundColor = [UIColor clearColor];
    self.messageView.alpha = 0.f;
    [self addSubview:self.messageView];
    [self.messageView addSubview:({
        UIBlurEffect *blurEffect = [UIBlurEffect effectWithStyle:UIBlurEffectStyleDark];
        UIVisualEffectView *visualView = [[UIVisualEffectView alloc] initWithEffect:blurEffect];
        visualView.frame = CGRectMake(0, 0, DHPX(260), MainHeight);
        visualView;
    })];
    
  
    [self refreshSubItems];
    
    // 执行动画
    POPBasicAnimation *alpha = [POPBasicAnimation animationWithPropertyNamed:kPOPViewAlpha];
    alpha.toValue = @(1.f);
    alpha.duration = 0.3f;
    [self.messageView pop_addAnimation:alpha forKey:nil];
    
    CGRect fromValue = CGRectMake(MainWidth, 0, DHPX(260), MainHeight);
    CGRect toValue = CGRectMake(MainWidth -DHPX(260), 0, DHPX(260), MainHeight);
    POPBasicAnimation *frameAnimation = [POPBasicAnimation animationWithPropertyNamed:kPOPViewFrame];
    frameAnimation.duration = 0.3;
    frameAnimation.fromValue = [NSValue valueWithCGRect:fromValue];
    frameAnimation.toValue = [NSValue valueWithCGRect:toValue];
    frameAnimation.timingFunction = [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionEaseInEaseOut];
    @weakify(self);
    frameAnimation.completionBlock = ^(POPAnimation *anim, BOOL finished) {
        @strongify(self);
        [self.messageView.subviews enumerateObjectsUsingBlock:^(__kindof UIView *obj, NSUInteger idx, BOOL * _Nonnull stop) {
            if ([obj isKindOfClass:[UIButton class]]) {
                UIButton *btn = obj;
                btn.userInteractionEnabled = YES;
            }
        }];
    };
    [self.messageView pop_addAnimation:frameAnimation forKey:@"frameAnimation"];
}


- (void)removeViews {
    if (self.willDisappearBlock){
        self.willDisappearBlock(self);
    }
    
    POPBasicAnimation *blackViewAlpha = [POPBasicAnimation animationWithPropertyNamed:kPOPViewAlpha];
    blackViewAlpha.toValue = @(0.f);
    blackViewAlpha.duration = 0.3f;
    [self.blackView pop_addAnimation:blackViewAlpha forKey:nil];
    
    POPBasicAnimation *alpha = [POPBasicAnimation animationWithPropertyNamed:kPOPViewAlpha];
    alpha.toValue = @(0.f);
    alpha.duration = 0.3f;
    [self.messageView pop_addAnimation:alpha forKey:nil];
    
    CGRect fromValue = CGRectMake(MainWidth -DHPX(260), 0, DHPX(260), MainHeight);
    CGRect toValue = CGRectMake(MainWidth, 0, DHPX(260), MainHeight);
    POPBasicAnimation *frameAnimation = [POPBasicAnimation animationWithPropertyNamed:kPOPViewFrame];
    frameAnimation.duration = 0.3;
    frameAnimation.fromValue = [NSValue valueWithCGRect:fromValue];
    frameAnimation.toValue = [NSValue valueWithCGRect:toValue];
    frameAnimation.timingFunction = [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionEaseInEaseOut];
    @weakify(self);
    frameAnimation.completionBlock = ^(POPAnimation *anim, BOOL finished) {
        @strongify(self);
        [self removeFromSuperview];
        if (self.didDisappearBlock){
            self.didDisappearBlock(self);
        }
    };
    [self.messageView pop_addAnimation:frameAnimation forKey:@"frameAnimation"];
}

- (void)dealloc{
    NSLog(@"😊😊😊-----dealloc-----😊😊😊%@" , NSStringFromClass([self class]));
}


- (void)refreshSubItems {
    [self.listView setHiddenAnimated:NO];
    [self.messageView addSubview:self.listView];
    [self.listView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsMake(0, 0, 0, 0));
    }];
    
    [self.listView.tableView reloadData];
    [self _refreshItems];
    
    
    ///VIp权益提示页面
    [self.ensureView setHiddenAnimated:YES];
    [self.messageView addSubview:self.ensureView];
    [self.ensureView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsMake(0, 0, 0, 0));
    }];
}





/*
 // Only override drawRect: if you perform custom drawing.
 // An empty implementation adversely affects performance during animation.
 - (void)drawRect:(CGRect)rect {
 // Drawing code
 }
 */


- (void)_clickedVideoListModelItem:(VideoListModel *)model index:(NSInteger)index{
    if ( [self.delegate respondsToSelector:@selector(definitionView:didSelectAsset:)] ) {
        [self.delegate definitionView:self didSelectAsset:self.assets[index]];
    }
    
    ///统计
    NSString *control_result = [self traceControlResult: model.definitionTitle];
    ReportMrkLogParms(2, @"清晰度切换", @"page_play", @"btn_play_definition_cut", nil, 0, @{@"control_result":control_result?:@"", @"courseId": self.videoPlayer.courseModel.courseId?:@"", @"course_state": self.videoPlayer.tracePlaybackType});
}

///控件结果：原画（original）；超清（super ）；高清（high）；标清（standard）；流畅（fluency）
- (NSString *)traceControlResult:(NSString *)selectName {
    NSString *control_result = @"";
    if ([selectName containsString:@"原画"]){
        control_result = @"original";
    }
    if ([selectName containsString:@"超清"]){
        control_result = @"super";
    }
    if ([selectName containsString:@"高清"]){
        control_result = @"high";
    }
    if ([selectName containsString:@"标清"]){
        control_result = @"standard";
    }
    if ([selectName containsString:@"流畅"]){
        control_result = @"fluency";
    }
    return control_result;
}

#pragma mark -

- (UIColor *)selectedTextColor {
    if ( _selectedTextColor == nil )
        return [UIColor colorWithHexString:@"#32FBFF"];
    return _selectedTextColor;
}

- (void)setAssets:(nullable NSArray<SJVideoPlayerURLAsset *> *)assets {
    _assets = assets.copy;
    
    NSMutableArray<VideoListModel *> *m = [NSMutableArray new];
    [_assets enumerateObjectsUsingBlock:^(SJVideoPlayerURLAsset * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        [m addObject:obj.playDataModel];
    }];
    self.items = m;
    
    [self _refreshItems];
    self.listView.dataSourceArr = self.items.mutableCopy;
    [self.listView.tableView reloadData];
}

- (void)_refreshItems {
    SJVideoPlayerURLAsset *_Nullable cur = _videoPlayer.URLAsset;
    if ( cur == nil ) { return; }
    
    SJVideoDefinitionSwitchingInfo *info = _videoPlayer.definitionSwitchingInfo;
    SJVideoPlayerURLAsset *selected = cur;
    if ( info.switchingAsset != nil &&
        info.status != SJDefinitionSwitchStatusFailed ) {
        selected = info.switchingAsset;
    }
    
    NSLog(@"selected.playurl===========%@", selected.playUrl);
    __block NSInteger selectIndex = NSNotFound;
    [self.items enumerateObjectsUsingBlock:^(VideoListModel * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([selected.definition_lastName isEqualToString:obj.definition]) {
            selectIndex = idx;
            *stop = YES;
        }
    }];
    self.listView.selectIndex = selectIndex;
    NSLog(@"selectIndex===========%ld", selectIndex);
}

- (VideoDefinitionListView *)listView {
    if (!_listView) {
        _listView = [[VideoDefinitionListView alloc] init];
        _listView.hidden = YES;
        _listView.alpha = 0;
        @weakify(self);
        _listView.definitionSelectBlock = ^(VideoListModel * _Nonnull model, NSInteger index) {
            [self_weak_ _clickedVideoListModelItem:model index:index];
        };
        _listView.definitionTipOpenVipBlock = ^(VideoListModel * _Nonnull model) {
            @strongify(self);
            ///开通会员提示
            [self.listView setHiddenAnimated:YES];
            
            self.ensureView.tipStr = ({
                NSString *tipStr = @"";
                NSString *vip = @"趣练VIP";
                if (model.vipType.intValue == 30){
                    vip = @"绝影VIP";
                }
                tipStr = [NSString stringWithFormat:@"%@画质是%@权益\n开通即可开启体验", model.definitionTitle, vip];
                tipStr;
            });
            self.ensureView.ensureOperationBlock = ^(NSInteger tag) {
                @strongify(self);
                if (tag == 2001) { ///开通会员操作
                    NSString *control_result = [self traceControlResult: model.definitionTitle];
                    ReportMrkLogParms(2, @"清晰度切换-开通会员", @"page_play", @"btn_definition_cut_vip_open", nil, 0, @{@"control_result": control_result, @"courseId": self.videoPlayer.courseModel.courseId?:@"", @"course_state": self.videoPlayer.tracePlaybackType});
                    if (self.delegate && [self.delegate respondsToSelector:@selector(openCourseVIPView:withData:)] ) {
                        [self.delegate openCourseVIPView:self withData:nil];
                    }
                    [self hide];
                    return;
                }
                
                ///关闭弹窗
                [self.listView setHiddenAnimated:NO];
                [self.ensureView setHiddenAnimated:YES];
            };
            self.ensureView.averageDailyVue.hidden = !UserInfo.isNormalMember;
            [self.ensureView setHiddenAnimated:NO];
        };
    }
    return _listView;
}

- (VideoDefinitionEnsureView *)ensureView {
    if (!_ensureView) {
        _ensureView = [[VideoDefinitionEnsureView alloc] init];
        _ensureView.hidden = YES;
        _ensureView.alpha = 0;
    }
    return _ensureView;
}

@end









@interface VideoDefinitionListView() <UITableViewDelegate, UITableViewDataSource>
@property (nonatomic, strong) UILabel *titleLab;
@end

@implementation VideoDefinitionListView

- (UILabel *)titleLab {
    if (!_titleLab) {
        UILabel *label = [[UILabel alloc] init];
        label.textAlignment = NSTextAlignmentLeft;
        label.textColor = [[UIColor whiteColor] colorWithAlphaComponent:0.8];
        label.font = [UIFont systemFontOfSize:14];
        label.text = @"画质";
        _titleLab = label;
    }
    return _titleLab;
}

- (BaseTableView *)tableView{
    if (!_tableView){
        _tableView = [[BaseTableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
        _tableView.delegate = self;
        _tableView.dataSource = self;
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.backgroundColor = [UIColor clearColor];
//        _tableView.scrollEnabled = YES;
    }
    return _tableView;
}

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        
        [self addSubview:self.titleLab];
        [self.titleLab mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.mas_top).offset(DHPX(32));
            make.left.equalTo(self.mas_left).offset(20);
            make.right.equalTo(self.mas_right).offset(-20);
            make.height.mas_equalTo(DHPX(22));
        }];

        [self addSubview:self.tableView];
        [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.titleLab.mas_bottom).offset(0);
            make.left.equalTo(self.mas_left).offset(20);
            make.right.equalTo(self.mas_right).offset(-20);
            make.bottom.equalTo(self.mas_bottom).offset(0);
        }];
    }
    return self;
}

- (void)layoutSubviews {
    [super layoutSubviews];
}

- (void)setDataSourceArr:(NSMutableArray *)dataSourceArr {
    _dataSourceArr = dataSourceArr;
}

- (void)setSelectIndex:(NSInteger)selectIndex {
    _selectIndex = selectIndex;
    
    if (selectIndex != NSNotFound) {
        NSIndexPath *indexPath = [NSIndexPath indexPathForRow:selectIndex inSection:0];
        [self.tableView selectRowAtIndexPath:indexPath animated:YES scrollPosition:UITableViewScrollPositionNone];
        if ([self.tableView.delegate respondsToSelector:@selector(tableView:didSelectRowAtIndexPath:)]) {
            [self.tableView.delegate tableView:self.tableView didSelectRowAtIndexPath:indexPath];
        }
    }
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section{
    return self.dataSourceArr.count;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    return WKDHPX(45+8);
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    static NSString * identifier = @"showIdentifier";
    VideoDefinitionListCell *cell = [tableView dequeueReusableCellWithIdentifier:identifier];
    if (!cell) {
        cell = [[VideoDefinitionListCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:identifier];
        cell.selectionStyle = UITableViewCellSelectionStyleNone;
        cell.accessoryType = UITableViewCellAccessoryNone;
        cell.backgroundColor = [UIColor clearColor];
    }
    VideoListModel *model = [self.dataSourceArr objectAtIndex:indexPath.row];
    cell.model = model;
    return cell;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    VideoListModel *model = [self.dataSourceArr objectAtIndex:indexPath.row];
//    ///过滤选中cell的重复点击
//    NSIndexPath *selectIndexPath = [tableView indexPathForSelectedRow];
//    if ([indexPath compare:selectIndexPath] == NSOrderedSame) {
//        return;
//    }
    
    if (indexPath.row == self.selectIndex) {
        return;
    }
    
    BOOL vipType = (UserInfo.vipType >= model.vipType.intValue) && UserInfo.isMember;
    ///不满足观看条件
    if (model.showVipIcon && !vipType){
        ///取消本地点击选中
        [tableView deselectRowAtIndexPath:indexPath animated:YES];
        
        ///选中之前的
        NSIndexPath *ins = [NSIndexPath indexPathForRow:self.selectIndex inSection:0];
        [self.tableView selectRowAtIndexPath:ins animated:YES scrollPosition:UITableViewScrollPositionNone];
        
        if (self.definitionTipOpenVipBlock){
            self.definitionTipOpenVipBlock(model);
        }
        return;
    }
    
    if (self.definitionSelectBlock){
        self.definitionSelectBlock(model, indexPath.row);
    }
}


@end







@interface VideoDefinitionListCell()
@property (nonatomic, strong) UIView *backContentView;
@property (nonatomic, strong) UILabel *definitionLab; ///清晰度
@property (nonatomic, strong) UIImageView *tagImageView;
@end

@implementation VideoDefinitionListCell
- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(nullable NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        [self addSubview];
    }
    return self;
}

- (void)addSubview {
    self.backContentView = [[UIView alloc] init];
    self.backContentView.backgroundColor = [[UIColor whiteColor] colorWithAlphaComponent:0.2];
    self.backContentView.layer.cornerRadius = 4.0f;
    self.backContentView.layer.masksToBounds = YES;
    self.backContentView.layer.borderWidth = CGFloatFromPixel(1.0);
    self.backContentView.layer.borderColor = [UIColor clearColor].CGColor;
    [self addSubview:self.backContentView];
    [self.backContentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsMake(WKDHPX(8), 0, 0, 0));
    }];
    
    //清晰度
    self.definitionLab = [[UILabel alloc] init];
    [self.backContentView addSubview:self.definitionLab];
    self.definitionLab.textColor = [UIColor whiteColor];
    self.definitionLab.font = kMedium_Font_NoDHPX(WKDHPX(13));
    self.definitionLab.text = @"高清";
    [self.definitionLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.backContentView.mas_left).offset(WKDHPX(8));
        make.right.equalTo(self.backContentView.mas_right).offset(-WKDHPX(8));
        make.height.mas_equalTo(22);
        make.centerY.equalTo(self.backContentView.mas_centerY).offset(0);
    }];
    
    //VIP标签
    self.tagImageView = [[UIImageView alloc] init];
    self.tagImageView.autoresizingMask = UIViewAutoresizingFlexibleTopMargin | UIViewAutoresizingFlexibleRightMargin;
    self.tagImageView.contentMode = UIViewContentModeScaleAspectFill;
    [self.backContentView addSubview:self.tagImageView];
    [self.tagImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.backContentView.mas_top);
        make.right.mas_equalTo(self.backContentView.mas_right);
        make.height.mas_equalTo(WKDHPX(16));
        make.width.mas_equalTo(WKDHPX(47));
    }];
}

- (void)setModel:(VideoListModel *)model{
    _model = model;
    
    self.definitionLab.text = model.definitionTitle;
    
    self.tagImageView.image = nil;
    if (model.showVipIcon) {
        UIImage *iconImage = nil;
        NSInteger viptype = model.vipType.intValue;
        switch (viptype) {
            case 10:
                iconImage = [AlivcImage imageNamed:@"video_definition_vip"];
                break;
            case 30:
                iconImage = [AlivcImage imageNamed:@"video_definition_enjoyvip"];
                break;
            default:
                break;
        }
        if (iconImage != nil) {
            self.tagImageView.image = iconImage;
        }
    }
}

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];
    // Configure the view for the selected state
    if (selected) {
        self.backContentView.backgroundColor = [UIColorHex(#00FFFF) colorWithAlphaComponent:0.2];
        self.backContentView.layer.borderColor = [UIColorHex(#00FFFF) colorWithAlphaComponent:1.0].CGColor;
    } else {
        self.backContentView.backgroundColor = [UIColorHex(#363A44) colorWithAlphaComponent:0.6];
        self.backContentView.layer.borderColor = [UIColor clearColor].CGColor;
    }
}

@end




@interface VideoDefinitionEnsureView ()
@property (nonatomic, strong) UILabel *descripLab;
@property (nonatomic, strong) UIButton *ensureBtn;
@property (nonatomic, strong) UIButton *cancelBtn;
@end


@implementation VideoDefinitionEnsureView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {

        [self addSubview:self.descripLab];
        [self.descripLab mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.mas_equalTo(self.mas_centerX);
            make.bottom.mas_equalTo(self.mas_centerY);
            make.left.mas_equalTo(WKDHPX(26));
            make.right.mas_equalTo(-WKDHPX(26));
        }];
        
        [self addSubview:self.ensureBtn];
        [self.ensureBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.descripLab.mas_bottom).offset(DHPX(45));
            make.centerX.mas_equalTo(self);
            make.size.mas_equalTo(CGSizeMake(DHPX(138), DHPX(36)));
        }];
        
        [self addSubview:self.averageDailyVue];
        [self.averageDailyVue mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.ensureBtn.mas_centerX);
            make.top.equalTo(self.ensureBtn.mas_top).offset(-WKDHPX(10));
            make.height.mas_equalTo(WKDHPX(18));
        }];
        self.averageDailyVue.hidden = YES;
        
        [self addSubview:self.cancelBtn];
        [self.cancelBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.ensureBtn.mas_bottom).offset(WKDHPX(16));
            make.centerX.mas_equalTo(self);
            make.size.mas_equalTo(CGSizeMake(DHPX(96), DHPX(20)));
        }];
    }
    return self;
}

- (void)layoutSubviews {
    [super layoutSubviews];
    [self.averageDailyVue partCornerRadius:8 byRoundingCorners:UIRectCornerTopLeft | UIRectCornerBottomRight];
}

- (void)setTipStr:(NSString *)tipStr {
    _tipStr = tipStr;
    self.descripLab.text = tipStr;
}

- (UILabel *)descripLab {
    if (!_descripLab) {
        _descripLab = [[UILabel alloc] init];
        _descripLab.font = [UIFont systemFontOfSize:WKDHPX(16) weight:UIFontWeightMedium];
        _descripLab.textColor = [UIColor whiteColor];
        _descripLab.textAlignment = NSTextAlignmentCenter;
        _descripLab.numberOfLines = 0;
        _descripLab.text = @"当前画质为会员专享\n开通会员即可体验";
    }
    return _descripLab;
}

- (UIButton *)ensureBtn{
    if (!_ensureBtn) {
        _ensureBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_ensureBtn setTitle:@"开通会员" forState:UIControlStateNormal];
        [_ensureBtn setTitleColor:UIColorHex(#672F15) forState:UIControlStateNormal];
        _ensureBtn.titleLabel.font = [UIFont systemFontOfSize:15];
        [_ensureBtn addTarget:self action:@selector(moreBtnClicked:) forControlEvents:UIControlEventTouchUpInside];
        _ensureBtn.tag = 2001;
        _ensureBtn.layer.cornerRadius = DHPX(36)/2;
        _ensureBtn.layer.masksToBounds = YES;
        [_ensureBtn  az_setGradientBackgroundWithColors:@[[UIColor colorWithHexString:@"#FFECDF"],
                                                          [UIColor colorWithHexString:@"#F0BA8A"]]
                                              locations:nil
                                             startPoint:CGPointMake(0, 0)
                                               endPoint:CGPointMake(1, 0)];
    }
    return _ensureBtn;
}

- (UIButton *)averageDailyVue {
    if (!_averageDailyVue) {
        _averageDailyVue = [UIButton buttonWithType:UIButtonTypeCustom];
        _averageDailyVue.userInteractionEnabled = NO;
        _averageDailyVue.titleLabel.font = kSystem_Font_NoDHPX(WKDHPX(11));
        [_averageDailyVue setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        [_averageDailyVue az_setGradientBackgroundWithColors:@[[UIColor colorWithHexString:@"#EF3473"],[UIColor colorWithHexString:@"#FE2550"]]
                                                locations:@[@0,@0.5]
                                               startPoint:CGPointMake(0, 0.5)
                                                 endPoint:CGPointMake(1, 0.5)];
    }
    return _averageDailyVue;
}

- (UIButton *)cancelBtn{
    if (!_cancelBtn) {
        _cancelBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_cancelBtn setTitle:@"我知道了" forState:UIControlStateNormal];
        [_cancelBtn setTitleColor:[UIColor.whiteColor colorWithAlphaComponent:0.7] forState:UIControlStateNormal];
        _cancelBtn.titleLabel.font = [UIFont systemFontOfSize:15];
        [_cancelBtn addTarget:self action:@selector(moreBtnClicked:) forControlEvents:UIControlEventTouchUpInside];
        _cancelBtn.tag = 2002;
    }
    return _cancelBtn;
}

- (void)moreBtnClicked:(UIButton *)sender {
    NSInteger tag = sender.tag;
    if (self.ensureOperationBlock) {
        self.ensureOperationBlock(tag);
    }
}

@end

NS_ASSUME_NONNULL_END




