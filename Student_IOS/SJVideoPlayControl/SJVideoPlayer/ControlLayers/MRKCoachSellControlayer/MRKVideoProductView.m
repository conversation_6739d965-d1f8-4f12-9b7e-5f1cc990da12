//
//  MRKVideoProductView.m
//  Student_IOS
//
//  Created by <PERSON><PERSON> on 2023/7/13.
//

#import "MRKVideoProductView.h"
#import "NSAttributedString+SJMake.h"
#import "UIView+SJAnimationAdded.h"
#import "POP.h"
#import "UIView+Animation.h"

NS_ASSUME_NONNULL_BEGIN

@interface MRKVideoProductNavView : UIView
@property (nonatomic, copy) void (^closeOperationBlock) (NSInteger tag);
@property (nonatomic, copy) NSString *navTitleStr;
- (void)refreshCloseItems:(id)x;
@end


@interface MRKVideoProductView ()<UIScrollViewDelegate, MrkWebViewDelegate>
@property (nonatomic, strong) UIView *maskLayerView;
@property (nonatomic, strong) UIView *messageView;
@property (nonatomic, strong) MRKVideoProductNavView *navBarView;
@property (nonatomic, strong) MrkWebProgressView *webProgressView;
@property (nonatomic, strong) MrkWebView *webView;
@end

@implementation MRKVideoProductView

- (void)show {
    if (self.contentView) {
        [self.contentView addSubview:self];
        
        self.isDisplayOnScreen = YES;
        [self createBlackView];
        [self createMessageView];
        
        if (self.autoHiddenDelay > 0) {
            [self performSelector:@selector(hide) withObject:nil afterDelay:self.autoHiddenDelay];
        }
    }
}

- (void)hide {
    [super hide];
    
    if (self.contentView) {
        self.isDisplayOnScreen = NO;
        [self removeViews];
    }
}


- (void)createBlackView {
    self.maskLayerView = [[UIView alloc] init];
    self.maskLayerView.backgroundColor = [UIColor clearColor];
    [self addSubview:self.maskLayerView];
    [self.maskLayerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsMake(0, 0, 0, 0));
    }];
    
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] init];
    [self.maskLayerView addGestureRecognizer:tap];
    @weakify(self);
    [tap.rac_gestureSignal subscribeNext:^(id x) {
        [self_weak_ hide];
    }];
}


- (void)createMessageView {
    // 创建信息窗体view
    self.messageView = [[UIView alloc] init];
    self.messageView.backgroundColor = [UIColor lightGrayColor];
    self.messageView.alpha = 0.f;
    [self addSubview:self.messageView];
    self.messageView.frame = CGRectMake(MainWidth, 0, DHPX(375), MainHeight);
    
    /**添加刷新对应的item*/
    [self _refreshItems];
    
    // 执行动画
    POPBasicAnimation *alpha = [POPBasicAnimation animationWithPropertyNamed:kPOPViewAlpha];
    alpha.toValue = @(1.f);
    alpha.duration = 0.3f;
    [self.messageView pop_addAnimation:alpha forKey:nil];
    
    CGRect fromValue = CGRectMake(MainWidth, 0, DHPX(375), MainHeight);
    CGRect toValue = CGRectMake(MainWidth -DHPX(375), 0, DHPX(375), MainHeight);
    POPBasicAnimation *frameAnimation = [POPBasicAnimation animationWithPropertyNamed:kPOPViewFrame];
    frameAnimation.duration = 0.3;
    frameAnimation.fromValue = [NSValue valueWithCGRect:fromValue];
    frameAnimation.toValue = [NSValue valueWithCGRect:toValue];
    frameAnimation.timingFunction = [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionEaseInEaseOut];
    @weakify(self);
    frameAnimation.completionBlock = ^(POPAnimation *anim, BOOL finished) {
        @strongify(self);
        [self.messageView.subviews enumerateObjectsUsingBlock:^(__kindof UIView *obj, NSUInteger idx, BOOL * _Nonnull stop) {
            if ([obj isKindOfClass:[UIButton class]]) {
                UIButton *btn = obj;
                btn.userInteractionEnabled = YES;
            }
        }];
    };
    [self.messageView pop_addAnimation:frameAnimation forKey:@"frameAnimation"];
}


- (void)removeViews {
    if (self.willDisappearBlock){
        self.willDisappearBlock(self);
    }
    
    POPBasicAnimation *alpha = [POPBasicAnimation animationWithPropertyNamed:kPOPViewAlpha];
    alpha.toValue = @(0.f);
    alpha.duration = 0.3f;
    [self.messageView pop_addAnimation:alpha forKey:nil];
    
    CGRect fromValue = CGRectMake(MainWidth -DHPX(375), 0, DHPX(375), MainHeight);
    CGRect toValue = CGRectMake(MainWidth, 0, DHPX(375), MainHeight);
    POPBasicAnimation *frameAnimation = [POPBasicAnimation animationWithPropertyNamed:kPOPViewFrame];
    frameAnimation.duration = 0.3;
    frameAnimation.fromValue = [NSValue valueWithCGRect:fromValue];
    frameAnimation.toValue = [NSValue valueWithCGRect:toValue];
    frameAnimation.timingFunction = [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionEaseInEaseOut];
    @weakify(self);
    frameAnimation.completionBlock = ^(POPAnimation *anim, BOOL finished) {
        @strongify(self);
        [self removeFromSuperview];
        if (self.didDisappearBlock){
            self.didDisappearBlock(self);
        }
    };
    [self.messageView pop_addAnimation:frameAnimation forKey:@"frameAnimation"];
}

- (void)dealloc{
    NSLog(@"😊😊😊-----dealloc-----😊😊😊%@" , NSStringFromClass([self class]));
}

/*
 // Only override drawRect: if you perform custom drawing.
 // An empty implementation adversely affects performance during animation.
 - (void)drawRect:(CGRect)rect {
 // Drawing code
 }
 */

#pragma mark -

- (void)_refreshItems {
    [self.messageView addSubview:self.navBarView];
    [self.navBarView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.messageView.mas_top).offset(0);
        make.left.mas_equalTo(self.messageView.mas_left);
        make.right.mas_equalTo(self.messageView.mas_right);
        make.height.mas_equalTo(DHPX(50));
    }];
    

    self.webView.delegate = self;
    if (@available(iOS 11.0, *)) {
        self.webView.realWebView.scrollView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    }
    [self.messageView addSubview:self.webView];
    [self.webView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsMake(DHPX(50), 0, 0, 0));
    }];
    

    [self.messageView addSubview:self.webProgressView];
    [self.webProgressView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.navBarView.mas_bottom).offset(0);
        make.left.mas_equalTo(self.messageView.mas_left);
        make.right.mas_equalTo(self.messageView.mas_right);
        make.height.mas_equalTo(2.5);
    }];
    
    ///进度处理
    @weakify(self);
    [RACObserve(self.webView, estimatedProgress) subscribeNext:^(id x) {
        @strongify(self);
        double progress = [x doubleValue];
        [self.webProgressView setProgress:progress animated:YES];
    }];
    
    [RACObserve(self.webView.realWebView, canGoBack) subscribeNext:^(id x) {
        @strongify(self);
        [self.navBarView refreshCloseItems:x];
    }];
}

///加载Url
- (void)setHttpLoadUrl:(NSString *)httpLoadUrl {
    _httpLoadUrl = httpLoadUrl;
    NSMutableURLRequest *requset = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:httpLoadUrl]
                                                           cachePolicy:NSURLRequestUseProtocolCachePolicy
                                                       timeoutInterval:10];
    [self.webView loadRequest:requset];
}

- (MRKVideoProductNavView *)navBarView{
    if (!_navBarView) {
        _navBarView = [[MRKVideoProductNavView alloc] init];
        @weakify(self);
        _navBarView.closeOperationBlock = ^(NSInteger tag) {
            @strongify(self);
            [self webgoBack:tag];
        };
    }
    return _navBarView;
}

- (MrkWebView *)webView{
    if (!_webView) {
        _webView = [[MrkWebView alloc] init];
        [_webView setScalesPageToFit:YES];
    }
    return _webView;
}

- (MrkWebProgressView *)webProgressView{
    if (!_webProgressView) {
        _webProgressView = [[MrkWebProgressView alloc] init];
        _webProgressView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleTopMargin;
        _webProgressView.progressBarView.backgroundColor = UIColorHex(5791FE);
    }
    return _webProgressView;
}



#pragma mark - WebViewDelegate
- (void)webView:(MrkWebView * _Nonnull)webView didFailLoadWithError:(NSError * _Nonnull)error {
    
}

- (void)webView:(MrkWebView * _Nonnull)webView didLoadWithTitle:(NSString * _Nonnull)title {
    self.navBarView.navTitleStr = title;
}

- (BOOL)webView:(MrkWebView * _Nonnull)webView shouldStartLoadWith:(NSURLRequest * _Nonnull)request navigationType:(WKNavigationType)navigationType {
    return YES;
}

- (void)webViewDidFinishLoad:(MrkWebView * _Nonnull)webView {
    
}

- (void)webViewDidStartLoad:(MrkWebView * _Nonnull)webView {
    
}

#pragma mark - webView中返回按钮的点击方法
- (void)webgoBack:(NSInteger)tag {
    if (tag == 2000) {[self hide]; return;}
    
    if (self.webView.canGoBack) {
        [self.webView goBack];
    }else {
        [self hide];
    }
}

/*
 // Only override drawRect: if you perform custom drawing.
 // An empty implementation adversely affects performance during animation.
 - (void)drawRect:(CGRect)rect {
 // Drawing code
 }
 */

@end






@interface MRKVideoProductNavView ()
@property (nonatomic, strong) UIButton *closeBtn;
@property (nonatomic, strong) UIButton *webBackBtn;
@property (nonatomic, strong) UILabel *titleLab;
@end


@implementation MRKVideoProductNavView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = UIColor.whiteColor;
        
        [self addSubview:self.titleLab];
        [self.titleLab mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.mas_equalTo(self.mas_centerX);
            make.centerY.mas_equalTo(self.mas_centerY);
            make.width.mas_equalTo(100);
        }];
        
        [self addSubview:self.webBackBtn];
        [self.webBackBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.mas_equalTo(self.mas_left).offset(DHPX(5));
            make.centerY.mas_equalTo(self.mas_centerY);
            make.size.mas_equalTo(CGSizeMake(DHPX(0), DHPX(30)));
        }];
        
        [self addSubview:self.closeBtn];
        [self.closeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.mas_equalTo(self.mas_left).offset(DHPX(5));
            make.centerY.mas_equalTo(self.mas_centerY);
            make.size.mas_equalTo(CGSizeMake(DHPX(30), DHPX(30)));
        }];
        [self bringSubviewToFront:self.closeBtn];
    }
    return self;
}


- (void)setNavTitleStr:(NSString *)navTitleStr{
    _navTitleStr = navTitleStr;
    self.titleLab.text = navTitleStr;
}

- (void)refreshCloseItems:(id)x{
    if ([x boolValue]) {
        [self.closeBtn mas_updateConstraints:^(MASConstraintMaker *make) {
            make.left.mas_equalTo(self.mas_left).offset(DHPX(5) +DHPX(30) +DHPX(5));
        }];
        
        [self.webBackBtn mas_updateConstraints:^(MASConstraintMaker *make) {
            make.size.mas_equalTo(CGSizeMake(DHPX(30), DHPX(30)));
        }];
        
    }else {
        
        [self.closeBtn mas_updateConstraints:^(MASConstraintMaker *make) {
            make.left.mas_equalTo(self.mas_left).offset(DHPX(5));
        }];
        
        [self.webBackBtn mas_updateConstraints:^(MASConstraintMaker *make) {
            make.size.mas_equalTo(CGSizeMake(DHPX(0), DHPX(30)));
        }];
        
    }
}

- (UILabel *)titleLab {
    if (!_titleLab) {
        _titleLab = [[UILabel alloc] init];
        _titleLab.font = [UIFont systemFontOfSize:14 weight:UIFontWeightMedium];
        _titleLab.textColor = UIColorHex(#333333);
        _titleLab.textAlignment = NSTextAlignmentCenter;
    }
    return _titleLab;
}

- (UIButton *)closeBtn{
    if (!_closeBtn) {
        _closeBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_closeBtn setImage:[UIImage imageNamed:@"icon_back-3"] forState:UIControlStateNormal];
        [_closeBtn addTarget:self action:@selector(moreBtnClicked:) forControlEvents:UIControlEventTouchUpInside];
        _closeBtn.tag = 2000;
    }
    return _closeBtn;
}

- (UIButton *)webBackBtn{
    if (!_webBackBtn) {
        _webBackBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_webBackBtn setImage:[UIImage imageNamed:@"icon_back"] forState:UIControlStateNormal];
        [_webBackBtn addTarget:self action:@selector(moreBtnClicked:) forControlEvents:UIControlEventTouchUpInside];
        _webBackBtn.tag = 3000;
    }
    return _webBackBtn;
}

- (void)moreBtnClicked:(UIButton *)sender {
    NSInteger tag = sender.tag;
    if (self.closeOperationBlock) {
        self.closeOperationBlock(tag);
    }
}


/*
 // Only override drawRect: if you perform custom drawing.
 // An empty implementation adversely affects performance during animation.
 - (void)drawRect:(CGRect)rect {
 // Drawing code
 }
 */

@end

NS_ASSUME_NONNULL_END
