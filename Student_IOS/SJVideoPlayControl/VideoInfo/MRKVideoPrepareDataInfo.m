//
//  MRKVideoPrepareDataInfo.m
//  Student_IOS
//
//  Created by merit on 2022/4/13.
//

#import "MRKVideoPrepareDataInfo.h"
#import "MRKBuglyManager.h"
#import "NSTimer+SJAssetAdd.h"
#import "MRKDeviceURLRequest.h"
#import "MRKSignActivity.h"

static NSString *const detailSignalName = @"VideoDataInfo";
static NSString *const courseSignalName = @"VideoCourseStatusInfo";

typedef void(^DeviceDataBlock)(void);

@interface MRKVideoPrepareDataInfo (){
    NSInteger _reTryCount; ///记录40状态后获取不到开播时间次数
    NSTimer *_Nullable _timer;
}
@property (nonatomic, strong) RACSubject *updateDetailSignal;
@property (nonatomic, strong) RACSubject *courseStatusSignal;
@property (nonatomic, copy) DeviceDataBlock dataBlock;

@property (nonatomic, assign) BOOL needRefreshCourseStatus; ///刷新课程状态的阀值
@property (nonatomic, strong) MRKSignActivity *checkActivity;
@end

@implementation MRKVideoPrepareDataInfo


- (BOOL)isVideoAdaptScreen {
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    return [userDefaults boolForKey:@"VideoAdaptScreenTip_2"];
}

- (void)setVideoAdaptScreen:(BOOL)videoAdaptScreen {
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    [userDefaults setBool:videoAdaptScreen forKey:@"VideoAdaptScreenTip_2"];
    [userDefaults synchronize];
}

- (MRKSignActivity *)checkActivity{
    if(!_checkActivity) {
        _checkActivity = [[MRKSignActivity alloc] init];
    }
    return _checkActivity;
}

- (NSNumber *)type{
    NSInteger type = self.playbackType == SJPlaybackTypeREAL ? 2 : 1 ;
    return @(type);
}

- (NSNumber *)playStatus{
    NSInteger playStatus = self.playbackType == SJPlaybackTypeLIVE ? 1 : 2 ;
    return @(playStatus);
}

- (instancetype)init {
    self = [super init];
    if(self){
        self.updateDetailSignal = [[RACSubject subject] setNameWithFormat:detailSignalName];
        self.courseStatusSignal = [[RACSubject subject] setNameWithFormat:courseSignalName];
        
        ///直播监听课程状态用
        self.needRefreshCourseStatus = NO;
        [self initTimer];
        
        
        ///组合并聚合 [监听设备model 刷新教案]
        @weakify(self);
        RACSignal *reduceSignal = [RACSignal combineLatest:@[RACObserve(self, eqModel), RACObserve(self, mappingPlanModel)]
                                                    reduce:^id(EquipmentDetialModel *eqModel, MRKVideoLessonPlanModel *mappingPlanModel){
            return @(eqModel != nil && ![eqModel.idd isEqualToString:mappingPlanModel.modelId]);
        }];
        [reduceSignal takeUntil:[self rac_willDeallocSignal]];
        [[reduceSignal distinctUntilChanged] subscribeNext:^(id x) {
            @strongify(self);
            ///YES 说明映射教案有问题， 需要刷新教案
            if ([x boolValue]) {
                [self getCourseMappingTeachingPlanInfo:^{}];
            }
        }];
    }
    return self;
}

#pragma mark - 查询课程在aiPlan中的状态
- (void)AIPlanCheckCoiurse{
    NSDictionary *parms = @{
        @"taskType": @"1", ///课程1. 动作9
        @"targetId": self.liveModel.courseId ?:@""
    };
    [MRKBaseRequest mrkSilenceGetRequestUrl:@"/course/training-plan/target-info"
                                    andParm:parms
                   completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        id data = [request.responseObject objectForKey:@"data"];
        MRKAIPlanCheckModel *model = [MRKAIPlanCheckModel modelWithJSON:data];
        self.planCheckModel = model;
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        
    }];
}

- (void)dealloc {
    if ([_timer isValid]) {
        [_timer invalidate];
        _timer = nil;
    }
    NSLog(@"😊😊😊-----dealloc-----😊😊😊%@" , NSStringFromClass([self class]));
}

- (void)initTimer {
    if ( _timer != nil ) return;
    
    __weak typeof(self) _self = self;
    _timer = [NSTimer sj_timerWithTimeInterval:2.0 repeats:YES usingBlock:^(NSTimer * _Nonnull timer) {
        __strong typeof(_self) self = _self;
        if ( !self ) {
            [timer invalidate];
            return ;
        }
        
        if (self.needRefreshCourseStatus) {
            [self getCourseStatusInfo:nil];
        }
    }];
    
    [NSRunLoop.mainRunLoop addTimer:_timer forMode:NSRunLoopCommonModes];
    [_timer fire];
}

- (void)destroyTimer {
    self.needRefreshCourseStatus = NO;
    
    if ([_timer isValid]) {
        [_timer invalidate];
        _timer = nil;
    }
}

- (void)refreshDataSource{
    @weakify(self)
    RACSignal *goodsSignal = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self getCourseGoods:^{
            [subscriber sendNext:nil];
        }];
        return nil;
    }];
    
    RACSignal *userSignal = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self getUserInfo:^{
            [subscriber sendNext:nil];
        }];
        return nil;
    }];
    
    RACSignal *mappinSignal = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self getDeviceMappingInfo:^{
            [subscriber sendNext:nil];
        }];
        return nil;
    }];
    
    RACSignal *videoListSignal = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self getVideoListInfo:^{
            [subscriber sendNext:nil];
        }];
        return nil;
    }];
    
    RACSignal *equipmentInfoSignal = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self getEquipmentInfo:^{
            [subscriber sendNext:nil];
        }];
        return nil;
    }];
    
    RACSignal *teachPlanSignal = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self getCourseTeachingPlanInfo:^{
            [subscriber sendNext:nil];
        }];
        return nil;
    }];
    
    RACSignal *courseStatusSignal = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        if (self.liveModel.videoType != MrkVideoTypeLive) {
            [subscriber sendNext:nil];
            [self destroyTimer];
        }else{
            [self getCourseStatusInfo:^{
                [subscriber sendNext:nil];
            }];
        }
        return nil;
    }];
    
    RACSignal *deviceListSignal = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self getUserBuildDeviceListInfo:^{
            [subscriber sendNext:nil];
        }];
        return nil;
    }];
    
    RACSignal *checkSignal = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self checkActivity:^{
            [subscriber sendNext:nil];
        }];
        return nil;
    }];
    
    RACSignal *vipAction = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self requestVipInfo:^{
            [subscriber sendNext:nil];
        }];
        return nil;
    }];
    
    NSMutableArray *arr = [NSMutableArray arrayWithArray:@[goodsSignal, userSignal, mappinSignal, videoListSignal, equipmentInfoSignal, teachPlanSignal, courseStatusSignal, deviceListSignal, checkSignal]];
    if (UserInfo.isNormalMember) {
        [arr appendObject:vipAction];
    }
    
    [[RACSignal combineLatest:arr] subscribeNext:^(id x) {
        @strongify(self);
        [(RACSubject *)self.updateDetailSignal sendNext:x];
    }];
}


#pragma mark - 会员数据 -
- (void)requestVipInfo:(void(^)(void))completion {
    @weakify(self);
    [MRKBaseRequest mrkRequestType:YTKRequestMethodGET
                               url:@"/user/member-product/vip-product"
                           andParm:@{@"vipType": @(UserInfo.vipType)}
                      notShowError:YES
          completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        @strongify(self);
        NSLog(@"======= %@",request.responseObject);
        id data = [request.responseObject valueForKeyPath:@"data.ordinaries"];
        NSArray *arr = [NSArray modelArrayWithClass:[MRKVipCardModel class] json:data];
        id data1 = [request.responseObject valueForKeyPath:@"data.upgrades"];
        NSArray *arr1 = [NSArray modelArrayWithClass:[MRKVipCardModel class] json:data1];
        MRKVipCardModel *model = nil;
        if (arr1.count > 0) {
            model = arr1.firstObject;
        }
        NSMutableArray *products = arr.mutableCopy;
        if (model != nil) {
            [products insertObject:model atIndex:0];
        }
        self.vipModel = products.firstObject;
        if (completion) {
            completion();
        }
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        if (completion) {
            completion();
        }
    }];
}

#pragma mark - HTTP -

///教练信息model
- (MRKCoachModel *)coachModel{
    return self.liveModel.coachPO;
}

///课程播放类型
- (SJPlaybackType)playbackType{
    SJPlaybackType playType = SJPlaybackTypeUnknown;
    switch (self.liveModel.videoType) {
        case MrkVideoTypeLive:{
            playType = SJPlaybackTypeLIVE;
        } break;
        case MrkVideoTypeVOD:{
            playType = SJPlaybackTypeVOD;
            if (self.liveModel.isRealVideoCourse) {
                playType = SJPlaybackTypeREAL;
            }
        } break;
        default:
            break;
    }
    return playType;
}

- (NSString *)tracePlaybackType {
    switch (self.playbackType) {
        case SJPlaybackTypeLIVE:
            return @"living";
            break;
        case SJPlaybackTypeVOD:
            return @"recorded";
            break;
        case SJPlaybackTypeREAL:
            return @"liveVideo";
            break;//实景
        default:
            return @"local";
            break;
    }
}

///设备大类id
- (NSString *)equipmentId{
    NSString *equipTypeId = self.liveModel.equipmentId;
    return equipTypeId;
}

///设备名称
- (NSString *)equipmentName{
    return self.equipInfoName;
}

///是否在活动中
- (BOOL)isOnActivity{
    ActivityEquipmentModel *model = self.liveModel.courseActivityPO;
    return model.isActivity.boolValue;
}

///是不是试看5分钟
- (BOOL)isTestPlay {
    if (self.liveModel.isVipCourse && self.liveModel.videoType == MrkVideoTypeVOD) {
        if (!self.isMember || self.memberInfo.vipType < self.liveModel.vipType.intValue){
            return YES;
        }
    }
    return NO;
}

///是否展示播放历史记录
- (BOOL)isHistoryPlayTime{
    ///跳转进度时长为0不显示
    if (self.planModel.playTime == 0) { return NO; }
    ///活动不显示跳转进度
    if ([self isOnActivity]) { return NO; }
    ///在试看环节不显示
    if ([self isTestPlay]) { return NO; }
    
    ///在视频开始的15%  和 结尾15% 不显示跳转进度
    NSInteger courseTime1 = (self.liveModel.courseTime.intValue *60) *0.15;
    NSInteger courseTime2 = (self.liveModel.courseTime.intValue *60) *0.85;
    if (self.planModel.playTime < courseTime1 || self.planModel.playTime > courseTime2) {
        return NO;
    }
    return YES;
}

///是否展示连接弹窗
- (BOOL)isNeedConnectMachine{
    return self.liveModel.isSupportConnection && !self.isConnectMachine;
}

///是否电磁控和是merit课程
- (BOOL)isMeritControl {
    if (self.liveModel.isMeritCourse &&
        self.liveModel.isSupportConnection &&
        self.eqModel.isElectromagneticControl ) {
        return YES;
    }
    return NO;
}

///是否控制调节按钮
- (BOOL)isShowAutoControl {
    return self.meritControl;
}

///是否连接设备中
- (BOOL)isConnectMachine{
    NSString *equipTypeId = self.liveModel.equipmentId;
    if ([BluetoothManager isConnectEquipmentType:equipTypeId]) {
        return YES;
    }
    return NO;
}

- (void)changeVideoStatus{
    self.liveModel.courseStatus = @"40";
    self.liveModel.videoType = MrkVideoTypeLive;
}

///直播35状态屏蔽交互
- (BOOL)videoLivingCanInteraction{
    if (self.liveModel.courseStatus.integerValue == 35) {
        return NO;
    }
    return YES;
}

///连接设备名称
- (NSString *)equipInfoName{
    NSString *equipmentId = self.liveModel.equipmentId;
    NSString *equipInfoName = @"";
    if( [BluetoothManager isConnectEquipmentType:equipmentId] ){
        BluetoothModel *bModel = [BlueDataStorageManager connectBMFromProductID:equipmentId];
        equipInfoName = bModel.localName;
    }
    return equipInfoName ?:@"";
}

///是否绑过当前类型设备
- (BOOL)isBuildDevice {
    return self.buildDeviceArr.count > 0;
}

- (BOOL)isUseRateKcal{
    return self.checkActivity.useRateKcal;
}

- (id)activityModel{
    return self.checkActivity.activityModel;
}

- (BOOL)isSupportDisplayResistance {
    NSInteger equipType = self.liveModel.equipmentId.intValue;
    return  equipType == BoatEquipment || equipType == BicycleEquipment || equipType == EllipticalEquipment;
}


#pragma mark - 查询录播商品数据
- (void)getCourseGoods:(void(^)(void))completion {
    NSDictionary *parms = @{
        @"courseId":self.liveModel.courseId?:@"",
        @"status": self.playbackType == SJPlaybackTypeLIVE? @"1":@""
    };
    [MRKBaseRequest mrkSilenceGetRequestUrl:@"/course/course_goods"
                                    andParm:parms
                   completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        id data = [request.responseObject objectForKey:@"data"];
        self.courseGoodsArr = [NSArray modelArrayWithClass:[MRKCourseGoodsModel class] json:data];
        completion();
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        completion();
    }];
}

#pragma mark - 查询用户Vip数据
- (void)getUserInfo:(void(^)(void))completion {
    [MRKBaseRequest mrkSilenceGetRequestUrl:@"/user/user-member"
                                    andParm:nil
                   completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        id data = [request.responseObject valueForKeyPath:@"data"];
        self.memberInfo = [MemberInfoDataDTO modelWithJSON:data];
        self.isMember = self.memberInfo.isMember;
        ///更新缓存会员信息
        [Login changeLogindData:self.memberInfo];
        completion();
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        completion();
    }];
}

#pragma mark - 查询开启用户阻力映射状态
- (void)getDeviceMappingInfo:(void(^)(void))completion {
    [MRKBaseRequest mrkSilenceGetRequestUrl:@"/user/user-setting/get"
                                    andParm:@{@"type":@"2"}
                   completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        id data = [request.responseObject valueForKeyPath:@"data"];
        self.openMapSetDevice = [data boolValue];
        completion();
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        completion();
    }];
}

#pragma mark - 视频播放地址列表
- (void)getVideoListInfo:(void(^)(void))completion {
    NSDictionary *parms = @{
        @"courseId": self.liveModel.courseId ?:@"",
        @"type" : self.type
    };
    [MRKBaseRequest mrkGetRequestUrl:@"/course/courseVideoController/course-videos"
                             andParm:parms
            completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"视频播放地址列表 ==== %@", request.responseObject);
        id data = [request.responseObject valueForKeyPath:@"data.items"];
        self.videoListArr = [NSArray modelArrayWithClass:[VideoListModel class] json:data];
        self.defaultPlayIndex = ({
            __block NSInteger defaultIndex = 0;
            [self.videoListArr enumerateObjectsUsingBlock:^(id  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
                VideoListModel *mode = (VideoListModel *)obj;
                if (mode.defaultCheck) {
                    defaultIndex = idx;
                    *stop = YES;
                }
            }];
            defaultIndex;
        });
        completion();
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        completion();
        MLog(@"视频播放地址列表请求异常 ==== courseId:%@", self.liveModel.courseId);
    }];
}

#pragma mark - 设备数据/ 底部显示数据
- (void)getEquipmentInfo:(void(^)(void))completion {
    @weakify(self)
    RACSignal *displaySignal = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self getDisplayConfig:^{
            [subscriber sendNext:nil];
        }];
        return nil;
    }];
    
    RACSignal *equipmentAction = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self getEquipmentConfig:^{
            [subscriber sendNext:nil];
        }];
        return nil;
    }];
    
    [[RACSignal combineLatest:@[displaySignal, equipmentAction]] subscribeNext:^(id x) {
        completion();
    }];
}

- (void)getDisplayConfig:(void(^)(void))completion {
    NSDictionary *parms = @{
        @"equipTypeId" : self.liveModel.equipmentId ?:@"",
        @"equipInfoName" : self.equipInfoName ?:@""
    };
    [MRKBaseRequest mrkSilenceGetRequestUrl:@"/course/courseShowConfig/v2"
                                    andParm:parms
                   completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        id configs = [request.responseObject valueForKeyPath:@"data.configs"];
        self.deviceDisplayArr = [NSArray modelArrayWithClass:[MRKEqDisplayModel class] json:configs];
        completion();
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        completion();
    }];
}

- (void)getEquipmentConfig:(void(^)(void))completion {
    if (!self.isConnectMachine) {
        completion();
        return;
    }
    
    NSDictionary *parms = @{
        @"equipName" : self.equipInfoName ?:@"",
        @"oneLevelTypeId" : self.liveModel.equipmentId ?:@""
    };
    [MRKBaseRequest mrkSilenceGetRequestUrl:@"/equip/equipment/equipmentInfoController/getEquipTypeInfoByName/v2"
                                    andParm:parms
                   completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        id data = [request.responseObject valueForKeyPath:@"data"];
        MLog(@"视频设备数据 ==== %@", data);
        EquipmentDetialModel *model = [EquipmentDetialModel modelWithJSON:data];
        model.productID = self.liveModel.equipmentId;
        self.eqModel = model;
        self.checkActivity.eqModel = model;
        completion();
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        completion();
    }];
}

#pragma mark - 查询课程默认教案
- (void)getCourseTeachingPlanInfo:(void(^)(void))completion {
    ///实景屏蔽教案
    if (self.playbackType == SJPlaybackTypeREAL){
        completion();
        return;
    }
    
    NSDictionary *parms = @{
        @"courseId": self.liveModel.courseId ?:@"",
        @"type": self.type,
        @"isNewVersion" : @1}.mutableCopy;
    [MRKBaseRequest mrkSilenceGetRequestUrl:@"/course/play"
                                    andParm:parms
                   completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        id data = [request.responseObject valueForKeyPath:@"data"];
        self.planModel = [MRKVideoLessonPlanModel modelWithJSON:data];
        NSLog(@"self.planModel  ======= %@", data);
        completion();
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        completion();
        MLog(@"教案数据请求失败 ==== courseId:%@", self.liveModel.courseId);
    }];
}

#pragma mark - 查询阻力映射后的教案
- (void)getCourseMappingTeachingPlanInfo:(void(^)(void))completion {
    ///实景屏蔽教案
    if (self.playbackType == SJPlaybackTypeREAL){
        completion();
        return;
    }
    
    ///跑步机档位统一，不需要映射教案
    if (self.equipmentId.intValue == TreadmillEquipment){
        completion();
        return;
    }
    
    ///不是超燃脂课程， 设备为跑步机，不是电磁控设备
    if (!self.liveModel.isMeritCourse || !self.eqModel.isElectromagneticControl){
        completion();
        return;
    }
    
    NSDictionary *parms = @{
        @"courseId": self.liveModel.courseId ?:@"",
        @"modelId": self.eqModel.modelId ?:@"", //设备型号id [2023-8-28junq 新加]
        @"type": self.type,
        @"isNewVersion" : @1
    };
    [MRKBaseRequest mrkSilenceGetRequestUrl:@"/course/play/convert/Resistance"
                                    andParm:parms
                   completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        id data = [request.responseObject valueForKeyPath:@"data"];
        self.mappingPlanModel = [MRKVideoLessonPlanModel modelWithJSON:data];
        self.mappingPlanModel.modelId = self.eqModel.idd;
        NSLog(@"self.mappingPlanModel  ======= %@", data);
        completion();
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        completion();
    }];
}


#pragma mark - 查询用户绑定设备列表
- (void)getUserBuildDeviceListInfo:(void(^)(void))completion {
    [MRKDeviceURLRequest requestMyDevice:@{@"productId":self.liveModel.equipmentId?:@""}
                                 success:^(id data) {
        self.buildDeviceArr = data;
        completion();
    } fail:^(id data) {
        completion();
    }];
}

#pragma mark - 查询当前设备大类 -> 活动状态
- (void)checkActivity:(void(^)(void))completion {
    [self.checkActivity productId:self.liveModel.equipmentId requestCheckActivity:^{
        completion();
    }];
}

#pragma mark - 查询课程状态
- (void)getCourseStatusInfo:(void(^)(void))completion {
    [MRKBaseRequest mrkSilenceGetRequestUrl:@"/course/course-status/v1"
                                    andParm:@{@"courseId": self.liveModel.courseId ?:@""}
                   completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        id data = [request.responseObject valueForKeyPath:@"data"];
        NSInteger courseStatus = [[data valueForKey:@"status"] integerValue];
        if (courseStatus == 35) {
            self.needRefreshCourseStatus = YES;
        }else if (courseStatus == 40) {
            NSTimeInterval actualLiveTime = [[data valueForKey:@"actualLiveTimestamp"] doubleValue];
            if (actualLiveTime > 0) {
                ///直播开始时间
                self.actualLiveTime = actualLiveTime/1000;
                [self changeVideoStatus];
                
                if (completion == nil) {
                    [(RACSubject *)self.courseStatusSignal sendNext:nil];
                }
                [self destroyTimer];
            } else {
                [self courseStatusRetry:YES];
            }
        }
        
        if (completion){
            completion();
        }
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        [self courseStatusRetry:NO];
        if (completion) {
            completion();
        }
    }];
}

- (void)courseStatusRetry:(BOOL)actualLiveTimeError{
    if (actualLiveTimeError) {
        _reTryCount ++;
        if (_reTryCount >= 4) {
            [self destroyTimer];
            MLog(@"直播40状态,开播时间为空==== courseId:%@", self.liveModel.courseId);
            return;
        }
    }
    
    self.needRefreshCourseStatus = YES;
}













#pragma mark - 提交播放时长统计
- (void)requestPlayTime:(NSTimeInterval)playTime progressTime:(NSTimeInterval)progressTime{
    NSDictionary *parms = @{
        @"courseId" : self.liveModel.courseId ?:@"",
        @"playStatus" : self.playStatus,
        @"type" : self.type,
        @"progressTime" : @(progressTime),
        @"playTime" : @(playTime)
    };
    [MRKBaseRequest mrkSilenceGetRequestUrl:@"/course/play/close"
                                    andParm:parms
                   completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        
    }];
    
    
    /**
     训练计划训练触发 【建兴让的】
     @note 防止未触发训练导致的计划课程未训练
     **/
    [MRKBaseRequest mrkSilenceGetRequestUrl:@"/course/coursePlanUserAssociatedController/trainInfo"
                                    andParm:@{@"courseId":self.liveModel.courseId?:@""}
                   completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        
    }];
}

#pragma mark - 刷新阻力映射
- (void)updateMappingResistance:(BOOL)isOn completion:(void(^)(BOOL completion))completion {
    NSDictionary *parms = @{
        @"type":@"2",
        @"operation":isOn?@"1":@"0"
    };
    [MRKBaseRequest mrkPostRequestUrl:@"/user/user-setting"
                             andParm:parms
            completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        self.openMapSetDevice = isOn;
        if (completion) {
            completion(YES);
        }
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        if (completion) {
            completion(NO);
        }
    }];
}

#pragma mark - 映射阻力查找操作
- (void)mappingResistance:(CourseLinkModel *)model completion:(void(^)(CourseLinkModel *mod))completion{
    @weakify(self);
    void(^sendLinkModelBlock)(void) = ^{
        @strongify(self);
        if (self.eqModel.maxResistance.intValue > 0 && model.adviseNum.intValue > self.eqModel.maxResistance.intValue){
            model.adviseNum = self.eqModel.maxResistance; ///如果教案阻力大于设备支持的最大阻力，转成当前设备支持的最大阻力
        }
        if (self.eqModel.maxSlope.intValue > 0 && model.slopeNum.intValue > self.eqModel.maxSlope.intValue) {
            model.slopeNum = self.eqModel.maxSlope; ///如果教案坡度大于设备支持的最大坡度，转成当前设备支持的最大坡度
        }
        completion(model);
        return;
    };
    
    ///没有开启映射 || 映射数据为空， 原始数据下发
    if ( self.openMapSetDevice == NO || self.mappingPlanModel == nil) {
        sendLinkModelBlock();
        return;
    }
    
    ///映射教案数据和当前设备匹配不上， 原始数据下发
    if (![self.mappingPlanModel.modelId isEqualToString:self.eqModel.idd]) {
        sendLinkModelBlock();
        return;
    }
    
    ///查询指令信息在映射数据中的对应的数据
   __block BOOL findMappingData = NO;
    NSMutableArray<CourseLinkModel *> *courseLinkPOS = self.mappingPlanModel.courseLinkPOS;
    [courseLinkPOS enumerateObjectsUsingBlock:^(CourseLinkModel * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([model.cid isEqualToString:obj.cid]){
            completion(obj);
            findMappingData = YES;
            *stop = YES;
        }
    }];
    
    if (!findMappingData) {
        sendLinkModelBlock();
    }
}

@end



