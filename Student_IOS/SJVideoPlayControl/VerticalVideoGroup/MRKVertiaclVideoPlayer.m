//
//  MRKVertiaclVideoPlayer.m
//  Student_IOS
//
//  Created by merit on 2022/7/18.
//

#import "MRKVertiaclVideoPlayer.h"
#import "UIView+SJAnimationAdded.h"
#import <objc/message.h>
#import "SJReachability.h"
#import "SJBaseVideoPlayer.h"
#import "SJBaseVideoPlayerConst.h"
#import "UIView+SJBaseVideoPlayerExtended.h"
#import "NSTimer+SJAssetAdd.h"
#import "MRKVideoDefinitionView.h"

NS_ASSUME_NONNULL_BEGIN


@interface MRKVertiaclVideoPlayer ()
<SJSwitchVideoDefinitionControlLayerDelegate,
SJMoreSettingControlLayerDelegate,
SJNotReachableControlLayerDelegate,
MRKVipHolderLayerDelegate,
MrkVerticalEdgeControlLayerDelegate,
MRKVideoPlayDoneLayerDelegate,
MRKVideoPLayPrepareLayerDelegate>

@property (nonatomic, strong, nullable) id<SJSmallViewFloatingControllerObserverProtocol> sj_smallViewFloatingControllerObserver;
@property (nonatomic, strong, readonly) SJVideoDefinitionSwitchingInfoObserver *sj_switchingInfoObserver;
@property (nonatomic, strong, readonly) id<SJControlLayerAppearManagerObserver> sj_appearManagerObserver;
@property (nonatomic, strong, readonly) id<SJControlLayerSwitcherObserver> sj_switcherObserver;
@property (nonatomic, strong, nullable) SJEdgeControlButtonItem *definitionItem;
/// 用于断网之后(当网络恢复后使播放器自动恢复播放)
@property (nonatomic, strong, nullable) id<SJReachabilityObserver> sj_reachabilityObserver;
@property (nonatomic, strong, nullable) NSTimer *sj_timeoutTimer;
@property (nonatomic) BOOL sj_isTimeout;
@end

@implementation MRKVertiaclVideoPlayer

- (void)dealloc {
    [NSNotificationCenter.defaultCenter removeObserver:self];
#ifdef DEBUG
    NSLog(@"%d \t %s", (int)__LINE__, __func__);
#endif
}

+ (NSString *)version {
    return @"v3.3.2";
}

+ (instancetype)player {
    return [[self alloc] init];
}

- (instancetype)init {
    self = [self _init];
    if ( !self ) return nil;
    
    [self.switcher switchControlLayerForIdentifier:SJControlLayer_Edge];   // 切换到添加的控制层
    self.defaultEdgeControlLayer.hiddenBottomProgressIndicator = NO;       // 显示底部进度条
    self.defaultEdgeControlLayer.bottomProgressIndicatorHeight = 2;
    
    self.useLessonPlanControl = YES;                                       // 默认使用教案
    self.closeBarrageControl = NO;                                         // 默认不关闭弹幕
    
    return self;
}

- (instancetype)_init {
    self = [super init];
    if ( !self ) return nil;
    
    [self _observeNotifies];
    [self _initializeSwitcher];
    [self _initializeSwitcherObserver];
    [self _initializeSettingsObserver];
    [self _initializeAppearManagerObserver];
    [self _initializeReachabilityObserver];
    [self _configurationsDidUpdate];
    
    return self;
}


///
/// 点击了剪辑按钮
///
- (void)_clipsItemWasTapped:(SJEdgeControlButtonItem *)clipsItem {
    
}

///
/// 点击了切换清晰度按钮
///
- (void)_definitionItemWasTapped:(SJEdgeControlButtonItem *)definitionItem {
    self.defaultSwitchVideoDefinitionControlLayer.assets = self.definitionURLAssets;
    [self.switcher switchControlLayerForIdentifier:SJControlLayer_SwitchVideoDefinition];
}

///
/// 点击了返回按钮
///
- (void)_backButtonWasTapped {
    if ( self.isFullscreen && ![self _whetherToSupportOnlyOneOrientation] ) {
        [self rotate];
    }
    else if ( self.isFitOnScreen ) {
        self.fitOnScreen = NO;
    }
    else {
        UIViewController *vc = [self.view lookupResponderForClass:UIViewController.class];
        [vc.view endEditing:YES];
        vc.presentingViewController ? [vc dismissViewControllerAnimated:YES completion:nil] :
        [vc.navigationController popViewControllerAnimated:YES];
    }
}

#pragma mark -

///
/// 选择了一个清晰度
///
- (void)controlLayer:(SJSwitchVideoDefinitionControlLayer *)controlLayer didSelectAsset:(SJVideoPlayerURLAsset *)asset {
    SJVideoPlayerURLAsset *selected = self.URLAsset;
    SJVideoDefinitionSwitchingInfo *info = self.definitionSwitchingInfo;
    if ( info.switchingAsset != nil && info.status != SJDefinitionSwitchStatusFailed ) {
        selected = info.switchingAsset;
    }
    
    if ( asset != selected ) {
        [self sj_switchingInfoObserver];
        [self switchVideoDefinition:asset];
    }
    [self.switcher switchToPreviousControlLayer];
}

- (void)closeGuideControlLayer:(id<SJControlLayer>)controlLayer{
    [self.switcher switchControlLayerForIdentifier:SJControlLayer_Edge];
    if (self.guideControlLayerHidden) {
        self.guideControlLayerHidden();
    }
}

///
/// 点击了控制层空白区域
///
- (void)tappedBlankAreaOnTheControlLayer:(id<SJControlLayer>)controlLayer {
    [self.switcher switchToPreviousControlLayer];
}

///
/// 点击了控制层上的返回按钮
///
- (void)backItemWasTappedForControlLayer:(id<SJControlLayer>)controlLayer {
    NSLog(@"video ===== backItemWasTappedForControlLayer");
    
    /// 直播转码层返回直接返回
    if ([controlLayer isKindOfClass:[MRKVideoPLayPrepareLayer class]] || self.playbackController.currentTime <= 0) {
        [self _backButtonWasTapped];
        return;
    }
    
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wundeclared-selector"
    if ([self.controllerVC respondsToSelector:@selector(controllerPopBack:)]) {
        [self.controllerVC performSelector:@selector(controllerPopBack:) withObject:controlLayer];
    }
#pragma clang diagnostic pop
}

///
/// 返回训练报告
///
- (void)backItemWasTappedToReportController:(id<SJControlLayer>)controlLayer{
    [self.trainManager skipReportVC];
}

///
/// 点击了控制层上的刷新按钮
///
- (void)reloadItemWasTappedForControlLayer:(id<SJControlLayer>)controlLayer {
    [self refresh];
    [self.switcher switchControlLayerForIdentifier:SJControlLayer_Edge];
}

///
/// 点击了控制层开通会员
///
- (void)openCourseVIPForControlLayer:(id<SJControlLayer>)controlLayer withData:(nullable id)data{
    [[RouteManager sharedInstance]  skipVIP];
}

#pragma mark -

- (void)setSmallViewFloatingController:(nullable id<SJSmallViewFloatingController>)smallViewFloatingController {
    [super setSmallViewFloatingController:smallViewFloatingController];
    [self _initializeSmallViewFloatingControllerObserverIfNeeded:smallViewFloatingController];
}

#pragma mark -

@synthesize defaultEdgeControlLayer = _defaultEdgeControlLayer;
- (MrkVerticalEdgeControlLayer *)defaultEdgeControlLayer {
    if ( !_defaultEdgeControlLayer ) {
        _defaultEdgeControlLayer = [MrkVerticalEdgeControlLayer new];
//        _defaultEdgeControlLayer.delegate = self;
    }
    return _defaultEdgeControlLayer;
}

@synthesize defaultMoreSettingControlLayer = _defaultMoreSettingControlLayer;
- (SJMoreSettingControlLayer *)defaultMoreSettingControlLayer {
    if ( !_defaultMoreSettingControlLayer ) {
        _defaultMoreSettingControlLayer = [SJMoreSettingControlLayer new];
        _defaultMoreSettingControlLayer.delegate = self;
    }
    return _defaultMoreSettingControlLayer;
}

@synthesize defaultLoadFailedControlLayer = _defaultLoadFailedControlLayer;
- (SJLoadFailedControlLayer *)defaultLoadFailedControlLayer {
    if ( !_defaultLoadFailedControlLayer ) {
        _defaultLoadFailedControlLayer = [SJLoadFailedControlLayer new];
        _defaultLoadFailedControlLayer.delegate = self;
    }
    return _defaultLoadFailedControlLayer;
}

@synthesize defaultNotReachableControlLayer = _defaultNotReachableControlLayer;
- (SJNotReachableControlLayer *)defaultNotReachableControlLayer {
    if ( !_defaultNotReachableControlLayer ) {
        _defaultNotReachableControlLayer = [[SJNotReachableControlLayer alloc] initWithFrame:self.view.bounds];
        _defaultNotReachableControlLayer.delegate = self;
    }
    return _defaultNotReachableControlLayer;
}

@synthesize defaultSmallViewControlLayer = _defaultSmallViewControlLayer;
- (SJFloatSmallViewControlLayer *)defaultFloatSmallViewControlLayer {
    if ( _defaultSmallViewControlLayer == nil ) {
        _defaultSmallViewControlLayer = [[SJFloatSmallViewControlLayer alloc] initWithFrame:self.view.bounds];
    }
    return _defaultSmallViewControlLayer;
}

@synthesize defaultSwitchVideoDefinitionControlLayer = _defaultSwitchVideoDefinitionControlLayer;
- (SJSwitchVideoDefinitionControlLayer *)defaultSwitchVideoDefinitionControlLayer {
    if ( _defaultSwitchVideoDefinitionControlLayer == nil ) {
        _defaultSwitchVideoDefinitionControlLayer = [[SJSwitchVideoDefinitionControlLayer alloc] initWithFrame:self.view.bounds];
        _defaultSwitchVideoDefinitionControlLayer.delegate = self;
    }
    return _defaultSwitchVideoDefinitionControlLayer;
}

@synthesize vipHolderLayer = _vipHolderLayer;
- (MRKVipHolderLayer *)vipHolderLayer {
    if ( _vipHolderLayer == nil ) {
        _vipHolderLayer = [[MRKVipHolderLayer alloc] initWithFrame:self.view.bounds];
        _vipHolderLayer.delegate = self;
    }
    return _vipHolderLayer;
}

@synthesize videoPlayDoneLayer = _videoPlayDoneLayer;
- (MRKVideoPlayDoneLayer *)videoPlayDoneLayer {
    if ( _videoPlayDoneLayer == nil ) {
        _videoPlayDoneLayer = [[MRKVideoPlayDoneLayer alloc] initWithFrame:self.view.bounds];
        _videoPlayDoneLayer.delegate = self;
    }
    return _videoPlayDoneLayer;
}

@synthesize videoPlayPrepareLayer = _videoPlayPrepareLayer;
- (MRKVideoPLayPrepareLayer *)videoPlayPrepareLayer {
    if ( _videoPlayPrepareLayer == nil ) {
        _videoPlayPrepareLayer = [[MRKVideoPLayPrepareLayer alloc] initWithFrame:self.view.bounds];
        _videoPlayPrepareLayer.delegate = self;
    }
    return _videoPlayPrepareLayer;
}


@synthesize sj_switchingInfoObserver = _sj_switchingInfoObserver;
- (SJVideoDefinitionSwitchingInfoObserver *)sj_switchingInfoObserver {
    if ( _sj_switchingInfoObserver == nil ) {
        _sj_switchingInfoObserver = [self.definitionSwitchingInfo getObserver];
        __weak typeof(self) _self = self;
        _sj_switchingInfoObserver.statusDidChangeExeBlock = ^(SJVideoDefinitionSwitchingInfo * _Nonnull info) {
            __strong typeof(_self) self = _self;
            if ( !self ) return ;
            if ( self.isDisabledDefinitionSwitchingPrompt ) return;
            switch ( info.status ) {
                case SJDefinitionSwitchStatusUnknown:
                    break;
                case SJDefinitionSwitchStatusSwitching: {
                    [self.prompt show:[NSAttributedString sj_UIKitText:^(id<SJUIKitTextMakerProtocol>  _Nonnull make) {
                        make.append([NSString stringWithFormat:@"%@【%@】请稍后...", SJVideoPlayerConfigurations.shared.localizedStrings.definitionSwitchingPrompt, info.switchingAsset.definition_fullName]);
                        make.textColor(UIColor.whiteColor);
                        make.font([UIFont systemFontOfSize:14]);
                    }] duration:-1];
                }
                    break;
                case SJDefinitionSwitchStatusFinished: {
                    [self.prompt hidden];
                    [self.prompt show:[NSAttributedString sj_UIKitText:^(id<SJUIKitTextMakerProtocol>  _Nonnull make) {
                        if (UserInfo.isMember) {
                            NSString *vipTip = nil;
                            UIImage *icon = nil;
                            int vipType = UserInfo.vipType;
                            switch (vipType) {
                                case 10: {
                                    vipTip = @"尊贵的趣练VIP，已为您切换至";
                                    icon = [UIImage imageNamed:@"record_vip"];
                                }  break;
                                case 30:{
                                    vipTip = @"尊贵的绝影会员，已为您切换至";
                                    icon = [UIImage imageNamed:@"record_xenjoy"];
                                }  break;
                                default:
                                    vipTip = SJVideoPlayerConfigurations.shared.localizedStrings.definitionSwitchSuccessfullyPrompt;
                                    break;
                            }
                            
                            if (icon != nil) {
                                make.appendImage(^(id<SJUTImageAttachment>  _Nonnull make) {
                                    make.image = icon;
                                    make.bounds = CGRectMake(0, -5, 23, 20);
                                });
                            }
                            make.append([NSString stringWithFormat:@"%@【%@】画质", vipTip, info.currentPlayingAsset.definition_fullName]);
                            make.textColor(UIColorHex(#F4C8A1));
                        }else{
                            make.append([NSString stringWithFormat:@"%@", SJVideoPlayerConfigurations.shared.localizedStrings.definitionSwitchSuccessfullyPrompt]);
                            make.textColor(UIColor.whiteColor);
                        }
                        make.font([UIFont systemFontOfSize:14]);
                        make.alignment(NSTextAlignmentCenter);
                    }] duration:3];
                }
                    break;
                case SJDefinitionSwitchStatusFailed: {
                    [self.prompt hidden];
                    [self.prompt show:[NSAttributedString sj_UIKitText:^(id<SJUIKitTextMakerProtocol>  _Nonnull make) {
                        make.append(SJVideoPlayerConfigurations.shared.localizedStrings.definitionSwitchFailedPrompt);
                        make.textColor(UIColor.whiteColor);
                        make.font([UIFont systemFontOfSize:14]);
                    }]];
                }
                    break;
            }
            [self _updateContentForDefinitionItemIfNeeded];
        };
    }
    return _sj_switchingInfoObserver;
}

- (id<SJSmallViewFloatingController>)smallViewFloatingController {
    id<SJSmallViewFloatingController> smallViewFloatingController = [super smallViewFloatingController];
    [self _initializeSmallViewFloatingControllerObserverIfNeeded:smallViewFloatingController];
    return smallViewFloatingController;
}

#pragma mark -

// - initialize -

- (void)_observeNotifies {
    [NSNotificationCenter.defaultCenter addObserver:self selector:@selector(_switchControlLayerIfNeeded) name:SJVideoPlayerPlaybackTimeControlStatusDidChangeNotification object:self];
    [NSNotificationCenter.defaultCenter addObserver:self selector:@selector(_resumeOrStopTimeoutTimer) name:SJVideoPlayerPlaybackTimeControlStatusDidChangeNotification object:self];
    [NSNotificationCenter.defaultCenter addObserver:self selector:@selector(_switchControlLayerIfNeeded) name:SJVideoPlayerAssetStatusDidChangeNotification object:self];
    [NSNotificationCenter.defaultCenter addObserver:self selector:@selector(_switchControlLayerIfNeeded) name:SJVideoPlayerPlaybackDidFinishNotification object:self];
    [NSNotificationCenter.defaultCenter addObserver:self selector:@selector(_configurationsDidUpdate) name:SJVideoPlayerConfigurationsDidUpdateNotification object:nil];
}

- (void)_initializeSwitcher {
    _switcher = [[SJControlLayerSwitcher alloc] initWithPlayer:self];
    __weak typeof(self) _self = self;
    _switcher.resolveControlLayer = ^id<SJControlLayer> _Nullable(SJControlLayerIdentifier identifier) {
        __strong typeof(_self) self = _self;
        if ( !self ) return nil;
        if ( identifier == SJControlLayer_Edge )
            return self.defaultEdgeControlLayer;
        else if ( identifier == SJControlLayer_NotReachableAndPlaybackStalled )
            return self.defaultNotReachableControlLayer;
        else if ( identifier == SJControlLayer_More )
            return self.defaultMoreSettingControlLayer;
        else if ( identifier == SJControlLayer_LoadFailed )
            return self.defaultLoadFailedControlLayer;
        else if ( identifier == SJControlLayer_FloatSmallView )
            return self.defaultFloatSmallViewControlLayer;
        else if ( identifier == SJControlLayer_SwitchVideoDefinition )
            return self.defaultSwitchVideoDefinitionControlLayer;
        else if ( identifier == SJControlLayer_VideoVipHolder )
            return self.vipHolderLayer;
        else if ( identifier == SJControlLayer_VideoPlayDoneHolder )
            return self.videoPlayDoneLayer;
        else if ( identifier == SJControlLayer_VideoPlayPrepareHolder )
            return self.videoPlayPrepareLayer;
        
        return nil;
    };
}

- (void)_initializeSwitcherObserver {
    _sj_switcherObserver = [_switcher getObserver];
    __weak typeof(self) _self = self;
    _sj_switcherObserver.playerWillBeginSwitchControlLayer = ^(id<SJControlLayerSwitcher>  _Nonnull switcher, id<SJControlLayer>  _Nonnull controlLayer) {
        __strong typeof(_self) self = _self;
        if ( !self ) return;
        if ( [controlLayer respondsToSelector:@selector(setHiddenBackButtonWhenOrientationIsPortrait:)] ) {
            [(MrkVerticalEdgeControlLayer *)controlLayer setHiddenBackButtonWhenOrientationIsPortrait:self.defaultEdgeControlLayer.isHiddenBackButtonWhenOrientationIsPortrait];
        }
    };
    ///切换层级结束后
    _sj_switcherObserver.playerDidEndSwitchControlLayer = ^(id<SJControlLayerSwitcher>  _Nonnull switcher, id<SJControlLayer>  _Nonnull controlLayer) {
        __strong typeof(_self) self = _self;
        if ( !self ) return;
        
        ///切换到MRKVideoPlayDoneLayer, 视频中止stop
        if ([controlLayer isKindOfClass:[MRKVideoPlayDoneLayer class]]) {
            // [self pauseForAlert];
            self.showPalyDoneLayer = YES;
        }
    };
}

- (void)_initializeSettingsObserver {
    [NSNotificationCenter.defaultCenter addObserver:self selector:@selector(_configurationsDidUpdate) name:SJVideoPlayerConfigurationsDidUpdateNotification object:nil];
}

- (void)_initializeSmallViewFloatingControllerObserverIfNeeded:(nullable id<SJSmallViewFloatingController>)smallViewFloatingController {
    if ( _sj_smallViewFloatingControllerObserver.controller != smallViewFloatingController ) {
        _sj_smallViewFloatingControllerObserver = [smallViewFloatingController getObserver];
        __weak typeof(self) _self = self;
        _sj_smallViewFloatingControllerObserver.onAppearChanged = ^(id<SJSmallViewFloatingController>  _Nonnull controller) {
            __strong typeof(_self) self = _self;
            if ( !self ) return ;
            if ( controller.isAppeared ) {
                if ( self.switcher.currentIdentifier != SJControlLayer_FloatSmallView ) {
                    [self.controlLayerDataSource.controlView removeFromSuperview];
                    [self.switcher switchControlLayerForIdentifier:SJControlLayer_FloatSmallView];
                }
            }
            else {
                if ( self.switcher.currentIdentifier == SJControlLayer_FloatSmallView ) {
                    [self.controlLayerDataSource.controlView removeFromSuperview];
                    [self.switcher switchControlLayerForIdentifier:SJControlLayer_Edge];
                }
            }
        };
    }
}

- (void)_configurationsDidUpdate {
    if ( self.presentView.placeholderImageView.image == nil )
        self.presentView.placeholderImageView.image = SJVideoPlayerConfigurations.shared.resources.placeholder;
    
}

// 播放器当前是否只支持一个方向
- (BOOL)_whetherToSupportOnlyOneOrientation {
    if ( self.rotationManager.autorotationSupportedOrientations == SJOrientationMaskPortrait ) return YES;
    if ( self.rotationManager.autorotationSupportedOrientations == SJOrientationMaskLandscapeLeft ) return YES;
    if ( self.rotationManager.autorotationSupportedOrientations == SJOrientationMaskLandscapeRight ) return YES;
    return NO;
}

- (void)_resumeOrStopTimeoutTimer {
    if ( self.isBuffering || self.isEvaluating ) {
        if ( SJReachability.shared.networkStatus == SJNetworkStatus_NotReachable && _sj_timeoutTimer == nil ) {
            __weak typeof(self) _self = self;
            _sj_timeoutTimer = [NSTimer sj_timerWithTimeInterval:3 repeats:YES usingBlock:^(NSTimer * _Nonnull timer) {
                [timer invalidate];
                __strong typeof(_self) self = _self;
                if ( !self ) return;
#ifdef DEBUG
                NSLog(@"%d \t %s \t 网络超时, 切换到无网控制层!", (int)__LINE__, __func__);
#endif
                self.sj_isTimeout = YES;
                [self _switchControlLayerIfNeeded];
            }];
            [_sj_timeoutTimer sj_fire];
            [NSRunLoop.mainRunLoop addTimer:_sj_timeoutTimer forMode:NSRunLoopCommonModes];
        }
    }
    else if ( _sj_timeoutTimer != nil ) {
        [_sj_timeoutTimer invalidate];
        _sj_timeoutTimer = nil;
        self.sj_isTimeout = NO;
    }
    
}

- (void)_switchControlLayerIfNeeded {
    
    if ( self.assetStatus == SJAssetStatusFailed ) {
        // 资源出错时
        // - 发生错误时, 切换到加载失败控制层
        //
        [self.switcher switchControlLayerForIdentifier:SJControlLayer_LoadFailed];
    } else if ( self.sj_isTimeout ) {
        // 当处于缓冲状态时
        // - 当前如果没有网络, 则切换到无网空制层
        //
        [self.switcher switchControlLayerForIdentifier:SJControlLayer_NotReachableAndPlaybackStalled];
    } else {
        if ( self.switcher.currentIdentifier == SJControlLayer_LoadFailed || self.switcher.currentIdentifier == SJControlLayer_NotReachableAndPlaybackStalled ) {
            [self.switcher switchControlLayerForIdentifier:SJControlLayer_Edge];
        }
    }
}

- (void)_initializeAppearManagerObserver {
    _sj_appearManagerObserver = [self.controlLayerAppearManager getObserver];
    
    __weak typeof(self) _self = self;
    _sj_appearManagerObserver.onAppearChanged = ^(id<SJControlLayerAppearManager>  _Nonnull mgr) {
        __strong typeof(_self) self = _self;
        if ( !self ) return ;
        // refresh edge button items
        if ( self.switcher.currentIdentifier == SJControlLayer_Edge ) {
            [self _updateAppearStateForMoteItemIfNeeded];
            [self _updateContentForDefinitionItemIfNeeded];
        }
    };
}

- (void)_initializeReachabilityObserver {
    _sj_reachabilityObserver = [self.reachability getObserver];
    __weak typeof(self) _self = self;
    _sj_reachabilityObserver.networkStatusDidChangeExeBlock = ^(id<SJReachability>  _Nonnull r) {
        __strong typeof(_self) self = _self;
        if ( !self ) return;
        if ( r.networkStatus == SJNetworkStatus_NotReachable ) {
            [self _resumeOrStopTimeoutTimer];
        }
        else if ( self.switcher.currentIdentifier == SJControlLayer_NotReachableAndPlaybackStalled ) {
#ifdef DEBUG
            NSLog(@"%d \t %s \t 网络恢复, 将刷新资源, 使播放器恢复播放!", (int)__LINE__, __func__);
#endif
            [self refresh];
        }
    };
}

- (void)_updateContentForDefinitionItemIfNeeded {
    if ( self.definitionURLAssets.count != 0 ) {
        SJVideoPlayerURLAsset *asset = self.URLAsset;
        
        UIView *view = self.definitionItem.customView;
        if (view && [view isKindOfClass:[MRKVideoDefinitionView class]]) {
            MRKVideoDefinitionView *vi  = (MRKVideoDefinitionView *)view;
            vi.definition = asset.definition_fullName;
        }
        [self.defaultEdgeControlLayer.topAdapter reload];
    }
}

- (void)_updateAppearStateForMoteItemIfNeeded {
    
}

- (void)switchControlLayerWithIdentifier:(SJControlLayerIdentifier)identifier {
    [self.switcher switchControlLayerForIdentifier:identifier];
}


@end


@implementation MRKVertiaclVideoPlayer (CommonSettings)
+ (void (^)(void (^ _Nonnull)(SJVideoPlayerConfigurations * _Nonnull)))update {
    return SJVideoPlayerConfigurations.update;
}

+ (void (^)(NSBundle * _Nonnull))setLocalizedStrings {
    return ^(NSBundle *bundle) {
        SJVideoPlayerConfigurations.update(^(SJVideoPlayerConfigurations * _Nonnull configs) {
            [configs.localizedStrings setFromBundle:bundle];
        });
    };
}

+ (void (^)(void (^ _Nonnull)(id<SJVideoPlayerLocalizedStrings> _Nonnull)))updateLocalizedStrings {
    return ^(void(^block)(id<SJVideoPlayerLocalizedStrings> strings)) {
        SJVideoPlayerConfigurations.update(^(SJVideoPlayerConfigurations * _Nonnull configs) {
            block(configs.localizedStrings);
        });
    };
}

+ (void (^)(void (^ _Nonnull)(id<SJVideoPlayerControlLayerResources> _Nonnull)))updateResources {
    return ^(void(^block)(id<SJVideoPlayerControlLayerResources> resources)) {
        SJVideoPlayerConfigurations.update(^(SJVideoPlayerConfigurations * _Nonnull configs) {
            block(configs.resources);
        });
    };
}
@end



#pragma mark -
@implementation MRKVertiaclVideoPlayer (SJExtendedSwitchVideoDefinitionControlLayer)

- (void)setDefinitionURLAssets:(nullable NSArray<SJVideoPlayerURLAsset *> *)definitionURLAssets {
    objc_setAssociatedObject(self, @selector(definitionURLAssets), definitionURLAssets, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    
    SJEdgeControlButtonItemAdapter *adapter = self.defaultEdgeControlLayer.topAdapter;
//    if ( definitionURLAssets != nil ) {
//        if ( self.definitionItem == nil ) {
//
//            MRKVideoDefinitionView *view = [[MRKVideoDefinitionView alloc] init];
//            view.frame = CGRectMake(0, 0, 60, 30);
//            self.definitionItem = [[SJEdgeControlButtonItem alloc] initWithCustomView:view tag:SJEdgeControlLayerTopItem_Definition];
//            [self.definitionItem addAction:[SJEdgeControlButtonItemAction actionWithTarget:self action:@selector(_definitionItemWasTapped:)]];
//
//            [adapter insertItem:self.definitionItem rearItem:SJEdgeControlLayerTopItem_Device];
//        }
//        [self _updateContentForDefinitionItemIfNeeded];
//    } else {
        self->_defaultSwitchVideoDefinitionControlLayer = nil;
        self.definitionItem = nil;
        [adapter removeItemForTag:SJEdgeControlLayerTopItem_Definition];
        
        [self.switcher deleteControlLayerForIdentifier:SJControlLayer_SwitchVideoDefinition];
        [self.defaultEdgeControlLayer.topAdapter reload];
//    }
}

- (nullable NSArray<SJVideoPlayerURLAsset *> *)definitionURLAssets {
    return objc_getAssociatedObject(self, _cmd);
}

- (void)setDisabledDefinitionSwitchingPrompt:(BOOL)disabledDefinitionSwitchingPrompt {
    objc_setAssociatedObject(self, @selector(isDisabledDefinitionSwitchingPrompt), @(disabledDefinitionSwitchingPrompt), OBJC_ASSOCIATION_RETAIN_NONATOMIC);
}

- (BOOL)isDisabledDefinitionSwitchingPrompt {
    return [objc_getAssociatedObject(self, _cmd) boolValue];
}

@end




@implementation MRKVertiaclVideoPlayer (RotationOrFitOnScreen)
- (void)setAutomaticallyPerformRotationOrFitOnScreen:(BOOL)automaticallyPerformRotationOrFitOnScreen {
    self.defaultEdgeControlLayer.automaticallyPerformRotationOrFitOnScreen = automaticallyPerformRotationOrFitOnScreen;
}
- (BOOL)automaticallyPerformRotationOrFitOnScreen {
    return self.defaultEdgeControlLayer.automaticallyPerformRotationOrFitOnScreen;
}

- (void)setOnlyUsedFitOnScreen:(BOOL)onlyUsedFitOnScreen {
    self.defaultEdgeControlLayer.onlyUsedFitOnScreen = onlyUsedFitOnScreen;
}
- (BOOL)onlyUsedFitOnScreen {
    return self.defaultEdgeControlLayer.onlyUsedFitOnScreen;
}

- (void)setUsesFitOnScreenFirst:(BOOL)usesFitOnScreenFirst {
    self.defaultEdgeControlLayer.usesFitOnScreenFirst = usesFitOnScreenFirst;
}
- (BOOL)usesFitOnScreenFirst {
    return self.defaultEdgeControlLayer.usesFitOnScreenFirst;
}
@end

NS_ASSUME_NONNULL_END
