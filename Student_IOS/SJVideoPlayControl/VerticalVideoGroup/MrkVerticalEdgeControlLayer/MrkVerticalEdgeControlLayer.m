//
//  MrkVerticalEdgeControlLayer.m
//  Student_IOS
//
//  Created by merit on 2022/7/18.
//

#import "MrkVerticalEdgeControlLayer.h"
#import "Masonry.h"
#import "SJBaseVideoPlayer.h"
#import "SJTimerControl.h"

#import "SJEdgeControlLayer.h"
#import "SJVideoPlayerURLAsset+SJControlAdd.h"
#import "SJDraggingProgressPopupView.h"
#import "UIView+SJAnimationAdded.h"
#import "SJVideoPlayerConfigurations.h"
#import "SJProgressSlider.h"
#import "SJLoadingView.h"
#import "SJDraggingObservation.h"
#import "SJScrollingTextMarqueeView.h"
#import "SJFullscreenModeStatusBar.h"
#import "SJSpeedupPlaybackPopupView.h"
#import <objc/message.h>


#import "MRKRenderModel.h"
#import "MRKFatBurningModel.h"
#import "MrkVerticalProgressTipView.h"
#import "MRKVideoBTConnectView.h"
#import "SpeechSynthesizerManager.h"
#import "PopoverView.h"

#pragma mark - Top

@interface MrkVerticalEdgeControlLayer ()<SJProgressSliderDelegate> {
    CGSize _previousSize;
}
@property (nonatomic, strong) MRKVideoBTConnectView *BTConnectView;

@property (nonatomic, weak, nullable) SJBaseVideoPlayer *videoPlayer;
@property (nonatomic, strong, readonly) SJProgressSlider *bottomProgressIndicator;

// 固定左上角的返回按钮. 设置`fixesBackItem`后显示
@property (nonatomic, strong, readonly) UIButton *fixedBackButton;
@property (nonatomic, strong, readonly) SJEdgeControlButtonItem *backItem;
@property (nonatomic, strong) MrkVerticalProgressTipView *progressTipView;

@property (nonatomic, strong, nullable) id<SJReachabilityObserver> reachabilityObserver;
@property (nonatomic, strong, readonly) SJTimerControl *dateTimerControl API_AVAILABLE(ios(11.0)); // refresh date for custom status bar

@property (nonatomic, assign) BOOL hasToastAlert; ///有无弹过指令窗
///
///
@property (nonatomic, strong) TrainingShowData *tyModel;
@property (nonatomic, strong) PopoverView *popoverView;             ///指令下发引导指示窗口
@end

@implementation MrkVerticalEdgeControlLayer
@synthesize restarted = _restarted;
@synthesize draggingProgressPopupView = _draggingProgressPopupView;
@synthesize draggingObserver = _draggingObserver;


- (MrkVerticalProgressTipView *)progressTipView{
    if (!_progressTipView) {
        _progressTipView =  [[MrkVerticalProgressTipView alloc] init];
        _progressTipView.hidden = YES;
    }
    return _progressTipView;
}



- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if ( !self ) return nil;
    
    self.topHeight = 50;
    
    self.bottomDataHeight = 80;
    self.bottomHeight = 80;
    _bottomProgressIndicatorHeight = 1;
    _automaticallyPerformRotationOrFitOnScreen = YES;
    
    [self _setupView];
    self.autoAdjustTopSpacing = YES;
    self.hiddenBottomProgressIndicator = YES;
    
    self.rightMargin = 10;
    self.rightWidth = 50;
    
    
    
    ///添加超然脂率
    [self addSubview:self.progressTipView];
    [self.progressTipView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.mas_left).offset(16);
        if (@available(iOS 11.0, *)) {
            make.bottom.equalTo(self.mas_safeAreaLayoutGuideBottom).offset(-80);
        }else{
            make.bottom.equalTo(self.mas_bottom).offset(-80);
        }
        make.height.mas_equalTo(72);
//        make.width.mas_equalTo(200);
    }];
    [self bringSubviewToFront:self.progressTipView];
    
    
    return self;
}

- (void)dealloc {
    [NSNotificationCenter.defaultCenter removeObserver:self];
    [SpeechSynthesizerManager.shared stopSpeaking];
}

///
/// controlLayerDidAppear
///
- (void)controlLayerDidAppear{
    
}

///
/// controlLayerDidDisappear
///
- (void)controlLayerDidDisappear{
    
}

- (void)layoutSubviews {
    [super layoutSubviews];
    
    if ( !CGSizeEqualToSize(_previousSize, self.bounds.size) ) {
        if (@available(iOS 11.0, *)) {
            [self _updateAppearStateForCustomStatusBar];
        }
        [self _updateLayoutForBottomProgressIndicator];
    }
    _previousSize = self.bounds.size;
}






#pragma mark -

///
/// 切换器(player.switcher)重启该控制层
///
- (void)restartControlLayer {
    _restarted = YES;
    sj_view_makeAppear(self.controlView, YES);
    
    [self _showOrHiddenLoadingView];
    [self _updateAppearStateForContainerViews];
    [self _reloadAdaptersIfNeeded];
}

///
/// 控制层退场
///
- (void)exitControlLayer {
    _restarted = NO;
    
    sj_view_makeDisappear(self.controlView, YES, ^{
        if ( !self->_restarted ) [self.controlView removeFromSuperview];
    });
    
    sj_view_makeDisappear(_topContainerView, YES);
    sj_view_makeDisappear(_leftContainerView, YES);
    sj_view_makeDisappear(_bottomContainerView, YES);
    sj_view_makeDisappear(_rightContainerView, YES);
    sj_view_makeDisappear(_draggingProgressPopupView, YES);
    sj_view_makeDisappear(_centerContainerView, YES);
    
    sj_view_makeDisappear(_topDataView, YES);
    sj_view_makeDisappear(_bottomDataView, YES);
}






#pragma mark - item actions

- (void)_fixedBackButtonWasTapped {
    [self.backItem performActions];
}

- (void)_backItemWasTapped {
    if ( [self.delegate respondsToSelector:@selector(backItemWasTappedForControlLayer:)] ) {
        [self.delegate backItemWasTappedForControlLayer:self];
    }
}

- (void)_lockItemWasTapped {
//    self.videoPlayer.lockedScreen = !self.videoPlayer.isLockedScreen;
}

- (void)_playItemWasTapped {
    _videoPlayer.isPaused ? [self.videoPlayer play] : [self.videoPlayer pauseForUser];
}

- (void)_fullItemWasTapped {
    if ( _onlyUsedFitOnScreen ) {
        [_videoPlayer setFitOnScreen:!_videoPlayer.isFitOnScreen];
        return;
    }
    
    if ( _usesFitOnScreenFirst && !_videoPlayer.isFitOnScreen ) {
        [_videoPlayer setFitOnScreen:YES];
        return;
    }
    
    [_videoPlayer rotate];
}

- (void)_replayItemWasTapped {
    [_videoPlayer replay];
}

- (void)_autoItemWasTapped {
    self.videoPlayer.useLessonPlanControl = !self.videoPlayer.useLessonPlanControl;
    
    NSString *tipStr = _videoPlayer.isUseLessonPlanControl ? @"开启AI调节" : @"关闭AI调节";
    [self.videoPlayer.prompt show:[NSAttributedString sj_UIKitText:^(id<SJUIKitTextMakerProtocol>  _Nonnull make) {
        make.append(tipStr);
        make.font([UIFont systemFontOfSize:14 weight:UIFontWeightMedium]);
        make.textColor([UIColor whiteColor]);
    }] duration:3];
}

- (void)_deviceItemWasTapped {
    if ( [self.delegate respondsToSelector:@selector(connectDeviceWasTappedForControlLayer:)] ) {
        [self.delegate connectDeviceWasTappedForControlLayer:self];
    }
}

/// 弹幕按钮点击
- (void)_danmuItemWasTapped {
    self.videoPlayer.closeBarrageControl = !self.videoPlayer.closeBarrageControl;
}

- (void)_tvItemWasTapped {
    if ( [self.delegate respondsToSelector:@selector(linkTVItemWasTappedForControlLayer:)] ) {
        [self.delegate linkTVItemWasTappedForControlLayer:self];
    }
}

- (void)_shareItemWasTapped {
    if ( [self.delegate respondsToSelector:@selector(shareItemWasTappedForControlLayer:)] ) {
        [self.delegate shareItemWasTappedForControlLayer:self];
    }
}











#pragma mark - slider delegate methods

- (void)sliderWillBeginDragging:(SJProgressSlider *)slider {
    if ( _videoPlayer.assetStatus != SJAssetStatusReadyToPlay ) {
        [slider cancelDragging];
        return;
    }
    else if ( _videoPlayer.canSeekToTime && !_videoPlayer.canSeekToTime(_videoPlayer) ) {
        [slider cancelDragging];
        return;
    }
    
    [self _willBeginDragging];
}

- (void)slider:(SJProgressSlider *)slider valueDidChange:(CGFloat)value {
    if ( slider.isDragging ) [self _didMove:value];
}

- (void)sliderDidEndDragging:(SJProgressSlider *)slider {
    [self _endDragging];
}





#pragma mark - player delegate methods

- (UIView *)controlView {
    return self;
}

- (void)installedControlViewToVideoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer {
    _videoPlayer = videoPlayer;
    SJNSLog(@"");

    sj_view_makeDisappear(_topContainerView, NO);
    sj_view_makeDisappear(_leftContainerView, NO);
    sj_view_makeDisappear(_bottomContainerView, NO);
    sj_view_makeDisappear(_rightContainerView, NO);
    sj_view_makeDisappear(_centerContainerView, NO);
    
    sj_view_makeDisappear(_topDataView, NO);
    sj_view_makeDisappear(_bottomDataView, NO);
    
    [self _reloadSizeForBottomTimeLabel];
    [self _updateContentForBottomCurrentTimeItemIfNeeded];
    [self _updateContentForBottomDurationItemIfNeeded];
    
    _reachabilityObserver = [videoPlayer.reachability getObserver];
    __weak typeof(self) _self = self;
    _reachabilityObserver.networkSpeedDidChangeExeBlock = ^(id<SJReachability> r) {
        __strong typeof(_self) self = _self;
        if ( !self ) return;
        [self _updateNetworkSpeedStrForLoadingView];
    };
}

///
/// 当播放器尝试自动隐藏控制层之前 将会调用这个方法
///
- (BOOL)controlLayerOfVideoPlayerCanAutomaticallyDisappear:(__kindof SJBaseVideoPlayer *)videoPlayer {
    return YES;
}




///视图切换
- (void)controlLayerNeedAppear:(__kindof SJBaseVideoPlayer *)videoPlayer {
    if ( videoPlayer.isLockedScreen )
        return;
    
    SJNSLog(@"");
    [self _updateAppearStateForResidentBackButtonIfNeeded];
    [self _updateAppearStateForContainerViews];
    [self _reloadAdaptersIfNeeded];
    [self _updateContentForBottomCurrentTimeItemIfNeeded];
    [self _updateAppearStateForBottomProgressIndicatorIfNeeded];
    if (@available(iOS 11.0, *)) {
        [self _reloadCustomStatusBarIfNeeded];
    }
}

- (void)controlLayerNeedDisappear:(__kindof SJBaseVideoPlayer *)videoPlayer {
    if ( videoPlayer.isLockedScreen )
        return;
    
    SJNSLog(@"");
    [self _updateAppearStateForResidentBackButtonIfNeeded];
    [self _updateAppearStateForContainerViews];
    [self _updateAppearStateForBottomProgressIndicatorIfNeeded];
}


- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer prepareToPlay:(SJVideoPlayerURLAsset *)asset {
    SJNSLog(@"");
    
    [self _reloadSizeForBottomTimeLabel];
    [self _updateContentForBottomDurationItemIfNeeded];
    [self _updateContentForBottomCurrentTimeItemIfNeeded];
    
    [self _updateContentForBottomProgressIndicatorIfNeeded];
    [self _updateAppearStateForResidentBackButtonIfNeeded];
    [self _updateAppearStateForBottomProgressIndicatorIfNeeded];
    
    [self _reloadAdaptersIfNeeded];
    [self _showOrHiddenLoadingView];
}

- (void)videoPlayerPlaybackStatusDidChange:(__kindof SJBaseVideoPlayer *)videoPlayer {
    SJNSLog(@"");
    
    [self _reloadAdaptersIfNeeded];
    [self _showOrHiddenLoadingView];
    [self _updateContentForBottomCurrentTimeItemIfNeeded];
    [self _updateContentForBottomDurationItemIfNeeded];
    [self _updateContentForBottomProgressIndicatorIfNeeded];
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer pictureInPictureStatusDidChange:(SJPictureInPictureStatus)status API_AVAILABLE(ios(14.0)) {
    SJNSLog(@"");
    [self _updateContentForPictureInPictureItem];
    [self.topAdapter reload];
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer currentTimeDidChange:(NSTimeInterval)currentTime {
    [self _updateContentForBottomCurrentTimeItemIfNeeded];
    [self _updateContentForBottomProgressIndicatorIfNeeded];
    [self _updateCurrentTimeForDraggingProgressPopupViewIfNeeded];
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer durationDidChange:(NSTimeInterval)duration {
    [self _reloadSizeForBottomTimeLabel];
    [self _updateContentForBottomDurationItemIfNeeded];
    [self _updateContentForBottomProgressIndicatorIfNeeded];
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer playableDurationDidChange:(NSTimeInterval)duration {

}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer playbackTypeDidChange:(SJPlaybackType)playbackType {
    SJNSLog(@"");
    
    switch ( playbackType ) {
        case SJPlaybackTypeLIVE: {
            
            [self.bottomAdapter removeAllItems];
            [self.bottomAdapter reload];
        }
            break;
        case SJPlaybackTypeUnknown:
        case SJPlaybackTypeVOD:
        case SJPlaybackTypeFILE: {
            
        }
            break;
        case SJPlaybackTypeREAL:{
          
//            SJEdgeControlButtonItem *coachItem = [_topAdapter itemForTag:SJEdgeControlLayerTopItem_Coach];
//            coachItem.hidden = YES;
//
//            SJEdgeControlButtonItem *danmuItem = [_topAdapter itemForTag:SJEdgeControlLayerTopItem_Danmu];
//            danmuItem.hidden = YES;
            
            SJEdgeControlButtonItem *shareItem = [_topAdapter itemForTag:SJEdgeControlLayerTopItem_Share];
            shareItem.hidden = YES;
            
            [self.topAdapter reload];
        }
            break;
    }
  
    [self _showOrRemoveBottomProgressIndicator];
}

- (BOOL)canTriggerRotationOfVideoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer {
    SJNSLog(@"");
    if ( _onlyUsedFitOnScreen )
        return NO;
    if ( _usesFitOnScreenFirst )
        return videoPlayer.isFitOnScreen;
    
    return YES;
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer willRotateView:(BOOL)isFull {
    SJNSLog(@"");
    [self _updateAppearStateForResidentBackButtonIfNeeded];
    [self _updateAppearStateForContainerViews];
    [self _reloadAdaptersIfNeeded];
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer willFitOnScreen:(BOOL)isFitOnScreen {
    SJNSLog(@"");
    [self _updateAppearStateForResidentBackButtonIfNeeded];
    [self _updateAppearStateForContainerViews];
    [self _reloadAdaptersIfNeeded];
}

/// 是否可以触发播放器的手势
- (BOOL)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer gestureRecognizerShouldTrigger:(SJPlayerGestureType)type location:(CGPoint)location {
    SJEdgeControlButtonItemAdapter *adapter = nil;
    BOOL(^_locationInTheView)(UIView *) = ^BOOL(UIView *container) {
        return CGRectContainsPoint(container.frame, location) && !sj_view_isDisappeared(container);
    };
    
    if ( _locationInTheView(_topContainerView) ) {
        adapter = _topAdapter;
    }
    else if ( _locationInTheView(_topDataView) ) {
        adapter = nil;
    }
    else if ( _locationInTheView(_bottomContainerView) ) {
        adapter = _bottomAdapter;
    }
    else if ( _locationInTheView(_leftContainerView) ) {
        adapter = _leftAdapter;
    }
    else if ( _locationInTheView(_rightContainerView) ) {
        adapter = _rightAdapter;
    }
    else if ( _locationInTheView(_centerContainerView) ) {
        adapter = _centerAdapter;
    }
    if ( !adapter ) return YES;
    
    CGPoint point = [self.controlView convertPoint:location toView:adapter.view];
    if ( !CGRectContainsPoint(adapter.view.frame, point) ) return YES;
    
    SJEdgeControlButtonItem *_Nullable item = [adapter itemAtPoint:point];
    return item != nil ? (item.actions.count == 0)  : YES;
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer panGestureTriggeredInTheHorizontalDirection:(SJPanGestureRecognizerState)state progressTime:(NSTimeInterval)progressTime {
//    switch ( state ) {
//        case SJPanGestureRecognizerStateBegan:
//            [self _willBeginDragging];
//            break;
//        case SJPanGestureRecognizerStateChanged:
//            [self _didMove:progressTime];
//            break;
//        case SJPanGestureRecognizerStateEnded:
//            [self _endDragging];
//            break;
//    }
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer longPressGestureStateDidChange:(SJLongPressGestureRecognizerState)state {
    if ( [(id)self.speedupPlaybackPopupView respondsToSelector:@selector(layoutInRect:gestureState:playbackRate:)] ) {
        if ( state == SJLongPressGestureRecognizerStateBegan ) {
            if ( self.speedupPlaybackPopupView.superview != self ) {
                [self insertSubview:self.speedupPlaybackPopupView atIndex:0];
            }
        }
        [self.speedupPlaybackPopupView layoutInRect:self.frame gestureState:state playbackRate:videoPlayer.rate];
    }
    else {
        switch ( state ) {
            case SJLongPressGestureRecognizerStateChanged: break;
            case SJLongPressGestureRecognizerStateBegan: {
                if ( self.speedupPlaybackPopupView.superview != self ) {
                    [self insertSubview:self.speedupPlaybackPopupView atIndex:0];
                    [self.speedupPlaybackPopupView mas_makeConstraints:^(MASConstraintMaker *make) {
                        make.center.equalTo(self.topAdapter);
                    }];
                }
                self.speedupPlaybackPopupView.rate = videoPlayer.rateWhenLongPressGestureTriggered;
                [self.speedupPlaybackPopupView show];
            }
                break;
            case SJLongPressGestureRecognizerStateEnded: {
                [self.speedupPlaybackPopupView hidden];
            }
                break;
        }
    }
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer presentationSizeDidChange:(CGSize)size {
    if ( _automaticallyPerformRotationOrFitOnScreen && !videoPlayer.isFullscreen && !videoPlayer.isFitOnScreen ) {
        _onlyUsedFitOnScreen = size.width < size.height;
    }
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer SEIData:(id)model {
    
    [self sendInstructionsWithData:model];
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer useLessonPlanControl:(BOOL)useLessonPlanControl{
    [self _reloadDataAdapterIfNeeded];
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer closeBarrageControl:(BOOL)closeBarrageControl{
    [self _reloadTopAdapterIfNeeded];
}

- (void)videoPlayerBluetooth:(__kindof SJBaseVideoPlayer *)videoPlayer andTymodel:(id)model{
    ///设备蓝牙数据处理
    [self dataAdapterUpdateWithModel:model];
}

- (void)videoPlayerBluetooth:(__kindof SJBaseVideoPlayer *)videoPlayer andRdmodel:(id)model{
    ///心率蓝牙数据处理
}

- (void)videoPlayerBluetooth:(__kindof SJBaseVideoPlayer *)videoPlayer connectDevice:(DEVICE_CONNECT_STATUS)status{
    
    [self _updateConnectForTopDeviceItemIfNeeded];
    [self _reloadDataAdapterIfNeeded];
    
    ///隐藏连接弹窗
    if (status == DeviceConnected || status == DeviceAutoConnecting){
        if (self.BTConnectView) {
            [self.BTConnectView hide];
            self.BTConnectView = nil;
        }
    }
}

///蓝牙断连/**设备断连弹窗*/
- (void)videoPlayerDisconnectBluetooth:(__kindof SJBaseVideoPlayer *)videoPlayer{
    if (self.BTConnectView) {
        [self.BTConnectView hide];
        self.BTConnectView = nil;
    }
    
    self.BTConnectView = [MRKVideoBTConnectView build];
    @weakify(self);
    self.BTConnectView.selectBlock = ^(__kindof AlertBaseView *alertView, NSInteger index) {
        @strongify(self);
        [alertView hide];
        if (index == 1) {
            [self _deviceItemWasTapped];
        }
    };
    [self.BTConnectView showIn:self];
}






- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer andCommandModel:(id)model{
    [self sendInstructionsWithData:model];
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer receiveDanmu:(id)model{
   
}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer type:(NSInteger)type LIVERankData:(id)model{

}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer type:(NSInteger)type VODRankData:(id)model{

}

- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer type:(NSInteger)type kcalIncremenData:(id)model{

}

- (void)videoPlayerRankRequestError:(__kindof SJBaseVideoPlayer *)videoPlayer type:(NSInteger)type{

}


- (void)videoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer fatBurningModel:(id)model{
    if (![model isKindOfClass:[MRKFatBurningModel class]]) {  return; }
  
  
    
}










/// 这是一个只有在播放器锁屏状态下, 才会回调的方法
/// 当播放器锁屏后, 用户每次点击都会回调这个方法
- (void)tappedPlayerOnTheLockedState:(__kindof SJBaseVideoPlayer *)videoPlayer {
//    if ( sj_view_isDisappeared(_leftContainerView) ) {
//        sj_view_makeAppear(_leftContainerView, YES);
//        [self.lockStateTappedTimerControl start];
//    }
//    else {
//        sj_view_makeDisappear(_leftContainerView, YES);
//        [self.lockStateTappedTimerControl clear];
//    }
}

- (void)lockedVideoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer {
//    [self _updateAppearStateForResidentBackButtonIfNeeded];
//    [self _updateAppearStateForBottomProgressIndicatorIfNeeded];
//    [self _updateAppearStateForContainerViews];
//    [self _reloadAdaptersIfNeeded];
//    [self.lockStateTappedTimerControl start];
}

- (void)unlockedVideoPlayer:(__kindof SJBaseVideoPlayer *)videoPlayer {
//    [self _updateAppearStateForBottomProgressIndicatorIfNeeded];
//    [self.lockStateTappedTimerControl clear];
//    [videoPlayer controlLayerNeedAppear];
}

- (void)videoPlayer:(SJBaseVideoPlayer *)videoPlayer reachabilityChanged:(SJNetworkStatus)status {
    if (@available(iOS 11.0, *)) {
        [self _reloadCustomStatusBarIfNeeded];
    }
    if ( _disabledPromptWhenNetworkStatusChanges ) return;
    if ( [self.videoPlayer.URLAsset.mediaURL isFileURL] ) return; // return when is local video.
    
    switch ( status ) {
        case SJNetworkStatus_NotReachable: {
            
            ///无网络提示
            [_videoPlayer.prompt show:[NSAttributedString sj_UIKitText:^(id<SJUIKitTextMakerProtocol>  _Nonnull make) {
                make.append(SJVideoPlayerConfigurations.shared.localizedStrings.unstableNetworkPrompt);
                make.font([UIFont systemFontOfSize:14 weight:UIFontWeightMedium]);
                make.textColor(UIColor.whiteColor);
            }] duration:3];
        }
            break;
        case SJNetworkStatus_ReachableViaWWAN: {
            
            ///4G流量提示
            [_videoPlayer.prompt show:[NSAttributedString sj_UIKitText:^(id<SJUIKitTextMakerProtocol>  _Nonnull make) {
                make.append(SJVideoPlayerConfigurations.shared.localizedStrings.cellularNetworkPrompt);
                make.font([UIFont systemFontOfSize:14 weight:UIFontWeightMedium]);
                make.textColor(UIColor.whiteColor);
            }] duration:3];
        }
            break;
        case SJNetworkStatus_ReachableViaWiFi: {}
            break;
    }
}

#pragma mark -

- (NSString *)stringForSeconds:(NSInteger)secs {
    return _videoPlayer ? [_videoPlayer stringForSeconds:secs] : @"";
}

#pragma mark -

- (void)setFixesBackItem:(BOOL)fixesBackItem {
    if ( fixesBackItem == _fixesBackItem )
        return;
    _fixesBackItem = fixesBackItem;
    dispatch_async(dispatch_get_main_queue(), ^{
        if ( self->_fixesBackItem ) {
            [self.controlView addSubview:self.fixedBackButton];
            [self->_fixedBackButton mas_makeConstraints:^(MASConstraintMaker *make) {
                make.top.left.bottom.equalTo(self.topAdapter.view);
                make.width.equalTo(self.topAdapter.view.mas_height);
            }];
            
            [self _updateAppearStateForResidentBackButtonIfNeeded];
            [self _reloadTopAdapterIfNeeded];
        }
        else {
            if ( self->_fixedBackButton ) {
                [self->_fixedBackButton removeFromSuperview];
                self->_fixedBackButton = nil;
                
                // back item
                [self _reloadTopAdapterIfNeeded];
            }
        }
    });
}

- (void)setHiddenBottomProgressIndicator:(BOOL)hiddenBottomProgressIndicator {
    if ( hiddenBottomProgressIndicator != _hiddenBottomProgressIndicator ) {
        _hiddenBottomProgressIndicator = hiddenBottomProgressIndicator;
        dispatch_async(dispatch_get_main_queue(), ^{
            [self _showOrRemoveBottomProgressIndicator];
        });
    }
}

- (void)setBottomProgressIndicatorHeight:(CGFloat)bottomProgressIndicatorHeight {
    if ( bottomProgressIndicatorHeight != _bottomProgressIndicatorHeight ) {
        _bottomProgressIndicatorHeight = bottomProgressIndicatorHeight;
        dispatch_async(dispatch_get_main_queue(), ^{
            [self _updateLayoutForBottomProgressIndicator];
        });
    }
}

- (void)setLoadingView:(nullable UIView<SJLoadingView> *)loadingView {
    if ( loadingView != _loadingView ) {
        [_loadingView removeFromSuperview];
        _loadingView = loadingView;
        if ( loadingView != nil ) {
            [self.controlView addSubview:loadingView];
            [loadingView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.center.offset(0);
            }];
        }
    }
}

- (void)setDraggingProgressPopupView:(nullable __kindof UIView<SJDraggingProgressPopupView> *)draggingProgressPopupView {
    _draggingProgressPopupView = draggingProgressPopupView;
    [self _updateForDraggingProgressPopupView];
}

- (void)setTitleView:(nullable __kindof UIView<SJScrollingTextMarqueeView> *)titleView {
    _titleView = titleView;
    [self _reloadTopAdapterIfNeeded];
}

- (void)setCustomStatusBar:(UIView<SJFullscreenModeStatusBar> *)customStatusBar NS_AVAILABLE_IOS(11.0) {
    if ( customStatusBar != _customStatusBar ) {
        [_customStatusBar removeFromSuperview];
        _customStatusBar = customStatusBar;
        [self _reloadCustomStatusBarIfNeeded];
    }
}

- (void)setShouldShowCustomStatusBar:(BOOL (^)(MrkVerticalEdgeControlLayer * _Nonnull))shouldShowCustomStatusBar NS_AVAILABLE_IOS(11.0) {
    _shouldShowCustomStatusBar = shouldShowCustomStatusBar;
    [self _updateAppearStateForCustomStatusBar];
}

- (void)setspeedupPlaybackPopupView:(UIView<SJSpeedupPlaybackPopupView> *)speedupPlaybackPopupView {
    if ( _speedupPlaybackPopupView != speedupPlaybackPopupView ) {
        [_speedupPlaybackPopupView removeFromSuperview];
        _speedupPlaybackPopupView = speedupPlaybackPopupView;
    }
}

- (void)setOnlyUsedFitOnScreen:(BOOL)onlyUsedFitOnScreen {
    if ( onlyUsedFitOnScreen != _onlyUsedFitOnScreen ) {
        _onlyUsedFitOnScreen = onlyUsedFitOnScreen;
        if ( _onlyUsedFitOnScreen ) {
            _automaticallyPerformRotationOrFitOnScreen = NO;
        }
    }
}

#pragma mark - setup view

- (void)_setupView {
    [self _addItemsToTopAdapter];
    [self _addItemsToLeftAdapter];
    [self _addItemsToBottomAdapter];
    [self _addItemsToRightAdapter];
    [self _addItemsToCenterAdapter];
    
    [self _addItemsToDataAdapter];   ///添加进度展示层
//    [self _addItemsBottomDataAdapter];   ///添加数据展示层
    
//
//    self.topContainerView.sjv_disappearDirection = SJViewDisappearAnimation_None;
//    self.leftContainerView.sjv_disappearDirection = SJViewDisappearAnimation_None;
//    self.bottomContainerView.sjv_disappearDirection = SJViewDisappearAnimation_None;
//    self.rightContainerView.sjv_disappearDirection = SJViewDisappearAnimation_None;
//    self.centerContainerView.sjv_disappearDirection = SJViewDisappearAnimation_None;
//
//
//    self.topDataView.sjv_disappearDirection = SJViewDisappearAnimation_None;
//    self.bottomDataView.sjv_disappearDirection = SJViewDisappearAnimation_None;
    
    sj_view_initializes(@[self.topContainerView,
                          self.leftContainerView,
                          self.bottomContainerView,
                          self.rightContainerView,
                          self.topDataView,
                          self.bottomDataView
                        ]);
    
    [NSNotificationCenter.defaultCenter addObserver:self
                                           selector:@selector(_resetControlLayerAppearIntervalForItemIfNeeded:)
                                               name:SJEdgeControlButtonItemPerformedActionNotification
                                             object:nil];
}

@synthesize fixedBackButton = _fixedBackButton;
- (UIButton *)fixedBackButton {
    if ( _fixedBackButton ) return _fixedBackButton;
    _fixedBackButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [_fixedBackButton setImage:SJVideoPlayerConfigurations.shared.resources.backImage forState:UIControlStateNormal];
    [_fixedBackButton addTarget:self action:@selector(_fixedBackButtonWasTapped) forControlEvents:UIControlEventTouchUpInside];
    return _fixedBackButton;
}

@synthesize bottomProgressIndicator = _bottomProgressIndicator;
- (SJProgressSlider *)bottomProgressIndicator {
    if ( _bottomProgressIndicator ) return _bottomProgressIndicator;
    _bottomProgressIndicator = [SJProgressSlider new];
    _bottomProgressIndicator.pan.enabled = NO;
    _bottomProgressIndicator.trackHeight = _bottomProgressIndicatorHeight;
    _bottomProgressIndicator.round = NO;
    id<SJVideoPlayerControlLayerResources> sources = SJVideoPlayerConfigurations.shared.resources;
    UIColor *traceColor = sources.bottomIndicatorTraceColor ?: sources.progressTraceColor;
    UIColor *trackColor = sources.bottomIndicatorTrackColor ?: sources.progressTrackColor;
    _bottomProgressIndicator.traceImageView.backgroundColor = traceColor;
    _bottomProgressIndicator.trackImageView.backgroundColor = trackColor;
    return _bottomProgressIndicator;
}

@synthesize loadingView = _loadingView;
- (UIView<SJLoadingView> *)loadingView {
    if ( _loadingView == nil ) {
        [self setLoadingView:[SJLoadingView.alloc initWithFrame:CGRectZero]];
    }
    return _loadingView;
}

- (__kindof UIView<SJDraggingProgressPopupView> *)draggingProgressPopupView {
    if ( _draggingProgressPopupView == nil ) {
        [self setDraggingProgressPopupView:[SJDraggingProgressPopupView.alloc initWithFrame:CGRectZero]];
    }
    return _draggingProgressPopupView;
}

- (id<SJDraggingObservation>)draggingObserver {
    if ( _draggingObserver == nil ) {
        _draggingObserver = [SJDraggingObservation new];
    }
    return _draggingObserver;
}

@synthesize titleView = _titleView;
- (UIView<SJScrollingTextMarqueeView> *)titleView {
    if ( _titleView == nil ) {
        [self setTitleView:[SJScrollingTextMarqueeView.alloc initWithFrame:CGRectZero]];
    }
    return _titleView;
}

@synthesize speedupPlaybackPopupView = _speedupPlaybackPopupView;
- (UIView<SJSpeedupPlaybackPopupView> *)speedupPlaybackPopupView {
    if ( _speedupPlaybackPopupView == nil ) {
        _speedupPlaybackPopupView = [SJSpeedupPlaybackPopupView.alloc initWithFrame:CGRectZero];
    }
    return _speedupPlaybackPopupView;
}

@synthesize customStatusBar = _customStatusBar;
- (UIView<SJFullscreenModeStatusBar> *)customStatusBar {
    if ( _customStatusBar == nil ) {
        [self setCustomStatusBar:[SJFullscreenModeStatusBar.alloc initWithFrame:CGRectZero]];
    }
    return _customStatusBar;
}

@synthesize shouldShowCustomStatusBar = _shouldShowCustomStatusBar;
- (BOOL (^)(MrkVerticalEdgeControlLayer * _Nonnull))shouldShowCustomStatusBar {
    if ( _shouldShowCustomStatusBar == nil ) {
        BOOL is_iPhoneX = _screen.is_iPhoneX;
        [self setShouldShowCustomStatusBar:^BOOL(MrkVerticalEdgeControlLayer * _Nonnull controlLayer) {
            if ( controlLayer.videoPlayer.isFitOnScreen ) return NO;
            
            BOOL isFullscreen = controlLayer.videoPlayer.isFullscreen;
            if ( isFullscreen == NO ) {
                CGRect bounds = UIScreen.mainScreen.bounds;
                if ( bounds.size.width > bounds.size.height )
                    isFullscreen = CGRectEqualToRect(controlLayer.bounds, bounds);
            }
            
            BOOL shouldShow = NO;
            if ( isFullscreen ) {
                ///
                /// 13 以后, 全屏后显示自定义状态栏
                ///
                if ( @available(iOS 13.0, *) ) {
                    shouldShow = YES;
                }
                ///
                /// 11 仅 iPhone X 显示自定义状态栏
                ///
                else if ( @available(iOS 11.0, *) ) {
                    shouldShow = is_iPhoneX;
                }
            }
            return shouldShow;
        }];
    }
    return _shouldShowCustomStatusBar;
}

@synthesize dateTimerControl = _dateTimerControl;
- (SJTimerControl *)dateTimerControl {
    if ( _dateTimerControl == nil ) {
        _dateTimerControl = SJTimerControl.alloc.init;
        _dateTimerControl.interval = 1;
        __weak typeof(self) _self = self;
        _dateTimerControl.exeBlock = ^(SJTimerControl * _Nonnull control) {
            __strong typeof(_self) self = _self;
            if ( !self ) return;
            self.customStatusBar.isHidden ? [control interrupt] : [self _reloadCustomStatusBarIfNeeded];
        };
    }
    return _dateTimerControl;
}










- (UIImage *)backGaryImage:(UIImage *)image{
    UIImageView *imageView = [[UIImageView alloc] init];
    imageView.backgroundColor = [UIColor colorWithHexString:@"#1D1D2B" alpha:0.2];
    imageView.contentMode = UIViewContentModeCenter;
    imageView.image = image;
    imageView.size = CGSizeMake(38, 38);
    imageView.layer.cornerRadius = 19.0f;
    imageView.layer.masksToBounds = YES;
    return imageView.snapshotImage;
}


- (void)_addItemsToTopAdapter {
    SJEdgeControlButtonItem *backItem = [SJEdgeControlButtonItem placeholderWithType:SJButtonItemPlaceholderType_49x49 tag:SJEdgeControlLayerTopItem_Back];
    backItem.resetsAppearIntervalWhenPerformingItemAction = NO;
    [backItem addAction:[SJEdgeControlButtonItemAction actionWithTarget:self action:@selector(_backItemWasTapped)]];
    [self.topAdapter addItem:backItem];
    _backItem = backItem;
    
    
    SJEdgeControlButtonItem *titleItem = [SJEdgeControlButtonItem placeholderWithType:SJButtonItemPlaceholderType_49xFill tag:SJEdgeControlLayerTopItem_Title];
    [self.topAdapter addItem:titleItem];
    
    
    SJEdgeControlButtonItem *shareItem = [SJEdgeControlButtonItem placeholderWithType:SJButtonItemPlaceholderType_49x49 tag:SJEdgeControlLayerTopItem_Share];
    [shareItem addAction:[SJEdgeControlButtonItemAction actionWithTarget:self action:@selector(_shareItemWasTapped)]];
    [self.topAdapter addItem:shareItem];
    
    [self.topAdapter reload];
}

- (void)_addItemsToLeftAdapter {
    
    [self.leftAdapter reload];
}

- (void)_addItemsToBottomAdapter {

    SJEdgeControlButtonItem *titleItem = [SJEdgeControlButtonItem placeholderWithType:SJButtonItemPlaceholderType_49xFill tag:SJEdgeControlLayerBottomItem_Holder];
    [self.bottomAdapter addItem:titleItem];
    
    // 当前时间
    SJEdgeControlButtonItem *currentTimeItem = [SJEdgeControlButtonItem placeholderWithSize:8 tag:SJEdgeControlLayerBottomItem_CurrentTime];
    [self.bottomAdapter addItem:currentTimeItem];
    
    // 时间分隔符
    SJEdgeControlButtonItem *separatorItem = [[SJEdgeControlButtonItem alloc] initWithTitle:[NSAttributedString sj_UIKitText:^(id<SJUIKitTextMakerProtocol>  _Nonnull make) {
        make.append(@"/");
        make.font([UIFont fontWithName:fontNamePing size:16]);
        make.textColor([UIColor whiteColor]);
        make.alignment(NSTextAlignmentCenter);
    }] target:nil action:NULL tag:SJEdgeControlLayerBottomItem_Separator];
    [self.bottomAdapter addItem:separatorItem];
 
    // 全部时长
    SJEdgeControlButtonItem *durationTimeItem = [SJEdgeControlButtonItem placeholderWithSize:8 tag:SJEdgeControlLayerBottomItem_DurationTime];
    [self.bottomAdapter addItem:durationTimeItem];
    
    // 播放按钮
    SJEdgeControlButtonItem *playItem = [SJEdgeControlButtonItem placeholderWithType:SJButtonItemPlaceholderType_49x49 tag:SJEdgeControlLayerBottomItem_Play];
    [playItem addAction:[SJEdgeControlButtonItemAction actionWithTarget:self action:@selector(_playItemWasTapped)]];
    [self.bottomAdapter addItem:playItem];

    [self.bottomAdapter reload];
}

- (void)_addItemsToDataAdapter{

}

- (void)_addItemsBottomDataAdapter {
    MRKVideoPrepareDataInfo *videoPlayData = _videoPlayer.videoPlayData;

    SJEdgeControlButtonItem *item = [SJEdgeControlButtonItem placeholderWithSize:DHPX(90) tag:SJEdgeControlLayerCenterItem_Kcal];
    item.dataStr = @"--";
    item.holdStr = @"消耗(千卡)";
    [self.dataAdapter addItem:item];
    
    
    SJEdgeControlButtonItem *item1 = [SJEdgeControlButtonItem placeholderWithSize:DHPX(90) tag:SJEdgeControlLayerCenterItem_RateOfTread];
    item1.dataStr = @"--";
    item1.holdStr = @"踏频(rpm)";
    [self.dataAdapter addItem:item1];
    
    
    SJEdgeControlButtonItem *item2 = [SJEdgeControlButtonItem placeholderWithSize:DHPX(90) tag:SJEdgeControlLayerCenterItem_Resistance];
    item2.dataStr = @"--";
    item2.holdStr = @"阻力";
    [self.dataAdapter addItem:item2];
    
    [self.dataAdapter reload];
}


- (void)_addItemsToRightAdapter {
    
    
//    SJEdgeControlButtonItem *shareItem = [SJEdgeControlButtonItem placeholderWithType:SJButtonItemPlaceholderType_49x49 tag:SJEdgeControlLayerTopItem_Share];
//    [shareItem addAction:[SJEdgeControlButtonItemAction actionWithTarget:self action:@selector(_shareItemWasTapped)]];
//    [self.rightAdapter addItem:shareItem];
    
    
    SJEdgeControlButtonItem *tvItem = [SJEdgeControlButtonItem placeholderWithType:SJButtonItemPlaceholderType_49x49 tag:SJEdgeControlLayerTopItem_TV];
    [tvItem addAction:[SJEdgeControlButtonItemAction actionWithTarget:self action:@selector(_tvItemWasTapped)]];
    [self.rightAdapter addItem:tvItem];
    
    
    SJEdgeControlButtonItem *deviceItem = [SJEdgeControlButtonItem placeholderWithType:SJButtonItemPlaceholderType_49x49 tag:SJEdgeControlLayerTopItem_Device];
    [deviceItem addAction:[SJEdgeControlButtonItemAction actionWithTarget:self action:@selector(_deviceItemWasTapped)]];
    [self.rightAdapter addItem:deviceItem];
    
    
    [self.rightAdapter reload];
}

- (void)_addItemsToCenterAdapter {
    
    ///重播按钮
    UILabel *replayLabel = [UILabel new];
    replayLabel.numberOfLines = 0;
    SJEdgeControlButtonItem *replayItem = [SJEdgeControlButtonItem frameLayoutWithCustomView:replayLabel tag:SJEdgeControlLayerCenterItem_Replay];
    [replayItem addAction:[SJEdgeControlButtonItemAction actionWithTarget:self action:@selector(_replayItemWasTapped)]];
    [self.centerAdapter addItem:replayItem];
    
    // 播放按钮
    SJEdgeControlButtonItem *playItem = [SJEdgeControlButtonItem placeholderWithType:SJButtonItemPlaceholderType_49x49 tag:SJEdgeControlLayerBottomItem_Play];
    [playItem addAction:[SJEdgeControlButtonItemAction actionWithTarget:self action:@selector(_playItemWasTapped)]];
    [self.centerAdapter addItem:playItem];
    
    [self.centerAdapter reload];
}


- (void)dataAdapterUpdateWithModel:(TrainingShowData *)model{
    @weakify(self);
    dispatch_async(dispatch_get_main_queue(), ^{
        @strongify(self);
        TrainingShowData *m = model;
        self.tyModel = model;
        for (SJEdgeControlButtonItem *item in self.dataAdapter.items) {
            NSInteger tag = (NSInteger) item.tag;
            switch (tag) {
                case 50002: {///消耗(kcal)
                    item.dataStr = m.totalKcal;
                }break;
                case 50004: {///踏频(rpm)/桨频(spm)
                    item.dataStr = m.spm;
                }break;
                case 50005: {///阻力(lv)
                    item.dataStr = m.resistance;
                }break;
                default: break;
            }
            [self->_dataAdapter updateContentForItemWithTag:item.tag];
        }
    });
}














#pragma mark - appear state

- (void)_updateAppearStateForContainerViews {
    [self _updateAppearStateForTopContainerView];
    [self _updateAppearStateForLeftContainerView];
    [self _updateAppearStateForBottomContainerView];
    [self _updateAppearStateForRightContainerView];
    [self _updateAppearStateForCenterContainerView];
    
    [self _updateAppearStateForTopDataContainerView];
    [self _updateAppearStateForBottomDataContainerView];
    
    if (@available(iOS 11.0, *)) {
        [self _updateAppearStateForCustomStatusBar];
    }
}

- (void)_updateAppearStateForTopContainerView {
    if ( 0 == _topAdapter.numberOfItems ) {
        sj_view_makeDisappear(_topContainerView, YES);
        return;
    }
    
    /// 锁屏状态下, 使隐藏
    if ( _videoPlayer.isLockedScreen ) {
        sj_view_makeDisappear(_topContainerView, YES);
        return;
    }
    
    /// 是否显示
    if ( _videoPlayer.isControlLayerAppeared ) {
        sj_view_makeAppear(_topContainerView, YES);
    } else {
        sj_view_makeDisappear(_topContainerView, YES);
    }
}


- (void)_updateAppearStateForTopDataContainerView {
    /// 是否显示
    if ( _videoPlayer.isControlLayerAppeared ) {
        sj_view_makeDisappear(_topDataView, YES);
    } else {
        sj_view_makeAppear(_topDataView, YES);
    }
}



- (void)_updateAppearStateForLeftContainerView {
    if ( 0 == _leftAdapter.numberOfItems ) {
        sj_view_makeDisappear(_leftContainerView, YES);
        return;
    }
    
    /// 锁屏状态下显示
    if ( _videoPlayer.isLockedScreen ) {
        sj_view_makeAppear(_leftContainerView, YES);
        return;
    }
    
    /// 是否显示
    sj_view_makeAppear(_leftContainerView, YES);
}

/// 更新显示状态
- (void)_updateAppearStateForBottomContainerView {
    if ( 0 == _bottomAdapter.numberOfItems ) {
        sj_view_makeDisappear(_bottomContainerView, YES);
        return;
    }
    
    /// 锁屏状态下, 使隐藏
    if ( _videoPlayer.isLockedScreen ) {
        sj_view_makeDisappear(_bottomContainerView, YES);
        return;
    }
    
    /// 是否显示
    if ( _videoPlayer.isControlLayerAppeared ) {
        sj_view_makeAppear(_bottomContainerView, YES);
       
    } else {
        sj_view_makeDisappear(_bottomContainerView, YES);
    }
}


- (void)_updateAppearStateForBottomDataContainerView {
    if ( 0 == _dataAdapter.numberOfItems ) {
        sj_view_makeDisappear(_bottomDataView, YES);
        return;
    }
    
    /// 是否显示
    if ( _videoPlayer.isControlLayerAppeared ) {
        sj_view_makeDisappear(_bottomDataView, YES);
    } else {
        sj_view_makeAppear(_bottomDataView, YES);
    }
}

/// 更新显示状态
- (void)_updateAppearStateForRightContainerView {
    if ( 0 == _rightAdapter.numberOfItems ) {
        sj_view_makeDisappear(_rightContainerView, YES);
        return;
    }
    
    /// 锁屏状态下, 使隐藏
    if ( _videoPlayer.isLockedScreen ) {
        sj_view_makeDisappear(_rightContainerView, YES);
        return;
    }
    
    /// 是否显示
    if ( _videoPlayer.isControlLayerAppeared ) {
        sj_view_makeAppear(_rightContainerView, YES);
    } else {
        sj_view_makeDisappear(_rightContainerView, YES);
    }
}

- (void)_updateAppearStateForCenterContainerView {
    if ( 0 == _centerAdapter.numberOfItems ) {
        sj_view_makeDisappear(_centerContainerView, YES);
        return;
    }
    
    sj_view_makeAppear(_centerContainerView, YES);
}

- (void)_updateAppearStateForBottomProgressIndicatorIfNeeded {
    if ( _bottomProgressIndicator == nil )
        return;
    
    _videoPlayer.isControlLayerAppeared && !_videoPlayer.isLockedScreen ?
            sj_view_makeDisappear(_bottomProgressIndicator, YES) :
            sj_view_makeAppear(_bottomProgressIndicator, YES);
}

- (void)_updateAppearStateForCustomStatusBar NS_AVAILABLE_IOS(11.0) {
    BOOL shouldShow = self.shouldShowCustomStatusBar(self);
    if ( shouldShow ) {
        if ( self.customStatusBar.superview == nil ) {
            static dispatch_once_t onceToken;
            dispatch_once(&onceToken, ^{
                UIDevice.currentDevice.batteryMonitoringEnabled = YES;
            });
            
            [NSNotificationCenter.defaultCenter addObserver:self selector:@selector(_reloadCustomStatusBarIfNeeded) name:UIDeviceBatteryLevelDidChangeNotification object:nil];
            [NSNotificationCenter.defaultCenter addObserver:self selector:@selector(_reloadCustomStatusBarIfNeeded) name:UIDeviceBatteryStateDidChangeNotification object:nil];
            
            [self.topContainerView addSubview:self.customStatusBar];
            [self.customStatusBar mas_makeConstraints:^(MASConstraintMaker *make) {
                make.top.offset(0);
                make.left.right.equalTo(self.topAdapter);
                make.height.offset(20);
            }];
        }
    }
    
    _customStatusBar.hidden = !shouldShow;
    _customStatusBar.isHidden ? [self.dateTimerControl interrupt] : [self.dateTimerControl resume];
}


/// 暂不实现
- (void)_updateContentForPictureInPictureItem API_AVAILABLE(ios(14.0)) {
    
    
}

#pragma mark - update items

- (void)_reloadAdaptersIfNeeded {
    [self _reloadTopAdapterIfNeeded];
    [self _reloadLeftAdapterIfNeeded];
    [self _reloadBottomAdapterIfNeeded];
    [self _reloadRightAdapterIfNeeded];
    [self _reloadCenterAdapterIfNeeded];
    
    [self _reloadDataAdapterIfNeeded];
//    [self _reloadDataAdapterIfNeeded];
}

- (void)_reloadTopAdapterIfNeeded {
    if ( sj_view_isDisappeared(_topContainerView) ) return;
    
    id<SJVideoPlayerControlLayerResources> sources = SJVideoPlayerConfigurations.shared.resources;
    BOOL isFullscreen = _videoPlayer.isFullscreen;
    BOOL isFitOnScreen = _videoPlayer.isFitOnScreen;
    BOOL isPlayOnScrollView = _videoPlayer.isPlayOnScrollView;
    BOOL isSmallscreen = !isFullscreen && !isFitOnScreen;

    // back item
    {
        SJEdgeControlButtonItem *backItem = [self.topAdapter itemForTag:SJEdgeControlLayerTopItem_Back];
        if ( backItem != nil ) {
            if ( _fixesBackItem ) {
                backItem.alpha = 0;
                backItem.image = nil;
            }
            else {
                if ( isFullscreen || isFitOnScreen )
                    backItem.hidden = NO;
                else if ( _hiddenBackButtonWhenOrientationIsPortrait )
                    backItem.hidden = YES;
                else
                    backItem.hidden = isPlayOnScrollView;
                
                if ( backItem.hidden == NO )
                    backItem.image = [self backGaryImage:sources.backImage];
            }
        }
    }
    
    // title item
    {
        SJEdgeControlButtonItem *titleItem = [self.topAdapter itemForTag:SJEdgeControlLayerTopItem_Title];
        if ( titleItem != nil ) {
//            if ( self.isHiddenTitleItemWhenOrientationIsPortrait && isSmallscreen ) {
//                titleItem.hidden = YES;
//            } else {
//                if ( titleItem.customView != self.titleView )
//                    titleItem.customView = self.titleView;
//                SJVideoPlayerURLAsset *asset = _videoPlayer.URLAsset;
//                NSAttributedString *_Nullable attributedTitle = asset.attributedTitle;
//                self.titleView.attributedText = attributedTitle;
//                titleItem.hidden = (attributedTitle.length == 0);
//            }
//
//            if ( titleItem.hidden == NO ) {
//                // margin
//                NSInteger atIndex = [_topAdapter indexOfItemForTag:SJEdgeControlLayerTopItem_Title];
//                CGFloat left  = [_topAdapter isHiddenWithRange:NSMakeRange(0, atIndex)] ? 16 : 0;
//                CGFloat right = [_topAdapter isHiddenWithRange:NSMakeRange(atIndex, _topAdapter.numberOfItems)] ? 16 : 0;
//                titleItem.insets = SJEdgeInsetsMake(left, right);
//            }
        }
    }

  
    // share item
    {
        SJEdgeControlButtonItem *shareItem = [self.topAdapter itemForTag:SJEdgeControlLayerTopItem_Share];
        if ( shareItem != nil ) {
            if ( shareItem.hidden == NO ) {
                // margin
                shareItem.image = [self backGaryImage:sources.shareImage];
            }
        }
    }
    
    [_topAdapter reload];
}

- (void)_reloadLeftAdapterIfNeeded {
    if ( sj_view_isDisappeared(_leftContainerView) ) return;
    
    SJEdgeControlButtonItem *rankItem = [self.leftAdapter itemForTag:SJEdgeControlLayerLeftItem_Rank];
    if ( rankItem != nil ) {
        BOOL isShowRoll = _videoPlayer.videoPlayData.liveModel.isShowRankControl;
        rankItem.hidden = !isShowRoll;
    }
    [_leftAdapter reload];
}

- (void)_reloadBottomAdapterIfNeeded {
    if ( sj_view_isDisappeared(_bottomContainerView) ) return;
    
    id<SJVideoPlayerControlLayerResources> sources = SJVideoPlayerConfigurations.shared.resources;
    // play item
    {
        SJEdgeControlButtonItem *playItem = [self.bottomAdapter itemForTag:SJEdgeControlLayerBottomItem_Play];
        if ( playItem != nil && playItem.hidden == NO ) {
            playItem.image = _videoPlayer.isPaused ? sources.playImage : sources.pauseImage;
        }
    }
    
    [_bottomAdapter reload];
}


- (void)reloadDataAdapter{
    MRKVideoPrepareDataInfo *videoPlayData = _videoPlayer.videoPlayData;
    ///添加蓝牙展示数据
    if (videoPlayData.liveModel.isSupportConnection) {
        [self _addItemsBottomDataAdapter];
    }
   
    
//    self.stepView.isHiddenHotView = !videoPlayData.liveModel.isShowHotProgress;  // 热力图是否显示
//    self.stepView.equipmentId = videoPlayData.liveModel.equipmentId;
//    // 热力进度条赋值
//    if (videoPlayData.planModel.courseCataloguePOS.count > 0) {
//        // 整理成所需要的环节数据
//        self.stepView.hidden = NO;
//        self.stepView.progressData = videoPlayData.planModel.courseCataloguePOS;
//    }
//
//    [self.dmListView isHiddenDanmuView:!_videoPlayer.courseModel.isShowRankControl];
//    if (self.dmListView.hidden == NO) {
//        [self.dmListView welcomeToRoom:@"欢迎来到MERIT课程，和数以万计的燃友们一起冲击燃脂！我们互相鼓励，互相监督，共同进步；课中良好氛围需要大家一起维护，发布违规言论我们将对账号进行封禁处理。"];
//    }
    
    [self _reloadDataAdapterIfNeeded];
}

- (void)_reloadDataAdapterIfNeeded {
//    if ( sj_view_isDisappeared(_bottomDataView) ) return;
    
    id<SJVideoPlayerControlLayerResources> sources = SJVideoPlayerConfigurations.shared.resources;
    // auto item
    {
        self.usePlanCourseControl.hidden = YES;
    }
    
    
    MRKVideoPrepareDataInfo *videoPlayData = _videoPlayer.videoPlayData;
    int equipmentId = videoPlayData.equipmentId.intValue;
    switch (equipmentId) {
        case BoatEquipment: case BicycleEquipment: case EllipticalEquipment: {
           
            // 阻力 item
            SJEdgeControlButtonItem *item4 = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_Resistance];
            if ( item4 != nil) {
                if (_videoPlayer.videoPlayData.isShowAutoControl) {
                    item4.showControl = _videoPlayer.useLessonPlanControl;
                    item4.holdStr = _videoPlayer.useLessonPlanControl?@"自动阻力(lv)":@"阻力(lv)";
                }else{
                    item4.showControl = NO;
                    item4.holdStr = @"阻力(lv)";
                }
            }
        }break;
        case TreadmillEquipment: {
            // 速度 item
            SJEdgeControlButtonItem *item8 = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_Speed];
            if ( item8 != nil) {
                if (_videoPlayer.videoPlayData.isShowAutoControl) {
                    item8.showControl = _videoPlayer.useLessonPlanControl;
                    item8.holdStr = _videoPlayer.useLessonPlanControl?@"自动速度(公里/时)":@"速度(公里/时)";
                }else{
                    item8.showControl = NO;
                    item8.holdStr = @"速度(公里/时)";
                }
            }
            
            // 坡度 item
            SJEdgeControlButtonItem *item7 = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_Slope];
            if ( item7 != nil) {
                if (_videoPlayer.videoPlayData.isShowAutoControl) {
                    item7.showControl = _videoPlayer.useLessonPlanControl;
                    item7.holdStr = _videoPlayer.useLessonPlanControl?@"自动坡度":@"坡度";
                }else{
                    item7.showControl = NO;
                    item7.holdStr = @"坡度";
                }
            }
        }break;
        default: break;
    }
    
    [_dataAdapter reload];
}


- (void)_reloadRightAdapterIfNeeded {
    if ( sj_view_isDisappeared(_rightContainerView) ) return;
    
    id<SJVideoPlayerControlLayerResources> sources = SJVideoPlayerConfigurations.shared.resources;
//    // share item
//    {
//        SJEdgeControlButtonItem *shareItem = [self.rightAdapter itemForTag:SJEdgeControlLayerTopItem_Share];
//        if ( shareItem != nil ) {
//            if ( shareItem.hidden == NO ) {
//                // margin
//                shareItem.image = [self backGaryImage:sources.shareImage];
//            }
//        }
//    }
    
    
    // tv item
    {
        SJEdgeControlButtonItem *tvItem = [self.rightAdapter itemForTag:SJEdgeControlLayerTopItem_TV];
        if ( tvItem != nil ) {
            tvItem.hidden = NO;
            if ( tvItem.hidden == NO ) {
                // margin
                tvItem.image = [self backGaryImage:sources.tvImage];
            }
        }
    }
    
    
    // Device item
    {
        SJEdgeControlButtonItem *deviceItem = [self.rightAdapter itemForTag:SJEdgeControlLayerTopItem_Device];
        if ( deviceItem != nil ) {
            deviceItem.hidden = !_videoPlayer.courseModel.isSupportConnection;
            if ( deviceItem.hidden == NO ) {
                // margin
//                UIImage *image = _videoPlayer.isConnectDevice ? sources.device_LImage : sources.device_DImage;
//                deviceItem.image = [self backGaryImage:image]; ;
                 MRKVideoPrepareDataInfo *videoPlayData = _videoPlayer.videoPlayData;
                 deviceItem.image = [AlivcImage mrkEquipmentImageWith:videoPlayData.equipmentId connect:_videoPlayer.isConnectDevice];
            }
        }
    }
    
    
    [self.rightAdapter reload];
}

- (void)_reloadCenterAdapterIfNeeded {
    if ( sj_view_isDisappeared(_centerContainerView) ) return;
    
    SJEdgeControlButtonItem *replayItem = [self.centerAdapter itemForTag:SJEdgeControlLayerCenterItem_Replay];
    if ( replayItem != nil ) {
        replayItem.hidden = !_videoPlayer.isPlaybackFinished;
        if ( replayItem.hidden == NO && replayItem.title == nil ) {
            id<SJVideoPlayerControlLayerResources> resources = SJVideoPlayerConfigurations.shared.resources;
            id<SJVideoPlayerLocalizedStrings> strings = SJVideoPlayerConfigurations.shared.localizedStrings;
            UILabel *textLabel = replayItem.customView;
            textLabel.attributedText = [NSAttributedString sj_UIKitText:^(id<SJUIKitTextMakerProtocol>  _Nonnull make) {
                make.alignment(NSTextAlignmentCenter).lineSpacing(6);
                make.font(resources.replayTitleFont);
                make.textColor(resources.replayTitleColor);
                if ( resources.replayImage != nil ) {
                    make.appendImage(^(id<SJUTImageAttachment>  _Nonnull make) {
                        make.image = resources.replayImage;
                    });
                }
                if ( strings.replay.length != 0 ) {
                    if ( resources.replayImage != nil ) make.append(@"\n");
                    make.append(strings.replay);
                }
            }];
            textLabel.bounds = (CGRect){CGPointZero, [textLabel.attributedText sj_textSize]};
        }
    }
    
    SJEdgeControlButtonItem *playItem = [self.centerAdapter itemForTag:SJEdgeControlLayerBottomItem_Play];
    if ( playItem != nil ) {
        BOOL isPaused = _videoPlayer.isPaused && !_videoPlayer.isPlaybackFinished;
        playItem.hidden = !isPaused;
        if ( playItem.hidden == NO ) {
            playItem.image = [AlivcImage imageNamed:@"icon_big_play"];
        }
    }
    
    [_centerAdapter reload];
}


///刷新设备蓝牙连接状态图标
- (void)_updateConnectForTopDeviceItemIfNeeded {
    if ( sj_view_isDisappeared(_topContainerView) )
        return;
    id<SJVideoPlayerControlLayerResources> sources = SJVideoPlayerConfigurations.shared.resources;
    SJEdgeControlButtonItem *deviceItem = [self.rightAdapter itemForTag:SJEdgeControlLayerTopItem_Device];
    if ( deviceItem != nil && deviceItem.hidden == NO ) {
        // margin
        UIImage *image = _videoPlayer.isConnectDevice ? sources.device_LImage : sources.device_DImage;
        deviceItem.image = [self backGaryImage:image]; ;
    }
}


- (void)_updateContentForBottomCurrentTimeItemIfNeeded {
    if ( sj_view_isDisappeared(_bottomContainerView) )
        return;
    NSString *currentTimeStr = [_videoPlayer stringForSeconds:_videoPlayer.currentTime];
    SJEdgeControlButtonItem *currentTimeItem = [_bottomAdapter itemForTag:SJEdgeControlLayerBottomItem_CurrentTime];
    if ( currentTimeItem != nil && currentTimeItem.isHidden == NO ) {
        currentTimeItem.title = [self _textForTimeString:currentTimeStr];
        [_bottomAdapter updateContentForItemWithTag:SJEdgeControlLayerBottomItem_CurrentTime];
    }
}

- (void)_updateContentForBottomDurationItemIfNeeded {
    SJEdgeControlButtonItem *durationTimeItem = [_bottomAdapter itemForTag:SJEdgeControlLayerBottomItem_DurationTime];
    if ( durationTimeItem != nil && durationTimeItem.isHidden == NO ) {
        durationTimeItem.title = [self _textForTimeString:[_videoPlayer stringForSeconds:_videoPlayer.duration]];
        [_bottomAdapter updateContentForItemWithTag:SJEdgeControlLayerBottomItem_DurationTime];
    }
}

- (void)_reloadSizeForBottomTimeLabel {
    // 00:00
    // 00:00:00
    NSString *ms = @"00:00";
    NSString *hms = @"00:00:00";
    NSString *durationTimeStr = [_videoPlayer stringForSeconds:_videoPlayer.duration];
    NSString *format = (durationTimeStr.length == ms.length)?ms:hms;
    CGSize formatSize = [[self _textForTimeString:format] sj_textSize];
    
    SJEdgeControlButtonItem *currentTimeItem = [_bottomAdapter itemForTag:SJEdgeControlLayerBottomItem_CurrentTime];
    SJEdgeControlButtonItem *durationTimeItem = [_bottomAdapter itemForTag:SJEdgeControlLayerBottomItem_DurationTime];
    
    if ( !durationTimeItem && !currentTimeItem ) return;
    currentTimeItem.size = formatSize.width;
    durationTimeItem.size = formatSize.width;
    [_bottomAdapter reload];
}

- (void)_updateContentForBottomProgressIndicatorIfNeeded {
    if ( _bottomProgressIndicator != nil && !sj_view_isDisappeared(_bottomProgressIndicator) ) {
        _bottomProgressIndicator.value = _videoPlayer.currentTime;
        _bottomProgressIndicator.maxValue = _videoPlayer.duration ? : 1;
    }
}

- (void)_updateCurrentTimeForDraggingProgressPopupViewIfNeeded {
    if ( !sj_view_isDisappeared(_draggingProgressPopupView) )
        _draggingProgressPopupView.currentTime = _videoPlayer.currentTime;
}


- (void)_updateCurrentTimeForStepView {
//    if (self.stepView.hidden == NO) {
//        NSNumber *value = self.videoPlayer.blueManager.dataManager.tyModel.hotProgressValue;
//        [self.stepView setHotProgressTime:_videoPlayer.currentTime hotValue:value];
//    }
}

- (void)_updateAppearStateForResidentBackButtonIfNeeded {
    if ( !_fixesBackItem )
        return;
    BOOL isFitOnScreen = _videoPlayer.isFitOnScreen;
    BOOL isFull = _videoPlayer.isFullscreen;
    BOOL isLockedScreen = _videoPlayer.isLockedScreen;
    if ( isLockedScreen ) {
        _fixedBackButton.hidden = YES;
    }
    else {
        BOOL isPlayOnScrollView = _videoPlayer.isPlayOnScrollView;
        _fixedBackButton.hidden = isPlayOnScrollView && !isFitOnScreen && !isFull;
    }
}

- (void)_updateNetworkSpeedStrForLoadingView {
    if ( !_videoPlayer || !self.loadingView.isAnimating )
        return;
    
    if ( self.loadingView.showsNetworkSpeed && ![_videoPlayer.URLAsset.mediaURL isFileURL] ) {
        self.loadingView.networkSpeedStr = [NSAttributedString sj_UIKitText:^(id<SJUIKitTextMakerProtocol>  _Nonnull make) {
            id<SJVideoPlayerControlLayerResources> resources = SJVideoPlayerConfigurations.shared.resources;
            make.font(resources.loadingNetworkSpeedTextFont);
            make.textColor(resources.loadingNetworkSpeedTextColor);
            make.alignment(NSTextAlignmentCenter);
            make.append(self.videoPlayer.reachability.networkSpeedStr);
        }];
    }
    else {
        self.loadingView.networkSpeedStr = nil;
    }
}

- (void)_reloadCustomStatusBarIfNeeded NS_AVAILABLE_IOS(11.0) {
    if ( sj_view_isDisappeared(_customStatusBar) )
        return;
    _customStatusBar.networkStatus = _videoPlayer.reachability.networkStatus;
    _customStatusBar.date = NSDate.date;
    _customStatusBar.batteryState = UIDevice.currentDevice.batteryState;
    _customStatusBar.batteryLevel = UIDevice.currentDevice.batteryLevel;
}

#pragma mark  ---------------- Progres---------------

- (void)_updateForDraggingProgressPopupView {
    SJDraggingProgressPopupViewStyle style = SJDraggingProgressPopupViewStyleNormal;
    if ( !_videoPlayer.URLAsset.isM3u8 &&
         [_videoPlayer.playbackController respondsToSelector:@selector(screenshotWithTime:size:completion:)] ) {
        if ( _videoPlayer.isFullscreen ) {
            style = SJDraggingProgressPopupViewStyleFullscreen;
        }
        else if ( _videoPlayer.isFitOnScreen ) {
            style = SJDraggingProgressPopupViewStyleFitOnScreen;
        }
    }
    _draggingProgressPopupView.style = style;
    _draggingProgressPopupView.duration = _videoPlayer.duration ?: 1;
    _draggingProgressPopupView.currentTime = _videoPlayer.currentTime;
    _draggingProgressPopupView.dragTime = _videoPlayer.currentTime;
}

- (nullable NSAttributedString *)_textForTimeString:(NSString *)timeStr {
    id<SJVideoPlayerControlLayerResources> resources = SJVideoPlayerConfigurations.shared.resources;
    return [NSAttributedString sj_UIKitText:^(id<SJUIKitTextMakerProtocol>  _Nonnull make) {
        make.append(timeStr).font(resources.timeLabelFont).textColor(resources.timeLabelColor).alignment(NSTextAlignmentCenter);
    }];
}

/// 此处为重置控制层的隐藏间隔.(如果点击到当前控制层上的item, 则重置控制层的隐藏间隔)
- (void)_resetControlLayerAppearIntervalForItemIfNeeded:(NSNotification *)note {
    SJEdgeControlButtonItem *item = note.object;
    if ( item.resetsAppearIntervalWhenPerformingItemAction ) {
        if ( [_topAdapter containsItem:item] ||
             [_leftAdapter containsItem:item] ||
             [_bottomAdapter containsItem:item] ||
             [_rightAdapter containsItem:item])
            
            [_videoPlayer controlLayerNeedAppear];
    }
}

- (void)_showOrRemoveBottomProgressIndicator {
    if ( _hiddenBottomProgressIndicator || _videoPlayer.playbackType == SJPlaybackTypeLIVE ) {
        if ( _bottomProgressIndicator ) {
            [_bottomProgressIndicator removeFromSuperview];
            _bottomProgressIndicator = nil;
        }
    } else {
        if ( !_bottomProgressIndicator ) {
            [self.controlView addSubview:self.bottomProgressIndicator];
            [self _updateLayoutForBottomProgressIndicator];
        }
    }
}

- (void)_updateLayoutForBottomProgressIndicator {
    if ( _bottomProgressIndicator == nil ) return;
    _bottomProgressIndicator.trackHeight = _bottomProgressIndicatorHeight;
    if (_screen.is_iPhoneX) {
        _bottomProgressIndicator.frame = (CGRect){kScreenPadding+20, self.bounds.size.height - _bottomProgressIndicatorHeight, self.bounds.size.width - kScreenPadding*2 - 20*2, _bottomProgressIndicatorHeight};
    } else {
        _bottomProgressIndicator.frame = (CGRect){0, self.bounds.size.height - _bottomProgressIndicatorHeight, self.bounds.size.width, _bottomProgressIndicatorHeight};
    }
}

- (void)_showOrHiddenLoadingView {
    if ( _videoPlayer == nil || _videoPlayer.URLAsset == nil ) {
        [self.loadingView stop];
        return;
    }
    
    if ( _videoPlayer.isPaused ) {
        [self.loadingView stop];
    }
    else if ( _videoPlayer.assetStatus == SJAssetStatusPreparing ) {
        [self.loadingView start];
    }
    else if ( _videoPlayer.assetStatus == SJAssetStatusFailed ) {
        [self.loadingView stop];
    }
    else if ( _videoPlayer.assetStatus == SJAssetStatusReadyToPlay ) {
        self.videoPlayer.reasonForWaitingToPlay == SJWaitingToMinimizeStallsReason ? [self.loadingView start] : [self.loadingView stop];
    }
}

- (void)_willBeginDragging {
    [self.controlView addSubview:self.draggingProgressPopupView];
    [self _updateForDraggingProgressPopupView];
    [_draggingProgressPopupView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.offset(0);
    }];
    
    sj_view_initializes(_draggingProgressPopupView);
    sj_view_makeAppear(_draggingProgressPopupView, NO);
    
    if ( _draggingObserver.willBeginDraggingExeBlock )
        _draggingObserver.willBeginDraggingExeBlock(_draggingProgressPopupView.dragTime);
}

- (void)_didMove:(NSTimeInterval)progressTime {
    _draggingProgressPopupView.dragTime = progressTime;
//    // 是否生成预览图
//    if ( _draggingProgressPopupView.isPreviewImageHidden == NO ) {
//        __weak typeof(self) _self = self;
//        [_videoPlayer screenshotWithTime:progressTime
//                                    size:CGSizeMake(_draggingProgressPopupView.frame.size.width, _draggingProgressPopupView.frame.size.height)
//                              completion:^(SJBaseVideoPlayer * _Nonnull videoPlayer, UIImage * _Nullable image, NSError * _Nullable error) {
//            __strong typeof(_self) self = _self;
//            if ( !self ) return;
//            [self.draggingProgressPopupView setPreviewImage:image];
//        }];
//    }
    
    if ( _draggingObserver.didMoveExeBlock )
        _draggingObserver.didMoveExeBlock(_draggingProgressPopupView.dragTime);
}

- (void)_endDragging {
    NSTimeInterval time = _draggingProgressPopupView.dragTime;
    if ( _draggingObserver.willEndDraggingExeBlock )
        _draggingObserver.willEndDraggingExeBlock(time);
    
    [_videoPlayer seekToTime:time completionHandler:nil];

    sj_view_makeDisappear(_draggingProgressPopupView, YES, ^{
        if ( sj_view_isDisappeared(self->_draggingProgressPopupView) ) {
            [self->_draggingProgressPopupView removeFromSuperview];
        }
    });
    
    if ( _draggingObserver.didEndDraggingExeBlock )
        _draggingObserver.didEndDraggingExeBlock(time);
}



#pragma mark  ---------------- 课程指令设置 ---------------
///接收到指令信息
- (void)sendInstructionsWithData:(CourseLinkModel *)model{
    if (!model) return;
    
    // 判断是Merit课程 和 是电磁控设备 才发指令
    if (_videoPlayer.videoPlayData.isMeritControl) {
        [self instructionsOperationData:model];
    }
}

///指令操作
- (void)instructionsOperationData:(CourseLinkModel *)model{
    ///教案关闭
    if (!self.videoPlayer.isUseLessonPlanControl) { return; }
    
    NSString *typeId = _videoPlayer.videoPlayData.equipmentId;
    EquipmentDetialModel *eqModel = _videoPlayer.videoPlayData.eqModel;
    
    ///跑步机
    if (typeId.intValue == TreadmillEquipment) {
        ///跑步机是否有坡度下发
        BOOL treadmillHasSlopeControl = _videoPlayer.videoPlayData.planModel.treadmillHasSlopeControl;
        
        ///跑步机是否需要下发指令
        if (![_videoPlayer.trainManager isNeedInstructionWithModel:model]) {
            return;
        }
        
        UIView *pointView = nil;                                                        ///锚点view
        NSDictionary *orderParms = nil;                                                 ///指令数据
        NSMutableAttributedString *tipStr = [[NSMutableAttributedString alloc] init];   ///提示字段
        NSString *soundTipStr = @"";
        
        NSNumber *speedOrderNum = model.minNum;
        NSNumber *slopeOrderNum = model.adviseNum;
        double speed = 0.0;
        if (speedOrderNum.doubleValue > 0) {
            speed = model.minNum.doubleValue/10;
            if (speed > eqModel.maxSpeed.intValue && eqModel.maxSpeed.intValue != 0) {
                speed = eqModel.maxSpeed.intValue;
            }
        }
        NSString *speedTipStr =[NSString stringWithFormat:@"%.1f", speed];
        /// 跑步机调节速度
        SJEdgeControlButtonItem *speeditem = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_Speed];
        if (speeditem != nil) {
            [tipStr appendAttributedString:CombineAttributedString(@"速度将调至", speedTipStr)];
            soundTipStr = [NSString stringWithFormat:@"速度将调至%@", speedTipStr];
            speedOrderNum = @(speed*10);
            ///速度Item加提示
            TrainDataItemView *speedItemView = speeditem.partView;
            [speedItemView reloadNumTip:@(speed)];
            pointView = speedItemView; ///锚点
        }
        
        /// 跑步机是否可调节坡度
        /// @note 有负坡度显示
        SJEdgeControlButtonItem *slopeitem = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_Slope];
        if (slopeitem != nil && eqModel.showSlope.boolValue && treadmillHasSlopeControl) {
            int slopeNum = model.adviseNum.intValue;
            if (slopeNum > eqModel.maxSlope.intValue) {
                slopeNum = eqModel.maxSlope.intValue;
            }
            if (slopeNum < eqModel.minSlope.intValue) {
                slopeNum = eqModel.minSlope.intValue;
            }
            NSString *slopeStr = [NSString stringWithFormat:@"%d", slopeNum];
            NSString *slopeTipStr = [NSString stringWithFormat:@"%@坡度将调至", tipStr.length > 0 ? @"，" : @""];
            [tipStr appendAttributedString:CombineAttributedString(slopeTipStr, slopeStr)];
            soundTipStr = [NSString stringWithFormat:@"%@,坡度将调至%@", soundTipStr, slopeTipStr];
            slopeOrderNum = @(slopeNum);
            ///坡度Item加提示
            TrainDataItemView *slopeItemView = slopeitem.partView;
            [slopeItemView reloadNumTip:@(slopeNum)];
            
            if (pointView == nil) {///修改锚点
                pointView = slopeItemView;
            }
        }
        
        if (pointView == nil) return; ///无锚点直接return
        orderParms = @{
            Speed : speedOrderNum ?:@0,
            Slope : slopeOrderNum ?:@0,
            BlueDeviceType : typeId,
            @"status" : @(_videoPlayer.trainManager.treamillStatus)
        };
        [self setOrderNotificationData:orderParms
                          andPointView:pointView
                             andTipStr:tipStr
                           andSoundStr:soundTipStr
                        andControlType:@"3"
                      andControlNumber:speedTipStr];
    }
    ///划船机,单车,力量站 椭圆机 可支持坡度调节
    if(typeId.intValue == BoatEquipment ||
       typeId.intValue == BicycleEquipment ||
       typeId.intValue == PowerEquipment ||
       typeId.intValue == EllipticalEquipment) {
        
        SJEdgeControlButtonItem *slopeitem = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_Slope];
        SJEdgeControlButtonItem *item = [self.dataAdapter itemForTag:SJEdgeControlLayerCenterItem_Resistance]; ///
        
        if(slopeitem == nil) {
            ///不支持坡度调节 ：有建议阻力（不为0），发送建议阻力；建议阻力位0，不发送指令；有建议坡度，不发送
            if (model.adviseNum.intValue == 0) {return;}
            if (item == nil) return;
            
            @weakify(self);
            [_videoPlayer.videoPlayData mappingResistance:model completion:^(CourseLinkModel * _Nonnull mod) {
                @strongify(self);
                
                ///锚点\阻力Item加提示
                TrainDataItemView *pointView = item.partView;
                [pointView reloadNumTip:mod.adviseNum];
                
                NSString *resistanceTipStr = [NSString stringWithFormat:@"%d", mod.adviseNum.intValue];
                [self setOrderNotificationData:@{Resistance :mod.adviseNum ?:@0, BlueDeviceType :typeId}
                                  andPointView:pointView
                                     andTipStr:CombineAttributedString(@"阻力将调至",resistanceTipStr)
                                   andSoundStr:[NSString stringWithFormat:@"阻力将调至%@", resistanceTipStr]
                                andControlType:@"1"
                              andControlNumber:resistanceTipStr];
            }];
        } else {
            ///支持坡度调节 ：有建议阻力（不为0）和建议坡度，同时发送建议阻力和建议坡度；建议阻力位0，只发送建议坡度（坡度可以为0 ）；
            @weakify(self);
            [_videoPlayer.videoPlayData mappingResistance:model completion:^(CourseLinkModel * _Nonnull mod) {
                @strongify(self);
                NSMutableDictionary *orderParms = [NSMutableDictionary dictionaryWithDictionary:@{
                    BlueDeviceType:typeId
                }];
                NSMutableAttributedString *tipStr = [[NSMutableAttributedString alloc] init];   ///提示字段
                NSString *soundTipStr = @"";
                UIView *pointView = nil;
                ///锚点/阻力Item加提示
                if(item != nil && model.adviseNum.intValue > 0) {
                    [orderParms setObject:mod.adviseNum?:@0 forKey:Resistance];
                    TrainDataItemView *partView = item.partView;
                    [partView reloadNumTip:mod.adviseNum];
                    pointView = partView;
                    NSString *resistanceTipStr = [NSString stringWithFormat:@"%@", mod.adviseNum];
                    [tipStr appendAttributedString:CombineAttributedString(@"阻力将调至",resistanceTipStr)];
                    soundTipStr = [NSString stringWithFormat:@"阻力将调至%@", resistanceTipStr];
                }
                ///坡度Item加提示
                if (mod.isChangeSlope) {
                    [orderParms setObject:mod.slopeNum ?:@0 forKey:Slope];
                    TrainDataItemView *slopePartView = slopeitem.partView;
                    [slopePartView reloadNumTip:mod.slopeNum];
                    pointView = slopeitem.partView;
                    NSString *slopeTipStr = [NSString stringWithFormat:@"%@", mod.slopeNum];
                    [tipStr appendAttributedString:CombineAttributedString([NSString stringWithFormat:@"%@坡度将调至", model.adviseNum.intValue > 0 ? @"，" : @"" ], slopeTipStr)];
                    soundTipStr = [NSString stringWithFormat:@"%@%@坡度将调至%@", soundTipStr, model.adviseNum.intValue > 0 ? @"，" : @"", slopeTipStr];
                }
                if (pointView) {
                    [self setOrderNotificationData:orderParms
                                      andPointView:pointView
                                         andTipStr:tipStr
                                       andSoundStr:soundTipStr
                                    andControlType:model.adviseNum.intValue > 0 ? @"1" : @"2"
                                  andControlNumber:[NSString stringWithFormat:@"%@", model.adviseNum.intValue > 0 ? model.adviseNum : mod.slopeNum]];
                }
            }];
        }
    }
}


NS_INLINE NSMutableAttributedString *CombineAttributedString(NSString *str1, NSString *str2) {
    NSMutableAttributedString *text = [[NSMutableAttributedString alloc] initWithString:str1?:@""];
    text.color = [UIColor whiteColor];
    text.font = [UIFont systemFontOfSize:13];
    
    NSMutableAttributedString *speedAttriTipStr = [[NSMutableAttributedString alloc] initWithString:str2?:@""];
    speedAttriTipStr.color = [UIColor whiteColor];
    speedAttriTipStr.font = [UIFont systemFontOfSize:15 weight:UIFontWeightMedium];
    [text appendAttributedString:speedAttriTipStr];
    return text;
}

///设备指令通知
- (void)setOrderNotificationData:(NSDictionary *)data andPointView:(UIView *)view andTipStr:(NSAttributedString *)tip andSoundStr:(NSString *)soundStr andControlType:(NSString *)controlType andControlNumber:(NSString *)controlNumber{
    if (!self.hasToastAlert) {
        self.hasToastAlert = YES;
        
        @weakify(self);
        void(^sendNotificationBlock)(void) = ^{
            @strongify(self);
            self.hasToastAlert = NO;
            ///教案是否开启
            if (self.videoPlayer.isUseLessonPlanControl){
                [[NSNotificationCenter defaultCenter] postNotificationName:SetResistanceSlopeSpeedNotification object:data];
            }
            self.videoPlayer.trainManager.isSendControl = NO;
        };
        
        if ([[UIApplication sharedApplication] applicationState] == UIApplicationStateBackground && self.videoPlayer.courseModel.equipmentId.intValue != TreadmillEquipment && soundStr.length > 0) { // 后台且不是跑步机
            /// 播放语音
            [[SpeechSynthesizerManager shared] speak:soundStr];
            
            self.videoPlayer.trainManager.controlType = controlType;
            self.videoPlayer.trainManager.controlNumber = controlNumber;
            self.videoPlayer.trainManager.isSendControl = YES;
        }
        
        //判断当前是否是数据层
        if ( sj_view_isDisappeared(_bottomDataView) ) {
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(5.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                sendNotificationBlock();
            });
            return;
        }
        
        self.popoverView = [PopoverView popoverView];
        self.popoverView.dismissBlock = ^{
            sendNotificationBlock();
        };
        [self.popoverView showPointView:view inView:self withAttributText:tip];
        MLog(@"_setOrderNotificationData下发指令 ==== %@", data);
    }
}

 

#pragma mark -设置建议踏频桨频提示
- (void)_reloadDataAdapterForAdviceTipView {
    
}

- (void)_updateCurrentTimeForAdviceTipView {
  
}

@end


@implementation SJEdgeControlButtonItem (SJVerticalControlLayerExtended)
- (void)setResetsAppearIntervalWhenPerformingItemAction:(BOOL)resetsAppearIntervalWhenPerformingItemAction {
    objc_setAssociatedObject(self, @selector(resetsAppearIntervalWhenPerformingItemAction), @(resetsAppearIntervalWhenPerformingItemAction), OBJC_ASSOCIATION_RETAIN_NONATOMIC);
}
- (BOOL)resetsAppearIntervalWhenPerformingItemAction {
    id result = objc_getAssociatedObject(self, _cmd);
    return result == nil ? YES : [result boolValue];
}
@end
