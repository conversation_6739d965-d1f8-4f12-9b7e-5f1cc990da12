//
//  AlivcImage.m
//  AliyunVideoClient_Entrance
//
//  Created by <PERSON><PERSON><PERSON> on 2018/10/10.
//  Copyright © 2018年 Alibaba. All rights reserved.
//

#import "AlivcImage.h"

@implementation AlivcImage

+ (UIImage *)imageNamed:(NSString *)imageName{
    if (!imageName) return nil;
    
    UIImage *image = [[self imageCache] objectForKey:imageName];
    if (image) return image;
    NSString *path = [self filePathForVideoImage:imageName];
    if (!path) return nil;
    
    image = [UIImage imageWithContentsOfFile:path];
    image = [image imageByDecoded];
    if (!image) return nil;
    
    [[self imageCache] setObject:image forKey:imageName];
    return image;
}
+ (NSString *)filePathForVideoImage:(NSString *)name{
    NSString *ext = name.pathExtension;
    if (ext.length == 0) ext = @"png";
    NSBundle * bundle = ({
        static NSBundle *bundle;
        static dispatch_once_t onceToken;
        dispatch_once(&onceToken, ^{
            NSString *path = [[NSBundle mainBundle] pathForResource:@"VideoImage" ofType:@"bundle"];
            bundle = [NSBundle bundleWithPath:path];
        });
        bundle;
    });
    NSString *path = [bundle pathForScaledResource:name ofType:ext];
    return path;
}



+ (UIImage *__nullable)e_imageNamed:(NSString *)imageName{
    if (!imageName) return nil;
    
    UIImage *image = [[self imageCache] objectForKey:imageName];
    if (image) return image;
    NSString *path = [self filePathForVideoEquipmentImage:imageName];
    if (!path) return nil;
    
    image = [UIImage imageWithContentsOfFile:path];
    image = [image imageByDecoded];
    if (!image) return nil;
    
    [[self imageCache] setObject:image forKey:imageName];
    return image;
}
+ (NSString *)filePathForVideoEquipmentImage:(NSString *)name{
    NSString *ext = name.pathExtension;
    if (ext.length == 0) ext = @"png";
    NSBundle * bundle = ({
        static NSBundle *bundle;
        static dispatch_once_t onceToken;
        dispatch_once(&onceToken, ^{
            NSString *path = [[NSBundle mainBundle] pathForResource:@"VideoBlueToothIcon" ofType:@"bundle"];
            bundle = [NSBundle bundleWithPath:path];
        });
        bundle;
    });
    NSString *path = [bundle pathForScaledResource:name ofType:ext];
    return path;
}


+ (UIImage *)r_imageNamed:(NSString *)name {
    if (!name) return nil;
    
    UIImage *image = [[self imageCache] objectForKey:name];
    if (image) return image;
    NSString *path = [self filePathForRaceImage:name];
    if (!path) return nil;
    
    image = [UIImage imageWithContentsOfFile:path];
    image = [image imageByDecoded];
    if (!image) return nil;
    
    [[self imageCache] setObject:image forKey:name];
    return image;
}
+ (NSString *)filePathForRaceImage:(NSString *)name{
    NSString *ext = name.pathExtension;
    if (ext.length == 0) ext = @"png";
    NSBundle * bundle = ({
        static NSBundle *bundle;
        static dispatch_once_t onceToken;
        dispatch_once(&onceToken, ^{
            NSString *path = [[NSBundle mainBundle] pathForResource:@"RaceVideoImage" ofType:@"bundle"];
            bundle = [NSBundle bundleWithPath:path];
        });
        bundle;
    });
    NSString *path = [bundle pathForScaledResource:name ofType:ext];
    return path;
}





+ (YYMemoryCache *)imageCache{
    static YYMemoryCache *cache;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        cache = [YYMemoryCache new];
        cache.shouldRemoveAllObjectsOnMemoryWarning = NO;
        cache.shouldRemoveAllObjectsWhenEnteringBackground = NO;
        cache.name = @"MeritImageCache";
    });
    return cache;
}

+ (NSString *__nullable)pathOfImageName:(NSString *)imageName {
    NSBundle *currentBundle = [NSBundle bundleForClass:[self class]];
    NSString *imagePath = [NSString stringWithFormat:@"VideoImage.bundle/%@",imageName];
    NSString *fullPath = [[currentBundle resourcePath] stringByAppendingPathComponent:imagePath];
    return fullPath;
}

+ (UIImage *)imageName:(NSString *)imageName inBundle:(NSString *)bundle{
    NSString *imagePath = [[NSBundle mainBundle] pathForResource:imageName ofType:@"png" inDirectory:bundle];
    UIImage *image = [UIImage imageWithContentsOfFile:imagePath];
    return image;
}




+ (UIImage *)mrkEquipmentImageWith:(NSString *)equipmentId connect:(BOOL)isConnect{
    NSString *imageName = @"";
    
    int deviceId = equipmentId.intValue;
    switch (deviceId) {
        case BoatEquipment: //划船机
            imageName = isConnect ?  @"icon_boating_on" : @"icon_boating_off";
            break;
        case BicycleEquipment: //动感单车
            imageName = isConnect ?  @"icon_bicycle_on" : @"icon_bicycle_off";
            break;
        case EllipticalEquipment://椭圆机
            imageName = isConnect ?  @"icon_elliptical_on" : @"icon_elliptical_off";
            break;
        case TreadmillEquipment://跑步机
            imageName = isConnect ?  @"icon_running_on" : @"icon_running_off";
            break;
        case StairClimbEquiment://爬楼机
            imageName = isConnect ?  @"icon_palouji_on" : @"icon_palouji_off";
            break;
        case SkipRopeEquipment: ///跳绳
            imageName = isConnect ?  @"icon_jump_on" : @"icon_jump_off";
            break;
        case JinMoQiangEquipment: ///筋膜枪
            imageName = isConnect ?  @"icon_qiang_on" : @"icon_qiang_off";
            break;
        case FatScaleEquipment: ///体脂秤
            imageName = isConnect ?  @"icon_scale_on" : @"icon_scale_off";
            break;
        case FLSBEquipment: ///飞力士棒
            imageName = isConnect ?  @"icon_feilishibang_on" : @"icon_feilishibang_off";
            break; 
        case PowerEquipment: ///力量站
            imageName = isConnect ?  @"icon_power_on" : @"icon_power_off";
            break;
        case KettleBellEquipment: ///智能壶铃
            imageName = isConnect ?  @"icon_kettlebell_on" : @"icon_kettlebell_off";
            break;
        default:
            imageName = isConnect ?  @"blueTooth_on" : @"blueTooth_off";
            break;
    }
    
    return [AlivcImage e_imageNamed:imageName];
}



+ (UIImage *)mrkHotEquipmentImageWith:(NSString *)equipmentId connect:(BOOL)isConnect{
    NSString *imageName = @"";
    
    int deviceId = equipmentId.intValue;
    switch (deviceId) {
        case BoatEquipment: //划船机
            imageName = isConnect ?  @"icon_boating_on_hot" : @"icon_boating_off";
            break;
        case BicycleEquipment: //动感单车
            imageName = isConnect ?  @"icon_bicycle_on_hot" : @"icon_bicycle_off";
            break;
        case EllipticalEquipment://椭圆机
            imageName = isConnect ?  @"icon_elliptical_on_hot" : @"icon_elliptical_off";
            break;
        case TreadmillEquipment://跑步机
            imageName = isConnect ?  @"icon_running_on_hot" : @"icon_running_off";
            break;
        case StairClimbEquiment://爬楼机
            imageName = isConnect ?  @"icon_palouji_on_hot" : @"icon_palouji_off";
            break;
        case SkipRopeEquipment: ///跳绳
            imageName = isConnect ?  @"icon_jump_on_hot" : @"icon_jump_off";
            break;
        case JinMoQiangEquipment: ///筋膜枪
            imageName = isConnect ?  @"icon_qiang_on_hot" : @"icon_qiang_off";
            break;
        case FatScaleEquipment: ///体脂秤
            imageName = isConnect ?  @"icon_scale_on_hot" : @"icon_scale_off";
            break;
        case FLSBEquipment: ///飞力士棒
            imageName = isConnect ?  @"icon_feilishibang_on_hot" : @"icon_feilishibang_off";
            break;
        case PowerEquipment: ///力量站
            imageName = isConnect ?  @"icon_power_on_hot" : @"icon_power_off";
            break;
        case KettleBellEquipment: ///智能壶铃
            imageName = isConnect ?  @"icon_kettlebell_on_hot" : @"icon_kettlebell_off";
            break;
        default:
            imageName = isConnect ?  @"blueTooth_on_hot" : @"blueTooth_off";
            break;
    }
    
    return [AlivcImage e_imageNamed:imageName];
}

@end
