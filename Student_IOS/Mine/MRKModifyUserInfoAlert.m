//
//  MRKModifyUserInfoAlert.m
//  Student_IOS
//
//  Created by merit on 2021/10/16.
//

#import "MRKModifyUserInfoAlert.h"
#import "UIView+AZGradient.h"
#import "UIView+Effects.h"
#import "YYTextKeyboardManager.h"



@interface MRKModifyUserInfoAlert ()<UITextFieldDelegate, YYTextKeyboardObserver>
@property (nonatomic, strong) UILabel *titleLab;
@property (nonatomic, strong) UILabel *descripLab;
@property (nonatomic, strong) UITextField *textField;
@property (nonatomic, strong) UIButton *ensureBtn;
@property (nonatomic, strong) UIButton *cancelBtn;
@end

@implementation MRKModifyUserInfoAlert

- (void)dealloc {
    [[YYTextKeyboardManager defaultManager] removeObserver:self];
}

#pragma mark  - YYTextKeyboardObserver -
- (void)keyboardChangedWithTransition:(YYTextKeyboardTransition)transition {
    if (transition.toVisible) {
        NSLog(@"self.textField becomeFirstResponder");
        CGRect toFrame = [[YYTextKeyboardManager defaultManager] convertRect:transition.toFrame toView:self];
        if (transition.animationDuration == 0) {
            self.containerView.bottom = CGRectGetMinY(toFrame) - 30;
        } else {
            [UIView animateWithDuration:transition.animationDuration
                                  delay:0
                                options:transition.animationOption | UIViewAnimationOptionBeginFromCurrentState
                             animations:^{
                self.containerView.bottom = CGRectGetMinY(toFrame) - 30;
            } completion:NULL];
        }
    } else {
        NSLog(@"self.textField resignFirstResponder");
        self.containerView.bottom = kScreenHeight/2 + DHPX(240)/2;
    }
}

- (void)touchesEnded:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event{
    [self.textField resignFirstResponder];
}

- (void)layoutContainerView{
    self.isAutoHidden = NO;
    
    [self.containerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_equalTo(self.containerView.superview.mas_centerY);
        make.centerX.mas_equalTo(self.containerView.superview.mas_centerX);
        make.size.mas_equalTo(CGSizeMake(DHPX(340), DHPX(240)));
    }];
}

- (void)setupContainerViewAttributes{
    //设置containerview的属性,比如切边啥的
    self.containerView.backgroundColor = [UIColor whiteColor];
    
    self.containerView.size = CGSizeMake(DHPX(340), DHPX(240));
    [self.containerView viewCornerWith:ViewRadiusMake(8, 8, 8, 8)];
    
    [[YYTextKeyboardManager defaultManager] addObserver:self];
}

- (void)setupContainerSubViews{
    
    [self.containerView addSubview:self.titleLab];
    [self.titleLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.containerView.mas_top).offset(DHPX(20));
        make.centerX.mas_equalTo(self.containerView);
        make.left.right.mas_equalTo(0);
        make.height.mas_equalTo(DHPX(25));
    }];
    
    [self.containerView addSubview:self.textField];
    self.textField.frame = CGRectMake(20, DHPX(60), DHPX(340) - 40, WKDHPX(50));
    
    [self.containerView addSubview:self.descripLab];
    [self.descripLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.textField.mas_bottom).offset(DHPX(20));
        make.left.mas_equalTo(30);
        make.right.mas_equalTo(-30);
        make.height.mas_equalTo(DHPX(40));
    }];
    
    CALayer *line1 = [[CALayer alloc]init];
    line1.backgroundColor = UIColorHex(#F6F6F8).CGColor;
    line1.frame = CGRectMake(0, DHPX(240) - DHPX(48) -10, DHPX(340), CGFloatFromPixel(1.0));
    [self.containerView.layer addSublayer:line1];
    
    CALayer *line2 = [[CALayer alloc] init];
    line2.backgroundColor = UIColorHex(#F6F6F8).CGColor;
    line2.frame = CGRectMake(DHPX(340)/2, DHPX(240) - DHPX(48), CGFloatFromPixel(1.0), DHPX(48));
    [self.containerView.layer addSublayer:line2];
    
    [self.containerView addSubview:self.cancelBtn];
    [self.cancelBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_equalTo(self.containerView.mas_bottom);
        make.left.mas_equalTo(0);
        make.size.mas_equalTo(CGSizeMake(DHPX(340)/2,  DHPX(48)));
    }];
    
    [self.containerView addSubview:self.ensureBtn];
    [self.ensureBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_equalTo(self.containerView.mas_bottom);
        make.right.mas_equalTo(0);
        make.size.mas_equalTo(CGSizeMake(DHPX(340)/2, DHPX(48)));
    }];
}

- (void)layoutContainerViewSubViews{
    [self layoutIfNeeded];
}

- (void)becomeResponder{
    [self.textField becomeFirstResponder];
}
#pragma mark - UITextFieldDelegate -

- (BOOL)textFieldShouldBeginEditing:(UITextField *)textField{
    return YES;
}

- (void)textFieldDidBeginEditing:(UITextField *)textField{
    
}

- (BOOL)textFieldShouldEndEditing:(UITextField *)textField{
    return YES;
}

- (void)textFieldDidEndEditing:(UITextField *)textField{
    
}

#pragma mark - UserInfoAlertType -

- (void)setType:(UserInfoAlertType)type{
    _type = type;
    
    switch (type) {
        case UserInfoAlertNick_name:{
            
            self.titleLab.text = @"请输入昵称";
            
            NSString *tip = @"1、请输入1-15个字符，可由中英文、数字组成\n2、此昵称非登录名，仅在涉及公开个人信息时显示";
            NSMutableAttributedString *attriString = [[NSMutableAttributedString alloc] initWithString:tip];
            NSMutableParagraphStyle *paragraphStyle = [[NSMutableParagraphStyle alloc] init];
            [paragraphStyle setLineSpacing:5];
            [attriString addAttribute:NSParagraphStyleAttributeName value:paragraphStyle range:NSMakeRange(0, [tip length])];
            self.descripLab.attributedText = attriString;
            
            
            
            NSDictionary *attributes = @{NSForegroundColorAttributeName:DescribeTextColor,
                                         NSFontAttributeName:[UIFont systemFontOfSize:14]};
            NSMutableAttributedString *placeholderStr = [[NSMutableAttributedString alloc] initWithString:@"请输入 1-15 个字符"
                                                                                               attributes:attributes];
            self.textField.attributedPlaceholder = placeholderStr;
            
        } break;
            
        default:
            break;
    }
}

#pragma mark - Lazy -


- (UILabel *)titleLab {
    if (!_titleLab) {
        UILabel *label = [[UILabel alloc] init];
        label.textAlignment = NSTextAlignmentCenter;
        label.textColor = MainTextColor;
        label.font = [UIFont systemFontOfSize:15 weight:UIFontWeightMedium];
        _titleLab = label;
    }
    return _titleLab;
}

- (UILabel *)descripLab {
    if (!_descripLab) {
        UILabel *label = [[UILabel alloc] init];
        label.textAlignment = NSTextAlignmentLeft;
        label.textColor = UIColorHex(#666666);
        label.font = [UIFont systemFontOfSize:11];
        label.numberOfLines = 2;
        _descripLab = label;
    }
    return _descripLab;
}

- (UITextField *)textField{
    if (!_textField) {
        UITextField *textField = [[UITextField alloc] init];
        textField.delegate = self;
        textField.backgroundColor = [UIColor colorWithHexString:@"#F8F8FA"];
        textField.layer.cornerRadius = WKDHPX(50)/2;
        textField.layer.masksToBounds = YES;
        textField.font = [UIFont systemFontOfSize:15];
        
        UIView *leftView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, WKDHPX(50)/2, DHPX(48))];
        textField.leftView = leftView;
        textField.leftViewMode = UITextFieldViewModeAlways;
        
        UIView *rightView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, WKDHPX(50)/2, DHPX(48))];
        textField.rightView = rightView;
        textField.rightViewMode = UITextFieldViewModeAlways;
        
        textField.textColor = [UIColor blackColor];
        
        _textField = textField;
    }
    
    return _textField;
}

- (UIButton *)ensureBtn{
    if (!_ensureBtn) {
        _ensureBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_ensureBtn setTitleColor:UIColorHex(#16D2E3) forState:UIControlStateNormal];
        [_ensureBtn setTitle:@"确认" forState:UIControlStateNormal];
        _ensureBtn.titleLabel.font = [UIFont systemFontOfSize:15 weight:UIFontWeightMedium];
        [_ensureBtn addTarget:self action:@selector(openChallengeBtnClick:) forControlEvents:UIControlEventTouchUpInside];
        _ensureBtn.tag = 2000;
    }
    return _ensureBtn;
}

- (UIButton *)cancelBtn{
    if (!_cancelBtn) {
        _cancelBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_cancelBtn setTitleColor:UIColorHex(#666666) forState:UIControlStateNormal];
        [_cancelBtn setTitle:@"取消" forState:UIControlStateNormal];
        _cancelBtn.titleLabel.font = [UIFont systemFontOfSize:15 weight:UIFontWeightMedium];
        [_cancelBtn addTarget:self action:@selector(openChallengeBtnClick:) forControlEvents:UIControlEventTouchUpInside];
        _cancelBtn.tag = 3000;
    }
    return _cancelBtn;
}

- (void)openChallengeBtnClick:(UIButton *)sender{
    
    NSInteger tag = sender.tag;
    if (tag == 3000) {
        [self dismissAnimated:YES];
        if(self.closeBlock){
            self.closeBlock(sender);
        }
    }else{
        NSString *text = self.textField.text;
        if ([text isEmpty]) {
            [MBProgressHUD showMessage:@"请先输入昵称~" toView:nil];
            return;
        }

        if (self.userInfoBlock) {
            self.userInfoBlock(text, self, sender);
        }
        
    }
}

@end
