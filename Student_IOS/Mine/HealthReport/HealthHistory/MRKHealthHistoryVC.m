//
//  MRKHealthHistoryVC.m
//  Student_IOS
//
//  Created by merit on 2022/6/20.
//

#import "MRKHealthHistoryVC.h"
#import "MJDIYHeader.h"
#import "MJDIYAutoFooter.h"
#import "WeightMeasureViewController.h"
#import "HealthReportViewController.h"


@interface MRKHealthHistoryVC ()<UITableViewDelegate,UITableViewDataSource>
@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, assign) int page;
@property (nonatomic , strong) NSMutableArray *dataArray;
@property (nonatomic , strong) NSMutableArray *originArray;
@end

@implementation MRKHealthHistoryVC

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
}

- (void)viewDidLoad {
    self.tracePageId = @"page_equipment_health_scale_device_name_record";
    [super viewDidLoad];
    self.navTitle = @"历史记录";
    self.page = 0;
    
    self.dataArray = [NSMutableArray array];
    self.originArray = [NSMutableArray array];
    [self viewLayOut];
    // Do any additional setup after loading the view.
}

- (void)viewLayOut {
    self.tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStyleGrouped];
    self.tableView.backgroundColor = [UIColor clearColor];
    self.tableView.delegate = self;
    self.tableView.dataSource = self;
    self.tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    self.tableView.tableFooterView = [[UIView alloc] init];
    self.tableView.separatorColor = LineColor;
    [self.mrkContentView addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.mrkContentView.mas_top).offset(kNavBarHeight);
        make.left.equalTo(self.mrkContentView.mas_left).offset(0);
        make.width.mas_equalTo(RealScreenWidth);
        if (@available(iOS 11.0, *)) {
            make.bottom.equalTo(self.view.mas_safeAreaLayoutGuideBottom).offset(0);
        }else{
            make.bottom.equalTo(self.view.mas_bottom).offset(0);
        }
    }];
    
    
    @weakify(self);
    MrkEmptyView *emptyView = [MrkEmptyView emptyViewWithImage:[UIImage imageNamed:@"icon_booking_holder"]
                                            titleStr:@""
                                           detailStr:@"暂无数据"];

    emptyView.errorBtnClickBlock = ^{
        [self_weak_.tableView.mj_header beginRefreshing];
    };
    self.tableView.pageEmptyView = emptyView;
    
    MJDIYHeader *header = [MJDIYHeader headerWithRefreshingBlock:^{
        @strongify(self);
        self.page = 1;
        [self refresh];
    }];
    header.automaticallyChangeAlpha = YES;
    self.tableView.mj_header = header;
    
    MJDIYAutoFooter *footer = [MJDIYAutoFooter footerWithRefreshingBlock:^{
        @strongify(self);
        self.page ++;
        [self refresh];
    }];
    footer.automaticallyChangeAlpha = YES;
    self.tableView.mj_footer = footer;
    
    [self.tableView.mj_header beginRefreshing];
}





- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView{
    return self.dataArray.count;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    NSArray *arr = [self.dataArray[section] objectForKey:@"value"];
    return arr.count ;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    NSArray *arr = [self.dataArray[indexPath.section] objectForKey:@"value"];
    MRKHealthRecordModel *model = arr[indexPath.row];
    return model.isDayLastest ? WKDHPX(34+8) : WKDHPX(68);
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    NSArray *arr = [self.dataArray[indexPath.section] objectForKey:@"value"];
    MRKHealthRecordModel *model = arr[indexPath.row];
    if (model.isDayLastest) {
        static NSString *identifier = @"topCell";
        MRKHealthHistoryTopCell * cell = [tableView dequeueReusableCellWithIdentifier:identifier];
        if (!cell) {
            cell = [[MRKHealthHistoryTopCell alloc]initWithStyle:UITableViewCellStyleDefault reuseIdentifier:identifier];
            cell.selectionStyle = UITableViewCellSelectionStyleNone;
        }
        cell.record = model;
        return cell;
    }
    static NSString *identifier = @"lookAtCell";
    MRKHealthHistoryCell * cell = [tableView dequeueReusableCellWithIdentifier:identifier];
    if (!cell) {
        cell = [[MRKHealthHistoryCell alloc]initWithStyle:UITableViewCellStyleDefault reuseIdentifier:identifier];
        cell.selectionStyle = UITableViewCellSelectionStyleNone;
    }
    if (self.dataArray.count) {
        cell.record = model;
    }
    return cell;
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section{
    return WKDHPX(52-8);
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section{
    return WKDHPX(8);
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section{
    UILabel *myLabel = [[UILabel alloc] init];
    myLabel.frame = CGRectMake(WKDHPX(16), WKDHPX(8), kScreenWidth - WKDHPX(32), WKDHPX(52-16));
    myLabel.font = [UIFont fontWithName:fontNameMeDium size:WKDHPX(16)];
    myLabel.textColor = [UIColor colorWithHexString:titleNormalColor1];
    myLabel.text = [self tableView:tableView titleForHeaderInSection:section];
    
    UIView *headerView = [[UIView alloc] init];
    [headerView addSubview:myLabel];
    return headerView;
}

- (NSString *)tableView:(UITableView *)tableView titleForHeaderInSection:(NSInteger)section{
    NSDictionary *dic = self.dataArray[section];
    return [dic objectForKey:@"month"];
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    MRKHealthRecordModel *record = [self.dataArray[indexPath.section] objectForKey:@"value"][indexPath.row];
    if (record.isDayLastest || record.insertType.intValue == 2) {
        return;
    }
   
    tableView.traceEventId = @"btn_equipment_health_scale_device_name_record";
    NSString *rid = self.fromDataPage ? record.originId : record.measureId;
    if (record.electrodeType.intValue == 2){
        [[RouteManager sharedInstance] skipScaleSportWeb:rid];          
    } else {
        HealthReportViewController *vc = [HealthReportViewController new];
        vc.reportId = rid;
        [self.navigationController pushViewController:vc animated:YES];
    }
}

#pragma mark - 左滑删除

- (BOOL)tableView:(UITableView *)tableView canEditRowAtIndexPath:(NSIndexPath *)indexPath {
    NSArray *arr = [self.dataArray[indexPath.section] objectForKey:@"value"];
    MRKHealthRecordModel *model = arr[indexPath.row];
    return !model.isDayLastest;
}

//// This method supersedes -tableView:titleForDeleteConfirmationButtonForRowAtIndexPath: if return value is non-nil
//- (nullable NSArray<UITableViewRowAction *> *)tableView:(UITableView *)tableView editActionsForRowAtIndexPath:(NSIndexPath *)indexPath API_DEPRECATED_WITH_REPLACEMENT("tableView:trailingSwipeActionsConfigurationForRowAtIndexPath:", ios(8.0, 13.0)) API_UNAVAILABLE(tvos) API_UNAVAILABLE(visionos, watchos);

- (NSArray<UITableViewRowAction *> *)tableView:(UITableView *)tableView editActionsForRowAtIndexPath:(NSIndexPath *)indexPath {
    UITableViewRowAction *deleteRowAction = [UITableViewRowAction rowActionWithStyle:UITableViewRowActionStyleDestructive title:@"删除" handler:^(UITableViewRowAction *action, NSIndexPath *indexPath) {
        NSArray *arr = [self.dataArray[indexPath.section] objectForKey:@"value"];
        [self deleteRecord:arr[indexPath.row] atIndexPath:indexPath];
    }];
    deleteRowAction.backgroundColor = [UIColor colorWithHexString:@"#FF5363"];
    return @[deleteRowAction];
}

- (void)deleteRecord:(MRKHealthRecordModel *)record atIndexPath:(NSIndexPath *)indexPath {
    
    NSString *url = @"";
    NSDictionary *parms = @{};

    if (self.fromDataPage) {
        url = @"/user/userHealthRecord/deleteUserHealthRecord";
        parms = @{@"userHealthRecordId" : record.idd ? : @""};
    } else {
        url = @"/equip/record-user-association/bodyFatScale";
        parms = @{@"id" : record.idd ? : @""};
    }
    
    [MRKBaseRequest mrkRequestType:YTKRequestMethodDELETE
                               url:url
                           andParm:parms
          completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"delete__record==%@" , request.responseObject);
        ///删除源数据 然后直接刷新 22-11-11
        [self.originArray removeObject:record];
        self.dataArray = [self mergeMonthData:self.originArray];
        [self.tableView reloadData];
        
        BOOL haveData = self.dataArray.count > 0;
        [self.tableView hiddenEmptyView:haveData];
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"delete__record__error==%@" , request.error.localizedDescription);
    }];
}


- (void)endRefresh {
    if (self.tableView.mj_header.isRefreshing) {
        [self.tableView.mj_header endRefreshing];
    }
    if (self.tableView.mj_footer.isRefreshing) {
        [self.tableView.mj_footer endRefreshing];
    }
}


- (void)refresh {

    NSString *url = @"";
    NSDictionary *parms = @{};

    if (self.fromDataPage) {
        url = @"/user/userHealthRecord/pageHistoryHealth";
        parms = @{
            @"size" : @(kPageNumber),
            @"current" : @(self.page)
        };
    } else {
        url = @"/equip/record-user-association/historicalWeight";
        parms = @{
            @"size" : @(kPageNumber),
            @"current" : @(self.page),
            @"scaleUserId" : [HealthPersonModel currentScaleUserID]
        };
    }
    
    [MRKBaseRequest mrkRequestType:YTKRequestMethodGET
                               url:url
                           andParm:parms
          completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"history__%@" , request.responseObject);
        
        [self endRefresh];

        if (self.page == 1){
            [self.originArray removeAllObjects];
        }
        
        id data = [request.responseObject valueForKeyPath:@"data.records"];
        NSArray *dataArray = [NSArray modelArrayWithClass:[MRKHealthRecordModel class] json:data];
        [self.originArray addObjectsFromArray:dataArray];
        
        
        self.dataArray = [self mergeMonthData:self.originArray];
        [self.tableView reloadData];
        
        self.tableView.mj_footer.hidden = self.originArray.count == [[[request.responseObject objectForKey:@"data"] objectForKey:@"total"] intValue];
        {
            //空白页按钮事件
            BOOL haveData = self.dataArray.count > 0;
            [self.tableView hiddenEmptyView:haveData];
        }
        
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        NSLog(@"history__error--%@" , request.error.localizedDescription);
        self.tableView.mj_footer.hidden = YES;
        [self endRefresh];
        
        if ([self.tableView tableViewIsEmptyData]){
            [self.tableView mrkShowNetworkErrorEmptyView];
        }
    }];
}

- (NSMutableArray *)mergeMonthData:(NSArray *)data {
    NSMutableDictionary *dic = [NSMutableDictionary dictionary];
    NSMutableArray *array  = [NSMutableArray array];
    
    for (MRKHealthRecordModel *model in data) {
        if ([dic.allKeys containsObject:model.monthTimeInterval]) {
            array = [dic objectForKey:model.monthTimeInterval];
        } else {
            array = [NSMutableArray array];
        }
        [array addObject:model];
        [dic setObject:array forKey:model.monthTimeInterval];
    }
    
    NSArray *sortKeys = [dic.allKeys sortedArrayUsingComparator:^NSComparisonResult(NSString *obj1, NSString *obj2) {
        return [obj1 compare:obj2] == NSOrderedAscending;
    }];
    
    array = [NSMutableArray array];
    for (NSString *key in sortKeys) {
        NSArray *value = [dic objectForKey:key];
        value = [self mergeDayData:value];
        [array addObject:@{
            @"month" : [MRKTimeManager stringFromTimeInterval:key format:@"yyyy年MM月"],
            @"value" : value
        }];
    }
    return array;
}

- (NSArray *)mergeDayData:(NSArray *)data {
    NSMutableDictionary *dic = [NSMutableDictionary dictionary];
    NSMutableArray *array  = [NSMutableArray array];
    
    for (MRKHealthRecordModel *model in data) {
        if ([dic.allKeys containsObject:model.dayTimeInterval]) {
            array = [dic objectForKey:model.dayTimeInterval];
        } else {
            array = [NSMutableArray array];
        }
        [array addObject:model];
        [dic setObject:array forKey:model.dayTimeInterval];
    }
    
    NSArray *sortKeys = [dic.allKeys sortedArrayUsingComparator:^NSComparisonResult(NSString *obj1, NSString *obj2) {
        return [obj1 compare:obj2] == NSOrderedAscending;
    }];
    
    array = [NSMutableArray array];
  
    for (NSString *key in sortKeys) {
        NSMutableArray *value = [[dic objectForKey:key] mutableCopy];
        MRKHealthRecordModel *first = [MRKHealthRecordModel new];
        first.dayTime = ((MRKHealthRecordModel *)value.firstObject).dayTime;
        first.dayTimeInterval = ((MRKHealthRecordModel *)value.firstObject).dayTimeInterval;
        [value insertObject:first atIndex:0];
        [value enumerateObjectsUsingBlock:^(MRKHealthRecordModel * obj, NSUInteger idx, BOOL * _Nonnull stop) {
            obj.isDayLastest = idx == 0;
        }];
        [array addObjectsFromArray:value];
    }
    
    return array;
}


#pragma mark ---------Delegate -----------
- (BOOL)viewControllerIsNeedNavBar:(MRKBaseController *)viewController {
    return YES;
}

/*
 #pragma mark - Navigation
 
 // In a storyboard-based application, you will often want to do a little preparation before navigation
 - (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
 // Get the new view controller using [segue destinationViewController].
 // Pass the selected object to the new view controller.
 }
 */

@end



@interface MRKHealthHistoryCell ()
@property (nonatomic, strong) UILabel *timeLab;
@property (nonatomic, strong) UIImageView *healthTypeImageView;
@property (nonatomic, strong) UILabel *weightLab;
@property (nonatomic, strong) UILabel *describeLab;
@property (nonatomic, strong) UIButton *deleteBtn;
@property (nonatomic, strong) UIView *dataView;
@property (nonatomic, strong) UIView *lineView;
@end

@implementation MRKHealthHistoryCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self =[super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        
        [self addSubview];
    }
    return self;
}

- (void)addSubview {
    
//    [self.contentView addSubview:self.timeLab];
//    [self.timeLab mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.left.equalTo(self.contentView.mas_left).offset(16);
//        make.right.equalTo(self.contentView.mas_right).offset(-16);
//        make.top.equalTo(self.contentView.mas_top).offset(0);
//        make.height.mas_equalTo(32);
//    }];
    
    [self.contentView addSubview:self.dataView];
    [self.dataView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentView.mas_top).offset(0);
        make.left.right.bottom.equalTo(@0);
    }];
    [self.dataView addSubview:self.healthTypeImageView];
    [self.healthTypeImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView.mas_left).offset(WKDHPX(16));
        make.centerY.equalTo(self.dataView.mas_centerY).offset(0);
        make.width.height.mas_equalTo(WKDHPX(32));
    }];
    [self.dataView addSubview:self.weightLab];
    [self.weightLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.healthTypeImageView.mas_right).offset(WKDHPX(16));
        make.right.equalTo(self.contentView.mas_right).offset(-WKDHPX(52));
        make.centerY.equalTo(self.healthTypeImageView.mas_centerY).offset(-WKDHPX(12));
    }];
    [self.dataView addSubview:self.describeLab];
    [self.describeLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.healthTypeImageView.mas_right).offset(WKDHPX(16));
        make.right.equalTo(self.contentView.mas_right).offset(-WKDHPX(52));
        make.top.equalTo(self.weightLab.mas_bottom).offset(WKDHPX(2));
    }];
    [self.dataView addSubview:self.deleteBtn];
    [self.deleteBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.contentView.mas_right).offset(-WKDHPX(16));
        make.centerY.equalTo(self.healthTypeImageView.mas_centerY);
        make.width.height.mas_equalTo(WKDHPX(20));
    }];
    [self.contentView addSubview:self.lineView];
    [self.lineView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@(kLeftMarginSpace));
        make.height.equalTo(@(1));
        make.bottom.right.equalTo(@0);
    }];
}


- (void)lookAtButtonClick {
//    if (self.clickShow) {
//        self.clickShow(self);
//    }
}

-(void)setRecord:(MRKHealthRecordModel *)record {
    if (record) {
        _record = record;
//        self.timeLab.text = record.dayTime;
//        self.timeLab.hidden = !record.isDayLastest;
//
//        [self.timeLab mas_updateConstraints:^(MASConstraintMaker *make) {
//            make.height.equalTo(@(0.5));
//        }];
        NSMutableAttributedString *str1 = [[NSMutableAttributedString alloc]initWithString: [NSString stringWithFormat:@"体重 %@ 公斤   " , record.weight] attributes:@{NSFontAttributeName : [UIFont fontWithName:fontNameMeDium size:WKDHPX(16)] , NSForegroundColorAttributeName : [UIColor colorWithHexString:titleNormalColor1]}];
        NSMutableAttributedString *str4 = [[NSMutableAttributedString alloc]initWithString: [NSString stringWithFormat:@"%@" , [MRKTimeManager stringFromTimeInterval:record.dateTime format:@"HH:mm"]] attributes:@{NSFontAttributeName : [UIFont fontWithName:fontNamePing size:WKDHPX(13)] , NSForegroundColorAttributeName : [UIColor colorWithHexString:titleNormalColor3]}];
        [str1 appendAttributedString:str4];
        [self.deleteBtn setImage:[UIImage imageNamed:@"icon_arrow_right"] forState:UIControlStateNormal];
        self.weightLab.attributedText = str1;
        self.describeLab.text = [NSString stringWithFormat:@"BMI %@       体脂率 %@%%" , [record.bmi isNotEmpty]  ? record.bmi : @"--" , [record.bodyFatRate isNotEmpty] ?  record.bodyFatRate : @"--"];
        self.healthTypeImageView.image = [UIImage imageNamed:(record.insertType.intValue == 1 || record.insertType.intValue == 3) ? @"icon_scale_history" : @"icon_Manual_record"];
//        if (record.insertType.intValue == 1 || record.insertType.intValue == 3) {
//            //自动
//            [self.weightLab mas_updateConstraints:^(MASConstraintMaker *make) {
//                make.centerY.equalTo(self.healthTypeImageView.mas_centerY).offset(-WKDHPX(7));
//            }];
//            self.describeLab.hidden = self.deleteBtn.hidden = NO;
//        } else {
//            //手动
//            [self.weightLab mas_updateConstraints:^(MASConstraintMaker *make) {
//                make.centerY.equalTo(self.healthTypeImageView.mas_centerY);
//            }];
//            self.describeLab.hidden = self.deleteBtn.hidden = YES;
//        }
    }
}



- (UILabel *)timeLab{
    if (!_timeLab) {
        UILabel *lab = [[UILabel alloc] init];
        lab.textColor = UIColorHex(#848A9B);
        lab.font = [UIFont fontWithName:fontNamePing size:WKDHPX(13)];
//        lab.text = @"4月24日";
        lab.backgroundColor = UIColorHex(#F6F6F8);
        _timeLab = lab;
    }
    return _timeLab;
}

- (UIImageView *)healthTypeImageView{
    if (!_healthTypeImageView) {
        _healthTypeImageView = [[UIImageView alloc] init];
//        _healthTypeImageView.backgroundColor = UIColor.redColor;
    }
    return _healthTypeImageView;
}

- (UILabel *)weightLab{
    if (!_weightLab) {
        UILabel *lab = [[UILabel alloc] init];
        lab.textColor = [UIColor colorWithHexString:titleNormalColor1];
        lab.font = [UIFont fontWithName:fontNameMeDium size:WKDHPX(16)];
        lab.text = @"体重 --kg";
        _weightLab = lab;
    }
    return _weightLab;
}

- (UILabel *)describeLab{
    if (!_describeLab) {
        UILabel *lab = [[UILabel alloc]init];
        lab.textColor = [UIColor colorWithHexString:titleNormalColor1];
        lab.font = [UIFont fontWithName:fontNamePing size:WKDHPX(13)];
        lab.text = @"BMI-- 体脂率 --%";
        _describeLab = lab;
    }
    return _describeLab;
}

- (UIButton *)deleteBtn{
    if (!_deleteBtn) {
        _deleteBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_deleteBtn setImage:[UIImage imageNamed:@"icon_health_delete"] forState:UIControlStateNormal];
        _deleteBtn.userInteractionEnabled = NO;
//        [_deleteBtn addTarget:self action:@selector(deleteBtnClick:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _deleteBtn;
}

- (void)deleteBtnClick:(UIButton *)sender{
    if (_deleteRecordBlock) {
        _deleteRecordBlock(sender);
    }
}

- (UIView *)dataView {
    if (!_dataView) {
        _dataView = [UIView new];
    }
    return _dataView;
}

- (UIView *)lineView {
    if (!_lineView) {
        _lineView = [UIView new];
        _lineView.backgroundColor = UIColorHex(#F6F6F8);
    }
    return _lineView;
}

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];
    
    // Configure the view for the selected state
}

@end

@interface MRKHealthHistoryTopCell ()
@property(nonatomic, strong) UILabel *timeLab;

@end

@implementation MRKHealthHistoryTopCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self =[super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        [self addSubview];
    }
    return self;
}

- (void)addSubview {
    self.backgroundColor = [UIColor clearColor];
    UIView *bgView = [[UIView alloc] init];
    bgView.backgroundColor = [UIColor whiteColor];
    [self.contentView addSubview:bgView];
    [bgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self.contentView);
        make.top.equalTo(self.contentView.mas_top).offset(WKDHPX(8));
        make.height.mas_equalTo(WKDHPX(34));
    }];
    
    [bgView addSubview:self.timeLab];
    [self.timeLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(bgView.mas_left).offset(WKDHPX(16));
        make.right.equalTo(bgView.mas_right).offset(-WKDHPX(16));
        make.top.equalTo(bgView.mas_top).offset(0);
        make.height.mas_equalTo(WKDHPX(34));
    }];
}

-(void)setRecord:(MRKHealthRecordModel *)record {
    if (record) {
        _record = record;
        self.timeLab.text = record.dayTime;
    }
}

- (UILabel *)timeLab{
    if (!_timeLab) {
        UILabel *lab = [[UILabel alloc] init];
        lab.textColor = [UIColor colorWithHexString:titleNormalColor3];
        lab.font = kSystem_Font_NoDHPX(WKDHPX(13));
        _timeLab = lab;
    }
    return _timeLab;
}

@end


