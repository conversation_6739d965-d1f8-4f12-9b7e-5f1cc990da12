//
//  MRKCoachFineController.m
//  Student_IOS
//
//  Created by merit on 2022/2/23.
//

#import "MRKCoachFindController.h"
#import "MRKCoachCardCell.h" //教练卡的cell
#import "MRKCoachDetailController.h" //教练详情
#import "MRKTraceManager.h"

@interface MRKCoachFindController ()<UICollectionViewDelegate>

@property (nonatomic, strong) UICollectionView *collectionView;
@property (nonatomic, strong) DataSourceModel *viewModel;  //辅助展示的类
@property (nonatomic, strong) NSMutableArray *dataArray;   //展示的数据源
@property (nonatomic, assign) int page;  //当前页数

@end

@implementation MRKCoachFindController

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    
    [self initUI];
    
    [self initData];
}
/// 初始化UI
- (void)initUI {
    self.navTitle = @"找教练";
    
    [self.view addSubview:self.collectionView];
    [self.collectionView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(@(0));
        make.top.equalTo(self.mrk_navgationBar.mas_bottom).offset(0);
        make.bottom.mas_equalTo(-SafeBottom);
    }];
}
///初始化数据
- (void)initData {
    self.dataArray = [NSMutableArray array];
    @weakify(self);
    self.viewModel = [[DataSourceModel alloc] initWithCellID:NSStringFromClass([MRKCoachCardCell class])
                                          configureCellBlock:^(MRKCoachCardCell * cell, id  _Nonnull item, NSIndexPath * _Nonnull indexPath) {
        @strongify(self);
        //填充cell
        MRKCoachFindModel *model = [self.dataArray objectAtIndex:indexPath.row];
        [cell setCoachDetail:model];
        
        cell.focusBlock = ^{
            @strongify(self);
            if ([model.isFollow isEqualToString:@"1"]) {
                // 取消关注
                [self jxt_showAlertWithTitle:nil
                                     message:@"确认取消关注？"
                           appearanceProcess:^(JXTAlertController * _Nonnull alertMaker) {
                    alertMaker.
                    addActionDefaultTitle(@"取消").
                    addActionDefaultTitle(@"确定");
                } actionsBlock:^(NSInteger buttonIndex, UIAlertAction * _Nonnull action, JXTAlertController * _Nonnull alertSelf) {
                    @strongify(self);
                   if (buttonIndex == 1) {
                       [[MRKTraceManager sharedInstance] manualUploadTraceType:2 pageTitle:self.title pageId:self.tracePageId eventId:@"btn_profile_followedcoach_unfollow_confirm" route:self.tracePageRoute duration:0 extendPara:@{}];
                       [self focusCoach:@0 coachModel:model];
                   }else {
                       [[MRKTraceManager sharedInstance] manualUploadTraceType:2 pageTitle:self.title pageId:self.tracePageId eventId:@"btn_profile_followedcoach_unfollow_cancel" route:self.tracePageRoute duration:0 extendPara:@{}];
                   }
                }];
            }else {
                // 关注
                [self focusCoach:@1 coachModel:model];
            }
        };
    }];
    self.collectionView.dataSource = self.viewModel;
    // 数据源绑定
    RAC(self.viewModel,dataSource) = RACObserve(self, dataArray);
    
    
    MrkEmptyView *emptyView = [MrkEmptyView emptyViewWithImage:[UIImage imageNamed:@"icon_notes_holder"]
                                                      titleStr:@""
                                                     detailStr:@"暂无数据"];
    emptyView.errorBtnClickBlock = ^{
        [self_weak_.collectionView.mj_header beginRefreshing];
    };
    self.collectionView.pageEmptyView = emptyView;
    
    
    MJDIYHeader *header = [MJDIYHeader headerWithRefreshingBlock:^{
        @strongify(self);
        self.page = 1;
        [self refresh];
    }];
    header.automaticallyChangeAlpha = YES;
    self.collectionView.mj_header = header;
    
    MJDIYAutoFooter *footer = [MJDIYAutoFooter footerWithRefreshingBlock:^{
        @strongify(self);
        self.page ++;
        [self refresh];
    }];
    footer.automaticallyChangeAlpha = YES;
    self.collectionView.mj_footer = footer;
    
    
    [self.collectionView.mj_header beginRefreshing];
    
}

#pragma mark - 网络请求

/// 请求第一页数据
- (void)refresh {
    NSDictionary *parms = @{@"current":@(self.page),@"size":@"10"};
    [MRKBaseRequest mrkGetRequestUrl:@"/user/coach/coachPage"
                             andParm:parms
            completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        [self.collectionView.mj_header endRefreshing];
        [self.collectionView.mj_footer endRefreshing];
        
        if (self.page == 1){
            [self.dataArray removeAllObjects];
        }
        
        id data = [request.responseObject valueForKeyPath:@"data.records"];
        NSArray *dataArray = [NSArray modelArrayWithClass:[MRKCoachFindModel class] json:data];
        [self.dataArray addObjectsFromArray:dataArray];
        [self.collectionView reloadData];
        
        NSNumber *total = [request.responseObject valueForKeyPath:@"data.total"];
        self.collectionView.mj_footer.hidden = self.dataArray.count >= total.integerValue; //是否还有下一页请求
        
        {//无数据展位图
            BOOL haveData = self.dataArray.count > 0;
            [self.collectionView hiddenEmptyView:haveData];
        }
        
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        [self.collectionView.mj_header endRefreshing];
        [self.collectionView.mj_footer endRefreshing];
        
        if ([self.collectionView collectionViewIsEmptyData]){
            [self.collectionView mrkShowNetworkErrorEmptyView];
        }
    }];
}

/// 关注/取消关注教练
/// @param isFocus 关注，还是取消
- (void)focusCoach:(NSNumber *)isFocus coachModel:(MRKCoachFindModel *)model {
    [MRKBaseRequest mrkPostRequestUrl:@"/user/follow"
                              andParm: @{@"operation":isFocus,
                                         @"coachId":model.cid?:@""}
             completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        // 修改模型状态，刷新列表
        model.isFollow = [isFocus isEqualToNumber:@1] ? @"1" : @"0";
        [self.collectionView reloadData];
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        
    }];
}


#pragma mark - UICollectionViewDelegate
- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
    MRKCoachDetailController * detail = [[MRKCoachDetailController alloc]init];
    MRKCoachFindModel *model = [self.dataArray objectAtIndex:indexPath.row];
    detail.showCocoaId = model.cid;
    [self.navigationController pushViewController:detail animated:YES];
}

#pragma mark - lazy
/// 初始化UI
- (UICollectionView *)collectionView {
    if (!_collectionView) {
        
        UICollectionViewFlowLayout *layout = [[UICollectionViewFlowLayout alloc] init];
        CGFloat space1 = kLeftMarginSpace;
        CGFloat space2 = DHPX(11);
        CGFloat itemW = (MainWidth - 2 * kLeftMarginSpace - space2) / 2.0;
        CGFloat itemH = itemW * 1.115;
        layout.itemSize = CGSizeMake(itemW, itemH);  //cell的大小
        layout.sectionInset = UIEdgeInsetsMake(space1, space1, space1, space1);  //cell每段的间距
        layout.minimumLineSpacing = space1;  //cell上下间距
        layout.minimumInteritemSpacing = space2;  //cell左右间距
        
        _collectionView = [[UICollectionView alloc] initWithFrame:CGRectZero collectionViewLayout:layout];
        _collectionView.delegate = self;
        _collectionView.showsHorizontalScrollIndicator = NO;
        _collectionView.backgroundColor = UIColor.clearColor;
        [_collectionView registerClass:[MRKCoachCardCell class] forCellWithReuseIdentifier:NSStringFromClass([MRKCoachCardCell class])];
    }
    return _collectionView;
}


/// 是否需要导航条
- (BOOL)viewControllerIsNeedNavBar:(MRKBaseController *)viewController {
    return YES;
}

@end




@implementation MRKCoachFindModel
@end
