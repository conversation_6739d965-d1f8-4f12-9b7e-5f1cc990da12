//
//  MRKCoachCardCell.m
//  Student_IOS
//
//  Created by merit on 2022/2/23.
//

#import "MRKCoachCardCell.h"
#import "MRKCoachFindController.h"

@interface MRKCoachCardCell ()
/// 背景
@property (nonatomic, strong) UIView *bgView;
/// 背景图像
@property (nonatomic, strong) UIImageView *bgImageView;
/// 教练头像
@property (nonatomic, strong) UIImageView *avatarImageView;
/// 教练名称
@property (nonatomic, strong) UILabel *nameLabel;
/// 教练职称
@property (nonatomic, strong) UILabel *coachTitleLabel;
/// 关注状态
@property (nonatomic, strong) UIButton *focusButton;

@end

@implementation MRKCoachCardCell

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self cellInitUI];
    }
    return self;
}

/// cell添加UI
- (void)cellInitUI {
    
    //添加背景
    [self.contentView addSubview:self.bgView];
    [self.bgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsMake(0, 0, 0, 0));
    }];
    
    //添加背景图像
    [self.bgView addSubview:self.bgImageView];
    [self.bgImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(8);
        make.centerX.equalTo(self.bgView.mas_centerX);
        make.left.right.equalTo(@0);
    }];
    
    //添加头像
    [self.bgImageView addSubview:self.avatarImageView];
    [self.avatarImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsMake(0, 0, 0, 0));
    }];
    
    //添加名字
    [self.bgView addSubview:self.nameLabel];
    [self.nameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.bgImageView.mas_bottom).offset(6);
        make.left.mas_equalTo(DHPX(16));
        make.right.mas_equalTo(-DHPX(16));
        make.height.mas_equalTo(DHPX(18));
    }];
    
    //添加职称
    [self.bgView addSubview:self.coachTitleLabel];
    [self.coachTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.nameLabel.mas_bottom).offset(4);
        make.left.mas_equalTo(DHPX(16));
        make.right.mas_equalTo(-DHPX(16));
        make.height.mas_equalTo(DHPX(18));
    }];
    
    //关注
    [self.bgView addSubview:self.focusButton];
    [self.focusButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.coachTitleLabel.mas_bottom).offset(DHPX(8));
        make.centerX.equalTo(self.bgView.mas_centerX);
        make.width.equalTo(self.bgView.mas_width).multipliedBy(0.5);
        make.bottom.equalTo(self.bgView.mas_bottom).offset(-DHPX(12));
        make.height.mas_equalTo(DHPX(24));
    }];
}

/// 更改关注按钮状态
/// @param isFocus 是否关注
- (void)coachFocus:(BOOL)isFocus {
    if (isFocus) {
        [_focusButton setBackgroundColor:UIColor.whiteColor];
        _focusButton.layer.borderColor =  [UIColor colorWithHexString:@"#16D2E3"].CGColor;
        _focusButton.layer.borderWidth = 1.0;
        [_focusButton setTitle:@"已关注" forState:UIControlStateNormal];
        [_focusButton setTitleColor: [UIColor colorWithHexString:@"#16D2E3"] forState:UIControlStateNormal];
    }else {
        [_focusButton setBackgroundColor: [UIColor colorWithHexString:@"#16D2E3"]];
        [_focusButton setTitle:@"关注" forState:UIControlStateNormal];
        [_focusButton setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
    }
}

/// 点击是否关注按钮
- (void)focusAction:(UIButton *)btn {
    if (self.focusBlock) {
        self.focusBlock();
    }
}

- (void)setCoachDetail:(MRKCoachFindModel *)model {
    [self.avatarImageView sd_setImageWithURL:[NSURL URLWithString:model.selectAvatar] placeholderImage:UserInfo.avatarHoldingImage];
    self.nameLabel.text = model.name;
    self.coachTitleLabel.text = model.title;
    [self coachFocus:[model.isFollow isEqualToString:@"1"]];
}

#pragma mark - lazy
/// 初始化UI
/// 背景
- (UIView *)bgView {
    if (!_bgView) {
        _bgView = [[UIView alloc] init];
        _bgView.backgroundColor = UIColor.whiteColor;
        _bgView.layer.cornerRadius = 8;
        //阴影
        _bgView.layer.shadowColor =[UIColor colorWithHexString:@"#E8EAF2"].CGColor;
        _bgView.layer.shadowOffset = CGSizeMake(1,2);
        _bgView.layer.shadowOpacity = 1;
        _bgView.layer.shadowRadius = DHPX(20);
    }
    return _bgView;
}
/// 背景图像
- (UIImageView *)bgImageView {
    if (!_bgImageView) {
        _bgImageView = [[UIImageView alloc] init];
    }
    return _bgImageView;
}
///头像
- (UIImageView *)avatarImageView {
    if (!_avatarImageView) {
        _avatarImageView = [[UIImageView alloc] init];
        _avatarImageView.contentMode = UIViewContentModeScaleAspectFit;
    }
    return _avatarImageView;
}
/// 名字
- (UILabel *)nameLabel {
    if (!_nameLabel) {
        _nameLabel = [[UILabel alloc] init];
        _nameLabel.textColor = [UIColor colorWithHexString:titleNormalColor1];
        _nameLabel.font = [UIFont fontWithName:fontNameMeDium size:14];
        _nameLabel.textAlignment = NSTextAlignmentCenter;
    }
    return _nameLabel;
}
/// 职称
- (UILabel *)coachTitleLabel {
    if (!_coachTitleLabel) {
        _coachTitleLabel = [[UILabel alloc] init];
        _coachTitleLabel.textColor = [UIColor colorWithHexString:titleNormalColor3];
        _coachTitleLabel.font = [UIFont fontWithName:fontNamePing size:12];
        _coachTitleLabel.textAlignment = NSTextAlignmentCenter;
    }
    return _coachTitleLabel;
}
/// 关注按钮
- (UIButton *)focusButton {
    if (!_focusButton) {
        _focusButton = [[UIButton alloc] init];
        _focusButton.titleLabel.font = [UIFont fontWithName:fontNameMeDium size:12];
        _focusButton.layer.cornerRadius = DHPX(12);
        [_focusButton addTarget:self action:@selector(focusAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _focusButton;
}
@end
