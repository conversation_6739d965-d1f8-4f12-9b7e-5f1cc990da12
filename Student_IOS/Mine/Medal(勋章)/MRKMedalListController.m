//
//  MRKMedalListController.m
//  Student_IOS
//
//  Created by merit on 2022/6/29.
//

#import "MRKMedalListController.h"
#import "MRKMedalDetailController.h"
#import "MRKMedalView.h"
#import "MRKShareImageController.h"
#import "UIViewController+ShareImage.h"
#import "MRKMedalViewModel.h"

@interface MRKMedalListController ()<UICollectionViewDataSource, UICollectionViewDelegate, UICollectionViewDelegateFlowLayout>
@property (nonatomic, strong) UICollectionView *collectionView; // 勋章展示列表
@property (nonatomic, strong) NSMutableArray *dataArray;
@property (nonatomic, assign) int page;
@end

@implementation MRKMedalListController

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    // 添加下拉刷新
    self.dataArray = [NSMutableArray array];
    
    [self initUI];
}

- (void)initUI {
    [self.view addSubview:self.collectionView];
    [self.collectionView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsMake(0, 0, 0, 0));
    }];
    
    @weakify(self);
    MrkEmptyView *emptyView = [MrkEmptyView emptyViewWithImage:[UIImage imageNamed:@"icon_notes_holder"]
                                                      titleStr:@""
                                                     detailStr:@"一枚勋章都没点亮"];
    emptyView.errorBtnClickBlock = ^{
        [self.collectionView.mj_header beginRefreshing];
    };
    self.collectionView.pageEmptyView = emptyView;
    

    MJDIYHeader *header = [MJDIYHeader headerWithRefreshingBlock:^{
        @strongify(self);
        self.page = 1;
        [self requestData];
    }];
    header.automaticallyChangeAlpha = YES;
    self.collectionView.mj_header = header;
    
    MJRefreshAutoNormalFooter *footer = [MJRefreshAutoNormalFooter footerWithRefreshingBlock:^{
        @strongify(self);
        self.page++;
        [self requestData];
    }];
    footer.automaticallyChangeAlpha = YES;
    self.collectionView.mj_footer = footer;
    
    // 请求数据
    [self.collectionView.mj_header beginRefreshing];
}

/// 请求勋章数据
- (void)requestData {
    
    @weakify(self);
    [MRKMedalViewModel requestMedalList:self.page
                             categoryID:self.category.categoryId
                                success:^(NSArray<MRKMedalListModel *> * _Nonnull data, NSInteger total) {
        @strongify(self);
        
        [self endRefresh];
        if (self.page == 1) {
            [self.dataArray removeAllObjects];
        }
        [self.dataArray addObjectsFromArray:data];
        // 判断是否还有下一页数据
        BOOL isMore = self.dataArray.count < total;
        self.collectionView.mj_footer.hidden = !isMore;
        [self.collectionView reloadData];
        
        // 添加无数据占位图
        BOOL haveData = self.dataArray.count > 0;
        [self.collectionView hiddenEmptyView:haveData];
    } failure:^{
        [self endRefresh];
        
        if ([self.collectionView collectionViewIsEmptyData]){
            [self.collectionView mrkShowNetworkErrorEmptyView];
        }
    }];
    
}

- (void)endRefresh {
    if (self.collectionView.mj_header.isRefreshing) {
        [self.collectionView.mj_header endRefreshing];
    }
    if (self.collectionView.mj_footer.isRefreshing) {
        [self.collectionView.mj_footer endRefreshing];
    }
}


#pragma mark - UICollectionViewDataSource Methods
- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return self.dataArray.count;
}

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    MRKMedalListCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:NSStringFromClass([MRKMedalListCell class]) forIndexPath:indexPath];
    MRKMedalListModel *model = self.dataArray[indexPath.row];
    [cell setMedalDetailWith:model];
    return cell;
}

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
    MRKMedalListModel *model = self.dataArray[indexPath.row];
    if ([model.medalInfoId longLongValue] > 0) {
        MRKMedalDetailController *detailVC = [[MRKMedalDetailController alloc] init];
        detailVC.medalInfoId = model.medalInfoId;
        collectionView.traceEventId = @"btn_medal_click";
        collectionView.tracePara = @{@"medal_id": model.medalInfoId};
        detailVC.medalTitle = model.groupName;
        detailVC.growUserModel = self.growUserModel;
        [self.navigationController pushViewController:detailVC animated:YES];
    }
}


#pragma mark - lazy

- (UICollectionView *)collectionView {
    if (!_collectionView) {
        UICollectionViewFlowLayout *layout = [[UICollectionViewFlowLayout alloc] init];
        layout.sectionInset = UIEdgeInsetsMake((16), (16), (16), (16));
        layout.minimumLineSpacing = 0.0;
        layout.minimumInteritemSpacing = 0.0;
        //        layout.estimatedItemSize = CGSizeMake(200, 200);
        //        layout.itemSize = UICollectionViewFlowLayoutAutomaticSize;
        layout.itemSize = CGSizeMake((MainWidth - 32) / 3.0 - 8, (MainWidth - 32) / 3.0 - 8 + DHPX(50));
        
        _collectionView = [[UICollectionView alloc] initWithFrame:CGRectZero collectionViewLayout:layout];
        _collectionView.dataSource = self;
        _collectionView.delegate = self;
        _collectionView.backgroundColor = [UIColor whiteColor];
        [_collectionView registerClass:[MRKMedalListCell class] forCellWithReuseIdentifier:NSStringFromClass([MRKMedalListCell class])];
    }
    return _collectionView;
}


#pragma mark - JXCategoryListContentViewDelegate
/**
 实现 <JXCategoryListContentViewDelegate> 协议方法，返回该视图控制器所拥有的「视图」
 */
- (UIView *)listView {
    return self.view;
}


@end
