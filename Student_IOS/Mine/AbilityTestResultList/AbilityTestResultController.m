//
//  AbilityTestResultController.m
//  Student_IOS
//
//  Created by <PERSON><PERSON> on 2024/7/5.
//

#import "AbilityTestResultController.h"
#import "MJDIYHeader.h"


#import "MRKDeviceConnectAlertView.h"
#import "AbilityIntroduceVC.h"
#import "AbilityReportVC.h"
#import "MRKDeviceListManager.h"

@interface AbilityTestResultController ()<UITableViewDelegate,UITableViewDataSource>
@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) NSMutableArray *dataArray;
@property (nonatomic, assign) int page;
@property (nonatomic, assign) int SelectReducepage;
@end

@implementation AbilityTestResultController

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.mrkContentView.backgroundColor = UIColorHex(#05182C);
    self.navTitleColor = [UIColor whiteColor];
    self.navTitle = @"测评记录";
    self.dataArray = [[NSMutableArray alloc]init];
    
    [self viewLayOut];
    // Do any additional setup after loading the view.
}

- (void)viewLayOut {
    self.tableView.contentInset = UIEdgeInsetsMake(WKDHPX(16), 0, WKDHPX(16), 0);
    [self.mrkContentView addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsMake(kNavBarHeight, 0, 0, 0));
    }];
    
    @weakify(self);
    MrkEmptyView *emptyView = [MrkEmptyView emptyViewWithImage:[UIImage imageNamed:@"icon_content_holder"]
                                                      titleStr:@""
                                                     detailStr:@"暂无记录"];
    emptyView.detailLabTextColor = [UIColor whiteColor];
    emptyView.detailLabFont = kSystem_Font_NoDHPX(18);
    emptyView.fullCoverSuperView = YES;
    emptyView.tapEmptyViewBlock = ^{
        [self_weak_.tableView.mj_header beginRefreshing];
    };
    self.tableView.pageEmptyView = emptyView;
    
    MJDIYHeader *header = [MJDIYHeader headerWithRefreshingBlock:^{
        @strongify(self);
        self.page = 1;
        [self refreshMore];
    }];
    header.automaticallyChangeAlpha = YES;
    _tableView.mj_header = header;
    
    MJDIYAutoFooter *footer = [MJDIYAutoFooter footerWithRefreshingBlock:^{
        @strongify(self);
        self.page++;
        [self refreshMore];
    }];
    footer.automaticallyChangeAlpha = YES;
    _tableView.mj_footer = footer;
    
    [self.tableView.mj_header beginRefreshing];
}

- (void)refreshMore {
    NSMutableDictionary *parms = [NSMutableDictionary dictionary];
    [parms setObject:@(self.page)   forKey:@"current"];
    [parms setObject:@(kPageNumber) forKey:@"size"];
    [MRKBaseRequest mrkGetRequestUrl:@"/user/athleticismAssess/page"
                             andParm:parms
            completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        [self.tableView endRefresh];
        [self.tableView hiddenEmptyView];
        if (self.page == 1) {
            [self.dataArray removeAllObjects];
        }
        id data = [request.responseObject objectForKey:@"data"];
        id records = [data valueForKeyPath:@"records"];
        NSArray *array = [NSArray modelArrayWithClass:[MRKAbilityTestModel class] json:records];
        [self.dataArray addObjectsFromArray:array];
        [self.tableView reloadData];
        [self.tableView hiddenEmptyView:self.dataArray.count > 0];
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        [self.tableView endRefresh];
        if (self.dataArray.count <= 0){
            [self.tableView mrkShowNetworkErrorEmptyView];
        }
    }];
}

#pragma mark - UITableViewDelegate,UITableViewDataSource -
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.dataArray.count;
}
- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return UITableViewAutomaticDimension;
}
- (CGFloat)tableView:(UITableView *)tableView estimatedHeightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return UITableViewAutomaticDimension;
}
- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    static NSString *identifier = @"AbilityTestResultCell";
    AbilityTestResultCell * cell = [tableView dequeueReusableCellWithIdentifier:identifier];
    if (!cell) {
        cell = [[AbilityTestResultCell alloc]initWithStyle:UITableViewCellStyleDefault reuseIdentifier:identifier];
    }
    MRKAbilityTestModel *model = [self.dataArray objectAtIndex:indexPath.row];
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    cell.backgroundColor = UIColor.clearColor;
    cell.model = model;
    return cell;
}
- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    MRKAbilityTestModel *model = [self.dataArray objectAtIndex:indexPath.row];
//    if (model.isFinish){
        [self checkTest:model];
//        return;
//    }
//    [self beginTest:model];
}



//#pragma mark - 能力测评
//- (void)beginTest:(MRKAbilityTestModel *)model{
//    MRKDeviceModel *device = [self equimentDevice:model.productId];
//    if (device) {
//        if ([device.deviceUserRelId  isNotBlank] && [device.productId isNotBlank]){
//            AbilityIntroduceVC *vc = [[AbilityIntroduceVC alloc] init];
//            vc.deviceRelId = device.deviceUserRelId;
//            vc.equipmentTypeId = device.productId ;
//            [self.navigationController pushViewController:vc animated:YES];
//        } else {
//            [MBProgressHUD showMessage:@"参数异常"];
//        }
//    } else {
//        // 绑定设备弹窗
//        [MrkAlertManager showTopImageAlert:@"img_popover"
//                                     title:@"当前还未连接设备"
//                                   message:@"连接健身设备即可进行对应设备的能力评测"
//                                    cancel:@"暂不连接"
//                                    ensure:@"连接设备"
//                               handleIndex:^(NSInteger index) {
//            if (1 == index) {
//                [MRKDeviceConnectAlertView gotoSearchControllerVC:@""];
//            }
//        }];
//    }
//}

#pragma mark - 查看运动报告
- (void)checkTest:(MRKAbilityTestModel *)model {
//    MRKDeviceModel *device = [self equimentDevice:model.productId];
    AbilityReportVC *vc = [[AbilityReportVC alloc] init];
    vc.assessId = model.cid?:@"";
//    vc.canTestAgain = NO;
//    vc.deviceRelId = device.deviceUserRelId ?:@"";
    vc.equipmentTypeId = model.productId ?:@"";
    [self.navigationController pushViewController:vc animated:YES];
}

- (MRKDeviceModel *)equimentDevice:(NSString *)productId{
    __block MRKDeviceModel *model = nil;
    NSArray<MRKDeviceModel *> *deviceArr = [MRKDeviceListManager shareManager].deviceArray;
    [deviceArr enumerateObjectsUsingBlock:^(MRKDeviceModel *  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if (obj.productId.intValue == productId.intValue) {
            model = obj;
            *stop = YES;
        }
    }];
    return model;
}

- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
        _tableView.backgroundColor = [UIColor clearColor];
        _tableView.delegate = self;
        _tableView.dataSource = self;
        _tableView.rowHeight = UITableViewAutomaticDimension;/// 高度自适应
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.showsVerticalScrollIndicator = NO;
        _tableView.showsHorizontalScrollIndicator = NO;
        [_tableView registerClass:[AbilityTestResultCell class] forCellReuseIdentifier:@"AbilityTestResultCell"];
    }
    return _tableView;
}

#pragma mark ---------Delegate -----------
- (BOOL)viewControllerIsNeedNavBar:(MRKBaseController *)viewController {
    return YES;
}

- (BOOL)mrkNavigationBarIsHideBottomLine:(MRKNavigationBar *)navigationBar {
    return YES;
}

- (UIStatusBarStyle)navControllerStatusBarStyle:(MRKBaseController *)viewController{
    return UIStatusBarStyleLightContent;
}

- (UIImage *)mrkNavigationBarLeftButtonImage:(UIButton *)leftButton navigationBar:(MRKNavigationBar *)navigationBar {
    return [UIImage imageNamed:@"icon_back-4"];
}

/** 背景色 */
- (UIColor *)mrkNavigationBarBackgroundColor:(MRKNavigationBar *)navigationBar{
    return [UIColor colorWithWhite:1 alpha:0.0];
}

/*
 #pragma mark - Navigation
 
 // In a storyboard-based application, you will often want to do a little preparation before navigation
 - (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
 // Get the new view controller using [segue destinationViewController].
 // Pass the selected object to the new view controller.
 }
 */

@end





@interface AbilityTestResultCell ()
@property (nonatomic, strong) UIView *backContentView;
@property (nonatomic, strong) UILabel  *titleLab;
@property (nonatomic, strong) UILabel  *descripLab;
@property (nonatomic, strong) UILabel  *viewReportLab;
@property (nonatomic, strong) UIImageView *arrowImageView;
@property (nonatomic, strong) UILabel  *creatTimeLab;
@property (nonatomic, strong) UILabel  *testLab;
@end

@implementation AbilityTestResultCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier{
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.contentView.backgroundColor = [UIColor clearColor];
        
        [self.contentView addSubview:self.backContentView];
        [self.backContentView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.mas_equalTo(UIEdgeInsetsMake(0, WKDHPX(16), WKDHPX(12), WKDHPX(16)));
        }];
        
        [self.backContentView addSubview:self.titleLab];
        [self.titleLab mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.backContentView.mas_top).offset(WKDHPX(16));
            make.left.mas_equalTo(self.backContentView.mas_left).offset(WKDHPX(16));
        }];
        
        [self.backContentView addSubview:self.descripLab];
        [self.descripLab mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.titleLab.mas_bottom).offset(WKDHPX(10));
            make.left.mas_equalTo(self.backContentView.mas_left).offset(WKDHPX(16));
            make.bottom.mas_equalTo(self.backContentView.mas_bottom).offset(-WKDHPX(16));
        }];
        
        [self.backContentView addSubview:self.arrowImageView];
        [self.arrowImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.mas_equalTo(self.titleLab.mas_centerY);
            make.right.mas_equalTo(self.backContentView.mas_right).offset(-WKDHPX(10));
            make.size.mas_equalTo(CGSizeMake(WKDHPX(16), WKDHPX(16)));
        }];
        [self.backContentView addSubview:self.viewReportLab];
        [self.viewReportLab mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.mas_equalTo(self.arrowImageView.mas_centerY);
            make.right.mas_equalTo(self.arrowImageView.mas_left).offset(-WKDHPX(2));
        }];
        
        [self.backContentView addSubview:self.creatTimeLab];
        [self.creatTimeLab mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.mas_equalTo(self.descripLab.mas_centerY);
            make.right.mas_equalTo(self.backContentView.mas_right).offset(-WKDHPX(16));
        }];
        
        [self.backContentView addSubview:self.testLab];
        [self.testLab mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.mas_equalTo(self.backContentView.mas_centerY);
            make.right.mas_equalTo(self.backContentView.mas_right).offset(-WKDHPX(16));
            make.size.mas_equalTo(CGSizeMake(WKDHPX(64), WKDHPX(26)));
        }];
    }
    return self;
}

- (void)layoutSubviews{
    [super layoutSubviews];
}

- (void)setModel:(MRKAbilityTestModel *)model{
    _model = model;

    self.titleLab.attributedText = ({
        NSString *device = [MRKEquipmentTypeData deviceImageFromIconFont:model.productId];
        NSMutableAttributedString *deviceText = [[NSMutableAttributedString alloc] initWithString:device];
        deviceText.color = [UIColor whiteColor];
        deviceText.font = [UIFont fontWithName:@"iconfont" size:WKDHPX(24)];

        NSString *descrip = [NSString stringWithFormat:@"%@｜%@", model.productName, model.isFinish ? @"能力测评结果":@"暂无测评结果"];
        NSMutableAttributedString *attachText = [[NSMutableAttributedString alloc] initWithString:descrip];
        attachText.color = [UIColor whiteColor];
        attachText.font = kMedium_Font_NoDHPX(WKDHPX(14));
        [deviceText appendAttributedString:attachText];
        [deviceText addAttribute:NSBaselineOffsetAttributeName value:@(0.36 *(WKDHPX(24) -WKDHPX(14))) range:NSMakeRange(deviceText.length - attachText.length, attachText.length)];
        deviceText;
    });
    
    
    NSString *typeString = @"";
    int productId = model.productId.intValue;
    if (productId == BicycleEquipment) {
        typeString = @"骑行燃力值：";
    } else if(productId == BoatEquipment) {
        typeString = @"划行燃力值：";
    } else if(productId == EllipticalEquipment) {
        typeString = @"踏行燃力值：";
    } else if(productId == TreadmillEquipment) {
        typeString = @"跑步燃力值：";
    }
    if (model.isFinish){
        self.descripLab.attributedText = ({
            NSMutableAttributedString *attachText = [[NSMutableAttributedString alloc] initWithString:typeString];
            attachText.color = UIColorHex(#16D2E3);
            attachText.font = [UIFont systemFontOfSize:WKDHPX(14)];
            
            NSMutableAttributedString *dataText = [[NSMutableAttributedString alloc] initWithString:model.meritRate.stringValue?:@""];
            dataText.color = UIColorHex(#16D2E3);
            dataText.font = [UIFont systemFontOfSize:WKDHPX(20) weight:UIFontWeightHeavy];
            [attachText appendAttributedString:dataText];
            attachText.alignment = NSTextAlignmentCenter;
            attachText;
        });
        self.creatTimeLab.hidden = NO;
        self.creatTimeLab.text = model.createTime?:@"";
        self.arrowImageView.hidden = NO;
        self.viewReportLab.hidden = NO;
        
        self.testLab.hidden = YES;
    } else {
        self.descripLab.attributedText = ({
            NSMutableAttributedString *attachText = [[NSMutableAttributedString alloc] initWithString:@"快去测测你的运动能力吧～"];
            attachText.color = UIColorHex(#848A9B);
            attachText.font = [UIFont systemFontOfSize:WKDHPX(12)];
            attachText;
        });
        
        self.creatTimeLab.hidden = YES;
        self.arrowImageView.hidden = YES;
        self.viewReportLab.hidden = YES;
        
        self.testLab.hidden = NO;
    }
    
}

- (UIView *)backContentView {
    if (_backContentView) return _backContentView;
    UIView *view = [[UIView alloc] init];
    view.backgroundColor = [UIColor colorWithHexString:@"#213F60" alpha:0.2];
    view.layer.cornerRadius = WKDHPX(10);
    view.layer.masksToBounds = YES;
    _backContentView = view;
    return _backContentView;
}

- (UILabel *)titleLab {
    if (_titleLab) return _titleLab;
    UILabel *label = [[UILabel alloc] init];
    label.font = kMedium_Font_NoDHPX(WKDHPX(14));
    label.textColor = [UIColor whiteColor];
    _titleLab = label;
    return _titleLab;
}

- (UILabel *)descripLab {
    if (_descripLab) return _descripLab;
    UILabel *label = [[UILabel alloc] init];
    label.font = kSystem_Font_NoDHPX(WKDHPX(12));
    label.textColor = UIColorHex(#848A9B);
    _descripLab = label;
    return _descripLab;
}

- (UILabel *)creatTimeLab {
    if (_creatTimeLab) return _creatTimeLab;
    UILabel *label = [[UILabel alloc] init];
    label.font = kSystem_Font_NoDHPX(WKDHPX(12));
    label.textColor = UIColorHex(#848A9B);
    label.hidden = YES;
    _creatTimeLab = label;
    return _creatTimeLab;
}

- (UILabel *)viewReportLab {
    if (_viewReportLab) return _viewReportLab;
    UILabel *label = [[UILabel alloc] init];
    label.font = kSystem_Font_NoDHPX(WKDHPX(12));
    label.textColor = UIColorHex(#848A9B);
    label.text = @"查看报告";
    label.hidden = YES;
    _viewReportLab = label;
    return _viewReportLab;
}

- (UIImageView *)arrowImageView {
    if (_arrowImageView) return _arrowImageView;
    UIImageView *imagev = [[UIImageView alloc] init];
    imagev.contentMode = UIViewContentModeScaleAspectFill;
    imagev.image = [UIImage imageNamed:@"icon_cell_right_arrow"];
    imagev.hidden = YES;
    _arrowImageView = imagev;
    return _arrowImageView;
}

- (UILabel *)testLab {
    if (!_testLab){
        UILabel *label = [[UILabel alloc] init];
        label.font = kSystem_Font_NoDHPX(WKDHPX(14));
        label.textColor = [UIColor colorWithHexString:@"#16D2E3"];
        label.text = @"测一测";
        label.textAlignment = NSTextAlignmentCenter;
        label.layer.cornerRadius = WKDHPX(26)/2;
        label.layer.masksToBounds = YES;
        label.layer.borderColor = [UIColor colorWithHexString:@"#16D2E3"].CGColor;
        label.layer.borderWidth = CGFloatFromPixel(1.0);
        _testLab = label;
    }
    return _testLab;
}

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];
    
    // Configure the view for the selected state
}

@end

