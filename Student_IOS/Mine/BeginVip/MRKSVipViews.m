//
//  MRKSVipViews.m
//  Student_IOS
//
//  Created by merit on 2023/8/28.
//

#import "MRKSVipViews.h"
#import "SDCycleScrollView.h"
#import "UIImage+Helper.h"
#import "MRKVipPageModel.h"
#import "MRKVipCardModel.h"
#import "MRKVipPackageView.h"
#import "MRKTraceManager.h"
#import "UIView+AZGradient.h"
#import "SDCycleScrollView.h"



@interface MRKIAPMessageView()
@property (nonatomic, strong) UIImageView *backgroundImageView;
@property (nonatomic, strong) UIButton *exchangeAdvertBtn;

@property (nonatomic, strong) UIImageView *avatarImageView;
@property (nonatomic, strong) UILabel *nameLabel;
@property (nonatomic, strong) UILabel *remarkLabel;

@property (nonatomic, strong) UIImageView *rightsImageView;
@property (nonatomic, strong) UIImageView *bottomImageView;

@property (nonatomic, strong) AdvertModel *exchangeAdvert;       ///会员兑换按钮
@property (nonatomic, copy) NSString *vipType;
@end

@implementation MRKIAPMessageView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self addAllSubviews];
    }
    return self;
}

- (void)addAllSubviews {
    [self addSubview: self.backgroundImageView];
    [self.backgroundImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsZero);
    }];
    
    {
        [self addSubview: self.avatarImageView];
        [self.avatarImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.mas_top).offset(WKDHPX(30)+ kNavBarHeight);
            make.left.equalTo(self.mas_left).offset(WKDHPX(16));
            make.width.height.mas_equalTo(WKDHPX(38));
        }];
        
        [self addSubview: self.nameLabel];
        [self.nameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.avatarImageView.mas_right).offset(WKDHPX(8));
            make.top.equalTo(self.avatarImageView);
            make.right.equalTo(self.mas_right).offset(-WKDHPX(114));
        }];
        
        [self addSubview: self.remarkLabel];
        [self.remarkLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.nameLabel.mas_bottom).offset(WKDHPX(2));
            make.left.equalTo(self.nameLabel.mas_left);
            make.right.equalTo(self.mas_right).offset(-WKDHPX(114));
        }];
    }
    
    [self addSubview: self.exchangeAdvertBtn];
    [self.exchangeAdvertBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.avatarImageView.mas_centerY);
        make.right.equalTo(self.mas_right).offset(-WKDHPX(8));
        make.size.mas_equalTo(CGSizeMake(WKDHPX(90), WKDHPX(40)));
    }];
    @weakify(self);
    [[self.exchangeAdvertBtn rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(__kindof UIControl * _Nullable x) {
        @strongify(self);
        x.traceEventId = @"btn_profile_vip_redeem";
        [[NSNotificationCenter defaultCenter] postNotificationName:AdvertSkipToPageNotification
                                                            object:@{@"model":self.exchangeAdvert, @"position":@(1)}];
    }];
    
    
    [self addSubview: self.rightsImageView];
    [self.rightsImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.avatarImageView.mas_bottom).offset(WKDHPX(25));
        make.left.mas_equalTo(WKDHPX(16));
        make.right.mas_equalTo(-WKDHPX(16));
        make.height.mas_equalTo(self.rightsImageView.mas_width).multipliedBy(0.7);
        make.bottom.equalTo(self.mas_bottom);
    }];
    
    [self addSubview: self.bottomImageView];
    [self.bottomImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.mas_bottom);
        make.left.right.mas_equalTo(self);
        make.height.mas_equalTo(self.bottomImageView.mas_width).multipliedBy(0.1);
    }];
}

- (void)setExchangeAdvert:(AdvertModel *)exchangeAdvert{
    _exchangeAdvert = exchangeAdvert;
    NSString *url = exchangeAdvert.image;
    if ([url isNotBlank]) {
        self.exchangeAdvertBtn.hidden = NO;
        [self.exchangeAdvertBtn setContentHorizontalAlignment:UIControlContentHorizontalAlignmentFill];
        [self.exchangeAdvertBtn setContentVerticalAlignment:UIControlContentVerticalAlignmentFill];
        @weakify(self);
        [self.exchangeAdvertBtn setImageWithURL:[NSURL URLWithString:url]
                                       forState:UIControlStateNormal
                                    placeholder:nil
                                        options:YYWebImageOptionProgressiveBlur|YYWebImageOptionSetImageWithFadeAnimation
                                     completion:^(UIImage * _Nullable image, NSURL * _Nonnull url, YYWebImageFromType from, YYWebImageStage stage, NSError * _Nullable error) {
            @strongify(self);
            if (stage == YYWebImageStageFinished && image) {
                UIImage *newImage = [image imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
                [self.exchangeAdvertBtn setImage:newImage forState:UIControlStateNormal];
                self.exchangeAdvertBtn.imageView.tintColor = self.vipType.intValue == 30 ? UIColorHex(#FFE6CF): UIColorHex(#804E13);
            }
        }];
    }
}

- (void)configPageModel:(MRKVipPageModel *)pageModel vipType:(NSString *)vipType{
    self.vipType = vipType;
    NSInteger typeM = vipType.integerValue;
    switch (typeM) {
        case 10:
            self.backgroundImageView.image = [UIImage imageNamed:@"vip_message_background"];
            self.rightsImageView.image = [UIImage imageNamed:@"vip_message_content_background"];
            self.bottomImageView.image = [UIImage imageNamed:@"vip_message_background_bottom"];
            
            self.nameLabel.textColor = UIColorHex(#513007);
            break;
        case 30:
            self.backgroundImageView.image = [UIImage imageNamed:@"xvip_message_background"];
            self.rightsImageView.image = [UIImage imageNamed:@"xvip_message_content_background"];
            self.bottomImageView.image = [UIImage imageNamed:@"xvip_message_background_bottom"];
            
            self.nameLabel.textColor = UIColorHex(#FFE6CF);
            break;
            
        default:
            break;
    }
    
    ///兑换会员
    self.exchangeAdvert = pageModel.exchangeAdvert;
    
    [self setPurchaseViews:pageModel];
    
    MemberInfoDataDTO *currentInfoModel = nil;
    for (MemberInfoDataDTO *model in pageModel.vipInfomodel.items) {
        if (vipType.intValue == model.vipType){
            currentInfoModel = model;
            break;
        }
    }
    
    if (currentInfoModel == nil) return;
    if ([vipType isEqualToString:@"10"]) { // vip
        if (currentInfoModel.isExpire) {
            NSMutableAttributedString *attributedStr = [[NSMutableAttributedString alloc] initWithString:@"你还不是趣练VIP，开通立享各项权益"];
            attributedStr.color = UIColorHex(#513007);
            attributedStr.font = kSystem_Font_NoDHPX(WKDHPX(11));
            self.remarkLabel.attributedText = attributedStr;
            return;
        }
        
        NSMutableAttributedString *attributedStr = [[NSMutableAttributedString alloc] initWithString:[NSString stringWithFormat:@"趣练VIP 至 %@", currentInfoModel.expireDate]];
        attributedStr.color = UIColorHex(#513007);
        attributedStr.font = kSystem_Font_NoDHPX(WKDHPX(11));
        if (currentInfoModel.days <= 15 && currentInfoModel.days >0) {
            NSString *days = @(currentInfoModel.days).stringValue;
            NSString *partStr = [NSString stringWithFormat:@"  还剩 %@ 天", days];
            NSRange timeRang = [partStr rangeOfString:days];
            NSMutableAttributedString *text = [[NSMutableAttributedString alloc] initWithString:partStr];
            text.color = UIColorHex(#513007);
            text.font = kSystem_Font_NoDHPX(WKDHPX(11));
            [text setFont:kMedium_Font_NoDHPX(WKDHPX(14)) range:timeRang];
            [attributedStr appendAttributedString:text];
        }
        ///
        if ( pageModel.vipInfomodel.vipType == 20 && !pageModel.vipInfomodel.isExpire ) {
            [attributedStr appendAttributedString:[[NSMutableAttributedString alloc] initWithString:@"\n"]];
            
            NSString *partStr1 = @"正在使用SVIP权益中，VIP权益将在SVIP到期后继续生效";
            NSMutableAttributedString *text1 = [[NSMutableAttributedString alloc] initWithString:partStr1];
            text1.color = UIColorHex(#513007);
            text1.font = kSystem_Font_NoDHPX(WKDHPX(11));
            [attributedStr appendAttributedString:text1];
        }
        
        self.remarkLabel.attributedText = attributedStr;
        
    } else if ([vipType isEqualToString:@"30"]) { // 绝影
        if (currentInfoModel.isExpire) {
            NSMutableAttributedString *attributedStr = [[NSMutableAttributedString alloc] initWithString:@"你还不是会员，开通立享各项权益"];
            attributedStr.color = UIColorHex(#FAEBD8);
            attributedStr.font = kSystem_Font_NoDHPX(WKDHPX(11));
            self.remarkLabel.attributedText = attributedStr;
            
            return;
        }
        
        NSMutableAttributedString *attributedStr = [[NSMutableAttributedString alloc] initWithString:[NSString stringWithFormat:@"绝影VIP 至 %@", currentInfoModel.expireDate]];
        attributedStr.color = UIColorHex(#FAEBD8);
        attributedStr.font = kSystem_Font_NoDHPX(WKDHPX(11));
        if (currentInfoModel.days <= 15 && currentInfoModel.days >0) {
            NSString *days = @(currentInfoModel.days).stringValue;
            NSString *partStr = [NSString stringWithFormat:@"  还剩 %@ 天", days];
            NSRange timeRang = [partStr rangeOfString:days];
            NSMutableAttributedString *text = [[NSMutableAttributedString alloc] initWithString:partStr];
            text.color = UIColorHex(#FAEBD8);
            text.font = kSystem_Font_NoDHPX(WKDHPX(11));
            [text setFont:kMedium_Font_NoDHPX(WKDHPX(14)) range:timeRang];
            [attributedStr appendAttributedString:text];
        }
        
        
        self.remarkLabel.attributedText = attributedStr;
    }
}

- (void)setPurchaseViews:(MRKVipPageModel *)pageModel {
    NSArray *vipRightsItem = pageModel.vipExclusiveArray;
    if (vipRightsItem.count == 0) return;
    
    ///清空rightsImageView
    [self.rightsImageView removeAllSubviews];
    
    ///截取vipRightsItem 数组的前 8 个元素
    if (vipRightsItem.count > 8) {
        vipRightsItem = [vipRightsItem subarrayWithRange:NSMakeRange(0, 8)];
    }
    
    NSInteger total = vipRightsItem.count;
    int columns = 4;
    
//    CGFloat paddingTop = WKDHPX(55);
    CGFloat paddingTop = CGRectGetHeight(self.rightsImageView.frame) * 0.226;
    CGFloat paddingSide = WKDHPX(25);
    CGFloat spacing = WKDHPX(10);
    CGFloat tspacing = CGRectGetHeight(self.rightsImageView.frame) * 0.08;
    
    CGFloat totalSpacing = spacing * (columns - 1);
    CGFloat availableWidth = CGRectGetWidth(self.rightsImageView.frame) - paddingSide * 2 - totalSpacing;
    CGFloat itemWidth = floor(availableWidth / columns); // 单个 item 宽
    
    for (int i = 0; i < total; i++) {
        MRKPurchaseRightsItemView *view = [[MRKPurchaseRightsItemView alloc] init];
        [view itemData:vipRightsItem[i] vipType:self.vipType];
        [view addTarget:self action:@selector(clickItems:) forControlEvents:UIControlEventTouchUpInside];
        [self.rightsImageView addSubview:view];
        
        int row = i / columns;
        int col = i % columns;
        
        [view mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.rightsImageView).offset(paddingSide + col * (itemWidth + spacing));
            make.top.equalTo(self.rightsImageView).offset(paddingTop + row * (itemWidth + tspacing));
            make.width.mas_equalTo(itemWidth);
        }];
    }
}

///点击权益
- (void)clickItems:(MRKPurchaseRightsItemView *)item {
    MRKVipExclusiveItemModel *model = item.model;
    
    NSString *Url = @"";
    if (model.isMoreRights){
        Url = [NSString stringWithFormat:@"%@?vipType=%@", MRKVipPageXenjoyInterest, self.vipType];
    } else {
        Url = [NSString stringWithFormat:@"%@?groupId=%@&vipType=%@", MRKVipPageXenjoyInterest, model.groupId, self.vipType];
    }
    [[RouteManager sharedInstance] skipWeb: MRKAppH5LinkCombine(Url) hiddenNav:YES];
    ///统计
    ReportMrkLogParms(2, @"去解决", @"page_profile_vip", @"btn_profile_vip_details", nil, 0, nil);
}

- (UIImageView *)backgroundImageView{
    if (!_backgroundImageView) {
        _backgroundImageView = [[UIImageView alloc] init];
    }
    return _backgroundImageView;
}

- (UIImageView *)rightsImageView{
    if (!_rightsImageView) {
        _rightsImageView = [[UIImageView alloc] init];
        _rightsImageView.userInteractionEnabled = YES;
    }
    return _rightsImageView;
}

- (UIImageView *)bottomImageView{
    if (!_bottomImageView) {
        _bottomImageView = [[UIImageView alloc] init];
    }
    return _bottomImageView;
}

- (UIImageView *)avatarImageView{
    if (!_avatarImageView) {
        _avatarImageView = [[UIImageView alloc] init];
        _avatarImageView.borderColor = [UIColor whiteColor];
        _avatarImageView.borderWidth = 0.6;
        NSString *url = [NSString imageUrlClip:UserInfo.avatar andSize:CGSizeMake(WKDHPX(38), WKDHPX(38))];
        [_avatarImageView sd_setImageWithURL:[NSURL URLWithString:url] placeholderImage:UserInfo.avatarHoldingImage];
        _avatarImageView.cornerRadius = WKDHPX(19);
    }
    return _avatarImageView;
}

- (UILabel *)nameLabel{
    if (!_nameLabel) {
        _nameLabel = [[UILabel alloc] init];
        _nameLabel.font = kMedium_Font_NoDHPX(WKDHPX(15));
        _nameLabel.text = [NSString stringWithFormat:@"尊敬的%@",UserInfo.nickName];
    }
    return _nameLabel;
}

- (UILabel *)remarkLabel{
    if (!_remarkLabel) {
        _remarkLabel = [[UILabel alloc] init];
        _remarkLabel.font = kSystem_Font_NoDHPX(WKDHPX(12));
        _remarkLabel.numberOfLines = 0;
    }
    return _remarkLabel;
}

- (UIButton *)exchangeAdvertBtn {
    if (!_exchangeAdvertBtn){
        _exchangeAdvertBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        _exchangeAdvertBtn.hidden = YES;
    }
    return _exchangeAdvertBtn;
}

@end










@interface MRKPurchaseView()
@property (nonatomic, strong) UIScrollView *cardScrollerView;
@property (nonatomic, strong) UILabel *vipRulerDescrip;

@property (nonatomic, strong) UIButton *subscribeBtn;
@property (nonatomic, strong) UIButton *agreementBtn;
@property (nonatomic, strong) YYLabel *protocolLab;
@end

@implementation MRKPurchaseView

- (instancetype)initWithFrame:(CGRect)frame viewType:(MRKPurchaseViewType)viewType
{
    self = [super initWithFrame:frame];
    if (self) {
        self.selectIndex = 0;
        self.viewType = viewType;
        self.isClaimed = YES;
        
        [self layoutSubviewsUI];
    }
    return self;
}

- (void)layoutSubviewsUI {
    [self addSubview:self.cardScrollerView];
    [self addSubview:self.vipRulerDescrip];
    
    self.cardScrollerView.contentInset = UIEdgeInsetsMake(0, WKDHPX(16), 0, WKDHPX(16));
    [self.cardScrollerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.mas_top).offset(WKDHPX(30));
        make.left.equalTo(self.mas_left);
        make.right.equalTo(self.mas_right);
        make.height.mas_equalTo(WKDHPX(136));
        make.width.mas_equalTo(RealScreenWidth);
    }];
    
    [self.vipRulerDescrip mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.cardScrollerView.mas_bottom).offset(WKDHPX(12));
        make.left.equalTo(self.mas_left).offset(WKDHPX(16));
        make.width.mas_equalTo(RealScreenWidth - WKDHPX(24)*2);
    }];
    
    
    [self addSubview:self.subscribeBtn];
    [self addSubview:self.protocolLab];
    [self addSubview:self.agreementBtn];
    
    [self.subscribeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.cardScrollerView.mas_bottom).offset(WKDHPX(40));
        make.left.equalTo(self.mas_left).offset(WKDHPX(16));
        make.right.equalTo(self.mas_right).offset(-WKDHPX(16));
        make.height.mas_equalTo(WKDHPX(48));
    }];
    [self.protocolLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.subscribeBtn.mas_bottom).offset(WKDHPX(12));
        make.centerX.equalTo(self.mas_centerX).offset(10);
        make.bottom.mas_equalTo(-WKDHPX(30));
    }];
    [self.agreementBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.protocolLab.mas_left).offset(0);
        make.centerY.equalTo(self.protocolLab.mas_centerY);
        make.size.mas_equalTo(CGSizeMake(30, 30));
    }];
    
    if (self.viewType == MRKPurchaseViewTypeAlert){
        self.vipRulerDescrip.textColor = UIColorHex(#848A9B);
        
        self.subscribeBtn.backgroundColor = UIColorHex(#202121);
        [self.subscribeBtn setTitleColor:UIColorHex(#FFD5B6) forState:UIControlStateNormal];
        [self.subscribeBtn setTitle:@"立即开通" forState:UIControlStateNormal];
        
        [self.agreementBtn setImage:[UIImage imageNamed:@"icon_protocol_unagree"] forState:UIControlStateNormal];
    } else {
        self.vipRulerDescrip.textColor = UIColorHex(#D8D8D8);
        
        [self.subscribeBtn setTitleColor:UIColorHex(#282523) forState:UIControlStateNormal];
        [self.subscribeBtn az_setGradientBackgroundWithColors:@[[UIColor colorWithHexString:@"#FFF4E5"],
                                                                [UIColor colorWithHexString:@"#FFD6BA"]]
                                                    locations:nil
                                                   startPoint:CGPointMake(0, 0)
                                                     endPoint:CGPointMake(0, 1)];
        
        [self.agreementBtn setImage:[UIImage imageNamed:@"icon_protocol_unagree_w"] forState:UIControlStateNormal];
    }
    
    NSMutableAttributedString *protrolText = [[NSMutableAttributedString alloc] initWithString:@"我已阅读并同意"];
    protrolText.color = [UIColor colorWithHexString:@"#B3B5B9"];
    protrolText.font = [UIFont systemFontOfSize:WKDHPX(13)];
    @weakify(self);
    NSMutableAttributedString *text = [[NSMutableAttributedString alloc] initWithString:@" 会员服务协议 "];
    text.font = [UIFont systemFontOfSize:WKDHPX(13)];
    text.color = self.protrolHighlightColor;
    text.underlineStyle = NSUnderlineStyleSingle;
    [text setTextHighlightRange:text.rangeOfAll
                          color:self.protrolHighlightColor
                backgroundColor:[UIColor clearColor]
                      tapAction:^(UIView * _Nonnull containerView, NSAttributedString * _Nonnull text, NSRange range, CGRect rect) {
        [self_weak_ subscribeProtocolAction:0];
    }];
    [protrolText appendAttributedString:text];
    
    NSMutableAttributedString *andText= [[NSMutableAttributedString alloc] initWithString:@"和"];
    andText.color = [UIColor colorWithHexString:@"#B3B5B9"];
    andText.font = [UIFont systemFontOfSize:WKDHPX(13)];
    [protrolText appendAttributedString:andText];
    
    NSMutableAttributedString *renewText = [[NSMutableAttributedString alloc] initWithString:@" 自动续费服务协议 "];
    renewText.font = [UIFont systemFontOfSize:WKDHPX(13)];
    renewText.color = self.protrolHighlightColor;
    renewText.underlineStyle = NSUnderlineStyleSingle;
    [renewText setTextHighlightRange:renewText.rangeOfAll
                               color:self.protrolHighlightColor
                     backgroundColor:[UIColor clearColor]
                           tapAction:^(UIView * _Nonnull containerView, NSAttributedString * _Nonnull text, NSRange range, CGRect rect) {
        [self_weak_ subscribeProtocolAction:1];
    }];
    [protrolText appendAttributedString:renewText];
    self.protocolLab.attributedText = protrolText;
    self.protocolLab.textAlignment = NSTextAlignmentLeft;
}

- (void)layoutSubviews{
    [super layoutSubviews];
}

- (void)configPageModel:(MRKVipPageModel *)pageModel vipType:(NSString *)vipType claimedAlert:(BOOL)claimed{
    self.isClaimed = claimed;
    self.vipRulerArray = pageModel.vipRulerArray;
    
    ///
    if (self.viewType != MRKPurchaseViewTypeAlert){
        if (vipType.intValue == 30){
            self.backgroundColor = UIColorHex(#07112D);
        }else{
            self.backgroundColor = UIColorHex(#211E1C);
        }
    }
}

- (void)setTipPurchaseStr:(NSAttributedString *)tipPurchaseStr{
    _tipPurchaseStr = tipPurchaseStr;
    if (self.viewType != MRKPurchaseViewTypeAlert){
        [self.subscribeBtn setAttributedTitle:tipPurchaseStr forState:UIControlStateNormal];
    }
}

- (void)setVipRulerArray:(NSMutableArray<MRKVipCardModel *> *)vipRulerArray{
    _vipRulerArray = vipRulerArray;
    self.selectIndex = 0;
    
    [self addProductForSale];
    [self reloadVipRulerDescrip];
}

- (void)reloadVipRulerDescrip{
    if (self.vipRulerArray.count <= 0){
        self.vipRulerDescrip.text = @"";
        return;
    }
    
    MRKVipCardModel *model = self.vipRulerArray[self.selectIndex];
    self.vipRulerDescrip.text = model.descriptionStr;
}

- (void)addProductForSale{
    [self.cardScrollerView removeAllSubviews];
    
    float cellWidth = WKDHPX(109);
    float contentSize = 0;
    for (int i = 0; i < self.vipRulerArray.count; i++) {
        contentSize = (cellWidth + WKDHPX(8)) *i;
        
        MRKVipCardView *cardView = [[MRKVipCardView alloc] init];
        if (i == 0) {
            cardView.waitAlertDismiss = !self.isClaimed;///
        }
        cardView.tag = 1000 +i;
        cardView.showInAlert = self.viewType == MRKPurchaseViewTypeAlert;
        cardView.model = self.vipRulerArray[i];
        [cardView addTarget:self action:@selector(vipRulerClick:) forControlEvents:UIControlEventTouchUpInside];
        [self.cardScrollerView addSubview:cardView];
        [cardView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.cardScrollerView.mas_left).offset(contentSize);
            make.height.mas_equalTo(WKDHPX(136));
            make.width.mas_equalTo(cellWidth);
            make.top.equalTo(self.cardScrollerView.mas_top);
        }];
        self.cardScrollerView.contentSize = CGSizeMake(contentSize + cellWidth, 0);
    }
    
    ///默认第一个选中
    MRKVipCardView *cardView = (MRKVipCardView *)[self.cardScrollerView viewWithTag:(1000 +self.selectIndex)];
    cardView.hasSelectCurrent = YES;
}

- (void)vipRulerClick:(MRKVipCardView *)sender {
    sender.traceEventId = @"btn_profile_vip_membership";
    
    ///更新旧视图状态
    MRKVipCardView *oldCardView = (MRKVipCardView *)[self.cardScrollerView viewWithTag:(1000 +self.selectIndex)];
    oldCardView.hasSelectCurrent = NO;
    
    ///更新选中索引
    self.selectIndex = (int)sender.tag - 1000;
    if (self.selectIndexPuchaseBlock){
        self.selectIndexPuchaseBlock(self.selectIndex);
    }
    
    ///更新新视图状态
    MRKVipCardView *newCardView = (MRKVipCardView *)[self.cardScrollerView viewWithTag:(1000 +self.selectIndex)];
    newCardView.hasSelectCurrent = YES;
    
    [self reloadVipRulerDescrip];
}

- (UIScrollView *)cardScrollerView{
    if (!_cardScrollerView) {
        _cardScrollerView = [[UIScrollView alloc] init];
        _cardScrollerView.showsHorizontalScrollIndicator = NO;
        _cardScrollerView.pagingEnabled = NO;
    }
    return _cardScrollerView;
}

- (UILabel *)vipRulerDescrip {
    if (!_vipRulerDescrip) {
        UILabel *label = [[UILabel alloc] init];
        label.font = kSystem_Font_NoDHPX(WKDHPX(11));
        label.textColor = UIColorHex(#D8D8D8);
        _vipRulerDescrip = label;
    }
    return _vipRulerDescrip;
}

- (YYLabel *)protocolLab{
    if (!_protocolLab){
        _protocolLab = [[YYLabel alloc] init];
        _protocolLab.textVerticalAlignment = YYTextVerticalAlignmentCenter;
    }
    return _protocolLab;
}

- (UIColor *)protrolHighlightColor{
    if (self.viewType == MRKPurchaseViewTypeAlert){
        return UIColorHex(#17D2E3);
    }
    return UIColor.whiteColor;
}

- (UIButton *)agreementBtn {
    if (!_agreementBtn) {
        _agreementBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_agreementBtn setImage:[UIImage imageNamed:@"icon_protocol_agree"] forState:UIControlStateSelected];
        [_agreementBtn setImage:[UIImage imageNamed:@"icon_protocol_unagree"] forState:UIControlStateNormal];
        [_agreementBtn addTarget:self action:@selector(agreeButton:) forControlEvents:UIControlEventTouchUpInside];
        _agreementBtn.contentMode = UIViewContentModeCenter;
    }
    return _agreementBtn;
}

- (UIButton *)subscribeBtn {
    if (!_subscribeBtn){
        _subscribeBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_subscribeBtn setTitleColor:UIColorHex(#282523) forState:UIControlStateNormal];
        _subscribeBtn.titleLabel.font = [UIFont systemFontOfSize:16 weight:UIFontWeightMedium];
        [_subscribeBtn addTarget:self action:@selector(subscribeAction:) forControlEvents:UIControlEventTouchUpInside];
        _subscribeBtn.layer.cornerRadius = WKDHPX(48)/2;
        _subscribeBtn.layer.masksToBounds = YES;
    }
    return _subscribeBtn;
}

- (void)agreeButton:(UIButton *)sender{
    self.hasAgreeProtocol = !self.hasAgreeProtocol;
    if (self.agressProtocolBlock){
        self.agressProtocolBlock(self.hasAgreeProtocol);
    }
}

- (void)setHasAgreeProtocol:(BOOL)hasAgreeProtocol {
    _hasAgreeProtocol = hasAgreeProtocol;
    self.agreementBtn.selected = hasAgreeProtocol;
}

- (void)subscribeAction:(id)sender{
    MRKVipCardModel *model = self.vipRulerArray[self.selectIndex];
    if (self.subscribeVipPuchaseBlock){
        self.subscribeVipPuchaseBlock(model);
    }
}

- (void)subscribeProtocolAction:(NSInteger)index{
    if (self.subscribeProtocolBlock){
        self.subscribeProtocolBlock(index);
    }
}

/*
 // Only override drawRect: if you perform custom drawing.
 // An empty implementation adversely affects performance during animation.
 - (void)drawRect:(CGRect)rect {
 // Drawing code
 }
 */

@end








@interface MRKIAPBannerView()<SDCycleScrollViewDelegate>
@property (nonatomic, strong) SDCycleScrollView *cycleScrollView;
@property (nonatomic, strong) NSMutableArray *dataArray;
@end

@implementation MRKIAPBannerView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        
        ///banner
        [self addSubview:self.cycleScrollView];
        [self.cycleScrollView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.mas_top);
            make.bottom.equalTo(self.mas_bottom);
            make.left.equalTo(self.mas_left).offset(WKDHPX(16));
            make.width.mas_equalTo(RealScreenWidth - WKDHPX(16)*2);
            make.height.mas_equalTo(0);
        }];
    }
    return self;
}

- (void)configPageModel:(MRKVipPageModel *)pageModel vipType:(NSString *)vipType {
    if (vipType.intValue == 30){
        self.backgroundColor = UIColorHex(#07112D);
    }else{
        self.backgroundColor = UIColorHex(#211E1C);
    }
    
    self.dataArray = pageModel.vipAdverts;
    NSMutableArray *imges = [NSMutableArray array];
    for (AdvertModel *model in pageModel.vipAdverts) {
        [imges addObject:model.image];
    }
    
    self.cycleScrollView.imageURLStringsGroup = imges;
    self.cycleScrollView.autoScroll = imges.count > 1;
    [self.cycleScrollView mas_updateConstraints:^(MASConstraintMaker *make) {
        if (pageModel.vipAdverts.count > 0) {
            make.height.mas_equalTo((RealScreenWidth -WKDHPX(16)*2)/343.0*112);
            make.bottom.equalTo(self.mas_bottom).offset(-WKDHPX(30));
        }else{
            make.height.mas_equalTo(0);
            make.bottom.equalTo(self.mas_bottom);
        }
    }];
}

- (SDCycleScrollView *)cycleScrollView{
    if (!_cycleScrollView) {
        _cycleScrollView = [SDCycleScrollView cycleScrollViewWithFrame:CGRectZero
                                                              delegate:self
                                                      placeholderImage:[UIImage imageNamed:@"pic_1"]];
        _cycleScrollView.backgroundColor = UIColorRGB_BGColor;
        _cycleScrollView.pageControlAliment = SDCycleScrollViewPageContolAlimentCenter;
        _cycleScrollView.pageControlStyle = SDCycleScrollViewPageContolStyleClassic;
        _cycleScrollView.autoScrollTimeInterval = 3;
        _cycleScrollView.currentPageDotColor = [UIColor whiteColor];
        _cycleScrollView.pageDotColor = [UIColor colorWithHexString:@"#ffffff" alpha:0.5];
        _cycleScrollView.layer.masksToBounds = YES;
        _cycleScrollView.layer.cornerRadius = 8;
    }
    return _cycleScrollView;
}

- (void)cycleScrollView:(SDCycleScrollView *)cycleScrollView didSelectItemAtIndex:(NSInteger)index {
    AdvertModel *model = self.dataArray[index];
    [[NSNotificationCenter defaultCenter] postNotificationName:AdvertSkipToPageNotification object:@{@"model" : model ,@"position" : @(BannerPage)}];
}

@end









@interface MRKIAPImageView()
@property (nonatomic, strong) UIImageView *contentImageView;
@property (nonatomic, copy) NSString *vipType;
@end

@implementation MRKIAPImageView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        
        [self addSubview: self.contentImageView];
        [self.contentImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(WKDHPX(16));
            make.left.mas_equalTo(WKDHPX(16));
            make.right.mas_equalTo(-WKDHPX(16));
            make.height.mas_equalTo(300);
            make.bottom.mas_equalTo(-WKDHPX(20));
        }];
    }
    return self;
}

- (void)configPageModel:(MRKVipPageModel *)pageModel vipType:(NSString *)vipType{
    self.vipType = vipType;
    if (vipType.intValue == 30){
        self.backgroundColor = UIColorHex(#07112D);
    }else{
        self.backgroundColor = UIColorHex(#211E1C);
    }
    
    NSInteger type = vipType.integerValue;
    switch (type) {
        case 10: {
            
            NSString *url = @"https://static.merach.com/vip/new-general_equity.png";
            [self reloadContentImage:url];
        } break;
        case 30: {
            
            NSString *url = @"https://static.merach.com/vip/new-genera_x_equity.png";
            [self reloadContentImage:url];
        } break;
        default: break;
    }
}

- (void)reloadContentImage:(NSString *)url {
    __weak typeof(self) weakSelf = self;
    [self.contentImageView sd_setImageWithURL:[NSURL URLWithString:url]
                             placeholderImage:nil
                                      options:SDWebImageRefreshCached
                                    completed:^(UIImage * _Nullable image, NSError * _Nullable error, SDImageCacheType cacheType, NSURL * _Nullable imageURL) {
        __strong typeof(weakSelf) strongSelf = weakSelf;
        if (!strongSelf) return;
        if (image && !error) {
            dispatch_async(dispatch_get_main_queue(), ^{
                // 计算目标高度（基于固定宽度和图片宽高比）
                CGFloat targetWidth = CGRectGetWidth(strongSelf.contentImageView.bounds);
                CGFloat scaleFactor = targetWidth / image.size.width;
                CGFloat targetHeight = image.size.height * scaleFactor;
                [strongSelf.contentImageView mas_updateConstraints:^(MASConstraintMaker *make) {
                    make.height.mas_equalTo(targetHeight);
                }];
            });
        }
    }];
}

///"${AppConstant.URL_JVIP_DETAIL}groupId=${data.groupId}&vipType=${VIP_TYPE}",
- (void)detailAction:(UIButton *)sender {
    
    NSString *Url = [NSString stringWithFormat:@"%@?vipType=%@", MRKVipPageXenjoyInterest, self.vipType];
    [[RouteManager sharedInstance] skipWeb:MRKAppH5LinkCombine(Url) hiddenNav:YES];
    ///统计
    ReportMrkLogParms(2, @"去解决", @"page_profile_vip", @"btn_profile_vip_details", nil, 0, nil);
}

- (UIImageView *)contentImageView{
    if (!_contentImageView) {
        _contentImageView = [[UIImageView alloc] init];
        _contentImageView.contentMode = UIViewContentModeScaleAspectFill;
        _contentImageView.sd_imageTransition = SDWebImageTransition.fadeTransition;
    }
    return _contentImageView;
}

@end





















@interface MRKIAPBottomView()

@property (nonatomic, strong) MRKVipProtrolButton *restorePurchaseBtn;
@property (nonatomic, strong) MRKPurchaseProtocolView *vipProtocolView;
@property (nonatomic, strong) MRKVipPageModel *pageModel;
@end
@implementation MRKIAPBottomView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        //        self.backgroundColor = [UIColor whiteColor];
        [self addAllSubviews];
    }
    return self;
}

- (void)addAllSubviews {
    ///协议
    float btnHeight = RealScreenWidth *0.915 *0.175;
    [self addSubview:self.vipProtocolView];
    [self.vipProtocolView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.mas_top).offset(WKDHPX(16));
        make.left.equalTo(self.mas_left).offset(0);
        make.width.mas_equalTo(RealScreenWidth);
        make.bottom.mas_equalTo(@(-btnHeight - 30));
    }];
}

///会员协议
- (void)autoRenewClickAgain {
    MRKBaseController *base = (MRKBaseController *)[UIViewController currentViewController];
    [[MRKTraceManager sharedInstance] manualUploadTraceType:2 pageTitle:base.navTitle pageId:base.tracePageId eventId:@"btn_profile_vip_services_vip" route:base.tracePageRoute duration:0 extendPara:@{}];
    
    WebViewViewController *vc = [WebViewViewController new];
    vc.htmlURL = MRKAppH5LinkCombine(MRKLinkAutoRenewalProtocol);
    vc.titleString = @"会员服务协议";
    MRKNavigationController *nav = [[MRKNavigationController alloc] initWithRootViewController:vc];
    nav.modalPresentationStyle = UIModalPresentationFullScreen;
    [[UIViewController currentViewController] presentViewController:nav animated:YES completion:nil];
}

///用户协议
- (void)userProtocolClickAgain {
    WebViewViewController *vc = [WebViewViewController new];
    vc.htmlURL = MRKAppH5LinkCombine(MRKLinkUserProtocol);
    vc.titleString = @"用户协议";
    MRKNavigationController *nav = [[MRKNavigationController alloc] initWithRootViewController:vc];
    nav.modalPresentationStyle = UIModalPresentationFullScreen;
    [[UIViewController currentViewController] presentViewController:nav animated:YES completion:nil];
}

///隐私政策
- (void)privacyPolicyClickAgain {
    WebViewViewController *vc = [WebViewViewController new];
    vc.htmlURL = MRKAppH5LinkCombine(MRKLinkPrivacyProtocol);
    vc.titleString = @"隐私政策";
    MRKNavigationController *nav = [[MRKNavigationController alloc] initWithRootViewController:vc];
    nav.modalPresentationStyle = UIModalPresentationFullScreen;
    [[UIViewController currentViewController] presentViewController:nav animated:YES completion:nil];
}

///客服
- (void)customerServiceAgain {
    WebViewViewController *vc = [WebViewViewController new];
    vc.isHiddenNav = YES;
    vc.htmlURL = MRKAppH5LinkCombine(MRKUserHelpAndFeedback);
    UIViewController *controller = [UIViewController currentViewController];
    [controller.navigationController pushViewController:vc animated:YES];
}

///恢复购买
- (void)protrolBtnClick:(id)sender {
    if (self.restorePurchaseBlock) {
        self.restorePurchaseBlock();
    }
}

- (void)expandAction:(UIButton *)sender {
    sender.selected = !sender.selected;
    
    if (self.expandClickBlock) {
        self.expandClickBlock(sender.isSelected);
    }
}

- (void)configPageModel:(MRKVipPageModel *)pageModel vipType:(NSString *)vipType {
    if (vipType.intValue == 30){
        self.backgroundColor = UIColorHex(#07112D);
    }else{
        self.backgroundColor = UIColorHex(#211E1C);
    }
}

- (MRKVipProtrolButton *)restorePurchaseBtn{
    if (!_restorePurchaseBtn) {
        _restorePurchaseBtn = [[MRKVipProtrolButton alloc] init];
        _restorePurchaseBtn.backgroundColor = UIColorHex(#F8F8FA);
        _restorePurchaseBtn.btnTitle = @"苹果支付未到账";
        [_restorePurchaseBtn addTarget:self action:@selector(protrolBtnClick:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _restorePurchaseBtn;
}

///协议三个按钮
- (MRKPurchaseProtocolView *)vipProtocolView{
    if (!_vipProtocolView){
        _vipProtocolView = [[MRKPurchaseProtocolView alloc] init];
        @weakify(self);
        _vipProtocolView.userProtocolClickBlock = ^{
            [self_weak_ userProtocolClickAgain];
        };
        _vipProtocolView.privacyPolicyClickBlock = ^{
            [self_weak_ privacyPolicyClickAgain];
        };
        _vipProtocolView.vipProtocolClickBlock = ^{
            [self_weak_ autoRenewClickAgain];
        };
        _vipProtocolView.customerServiceBlock = ^{
            [self_weak_ customerServiceAgain];
        };
    }
    return _vipProtocolView;
}

@end




