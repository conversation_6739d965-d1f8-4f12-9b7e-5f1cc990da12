//
//  MRKSVipViews.m
//  Student_IOS
//
//  Created by merit on 2023/8/28.
//

#import "MRKSVipViews.h"
#import "Lottie_OC.h"
#import "SDCycleScrollView.h"
#import "UIImage+Helper.h"
#import "MRKVipPageModel.h"
#import "MRKVipCardModel.h"
#import "MRKVipPackageView.h"
#import "MRKTraceManager.h"
#import "UIView+AZGradient.h"

@interface MRKSVipHeaderVue()
@property (nonatomic, strong) UIImageView *backgroundImageView;
@property (nonatomic, strong) UIImageView *bgImageView;
@property (nonatomic, strong) UIImageView *topImageView;
@property (nonatomic, strong) UIImageView *iconImageView;
@property (nonatomic, strong) UILabel *nameLabel;
@property (nonatomic, strong) UILabel *remarkLabel;
@property (nonatomic, strong) LOTAnimationView *animationView;
@end

@implementation MRKSVipHeaderVue

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self addAllSubviews];
    }
    return self;
}

- (void)addAllSubviews {
    [self addSubview: self.backgroundImageView];
    [self.backgroundImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsZero);
        make.height.mas_equalTo(kNavBarHeight + (kScreenWidth - WKDHPX(32)) * 126 / 343 + WKDHPX(24));
    }];
    
    [self addSubview: self.bgImageView];
    [self.bgImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(WKDHPX(24) +kNavBarHeight);
        make.left.mas_equalTo(WKDHPX(16));
        make.right.mas_equalTo(-WKDHPX(16));
        make.height.mas_equalTo((ScreenWidth -WKDHPX(32)) * 126 / 343);
    }];
    
    [self.bgImageView addSubview: self.topImageView];
    [self.topImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.equalTo(self.bgImageView);
        make.size.mas_equalTo(CGSizeMake(WKDHPX(86), WKDHPX(23)));
    }];
    
    [self.bgImageView addSubview: self.iconImageView];
    self.iconImageView.cornerRadius = WKDHPX(19);
    [self.iconImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.bgImageView).offset(WKDHPX(16));
        make.top.equalTo(self.bgImageView).offset(WKDHPX(41));
        make.width.height.mas_equalTo(WKDHPX(38));
    }];
    
    [self.bgImageView addSubview: self.nameLabel];
    [self.nameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.bgImageView).offset(WKDHPX(66));
        make.top.equalTo(self.iconImageView);
        make.right.equalTo(self.mas_right).offset(-WKDHPX(160));
    }];
    
    [self.bgImageView addSubview: self.remarkLabel];
    [self.remarkLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.bgImageView).offset(WKDHPX(66));
        make.top.equalTo(self.nameLabel.mas_bottom).offset(WKDHPX(2));
    }];
    
    self.animationView = [LOTAnimationView animationNamed:@"diamond_animation2"];
    [self addSubview:self.animationView];
    [self.animationView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(-WKDHPX(27)+kNavBarHeight);
        make.right.mas_equalTo(-WKDHPX(14));
        make.size.mas_equalTo(CGSizeMake(WKDHPX(134), WKDHPX(134)));
    }];
}

- (void)defaultSetting:(NSString *)type {
    
    
    NSInteger typeM = type.integerValue;
    
    switch (typeM) {
        case 10: case 20:
            self.backgroundImageView.image = [UIImage imageNamed:@"img_vip_topbackground"];
            self.bgImageView.image = [UIImage imageNamed:@"vip_card_bg"];
            self.nameLabel.textColor = UIColorHex(#144390);
            break;
            
        case 30:
            self.backgroundImageView.image = [UIImage imageNamed:@"img_xenjoy_topbackground"];
            self.bgImageView.image = [UIImage imageNamed:@"xenjoy_card_bg"];
            self.nameLabel.textColor = UIColorHex(#FAEBD8);
            break;
            
        default:
            break;
    }
    
//    if ([type isEqualToString:@"10"]) { // vip
//        self.backgroundImageView.image = [UIImage imageNamed:@"img_vip_topbackground"];
//        self.bgImageView.image = [UIImage imageNamed:@"vip_card_bg"];
//        self.nameLabel.textColor = UIColorHex(#144390);
//    }else if ([type isEqualToString:@"20"]) { // svip
//        self.backgroundImageView.image = [UIImage imageNamed:@"img_svip_topbackground"];
//        self.bgImageView.image = [UIImage imageNamed:@"svip_card_bg"];
//        self.nameLabel.textColor = UIColorHex(#624125);
//    }else if ([type isEqualToString:@"30"]) { // 绝影
//        self.backgroundImageView.image = [UIImage imageNamed:@"img_xenjoy_topbackground"];
//        self.bgImageView.image = [UIImage imageNamed:@"xenjoy_card_bg"];
//        self.nameLabel.textColor = UIColorHex(#FAEBD8);
//    }

    if (self.animationView) {
        [self.animationView removeFromSuperview];
    }
    
    NSString *path = @"";
    if ([type isEqualToString:@"10"] || [type isEqualToString:@"20"]) { // vip
        path = @"diamond_animation2_vip";
    }else if ([type isEqualToString:@"30"]) { // 绝影
        path = @"diamond_animation2";
    }
    self.animationView = [LOTAnimationView animationNamed:path];
    [self addSubview:self.animationView];
    [self.animationView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(-WKDHPX(27)+kNavBarHeight); // 9 - (134 - 78) / 2
        make.right.mas_equalTo(-WKDHPX(14)); // - 37 + (134 - 98) / 2
        make.size.mas_equalTo(CGSizeMake(WKDHPX(134), WKDHPX(134)));
    }];
    [self.animationView play];
    [self.animationView setLoopAnimation:YES];
}

- (void)configPageModel:(MRKVipPageModel *)pageModel vipType:(NSString *)vipType{
    [self defaultSetting:vipType];
    
    MemberInfoDataDTO *currentInfoModel = nil;
    for (MemberInfoDataDTO *model in pageModel.vipInfomodel.items) {
        if (vipType.intValue == model.vipType){
            currentInfoModel = model;
            break;
        }
    }
    
    if (currentInfoModel == nil) return;
    if ([vipType isEqualToString:@"10"]) { // vip
        if (currentInfoModel.isExpire) {
            NSMutableAttributedString *attributedStr = [[NSMutableAttributedString alloc] initWithString:@"你还不是趣练VIP，开通立享各项权益"];
            attributedStr.color = UIColorHex(#144390);
            attributedStr.font = kSystem_Font_NoDHPX(WKDHPX(11));
            self.remarkLabel.attributedText = attributedStr;
            self.topImageView.image = [UIImage imageNamed:@"xenjoy_card_unopen"];
            return;
        }

        NSMutableAttributedString *attributedStr = [[NSMutableAttributedString alloc] initWithString:[NSString stringWithFormat:@"趣练VIP 至 %@", currentInfoModel.expireDate]];
        attributedStr.color = UIColorHex(#144390);
        attributedStr.font = kSystem_Font_NoDHPX(WKDHPX(11));
        if (currentInfoModel.days <= 15 && currentInfoModel.days >0) {
            NSString *days = @(currentInfoModel.days).stringValue;
            NSString *partStr = [NSString stringWithFormat:@"  还剩 %@ 天", days];
            NSRange timeRang = [partStr rangeOfString:days];
            NSMutableAttributedString *text = [[NSMutableAttributedString alloc] initWithString:partStr];
            text.color = UIColorHex(#144390);
            text.font = kSystem_Font_NoDHPX(WKDHPX(11));
            [text setFont:kMedium_Font_NoDHPX(WKDHPX(14)) range:timeRang];
            [attributedStr appendAttributedString:text];
        }
        ///
        if ( pageModel.vipInfomodel.vipType == 20 && !pageModel.vipInfomodel.isExpire ) {
            [attributedStr appendAttributedString:[[NSMutableAttributedString alloc] initWithString:@"\n"]];
            
            NSString *partStr1 = @"正在使用SVIP权益中，VIP权益将在SVIP到期后继续生效";
            NSMutableAttributedString *text1 = [[NSMutableAttributedString alloc] initWithString:partStr1];
            text1.color = UIColorHex(#144390);
            text1.font = kSystem_Font_NoDHPX(WKDHPX(11));
            [attributedStr appendAttributedString:text1];
        }
        
        self.topImageView.image = [UIImage imageNamed:@"img_vip_state_opened"];
        self.remarkLabel.attributedText = attributedStr;
        
    } else if ([vipType isEqualToString:@"20"]) { // svip
        if (currentInfoModel.isExpire) {
            NSMutableAttributedString *attributedStr = [[NSMutableAttributedString alloc] initWithString:@"你还不是超燃SVIP，开通立享各项权益"];
            attributedStr.color = UIColorHex(#624125);
            attributedStr.font = kSystem_Font_NoDHPX(WKDHPX(11));
            self.remarkLabel.attributedText = attributedStr;
            self.topImageView.image = [UIImage imageNamed:@"xenjoy_card_unopen"];
            return;
        }
        
        NSMutableAttributedString *attributedStr = [[NSMutableAttributedString alloc] initWithString:[NSString stringWithFormat:@"超燃SVIP 至 %@", currentInfoModel.expireDate]];
        attributedStr.color = UIColorHex(#624125);
        attributedStr.font = kSystem_Font_NoDHPX(WKDHPX(11));
        if (currentInfoModel.days <= 15 && currentInfoModel.days >0) {
            NSString *days = @(currentInfoModel.days).stringValue;
            NSString *partStr = [NSString stringWithFormat:@"  还剩 %@ 天", days];
            NSRange timeRang = [partStr rangeOfString:days];
            NSMutableAttributedString *text = [[NSMutableAttributedString alloc] initWithString:partStr];
            text.color = UIColorHex(#624125);
            text.font = kSystem_Font_NoDHPX(WKDHPX(11));
            [text setFont:kMedium_Font_NoDHPX(WKDHPX(14)) range:timeRang];
            [attributedStr appendAttributedString:text];
        }
 
        self.topImageView.image = [UIImage imageNamed:@"img_svip_state_opened"];
        self.remarkLabel.attributedText = attributedStr;

    } else if ([vipType isEqualToString:@"30"]) { // 绝影
        if (currentInfoModel.isExpire) {
            NSMutableAttributedString *attributedStr = [[NSMutableAttributedString alloc] initWithString:@"你还不是会员，开通立享各项权益"];
            attributedStr.color = UIColorHex(#FAEBD8);
            attributedStr.font = kSystem_Font_NoDHPX(WKDHPX(11));
            self.remarkLabel.attributedText = attributedStr;
            self.topImageView.image = [UIImage imageNamed:@"xenjoy_card_unopen"];
            return;
        }

        NSMutableAttributedString *attributedStr = [[NSMutableAttributedString alloc] initWithString:[NSString stringWithFormat:@"绝影VIP 至 %@", currentInfoModel.expireDate]];
        attributedStr.color = UIColorHex(#FAEBD8);
        attributedStr.font = kSystem_Font_NoDHPX(WKDHPX(11));
        if (currentInfoModel.days <= 15 && currentInfoModel.days >0) {
            NSString *days = @(currentInfoModel.days).stringValue;
            NSString *partStr = [NSString stringWithFormat:@"  还剩 %@ 天", days];
            NSRange timeRang = [partStr rangeOfString:days];
            NSMutableAttributedString *text = [[NSMutableAttributedString alloc] initWithString:partStr];
            text.color = UIColorHex(#FAEBD8);
            text.font = kSystem_Font_NoDHPX(WKDHPX(11));
            [text setFont:kMedium_Font_NoDHPX(WKDHPX(14)) range:timeRang];
            [attributedStr appendAttributedString:text];
        }
 
        self.topImageView.image = [UIImage imageNamed:@"xenjoy_card_open"];
        self.remarkLabel.attributedText = attributedStr;
    }
}






- (UIImageView *)backgroundImageView{
    if (!_backgroundImageView) {
        _backgroundImageView = [[UIImageView alloc] init];
    }
    return _backgroundImageView;
}

- (UIImageView *)bgImageView{
    if (!_bgImageView) {
        _bgImageView = [[UIImageView alloc] init];
    }
    return _bgImageView;
}

- (UIImageView *)topImageView{
    if (!_topImageView) {
        _topImageView = [[UIImageView alloc] init];
    }
    return _topImageView;
}

- (UIImageView *)iconImageView{
    if (!_iconImageView) {
        _iconImageView = [[UIImageView alloc] init];
        _iconImageView.borderColor = [UIColor whiteColor];
        _iconImageView.borderWidth = 0.6;
        NSString *url = [NSString imageUrlClip:UserInfo.avatar andSize:CGSizeMake(WKDHPX(38), WKDHPX(38))];
        [_iconImageView sd_setImageWithURL:[NSURL URLWithString:url] placeholderImage:UserInfo.avatarHoldingImage];
    }
    return _iconImageView;
}

- (UILabel *)remarkLabel{
    if (!_remarkLabel) {
        _remarkLabel = [[UILabel alloc] init];
        _remarkLabel.font = kSystem_Font_NoDHPX(WKDHPX(11));
        _remarkLabel.numberOfLines = 0;
    }
    return _remarkLabel;
}

- (UILabel *)nameLabel{
    if (!_nameLabel) {
        _nameLabel = [[UILabel alloc] init];
        _nameLabel.font = kMedium_Font_NoDHPX(WKDHPX(15));
        _nameLabel.text = [NSString stringWithFormat:@"尊敬的%@",UserInfo.nickName];
    }
    return _nameLabel;
}
@end









@interface MRKIAPCenterView()
@property (nonatomic, strong) UIImageView *bottomImageView;
@property (nonatomic, strong) UIView *backContentView;
@property (nonatomic, strong) UIImageView *lineView;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UIButton *detailButton;
@property (nonatomic, strong) UIImageView *contentImageView;

@property (nonatomic, strong) MRKVipPageModel *pageModel;
@property (nonatomic, strong) NSString *vipType;
@end

@implementation MRKIAPCenterView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = [UIColor clearColor];
        [self addAllSubviews];
    }
    return self;
}

- (void)addAllSubviews{
    [self addSubview:self.bottomImageView];
    [self.bottomImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(0);
        make.left.right.mas_equalTo(0);
        make.height.mas_equalTo(WKDHPX(26));
    }];
    
    self.backContentView = [[UIView alloc] init];
    [self addSubview: self.backContentView];
    [self.backContentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.bottomImageView.mas_bottom).offset(0);
        make.left.mas_equalTo(0);
        make.width.mas_equalTo(RealScreenWidth);
        make.bottom.mas_equalTo(0);
    }];
    
    [self addSubview: self.lineView];
    [self.lineView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.bottomImageView.mas_bottom).offset(WKDHPX(5));
        make.left.mas_equalTo(WKDHPX(16));
        make.width.mas_equalTo(WKDHPX(3));
        make.height.mas_equalTo(WKDHPX(16));
    }];
    [self addSubview: self.titleLabel];
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_equalTo(self.lineView);
        make.left.mas_equalTo(WKDHPX(24));
        make.height.mas_equalTo(WKDHPX(24));
    }];
    [self addSubview: self.detailButton];
    [self.detailButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_equalTo(self.lineView);
        make.right.mas_equalTo(-WKDHPX(16));
        make.size.mas_equalTo(CGSizeMake(WKDHPX(60), WKDHPX(20)));
    }];
    
    [self addSubview: self.contentImageView];
    [self.contentImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(WKDHPX(72));
        make.left.mas_equalTo(WKDHPX(16));
        make.right.mas_equalTo(-WKDHPX(16));
        make.height.mas_equalTo(300);
        make.bottom.mas_equalTo(-WKDHPX(46));
    }];
}

- (void)configPageModel:(MRKVipPageModel *)pageModel vipType:(NSString *)vipType{
    self.vipType = vipType;
    self.pageModel = pageModel;
    
    [self defaultSetting:vipType];
    self.titleLabel.text = @"尊享会员权益";
}

- (void)defaultSetting:(NSString *)type {
    
    NSInteger vipType = type.integerValue;
    
    switch (vipType) {
        case 10: {
            self.bottomImageView.image = [UIImage imageNamed:@"vip_card_bottom_bg"];
            self.backContentView.backgroundColor = [UIColor colorWithHexString:@"#2866CB"];
            self.lineView.image = [UIImage gradientColorImageFromColors:@[[UIColor colorWithHexString:@"#FFFFFF"],
                                                                          [UIColor colorWithHexString:@"#F3F8FF"]]
                                                           gradientType:GradientTypeTopToBottom
                                                                imgSize:CGSizeMake(WKDHPX(3), WKDHPX(16))];
            self.titleLabel.textColor = [UIColor colorWithHexString:@"#F3F8FF"];
            [self.detailButton setTintColor:[UIColor colorWithHexString:@"#F3F8FF"]];
            [self.detailButton setTitleColor:[UIColor colorWithHexString:@"#F3F8FF"] forState:UIControlStateNormal];
            
            NSString *url = @"https://static.merach.com/vip/general_equity.png";
            [self reloadContentImage:url];
        }
            
            break;
        case 20: {
            self.bottomImageView.image = [UIImage imageNamed:@"svip_card_bottom_bg"];
            self.backContentView.backgroundColor = [UIColor colorWithHexString:@"#9B754F"];
            self.lineView.image = [UIImage gradientColorImageFromColors:@[[UIColor colorWithHexString:@"#FFEFE1"],
                                                                          [UIColor colorWithHexString:@"#FFEFE1"]]
                                                           gradientType:GradientTypeTopToBottom
                                                                imgSize:CGSizeMake(WKDHPX(3), WKDHPX(16))];
            self.titleLabel.textColor = [UIColor colorWithHexString:@"#FFEFE1"];
            [self.detailButton setTintColor:[UIColor colorWithHexString:@"#FFEFE1"]];
            [self.detailButton setTitleColor:[UIColor colorWithHexString:@"#FFEFE1"] forState:UIControlStateNormal];
            
            NSString *url = @"https://static.merach.com/vip/general_equity.png";
            [self reloadContentImage:url];
        }
            
            break;
        case 30: {
            self.bottomImageView.image = [UIImage imageNamed:@"xenjoy_card_bottom_bg"];
            self.backContentView.backgroundColor = [UIColor colorWithHexString:@"#07112D"];
            self.lineView.image = [UIImage gradientColorImageFromColors:@[[UIColor colorWithHexString:@"#FEE8D6"],
                                                                          [UIColor colorWithHexString:@"#EFD7BF"]]
                                                           gradientType:GradientTypeTopToBottom
                                                                imgSize:CGSizeMake(WKDHPX(3), WKDHPX(16))];
            UIImage *backgroundImage = [UIImage gradientColorImageFromColors:@[[UIColor colorWithHexString:@"#FFD7B3"],
                                                                               [UIColor colorWithHexString:@"#FDF0E7"]]
                                                                gradientType:GradientTypeTopToBottom
                                                                     imgSize:CGSizeMake(WKDHPX(129), WKDHPX(24))];
            self.titleLabel.textColor = [UIColor colorWithPatternImage:backgroundImage];
            [self.detailButton setTintColor:[UIColor colorWithHexString:@"#FAEBD8"]];
            [self.detailButton setTitleColor:[UIColor colorWithHexString:@"#FAEBD8"] forState:UIControlStateNormal];
            
            NSString *url = @"https://static.merach.com/vip/genera_x_equity.png";
            [self reloadContentImage:url];
        }
            
            break;
            
        default:
            break;
    }
}

- (void)reloadContentImage:(NSString *)url {
    __weak typeof(self) weakSelf = self;
    [self.contentImageView sd_setImageWithURL:[NSURL URLWithString:url]
                             placeholderImage:nil
                                      options:SDWebImageRefreshCached
                                    completed:^(UIImage * _Nullable image, NSError * _Nullable error, SDImageCacheType cacheType, NSURL * _Nullable imageURL) {
        __strong typeof(weakSelf) strongSelf = weakSelf;
        if (!strongSelf) return;
        if (image && !error) {
            dispatch_async(dispatch_get_main_queue(), ^{
                // 计算目标高度（基于固定宽度和图片宽高比）
                CGFloat targetWidth = CGRectGetWidth(strongSelf.contentImageView.bounds);
                CGFloat scaleFactor = targetWidth / image.size.width;
                CGFloat targetHeight = image.size.height * scaleFactor;
                [strongSelf.contentImageView mas_updateConstraints:^(MASConstraintMaker *make) {
                    make.height.mas_equalTo(targetHeight);
                }];
            });
        }
    }];
}


- (void)detailAction:(UIButton *)sender {
    NSString *Url = [NSString stringWithFormat:@"%@?vipType=%@", MRKVipPageXenjoyInterest, self.vipType];
    [[RouteManager sharedInstance] skipWeb:MRKAppH5LinkCombine(Url) hiddenNav:YES];
    ///统计
    ReportMrkLogParms(2, @"去解决", @"page_profile_vip", @"btn_profile_vip_details", nil, 0, nil);
}

- (UIImageView *)bottomImageView{
    if (!_bottomImageView) {
        _bottomImageView = [[UIImageView alloc] init];
        _bottomImageView.image = [UIImage imageNamed:@"vip_card_bottom_bg"];
    }
    return _bottomImageView;
}

- (UIImageView *)lineView{
    if (!_lineView) {
        _lineView = [[UIImageView alloc] init];
        _lineView.cornerRadius = WKDHPX(1.5);
    }
    return _lineView;
}

- (UILabel *)titleLabel{
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.font = kMedium_Font_NoDHPX(WKDHPX(16));
    }
    return _titleLabel;
}

- (UIButton *)detailButton{
    if (!_detailButton) {
        _detailButton.alpha = 0.8;
        _detailButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_detailButton setImage:[UIImage imageNamed:@"vip_button_duihuan"] forState:UIControlStateNormal];
        [_detailButton addTarget:self action:@selector(detailAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _detailButton;
}

- (UIImageView *)contentImageView{
    if (!_contentImageView) {
        _contentImageView = [[UIImageView alloc] init];
        _contentImageView.contentMode = UIViewContentModeScaleAspectFill;
        _contentImageView.sd_imageTransition = SDWebImageTransition.fadeTransition;
    }
    return _contentImageView;
}
@end























@interface MRKIAPBottomView()<SDCycleScrollViewDelegate>
@property (nonatomic, strong) UIImageView *topImageView;
@property (nonatomic, strong) UIButton *expandButton;

@property (nonatomic, strong) MRKVipBannerView *vipBannerView;
@property (nonatomic, strong) MRKVipProtrolButton *restorePurchaseBtn;
@property (nonatomic, strong) MRKVipCardProtocolView *vipProtocolView;
@property (nonatomic, strong) MRKVipPageModel *pageModel;
@end
@implementation MRKIAPBottomView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
//        self.backgroundColor = [UIColor whiteColor];
        [self addAllSubviews];
    }
    return self;
}

- (void)addAllSubviews {
    [self addSubview:self.topImageView];
    [self.topImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(0);
        make.left.right.mas_equalTo(0);
        make.height.mas_equalTo(WKDHPX(18));
    }];
    
    [self addSubview: self.expandButton];
    [self.expandButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.topImageView.mas_top).offset(0);
        make.centerX.equalTo(self.topImageView.mas_centerX);
        make.width.height.mas_equalTo(WKDHPX(40));
    }];
    
    UIView *view = [[UIView alloc] init];
    view.backgroundColor = [UIColor whiteColor];
    [self addSubview:view];
    [view mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.mas_top).offset(WKDHPX(18));
        make.left.equalTo(self.mas_left).offset(0);
        make.width.mas_equalTo(kScreenWidth);
        make.bottom.mas_equalTo(self.mas_bottom);
    }];
    
    ///购买项
    [self addSubview:self.vipPackageView];
    [self.vipPackageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.topImageView.mas_bottom).offset(0);
        make.left.mas_equalTo(WKDHPX(16));
        make.right.mas_equalTo(-WKDHPX(16));
        make.width.mas_equalTo(kScreenWidth);
    }];
    
    ///banner
    [self addSubview:self.vipBannerView];
    [self.vipBannerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.vipPackageView.mas_bottom).offset(0);
        make.left.equalTo(self.mas_left).offset(0);
        make.width.mas_equalTo(kScreenWidth);
        make.height.mas_equalTo((kScreenWidth - WKDHPX(16)*2)/343.0*112 + WKDHPX(16));
    }];
    
//    ///banner
//    [self addSubview:self.restorePurchaseBtn];
//    [self.restorePurchaseBtn mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.top.mas_equalTo(self.vipBannerView.mas_bottom).offset(WKDHPX(16));
//        make.left.equalTo(self.mas_left).offset(WKDHPX(16));
//        make.right.equalTo(self.mas_right).offset(-WKDHPX(16));
//        make.width.mas_equalTo(kScreenWidth);
//        make.height.mas_equalTo(WKDHPX(52));
//    }];
    
    ///协议
    float btnHeight = kScreenWidth *0.915 *0.175;
    [self addSubview:self.vipProtocolView];
    [self.vipProtocolView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.vipBannerView.mas_bottom).offset(WKDHPX(16));
        make.left.equalTo(self.mas_left).offset(0);
        make.width.mas_equalTo(kScreenWidth);
        make.bottom.mas_equalTo(@(-btnHeight - 30));
    }];
}

- (void)adapterVipRights:(BOOL)adapt{
    self.backgroundColor = adapt ? [UIColor whiteColor]:[UIColor clearColor];
    self.expandButton.hidden = adapt;
}

- (void)configPageModel:(MRKVipPageModel *)pageModel vipType:(NSString *)vipType {
    self.pageModel = pageModel;
    // 会员购买
    self.vipPackageView.vipRulerArray = self.pageModel.vipRulerArray;
    self.vipPackageView.exchangeAdvert = self.pageModel.exchangeAdvert;
  
    self.vipBannerView.dataArray = self.pageModel.vipAdverts;
    [self.vipBannerView mas_updateConstraints:^(MASConstraintMaker *make) {
        if (self.pageModel.vipAdverts.count > 0) {
            make.height.mas_equalTo((kScreenWidth -WKDHPX(16)*2)/343.0*112 + WKDHPX(16));
        }else{
            make.height.mas_equalTo(0);
        }
    }];
}

///会员协议
- (void)autoRenewClickAgain {
    MRKBaseController *base = (MRKBaseController *)[UIViewController currentViewController];
    [[MRKTraceManager sharedInstance] manualUploadTraceType:2 pageTitle:base.navTitle pageId:base.tracePageId eventId:@"btn_profile_vip_services_vip" route:base.tracePageRoute duration:0 extendPara:@{}];
    
    WebViewViewController *vc = [WebViewViewController new];
    vc.htmlURL = MRKAppH5LinkCombine(MRKLinkAutoRenewalProtocol);
    vc.titleString = @"会员服务协议";
    MRKNavigationController *nav = [[MRKNavigationController alloc] initWithRootViewController:vc];
    nav.modalPresentationStyle = UIModalPresentationFullScreen;
    [[UIViewController currentViewController] presentViewController:nav animated:YES completion:nil];
}

///用户协议
- (void)userProtocolClickAgain {
    WebViewViewController *vc = [WebViewViewController new];
    vc.htmlURL = MRKAppH5LinkCombine(MRKLinkUserProtocol);
    vc.titleString = @"用户协议";
    MRKNavigationController *nav = [[MRKNavigationController alloc] initWithRootViewController:vc];
    nav.modalPresentationStyle = UIModalPresentationFullScreen;
    [[UIViewController currentViewController] presentViewController:nav animated:YES completion:nil];
}

///隐私政策
- (void)privacyPolicyClickAgain {
    WebViewViewController *vc = [WebViewViewController new];
    vc.htmlURL = MRKAppH5LinkCombine(MRKLinkPrivacyProtocol);
    vc.titleString = @"隐私政策";
    MRKNavigationController *nav = [[MRKNavigationController alloc] initWithRootViewController:vc];
    nav.modalPresentationStyle = UIModalPresentationFullScreen;
    [[UIViewController currentViewController] presentViewController:nav animated:YES completion:nil];
}

///客服
- (void)customerServiceAgain {
    
    WebViewViewController *vc = [WebViewViewController new];
    vc.isHiddenNav = YES;
    vc.htmlURL = MRKAppH5LinkCombine(MRKUserHelpAndFeedback);
    UIViewController *controller = [UIViewController currentViewController];
    [controller.navigationController pushViewController:vc animated:YES];
}

///恢复购买
- (void)protrolBtnClick:(id)sender {
    if (self.restorePurchaseBlock) {
        self.restorePurchaseBlock();
    }
}

- (void)expandAction:(UIButton *)sender {
    sender.selected = !sender.selected;
    
    if (self.expandClickBlock) {
        self.expandClickBlock(sender.isSelected);
    }
}

- (UIButton *)expandButton{
    if (!_expandButton) {
        _expandButton = [[UIButton alloc] init];
        [_expandButton setImage:[UIImage imageNamed:@"vip_qy_down"] forState:UIControlStateNormal];
        [_expandButton setImage:[UIImage imageNamed:@"vip_qy_up"] forState:UIControlStateSelected];
        [_expandButton addTarget:self action:@selector(expandAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _expandButton;
}

- (UIImageView *)topImageView{
    if (!_topImageView) {
        _topImageView = [[UIImageView alloc] init];
        _topImageView.image = [UIImage imageNamed:@"qy_img_bgwhite"];
    }
    return _topImageView;
}

///Vip会员套餐
- (MRKVipPackageView *)vipPackageView{
    if (!_vipPackageView){
        _vipPackageView = [[MRKVipPackageView alloc] init];
        @weakify(self);
        _vipPackageView.vipProtocolClickBlock = ^{
            [self_weak_ autoRenewClickAgain];
        };
        _vipPackageView.updateSvipTimeBlock = ^{
            @strongify(self);
            if (self.updateSvipTimeBlock) {
                self.updateSvipTimeBlock();
            }
        };
    }
    return _vipPackageView;
}

///Vip banner
- (MRKVipBannerView *)vipBannerView{
    if (!_vipBannerView){
        _vipBannerView = [[MRKVipBannerView alloc] init];
        _vipBannerView.bannerSelectBlock = ^(AdvertModel * _Nonnull model) {
            ///banner点击
            [[NSNotificationCenter defaultCenter] postNotificationName:AdvertSkipToPageNotification object:@{@"model" : model ,@"position" : @(BannerPage)}];
        };
    }
    return _vipBannerView;
}

- (MRKVipProtrolButton *)restorePurchaseBtn{
    if (!_restorePurchaseBtn) {
        _restorePurchaseBtn = [[MRKVipProtrolButton alloc] init];
        _restorePurchaseBtn.backgroundColor = UIColorHex(#F8F8FA);
        _restorePurchaseBtn.btnTitle = @"苹果支付未到账";
        [_restorePurchaseBtn addTarget:self action:@selector(protrolBtnClick:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _restorePurchaseBtn;
}

///协议三个按钮
- (MRKVipCardProtocolView *)vipProtocolView{
    if (!_vipProtocolView){
        _vipProtocolView = [[MRKVipCardProtocolView alloc] init];
        @weakify(self);
        _vipProtocolView.userProtocolClickBlock = ^{
            [self_weak_ userProtocolClickAgain];
        };
        _vipProtocolView.privacyPolicyClickBlock = ^{
            [self_weak_ privacyPolicyClickAgain];
        };
        _vipProtocolView.vipProtocolClickBlock = ^{
            [self_weak_ autoRenewClickAgain];
        };
        _vipProtocolView.customerServiceBlock = ^{
            [self_weak_ customerServiceAgain];
        };
    }
    return _vipProtocolView;
}

@end




