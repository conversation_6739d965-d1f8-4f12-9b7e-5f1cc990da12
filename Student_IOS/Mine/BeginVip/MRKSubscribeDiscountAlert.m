//
//  MRKSubscribeDiscountAlert.m
//  
//
//  Created by Junq on 2023/11/2.
//


#import "MRKSubscribeDiscountAlert.h"
#import "UIView+AZGradient.h"

@interface MRKSubscribeDiscountAlert ()
@property (nonatomic, strong) MRKLottieView *lottieView;
@property (nonatomic, strong) MRKSubscribeDiscountView *messageView;
@end

@implementation MRKSubscribeDiscountAlert

- (void)layoutContainerView{
    self.isAutoHidden = YES;
    UIView *baseSuperView = self.containerView.superview;
    [self.containerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_equalTo(baseSuperView.mas_centerY);
        make.centerX.mas_equalTo(baseSuperView.mas_centerX);
        make.width.mas_equalTo(DHPX(295));
        make.height.mas_equalTo(DHPX(347));
    }];
}

- (void)setupContainerViewAttributes{
    self.containerView.backgroundColor = UIColor.clearColor;
}

- (void)setupContainerSubViews{
    
    self.messageView.hidden = YES;
    [self.containerView addSubview:self.messageView];
    [self.messageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.mas_equalTo(self.containerView);
        make.centerY.mas_equalTo(self.containerView);
        make.size.mas_equalTo(CGSizeMake(DHPX(295), DHPX(347)));
    }];
    
    
    
    self.lottieView.hidden = NO;
    [self.containerView addSubview:self.lottieView];
    [self.lottieView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.mas_equalTo(self.containerView);
        make.centerY.mas_equalTo(self.containerView);
        make.size.mas_equalTo(CGSizeMake(DHPX(295), DHPX(257)));
    }];
    
    if (!self.lottieView.isAnimationPlaying) {
        NSDictionary *replacements = @{
            @"4 折": @"5\r折"
        };
        [self.lottieView replaceTexts:replacements];
        
        @weakify(self);
        [self.lottieView playWithCompletion:^{
            @strongify(self);
            self.lottieView.hidden = YES;
            self.messageView.hidden = NO;
        }];
    }
}

- (void)layoutContainerViewSubViews{
    [self layoutIfNeeded];
}

- (void)setVipUpdateRulerArray:(NSMutableArray *)vipUpdateRulerArray {
    _vipUpdateRulerArray = vipUpdateRulerArray;
    
    __block NSInteger index = 0;
    [vipUpdateRulerArray enumerateObjectsUsingBlock:^(id  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        MRKVipCardModel *model = (MRKVipCardModel *)obj;
        if ([self.defaultModel.appStoreCode isEqualToString:model.appStoreCode]) {
            index = idx;
            *stop = YES;
        }
    }];
  
 
}



- (MRKLottieView *)lottieView {
    if (!_lottieView) {
        _lottieView = [[MRKLottieView alloc] init];
        _lottieView.animationName = @"subscribe_animation";
        _lottieView.loopAnimationCount = 1;
    }
    return _lottieView;
}

- (MRKSubscribeDiscountView *)messageView {
    if (!_messageView) {
        _messageView = [[MRKSubscribeDiscountView alloc] init];
    }
    return _messageView;
}

- (void)ensureBtnClick:(UIButton *)sender{
    [self dismissAnimated:YES];
}

/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/

@end





@interface MRKSubscribeDiscountView ()
@property (nonatomic, strong) UILabel *titleLab;
@property (nonatomic, strong) UILabel *descripLab;
@property (nonatomic, strong) UIImageView *discountCardView;
@property (nonatomic, strong) UILabel *subscribeTipLab;
@property (nonatomic, strong) UILabel *priceLab;
@property (nonatomic, strong) UILabel *priceUnitLab;
@property (nonatomic, strong) UILabel *timerLab;
@property (nonatomic, strong) UIButton *subscribeBtn;
@property (nonatomic, strong) UIButton *cancelBtn;
@end

@implementation MRKSubscribeDiscountView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        
        [self addSubview:self.cancelBtn];
        [self.cancelBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.mas_equalTo(self.mas_centerX);
            make.bottom.mas_equalTo(self.mas_bottom);
            make.width.height.equalTo(@(WKDHPX(36)));
        }];
        
        
        UIView *backView = [[UIView alloc] init];
        backView.backgroundColor = UIColor.clearColor;
        [self addSubview:backView];
        [backView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.mas_equalTo(self.mas_centerX);
            make.top.mas_equalTo(self.mas_top);
            make.height.mas_equalTo(WKDHPX(64));
        }];
        
        [backView addSubview:self.titleLab];
        [self.titleLab mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.mas_equalTo(backView.mas_centerY);
            make.left.mas_equalTo(backView.mas_left);
            make.height.mas_equalTo(WKDHPX(64));
        }];
        
        [backView addSubview:self.descripLab];
        [self.descripLab mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.mas_equalTo(backView.mas_centerY);
            make.left.mas_equalTo(self.titleLab.mas_right).offset(DHPX(5));
            make.right.mas_equalTo(backView.mas_right);
            make.height.mas_equalTo(WKDHPX(64));
        }];
        
        {
            ///
            [self addSubview:self.discountCardView];
            [self.discountCardView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.top.mas_equalTo(self.mas_top).offset(WKDHPX(64));
                make.left.mas_equalTo(self.mas_left);
                make.right.mas_equalTo(self.mas_right);
                make.height.mas_equalTo(DHPX(206));
            }];
            
            [self.discountCardView addSubview:self.subscribeTipLab];
            [self.subscribeTipLab mas_makeConstraints:^(MASConstraintMaker *make) {
                make.top.mas_equalTo(self.discountCardView.mas_top);
                make.right.mas_equalTo(self.discountCardView.mas_right).offset(-DHPX(16));
                make.width.mas_equalTo(DHPX(76));
                make.height.mas_equalTo(DHPX(25));
            }];
            
            [self.discountCardView addSubview:self.priceLab];
            [self.priceLab mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerX.equalTo(self.discountCardView.mas_centerX);
                make.top.mas_equalTo(self.discountCardView.mas_top);
                make.height.mas_equalTo(DHPX(90));
            }];
            
            [self.discountCardView addSubview:self.priceUnitLab];
            [self.priceUnitLab mas_makeConstraints:^(MASConstraintMaker *make) {
                make.right.equalTo(self.priceLab.mas_left).offset(-DHPX(9));
                make.bottom.mas_equalTo(self.priceLab.mas_bottom).offset(DHPX(8));
                make.height.mas_equalTo(DHPX(90));
            }];
            
            [self.discountCardView addSubview:self.timerLab];
            [self.timerLab mas_makeConstraints:^(MASConstraintMaker *make) {
                make.top.mas_equalTo(self.priceLab.mas_bottom).offset(0);
                make.centerX.equalTo(self.discountCardView.mas_centerX);
                make.width.mas_equalTo(DHPX(117));
                make.height.mas_equalTo(DHPX(20));
            }];
            
            [self.discountCardView addSubview: self.subscribeBtn];
            [self.subscribeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
                make.bottom.mas_equalTo(self.discountCardView.mas_bottom).offset(-DHPX(8));
                make.centerX.equalTo(self.discountCardView.mas_centerX);
                make.width.mas_equalTo(DHPX(125));
                make.height.mas_equalTo(DHPX(40));
            }];
        }
    }
    return self;
}



- (void)setModel:(MRKVipCardModel *)model{
    self.titleLab.text = model.name;
    self.priceLab.attributedText = ({
        NSString *price = model.actualAmount ?:@"";
        NSMutableAttributedString *str = [[NSMutableAttributedString alloc] initWithString:@"¥"];
        str.font = BebasFont_Bold_NoDHPX(WKDHPX(16));
        NSMutableAttributedString *priceStr = [[NSMutableAttributedString alloc] initWithString:price];
        priceStr.font = BebasFont_Bold_NoDHPX(WKDHPX(28));
        [str appendAttributedString:priceStr];
        str;
    });
    self.averageDailyLab.text = [NSString stringWithFormat:@"每日仅需¥%@", model.dailyStartPrice];
}


#pragma mark - 懒加载

- (UILabel *)titleLab {
    if (!_titleLab) {
        UILabel *label = [[UILabel alloc] init];
        label.textColor = UIColorHex(#FDE7C7);
        label.font = kMedium_PingFangSC(WKDHPX(40));
        label.text = @"4折";
        _titleLab = label;
    }
    return _titleLab;
}

- (UILabel *)descripLab {
    if (!_descripLab) {
        UILabel *label = [[UILabel alloc] init];
        label.textColor = UIColorHex(#FDE7C7);
        label.font = kMedium_PingFangSC(WKDHPX(20));
        label.text = @"恭喜抽中最低折扣！";
        _descripLab = label;
    }
    return _descripLab;
}

- (UIImageView *)discountCardView {
    if (!_discountCardView) {
        _discountCardView = [[UIImageView alloc] init];
        _discountCardView.contentMode = UIViewContentModeScaleAspectFit;
        _discountCardView.image = [UIImage imageNamed:@"vip_price_discount_bg"];;
    }
    return _discountCardView;
}

- (UILabel *)subscribeTipLab {
    if (!_subscribeTipLab) {
        UILabel *label = [[UILabel alloc] init];
        label.backgroundColor = [UIColor.whiteColor colorWithAlphaComponent:0.16];
        label.textAlignment = NSTextAlignmentCenter;
        label.textColor = UIColorHex(#FFD5B6);
        label.font = kMedium_Font_NoDHPX(WKDHPX(14));
        label.text = @"包月立减";
        _subscribeTipLab = label;
    }
    return _subscribeTipLab;
}

- (UILabel *)priceLab {
    if (!_priceLab) {
        UILabel *label = [[UILabel alloc] init];
        label.textAlignment = NSTextAlignmentCenter;
        label.textColor = UIColorHex(#FFD5B6);
        label.font = BebasFont_Bold_NoDHPX(WKDHPX(80));
        label.text = @"25";
        _priceLab = label;
    }
    return _priceLab;
}

- (UILabel *)priceUnitLab {
    if (!_priceUnitLab) {
        UILabel *label = [[UILabel alloc] init];
        label.textAlignment = NSTextAlignmentCenter;
        label.textColor = UIColorHex(#FFD5B6);
        label.font = BebasFont_Bold_NoDHPX(WKDHPX(60));
        label.text = @"¥";
        _priceUnitLab = label;
    }
    return _priceUnitLab;
}

- (UILabel *)timerLab {
    if (!_timerLab) {
        UILabel *label = [[UILabel alloc] init];
        label.backgroundColor = [UIColor.whiteColor colorWithAlphaComponent:0.16];
        label.textAlignment = NSTextAlignmentCenter;
        label.textColor = UIColor.whiteColor;
        label.font = kSystem_Font_NoDHPX(WKDHPX(12));
        label.text = @"23:59:59 后失效";
        _timerLab = label;
    }
    return _timerLab;
}


#pragma mark - 事件处理

- (UIButton *)cancelBtn{
    if (!_cancelBtn) {
        UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
        [btn setImage:[UIImage imageNamed:@"icon_alert_close"] forState:UIControlStateNormal];
        [btn addTarget:self action:@selector(ensureBtnClick:) forControlEvents:UIControlEventTouchUpInside];
        _cancelBtn = btn;
    }
    return _cancelBtn;
}

- (UIButton *)subscribeBtn{
    if (!_subscribeBtn) {
        UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
        btn.backgroundColor = UIColorHex(#202121);
        [btn setTitle:@"立即使用" forState:UIControlStateNormal];
        [btn setTitleColor:[UIColor colorWithHexString:@"#FFD5B6"] forState:UIControlStateNormal];
        btn.titleLabel.font = kMedium_PingFangSC(14);
        [btn addTarget:self action:@selector(subscribeBtnClick:) forControlEvents:UIControlEventTouchUpInside];
        btn.layer.cornerRadius = DHPX(40)/2;
        btn.layer.masksToBounds = YES;
        _subscribeBtn = btn;
    }
    return _subscribeBtn;
}

- (void)ensureBtnClick:(UIButton *)sender{
   
}

- (void)subscribeBtnClick:(UIButton *)sender{

}

@end





