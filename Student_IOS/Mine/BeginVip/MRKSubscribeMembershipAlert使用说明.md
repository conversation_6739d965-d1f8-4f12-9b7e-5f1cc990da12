# MRKSubscribeMembershipAlert 使用说明

## 概述

`MRKSubscribeMembershipAlert` 是一个会员订阅弹窗组件，支持多种会员升级场景，提供了完整的用户交互和回调机制。

## 主要特性

### 🎯 支持的弹窗类型

```objc
typedef NS_ENUM(NSInteger, MembershipAlertType) {
    MembershipAlertTypeNormal,      // 普通会员升级
    MembershipAlertTypeAIPlan,      // AI计划升级
    MembershipAlertTypeCourse,      // 课程相关升级
};
```

### 🔧 核心功能

1. **多类型支持**：根据不同场景显示不同的标题和描述
2. **VIP卡片展示**：集成 `MRKSubscribeCardView` 显示会员套餐信息
3. **协议同意机制**：用户必须同意协议才能订阅
4. **完整的回调系统**：支持订阅、协议点击、取消等事件回调
5. **自适应UI**：根据协议同意状态动态更新按钮状态

## 使用方法

### 基本使用

```objc
#import "MRKSubscribeMembershipAlert.h"

// 创建VIP模型数据
MRKVipCardModel *vipModel = [[MRKVipCardModel alloc] init];
vipModel.name = @"连续包月";
vipModel.actualAmount = @"28";
vipModel.showAmount = @"35";
vipModel.dailyStartPrice = @"0.93";

// 创建弹窗
MRKSubscribeMembershipAlert *alert = [MRKSubscribeMembershipAlert alertWithType:MembershipAlertTypeNormal 
                                                                        vipModel:vipModel];
alert.membershipDelegate = self;

// 显示弹窗
[alert show];
```

### 代理方法实现

```objc
#pragma mark - MRKSubscribeMembershipAlertDelegate

/// 订阅按钮点击
- (void)membershipAlert:(MRKSubscribeMembershipAlert *)alert didClickSubscribeWithModel:(MRKVipCardModel *)model {
    NSLog(@"用户点击订阅，套餐：%@，价格：%@", model.name, model.actualAmount);
    
    // 处理订阅逻辑
    [self handleSubscriptionWithModel:model];
}

/// 协议链接点击
- (void)membershipAlert:(MRKSubscribeMembershipAlert *)alert didClickProtocolWithType:(NSString *)protocolType {
    if ([protocolType isEqualToString:@"member_service"]) {
        // 显示会员服务协议
        [self showMemberServiceProtocol];
    } else if ([protocolType isEqualToString:@"auto_renew"]) {
        // 显示自动续费协议
        [self showAutoRenewProtocol];
    }
}

/// 取消按钮点击
- (void)membershipAlertDidClickCancel:(MRKSubscribeMembershipAlert *)alert {
    NSLog(@"用户取消订阅");
    // 可以添加统计埋点等逻辑
}
```

### 不同类型的使用

```objc
// AI计划升级
MRKSubscribeMembershipAlert *aiAlert = [MRKSubscribeMembershipAlert alertWithType:MembershipAlertTypeAIPlan 
                                                                          vipModel:vipModel];
// 显示：标题"升级AI计划"，描述"开启AI智能训练，个性化定制专属计划"

// 课程相关升级
MRKSubscribeMembershipAlert *courseAlert = [MRKSubscribeMembershipAlert alertWithType:MembershipAlertTypeCourse 
                                                                              vipModel:vipModel];
// 显示：标题"解锁课程权限"，描述"升级会员，畅享海量精品课程"
```

### 动态更新数据

```objc
// 创建后更新VIP模型
[alert updateVipCardModel:newVipModel];

// 或者直接设置属性
alert.vipCardModel = newVipModel;
```

## 优化亮点

### 1. **类型安全**

**优化前**：
```objc
@property (nonatomic, strong) MembershipAlertType *alertType; // 错误：指针类型
```

**优化后**：
```objc
@property (nonatomic, assign) MembershipAlertType alertType; // 正确：枚举类型
```

### 2. **完整的代理系统**

**优化前**：空的点击事件，直接关闭弹窗

**优化后**：完整的代理回调系统
```objc
@protocol MRKSubscribeMembershipAlertDelegate <NSObject>
@optional
- (void)membershipAlert:(MRKSubscribeMembershipAlert *)alert didClickSubscribeWithModel:(MRKVipCardModel *)model;
- (void)membershipAlert:(MRKSubscribeMembershipAlert *)alert didClickProtocolWithType:(NSString *)protocolType;
- (void)membershipAlertDidClickCancel:(MRKSubscribeMembershipAlert *)alert;
@end
```

### 3. **协议同意机制**

**优化前**：同意按钮点击直接关闭弹窗

**优化后**：正确的状态切换和UI更新
```objc
- (void)agreeButton:(UIButton *)sender {
    sender.selected = !sender.selected;
    self.isAgreedProtocol = sender.selected;
    [self updateSubscribeButtonState]; // 更新订阅按钮状态
}
```

### 4. **智能的按钮状态管理**

```objc
- (void)updateSubscribeButtonState {
    self.subscribeBtn.enabled = self.isAgreedProtocol;
    self.subscribeBtn.alpha = self.isAgreedProtocol ? 1.0 : 0.6;
}
```

### 5. **修复布局约束错误**

**优化前**：错误的约束引用
```objc
make.top.mas_equalTo(self.mas_top).offset(DHPX(104)); // 错误：引用self
```

**优化后**：正确的约束引用
```objc
make.top.mas_equalTo(self.descripLab.mas_bottom).offset(DHPX(16)); // 正确：相对于描述标签
```

### 6. **完善的懒加载实现**

添加了缺失的属性实现：
- `descripLab` - 描述标签
- `subscribeView` - 订阅卡片视图
- `subscribeTipLab` - 订阅提示标签

## 架构设计

### 类图结构

```
MRKAlertView (基类)
    ↑
MRKSubscribeMembershipAlert
    ├── MRKSubscribeCardView (VIP卡片)
    ├── MRKVipCardModel (数据模型)
    └── MRKSubscribeMembershipAlertDelegate (代理协议)
```

### 生命周期

1. **初始化**：`alertWithType:vipModel:`
2. **数据设置**：`setVipCardModel:` / `setAlertType:`
3. **UI更新**：`updateUIForAlertType` / `updateSubscribeButtonState`
4. **显示**：`show` / `showWithCompletion:`
5. **用户交互**：协议同意 → 按钮状态更新 → 订阅点击
6. **回调处理**：代理方法调用
7. **关闭**：`dismissAnimated:`

## 最佳实践

### 1. **数据验证**

```objc
- (void)subscribeBtnClick:(UIButton *)sender {
    if (!self.isAgreedProtocol) {
        // 提示用户需要同意协议
        return;
    }
    
    if (!self.vipCardModel) {
        // 数据异常处理
        return;
    }
    
    // 执行订阅逻辑
}
```

### 2. **错误处理**

```objc
- (void)handleSubscriptionWithModel:(MRKVipCardModel *)model {
    if (!model.appStoreCode) {
        // 显示错误提示
        [self showErrorAlert:@"商品信息异常，请稍后重试"];
        return;
    }
    
    // 调用支付接口
    [PaymentManager purchaseProduct:model.appStoreCode completion:^(BOOL success, NSError *error) {
        if (success) {
            [self showSuccessAlert:@"订阅成功"];
        } else {
            [self showErrorAlert:error.localizedDescription];
        }
    }];
}
```

### 3. **内存管理**

```objc
@interface ViewController () <MRKSubscribeMembershipAlertDelegate>
@property (nonatomic, weak) MRKSubscribeMembershipAlert *currentAlert; // 使用weak避免循环引用
@end
```

## 总结

优化后的 `MRKSubscribeMembershipAlert` 具有以下优势：

1. **类型安全**：修复了类型错误，确保编译时安全
2. **功能完整**：实现了所有缺失的功能和UI组件
3. **交互友好**：正确的协议同意机制和按钮状态管理
4. **扩展性强**：支持多种弹窗类型，易于扩展新场景
5. **代码规范**：清晰的代码结构和完整的注释
6. **易于使用**：简单的API设计和完整的使用示例

这个组件现在可以安全地用于生产环境，为用户提供流畅的会员订阅体验。
