//
//  MRKVipPackageView.m
//  Student_IOS
//
//  Created by Junq on 2023/5/24.
//

#import "MRKVipPackageView.h"
#import "SDCycleScrollView.h"
#import "UIButton+Layout.h"
#import "UIView+Animation.h"
#import "NSArray+Sudoku.h"
#import "POP.h"

@interface MRKPurchaseProtocolView()
@property (nonatomic, strong) UILabel *autoRenewalTitle;
@property (nonatomic, strong) YYLabel *autoRenewalAbout;
@end


@implementation MRKPurchaseProtocolView
- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self addSubview:self.autoRenewalTitle];
        [self addSubview:self.autoRenewalAbout];
        
        [self.autoRenewalTitle mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.mas_top);
            make.left.equalTo(self.mas_left).offset(WKDHPX(16));
            make.right.equalTo(self.mas_right).offset(-WKDHPX(16));
            make.height.mas_equalTo(WKDHPX(24));
        }];
        
        [self.autoRenewalAbout mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.autoRenewalTitle.mas_bottom).offset(WKDHPX(20));
            make.left.equalTo(self.mas_left).offset(WKDHPX(16));
            make.width.mas_equalTo(RealScreenWidth -WKDHPX(16)*2);
            make.bottom.mas_equalTo(-WKDHPX(50));
        }];
    }
    return self;
}

- (UILabel *)autoRenewalTitle{
    if (!_autoRenewalTitle) {
        UILabel *label = [[UILabel alloc]init];
        label.textColor = [UIColor whiteColor];
        label.text = @"自动续费说明";
        label.font = [UIFont systemFontOfSize:WKDHPX(17)];
        label.textAlignment = NSTextAlignmentCenter;
        _autoRenewalTitle = label;
    }
    return _autoRenewalTitle;
}

- (YYLabel *)autoRenewalAbout{
    if (!_autoRenewalAbout) {
        YYLabel *label = [[YYLabel alloc] init];
        label.textAlignment = NSTextAlignmentLeft;
        label.textVerticalAlignment = YYTextVerticalAlignmentTop;
        label.numberOfLines = 0;
        label.left = WKDHPX(16);
        label.width = RealScreenWidth - WKDHPX(16)*2;
        label.displaysAsynchronously = YES;
        label.clearContentsBeforeAsynchronouslyDisplay = NO;
        label.preferredMaxLayoutWidth = RealScreenWidth - WKDHPX(16)*2;
        label.attributedText = self.explainAttributedStr;
        label.numberOfLines = 0;
        _autoRenewalAbout = label;
    }
    return _autoRenewalAbout;
}

- (NSMutableAttributedString *)explainAttributedStr {
    NSMutableAttributedString *attributeText = [[NSMutableAttributedString alloc] initWithString:@"续订："];
    attributeText.color = UIColor.whiteColor;
    attributeText.font = [UIFont systemFontOfSize:WKDHPX(13)];
    
    NSMutableAttributedString *text1 = [[NSMutableAttributedString alloc] initWithString:@"自动续费商品包括“连续包月/连续包季/连续包年“，您确定购买后，会在您的会员到期前24小时通过您签约的支付方式自动发起续费扣款。扣费成功后，您的会员有效期自动延长一个周期。\n\n"];
    text1.color = UIColorHex(#848A9B);
    [attributeText appendAttributedString:text1];
    
    NSMutableAttributedString *text2 = [[NSMutableAttributedString alloc] initWithString:@"终止方法："];
    text2.color = UIColor.whiteColor;
    [attributeText appendAttributedString:text2];
    
    NSMutableAttributedString *text3 = [[NSMutableAttributedString alloc] initWithString:@"苹果设备：“设置”>进入“iTunes Store/App Store” >点击“Apple ID”，在管理订阅中关闭自动续费的功能。\n\n"];
    text3.color = UIColorHex(#848A9B);
    [attributeText appendAttributedString:text3];
    
    NSMutableAttributedString *text4 = [[NSMutableAttributedString alloc] initWithString:@"温馨提示："];
    text4.color = UIColor.whiteColor;
    [attributeText appendAttributedString:text4];
    
    NSMutableAttributedString *text5 = [[NSMutableAttributedString alloc] initWithString:@"如未在会员到期前至少24小时关闭自动续费期，此服务将会自动续订。\n\n"];
    text5.color = UIColorHex(#848A9B);
    [attributeText appendAttributedString:text5];
    
    NSMutableAttributedString *text6 = [[NSMutableAttributedString alloc] initWithString:@"注："];
    text6.color = UIColor.whiteColor;
    [attributeText appendAttributedString:text6];
    
    NSMutableAttributedString *text7 = [[NSMutableAttributedString alloc] initWithString:@"虚拟商品购买后无法退换，敬请谅解！有任何疑问可点击此处\n"];
    text7.color = UIColorHex(#848A9B);
    [attributeText appendAttributedString:text7];
    
    @weakify(self);
    NSMutableAttributedString *text8 = [[NSMutableAttributedString alloc] initWithString:@"\n联系客服"];
    text8.color = UIColorHex(#848A9B);
    [text8 setTextHighlightRange:[text8.string rangeOfString:@"联系客服"]
                           color:[UIColor colorWithHexString:@"#1791E3"]
                 backgroundColor:[UIColor clearColor]
                       tapAction:^(UIView * _Nonnull containerView, NSAttributedString * _Nonnull text, NSRange range, CGRect rect) {
        @strongify(self);
        if (self.customerServiceBlock){
            self.customerServiceBlock();
        }
    }];
    [attributeText appendAttributedString:text8];
    return attributeText;
}

/*
 // Only override drawRect: if you perform custom drawing.
 // An empty implementation adversely affects performance during animation.
 - (void)drawRect:(CGRect)rect {
 // Drawing code
 }
 */

@end



@interface MRKVipProtrolButton()
@property (nonatomic, strong) UILabel *titleLab;
@property (nonatomic, strong) UIImageView *partView;
@end


@implementation MRKVipProtrolButton
- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        self.layer.cornerRadius = 8.0f;
        self.layer.masksToBounds = YES;
        
        [self addSubview:self.titleLab];
        [self addSubview:self.partView];
    }
    return self;
}

- (void)layoutSubviews{
    [super layoutSubviews];
    
    [self.titleLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_equalTo(self.mas_centerY);
        make.left.mas_equalTo(DHPX(10));
        make.size.mas_equalTo(CGSizeMake(DHPX(120), DHPX(20)));
    }];
    
    [self.partView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_equalTo(self.mas_right).offset(-DHPX(10));
        make.centerY.mas_equalTo(self.mas_centerY);
        make.size.mas_equalTo(CGSizeMake(15, 20));
    }];
}

- (void)setBtnTitle:(NSString *)btnTitle{
    _btnTitle = btnTitle;
    self.titleLab.text = btnTitle;
}

- (UIImageView *)partView{
    if (!_partView) {
        _partView = [[UIImageView alloc] init];
        _partView.contentMode = UIViewContentModeScaleAspectFill;
        _partView.image = [UIImage imageNamed:@"icon_arrow_16pt"];
        _partView.tintColor = [UIColor colorWithHexString:@"#333333"];
    }
    return _partView;
}

- (UILabel *)titleLab{
    if (!_titleLab) {
        _titleLab = [[UILabel alloc] init];
        _titleLab.font = [UIFont systemFontOfSize:14 weight:UIFontWeightMedium];
        _titleLab.textColor = [UIColor colorWithHexString:@"#333333"];
        _titleLab.textAlignment = 0;
    }
    return _titleLab;
}
@end







@interface MRKVipCardView()
@property (nonatomic, strong) UILabel *titleLab;
@property (nonatomic, strong) UILabel *priceLab;
@property (nonatomic, strong) UILabel *originalPriceLab;
@property (nonatomic, strong) UILabel *averageDailyLab;
@property (nonatomic, strong) UIImageView *tagImageView;

@property (nonatomic, assign) BOOL hasPointNumber;
@property (nonatomic, assign) NSInteger countDownNumber;
@end

@implementation MRKVipCardView

- (instancetype)init {
    self = [super init];
    if (self) {
        self.backgroundColor = UIColor.whiteColor;
        self.layer.cornerRadius = 8.0f;
        self.layer.masksToBounds = YES;
        
        [self addSubview:self.priceLab];
        [self addSubview:self.titleLab];
        [self addSubview:self.originalPriceLab];
        [self addSubview:self.averageDailyLab];
        [self addSubview:self.tagImageView];
        
        [self.priceLab mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(self.mas_centerY);
            make.centerX.equalTo(self.mas_centerX);
            make.height.mas_equalTo(WKDHPX(48));
        }];
        [self.titleLab mas_makeConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(self.priceLab.mas_top).offset(WKDHPX(4));
            make.left.equalTo(self.mas_left).offset(WKDHPX(8));
            make.right.equalTo(self.mas_right).offset(-WKDHPX(8));
            make.height.mas_equalTo(WKDHPX(18));
        }];
        [self.originalPriceLab mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.priceLab.mas_bottom);
            make.left.equalTo(self.mas_left).offset(WKDHPX(8));
            make.right.equalTo(self.mas_right).offset(-WKDHPX(8));
            make.height.mas_equalTo(WKDHPX(22));
        }];
        [self.averageDailyLab mas_makeConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(self.mas_bottom);
            make.left.equalTo(self.mas_left);
            make.right.equalTo(self.mas_right);
            make.height.mas_equalTo(WKDHPX(15));
        }];
        [self.tagImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.mas_top);
            make.left.equalTo(self.mas_left);
            make.size.mas_equalTo(CGSizeMake(WKDHPX(77), WKDHPX(18)));
        }];
        
        ///金额动画通知
        [[NSNotificationCenter defaultCenter] addObserver:self
                                                 selector:@selector(startPriceAnimation)
                                                     name:@"PurchasePriceAnimation"
                                                   object:nil];
        
        [[NSNotificationCenter defaultCenter] addObserver:self
                                                 selector:@selector(startTimerAnimation)
                                                     name:@"purchaseCouponNotification"
                                                   object:nil];
    }
    return self;
}

- (void)dealloc{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
    
    NSLog(@"MRKVipCardView ===== dealloc");
}

- (void)layoutSubviews{
    [super layoutSubviews];
}

- (UIImageView *)tagImageView{
    if (!_tagImageView) {
        _tagImageView = [[UIImageView alloc] init];
        _tagImageView.contentMode = UIViewContentModeScaleAspectFit;
        _tagImageView.autoresizingMask = UIViewAutoresizingFlexibleTopMargin | UIViewAutoresizingFlexibleLeftMargin;
    }
    return _tagImageView;
}

- (UILabel *)titleLab{
    if (!_titleLab) {
        _titleLab = [[UILabel alloc] init];
        _titleLab.textAlignment = NSTextAlignmentCenter;
        _titleLab.font = kMedium_Font_NoDHPX(WKDHPX(15));
        _titleLab.textColor = [UIColor colorWithHexString:@"#363A44"];
    }
    return _titleLab;
}

- (UILabel *)priceLab{
    if (!_priceLab) {
        _priceLab = [[UILabel alloc] init];
        _priceLab.textAlignment = NSTextAlignmentCenter;
        _priceLab.font = BebasFont_Bold_NoDHPX(WKDHPX(18));
        _priceLab.textColor = [UIColor colorWithHexString:@"#363A44"];
    }
    return _priceLab;
}

- (UILabel *)originalPriceLab{
    if (!_originalPriceLab) {
        _originalPriceLab = [[UILabel alloc] init];
        _originalPriceLab.textAlignment = NSTextAlignmentCenter;
        _originalPriceLab.textColor = [UIColor colorWithHexString:@"#848A9B"];
    }
    return _originalPriceLab;
}

- (UILabel *)averageDailyLab{
    if (!_averageDailyLab) {
        _averageDailyLab = [[UILabel alloc] init];
        _averageDailyLab.backgroundColor = UIColorHex(#C19C71);
        _averageDailyLab.textAlignment = NSTextAlignmentCenter;
        _averageDailyLab.font = kSystem_Font_NoDHPX(WKDHPX(11));
        _averageDailyLab.textColor = UIColor.whiteColor;
        _averageDailyLab.hidden = YES;
    }
    return _averageDailyLab;
}

- (void)setModel:(MRKVipCardModel *)model{
    _model = model;
    
    self.backgroundColor = self.showInAlert ? UIColorHex(#F8F8FA) : UIColor.whiteColor;
    self.titleLab.text = model.name;
    
    ///
    NSString *couponExpireTime = model.couponExpireTime;
    if (couponExpireTime.doubleValue > 0) {
        NSTimeInterval nowTime = [[NSDate date] timeIntervalSince1970];// 当前秒级时间戳
        NSTimeInterval expireSeconds = couponExpireTime.doubleValue/1000;
        NSInteger leftSeconds = (NSInteger)(expireSeconds - nowTime);
        
        if (leftSeconds > 0) {
            self.countDownNumber = leftSeconds;
        }
    }
    
    ///执行一次
    [self startTimerAnimation];
    
    ///如果是在等待弹窗中，金额需要展示showAmount
    if (self.waitAlertDismiss && [model.showAmount isNotBlank]) {
        self.priceLab.attributedText = ({
            NSMutableAttributedString *str = [[NSMutableAttributedString alloc] initWithString:@"¥"];
            str.font = BebasFont_Bold_NoDHPX(WKDHPX(18));
            NSMutableAttributedString *priceStr = [[NSMutableAttributedString alloc] initWithString:model.showAmount ?:@""];
            priceStr.font = BebasFont_Bold_NoDHPX(WKDHPX(32));
            [str appendAttributedString:priceStr];
            str;
        });
    } else {
        self.priceLab.attributedText = ({
            NSString *price = model.actualAmount?:@"";
            if (model.isAuto && [model.firstAmount isNotBlank]){
                price = model.firstAmount;
            }
            NSMutableAttributedString *str = [[NSMutableAttributedString alloc] initWithString:@"¥"];
            str.font = BebasFont_Bold_NoDHPX(WKDHPX(18));
            NSMutableAttributedString *priceStr = [[NSMutableAttributedString alloc] initWithString:price];
            priceStr.font = BebasFont_Bold_NoDHPX(WKDHPX(32));
            [str appendAttributedString:priceStr];
            str;
        });
    }
    
    ///
    if ([model.showAmount isNotBlank] && ![model.showAmount isEqualToString:model.actualAmount]) {
        self.originalPriceLab.hidden = NO;
        self.originalPriceLab.attributedText = ({
            NSString *str = [NSString stringWithFormat:@"¥%@", model.showAmount];
            NSMutableAttributedString *priceStr = [[NSMutableAttributedString alloc] initWithString:str];
            priceStr.font = kSystem_Font_NoDHPX(WKDHPX(13));
            priceStr.strikethroughStyle = NSUnderlineStyleSingle;
            priceStr;
        });
    } else {
        self.originalPriceLab.hidden = YES;
    }
    
    self.tagImageView.image = nil;
    if ([model.labelIcon isNotBlank]) {
        [self.tagImageView setImageURL:[NSURL URLWithString:model.labelIcon]];
    }
}

- (void)setHasSelectCurrent:(BOOL)hasSelectCurrent{
    _hasSelectCurrent = hasSelectCurrent;
    
    if (hasSelectCurrent){
        self.backgroundColor = UIColorHex(#FFECDE);
        
        self.titleLab.textColor = [UIColor colorWithHexString:@"#A05700"];
        self.priceLab.textColor = [UIColor colorWithHexString:@"#804E13"];
    } else {
        self.backgroundColor = self.showInAlert ? UIColorHex(#F8F8FA) : UIColor.whiteColor;
        
        self.titleLab.textColor = [UIColor colorWithHexString:@"#363A44"];
        self.priceLab.textColor = [UIColor colorWithHexString:@"#363A44"];
    }
}

- (void)startPriceAnimation{
    self.waitAlertDismiss = NO;
    if (self.model == nil) return;
    if (self.tag != 1000) return;///
    if ([self.model.showAmount isEmpty]) return;
    
    NSString *fromStr = self.model.showAmount ?: @"";
    NSString *toStr = ({
        NSString *price = self.model.actualAmount ?: @"";
        if (self.model.isAuto && [self.model.firstAmount isNotBlank]){
            price = self.model.firstAmount;
        }
        price;
    });
    
    if ([fromStr isEqualToString:@""] || [toStr isEqualToString:@""]) return;
    if ([fromStr isEqualToString:toStr]) return;
    
    ///标记精度，无奈之举
    if ([fromStr containsString:@"."] || [toStr containsString:@"."]) {
        self.hasPointNumber = YES;
    }
    
    [self setLabelAnimation:self.priceLab
               andFromValue:[NSNumber numberWithString:fromStr]
                 andToValue:[NSNumber numberWithString:toStr]];
}

- (void)setLabelAnimation:(UILabel *)label andFromValue:(NSNumber *)fromValue andToValue:(NSNumber *)toValue{
    POPBasicAnimation *anim = [POPBasicAnimation animation];
    anim.duration = 1.0;
    anim.timingFunction = [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionEaseInEaseOut];
    anim.property = [POPAnimatableProperty propertyWithName:@"com.merit.number" initializer:^(POPMutableAnimatableProperty *prop) {
        prop.readBlock = ^(id obj, CGFloat values[]) {
            values[0] = [[obj description] floatValue];
        };
        prop.writeBlock = ^(id obj, const CGFloat values[]) {
            if ([obj isKindOfClass:[UILabel class]]) {
                UILabel *label = (UILabel *)obj;
                NSString *text = self.hasPointNumber ? [NSString stringWithFormat:@"%.1f",values[0]] : [NSString stringWithFormat:@"%.0f",values[0]];
               
                NSMutableAttributedString *attributedText = [[NSMutableAttributedString alloc] initWithString:@"¥"];
                attributedText.font = BebasFont_Bold_NoDHPX(WKDHPX(18));
                NSMutableAttributedString *priceStr = [[NSMutableAttributedString alloc] initWithString:text];
                priceStr.font = BebasFont_Bold_NoDHPX(WKDHPX(32));
                [attributedText appendAttributedString:priceStr];
                label.attributedText = attributedText;
            }
        };
        prop.threshold = self.hasPointNumber ? 0.1 : 1.0;
    }];
    anim.fromValue = fromValue;
    anim.toValue = toValue;
    [label pop_addAnimation:anim forKey:@"counting"];
}

- (void)startTimerAnimation{
    if (self.model == nil) return;
    if (self.countDownNumber <= 0) {
        self.averageDailyLab.text = @"00:00:00 后失效";
        self.averageDailyLab.hidden = YES;
        return;
    }
    
    NSInteger seconds = self.countDownNumber--;
    NSString *hour = [NSString stringWithFormat:@"%02ld",seconds/3600];
    NSString *minute = [NSString stringWithFormat:@"%02ld",(seconds%3600)/60];
    NSString *second = [NSString stringWithFormat:@"%02ld",seconds%60];
    NSString *formatTime = [NSString stringWithFormat:@"%@:%@:%@ 后失效", hour, minute, second];
    self.averageDailyLab.text = formatTime;
    self.averageDailyLab.hidden = NO;
}

/*
 // Only override drawRect: if you perform custom drawing.
 // An empty implementation adversely affects performance during animation.
 - (void)drawRect:(CGRect)rect {
 // Drawing code
 }
 */

@end



@interface MRKVipPurchaseBottomView()
@property (nonatomic, strong) UIButton *subscribeBtn;
@property (nonatomic, strong) UIButton *agreementBtn;
@property (nonatomic, strong) YYLabel *protocolLab;
@end

@implementation MRKVipPurchaseBottomView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        
        [self layoutSubviewsUI];
        [self layoutSubviewsData];
    }
    return self;
}

- (void)layoutSubviewsUI{
    [self addSubview:self.subscribeBtn];
    [self addSubview:self.protocolLab];
    [self addSubview:self.agreementBtn];
    
    [self.subscribeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.mas_top).offset(WKDHPX(20));
        make.left.equalTo(self.mas_left).offset(WKDHPX(16));
        make.right.equalTo(self.mas_right).offset(-WKDHPX(16));
        make.height.mas_equalTo(WKDHPX(48));
    }];
    [self.protocolLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.subscribeBtn.mas_bottom).offset(WKDHPX(12));
        make.centerX.equalTo(self.mas_centerX).offset(10);
    }];
    [self.agreementBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.protocolLab.mas_left).offset(0);
        make.centerY.equalTo(self.protocolLab.mas_centerY);
        make.size.mas_equalTo(CGSizeMake(30, 30));
    }];
}

- (void)layoutSubviewsData{
    NSMutableAttributedString *protrolText = [[NSMutableAttributedString alloc] initWithString:@"我已阅读并同意"];
    protrolText.color = [UIColor colorWithHexString:@"#B3B5B9"];
    protrolText.font = [UIFont systemFontOfSize:WKDHPX(13)];
    
    @weakify(self);
    NSMutableAttributedString *text = [[NSMutableAttributedString alloc] initWithString:@" 会员服务协议 "];
    text.font = [UIFont systemFontOfSize:WKDHPX(13)];
    text.color = UIColorHex(#202121);
    text.underlineStyle = NSUnderlineStyleSingle;
    [text setTextHighlightRange:text.rangeOfAll
                          color:UIColorHex(#202121)
                backgroundColor:[UIColor clearColor]
                      tapAction:^(UIView * _Nonnull containerView, NSAttributedString * _Nonnull text, NSRange range, CGRect rect) {
        [self_weak_ subscribeProtocolAction:0];
    }];
    [protrolText appendAttributedString:text];
    
    NSMutableAttributedString *andText= [[NSMutableAttributedString alloc] initWithString:@"和"];
    andText.color = [UIColor colorWithHexString:@"#B3B5B9"];
    andText.font = [UIFont systemFontOfSize:WKDHPX(13)];
    [protrolText appendAttributedString:andText];
    
    NSMutableAttributedString *renewText = [[NSMutableAttributedString alloc] initWithString:@" 自动续费服务协议 "];
    renewText.font = [UIFont systemFontOfSize:WKDHPX(13)];
    renewText.color = UIColorHex(#202121);
    renewText.underlineStyle = NSUnderlineStyleSingle;
    [renewText setTextHighlightRange:renewText.rangeOfAll
                               color:UIColorHex(#202121)
                     backgroundColor:[UIColor clearColor]
                           tapAction:^(UIView * _Nonnull containerView, NSAttributedString * _Nonnull text, NSRange range, CGRect rect) {
        [self_weak_ subscribeProtocolAction:1];
    }];
    [protrolText appendAttributedString:renewText];
    self.protocolLab.attributedText = protrolText;
    self.protocolLab.textAlignment = NSTextAlignmentLeft;
}

- (void)layoutSubviews{
    [super layoutSubviews];
}

- (void)setTipPurchaseStr:(NSAttributedString *)tipPurchaseStr{
    _tipPurchaseStr = tipPurchaseStr;
    [self.subscribeBtn setAttributedTitle:tipPurchaseStr forState:UIControlStateNormal];
}

- (YYLabel *)protocolLab{
    if (!_protocolLab){
        _protocolLab = [[YYLabel alloc] init];
        _protocolLab.textVerticalAlignment = YYTextVerticalAlignmentCenter;
    }
    return _protocolLab;
}

- (UIButton *)agreementBtn {
    if (!_agreementBtn) {
        _agreementBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_agreementBtn setImage:[UIImage imageNamed:@"icon_protocol_agree"] forState:UIControlStateSelected];
        [_agreementBtn setImage:[UIImage imageNamed:@"icon_protocol_unagree"] forState:UIControlStateNormal];
        [_agreementBtn addTarget:self action:@selector(agreeButton:) forControlEvents:UIControlEventTouchUpInside];
        _agreementBtn.contentMode = UIViewContentModeCenter;
    }
    return _agreementBtn;
}

- (UIButton *)subscribeBtn {
    if (!_subscribeBtn){
        _subscribeBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        _subscribeBtn.backgroundColor = UIColorHex(#202121);
        [_subscribeBtn setTitleColor:UIColorHex(#FFEEDA) forState:UIControlStateNormal];
        _subscribeBtn.titleLabel.font = [UIFont systemFontOfSize:16 weight:UIFontWeightMedium];
        [_subscribeBtn addTarget:self action:@selector(subscribeAction:) forControlEvents:UIControlEventTouchUpInside];
        _subscribeBtn.layer.cornerRadius = WKDHPX(48)/2;
        _subscribeBtn.layer.masksToBounds = YES;
    }
    return _subscribeBtn;
}

- (void)agreeButton:(UIButton *)sender{
    self.hasAgreeProtocol = !self.hasAgreeProtocol;
}

- (void)setHasAgreeProtocol:(BOOL)hasAgreeProtocol {
    _hasAgreeProtocol = hasAgreeProtocol;
    self.agreementBtn.selected = hasAgreeProtocol;
}

- (void)subscribeAction:(id)sender{
    if (self.subscribeVipPuchaseBlock){
        self.subscribeVipPuchaseBlock();
    }
}

- (void)subscribeProtocolAction:(NSInteger)index{
    if (self.subscribeProtocolBlock){
        self.subscribeProtocolBlock(index);
    }
}

/*
 // Only override drawRect: if you perform custom drawing.
 // An empty implementation adversely affects performance during animation.
 - (void)drawRect:(CGRect)rect {
 // Drawing code
 }
 */

@end







@interface MRKPurchaseRightsItemView()
@property (nonatomic, strong) UIImageView *bgImageView;
@property (nonatomic, strong) UIImageView *iconImageView;
@property (nonatomic, strong) UILabel *titleLab;
@end

@implementation MRKPurchaseRightsItemView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        [self addSubview:self.self.bgImageView];
        [self.bgImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.mas_top).offset(0);
            make.centerX.equalTo(self.mas_centerX);
            make.size.mas_equalTo(CGSizeMake(WKDHPX(40), WKDHPX(40)));
        }];
        
        [self.bgImageView addSubview:self.iconImageView];
        [self.iconImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.center.equalTo(self.bgImageView);
            make.size.mas_equalTo(CGSizeMake(WKDHPX(22), WKDHPX(22)));
        }];
        
        [self addSubview:self.titleLab];
        [self.titleLab mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.bgImageView.mas_bottom).offset(WKDHPX(6));
            make.left.equalTo(self.mas_left);
            make.right.equalTo(self.mas_right);
            make.bottom.equalTo(self.mas_bottom);
        }];
    }
    return self;
}

- (void)layoutSubviews{
    [super layoutSubviews];
}

- (UIImageView *)bgImageView{
    if (!_bgImageView) {
        _bgImageView = [[UIImageView alloc] init];
        _bgImageView.contentMode = UIViewContentModeScaleAspectFit;
    }
    return _bgImageView;
}

- (UIImageView *)iconImageView{
    if (!_iconImageView) {
        _iconImageView = [[UIImageView alloc] init];
        _iconImageView.contentMode = UIViewContentModeScaleAspectFit;
        _iconImageView.sd_imageTransition = SDWebImageTransition.fadeTransition;
    }
    return _iconImageView;
}

- (UILabel *)titleLab{
    if (!_titleLab) {
        _titleLab = [[UILabel alloc] init];
        _titleLab.textAlignment = NSTextAlignmentCenter;
        _titleLab.font = kSystem_Font_NoDHPX(WKDHPX(12));
        _titleLab.textColor = UIColor.whiteColor;
    }
    return _titleLab;
}

- (void)itemData:(MRKVipExclusiveItemModel *)model vipType:(NSString *)vipType{
    self.model = model;
    if (vipType.intValue == 30){
        self.bgImageView.image = [UIImage imageNamed:@"vip_qy_small_bg"];
    }else{
        self.bgImageView.image = [UIImage imageNamed:@"xvip_qy_small_bg"];
    }
    
    self.titleLab.text = model.name;
    if (model.isMoreRights){
        self.iconImageView.image = [UIImage imageNamed:@"vip_qy_more_bg"];
    } else {
        [self.iconImageView  sd_setImageWithURL:[NSURL URLWithString:model.icon]];
    }
}

/*
 // Only override drawRect: if you perform custom drawing.
 // An empty implementation adversely affects performance during animation.
 - (void)drawRect:(CGRect)rect {
 // Drawing code
 }
 */

@end
