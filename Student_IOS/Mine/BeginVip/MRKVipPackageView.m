//
//  MRKVipPackageView.m
//  Student_IOS
//
//  Created by <PERSON><PERSON> on 2023/5/24.
//

#import "MRKVipPackageView.h"
#import "SDCycleScrollView.h"
#import "UIButton+Layout.h"
#import "UIView+Animation.h"
#import "NSArray+Sudoku.h"



@interface MRKVipHeaderView()
@property (nonatomic, strong) UIImageView *backGroundImageView;
@property (nonatomic, strong) UIImageView *backCardImageView;
@property (nonatomic, strong) UIImageView *backBottomImageView;
@property (nonatomic, strong) UIImageView *stoneImageView;
@property (nonatomic, strong) UIImageView *shadowImageView;

@property (nonatomic, strong) UIImageView *avatarImageView;
@property (nonatomic, strong) UILabel *nickNameLab;
@property (nonatomic, strong) UILabel *descripLab;

@property (nonatomic, strong) UILabel *vipDescripLab;
//@property (nonatomic, strong) UIButton *recordBtn;
@end


@implementation MRKVipHeaderView


- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        self.clipsToBounds = YES;
        
        [self addSubview:self.backGroundImageView];
        [self addSubview:self.backCardImageView];
        [self addSubview:self.backBottomImageView];
        [self addSubview:self.stoneImageView];
        [self addSubview:self.shadowImageView];
       
        
        [self addSubview:self.avatarImageView];
        [self addSubview:self.nickNameLab];
        [self addSubview:self.descripLab];
        
        [self addSubview:self.vipDescripLab];
//        [self addSubview:self.recordBtn];
        
        [self insertSubview:self.shadowImageView belowSubview:self.stoneImageView];
        [self addAnimatioin];
    }
    return self;
}

// 添加动画
- (void)addAnimatioin {
    [self.stoneImageView animationTranslationY:10 repeatCount:HUGE_VALF];
    [self.shadowImageView animationZoomScale:1.2 duration:2.0 repeat:0];
}


- (void)layoutSubviews{
    [super layoutSubviews];
    
    [self.backGroundImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsMake(0, 0, 0, 0));
    }];
    
    [self.backCardImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.backGroundImageView.mas_left).offset(18);
        make.right.equalTo(self.backGroundImageView.mas_right).offset(-18);
        make.height.mas_equalTo(kScreenWidth *0.368);
        make.bottom.equalTo(self.mas_bottom).offset(-6);
    }];
    
    [self.backBottomImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.backGroundImageView.mas_left).offset(0);
        make.right.equalTo(self.backGroundImageView.mas_right).offset(0);
        make.height.mas_equalTo(kScreenWidth *0.067);
        make.bottom.equalTo(self.mas_bottom).offset(0);
    }];
    
    [self.stoneImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.backCardImageView.mas_top).offset(-DHPX(20));
        make.right.equalTo(self.backCardImageView.mas_right).offset(0);
        make.size.mas_equalTo(CGSizeMake(kScreenWidth *0.266, kScreenWidth *0.266));
    }];
    
    [self.shadowImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.stoneImageView.mas_bottom).offset(0);
        make.centerX.equalTo(self.stoneImageView.mas_centerX);
        make.size.mas_equalTo(CGSizeMake(DHPX(40), DHPX(20)));
    }];
    
    
    
    float topRatio = (float)35.0/138.0;
    
    [self.avatarImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.backCardImageView.mas_top).offset(topRatio * (kScreenWidth *0.368));
        make.left.equalTo(self.backCardImageView.mas_left).offset(DHPX(10));
        make.size.mas_equalTo(CGSizeMake(DHPX(40), DHPX(40)));
    }];

    [self.nickNameLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.avatarImageView.mas_right).offset(10);
        make.right.equalTo(@(DHPX(-130)));
        make.top.equalTo(self.avatarImageView.mas_top).offset(0);
        make.height.equalTo(@(DHPX(22)));
    }];

    [self.descripLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.nickNameLab.mas_left).offset(0);
        make.top.equalTo(self.nickNameLab.mas_bottom).offset(0);
        make.height.equalTo(@(DHPX(18)));
    }];

    [self.vipDescripLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.avatarImageView.mas_left);
        make.bottom.equalTo(self.backBottomImageView.mas_top).offset(-DHPX(10));
        make.height.equalTo(@(DHPX(22)));
    }];
    
//    [self.recordBtn mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.centerY.mas_equalTo(self.vipDescripLab.mas_centerY);
//        make.right.equalTo(self.backCardImageView.mas_right).offset(-10);
//        make.size.mas_equalTo(CGSizeMake(DHPX(76), DHPX(28)));
//    }];
}

- (void)setPageModel:(MRKVipPageModel *)pageModel{
    _pageModel = pageModel;
    
    NSString *url = [NSString imageUrlClip:UserInfo.avatar andSize:CGSizeMake(DHPX(40.0), DHPX(40.0))];
    [self.avatarImageView sd_setImageWithURL:[NSURL URLWithString:url] placeholderImage:UserInfo.avatarHoldingImage];
    
    self.nickNameLab.text = UserInfo.nickName;
    
    if ([pageModel.expireDate isEmpty] && !pageModel.isMember ) {
//        self.recordBtn.hidden = YES;
        self.descripLab.text = @"你还不是会员，开通立享权益";
        self.backCardImageView.image = [UIImage imageNamed:@"bg_header_unvipcard"];
    } else if ([pageModel.expireDate isNotBlank] && pageModel.isMember){
//        self.recordBtn.hidden = NO;
        self.descripLab.text = [NSString stringWithFormat:@"%@到期", pageModel.expireDate];
        self.backCardImageView.image = [UIImage imageNamed:@"bg_header_vipcard"];
    } else {
//        self.recordBtn.hidden = NO;
        self.descripLab.text = @"会员已过期，续费后可享VIP特权";
        self.backCardImageView.image = [UIImage imageNamed:@"bg_header_unvipcard"];
    }
}


//- (void)setRenewModel:(MRKAutoRenewModel *)renewModel{
//    _renewModel = renewModel;
//    if (renewModel.isOpen){
//        [self.recordBtn setTitle:@"续费管理" forState:UIControlStateNormal];
//    }else{
//        self.recordBtn.traceEventId = @"btn_profile_vip_record";
//        [self.recordBtn setTitle:@"开通记录" forState:UIControlStateNormal];
//    }
//}

- (UIImageView *)backGroundImageView{
    if (!_backGroundImageView) {
        _backGroundImageView = [[UIImageView alloc] init];
        _backGroundImageView.contentMode = UIViewContentModeScaleAspectFill;
        _backGroundImageView.autoresizingMask = UIViewAutoresizingFlexibleTopMargin | UIViewAutoresizingFlexibleLeftMargin;
        _backGroundImageView.image = [UIImage imageNamed:@"img_top_background"];
    }
    return _backGroundImageView;
}

- (UIImageView *)backCardImageView{
    if (!_backCardImageView) {
        _backCardImageView = [[UIImageView alloc] init];
        _backCardImageView.contentMode = UIViewContentModeScaleAspectFill;
        _backCardImageView.autoresizingMask = UIViewAutoresizingFlexibleTopMargin | UIViewAutoresizingFlexibleLeftMargin;
        _backCardImageView.image = [UIImage imageNamed:@"bg_header_unvipcard"];
    }
    return _backCardImageView;
}

- (UIImageView *)backBottomImageView{
    if (!_backBottomImageView) {
        _backBottomImageView = [[UIImageView alloc] init];
        _backBottomImageView.contentMode = UIViewContentModeScaleAspectFill;
        _backBottomImageView.autoresizingMask = UIViewAutoresizingFlexibleTopMargin | UIViewAutoresizingFlexibleLeftMargin;
        _backBottomImageView.image = [UIImage imageNamed:@"bg_header_bottom"];
    }
    return _backBottomImageView;
}

- (UIImageView *)stoneImageView{
    if (!_stoneImageView) {
        _stoneImageView = [[UIImageView alloc] init];
        _stoneImageView.contentMode = UIViewContentModeScaleAspectFill;
        _stoneImageView.autoresizingMask = UIViewAutoresizingFlexibleTopMargin | UIViewAutoresizingFlexibleLeftMargin;
        _stoneImageView.image = [UIImage imageNamed:@"bg_header_stone"];
    }
    return _stoneImageView;
}

- (UIImageView *)shadowImageView {
    if (!_shadowImageView) {
        _shadowImageView = [[UIImageView alloc] init];
        _shadowImageView.contentMode = UIViewContentModeScaleAspectFill;
        _shadowImageView.autoresizingMask = UIViewAutoresizingFlexibleTopMargin | UIViewAutoresizingFlexibleLeftMargin;
        _shadowImageView.image = [UIImage imageNamed:@"medal_shadow"];
    }
    return _shadowImageView;
}

- (UIImageView *)avatarImageView{
    if (!_avatarImageView) {
        _avatarImageView = [[UIImageView alloc] init];
        _avatarImageView.contentMode = UIViewContentModeScaleAspectFill;
        _avatarImageView.layer.cornerRadius = DHPX(40)/2;
        _avatarImageView.layer.masksToBounds = YES;
    }
    return _avatarImageView;
}

- (UILabel *)nickNameLab{
    if (!_nickNameLab) {
        _nickNameLab = [[UILabel alloc] init];
        _nickNameLab.font = [UIFont systemFontOfSize:15 weight:UIFontWeightMedium];
        _nickNameLab.textColor = [UIColor colorWithHexString:@"#582A15"];
    }
    return _nickNameLab;
}

- (UILabel *)descripLab{
    if (!_descripLab) {
        _descripLab = [[UILabel alloc] init];
        _descripLab.textColor = [UIColor colorWithHexString:@"#5B2C17"];
        _descripLab.font = [UIFont systemFontOfSize:13];
    }
    return _descripLab;
}

- (UILabel *)vipDescripLab{
    if (!_vipDescripLab) {
        _vipDescripLab = [[UILabel alloc] init];
        _vipDescripLab.textColor = [UIColor colorWithHexString:@"#5F311E"];
        _vipDescripLab.font = [UIFont systemFontOfSize:16 weight:UIFontWeightMedium];
        _vipDescripLab.text = @"享8大特权，甄选课程抢先体验";
    }
    return _vipDescripLab;
}

//- (UIButton *)recordBtn{
//    if (!_recordBtn){
//        _recordBtn = [UIButton buttonWithType:UIButtonTypeCustom];
//        [_recordBtn setTitle:@"开通记录" forState:UIControlStateNormal];
//        [_recordBtn setTitleColor:[UIColor colorWithHexString:@"#5F311E"] forState:UIControlStateNormal];
//        [_recordBtn.titleLabel setFont:[UIFont systemFontOfSize:14]];
//        [_recordBtn addTarget:self action:@selector(recordBtnClick) forControlEvents:UIControlEventTouchUpInside];
//        _recordBtn.layer.cornerRadius = DHPX(28)/2;
//        _recordBtn.layer.borderWidth = 1.0f;
//        _recordBtn.layer.borderColor = [UIColor colorWithHexString:@"#5F311E"].CGColor;
//        _recordBtn.layer.masksToBounds = YES;
//    }
//    return _recordBtn;
//}

//- (void)recordBtnClick{
//    if (self.recordBtnClickBlock){
//        self.recordBtnClickBlock();
//    }
//}

@end



@interface MRKVipBannerView ()<SDCycleScrollViewDelegate>
@property (nonatomic, strong) SDCycleScrollView *cycleScrollView;
@end

@implementation MRKVipBannerView

- (instancetype)init {
    self = [super init];
    if (self) {
        
        [self addSubview:self.cycleScrollView];
    }
    return self;
}

- (void)layoutSubviews{
    [super layoutSubviews];
    
    [self.cycleScrollView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsMake(WKDHPX(16), WKDHPX(16), 0, WKDHPX(16)));
    }];
}

- (void)setDataArray:(NSMutableArray *)dataArray{
    _dataArray = dataArray;
    
    NSMutableArray *imges = [NSMutableArray array];
    for (AdvertModel *model in dataArray) {
        [imges addObject:model.image];
    }
    
    self.cycleScrollView.imageURLStringsGroup = imges;
    self.cycleScrollView.autoScroll = imges.count > 1;
}

- (SDCycleScrollView *)cycleScrollView{
    if (!_cycleScrollView) {
        _cycleScrollView = [SDCycleScrollView cycleScrollViewWithFrame:CGRectZero
                                                              delegate:self
                                                      placeholderImage:[UIImage imageNamed:@"pic_1"]];
        _cycleScrollView.backgroundColor = UIColorRGB_BGColor;
        _cycleScrollView.pageControlAliment = SDCycleScrollViewPageContolAlimentCenter;
        _cycleScrollView.pageControlStyle = SDCycleScrollViewPageContolStyleClassic;
        _cycleScrollView.autoScrollTimeInterval = 3;
        _cycleScrollView.currentPageDotColor = [UIColor whiteColor];
        _cycleScrollView.pageDotColor = [UIColor colorWithHexString:@"#ffffff" alpha:0.5];
        _cycleScrollView.layer.masksToBounds = YES;
        _cycleScrollView.layer.cornerRadius = 8;
    }
    return _cycleScrollView;
}

- (void)cycleScrollView:(SDCycleScrollView *)cycleScrollView didSelectItemAtIndex:(NSInteger)index {
    
    AdvertModel *model = self.dataArray[index];
    if (self.bannerSelectBlock) {
        self.bannerSelectBlock(model);
    }
}

@end












@interface MRKVipCardProtocolView()
@property (nonatomic, strong) UILabel *autoRenewalTitle;
@property (nonatomic, strong) YYLabel *autoRenewalAbout;

@property (nonatomic, strong) MRKVipProtrolButton *userProtocol;
@property (nonatomic, strong) MRKVipProtrolButton *privacyPolicy;
@property (nonatomic, strong) MRKVipProtrolButton *automaticRenewal;
@end


@implementation MRKVipCardProtocolView
- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self addSubview:self.autoRenewalTitle];
        [self addSubview:self.autoRenewalAbout];
        
        [self addSubview:self.userProtocol];
        [self addSubview:self.privacyPolicy];
        [self addSubview:self.automaticRenewal];
        [self addAllSubviews];
    }
    return self;
}

- (void)addAllSubviews{
    [self.autoRenewalTitle mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.mas_top).offset(0);
        make.left.equalTo(self.mas_left).offset(16);
        make.width.mas_equalTo(150);
        make.height.mas_equalTo(24);
    }];
    [self.autoRenewalAbout mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.autoRenewalTitle.mas_bottom).offset(DHPX(12));
        make.left.equalTo(self.mas_left).offset(16);
        make.width.mas_equalTo(kScreenWidth -16*2);
    }];
    
    [self.userProtocol mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.autoRenewalAbout.mas_bottom).offset(DHPX(32));
        make.left.equalTo(self.mas_left).offset(16);
        make.height.mas_equalTo(DHPX(45));
        make.width.mas_equalTo(MainWidth-16*2);
    }];
    [self.privacyPolicy mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.userProtocol.mas_bottom).offset(DHPX(8));
        make.left.equalTo(self.mas_left).offset(16);
        make.height.mas_equalTo(DHPX(45));
        make.width.mas_equalTo(MainWidth-16*2);
    }];
    [self.automaticRenewal mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.privacyPolicy.mas_bottom).offset(DHPX(8));
        make.left.equalTo(self.mas_left).offset(16);
        make.height.mas_equalTo(DHPX(45));
        make.width.mas_equalTo(MainWidth-16*2);
        make.bottom.equalTo(@-30);
    }];
}

- (UILabel *)autoRenewalTitle{
    if (!_autoRenewalTitle) {
        UILabel *label = [[UILabel alloc]init];
        label.textColor = [UIColor colorWithHexString:@"#333333"];
        label.text = @"自动续费说明";
        label.font = [UIFont fontWithName:@"DINAlternate-Bold" size:17];
        _autoRenewalTitle = label;
    }
    return _autoRenewalTitle;
}

- (YYLabel *)autoRenewalAbout{
    if (!_autoRenewalAbout) {
        YYLabel *label = [[YYLabel alloc] init];
        label.textAlignment = NSTextAlignmentLeft;
        label.textVerticalAlignment = YYTextVerticalAlignmentTop;
        label.numberOfLines = 0;
        label.left = DHPX(16);
        label.width = kScreenWidth - DHPX(16)*2;
        label.displaysAsynchronously = YES;
        label.clearContentsBeforeAsynchronouslyDisplay = NO;
        label.preferredMaxLayoutWidth =  kScreenWidth - DHPX(16)*2;
        label.attributedText = self.explainAttributedStr;
        label.numberOfLines = 0;
        _autoRenewalAbout = label;
    }
    return _autoRenewalAbout;
}

- (NSMutableAttributedString *)explainAttributedStr {
    
    NSMutableAttributedString *attributeText = [[NSMutableAttributedString alloc] initWithString:@"续订："];
    attributeText.color = UIColorHex(#333333);
    attributeText.font = [UIFont systemFontOfSize:13];
    
    NSMutableAttributedString *text1 = [[NSMutableAttributedString alloc] initWithString:@"自动续费商品包括“连续包月/连续包季/连续包年“，您确定购买后，会在您的会员到期前24小时通过您签约的支付方式自动发起续费扣款。扣费成功后，您的会员有效期自动延长一个周期。\n\n"];
    text1.color = UIColorHex(#848A9B);
    [attributeText appendAttributedString:text1];
    
    NSMutableAttributedString *text2 = [[NSMutableAttributedString alloc] initWithString:@"终止方法："];
    text2.color = UIColorHex(#333333);
    [attributeText appendAttributedString:text2];
    
    NSMutableAttributedString *text3 = [[NSMutableAttributedString alloc] initWithString:@"苹果设备：“设置”>进入“iTunes Store/App Store” >点击“Apple ID”，在管理订阅中关闭自动续费的功能。\n\n"];
    text3.color = UIColorHex(#848A9B);
    [attributeText appendAttributedString:text3];
    
    NSMutableAttributedString *text4 = [[NSMutableAttributedString alloc] initWithString:@"温馨提示："];
    text4.color = UIColorHex(#333333);
    [attributeText appendAttributedString:text4];
    
    NSMutableAttributedString *text5 = [[NSMutableAttributedString alloc] initWithString:@"如未在会员到期前至少24小时关闭自动续费期，此服务将会自动续订。\n\n"];
    text5.color = UIColorHex(#848A9B);
    [attributeText appendAttributedString:text5];
    
    NSMutableAttributedString *text6 = [[NSMutableAttributedString alloc] initWithString:@"注："];
    text6.color = UIColorHex(#333333);
    [attributeText appendAttributedString:text6];
    
    NSMutableAttributedString *text7 = [[NSMutableAttributedString alloc] initWithString:@"虚拟商品购买后无法退换，敬请谅解！有任何疑问可点击此处\n"];
    text7.color = UIColorHex(#848A9B);
    [attributeText appendAttributedString:text7];
    
    @weakify(self);
    NSMutableAttributedString *text8 = [[NSMutableAttributedString alloc] initWithString:@"联系客服"];
    text8.color = UIColorHex(#848A9B);
    [text8 setTextHighlightRange:[text8.string rangeOfString:@"联系客服"]
                          color:[UIColor colorWithHexString:@"#1791E3"]
                backgroundColor:[UIColor clearColor]
                      tapAction:^(UIView * _Nonnull containerView, NSAttributedString * _Nonnull text, NSRange range, CGRect rect) {
        @strongify(self);
        if (self.customerServiceBlock){
            self.customerServiceBlock();
        }
    }];
    [attributeText appendAttributedString:text8];
    
    
    return attributeText;
}

- (MRKVipProtrolButton *)userProtocol{
    if (!_userProtocol) {
        _userProtocol = [[MRKVipProtrolButton alloc] init];
        _userProtocol.backgroundColor = UIColorHex(#F8F8FA);
        _userProtocol.btnTitle = @"用户协议";
        [_userProtocol addTarget:self action:@selector(protrolBtnClick:) forControlEvents:UIControlEventTouchUpInside];
        _userProtocol.tag = 3000;
    }
    return _userProtocol;
}

- (MRKVipProtrolButton *)privacyPolicy{
    if (!_privacyPolicy) {
        _privacyPolicy = [[MRKVipProtrolButton alloc] init];
        _privacyPolicy.backgroundColor = UIColorHex(#F8F8FA);
        _privacyPolicy.btnTitle = @"隐私政策";
        [_privacyPolicy addTarget:self action:@selector(protrolBtnClick:) forControlEvents:UIControlEventTouchUpInside];
        _privacyPolicy.tag = 4000;
    }
    return _privacyPolicy;
}

- (MRKVipProtrolButton *)automaticRenewal{
    if (!_automaticRenewal) {
        _automaticRenewal = [[MRKVipProtrolButton alloc] init];
        _automaticRenewal.backgroundColor = UIColorHex(#F8F8FA);
        _automaticRenewal.btnTitle = @"自动续费服务协议";
        [_automaticRenewal addTarget:self action:@selector(protrolBtnClick:) forControlEvents:UIControlEventTouchUpInside];
        _automaticRenewal.tag = 5000;
    }
    return _automaticRenewal;
}

- (void)protrolBtnClick:(MRKVipProtrolButton *)sender {
    NSInteger tag = sender.tag;
    
    switch (tag) {
        case 3000:
        {
            sender.traceEventId = @"btn_profile_vip_services_user";
            if (self.userProtocolClickBlock){
                self.userProtocolClickBlock();
            }
        }
            break;
        case 4000:
        {
            sender.traceEventId = @"btn_profile_vip_privacy";
            if (self.privacyPolicyClickBlock){
                self.privacyPolicyClickBlock();
            }
        }
            break;
        case 5000:
            if (self.vipProtocolClickBlock){
                self.vipProtocolClickBlock();
            }
            break;
        default:
            break;
    }
}

/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/

@end



@interface MRKVipProtrolButton()
@property (nonatomic, strong) UILabel *titleLab;
@property (nonatomic, strong) UIImageView *partView;
@end


@implementation MRKVipProtrolButton
- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        self.layer.cornerRadius = 8.0f;
        self.layer.masksToBounds = YES;
        
        [self addSubview:self.titleLab];
        [self addSubview:self.partView];
    }
    return self;
}

- (void)layoutSubviews{
    [super layoutSubviews];
    
    [self.titleLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_equalTo(self.mas_centerY);
        make.left.mas_equalTo(DHPX(10));
        make.size.mas_equalTo(CGSizeMake(DHPX(120), DHPX(20)));
    }];
    
    [self.partView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_equalTo(self.mas_right).offset(-DHPX(10));
        make.centerY.mas_equalTo(self.mas_centerY);
        make.size.mas_equalTo(CGSizeMake(15, 20));
    }];
}

- (void)setBtnTitle:(NSString *)btnTitle{
    _btnTitle = btnTitle;
    self.titleLab.text = btnTitle;
}

- (UIImageView *)partView{
    if (!_partView) {
        _partView = [[UIImageView alloc] init];
        _partView.contentMode = UIViewContentModeScaleAspectFill;
        _partView.image = [UIImage imageNamed:@"icon_arrow_16pt"];
        _partView.tintColor = [UIColor colorWithHexString:@"#333333"];
    }
    return _partView;
}

- (UILabel *)titleLab{
    if (!_titleLab) {
        _titleLab = [[UILabel alloc] init];
        _titleLab.font = [UIFont systemFontOfSize:14 weight:UIFontWeightMedium];
        _titleLab.textColor = [UIColor colorWithHexString:@"#333333"];
        _titleLab.textAlignment = 0;
    }
    return _titleLab;
}
@end







@interface MRKVipCardView()
@property (nonatomic, strong) UIImageView *backImageView;
@property (nonatomic, strong) UILabel *titleLab;
@property (nonatomic, strong) UILabel *priceLab;
@property (nonatomic, strong) UILabel *originalPriceLab;
@property (nonatomic, strong) UILabel *averageDailyLab;
@property (nonatomic, strong) UIImageView *tagImageView;
@property (nonatomic, strong) UIButton *updateBtn;
@end

@implementation MRKVipCardView

- (UIView *)hitTest:(CGPoint)point withEvent:(UIEvent *)event {
    UIView *hitView = [super hitTest:point withEvent:event];
    if (hitView == self.backImageView) {
        return self;
    }
    if (hitView == self.updateBtn) {
        return self.updateBtn;
    }
    return hitView;
}


- (instancetype)init {
    self = [super init];
    if (self) {
        self.clipsToBounds = NO;
        [self addSubview:self.backImageView];
        [self.backImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.mas_equalTo(UIEdgeInsetsZero);
        }];
        
        [self.backImageView addSubview:self.priceLab];
        [self.backImageView addSubview:self.titleLab];
        [self.backImageView addSubview:self.originalPriceLab];
        [self.backImageView addSubview:self.averageDailyLab];
        [self.backImageView addSubview:self.updateBtn];
        [self.backImageView addSubview:self.tagImageView];
        
        [self.priceLab mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(self.backImageView.mas_centerY);
            make.centerX.equalTo(self.backImageView.mas_centerX);
            make.height.mas_equalTo(WKDHPX(48));
        }];
        
        [self.titleLab mas_makeConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(self.priceLab.mas_top).offset(WKDHPX(4));
            make.left.equalTo(self.backImageView.mas_left).offset(WKDHPX(8));
            make.right.equalTo(self.backImageView.mas_right).offset(-WKDHPX(8));
            make.height.mas_equalTo(WKDHPX(18));
        }];

        [self.originalPriceLab mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.priceLab.mas_bottom);
            make.left.equalTo(self.backImageView.mas_left).offset(WKDHPX(8));
            make.right.equalTo(self.backImageView.mas_right).offset(-WKDHPX(8));
            make.height.mas_equalTo(WKDHPX(22));
        }];
        
        [self.averageDailyLab mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.originalPriceLab.mas_bottom).offset(WKDHPX(2));
            make.left.equalTo(self.backImageView.mas_left).offset(WKDHPX(8));
            make.right.equalTo(self.backImageView.mas_right).offset(-WKDHPX(8));
            make.height.mas_equalTo(WKDHPX(13));
        }];
        
        [self.updateBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(self.backImageView.mas_bottom).offset(-WKDHPX(4));
            make.centerX.equalTo(self.backImageView.mas_centerX);
            make.size.mas_equalTo(CGSizeMake(WKDHPX(80), WKDHPX(22)));
        }];
        
        [self.tagImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.backImageView.mas_top).offset(0);
            make.left.equalTo(self.backImageView.mas_left).offset(0);
            make.size.mas_equalTo(CGSizeMake(WKDHPX(77), WKDHPX(18)));
//            make.height.mas_equalTo(WKDHPX(18));
        }];
    }
    return self;
}

- (void)layoutSubviews{
    [super layoutSubviews];

}

- (UIImageView *)backImageView{
    if (!_backImageView) {
        _backImageView = [[UIImageView alloc] init];
        _backImageView.contentMode = UIViewContentModeScaleAspectFit;
        _backImageView.autoresizingMask = UIViewAutoresizingFlexibleTopMargin | UIViewAutoresizingFlexibleLeftMargin;
        _backImageView.image = [UIImage imageNamed:@"bg_unselect_card"];
        _backImageView.userInteractionEnabled = YES;
    }
    return _backImageView;
}

- (UIImageView *)tagImageView{
    if (!_tagImageView) {
        _tagImageView = [[UIImageView alloc] init];
        _tagImageView.contentMode = UIViewContentModeScaleAspectFit;
        _tagImageView.autoresizingMask = UIViewAutoresizingFlexibleTopMargin | UIViewAutoresizingFlexibleLeftMargin;
    }
    return _tagImageView;
}

- (UILabel *)titleLab{
    if (!_titleLab) {
        _titleLab = [[UILabel alloc] init];
        _titleLab.textAlignment = NSTextAlignmentCenter;
        _titleLab.font = kMedium_Font_NoDHPX(WKDHPX(15));
        _titleLab.textColor = [UIColor colorWithHexString:@"#363A44"];
    }
    return _titleLab;
}

- (UILabel *)averageDailyLab{
    if (!_averageDailyLab) {
        _averageDailyLab = [[UILabel alloc] init];
        _averageDailyLab.textAlignment = NSTextAlignmentCenter;
        _averageDailyLab.font = kSystem_Font_NoDHPX(WKDHPX(11));
        _averageDailyLab.textColor = [UIColor colorWithHexString:@"#848A9B"];
    }
    return _averageDailyLab;
}

- (UILabel *)priceLab{
    if (!_priceLab) {
        _priceLab = [[UILabel alloc] init];
        _priceLab.textAlignment = NSTextAlignmentCenter;
        _priceLab.font = BebasFont_Bold_NoDHPX(WKDHPX(18));
        _priceLab.textColor = [UIColor colorWithHexString:@"#363A44"];
    }
    return _priceLab;
}

- (UILabel *)originalPriceLab{
    if (!_originalPriceLab) {
        _originalPriceLab = [[UILabel alloc] init];
        _originalPriceLab.textAlignment = NSTextAlignmentCenter;
        _originalPriceLab.textColor = [UIColor colorWithHexString:@"#848A9B"];
    }
    return _originalPriceLab;
}

- (UIButton *)updateBtn{
    if (!_updateBtn) {
        _updateBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        _updateBtn.titleLabel.font = kSystem_Font_NoDHPX(WKDHPX(10));
        [_updateBtn setTitle:@"选择升级时长" forState:UIControlStateNormal];
        [_updateBtn setTitleColor:[UIColor colorWithHexString:@"#A05700"] forState:UIControlStateNormal];
        [_updateBtn addTarget:self action:@selector(detailAction:) forControlEvents:UIControlEventTouchUpInside];
        _updateBtn.layer.cornerRadius = WKDHPX(22)/2;
        _updateBtn.layer.masksToBounds = YES;
        _updateBtn.layer.borderColor = [UIColor colorWithHexString:@"#A05700"].CGColor;
        _updateBtn.layer.borderWidth = CGFloatFromPixel(1.0);
    }
    return _updateBtn;
}

- (void)detailAction:(id)sender {
    if (self.vipProductChangeBlock) {
        self.vipProductChangeBlock();
    }
}

- (void)setModel:(MRKVipCardModel *)model{
    _model = model;
    
    self.titleLab.text = model.name;
    self.priceLab.attributedText = ({
        NSString *price = model.actualAmount?:@"";
        if (model.isAuto && [model.firstAmount isNotBlank]){
            price = model.firstAmount;
        }
        
        NSMutableAttributedString *str = [[NSMutableAttributedString alloc] initWithString:@"¥"];
        str.font = BebasFont_Bold_NoDHPX(WKDHPX(18));
        NSMutableAttributedString *priceStr = [[NSMutableAttributedString alloc] initWithString:price];
        priceStr.font = BebasFont_Bold_NoDHPX(WKDHPX(32));
        [str appendAttributedString:priceStr];
        str;
    });
    
    ///
    if ([model.showAmount isNotBlank] && ![model.showAmount isEqualToString:model.actualAmount]) {
        self.originalPriceLab.attributedText = ({
            NSString *str = [NSString stringWithFormat:@"¥%@", model.showAmount];
            NSMutableAttributedString *priceStr = [[NSMutableAttributedString alloc] initWithString:str];
            priceStr.font = kSystem_Font_NoDHPX(WKDHPX(13));
            priceStr.strikethroughStyle = NSUnderlineStyleSingle;
            priceStr;
        });
    }
    
    self.tagImageView.image = nil;
    if ([model.labelIcon isNotBlank]) {
        [self.tagImageView setImageURL:[NSURL URLWithString:model.labelIcon]];
    }
    
    self.averageDailyLab.text = [NSString stringWithFormat:@"每日仅需¥%@", model.dailyStartPrice];
    self.averageDailyLab.hidden = model.configType.intValue == 2; // 显示升级按钮就不显示每日仅需
    
    ///显示升级按钮
    self.updateBtn.hidden = !(model.configType.intValue == 2);
}


- (void)setShadowStatus:(BOOL)shadowStatus{
    _shadowStatus = shadowStatus;
    
    if (shadowStatus){
        self.titleLab.textColor = [UIColor colorWithHexString:@"#A05700"];
        self.priceLab.textColor = [UIColor colorWithHexString:@"#804E13"];
        self.backImageView.image = [UIImage imageNamed:@"bg_select_card"];
        self.averageDailyLab.textColor = [UIColor colorWithHexString:@"#804E13"];
//        jxt_getSafeMainQueue(^{
//            [self addShadowToView:self.backImageView withColor: [UIColor colorWithHexString:@"#FADDB8"]];
//        });

    } else {
        self.titleLab.textColor = [UIColor colorWithHexString:@"#363A44"];
        self.priceLab.textColor = [UIColor colorWithHexString:@"#363A44"];
        self.backImageView.image = [UIImage imageNamed:@"bg_unselect_card"];
        self.averageDailyLab.textColor = [UIColor colorWithHexString:@"#848A9B"];
//        jxt_getSafeMainQueue(^{
//            [self removeShadowToView:self.backImageView];
//        });
    }
}

- (void)removeShadowToView:(UIView *)theView{
    theView.layer.shadowColor = UIColor.clearColor.CGColor;
    theView.layer.shadowOpacity = 0.5;
    theView.layer.shadowOffset = CGSizeZero;
    theView.layer.shadowRadius = 5;

    theView.layer.shadowPath = nil;
}

- (void)addShadowToView:(UIView *)theView withColor:(UIColor *)theColor {
    theView.layer.shadowColor = theColor.CGColor;
    theView.layer.shadowOpacity = 0.5;
    theView.layer.shadowOffset = CGSizeZero;
    theView.layer.shadowRadius = 5;

    float shadowPathWidth = theView.layer.shadowRadius;
    float cellWidth = WKDHPX(109);
    float cellHeight = WKDHPX(138);
    CGRect shadowRect = CGRectMake(0, cellHeight -shadowPathWidth/2, cellWidth, shadowPathWidth);

    UIBezierPath *path = [UIBezierPath bezierPathWithRect:shadowRect];
    theView.layer.shadowPath = path.CGPath;
}

/*
 // Only override drawRect: if you perform custom drawing.
 // An empty implementation adversely affects performance during animation.
 - (void)drawRect:(CGRect)rect {
 // Drawing code
 }
 */

@end



@interface MRKVipPurchaseBottomView()
@property (nonatomic, strong) UIButton *subscribeBtn;
@property (nonatomic, strong) UIButton *agreementBtn;
@property (nonatomic, strong) YYLabel *protocolLab;
@end

@implementation MRKVipPurchaseBottomView

- (instancetype)init {
    self = [super init];
    if (self) {
        
        [self addSubview:self.subscribeBtn];
        [self addSubview:self.protocolLab];
        [self addSubview:self.agreementBtn];
        
        [self.subscribeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.mas_top).offset(WKDHPX(20));
            make.left.equalTo(self.mas_left).offset(WKDHPX(16));
            make.right.equalTo(self.mas_right).offset(-WKDHPX(16));
            make.height.mas_equalTo(WKDHPX(48));
        }];
        [self.protocolLab mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.subscribeBtn.mas_bottom).offset(WKDHPX(12));
            make.centerX.equalTo(self.mas_centerX).offset(10);
        }];
        [self.agreementBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(self.protocolLab.mas_left).offset(0);
            make.centerY.equalTo(self.protocolLab.mas_centerY);
            make.size.mas_equalTo(CGSizeMake(30, 30));
        }];
    }
    return self;
}

- (void)layoutSubviews{
    [super layoutSubviews];
}

- (void)setTipPurchaseStr:(NSAttributedString *)tipPurchaseStr{
    _tipPurchaseStr = tipPurchaseStr;
    [self.subscribeBtn setAttributedTitle:tipPurchaseStr forState:UIControlStateNormal];
}

- (YYLabel *)protocolLab{
    if (!_protocolLab){
        _protocolLab = [[YYLabel alloc] init];
        _protocolLab.textVerticalAlignment = YYTextVerticalAlignmentCenter;
        _protocolLab.attributedText = ({
            NSMutableAttributedString *protrolText = [[NSMutableAttributedString alloc] initWithString:@"我已阅读并同意"];
            protrolText.color = [UIColor colorWithHexString:@"#B3B5B9"];
            protrolText.font = [UIFont systemFontOfSize:WKDHPX(13)];
            
            NSMutableAttributedString *text = [[NSMutableAttributedString alloc] initWithString:@" 会员服务协议 "];
            text.font = [UIFont systemFontOfSize:WKDHPX(13)];
            text.color = UIColorHex(#202121);
            text.underlineStyle = NSUnderlineStyleSingle;
            [text setTextHighlightRange:text.rangeOfAll
                                  color:UIColorHex(#202121)
                        backgroundColor:[UIColor clearColor]
                              tapAction:^(UIView * _Nonnull containerView, NSAttributedString * _Nonnull text, NSRange range, CGRect rect) {
                
            }];
            [protrolText appendAttributedString:text];
            
            NSMutableAttributedString *andText= [[NSMutableAttributedString alloc] initWithString:@"和"];
            andText.color = [UIColor colorWithHexString:@"#B3B5B9"];
            andText.font = [UIFont systemFontOfSize:WKDHPX(13)];
            [protrolText appendAttributedString:andText];
            
            NSMutableAttributedString *renewText = [[NSMutableAttributedString alloc] initWithString:@" 自动续费服务协议 "];
            renewText.font = [UIFont systemFontOfSize:WKDHPX(13)];
            renewText.color = UIColorHex(#202121);
            renewText.underlineStyle = NSUnderlineStyleSingle;
            [renewText setTextHighlightRange:renewText.rangeOfAll
                                       color:UIColorHex(#202121)
                             backgroundColor:[UIColor clearColor]
                                   tapAction:^(UIView * _Nonnull containerView, NSAttributedString * _Nonnull text, NSRange range, CGRect rect) {
                
            }];
            [protrolText appendAttributedString:renewText];
            protrolText;
        });
        _protocolLab.textAlignment = NSTextAlignmentLeft;
    }
    return _protocolLab;
}

- (UIButton *)agreementBtn {
    if (!_agreementBtn) {
        _agreementBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_agreementBtn setImage:[UIImage imageNamed:@"protocol_selected"] forState:UIControlStateSelected];
        [_agreementBtn setImage:[UIImage imageNamed:@"protocol_unSelected"] forState:UIControlStateNormal];
        [_agreementBtn addTarget:self action:@selector(agreeButton:) forControlEvents:UIControlEventTouchUpInside];
        _agreementBtn.contentMode = UIViewContentModeCenter;
    }
    return _agreementBtn;
}

- (UIButton *)subscribeBtn {
    if (!_subscribeBtn){
        _subscribeBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        _subscribeBtn.backgroundColor = UIColorHex(#202121);
        [_subscribeBtn setTitleColor:UIColorHex(#FFEEDA) forState:UIControlStateNormal];
        _subscribeBtn.titleLabel.font = [UIFont systemFontOfSize:16 weight:UIFontWeightMedium];
        [_subscribeBtn addTarget:self action:@selector(aiPlanDescripAction:) forControlEvents:UIControlEventTouchUpInside];
        _subscribeBtn.layer.cornerRadius = WKDHPX(48)/2;
        _subscribeBtn.layer.masksToBounds = YES;
    }
    return _subscribeBtn;
}

- (void)agreeButton:(UIButton *)sender{
    sender.selected = !sender.selected;
}

- (void)aiPlanDescripAction:(id)sender{
    if (self.subscribeBlock){
        self.subscribeBlock();
    }
}


/*
 // Only override drawRect: if you perform custom drawing.
 // An empty implementation adversely affects performance during animation.
 - (void)drawRect:(CGRect)rect {
 // Drawing code
 }
 */

@end







@interface MRKPurchaseRightsItemView()
@property (nonatomic, strong) UIImageView *iconImageView;
@property (nonatomic, strong) UILabel *titleLab;
@end

@implementation MRKPurchaseRightsItemView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        
        [self addSubview:self.iconImageView];
        [self.iconImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.mas_top).offset(0);
            make.centerX.equalTo(self.mas_centerX);
            make.size.mas_equalTo(CGSizeMake(WKDHPX(40), WKDHPX(40)));
        }];
        
        [self addSubview:self.titleLab];
        [self.titleLab mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.iconImageView.mas_bottom).offset(WKDHPX(6));
            make.left.equalTo(self.mas_left);
            make.right.equalTo(self.mas_right);
            make.bottom.equalTo(self.mas_bottom);
        }];
    }
    return self;
}

- (void)layoutSubviews{
    [super layoutSubviews];

}

- (UIImageView *)iconImageView{
    if (!_iconImageView) {
        _iconImageView = [[UIImageView alloc] init];
        _iconImageView.contentMode = UIViewContentModeScaleAspectFit;
        _iconImageView.autoresizingMask = UIViewAutoresizingFlexibleTopMargin | UIViewAutoresizingFlexibleWidth;
        _iconImageView.userInteractionEnabled = YES;
        _iconImageView.sd_imageTransition = SDWebImageTransition.fadeTransition;
    }
    return _iconImageView;
}

- (UILabel *)titleLab{
    if (!_titleLab) {
        _titleLab = [[UILabel alloc] init];
        _titleLab.textAlignment = NSTextAlignmentCenter;
        _titleLab.font = kSystem_Font_NoDHPX(WKDHPX(12));
        _titleLab.textColor = UIColor.whiteColor;
    }
    return _titleLab;
}

- (void)setModel:(MRKVipExclusiveItemModel *)model{
    _model = model;
    
    self.titleLab.text = model.name;
    [self.iconImageView  sd_setImageWithURL:[NSURL URLWithString:model.icon]];
}
/*
 // Only override drawRect: if you perform custom drawing.
 // An empty implementation adversely affects performance during animation.
 - (void)drawRect:(CGRect)rect {
 // Drawing code
 }
 */

@end
