//
//  MRKSubscribeCardView.m
//  Student_IOS
//
//  Created by <PERSON>q on 2025/8/4.
//

#import "MRKSubscribeCardView.h"

@interface MRKSubscribeCardView()
@property (nonatomic, strong) UILabel *titleLab;
@property (nonatomic, strong) UILabel *priceLab;
@property (nonatomic, strong) UILabel *originalPriceLab;
@property (nonatomic, strong) UILabel *averageDailyLab;
@property (nonatomic, strong) UIImageView *tagImageView;
@end

@implementation MRKSubscribeCardView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        
        
        [self addSubview:self.titleLab];
        [self addSubview:self.priceLab];
        [self addSubview:self.originalPriceLab];
        [self addSubview:self.averageDailyLab];
        [self addSubview:self.tagImageView];
        
        [self.titleLab mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.mas_left).offset(WKDHPX(8));
            make.top.equalTo(self.mas_top).offset(WKDHPX(4));
            make.right.equalTo(self.mas_right).offset(-WKDHPX(58));
            make.height.mas_equalTo(WKDHPX(18));
        }];
        
        [self.priceLab mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(self.mas_centerY);
            make.left.equalTo(self.mas_left).offset(WKDHPX(8));
            make.height.mas_equalTo(WKDHPX(48));
        }];
        
        [self.originalPriceLab mas_makeConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(self.priceLab.mas_bottom);
            make.left.equalTo(self.priceLab.mas_right).offset(WKDHPX(4));
            make.height.mas_equalTo(WKDHPX(22));
        }];
        
        [self.averageDailyLab mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.originalPriceLab.mas_bottom).offset(WKDHPX(2));
            make.left.equalTo(self.mas_left).offset(WKDHPX(8));
            make.right.equalTo(self.mas_right).offset(-WKDHPX(8));
            make.height.mas_equalTo(WKDHPX(13));
        }];

        [self.tagImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.mas_top).offset(0);
            make.right.equalTo(self.mas_right).offset(0);
            make.size.mas_equalTo(CGSizeMake(WKDHPX(77), WKDHPX(18)));
        }];
    }
    return self;
}

- (instancetype)init {
    self = [super init];
    if (self) {
   
   
    
    }
    return self;
}

- (void)layoutSubviews{
    [super layoutSubviews];

}



- (UILabel *)titleLab{
    if (!_titleLab) {
        _titleLab = [[UILabel alloc] init];
        _titleLab.textAlignment = NSTextAlignmentCenter;
        _titleLab.font = kMedium_Font_NoDHPX(WKDHPX(15));
        _titleLab.textColor = [UIColor colorWithHexString:@"#363A44"];
    }
    return _titleLab;
}

- (UIImageView *)tagImageView{
    if (!_tagImageView) {
        _tagImageView = [[UIImageView alloc] init];
        _tagImageView.contentMode = UIViewContentModeScaleAspectFit;
        _tagImageView.autoresizingMask = UIViewAutoresizingFlexibleTopMargin | UIViewAutoresizingFlexibleLeftMargin;
    }
    return _tagImageView;
}

- (UILabel *)averageDailyLab{
    if (!_averageDailyLab) {
        _averageDailyLab = [[UILabel alloc] init];
        _averageDailyLab.textAlignment = NSTextAlignmentCenter;
        _averageDailyLab.font = kSystem_Font_NoDHPX(WKDHPX(11));
        _averageDailyLab.textColor = [UIColor colorWithHexString:@"#848A9B"];
    }
    return _averageDailyLab;
}

- (UILabel *)priceLab{
    if (!_priceLab) {
        _priceLab = [[UILabel alloc] init];
        _priceLab.textAlignment = NSTextAlignmentCenter;
        _priceLab.font = BebasFont_Bold_NoDHPX(WKDHPX(18));
        _priceLab.textColor = [UIColor colorWithHexString:@"#363A44"];
    }
    return _priceLab;
}

- (UILabel *)originalPriceLab{
    if (!_originalPriceLab) {
        _originalPriceLab = [[UILabel alloc] init];
        _originalPriceLab.textAlignment = NSTextAlignmentCenter;
        _originalPriceLab.textColor = [UIColor colorWithHexString:@"#848A9B"];
    }
    return _originalPriceLab;
}



- (void)setModel:(MRKVipCardModel *)model{
    _model = model;
    
    self.titleLab.text = model.name;
    self.priceLab.attributedText = ({
        NSString *price = model.actualAmount?:@"";
        if (model.isAuto && [model.firstAmount isNotBlank]){
            price = model.firstAmount;
        }
        
        NSMutableAttributedString *str = [[NSMutableAttributedString alloc] initWithString:@"¥"];
        str.font = BebasFont_Bold_NoDHPX(WKDHPX(18));
        NSMutableAttributedString *priceStr = [[NSMutableAttributedString alloc] initWithString:price];
        priceStr.font = BebasFont_Bold_NoDHPX(WKDHPX(32));
        [str appendAttributedString:priceStr];
        str;
    });
    
    ///
    if ([model.showAmount isNotBlank] && ![model.showAmount isEqualToString:model.actualAmount]) {
        self.originalPriceLab.attributedText = ({
            NSString *str = [NSString stringWithFormat:@"¥%@", model.showAmount];
            NSMutableAttributedString *priceStr = [[NSMutableAttributedString alloc] initWithString:str];
            priceStr.font = kSystem_Font_NoDHPX(WKDHPX(13));
            priceStr.strikethroughStyle = NSUnderlineStyleSingle;
            priceStr;
        });
    }
    
    self.tagImageView.image = nil;
    if ([model.labelIcon isNotBlank]) {
        [self.tagImageView setImageURL:[NSURL URLWithString:model.labelIcon]];
    }
    
    self.averageDailyLab.text = [NSString stringWithFormat:@"每日仅需¥%@", model.dailyStartPrice];
    self.averageDailyLab.hidden = model.configType.intValue == 2; // 显示升级按钮就不显示每日仅需
    

}


- (void)setShadowStatus:(BOOL)shadowStatus{
    _shadowStatus = shadowStatus;
    
    if (shadowStatus){
        self.titleLab.textColor = [UIColor colorWithHexString:@"#A05700"];
        self.priceLab.textColor = [UIColor colorWithHexString:@"#804E13"];
    
        self.averageDailyLab.textColor = [UIColor colorWithHexString:@"#804E13"];
    } else {
        self.titleLab.textColor = [UIColor colorWithHexString:@"#363A44"];
        self.priceLab.textColor = [UIColor colorWithHexString:@"#363A44"];
 
        self.averageDailyLab.textColor = [UIColor colorWithHexString:@"#848A9B"];
    }
}

- (void)removeShadowToView:(UIView *)theView{
    theView.layer.shadowColor = UIColor.clearColor.CGColor;
    theView.layer.shadowOpacity = 0.5;
    theView.layer.shadowOffset = CGSizeZero;
    theView.layer.shadowRadius = 5;

    theView.layer.shadowPath = nil;
}

- (void)addShadowToView:(UIView *)theView withColor:(UIColor *)theColor {
    theView.layer.shadowColor = theColor.CGColor;
    theView.layer.shadowOpacity = 0.5;
    theView.layer.shadowOffset = CGSizeZero;
    theView.layer.shadowRadius = 5;

    float shadowPathWidth = theView.layer.shadowRadius;
    float cellWidth = WKDHPX(109);
    float cellHeight = WKDHPX(138);
    CGRect shadowRect = CGRectMake(0, cellHeight -shadowPathWidth/2, cellWidth, shadowPathWidth);

    UIBezierPath *path = [UIBezierPath bezierPathWithRect:shadowRect];
    theView.layer.shadowPath = path.CGPath;
}

/*
 // Only override drawRect: if you perform custom drawing.
 // An empty implementation adversely affects performance during animation.
 - (void)drawRect:(CGRect)rect {
 // Drawing code
 }
 */

@end
