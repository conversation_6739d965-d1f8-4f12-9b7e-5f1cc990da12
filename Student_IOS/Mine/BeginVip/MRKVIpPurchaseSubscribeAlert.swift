//
//  MRKVIpPhuseSubscribeAlert.swift
//  Student_IOS
//
//  Created by <PERSON><PERSON> on 2025/8/11.
//

import UIKit
import SnapKit


/// 会员订阅弹窗
@objc
class MRKVIpPurchaseSubscribeAlert: MRKSwiftAlertView {
    
   // MARK: - UI属性（懒加载）
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.textAlignment = .left
        label.textColor = .black
        label.font = UIFont.systemFont(ofSize: 22, weight: .semibold)
        label.text = "选择升级SVIP时长"
        return label
    }()
    
    private lazy var descripLabel: UILabel = {
        let label = UILabel()
        label.textAlignment = .left
        label.textColor = UIColor(hexString: "#BF7C08")
        label.font = UIFont.systemFont(ofSize: 12)
        label.numberOfLines = 0
        label.text = "今日 AI 问询次数已用完，开通会员享更多次数"
        return label
    }()
    
    private lazy var cancelButton: UIButton = {
        let btn = UIButton(type: .custom)
        btn.setImage(UIImage(named: "icon_close_o")?.withRenderingMode(.alwaysTemplate), for: .normal)
        btn.imageView?.tintColor = UIColor(hexString: "A99481")
        btn.addTarget(self, action: #selector(cancelBtnClick), for: .touchUpInside)
        return btn
    }()

   private lazy var backgroundView: MRKSubscribeBackground = {
        let imgView = MRKSubscribeBackground()
        imgView.backgroundColor = .clear
        return imgView
    }()
    
    private lazy var purchaseView: MRKPurchaseView = {
        let view = MRKPurchaseView(frame: .zero, viewType: .alert)
        view.selectIndexPuchaseBlock = { [weak self] index in
            self?.currentPurchaseIndex = Int(index)
            self?.tapLogType()
        }
        view.subscribeVipPuchaseBlock = { [weak self] model in
            self?.purchaseViewSelect()
        }
        view.subscribeProtocolBlock = { [weak self] index in
            if index == 0 {
                self?.userProtocol()
            } else {
                self?.autoRenewProtocol()
            }
        }
        view.agressProtocolBlock = { [weak self] agree in
            self?.isAgreeProtocol = agree
        }
        return view
    }()
    
    private lazy var subscribeMiaImageView: UIImageView = {
        let imgView = UIImageView()
        imgView.contentMode = .scaleAspectFit
        imgView.image = UIImage(named: "vip_alert_mia_cion")
        return imgView
    }()
    
    private lazy var subscribeAIPlanImageView: UIImageView = {
        let imgView = UIImageView()
        imgView.contentMode = .scaleAspectFit
        imgView.image = UIImage(named: "vip_alert_aiplan_cion")
        return imgView
    }()
    
    
    
    
    private let purchaseManager = MRKPurchaseManager()
    /// 购买项数据
    private var vipRulerArray: [MRKVipCardModel] = []
    /// 当前购买索引
    private var currentPurchaseIndex: Int = 0
    /// 是否同意协议
    private var isAgreeProtocol: Bool = false
    
    // MARK: - 生命周期
    
    override func layoutContainerView() {
        guard let superview = containerView.superview else { return }
        containerView.mas_makeConstraints { make in
            make?.bottom.equalTo()(superview.mas_bottom)
            make?.centerX.equalTo()(superview.mas_centerX)
            make?.width.mas_equalTo()(sMinMainWidth)
        }
    }
    
    override func setupContainerViewAttributes() {
        containerView.backgroundColor = .clear
        
        ///购买成功通知
        NotificationCenter.default.addObserver(self, selector: #selector(cancelBtnClick), name:NSNotification.Name("MeritVipCreateSuc"), object: nil)
    }
    
    override func setupContainerSubViews() {
        containerView.addSubview(cancelButton)
        containerView.addSubview(backgroundView)
        containerView.addSubview(subscribeMiaImageView)
        containerView.addSubview(subscribeAIPlanImageView)
        containerView.addSubview(titleLabel)
        containerView.addSubview(descripLabel)
        containerView.addSubview(purchaseView)
        
        ///
        loadVipProducts()
    }
    
    override func layoutContainerViewSubViews() {
        // 使用 Masonry 进行布局
        cancelButton.mas_makeConstraints { make in
            make?.top.equalTo()(containerView)?.offset()(0)
            make?.right.equalTo()(containerView)?.offset()(-WKDHPX_S(8))
            make?.width.height().mas_equalTo()(WKDHPX_S(40))
        }
        
        backgroundView.mas_makeConstraints { make in
            make?.top.equalTo()(containerView)?.offset()(WKDHPX_S(60))
            make?.left.right().equalTo()(containerView)
            make?.bottom.equalTo()(containerView)
        }
        
        subscribeMiaImageView.mas_makeConstraints { make in
            make?.top.equalTo()(backgroundView)?.offset()(-WKDHPX_S(25))
            make?.right.equalTo()(containerView)?.offset()(-WKDHPX_S(8))
            make?.width.mas_equalTo()(WKDHPX_S(86))
            make?.height.mas_equalTo()(WKDHPX_S(81))
        }
        
        subscribeAIPlanImageView.mas_makeConstraints { make in
            make?.right.equalTo()(containerView)?.offset()(-WKDHPX_S(80))
            make?.bottom.equalTo()(backgroundView.mas_top)?.offset()(WKDHPX_S(8))
            make?.width.mas_equalTo()(WKDHPX_S(248))
            make?.height.mas_equalTo()(WKDHPX_S(47))
        }
        
        titleLabel.mas_makeConstraints { make in
            make?.top.equalTo()(containerView)?.offset()(WKDHPX_S(84))
            make?.left.equalTo()(containerView)?.offset()(WKDHPX_S(16))
            make?.right.lessThanOrEqualTo()(containerView)?.offset()(-WKDHPX_S(16))
        }
        
        descripLabel.mas_makeConstraints { make in
            make?.top.equalTo()(titleLabel.mas_bottom)?.offset()(WKDHPX_S(12))
            make?.left.equalTo()(containerView)?.offset()(WKDHPX_S(16))
            make?.right.equalTo()(containerView)?.offset()(-WKDHPX_S(16))
        }
        
        purchaseView.mas_makeConstraints { make in
            if (alertType == PurchaseSubscribeAlertType.course){
                make?.top.equalTo()(titleLabel.mas_bottom)
            }else{
                make?.top.equalTo()(descripLabel.mas_bottom)
            }
            make?.left.right().equalTo()(containerView)
            make?.bottom.equalTo()(containerView)?.offset()(-DeviceMetrics.bottomSafeAreaHeight)
        }
    }
    
    // MARK: - 数据加载与交互
    
    /// 请求会员产品数据
    func loadVipProducts() {
        let viewType = UserInfo.vipType
        MRKBaseRequest.mrkRequestType(.GET, url: "/user/member-product/vip-product", andParm: ["vipType": viewType]) { [weak self] request in
            guard let self = self else { return }
            if  let response = request.responseObject as? [String: Any],
                let data = response["data"] as? [String: Any],
                let ordinaries = data["ordinaries"] as? [[String: Any]]
            {
                if let vipModels = NSArray.modelArray(with: MRKVipCardModel.self, json: ordinaries) as? [MRKVipCardModel] {
                    self.vipRulerArray = vipModels
                    self.startTimer()
                    self.purchaseView.vipRulerArray = NSMutableArray(array: vipModels)
                }
            }
            
        } failure: { _ in }
    }
    
    
    private var couponTimer: Timer?
    fileprivate func startTimer() {
        /// 检查是否有需要倒计时的卡券
        let needTimer = vipRulerArray.contains { model in
            if let expireTimeStr = model.couponExpireTime, let expireTime = Int64(expireTimeStr), expireTime > 0 {
                return true
            }
            return false
        }
        guard needTimer else { return }

        // 先销毁旧timer，避免多次创建
        couponTimer?.invalidate()
        couponTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { _ in
            NotificationCenter.default.post(name: NSNotification.Name("purchaseCouponNotification"), object: nil)
        }
        RunLoop.current.add(couponTimer!, forMode: .common)
    }

    ///
    deinit {
        NotificationCenter.default.removeObserver(self)
        
        couponTimer?.invalidate()
        couponTimer = nil
    }
    
    /// 购买按钮点击
    private func purchaseViewSelect() {
        selectLogType()
        
        guard !vipRulerArray.isEmpty else { return }
        guard currentPurchaseIndex < vipRulerArray.count else { return }
        guard isAgreeProtocol else {
            showTipActionAlertHandle { [weak self] index in
                if index == 1 {
                    self?.isAgreeProtocol = true
                    self?.purchaseView.hasAgreeProtocol = true
                    self?.purchaseViewSelect()
                }
            }
            return
        }
        
        let model = vipRulerArray[currentPurchaseIndex]
        
        guard let appStoreCode = model.appStoreCode else { return }
        purchaseManager.fetchesPaymentCode(appStoreCode)
    }
    
    /// 协议弹窗
    private func showTipActionAlertHandle(_ handle: @escaping (Int) -> Void) {
        let alert = MRKLoginTipAlert()
        alert.alertType = .vip
        alert.handle = handle
        alert.routerHandle = { [weak self] index in
            if index == 0 {
                self?.userProtocol()
            } else {
                self?.autoRenewProtocol()
            }
        }
        alert.show(in: self)
    }
    
    /// 用户协议
    private func userProtocol() {
        let vc = WebViewViewController()
        vc.htmlURL = MRKSwiftUseOCHelp.mrkAppH5LinkCombineSwift(MRKLinkUserProtocol)
        vc.titleString = "用户协议"
        let nav = MRKNavigationController(rootViewController: vc)
        nav.modalPresentationStyle = .fullScreen
        UIViewController.current()?.present(nav, animated: true)
    }
    
    /// 会员协议
    private func autoRenewProtocol() {
        let vc = WebViewViewController()
        vc.htmlURL = MRKSwiftUseOCHelp.mrkAppH5LinkCombineSwift(MRKLinkAutoRenewalProtocol)
        vc.titleString = "会员服务协议"
        let nav = MRKNavigationController(rootViewController: vc)
        nav.modalPresentationStyle = .fullScreen
        UIViewController.current()?.present(nav, animated: true)
        
        guard let base = UIViewController.current() as? MRKBaseController else { return }
        MRKSwiftUseOCHelp.manualUploadTraceType(2, pageTitle: base.navTitle, pageId: base.tracePageId, eventId: "btn_profile_vip_services_vip", route: base.tracePageRoute, duration: 0, extendPara: [:])
    }
    
    // MARK: - 关闭
    @objc private func cancelBtnClick() {
        dismiss(animated: true)
    }
    
    // MARK: - UI状态
    @objc public enum PurchaseSubscribeAlertType: Int  {
        case normal, aiPlan, liveStream, course
    }
    
    @objc public var alertType: PurchaseSubscribeAlertType = .normal {
        didSet {
            updateUIForAlertType()
        }
    }
    
    private func updateUIForAlertType() {
        switch alertType {
        case .normal:
            titleLabel.text = ""
            descripLabel.text = ""
        case .aiPlan:
            titleLabel.text = "你的专属AI私教-Mia"
            descripLabel.text = "今日 AI 问询次数已用完，开通会员享更多次数"
        case .liveStream:
            titleLabel.text = "开通会员观看直播教学"
            descripLabel.text = "真人教练在线带练，运动游戏化体验"
            subscribeAIPlanImageView.hidden(true)
            ReportMrkLogParms(1, "直播详情页 - 会员开通浮窗曝光", "page_live_detail", nil, nil, 0, nil);
        case .course:
            titleLabel.text = "3000+会员精品课随心练"
            descripLabel.text = ""
            descripLabel.hidden(true)
            subscribeAIPlanImageView.hidden(true)
            ReportMrkLogParms(1, "课程详情页 - 会员开通浮窗曝光", "page_course_detail", nil, nil, 0, nil);
        }
    }
    
    
    private func tapLogType() {
        switch alertType {
        case .normal: break
        
        case .aiPlan: break
          
        case .liveStream:
            ReportMrkLogParms(2, "直播详情页 - 会员开通浮窗曝光", "page_live_detail", "btn_live_package_click", nil, 0, nil);
        case .course:
            ReportMrkLogParms(2, "课程详情页 - 会员开通浮窗曝光", "page_course_detail", "btn_course_package_click", nil, 0, nil);
        }
    }
    
    private func selectLogType() {
        switch alertType {
        case .normal: break
        
        case .aiPlan: break
          
        case .liveStream:
            ReportMrkLogParms(2, "直播详情页 - 会员开通浮窗曝光", "page_live_detail", "btn_live_open_vip", nil, 0, nil);
        case .course:
            ReportMrkLogParms(2, "课程详情页 - 会员开通浮窗曝光", "page_course_detail", "btn_course_open_vip", nil, 0, nil);
        }
    }
}



fileprivate class MRKSubscribeBackground: UIView {
    // MARK: - 初始化
    public override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    public required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }
    
    // MARK: - UI布局
    
    private func setupUI() {
        backgroundColor = .clear
    }
    
    // MARK: - 绘制外边框和阴影
    
    public override func draw(_ rect: CGRect) {
        guard let context = UIGraphicsGetCurrentContext() else { return }
        
        let path = createCustomPath(in: rect)
        context.addPath(path.cgPath)
        context.clip()
        
        // 渐变颜色
        let colors = [UIColor(red: 1.0, green: 0.92, blue: 0.87, alpha: 1.0).cgColor,
                      UIColor.white.cgColor,
                      UIColor.white.cgColor]
        
        let colorSpace = CGColorSpaceCreateDeviceRGB()
        let gradient = CGGradient(colorsSpace: colorSpace,
                                  colors: colors as CFArray,
                                  locations: [0.0, 0.2, 1.0])!
        
        let startPoint = CGPoint(x: rect.midX, y: 0)
        let endPoint = CGPoint(x: rect.midX, y: rect.maxY)
        
        context.drawLinearGradient(gradient, start: startPoint, end: endPoint, options: [])
    }

    private func createCustomPath(in rect: CGRect) -> UIBezierPath {
        let w = rect.width
        let h = rect.height
        
        let cornerRadius: CGFloat = WKDHPX_S(24)       /// 左上角圆角
        let bumpHeight: CGFloat = WKDHPX_S(36)
        let bumpPoint1Padding: CGFloat = WKDHPX_S(120) ///右上角第一根轴线靠右距离
        let bumpPoint2Padding: CGFloat = WKDHPX_S(70)  ///右上角第二根轴线靠右距离
        let padding: CGFloat = WKDHPX_S(20)            ///轴线两侧间距
        let bumpPointCenter: CGFloat = (bumpPoint1Padding - bumpPoint2Padding)/2
       
        let path = UIBezierPath()
        /// 起点：左下角
        path.move(to: CGPoint(x: 0, y: h))
        /// 左边直线
        path.addLine(to: CGPoint(x: 0, y: cornerRadius))
        /// 左上角圆角
        path.addQuadCurve(to: CGPoint(x: cornerRadius, y: 0),
                          controlPoint: CGPoint(x: 0, y: 0))
        /// 顶部直线到右上曲线起点
        path.addLine(to: CGPoint(x: w - bumpPoint1Padding - padding, y: 0))
        /// 右上凸起 S 弯（拟合你图上的形状）
        path.addCurve(to: CGPoint(x: w - bumpPoint2Padding + padding, y: bumpHeight),
                      controlPoint1: CGPoint(x: w - bumpPoint1Padding + bumpPointCenter, y: 0),
                      controlPoint2: CGPoint(x: w - bumpPoint1Padding + bumpPointCenter, y: bumpHeight))
        /// 右侧下凸下角直线
        path.addLine(to: CGPoint(x: w - cornerRadius, y: bumpHeight))
        /// 右上角圆角（小幅圆角）
        path.addQuadCurve(to: CGPoint(x: w, y: bumpHeight + cornerRadius),
                          controlPoint: CGPoint(x: w, y: bumpHeight))
        /// 右边直线到底部
        path.addLine(to: CGPoint(x: w, y: h))
        /// 底部闭合
        path.addLine(to: CGPoint(x: 0, y: h))
        path.close()
        return path
    }
}
