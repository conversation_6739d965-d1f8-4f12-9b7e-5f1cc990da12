//
//  MRKVipPageModel.m
//  Student_IOS
//
//  Created by merit on 2022/10/25.
//

#import "MRKVipPageModel.h"
#import "MRKAdvertManager.h"

@interface MRKVipPageModel ()
@property (nonatomic, strong) RACSubject *updateVipSignal;
@property (nonatomic, strong) RACSubject *changeVipSignal;
@end

@implementation MRKVipPageModel

- (instancetype)init {
    self = [super init];
    if(self){
        self.updateVipSignal = [[RACSubject subject] setNameWithFormat:@"VipPagSiganlName"];
        self.changeVipSignal = [[RACSubject subject] setNameWithFormat:@"changeVipPagSiganlName"];
    }
    return self;
}

- (void)refreshDataSource {
    @weakify(self)
    RACSignal *product = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self userProductRequest:^(void) {
            [subscriber sendNext:nil];
        }];
        return nil;
    }];
    
    RACSignal *autoRenew = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self  autoRenewRequest:^(void) {
            [subscriber sendNext:nil];
        }];
        return nil;
    }];
    
    RACSignal *vipInfo = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self  getUserVipInfoRequest:^(void) {
            [subscriber sendNext:nil];
        }];
        return nil;
    }];
    
    RACSignal *banner = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self  getVipPageBanner:^(void) {
            [subscriber sendNext:nil];
        }];
        return nil;
    }];
    
    RACSignal *memberConvertsion = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self  getMemberConvertsionRequest:^(void){
            [subscriber sendNext:nil];
        }];
        return nil;
    }];
    
    RACSignal *vipRights = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self vipRightsRequest:^(void) {
            [subscriber sendNext:nil];
        }];
        return nil;
    }];
    
    RACSignal *purchaseDiscount = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self  fetchUserPurchaseDiscount:^(void){
            [subscriber sendNext:nil];
        }];
        return nil;
    }];
    
    [[RACSignal combineLatest:@[product, autoRenew, vipInfo, banner, memberConvertsion, vipRights, purchaseDiscount]] subscribeNext:^(id x) {
        @strongify(self);
        [(RACSubject *)self.updateVipSignal sendNext:x];
    }];
}


- (void)changeDataSource {
    @weakify(self)
    RACSignal *product = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self userProductRequest:^(void) {
            [subscriber sendNext:nil];
        }];
        return nil;
    }];
    
    RACSignal *vipRights = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self vipRightsRequest:^(void) {
            [subscriber sendNext:nil];
        }];
        return nil;
    }];
    
    [[RACSignal combineLatest:@[product, vipRights]] subscribeNext:^(id x) {
        @strongify(self);
        [(RACSubject *)self.changeVipSignal sendNext:x];
    }];
}

#pragma mark - HTTP -

///获取折扣领取时间
- (void)fetchUserPurchaseDiscount:(void(^)(void))completion {
    [MRKBaseRequest mrkRequestType:YTKRequestMethodGET
                               url:@"/user/member-product/coupon"
                           andParm:@{@"vipType": self.vipType?:@""}
          completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        id data = [request.responseObject valueForKeyPath:@"data"];
        self.couponModel = [MRKPurchaseCouponModel modelWithJSON:data];
        completion();
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        completion();
    }];
}

///vip 购买展示模块
- (void)userProductRequest:(void(^)(void))completion {
    [MRKBaseRequest mrkRequestType:YTKRequestMethodGET
                               url:@"/user/member-product/vip-product"
                           andParm:@{@"vipType": self.vipType?:@""}
          completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        id data = [request.responseObject valueForKeyPath:@"data.ordinaries"];
        NSArray *arr = [NSArray modelArrayWithClass:[MRKVipCardModel class] json:data];
        self.vipRulerArray = arr.mutableCopy;
        completion();
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        completion();
    }];
}

///会员权益
- (void)vipRightsRequest:(void(^)(void))completion {
    [MRKBaseRequest mrkRequestType:YTKRequestMethodGET
                               url:@"/app/exclusive-group/list"
                           andParm:@{@"vipType": self.vipType?:@""}
          completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        id data = [request.responseObject objectForKey:@"data"];
        NSArray *arr = [NSArray modelArrayWithClass:[MRKVipExclusiveItemModel class] json:data];
        ///截取并替换最后一个权益项
        NSMutableArray *array = [NSMutableArray arrayWithArray:arr];
        if (array.count >= 8) {
            array = [array subarrayWithRange:NSMakeRange(0, 8)].mutableCopy;
            ///替换最后一个
            MRKVipExclusiveItemModel *model = ({
                MRKVipExclusiveItemModel *m = [[MRKVipExclusiveItemModel alloc] init];
                m.name = @"更多权益";
                m.isMoreRights = YES;
                m;
            });
            [array replaceObjectAtIndex:7 withObject:model];
        }
        self.vipExclusiveArray = array;
        completion();
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        completion();
    }];
}

///自动续费信息
- (void)autoRenewRequest:(void(^)(void))completion {
    [MRKBaseRequest mrkRequestType:YTKRequestMethodGET
                               url:@"/user/member-product/query/member-order/sign"
                           andParm:nil
          completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        id data = [request.responseObject valueForKeyPath:@"data"];
        MRKAutoRenewModel *model = [MRKAutoRenewModel modelWithJSON:data];
        self.renewModel = model;
        completion();
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        completion();
    }];
}

///用户会员信息
- (void)getUserVipInfoRequest:(void(^)(void))completion {
    [MRKBaseRequest mrkRequestType:YTKRequestMethodGET
                               url:@"/user/user-member"
                           andParm:nil
          completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        id data = [request.responseObject valueForKeyPath:@"data"];
        self.vipInfomodel = [MemberInfoDataDTO modelWithJSON:data];
        self.isMember = self.vipInfomodel.isMember;
        self.expireDate = self.vipInfomodel.expireDate;
        self.level = self.vipInfomodel.level;
        completion();
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        completion();
    }];
}

///vipBanner
- (void)getVipPageBanner:(void(^)(void))completion {
    [MRKAdvertManager mrkRequestPositionCode:MRKMemberBannerCode
                    completeBlockWithSuccess:^(MRKAdvertDataModel * _Nullable model) {
        self.vipAdverts = model.adverts.mutableCopy;
        completion();
    }];
}

///兑换会员
- (void)getMemberConvertsionRequest:(void(^)(void))completion {
    ///屏蔽审核用户15058640036, 指望后台控制是指望不上了
    if ([UserInfo.phoneNum isEqualToString:@"15058640036"]) {
        completion();
        return;
    }
    
    [MRKAdvertManager mrkRequestPositionCode:MRKMemberConvertsionCode
                    completeBlockWithSuccess:^(MRKAdvertDataModel * _Nullable model) {
        if(model.adverts.count > 0){
            AdvertModel *mode = model.adverts.firstObject;
            self.exchangeAdvert = mode;
        }
        completion();
    }];
}

- (void)dealloc {
     NSLog(@"😊😊😊-----dealloc-----😊😊😊%@" , NSStringFromClass([self class]));
}
@end


