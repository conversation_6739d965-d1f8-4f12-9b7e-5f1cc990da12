//
//  MRKSubscribeMembershipAlert.h
//  Student_IOS
//
//  Created by Jun<PERSON> on 2025/8/4.
//

#import <UIKit/UIKit.h>
#import "MRKAlertView.h"
#import "MRKVipCardModel.h"

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, MembershipAlertType) {
    MembershipAlertTypeNormal,      // 普通会员升级
    MembershipAlertTypeAIPlan,      // AI计划升级
    MembershipAlertTypeCourse,      // 课程相关升级
};

@class MRKSubscribeMembershipAlert;

@protocol MRKSubscribeMembershipAlertDelegate <NSObject>
@optional
/// 订阅按钮点击回调
- (void)membershipAlert:(MRKSubscribeMembershipAlert *)alert didClickSubscribeWithModel:(MRKVipCardModel *)model;
/// 协议链接点击回调
- (void)membershipAlert:(MRKSubscribeMembershipAlert *)alert didClickProtocolWithType:(NSString *)protocolType;
/// 取消按钮点击回调
- (void)membershipAlertDidClickCancel:(MRKSubscribeMembershipAlert *)alert;
@end

@interface MRKSubscribeMembershipAlert : MRKAlertView

/// 弹窗类型
@property (nonatomic, assign) MembershipAlertType alertType;
/// 代理
@property (nonatomic, weak) id<MRKSubscribeMembershipAlertDelegate> membershipDelegate;
/// VIP卡片数据模型
@property (nonatomic, strong) MRKVipCardModel *vipCardModel;
/// 是否同意协议（默认NO）
@property (nonatomic, assign) BOOL isAgreedProtocol;

/// 便利构造方法
+ (instancetype)alertWithType:(MembershipAlertType)type vipModel:(MRKVipCardModel *)model;

@end

NS_ASSUME_NONNULL_END
