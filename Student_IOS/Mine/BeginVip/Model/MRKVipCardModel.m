//
//  MRKVipCardModel.m
//  Student_IOS
//
//  Created by merit on 2022/6/2.
//

#import "MRKVipCardModel.h"

@implementation MRKVipCardModel

+ (NSDictionary *)modelCustomPropertyMapper {
    return @{@"descriptionStr":@"description"};
}
@end

@implementation MRKVipCheapestModel

+ (NSDictionary *)modelCustomPropertyMapper {
    return @{@"cid":@"id"};
}

- (BOOL)modelCustomTransformFromDictionary:(NSDictionary *)dic {
    NSInteger status = _skuType.integerValue;
    switch (status) {
        case 1:
            _skuName = @"月";
            break;
        case 2:
            _skuName = @"季";
            break;
        case 3:
            _skuName = @"年";
            break;
        case 4:
            _skuName = @"日";
            break;
        case 5:
            _skuName = @"半年";
            break;
        default: break;
    }
    

    NSInteger vipPackageType = _vipPackageType.integerValue;
    switch (vipPackageType) {
        case 1:
            _vipPackageTypeStr = @"包月";
            break;
        case 2:
            _vipPackageTypeStr = @"包季";
            break;
        case 3:
            _vipPackageTypeStr = @"包年";
            break;
        case 4:
            _vipPackageTypeStr = @"连续包月";
            break;
        case 5:
            _vipPackageTypeStr = @"连续包季";
            break;
        case 6:
            _vipPackageTypeStr = @"连续包年";
            break;
        case 7:
            _vipPackageTypeStr = @"连续包半年";
            break;
        default:
            _vipPackageTypeStr = @"单日";
            break;
    }
    
    NSInteger freeType = _freeType.integerValue;
    switch (freeType) {
        case 1:
            _freeTypeStr = @"月";
            break;
        case 2:
            _freeTypeStr = @"季";
            break;
        case 3:
            _freeTypeStr = @"年";
            break;
        case 4:
            _freeTypeStr = @"日";
            break;
        default:
            break;
    }
    
    return YES;
}
@end

@implementation MRKVipExclusiveItemModel

@end
