//
//  MRKVipCardModel.h
//  Student_IOS
//
//  Created by merit on 2022/6/2.
//

#import <Foundation/Foundation.h>
#import "AdvertModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface MRKVipCardModel : MRKBaseModel
@property (nonatomic, copy) NSString *configId;                   //配置ID
@property (nonatomic, copy) NSString *configType;                 //配置类型：1-普通商品，2-升级商品
@property (nonatomic, copy) NSString *name;                       //显示名称
@property (nonatomic, copy, nullable) NSString *showAmount;       //显示金额
@property (nonatomic, copy, nullable) NSString *actualAmount;     //实际金额
@property (nonatomic, copy, nullable) NSString *dailyStartPrice;  //平均每天金额
@property (nonatomic, assign) BOOL isAuto;                        //是否自动续费：0-否，1-是
@property (nonatomic, copy, nullable) NSString *firstAmount;      //首次购买金额
@property (nonatomic, copy, nullable) NSString *appStoreCode;     //苹果商品编码
@property (nonatomic, copy, nullable) NSString *labelIcon;        //角标图标
@property (nonatomic, copy, nullable) NSString *descriptionStr;   //商品描述
@property (nonatomic, copy, nullable) NSString *couponExpireTime; //过期时间的毫秒时间戳

@property (nonatomic, assign) NSInteger *vipPackageType; /// 购买的会员包类型：1-包月，2-包季，3-包年，4-连续包月，5-连续包季，6-连续包年，7-连续包半年，8-单日,
@property (nonatomic, assign) NSInteger *freeType;       /// 免费类型：1-日,2-周,3-月，（新增）
@property (nonatomic, assign) NSInteger *freeNum;        /// 免费时长   当>0才是有效    （新增)
@end


@interface MRKVipCheapestModel : MRKBaseModel
@property (nonatomic, copy) NSString *cid;             //会员产品主键id
@property (nonatomic, copy) NSString *skuType;         //SKU类型：1-月，2-季，3-年，4-日
@property (nonatomic, copy) NSString *discountPrice;   //特惠价格
@property (nonatomic, copy) NSString *dailyStartPrice; //日计费起价
@property (nonatomic, copy) NSString *skuName;         //SKU类型：1-月，2-季，3-年，4-日
@property (nonatomic, copy) NSString *labelIcon;       //角标图标
@property (nonatomic, copy) NSString *vipPackageType;  //1-包月，2-包季，3-包年，4-连续包月，5-连续包季，6-连续包年，7-连续包半年，8-单日
@property (nonatomic, copy) NSString *vipPackageTypeStr;
@end


@interface MRKVipExclusiveItemModel : MRKBaseModel
@property (nonatomic, copy) NSString *name;         // 权益名
@property (nonatomic, copy) NSString *groupId;      // 权益分组id
@property (nonatomic, copy) NSString *code;         // 权益code
@property (nonatomic, copy) NSString *subTitle;     // 副标题
@property (nonatomic, copy) NSString *type;         // 类型：1-用户等级；2-会员权益
@property (nonatomic, assign) NSInteger isForDisplay; // 是否仅展示：1-是，0-否
@property (nonatomic, copy) NSString *val;          // 权益数量
@property (nonatomic, copy) NSString *cover;        // 权益图
@property (nonatomic, copy) NSString *icon;         // 权益图标
@property (nonatomic, copy) NSString *ext;          // 权益数量
@property (nonatomic, copy) NSString *createId;     // 创建id
@property (nonatomic, copy) NSString *updateId;     // 更新id
@property (nonatomic, copy) NSString *updateTime;   // 更新时间
@property (nonatomic, copy) NSString *updateName;   // 更新名称
///
@property (nonatomic, assign) BOOL isMoreRights;    // 是否更多权益
@end

NS_ASSUME_NONNULL_END
