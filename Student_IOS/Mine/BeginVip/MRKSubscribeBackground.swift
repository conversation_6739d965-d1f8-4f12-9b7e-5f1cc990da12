//
//  MRKSubscribeBackground.swift
//  Student_IOS
//
//  Created by <PERSON><PERSON> on 2025/8/6.
//

import UIKit
import SnapKit


@objcMembers
public class MRKSubscribeBackground: UIView {
    // MARK: - 初始化
    public override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        
    }
    
    public required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
        
    }
    
    // MARK: - UI布局
    
    private func setupUI() {
        backgroundColor = .clear
        
    }
    
    // MARK: - 绘制外边框和阴影
    
    public override func draw(_ rect: CGRect) {
        guard let context = UIGraphicsGetCurrentContext() else { return }
        
        let path = createCustomPath(in: rect)
        context.addPath(path.cgPath)
        context.clip()
        
        // 渐变颜色
        let colors = [UIColor(red: 1.0, green: 0.92, blue: 0.87, alpha: 1.0).cgColor,
                      UIColor.white.cgColor,
                      UIColor.white.cgColor]
        
        let colorSpace = CGColorSpaceCreateDeviceRGB()
        let gradient = CGGradient(colorsSpace: colorSpace,
                                  colors: colors as CFArray,
                                  locations: [0.0, 0.2, 1.0])!
        
        let startPoint = CGPoint(x: rect.midX, y: 0)
        let endPoint = CGPoint(x: rect.midX, y: rect.maxY)
        
        context.drawLinearGradient(gradient, start: startPoint, end: endPoint, options: [])
    }

    private func createCustomPath(in rect: CGRect) -> UIBezierPath {
        
        let w = rect.width
        let h = rect.height
        
        let cornerRadius: CGFloat = WKDHPX_S(24)       /// 左上角圆角
        let bumpHeight: CGFloat = WKDHPX_S(36)
        let bumpPoint1Padding: CGFloat = WKDHPX_S(120) ///右上角第一根轴线靠右距离
        let bumpPoint2Padding: CGFloat = WKDHPX_S(70)  ///右上角第二根轴线靠右距离
        let padding: CGFloat = WKDHPX_S(20)            ///轴线两侧间距
        
        let bumpPointCenter: CGFloat = (bumpPoint1Padding - bumpPoint2Padding)/2
       
        
        let path = UIBezierPath()
        
        // 起点：左下角
        path.move(to: CGPoint(x: 0, y: h))
        
        // 左边直线
        path.addLine(to: CGPoint(x: 0, y: cornerRadius))
        
        // 左上角圆角
        path.addQuadCurve(to: CGPoint(x: cornerRadius, y: 0),
                          controlPoint: CGPoint(x: 0, y: 0))
        
        // 顶部直线到右上曲线起点
        path.addLine(to: CGPoint(x: w - bumpPoint1Padding - padding, y: 0))
        
        
        // 右上凸起 S 弯（拟合你图上的形状）
        path.addCurve(to: CGPoint(x: w - bumpPoint2Padding + padding, y: bumpHeight),
                      controlPoint1: CGPoint(x: w - bumpPoint1Padding + bumpPointCenter, y: 0),
                      controlPoint2: CGPoint(x: w - bumpPoint1Padding + bumpPointCenter, y: bumpHeight))
        
        ///
        path.addLine(to: CGPoint(x: w - cornerRadius, y: bumpHeight))
        
        // 右上角圆角（小幅圆角）
        path.addQuadCurve(to: CGPoint(x: w, y: bumpHeight + cornerRadius),
                          controlPoint: CGPoint(x: w, y: bumpHeight))
        
        // 右边直线到底部
        path.addLine(to: CGPoint(x: w, y: h))
        
        // 底部闭合
        path.addLine(to: CGPoint(x: 0, y: h))
        path.close()
        return path
    }

}
  
