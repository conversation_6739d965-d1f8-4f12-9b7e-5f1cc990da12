//
//  MRKUserIAPController.m
//  Student_IOS
//
//  Created by Jun<PERSON> on 2023/11/6.
//

#import "MRKUserIAPController.h"
#import <StoreKit/StoreKit.h>
#import "DYFStoreManager.h"
#import "MRKSVipViews.h"
#import "MRKVipPackageView.h"
#import "MRKVipPageModel.h"
#import "MRKRestorePurchaseController.h"
#import "MRKRenewPagingController.h"
#import "MRKHomeAlertManager.h"
#import "MRKLoginTipAlert.h"
#import "MRKPurchaseManager.h"

@interface MRKUserIAPController ()<UIScrollViewDelegate, MRKAlertViewDelegate>
@property (nonatomic, strong) NSString *vipType; /// 类型 10 vip  30 绝影
@property (nonatomic, strong) MRKVipPageModel *pageModel;
@property (nonatomic, strong) MRKPurchaseManager *purchaseManager;

@property (nonatomic, strong) UIScrollView *baseScrollerView;
@property (nonatomic, strong) MRKIAPMessageView *messageView;
@property (nonatomic, strong) MRKPurchaseView *purchaseView;
@property (nonatomic, strong) MRKIAPBannerView *bannerView;
@property (nonatomic, strong) MRKIAPImageView *vipImageView;
@property (nonatomic, strong) MRKIAPBottomView *bottomTipView;
@property (nonatomic, strong) MRKVipPurchaseBottomView *purchaseBottomView;

@property (nonatomic, assign) int currentPurchaseIndex; /// 标识购买索引
@property (nonatomic, strong) NSMutableArray *vipRulerArray;
@property (nonatomic, assign) BOOL isClaimed; /// 标识弹窗是否领取优惠
@property (nonatomic, strong) NSTimer *couponTimer; /// 购买倒计时
///
@property (nonatomic, assign) float purchaseViewPaddingTop;
@property (nonatomic, assign) BOOL isAgreeProtocol;
@property (nonatomic, assign) BOOL hasChangeNavBarStatus;
@end

@implementation MRKUserIAPController

- (MRKVipPageModel *)pageModel{
    if(!_pageModel){
        _pageModel = [[MRKVipPageModel alloc] init];
    }
    return _pageModel;
}

- (MRKPurchaseManager *)purchaseManager{
    if(!_purchaseManager){
        _purchaseManager = [[MRKPurchaseManager alloc] init];
    }
    return _purchaseManager;
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
}

- (void)viewDidAppear:(BOOL)animated{
    [super viewDidAppear:animated];
    /// 刷新会员时长
    [self UpdateHeaderViewInfo];
    /// 请求弹窗
    [[MRKHomeAlertManager shareManager] addTarget:self checkAlertCode:MRKMemberPopupCode];
}

///刷新用户会员时长
- (void)UpdateHeaderViewInfo{
    @weakify(self);
    [self.pageModel getUserVipInfoRequest:^{
        @strongify(self);
        dispatch_async(dispatch_get_main_queue(), ^{
            [self.messageView configPageModel:self.pageModel vipType:self.vipType];
        });
    }];
}

- (void)dealloc{
    [self.couponTimer invalidate];
    self.couponTimer = nil;
    [[NSNotificationCenter defaultCenter] removeObserver:self];
    
}

- (void)viewDidLoad {
    self.vipType = UserInfo.vipType == 30 ? @"30" : @"10";
    
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    
    self.isClaimed = YES;
    self.navTitle = @"会员中心";
    self.navTitleColor = self.vipType.intValue == 30 ? UIColor.whiteColor : UIColorHex(#363A44);
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(UpdateUserInfo)
                                                 name:@"UpdateUserInfo"
                                               object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(UpdateProducttInfo)
                                                 name:@"PriceAnimationDoneNotification"
                                               object:nil];
    
    ReportMrkLogParms(1, @"VIP页面", @"page_profile_vip", nil, self.tracePageRoute, 0, @{@"member_type_id":self.vipType});
   
    
    @weakify(self);
    RAC(self.pageModel, vipType) = RACObserve(self, vipType);
    RAC(self, vipRulerArray) = RACObserve(self.pageModel, vipRulerArray);
    ///购买项点击选中
    [[[RACObserve(self.purchaseView, selectIndex) ignore:NULL] distinctUntilChanged] subscribeNext:^(id  _Nullable x) {
        @strongify(self);
        self.currentPurchaseIndex = [x intValue];
        [self reloadTipPurchaseStr];
    }];
    ///购买项协议点击
    [[[RACObserve(self.purchaseView, hasAgreeProtocol) ignore:NULL] distinctUntilChanged] subscribeNext:^(id  _Nullable x) {
        @strongify(self);
        self.purchaseBottomView.hasAgreeProtocol = [x boolValue];
        self.isAgreeProtocol = [x boolValue];
    }];
    ///底部协议点击
    [[[RACObserve(self.purchaseBottomView, hasAgreeProtocol) ignore:NULL] distinctUntilChanged] subscribeNext:^(id  _Nullable x) {
        @strongify(self);
        self.purchaseView.hasAgreeProtocol = [x boolValue];
        self.isAgreeProtocol = [x boolValue];
    }];
    
    [self layoutViewUI];
    [self configSignal];
    
    /// 请求弹窗
    [[MRKHomeAlertManager shareManager] addTarget:self requestAlertData:MRKMemberPopupCode];
}


- (void)configSignal {
    @weakify(self);
    [self.view beginLoading];
    self.baseScrollerView.alpha = 0.0;
    [self.pageModel.updateVipSignal subscribeNext:^(id x) {
        @strongify(self);
        [self.view endLoading];
        [self reloadPageData];
        
        dispatch_async(dispatch_get_main_queue(), ^{
            [UIView animateWithDuration:0.3 animations:^{
                self.baseScrollerView.alpha = 1.0f;
            } completion:^(BOOL finished) {
                [self purchaseCouponAlert];
            }];
        });
    }];
    
    [self.pageModel refreshDataSource];
}

- (void)reloadPageData{
    self.vipRulerArray = self.pageModel.vipRulerArray;
    if (self.pageModel.couponModel) {
        self.isClaimed = self.pageModel.couponModel.isClaimed;
    }
    
    __block BOOL startTimer = NO;
    [self.vipRulerArray enumerateObjectsUsingBlock:^(MRKVipCardModel * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if (obj.couponExpireTime.intValue > 0){
            startTimer = YES;
            *stop = YES;
        }
    }];
    
    if (startTimer) {
        [self startTimer];
    }
    
    ///
    [self reloadTipPurchaseStr];
    
    [self.messageView configPageModel:self.pageModel vipType:self.vipType];
    [self.purchaseView configPageModel:self.pageModel vipType:self.vipType claimedAlert:self.isClaimed];
    [self.bannerView configPageModel:self.pageModel vipType:self.vipType];
    [self.vipImageView configPageModel:self.pageModel vipType:self.vipType];
    [self.bottomTipView configPageModel:self.pageModel vipType:self.vipType];
    
    ///
    dispatch_async(dispatch_get_main_queue(), ^{
        self.purchaseViewPaddingTop = CGRectGetMaxY(self.purchaseView.frame);
    });
}

///开启优惠金额倒计时
- (void)startTimer {
    // 先销毁旧timer，避免多次创建
    [self.couponTimer invalidate];
    self.couponTimer = nil;
    ///
    YYWeakProxy *proxy = [YYWeakProxy proxyWithTarget:self];
    self.couponTimer = [NSTimer scheduledTimerWithTimeInterval:1.0 target:proxy selector:@selector(handleCouponTimer) userInfo:nil repeats:YES];
    [[NSRunLoop mainRunLoop] addTimer:self.couponTimer forMode:NSRunLoopCommonModes];
}

/// timer回调
- (void)handleCouponTimer {
    [[NSNotificationCenter defaultCenter] postNotificationName:@"purchaseCouponNotification" object:nil];
}

///未领取优惠弹窗
- (void)purchaseCouponAlert{
    if (self.pageModel.couponModel && !self.pageModel.couponModel.isClaimed) {
        MRKSubscribeDiscountAlert *alert = [MRKSubscribeDiscountAlert purchaseAlertViewWithModel:self.pageModel.couponModel];
        alert.isAutoHidden = false;
        alert.delegate = self;
        [alert showWindow];
    }
}
#pragma mark - alertViewDelegate
- (void)alertViewDidDismiss {
    self.isClaimed = YES;
    self.purchaseView.isClaimed = YES;
    [self reloadTipPurchaseStr];
    ///金额动画通知
    [[NSNotificationCenter defaultCenter] postNotificationName:@"PurchasePriceAnimation" object:nil];
}

///更新信息
- (void)UpdateUserInfo{
    @weakify(self);
    ///刷新用户会员时长
    [self.pageModel getUserVipInfoRequest:^{
        @strongify(self);
        dispatch_async(dispatch_get_main_queue(), ^{
            [self.messageView configPageModel:self.pageModel vipType:self.vipType];
        });
    }];
    
    ///刷新产品数据
    [self.pageModel userProductRequest:^{
        @strongify(self);
        dispatch_async(dispatch_get_main_queue(), ^{
            [self.purchaseView configPageModel:self.pageModel vipType:self.vipType claimedAlert:self.isClaimed];
        });
    }];
}

///更新信息
- (void)UpdateProducttInfo{
    @weakify(self);
    ///刷新产品数据
    [self.pageModel userProductRequest:^{
        @strongify(self);
        [self reloadPurchaseData];
    }];
}
- (void)reloadPurchaseData{
    self.vipRulerArray = self.pageModel.vipRulerArray;
 
    __block BOOL startTimer = NO;
    [self.vipRulerArray enumerateObjectsUsingBlock:^(MRKVipCardModel * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if (obj.couponExpireTime.intValue > 0){
            startTimer = YES;
            *stop = YES;
        }
    }];
    
    if (startTimer) {
        [self startTimer];
    }
    
    ///
    [self reloadTipPurchaseStr];
    [self.purchaseView configPageModel:self.pageModel vipType:self.vipType claimedAlert:self.isClaimed];
}

- (void)layoutViewUI {
    self.baseScrollerView.delegate = self;
    [self.mrkContentView addSubview:self.baseScrollerView];
    [self.baseScrollerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsZero);
    }];
    
    
    { ///add subview
        [self.baseScrollerView addSubview:self.messageView];
        [self.messageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.baseScrollerView.mas_top).offset(0);
            make.left.mas_equalTo(self.baseScrollerView.mas_left);
            make.centerX.mas_equalTo(self.mrkContentView.mas_centerX);
            make.width.mas_equalTo(RealScreenWidth);
        }];
        
        [self.baseScrollerView addSubview:self.purchaseView];
        [self.purchaseView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.messageView.mas_bottom);
            make.left.mas_equalTo(self.baseScrollerView.mas_left);
            make.width.mas_equalTo(RealScreenWidth);
        }];
        
        [self.baseScrollerView addSubview:self.bannerView];
        [self.bannerView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.purchaseView.mas_bottom);
            make.left.mas_equalTo(self.baseScrollerView.mas_left);
            make.width.mas_equalTo(RealScreenWidth);
        }];
        
        [self.baseScrollerView addSubview:self.vipImageView];
        [self.vipImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.bannerView.mas_bottom);
            make.left.mas_equalTo(self.baseScrollerView.mas_left);
            make.width.mas_equalTo(RealScreenWidth);
        }];
        
        [self.baseScrollerView addSubview:self.bottomTipView];
        [self.baseScrollerView insertSubview:self.bottomTipView aboveSubview:self.vipImageView];
        [self.bottomTipView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.vipImageView.mas_bottom).offset(0);
            make.left.mas_equalTo(self.baseScrollerView.mas_left);
            make.width.mas_equalTo(RealScreenWidth);
            make.bottom.mas_equalTo(0);
        }];
    }
    //    make.height.mas_equalTo(self.purchaseBtn.mas_width).multipliedBy(0.17);
    
    
    [self.baseScrollerView addSubview:self.purchaseBottomView];
    [self.baseScrollerView bringSubviewToFront:self.purchaseBottomView];
    [self.purchaseBottomView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.mas_equalTo(self.mrkContentView.mas_centerX);
        make.bottom.mas_equalTo(self.mrkContentView.mas_bottom);
        make.width.mas_equalTo(self.mrkContentView.mas_width);
        make.height.mas_equalTo(WKDHPX(130));
    }];
    MrkCornerMaskWithViewRadius(self.purchaseBottomView, ViewRadiusMake(8, 8, 0, 0));
}


- (UIScrollView *)baseScrollerView{
    if (!_baseScrollerView) {
        _baseScrollerView = [[UIScrollView alloc] init];
        _baseScrollerView.pagingEnabled = NO;
        _baseScrollerView.backgroundColor = [UIColor whiteColor];
        _baseScrollerView.showsVerticalScrollIndicator = NO;
    }
    return _baseScrollerView;
}

///Vip 信息
- (MRKIAPMessageView *)messageView{
    if (!_messageView){
        _messageView = [[MRKIAPMessageView alloc] init];
    }
    return _messageView;
}

///会员购买项
- (MRKPurchaseView *)purchaseView{
    if (!_purchaseView){
        _purchaseView = [[MRKPurchaseView alloc] initWithFrame:CGRectZero viewType:MRKPurchaseViewTypeNormal];
        @weakify(self);
        _purchaseView.subscribeVipPuchaseBlock = ^(MRKVipCardModel * _Nonnull model) {
            [self_weak_ purchaseViewSelect];
        };
        _purchaseView.subscribeProtocolBlock = ^(NSInteger index) {
            if (index == 0) {
                [self_weak_ userProtocol];
            }else{
                [self_weak_ autoRenewProtocal];
            }
        };
    }
    return _purchaseView;
}

/// banner
- (MRKIAPBannerView *)bannerView{
    if (!_bannerView){
        _bannerView = [[MRKIAPBannerView alloc] init];
    }
    return _bannerView;
}

/// banner
- (MRKIAPImageView *)vipImageView{
    if (!_vipImageView){
        _vipImageView = [[MRKIAPImageView alloc] init];
    }
    return _vipImageView;
}

///Vip底部显示
- (MRKIAPBottomView *)bottomTipView{
    if (!_bottomTipView){
        _bottomTipView = [[MRKIAPBottomView alloc] init];
        @weakify(self);
        _bottomTipView.restorePurchaseBlock = ^{
            @strongify(self);
            MRKRestorePurchaseController *vc = [[MRKRestorePurchaseController alloc] init];
            [self.navigationController pushViewController:vc animated:YES];
            
            ///统计
            ReportMrkLogParms(2, @"去解决", @"page_profile_vip", @"btn_profile_vip_solve", nil, 0, nil);
        };
    }
    return _bottomTipView;
}

///购买按钮
- (MRKVipPurchaseBottomView *)purchaseBottomView{
    if (!_purchaseBottomView) {
        _purchaseBottomView = [[MRKVipPurchaseBottomView alloc] init];
        _purchaseBottomView.backgroundColor = UIColor.whiteColor;
        _purchaseBottomView.hidden = NO;
        _purchaseBottomView.alpha = 0;
        @weakify(self);
        _purchaseBottomView.subscribeVipPuchaseBlock = ^{
            [self_weak_ purchaseViewSelect];
        };
        _purchaseBottomView.subscribeProtocolBlock = ^(NSInteger index) {
            if (index == 0) {
                [self_weak_ userProtocol];
            }else{
                [self_weak_ autoRenewProtocal];
            }
        };
    }
    return _purchaseBottomView;
}

///
- (void)reloadTipPurchaseStr{
    NSAttributedString *tipPurchaseStr = [self refershTipPurchaseStr];
    self.purchaseView.tipPurchaseStr = tipPurchaseStr;
    self.purchaseBottomView.tipPurchaseStr = tipPurchaseStr;
}

- (NSAttributedString *)refershTipPurchaseStr{
    ///如果没有购买项
    if (self.vipRulerArray.count <= 0){
        return nil;
    }
    ///如果 self.page数值异常
    if (self.currentPurchaseIndex >= self.vipRulerArray.count){
        return nil;
    }
    
    MRKVipCardModel *model = [self.vipRulerArray objectAtIndex:self.currentPurchaseIndex];
    
    @weakify(self);
    NSMutableAttributedString *attributedStr = [[NSMutableAttributedString alloc] init];
    attributedStr.font = [UIFont systemFontOfSize:WKDHPX(14)];
    [attributedStr appendAttributedString:({
        @strongify(self);
        NSString *price = @"";
        if (self.isClaimed) {///如果存在优惠
            price = model.actualAmount?:@"";
            if (model.isAuto && [model.firstAmount isNotBlank]){
                price = model.firstAmount;
            }
        } else {
            price = model.showAmount?:@"";
        }
        
        NSMutableAttributedString *str = [[NSMutableAttributedString alloc] initWithString:@"¥"];
        str.font = [UIFont systemFontOfSize:WKDHPX(14)];
        NSMutableAttributedString *priceStr = [[NSMutableAttributedString alloc] initWithString:price];
        priceStr.font = [UIFont systemFontOfSize:WKDHPX(20) weight:UIFontWeightMedium];
        [str appendAttributedString:priceStr];
        str;
    })];
    
    [attributedStr appendAttributedString:({
        NSString *tip = [NSString stringWithFormat:@" 确认协议，%@%@ ", self.pageModel.isMember ? @"续费":@"开通" , model.name];
        NSMutableAttributedString *text = [[NSMutableAttributedString alloc] initWithString:tip];
        text.font = [UIFont systemFontOfSize:WKDHPX(14)];
        text;
    })];
    
    return attributedStr;
}


- (void)purchaseViewSelect {
    self.purchaseBottomView.traceEventId = self.pageModel.isMember ? @"btn_profile_vip_renew": @"btn_profile_vip_purchase";
    ///如果没有购买项
    if (self.vipRulerArray.count <= 0){
        return;
    }
    
    ///如果 self.page数值异常
    if (self.currentPurchaseIndex >= self.vipRulerArray.count){
        return;
    }
    
    /// 是否同意协议
    if (!self.isAgreeProtocol){
        @weakify(self);
        [self showTipActionAlertHandle:^(NSInteger index) {
            @strongify(self);
            if (index == 1){
                self.isAgreeProtocol = YES;
                self.purchaseBottomView.hasAgreeProtocol = YES;
                self.purchaseView.hasAgreeProtocol = YES;
                ///
                [self purchaseViewSelect];
            }
        }];
        return;
    }
    
    NSArray *transactions = [SKPaymentQueue defaultQueue].transactions;
    DYFStoreLog(@"transactions ==== %@",transactions);
    
    MRKVipCardModel *model = [self.vipRulerArray objectAtIndex:self.currentPurchaseIndex];
    [self.purchaseManager fetchesPaymentCode:model.appStoreCode];
}

- (void)showTipActionAlertHandle:(void (^)(NSInteger index))handle{
    @weakify(self);
    MRKLoginTipAlert *alert = [[MRKLoginTipAlert alloc] init];
    alert.alertType = LoginTipAlertStyleVip;
    alert.hasAutoProtrol = self.purchaseView.isAutoproduct;
    alert.handle = handle;
    alert.routerHandle = ^(NSInteger index) {
        @strongify(self);
        if (index == 0) {
            [self userProtocol];
        }else{
            [self autoRenewProtocal];
        }
    };
    [alert showIn:self.view];
}


///会员协议
- (void)autoRenewProtocal {
    WebViewViewController *vc = [WebViewViewController new];
    vc.htmlURL = MRKAppH5LinkCombine(MRKLinkAutoRenewalProtocol);
    vc.titleString = @"会员服务协议";
    MRKNavigationController *nav = [[MRKNavigationController alloc] initWithRootViewController:vc];
    nav.modalPresentationStyle = UIModalPresentationFullScreen;
    [[UIViewController currentViewController] presentViewController:nav animated:YES completion:nil];
    
    MRKBaseController *base = (MRKBaseController *)[UIViewController currentViewController];
    [[MRKTraceManager sharedInstance] manualUploadTraceType:2 pageTitle:base.navTitle pageId:base.tracePageId eventId:@"btn_profile_vip_services_vip" route:base.tracePageRoute duration:0 extendPara:@{}];
}

///用户协议
- (void)userProtocol {
    WebViewViewController *vc = [WebViewViewController new];
    vc.htmlURL = MRKAppH5LinkCombine(MRKLinkUserProtocol);
    vc.titleString = @"用户协议";
    MRKNavigationController *nav = [[MRKNavigationController alloc] initWithRootViewController:vc];
    nav.modalPresentationStyle = UIModalPresentationFullScreen;
    [[UIViewController currentViewController] presentViewController:nav animated:YES completion:nil];
}

///隐私政策
- (void)privacyPolicy {
    WebViewViewController *vc = [WebViewViewController new];
    vc.htmlURL = MRKAppH5LinkCombine(MRKLinkPrivacyProtocol);
    vc.titleString = @"隐私政策";
    MRKNavigationController *nav = [[MRKNavigationController alloc] initWithRootViewController:vc];
    nav.modalPresentationStyle = UIModalPresentationFullScreen;
    [[UIViewController currentViewController] presentViewController:nav animated:YES completion:nil];
}

///客服
- (void)customerService {
    WebViewViewController *vc = [WebViewViewController new];
    vc.isHiddenNav = YES;
    vc.htmlURL = MRKAppH5LinkCombine(MRKUserHelpAndFeedback);
    UIViewController *controller = [UIViewController currentViewController];
    [controller.navigationController pushViewController:vc animated:YES];
}





#pragma mark - Delegate
- (UIStatusBarStyle)navControllerStatusBarStyle:(MRKBaseController *)viewController {
    if (self.vipType.intValue == 30){
        return UIStatusBarStyleLightContent;
    } else {
        return UIStatusBarStyleDarkContent;
    }
}

- (UIImage *)mrkNavigationBarLeftButtonImage:(UIButton *)leftButton navigationBar:(MRKNavigationBar *)navigationBar {
    if (self.vipType.intValue == 30){
        return [UIImage imageNamed:@"icon_back-4"];
    } else {
        return [UIImage imageNamed:@"icon_back"];
    }
}

- (BOOL)viewControllerIsNeedNavBar:(MRKBaseController *)viewController {
    return YES;
}

- (BOOL)mrkNavigationBarIsHideBottomLine:(MRKNavigationBar *)navigationBar {
    return YES;
}

- (NSString *)mrkNavigationBarRightButtonTitle:(UIButton *)rightButton navigationBar:(MRKNavigationBar *)navigationBar {
    if (self.vipType.intValue == 30){
        [rightButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    } else {
        [rightButton setTitleColor:UIColorHex(#363A44) forState:UIControlStateNormal];
    }
    rightButton.titleLabel.font = kSystem_Font_NoDHPX(WKDHPX(14));
    return self.pageModel.renewModel.isOpen ? @"续费管理" : @"开通记录";
}

///开通记录
- (void)rightButtonEvent:(UIButton *)sender navigationBar:(MRKNavigationBar *)navigationBar {
    sender.traceEventId = @"btn_profile_vip_record";
    ///购买记录
    MRKRenewPagingController *vc = [[MRKRenewPagingController alloc] init];
    vc.renewModel = self.pageModel.renewModel;
    [self.navigationController pushViewController:vc animated:YES];
}

/** 背景色 */
- (UIColor *)mrkNavigationBarBackgroundColor:(MRKNavigationBar *)navigationBar{
    return [self.navBarColor colorWithAlphaComponent:0];
}

#pragma mark - UIScrollViewDelegate
- (void)scrollViewDidScroll:(UIScrollView *)scrollView{
    CGFloat contentOffsetY = scrollView.contentOffset.y;
    
    CGFloat ScrollValue = DHPX(250) - kNavBarHeight;
    CGFloat alpha = MAX(MIN(contentOffsetY / ScrollValue, 1), 0);
    [self changeNavBarStatus:alpha > 0.5 ];
    self.mrk_navgationBar.backgroundColor = [self.navBarColor colorWithAlphaComponent:alpha];
    
    ///
    if (scrollView.contentOffset.y >= self.purchaseViewPaddingTop && self.purchaseViewPaddingTop > 0) {
        [self.purchaseBottomView setHiddenAnimated:NO];
    }else{
        [self.purchaseBottomView setHiddenAnimated:YES];
    }
}

- (void)changeNavBarStatus:(BOOL)isChange {
    if (self.vipType.intValue == 30) { return; }
    if (self.hasChangeNavBarStatus == isChange) { return; }
    self.hasChangeNavBarStatus = isChange;
    
    dispatch_async(dispatch_get_main_queue(), ^{
        self.statusBarStyle = isChange ? UIStatusBarStyleLightContent : UIStatusBarStyleDefault;
    });
    
    self.navTitleColor = isChange ? UIColor.whiteColor : UIColorHex(#363A44);
    
    UIImage *leftImage = isChange ? [UIImage imageNamed:@"icon_back-4"] : [UIImage imageNamed:@"icon_back"];
    if ([self.mrk_navgationBar.leftView isKindOfClass:[UIButton class]]) {
        UIButton *btn = (UIButton *)self.mrk_navgationBar.leftView;
        [btn setImage:leftImage forState:UIControlStateNormal];
    }
    
    UIColor *titleColor = isChange ? UIColor.whiteColor : UIColorHex(#363A44);
    if ([self.mrk_navgationBar.rightView isKindOfClass:[UIButton class]]) {
        UIButton *btn = (UIButton *)self.mrk_navgationBar.rightView;
        [btn setTitleColor:titleColor forState:UIControlStateNormal];
    }
}

- (UIColor *)navBarColor {
    NSInteger viptype = self.vipType.intValue;
    switch (viptype) {
        case 10:
            return UIColorHex(#211E1C);
            break;
        case 30:
            return UIColorHex(#07112D);
            break;
        default:
            break;
    }
    return UIColor.whiteColor;
}


/*
 #pragma mark - Navigation
 
 // In a storyboard-based application, you will often want to do a little preparation before navigation
 - (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
 // Get the new view controller using [segue destinationViewController].
 // Pass the selected object to the new view controller.
 }
 */

@end
