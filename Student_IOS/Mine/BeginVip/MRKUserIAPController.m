//
//  MRKUserIAPController.m
//  Student_IOS
//
//  Created by <PERSON><PERSON> on 2023/11/6.
//

#import "MRKUserIAPController.h"
#import <StoreKit/StoreKit.h>
#import "DYFStoreManager.h"

#import "MRKSVipViews.h"
#import "MRKVipPackageView.h"
#import "MRKVipPageModel.h"

#import "MRKRestorePurchaseController.h"
#import "MRKRenewPagingController.h"

#import "MRKHomeAlertManager.h"
#import "AwesomeIntroGuideView.h"
#import "UIViewController+Addons.h"
#import "MRKSubscribeDiscountAlert.h"

@interface MRKUserIAPController ()<UIScrollViewDelegate>
@property (nonatomic, strong) NSString *vipType; ///类型 10 vip  30 绝影
@property (nonatomic, strong) MRKVipPageModel *pageModel;

@property (nonatomic, strong) UIScrollView *baseScrollerView;
@property (nonatomic, strong) MRKIAPMessageView *messageView;
@property (nonatomic, strong) MRKPurchaseView *purchaseView;
@property (nonatomic, strong) MRKIAPBannerView *bannerView;
@property (nonatomic, strong) MRKIAPImageView *vipImageView;
@property (nonatomic, strong) MRKIAPBottomView *bottomTipView;

@property (nonatomic, strong) MRKVipPurchaseBottomView *purchaseBottomView;

@property (nonatomic, assign) int currentPurchaseIndex; ///标识购买索引
@property (nonatomic, strong) NSMutableArray *vipRulerArray;
@end

@implementation MRKUserIAPController

- (MRKVipPageModel *)pageModel{
    if(!_pageModel){
        _pageModel = [[MRKVipPageModel alloc] init];
    }
    return _pageModel;
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
}

- (void)viewDidAppear:(BOOL)animated{
    [super viewDidAppear:animated];
    
    ///刷新会员时长
    [self UpdateHeaderViewInfo];
    
    /// 请求弹窗
    [[MRKHomeAlertManager shareManager] addTarget:self checkAlertCode:MRKMemberPopupCode];
}

///刷新用户会员时长
- (void)UpdateHeaderViewInfo{
    @weakify(self);
    [self.pageModel getUserVipInfoRequest:^{
        @strongify(self);
        dispatch_async(dispatch_get_main_queue(), ^{
            [self.messageView configPageModel:self.pageModel vipType:self.vipType];
        });
    }];
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
}

- (void)dealloc{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}


- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    
    self.navTitle = @"会员中心";
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(UpdateUserInfo) name:@"UpdateUserInfo" object:nil];
    
    ///统计曝光
    @weakify(self);
    [[[RACObserve(self, vipType) ignore:NULL]  distinctUntilChanged] subscribeNext:^(id x) {
        @strongify(self);
        NSString *member_type_id = @"";
        int type = [x intValue];
        switch (type) {
            case 10: case 30:
                member_type_id = @(type).stringValue;
                break;
            default:  break;
        }
        
        if  ([member_type_id isNotBlank]) {
            ReportMrkLogParms(1, @"VIP页面", @"page_profile_vip", nil, self.tracePageRoute, 0, @{@"member_type_id":member_type_id});
        }
    }];

    self.vipType = UserInfo.vipType == 30 ? @"30" : @"10";
    
    RAC(self.pageModel, vipType) = RACObserve(self, vipType);
    RAC(self, vipRulerArray) = RACObserve(self.pageModel, vipRulerArray);
    [[[RACObserve(self.purchaseView, page) ignore:NULL] distinctUntilChanged] subscribeNext:^(id  _Nullable x) {
        @strongify(self);
        NSLog(@"self.vipPackageView.page === %@",x);
        self.currentPurchaseIndex = [x intValue];
        
        NSAttributedString *tipPurchaseStr = [self refershTipPurchaseStr];
        self.purchaseView.tipPurchaseStr = tipPurchaseStr;
        self.purchaseBottomView.tipPurchaseStr = tipPurchaseStr;
    }];
    
    [self layoutViewUI];
    [self configSignal];
    
    
    /// 请求弹窗
    [[MRKHomeAlertManager shareManager] addTarget:self requestAlertData:MRKMemberPopupCode];
}


- (void)configSignal {
    @weakify(self);
    [self.view beginLoading];
    self.baseScrollerView.alpha = 0.0;
    [self.pageModel.updateVipSignal subscribeNext:^(id x) {
        @strongify(self);
        [self.view endLoading];
        
        NSLog(@"self.pageModel.updateTrainingSignal========%@",x);
        [self reloadPageData];
        
        dispatch_async(dispatch_get_main_queue(), ^{
            [UIView animateWithDuration:0.3 animations:^{
                self.baseScrollerView.alpha = 1.0f;
            } completion:^(BOOL finished) {
                [self maskLayerHolder];
            }];
        });
    }];
    
    [self.pageModel refreshDataSource];
}

- (void)reloadPageData{
    self.vipRulerArray = self.pageModel.vipRulerArray;
    
    NSAttributedString *tipPurchaseStr = [self refershTipPurchaseStr];
    self.purchaseView.tipPurchaseStr = tipPurchaseStr;
    self.purchaseBottomView.tipPurchaseStr = tipPurchaseStr;
    
    [self.messageView configPageModel:self.pageModel vipType:self.vipType];
    [self.purchaseView configPageModel:self.pageModel vipType:self.vipType];
    [self.bannerView configPageModel:self.pageModel vipType:self.vipType];
    [self.vipImageView configPageModel:self.pageModel vipType:self.vipType];
    [self.bottomTipView configPageModel:self.pageModel vipType:self.vipType];
}

- (void)maskLayerHolder{
    User *user = [Login curLoginUser];
    NSString *expireDate = user.memberInfo.expireDate;
    ///七月说针对未付过费的新用户
    if ([expireDate isNotBlank]) return;
    
    CGRect pointViewRect = [self.view convertRect:self.purchaseView.frame toView:[UIApplication mrk_keyWindow]];
    if (pointViewRect.origin.y < 0 || pointViewRect.size.width == 0 || pointViewRect.size.height == 0) {
        return;
    }
    
    NSString *userDefaultsKey = [NSString stringWithFormat:@"UserVipInAppPurchase_%@", UserInfo.userId];
    
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    BOOL RegulateTip = [userDefaults boolForKey:userDefaultsKey];
    if (!RegulateTip){
        [userDefaults setBool:YES forKey:userDefaultsKey];
        [userDefaults synchronize];
        
        float normalPaddingTop = (MainHeight - pointViewRect.size.height)/2;
        float paddingTop = 0;
        ///需要滚动到中心点
        if (CGRectGetMinY(pointViewRect) > normalPaddingTop){
            CGPoint off = self.baseScrollerView.contentOffset;
            off.y += CGRectGetMinY(pointViewRect) - normalPaddingTop;
            [self.baseScrollerView setContentOffset:off animated:YES];
            
            paddingTop = CGRectGetMinY(pointViewRect) - normalPaddingTop;
        }
        
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            AwesomeIntroGuideView *marksView = [[AwesomeIntroGuideView alloc] initWithFrame:CGRectMake(0, 0, MainWidth, MainHeight)];
            marksView.guideShape = AwesomeIntroGuideShape_Other;
            marksView.maskColor = RGBA(0, 0, 0, 0.7);
            marksView.autoCalculateGuidePoint = NO;
            marksView.cutoutRadius = 16;
            marksView.animationDuration = 0.3;
            marksView.enableSkipButton = NO;
            marksView.enableTap = YES;
            marksView.enableAnimationShape = YES;
            [[UIApplication mrk_keyWindow] addSubview:marksView];
            
            CGRect rect = CGRectMake((MainWidth-210)/2, CGRectGetMinY(pointViewRect) - 40, 210, 30);
            [marksView loadMarks:@[self.purchaseView]];
            [marksView loadGuideImageItem:@[@{@"image":[UIImage imageNamed:@"vip_guide_tip"],
                                              @"point": [NSValue valueWithCGPoint:CGPointMake(CGRectGetMinX(rect), CGRectGetMinY(rect) -paddingTop)]}]];
            [marksView start];
        });
    }
}




///更新信息
- (void)UpdateUserInfo{
    @weakify(self);
    ///刷新用户会员时长
    [self.pageModel getUserVipInfoRequest:^{
        @strongify(self);
        dispatch_async(dispatch_get_main_queue(), ^{
            [self.messageView configPageModel:self.pageModel vipType:self.vipType];
        });
    }];
    
    ///刷新产品数据
    [self.pageModel userProductRequest:^{
        @strongify(self);
        dispatch_async(dispatch_get_main_queue(), ^{
            [self.bottomTipView configPageModel:self.pageModel vipType:self.vipType];
        });
    }];
}


- (void)layoutViewUI {
    
    self.baseScrollerView.delegate = self;
    [self.mrkContentView addSubview:self.baseScrollerView];
    [self.baseScrollerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsZero);
    }];
    
    
    { ///add subview
        [self.baseScrollerView addSubview:self.messageView];
        [self.messageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.baseScrollerView.mas_top).offset(0);
            make.left.mas_equalTo(self.baseScrollerView.mas_left);
            make.centerX.mas_equalTo(self.mrkContentView.mas_centerX);
            make.width.mas_equalTo(RealScreenWidth);
        }];
        
        [self.baseScrollerView addSubview:self.purchaseView];
        [self.purchaseView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.messageView.mas_bottom);
            make.left.mas_equalTo(self.baseScrollerView.mas_left);
            make.width.mas_equalTo(RealScreenWidth);
        }];
        
        [self.baseScrollerView addSubview:self.bannerView];
        [self.bannerView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.purchaseView.mas_bottom);
            make.left.mas_equalTo(self.baseScrollerView.mas_left);
            make.width.mas_equalTo(RealScreenWidth);
        }];
        
        [self.baseScrollerView addSubview:self.vipImageView];
        [self.vipImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.bannerView.mas_bottom);
            make.left.mas_equalTo(self.baseScrollerView.mas_left);
            make.width.mas_equalTo(RealScreenWidth);
        }];
        
        [self.baseScrollerView addSubview:self.bottomTipView];
        [self.baseScrollerView insertSubview:self.bottomTipView aboveSubview:self.vipImageView];
        [self.bottomTipView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.vipImageView.mas_bottom).offset(0);
            make.left.mas_equalTo(self.baseScrollerView.mas_left);
            make.width.mas_equalTo(RealScreenWidth);
            make.bottom.mas_equalTo(0);
        }];
    }
//    make.height.mas_equalTo(self.purchaseBtn.mas_width).multipliedBy(0.17);
    
    
    [self.baseScrollerView addSubview:self.purchaseBottomView];
    [self.baseScrollerView bringSubviewToFront:self.purchaseBottomView];
    [self.purchaseBottomView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.mas_equalTo(self.mrkContentView.mas_centerX);
        make.bottom.mas_equalTo(self.mrkContentView.mas_bottom);
        make.width.mas_equalTo(self.mrkContentView.mas_width);
        make.height.mas_equalTo(WKDHPX(130));
    }];
    MrkCornerMaskWithViewRadius(self.purchaseBottomView, ViewRadiusMake(8, 8, 0, 0));
}


- (UIScrollView *)baseScrollerView{
    if (!_baseScrollerView) {
        _baseScrollerView = [[UIScrollView alloc] init];
        _baseScrollerView.pagingEnabled = NO;
        _baseScrollerView.backgroundColor = [UIColor whiteColor];
        _baseScrollerView.showsVerticalScrollIndicator = NO;
    }
    return _baseScrollerView;
}

///Vip 信息
- (MRKIAPMessageView *)messageView{
    if (!_messageView){
        _messageView = [[MRKIAPMessageView alloc] init];
    }
    return _messageView;
}

///会员购买项
- (MRKPurchaseView *)purchaseView{
    if (!_purchaseView){
        _purchaseView = [[MRKPurchaseView alloc] init];
        @weakify(self);
        _purchaseView.vipProtocolClickBlock = ^{
            [self_weak_ autoRenewProtocal];
        };
    }
    return _purchaseView;
}

/// banner
- (MRKIAPBannerView *)bannerView{
    if (!_bannerView){
        _bannerView = [[MRKIAPBannerView alloc] init];
    }
    return _bannerView;
}

/// banner
- (MRKIAPImageView *)vipImageView{
    if (!_vipImageView){
        _vipImageView = [[MRKIAPImageView alloc] init];
    }
    return _vipImageView;
}

///Vip底部显示
- (MRKIAPBottomView *)bottomTipView{
    if (!_bottomTipView){
        _bottomTipView = [[MRKIAPBottomView alloc] init];
        @weakify(self);
        _bottomTipView.restorePurchaseBlock = ^{
            @strongify(self);
            MRKRestorePurchaseController *vc = [[MRKRestorePurchaseController alloc] init];
            [self.navigationController pushViewController:vc animated:YES];
            
            ///统计
            ReportMrkLogParms(2, @"去解决", @"page_profile_vip", @"btn_profile_vip_solve", nil, 0, nil);
        };
    }
    return _bottomTipView;
}

///购买按钮
- (MRKVipPurchaseBottomView *)purchaseBottomView{
    if (!_purchaseBottomView) {
        _purchaseBottomView = [[MRKVipPurchaseBottomView alloc] init];
        _purchaseBottomView.backgroundColor = UIColor.whiteColor;
        [_purchaseBottomView addTarget:self action:@selector(purchaseViewSelect) forControlEvents:UIControlEventTouchUpInside];
    }
    return _purchaseBottomView;
}

- (NSAttributedString *)refershTipPurchaseStr{
    ///如果没有购买项
    if (self.vipRulerArray.count <= 0){
        return nil;
    }
    ///如果 self.page数值异常
    if (self.currentPurchaseIndex >= self.vipRulerArray.count){
        return nil;
    }
    
    MRKVipCardModel *model = [self.vipRulerArray objectAtIndex:self.currentPurchaseIndex];
    
    NSMutableAttributedString *attributedStr = [[NSMutableAttributedString alloc] init];
    attributedStr.font = [UIFont systemFontOfSize:WKDHPX(14)];
    [attributedStr appendAttributedString:({
        NSString *price = model.actualAmount?:@"";
        if (model.isAuto && [model.firstAmount isNotBlank]){
            price = model.firstAmount;
        }
        
        NSMutableAttributedString *str = [[NSMutableAttributedString alloc] initWithString:@"¥"];
        str.font = [UIFont systemFontOfSize:WKDHPX(14)];
        NSMutableAttributedString *priceStr = [[NSMutableAttributedString alloc] initWithString:price];
        priceStr.font = [UIFont systemFontOfSize:WKDHPX(20) weight:UIFontWeightMedium];
        [str appendAttributedString:priceStr];
        str;
    })];
    
    [attributedStr appendAttributedString:({
        NSString *tip = [NSString stringWithFormat:@" 确认协议，%@%@ ", self.pageModel.isMember ? @"续费":@"开通" , model.name];
        NSMutableAttributedString *text = [[NSMutableAttributedString alloc] initWithString:tip];
        text.font = [UIFont systemFontOfSize:WKDHPX(14)];
        text;
    })];
  
    return attributedStr;
}

- (void)purchaseViewSelect {
    self.purchaseBottomView.traceEventId = self.pageModel.isMember ? @"btn_profile_vip_renew": @"btn_profile_vip_purchase";
    ///如果没有购买项
    if (self.vipRulerArray.count <= 0){
        return;
    }
    
    ///如果 self.page数值异常
    if (self.currentPurchaseIndex >= self.vipRulerArray.count){
        return;
    }
    
    NSArray *transactions = [SKPaymentQueue defaultQueue].transactions;
    DYFStoreLog(@"transactions ==== %@",transactions);
    
    MRKVipCardModel *model = [self.vipRulerArray objectAtIndex:self.currentPurchaseIndex];
    [self fetchesPaymentCode:model.appStoreCode ];
}

/// Strategy 1:
///  - Step 1: Requests localized information about a product from the Apple App Store.
///  - Step 2: Adds payment of the product with the given product identifier.
- (void)fetchesPaymentCode:(NSString *)productId {
    ///You need to check whether the device is not able or allowed to make payments before requesting product.
    if (![DYFStore canMakePayments]) {
        [self showTipsMessage:@"当前手机设备不支持或者未授权购买"];
        return;
    }
    
    MLog(@"_开始内购 ====productId:%@ ", productId);
    [self showLoading:@"加载中..."];
    [DYFStore.defaultStore requestProductWithIdentifier:productId success:^(NSArray *products, NSArray *invalidIdentifiers) {
        [self hideLoading];
        if (products.count == 1) {
            NSString *productId = ((SKProduct *)products[0]).productIdentifier;
            [self addPayment:productId];
            MLog(@"_开始内购 block ====productId:%@ ",  productId);
        } else {
            [self showTipsMessage:@"暂无此项购买销售"];
            MLog(@"_内购失败 ==== 暂无此项购买销售 ");
        }
    } failure:^(NSError *error) {
        
        [self hideLoading];
        
        NSString *value = error.userInfo[NSLocalizedDescriptionKey];
        NSString *msg = value ?: error.localizedDescription;
        // This indicates that the product cannot be fetched, because an error was reported.
        [self sendNotice:[NSString stringWithFormat:@"出现错误, %zi, %@", error.code, msg]];
        
        MLog(@"_内购失败 ==== 出现错误, 错误码:%zi , %@ ", error.code, msg);
    }];
}

- (void)addPayment:(NSString *)productId{
    // Get account name from your own user system.
    NSString *userIdentifier = UserInfo.userId;
    
    // This algorithm is negotiated with server developer.
//    NSString *userIdentifier = DYF_SHA256_HashValue(accountName);
    DYFStoreLog(@"userIdentifier: %@", userIdentifier);
    [DYFStoreManager.shared addPayment:productId userIdentifier:userIdentifier];
}


- (void)sendNotice:(NSString *)message {
    [self showAlertWithTitle:@"提示"
                     message:message
           cancelButtonTitle:nil
                      cancel:NULL
          confirmButtonTitle:@"知道了"
                     execute:^(UIAlertAction *action) {
        DYFStoreLog(@"Alert action title: %@", action.title);
    }];
}




///会员协议
- (void)autoRenewProtocal {
    MRKBaseController *base = (MRKBaseController *)[UIViewController currentViewController];
    [[MRKTraceManager sharedInstance] manualUploadTraceType:2 pageTitle:base.navTitle pageId:base.tracePageId eventId:@"btn_profile_vip_services_vip" route:base.tracePageRoute duration:0 extendPara:@{}];
    
    WebViewViewController *vc = [WebViewViewController new];
    vc.htmlURL = MRKAppH5LinkCombine(MRKLinkAutoRenewalProtocol);
    vc.titleString = @"会员服务协议";
    MRKNavigationController *nav = [[MRKNavigationController alloc] initWithRootViewController:vc];
    nav.modalPresentationStyle = UIModalPresentationFullScreen;
    [[UIViewController currentViewController] presentViewController:nav animated:YES completion:nil];
}

///用户协议
- (void)userProtocol {
    WebViewViewController *vc = [WebViewViewController new];
    vc.htmlURL = MRKAppH5LinkCombine(MRKLinkUserProtocol);
    vc.titleString = @"用户协议";
    MRKNavigationController *nav = [[MRKNavigationController alloc] initWithRootViewController:vc];
    nav.modalPresentationStyle = UIModalPresentationFullScreen;
    [[UIViewController currentViewController] presentViewController:nav animated:YES completion:nil];
}

///隐私政策
- (void)privacyPolicy {
    WebViewViewController *vc = [WebViewViewController new];
    vc.htmlURL = MRKAppH5LinkCombine(MRKLinkPrivacyProtocol);
    vc.titleString = @"隐私政策";
    MRKNavigationController *nav = [[MRKNavigationController alloc] initWithRootViewController:vc];
    nav.modalPresentationStyle = UIModalPresentationFullScreen;
    [[UIViewController currentViewController] presentViewController:nav animated:YES completion:nil];
}

///客服
- (void)customerService {
    WebViewViewController *vc = [WebViewViewController new];
    vc.isHiddenNav = YES;
    vc.htmlURL = MRKAppH5LinkCombine(MRKUserHelpAndFeedback);
    UIViewController *controller = [UIViewController currentViewController];
    [controller.navigationController pushViewController:vc animated:YES];
}




















- (BOOL)viewControllerIsNeedNavBar:(MRKBaseController *)viewController {
    return YES;
}

- (BOOL)mrkNavigationBarIsHideBottomLine:(MRKNavigationBar *)navigationBar {
    return YES;
}

- (NSString *)mrkNavigationBarRightButtonTitle:(UIButton *)rightButton navigationBar:(MRKNavigationBar *)navigationBar {
    [rightButton setTitleColor:[UIColor colorWithHexString:@"#363A44"] forState:UIControlStateNormal];
    rightButton.titleLabel.font = kSystem_Font_NoDHPX(WKDHPX(14));
    return self.pageModel.renewModel.isOpen ? @"续费管理" : @"开通记录";
}

///开通记录
- (void)rightButtonEvent:(UIButton *)sender navigationBar:(MRKNavigationBar *)navigationBar {
    sender.traceEventId = @"btn_profile_vip_record";
    ///购买记录
//    MRKRenewPagingController *vc = [[MRKRenewPagingController alloc] init];
//    vc.renewModel = self.pageModel.renewModel;
//    [self.navigationController pushViewController:vc animated:YES];
    
    
    MRKSubscribeDiscountAlert *alert = [[MRKSubscribeDiscountAlert alloc] initWithAnimationStyle:MRKActionAlertViewTransitionStyleBounce];
    [alert show];
    
}

/** 背景色 */
- (UIColor *)mrkNavigationBarBackgroundColor:(MRKNavigationBar *)navigationBar{
    return  [[UIColor colorWithHexString:self.navBarColorStr] colorWithAlphaComponent:0];
}

#pragma mark - UIScrollViewDelegate
- (void)scrollViewDidScroll:(UIScrollView *)scrollView{
    if (scrollView.contentOffset.y > 64 + kStatusBarHeight) {
        self.mrk_navgationBar.backgroundColor = [UIColor colorWithHexString:self.navBarColorStr];
    } else {
        CGFloat alpha = scrollView.contentOffset.y / (64 + kStatusBarHeight);
        self.mrk_navgationBar.backgroundColor = [[UIColor colorWithHexString:self.navBarColorStr] colorWithAlphaComponent:alpha];
    }
}

- (NSString *)navBarColorStr {
    NSString *colorStr = @"#000000";
    NSInteger viptype = self.vipType.intValue;
    switch (viptype) {
        case 10: case 20:
            colorStr = @"#FFF4E5";
            break;
        case 30:
            colorStr = @"#07112D";
            break;
        default:
            break;
    }
    return colorStr;
}


/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/

@end
