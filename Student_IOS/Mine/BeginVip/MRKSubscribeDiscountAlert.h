//
//  MRKSubscribeDiscountAlert.h
//  
//
//  Created by <PERSON><PERSON> on 2023/11/2.
//

#import <UIKit/UIKit.h>
#import "MRKAlertView.h"
#import "MRKVipCardModel.h"



NS_ASSUME_NONNULL_BEGIN

@interface MRKSubscribeDiscountAlert : MRKAlertView
@property (nonatomic, strong) MRKVipCardModel *defaultModel;
@property (nonatomic, strong) NSMutableArray *vipUpdateRulerArray;
@property (nonatomic, copy) void(^vipUpdateIndexBlock)(NSInteger index); ///选中回调
@end




@interface MRKSubscribeDiscountView : UIView
@property (nonatomic, strong) MRKVipCardModel *model;
@property (nonatomic, strong) UILabel *averageDailyLab;
@property (nonatomic, strong) UIImageView *selectIcon;
@end

NS_ASSUME_NONNULL_END
