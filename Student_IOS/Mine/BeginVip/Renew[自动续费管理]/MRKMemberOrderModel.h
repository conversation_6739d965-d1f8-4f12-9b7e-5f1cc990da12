//
//  MRKMemberOrderModel.h
//  Student_IOS
//
//  Created by merit on 2023/1/4.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface MRKMemberOrderModel : NSObject
@property (nonatomic, assign) int vipType;              //会员类型 :   10-VIP   20-SVIP[废弃]   30-XVIP(绝影)
@property (nonatomic, copy) NSString *openType;         //开通方式
@property (nonatomic, copy) NSString *openTime;         //开通时间：yyyy-MM-dd HH:mm:ss
@property (nonatomic, copy) NSString *memberTime;       //会员时间：yyyy-MM-dd HH:mm:ss
@property (nonatomic, assign) NSInteger days;           //开通天数
@end

NS_ASSUME_NONNULL_END
