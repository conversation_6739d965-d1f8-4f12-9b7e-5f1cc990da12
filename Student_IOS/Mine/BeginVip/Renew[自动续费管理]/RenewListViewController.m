//
//  RenewListViewController.m
//  Student_IOS
//
//  Created by merit on 2023/1/4.
//

#import "RenewListViewController.h"
#import <MJRefresh/MJRefresh.h>
#import "UIWindow+SIUtils.h"
#import "MRKMemberOrderModel.h"
#import "MJDIYAutoFooter.h"


static NSString * const RenewListIdentifier = @"RenewListTableViewCell";

@interface RenewListViewController () <UITableViewDataSource, UITableViewDelegate>
@property (nonatomic, copy) void(^scrollCallback)(UIScrollView *scrollView);
@property (nonatomic, assign) NSInteger pageIndex;
@property (nonatomic, strong) NSMutableArray *dataSource;
@end

@implementation RenewListViewController

- (void)dealloc {
    NSLog(@"ListViewController dealloced");
}

- (NSMutableArray *)dataSource {
    if (!_dataSource){
        _dataSource = [NSMutableArray array];
    }
    return _dataSource;
}

- (void)viewDidLoad {
    self.tracePageId = @"page_profile_vip_record";
    [super viewDidLoad];

    _pageIndex = 1;
    
    _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
    _tableView.backgroundColor = [UIColor whiteColor];
    _tableView.tableFooterView = [UIView new];
    _tableView.dataSource = self;
    _tableView.delegate = self;
    _tableView.separatorStyle = UITableViewCellSeparatorStyleSingleLine;
    _tableView.separatorColor = LineColor;
    [_tableView registerClass:[RenewListTableViewCell class] forCellReuseIdentifier:RenewListIdentifier];
    //列表的contentInsetAdjustmentBehavior失效，需要自己设置底部inset
    _tableView.contentInset = UIEdgeInsetsMake(0, 0, UIApplication.sharedApplication.keyWindow.jx_layoutInsets.bottom, 0);
    [self.view addSubview:_tableView];

    __weak typeof(self)weakSelf = self;
    MJDIYAutoFooter *footer = [MJDIYAutoFooter footerWithRefreshingBlock:^{
        [weakSelf requestPageData];
    }];
    footer.automaticallyChangeAlpha = YES;
    self.tableView.mj_footer = footer;

    
    MrkEmptyView *emptyView = [MrkEmptyView emptyViewWithImage:[UIImage imageNamed:@"icon_notes_holder"]
                                                      titleStr:@""
                                                     detailStr:@"暂无开通记录"];
    self.tableView.pageEmptyView = emptyView;
    
    [MBProgressHUD showLodingWithMessage:@"" view:self.view];
    [self requestPageData];
}

- (void)requestPageData{
    
    NSDictionary *parm = @{
        @"current":@(_pageIndex),
        @"size":@10
    };
    
    [MRKBaseRequest mrkRequestType:YTKRequestMethodGET
                               url:@"/user/member-product/query/member-order/record"
                           andParm:parm.mutableCopy
          completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        [MBProgressHUD hideHUDForView:self.view];
        [self.tableView.mj_footer endRefreshing];
        
        ///首次加载
        if (self.pageIndex == 1){
            [self.dataSource removeAllObjects];
        }
        
        id data = [request.responseObject valueForKeyPath:@"data.records"];
        NSArray *arr = [NSArray modelArrayWithClass:[MRKMemberOrderModel class] json:data];
        [self.dataSource addObjectsFromArray:arr];
        [self.tableView reloadData];
       
        int total = [[request.responseObject valueForKeyPath:@"data.total"] intValue];
        if (total > self.dataSource.count){
            self.pageIndex ++;
            self.tableView.mj_footer.hidden = NO;
        }else{
            self.tableView.mj_footer.hidden = YES;
        }
        [self.tableView hiddenEmptyView:self.dataSource.count > 0];
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        [MBProgressHUD hideHUDForView:self.view];
        [self.tableView.mj_footer endRefreshing];
        
        if (self.dataSource.count <= 0){
            [self.tableView mrkShowNetworkErrorEmptyView];
        }
    }];
}


- (void)viewDidLayoutSubviews {
    [super viewDidLayoutSubviews];

    self.tableView.frame = self.view.bounds;
}

#pragma mark - UITableViewDataSource, UITableViewDelegate

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.dataSource.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    RenewListTableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:RenewListIdentifier];
    if (!cell) {
        cell = [[RenewListTableViewCell alloc]initWithStyle:UITableViewCellStyleValue1 reuseIdentifier:RenewListIdentifier];
    }
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    MRKMemberOrderModel *model = self.dataSource[indexPath.row];
    [cell cellWithModel:model];
    return cell;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return 128;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    
}

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    !self.scrollCallback ?: self.scrollCallback(scrollView);
}

#pragma mark - JXPagingViewListViewDelegate

- (UIView *)listView {
    return self.view;
}

- (UIScrollView *)listScrollView {
    return self.tableView;
}

- (void)listViewDidScrollCallback:(void (^)(UIScrollView *))callback {
    self.scrollCallback = callback;
}

- (void)listWillAppear {
    NSLog(@"%@:%@", self.title, NSStringFromSelector(_cmd));
}

- (void)listDidAppear {
    NSLog(@"%@:%@", self.title, NSStringFromSelector(_cmd));
}

- (void)listWillDisappear {
    NSLog(@"%@:%@", self.title, NSStringFromSelector(_cmd));
}

- (void)listDidDisappear {
    NSLog(@"%@:%@", self.title, NSStringFromSelector(_cmd));
}

@end




@interface RenewListTableViewCell ()
@property (nonatomic, strong) UIImageView *vipIconView;
@property (nonatomic, strong) UILabel *titleLab;
@property (nonatomic, strong) UILabel *timeLab;

@property (nonatomic, strong) UILabel *openTypeLab;
@property (nonatomic, strong) UILabel *beginTimeLab; /// 开通时间
@property (nonatomic, strong) UILabel *endTimeLab;  /// 有效期至
@end

@implementation RenewListTableViewCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self =[super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        
        [self addInnerView];
    }
    return self;
}

- (void)addInnerView {
    
    [self.contentView addSubview:self.vipIconView];
    [self.vipIconView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView.mas_left).offset(16);
        make.top.equalTo(self.contentView.mas_top).offset(16);
        make.width.height.mas_equalTo(16);
    }];
    
    [self.contentView addSubview:self.titleLab];
    [self.titleLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.vipIconView.mas_right).offset(4);
        make.centerY.equalTo(self.vipIconView.mas_centerY);
        make.height.mas_equalTo(17);
        make.width.mas_equalTo(200);
    }];
    
    [self.contentView addSubview:self.timeLab];
    [self.timeLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.contentView.mas_right).offset(-16);
        make.centerY.equalTo(self.vipIconView.mas_centerY);
    }];
    
    [self.contentView addSubview:self.endTimeLab];
    [self.endTimeLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentView.mas_top).offset(47);
        make.left.equalTo(self.contentView.mas_left).offset(16);
        make.width.mas_equalTo(200);
        make.height.mas_equalTo(20);
    }];
    
    [self.contentView addSubview:self.openTypeLab];
    [self.openTypeLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.endTimeLab.mas_bottom).offset(12);
        make.left.equalTo(self.contentView.mas_left).offset(16);
        make.width.mas_equalTo(200);
        make.height.mas_equalTo(16);
    }];
    
    [self.contentView addSubview:self.beginTimeLab];
    [self.beginTimeLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.openTypeLab.mas_bottom).offset(4);
        make.left.equalTo(self.contentView.mas_left).offset(16);
        make.width.mas_equalTo(200);
        make.height.mas_equalTo(16);
    }];
}

- (void)cellWithModel:(MRKMemberOrderModel *)model {
    switch (model.vipType) {
        case 30:
            self.vipIconView.image = [UIImage imageNamed:@"record_xenjoy"];
            self.titleLab.text = @"绝影会员";
            break;
        default:
            self.vipIconView.image = [UIImage imageNamed:@"record_vip"];
            self.titleLab.text = @"趣练VIP";
            break;
    }
    
    self.timeLab.text = model.days > 0 ? [NSString stringWithFormat:@"+%ld天", model.days] : @"";
    self.openTypeLab.text = [NSString stringWithFormat:@"开通方式: %@", model.openType];
    self.endTimeLab.attributedText = ({
        NSMutableAttributedString *str = [[NSMutableAttributedString alloc] initWithString:@"有效期至: "];
        str.font = [UIFont systemFontOfSize:12 weight:UIFontWeightMedium];
        
        NSMutableAttributedString *priceStr = [[NSMutableAttributedString alloc] initWithString:model.memberTime?:@""];
        priceStr.font = [UIFont fontWithName:Bebas_Font size:12];
        [str appendAttributedString:priceStr];
        str.color = [UIColor colorWithHexString:@"#4C5362"];
        str;
    });
    
    self.beginTimeLab.attributedText = ({
        NSMutableAttributedString *str = [[NSMutableAttributedString alloc] initWithString:@"操作时间: "];
        str.font = [UIFont fontWithName:fontNamePing size:11];
        
        NSMutableAttributedString *priceStr = [[NSMutableAttributedString alloc] initWithString:model.openTime?:@""];
        priceStr.font = [UIFont fontWithName:Bebas_Font size:11];
        [str appendAttributedString:priceStr];
        str.color = [UIColor colorWithHexString:@"#999999"];
        str;
    });
}

- (UIImageView *)vipIconView {
    if (!_vipIconView){
        _vipIconView = [[UIImageView alloc] init];
    }
    return _vipIconView;
}

- (UILabel *)titleLab {
    if (!_titleLab){
        _titleLab = [[UILabel alloc]init];
        _titleLab.textColor = [UIColor colorWithHexString:@"#4C5362"];
        _titleLab.font = [UIFont fontWithName:Bebas_Font size:16];
    }
    return _titleLab;
}

- (UILabel *)timeLab {
    if (!_timeLab){
        _timeLab = [[UILabel alloc]init];
        _timeLab.textColor = [UIColor colorWithHexString:@"#4C5362"];
        _timeLab.font = [UIFont fontWithName:Bebas_Font size:14];
    }
    return _timeLab;
}

- (UILabel *)openTypeLab {
    if (!_openTypeLab){
        _openTypeLab = [[UILabel alloc]init];
        _openTypeLab.textColor = [UIColor colorWithHexString:@"#999999"];
        _openTypeLab.font = [UIFont fontWithName:fontNamePing size:11];
        _openTypeLab.text = @"开通方式: ";
    }
    return _openTypeLab;
}

- (UILabel *)beginTimeLab {
    if (!_beginTimeLab){
        _beginTimeLab = [[UILabel alloc]init];
        _beginTimeLab.textColor = [UIColor colorWithHexString:@"#666666"];
        _beginTimeLab.font = [UIFont fontWithName:fontNamePing size:13];
        _beginTimeLab.text = @"操作时间: ";
    }
    return _beginTimeLab;
}

- (UILabel *)endTimeLab {
    if (!_endTimeLab){
        _endTimeLab = [[UILabel alloc]init];
        _endTimeLab.textColor = [UIColor colorWithHexString:@"#4C5362"];
        _endTimeLab.font = [UIFont fontWithName:fontNameMeDium size:12];
        _endTimeLab.text = @"有效期至: ";
    }
    return _endTimeLab;
}


- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}

@end
