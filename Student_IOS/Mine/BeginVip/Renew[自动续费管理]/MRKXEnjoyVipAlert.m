
//
//  MRKXEnjoyVipAlert.m
//  Student_IOS
//
//  Created by <PERSON><PERSON> on 2023/8/28.
//

#import "MRKXEnjoyVipAlert.h"
#import "UIView+AZGradient.h"
#import "UIView+HyExtension.h"
#import "POP.h"


#pragma mark - AlertView

@interface MRKXEnjoyVipAlert ()
@property (nonatomic, strong) UIView *blackView;
@property (nonatomic, strong) UIView *messageView;

@property (nonatomic, strong) UIView *contentBackView;
@property (nonatomic, strong) UIImageView *backGroundView;
@property (nonatomic, strong) UIImageView *vipCardView;
@property (nonatomic, strong) UIImageView *vipPartView;

@property (nonatomic, strong) UILabel *titleLab;
@property (nonatomic, strong) UIButton *equitiesBtn;
@property (nonatomic, strong) UIButton *cancelBtn;
@end

@implementation MRKXEnjoyVipAlert

- (void)show {
    if (self.contentView) {
        [self.contentView addSubview:self];
        
        [self createBlackView];
        [self createMessageView];
    }
}

- (void)hide {
    [super hide];
    if (self.contentView) {
        [self removeViews];
    }
}

- (void)createBlackView {
    self.blackView = [[UIView alloc] init];
    self.blackView.backgroundColor = [UIColor colorWithWhite:0 alpha:0.4];
    [self addSubview:self.blackView];
    [self.blackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsMake(0, 0, 0, 0));
    }];
}

- (void)createMessageView {
    // 创建信息窗体view
    self.messageView = [[UIView alloc] init];
    self.messageView.size = CGSizeMake(DHPX(290), DHPX(440) + DHPX(50));
    self.messageView.backgroundColor = [UIColor clearColor];
    self.messageView.center = self.contentView.middlePoint;
    self.messageView.alpha = 0.f;
    [self addSubview:self.messageView];
    
    
    [self setupContainerSubViews];
    
    
    // 执行动画
    POPBasicAnimation  *alpha = [POPBasicAnimation animationWithPropertyNamed:kPOPViewAlpha];
    alpha.toValue             = @(1.f);
    alpha.duration            = 0.3f;
    [self.messageView pop_addAnimation:alpha forKey:nil];
    
    POPSpringAnimation *scale = [POPSpringAnimation animationWithPropertyNamed:kPOPLayerScaleXY];
    scale.fromValue           = [NSValue valueWithCGSize:CGSizeMake(1.1f, 1.1f)];
    scale.toValue             = [NSValue valueWithCGSize:CGSizeMake(1.f, 1.f)];
    scale.dynamicsTension     = 200;
    scale.dynamicsMass        = 1.3;
    scale.dynamicsFriction    = 10.3;
    scale.springSpeed         = 10;
    scale.springBounciness    = 10;
    [self.messageView.layer pop_addAnimation:scale forKey:nil];
    
    @weakify(self);
    scale.completionBlock = ^(POPAnimation *anim, BOOL finished) {
        @strongify(self);
        [self.messageView.subviews enumerateObjectsUsingBlock:^(__kindof UIView *obj, NSUInteger idx, BOOL * _Nonnull stop) {
            if ([obj isKindOfClass:[UIButton class]]) {
                UIButton *btn = obj;
                btn.userInteractionEnabled = YES;
            }
        }];
    };
}


- (void)removeViews {
    if (self.willDisappearBlock){
        self.willDisappearBlock(self);
    }
    [UIView animateWithDuration:0.2f animations:^{
        self.messageView.alpha     = 0.f;
        self.messageView.transform = CGAffineTransformMakeScale(0.9f, 0.9f);
    } completion:^(BOOL finished) {
        [self removeFromSuperview];
        if (self.didDisappearBlock){
            self.didDisappearBlock(self);
        }
    }];
}


- (void)setupContainerSubViews{
    [self.messageView addSubview:self.contentBackView];
    [self.contentBackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsZero);
    }];
    
    [self.contentBackView addSubview:self.backGroundView];
    [self.backGroundView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsMake(0, 0, DHPX(50), 0));
    }];
    
    [self.backGroundView addSubview:self.titleLab];
    [self.titleLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.backGroundView.mas_top).offset(DHPX(38));
        make.left.mas_equalTo(self.backGroundView.mas_left).offset(DHPX(32));
        make.width.mas_equalTo(DHPX(230));
    }];
    
    
    [self.backGroundView addSubview:self.vipPartView];
    [self.vipPartView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.backGroundView.mas_top).offset(DHPX(248));
        make.centerX.mas_equalTo(self.backGroundView);
        make.size.mas_equalTo(CGSizeMake(DHPX(268), DHPX(90)));
    }];
    
    
    [self.backGroundView addSubview:self.vipCardView];
    [self.vipCardView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.backGroundView.mas_top).offset(DHPX(150));
        make.centerX.mas_equalTo(self.backGroundView);
        make.size.mas_equalTo(CGSizeMake(DHPX(140), DHPX(85)));
    }];
    
  
    [self.backGroundView addSubview:self.equitiesBtn];
    [self.equitiesBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_equalTo(self.backGroundView.mas_bottom).offset(-DHPX(26));
        make.centerX.mas_equalTo(self.backGroundView);
        make.height.mas_equalTo(DHPX(40));
        make.width.mas_equalTo(DHPX(125));
    }];
    [self.equitiesBtn az_setGradientBackgroundWithColors:@[[UIColor colorWithHexString:@"#FCECC6"],
                                                           [UIColor colorWithHexString:@"#F5C1A4"]]
                                           locations:nil
                                          startPoint:CGPointMake(0, 0.5)
                                            endPoint:CGPointMake(1, 0.5)];
    

    [self.contentBackView addSubview:self.cancelBtn];
    [self.cancelBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.backGroundView.mas_bottom).offset(DHPX(12));
        make.centerX.mas_equalTo(self.backGroundView);
        make.height.mas_equalTo(DHPX(30));
        make.height.mas_equalTo(DHPX(30));
    }];

    
    self.vipCardView.layer.opacity = 0;
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self vipCardAnimation];
    });
}

- (void)layoutContainerViewSubViews{
    [self layoutIfNeeded];
}




- (UIView *)contentBackView {
    if (!_contentBackView) {
        _contentBackView = [[UIView alloc] init];
    }
    return _contentBackView;
}

- (UIImageView *)backGroundView{
    if (!_backGroundView) {
        _backGroundView = [[UIImageView alloc] init];
        _backGroundView.image = [UIImage imageNamed:@"popup_img2"];
        _backGroundView.contentMode = UIViewContentModeScaleAspectFill;
        _backGroundView.clipsToBounds = YES;
        _backGroundView.userInteractionEnabled = YES;
    }
    return _backGroundView;
}
- (UIImageView *)vipCardView{
    if (!_vipCardView) {
        _vipCardView = [[UIImageView alloc] init];
        _vipCardView.image = [UIImage imageNamed:@"popup_img_card"];
        _vipCardView.contentMode = UIViewContentModeScaleAspectFill;
        _vipCardView.clipsToBounds = YES;
    }
    return _vipCardView;
}
- (UIImageView *)vipPartView{
    if (!_vipPartView) {
        _vipPartView = [[UIImageView alloc] init];
        _vipPartView.image = [UIImage imageNamed:@"popup_img1"];
        _vipPartView.contentMode = UIViewContentModeScaleAspectFill;
        _vipPartView.clipsToBounds = YES;
    }
    return _vipPartView;
}

- (UILabel *)titleLab {
    if(!_titleLab) {
        UILabel *l = [UILabel new];
        l.textColor = [UIColor whiteColor];
        l.font = [UIFont systemFontOfSize:16 weight:UIFontWeightMedium];
        l.numberOfLines = 0;
        l.textAlignment = NSTextAlignmentLeft;
        //        l.lineBreakMode = NSLineBreakByCharWrapping;
        
        NSMutableAttributedString *text = [[NSMutableAttributedString alloc] initWithString:@"尊贵的 "];
        text.font = [UIFont systemFontOfSize:16];
        text.color = [UIColor whiteColor];
        
        NSString *name = [NSString stringWithFormat:@"%@\n", UserInfo.nickName];
        NSMutableAttributedString *nameText = [[NSMutableAttributedString alloc] initWithString:name];
        nameText.font = [UIFont systemFontOfSize:16 weight:UIFontWeightMedium];
        nameText.color = [UIColor colorWithHexString:@"#F9DCAC"];
        [text appendAttributedString:nameText];
        
        NSMutableAttributedString *Text1 = [[NSMutableAttributedString alloc] initWithString:@"您已获得专属 "];
        Text1.font = [UIFont systemFontOfSize:16];
        Text1.color = [UIColor whiteColor];
        [text appendAttributedString:Text1];
        
        NSMutableAttributedString *vipText = [[NSMutableAttributedString alloc] initWithString:@"绝影VIP"];
        vipText.font = [UIFont systemFontOfSize:16 weight:UIFontWeightMedium];
        vipText.color = [UIColor colorWithHexString:@"#F9DCAC"];
        [text appendAttributedString:vipText];
        
        text.lineSpacing = 10;
        l.attributedText = text;
        
        _titleLab = l;
    }
    return _titleLab;
}


- (UIButton *)equitiesBtn{
    if (!_equitiesBtn) {
        UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
        [btn setTitle:@"查看更多权益" forState:UIControlStateNormal];
        [btn setTitleColor:[UIColor colorWithHexString:@"#0D0D26"] forState:UIControlStateNormal];
        btn.titleLabel.font = [UIFont systemFontOfSize:15 weight:UIFontWeightMedium];
        [btn addTarget:self action:@selector(equitiesBtnClick:) forControlEvents:UIControlEventTouchUpInside];
        btn.layer.cornerRadius = DHPX(40)/2;
        btn.layer.masksToBounds = YES;
        _equitiesBtn = btn;
    }
    return _equitiesBtn;
}

- (void)equitiesBtnClick:(UIButton *)sender {
    if (self.XEnjoyVipClickBlock) {
        self.XEnjoyVipClickBlock();
    }
    [self hide];
}

- (UIButton *)cancelBtn{
    if (!_cancelBtn) {
        UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
        [btn setImage:[UIImage imageNamed:@"icon_close-8"] forState:UIControlStateNormal];
        btn.imageView.contentMode = UIViewContentModeScaleAspectFill;
        [btn addTarget:self action:@selector(ensureBtnClick:) forControlEvents:UIControlEventTouchUpInside];
        _cancelBtn = btn;
    }
    return _cancelBtn;
}

- (void)ensureBtnClick:(UIButton *)sender{
    [self hide];
}

- (void)vipCardAnimation {
    self.vipCardView.layer.opacity = 1.0;
    
    CABasicAnimation *rotation = [CABasicAnimation animationWithKeyPath:@"transform.rotation.y"];
    rotation.fromValue = [NSNumber numberWithFloat:0.f];
    rotation.toValue = [NSNumber numberWithFloat: M_PI *2];
    rotation.duration = 0.5;
    rotation.autoreverses = NO;
    rotation.cumulative = YES;
    rotation.fillMode = kCAFillModeForwards;
    rotation.repeatCount = 1;
    rotation.removedOnCompletion = YES;
    [self.vipCardView.layer addAnimation:rotation forKey:nil];
    
    CABasicAnimation *scale = [CABasicAnimation animationWithKeyPath:@"transform.scale"];
    scale.fromValue = [NSNumber numberWithFloat:0.5f];
    scale.toValue = [NSNumber numberWithFloat:1.0];
    scale.duration = 0.5;
    scale.fillMode = kCAFillModeForwards;
    scale.removedOnCompletion = YES;
    [self.vipCardView.layer addAnimation:scale forKey:nil];

    CAKeyframeAnimation *translation = [CAKeyframeAnimation animationWithKeyPath:@"transform.translation.y"];
    CGFloat height = 10;
    CGFloat currentY = self.vipCardView.transform.ty;
    translation.duration = 2.0;
    translation.values = @[@(currentY), @(currentY - height/4), @(currentY - height/4*2), @(currentY - height/4*3), @(currentY - height), @(currentY - height/ 4*3), @(currentY - height/4*2), @(currentY - height/4), @(currentY)];
    translation.keyTimes = @[@(0), @(0.025), @(0.085), @(0.2), @(0.5), @(0.8), @(0.915), @(0.975), @(1)];
    translation.timingFunction = [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionEaseInEaseOut];
    ///kCAFillModeForwards默认模式，动画开始前和结束后，动画对layer的状态没有影响。也就是说，动画开始前和结束后，layer都会处于添加动画前的状态。
    translation.fillMode = kCAFillModeForwards;
    translation.repeatCount = HUGE;
    translation.removedOnCompletion = NO;
    [self.vipCardView.layer addAnimation:translation forKey:@"translation"];
}

/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/

@end
