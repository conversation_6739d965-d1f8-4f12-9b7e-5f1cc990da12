//
//  MRKPagingTableHeaderView.m
//  Student_IOS
//
//  Created by merit on 2023/1/4.
//

#import "MRKPagingTableHeaderView.h"

@interface MRKPagingTableHeaderView()
@property (nonatomic, strong) UIImageView *bgImageView;
@property (nonatomic, strong) UIImageView *avatarBgImageView;
@property (nonatomic, strong) UIImageView *avatarImageView;
@property (nonatomic, strong) UILabel *nickLabel;
@property (nonatomic, strong) UILabel *timeLabel;
@property (nonatomic, assign) CGRect imageViewFrame;
@end

@implementation MRKPagingTableHeaderView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = [UIColor whiteColor];
        [self addSubview:self.bgImageView];
        [self addSubview:self.avatarBgImageView];
        [self addSubview:self.avatarImageView];
        [self addSubview:self.nickLabel];
        [self addSubview:self.timeLabel];
    }
    return self;
}

- (void)layoutSubviews {
    [super layoutSubviews];
    self.imageViewFrame = self.bounds;
    self.bgImageView.frame = self.bounds;
    
    [self.avatarBgImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        float paddingTop = kNavBarHeight + (100 - DHPX(76))/2;
        make.top.equalTo(self.mas_top).offset(paddingTop);
        make.left.equalTo(self.mas_left).offset(WKDHPX(16));
        make.size.mas_equalTo(CGSizeMake(DHPX(76), DHPX(76)));
    }];

    [self.avatarImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.avatarBgImageView);
        make.size.mas_equalTo(CGSizeMake(DHPX(40), DHPX(40)));
    }];
    
    [self.nickLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.avatarImageView.mas_right).offset(WKDHPX(25));
        make.right.equalTo(self.mas_right).offset(-WKDHPX(16));
        make.bottom.equalTo(self.avatarImageView.mas_centerY).offset(-WKDHPX(2));
    }];
    
    [self.timeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.avatarImageView.mas_right).offset(WKDHPX(25));
        make.right.equalTo(self.mas_right).offset(-WKDHPX(16));
        make.top.equalTo(self.avatarImageView.mas_centerY).offset(WKDHPX(2));
    }];
}

- (void)scrollViewDidScroll:(CGFloat)contentOffsetY {
    CGRect frame = self.imageViewFrame;
    frame.size.height -= contentOffsetY;
    frame.origin.y = contentOffsetY;
    self.bgImageView.frame = frame;
}

- (UIImageView *)bgImageView {
    if (!_bgImageView){
        _bgImageView = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"vip_renew_bg"]];
        _bgImageView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
        _bgImageView.clipsToBounds = YES;
        _bgImageView.contentMode = UIViewContentModeScaleAspectFill;
    }
    return _bgImageView;
}

- (UIImageView *)avatarBgImageView {
    if (!_avatarBgImageView){
        _avatarBgImageView = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"pic_ripple_vip"]];
        _avatarBgImageView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
        _avatarBgImageView.clipsToBounds = YES;
        _avatarBgImageView.contentMode = UIViewContentModeScaleAspectFill;
    }
    return _avatarBgImageView;
}

- (UIImageView *)avatarImageView {
    if (!_avatarImageView){
        _avatarImageView = [[UIImageView alloc] init];
        _avatarImageView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
        _avatarImageView.clipsToBounds = YES;
        _avatarImageView.contentMode = UIViewContentModeScaleAspectFill;
        [_avatarImageView sd_setImageWithURL:[NSURL URLWithString:UserInfo.avatar] placeholderImage:placeImage];
        _avatarImageView.layer.cornerRadius = DHPX(40)/2;
        _avatarImageView.layer.masksToBounds = YES;
    }
    return _avatarImageView;
}

- (UILabel *)nickLabel{
    if (!_nickLabel){
        _nickLabel = [[UILabel alloc] init];
        _nickLabel.font = [UIFont systemFontOfSize:16 weight:UIFontWeightMedium];
        _nickLabel.text = UserInfo.nickName;
        _nickLabel.textColor = [UIColor colorWithHexString:@"#FFD7B7"];
    }
    return _nickLabel;
}

- (UILabel *)timeLabel{
    if (!_timeLabel){
        _timeLabel = [[UILabel alloc] init];
        _timeLabel.font = [UIFont systemFontOfSize:12];
        _timeLabel.textColor = UIColorHex(#FFFFFF);
        _timeLabel.numberOfLines = 0;
        _timeLabel.text = @"";
    }
    return _timeLabel;
}


- (void)setModel:(MemberInfoDataDTO *)model {
    _model = model;
   
    if (model.isMember)
    {
        MemberInfoDataDTO *enjoyModel = [self getInfoModelWithType:@"30" withModel:model];
        if (!enjoyModel.isExpire) {
            self.timeLabel.attributedText = [self getAttributedStrWith:enjoyModel];
            return;
        }
        
        ///累加换行
        NSMutableAttributedString *totalAttributedStr = [[NSMutableAttributedString alloc] init];
        MemberInfoDataDTO *svipModel = [self getInfoModelWithType:@"20" withModel:model];
        if (!svipModel.isExpire) {
            [totalAttributedStr appendAttributedString:[self getAttributedStrWith:svipModel]];
        }
        
        MemberInfoDataDTO *vipModel = [self getInfoModelWithType:@"10" withModel:model];
        if (!vipModel.isExpire) {
            if(totalAttributedStr.length > 0) {
                [totalAttributedStr appendAttributedString:[[NSAttributedString alloc] initWithString:@"\n"]];
            }
            [totalAttributedStr appendAttributedString:[self getAttributedStrWith:vipModel]];
        }
        self.timeLabel.attributedText = totalAttributedStr;
    } else {
        
        if (model.vipType != 0 && [model.expireDate isNotBlank]) {
            self.timeLabel.text = @"会员已过期";
        }else {
            ///
            [self.nickLabel mas_updateConstraints:^(MASConstraintMaker *make) {
                make.centerY.equalTo(self.avatarImageView.mas_centerY);
            }];
        }
    }
}

- (MemberInfoDataDTO *)getInfoModelWithType:(NSString *)type withModel:(MemberInfoDataDTO *)model{
    __block MemberInfoDataDTO *infoModel = nil;
    [model.items enumerateObjectsUsingBlock:^(id  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        MemberInfoDataDTO *mod = (MemberInfoDataDTO *)obj;
        if (mod.vipType == type.intValue) {
            infoModel = mod;
            *stop = YES;
        }
    }];
    return infoModel;
}

- (NSMutableAttributedString *)getAttributedStrWith:(MemberInfoDataDTO *)model{
    NSString *part = @"";
    switch (model.vipType) {
        case 10:
            part = @"VIP";
            break;
        case 30:
            part = @"绝影VIP";
            break;
        default:
            break;
    }
    
    NSString *expireDate = model.expireDate?:@"";
    NSString *expireDateStr = [NSString stringWithFormat:@"%@有效期至: %@", part, model.expireDate];
    NSRange expireDateRang = [expireDateStr rangeOfString:expireDate];
    
    NSMutableAttributedString *attributedStr = [[NSMutableAttributedString alloc] initWithString:expireDateStr];
    attributedStr.color = [UIColor whiteColor];
    attributedStr.font = kSystem_Font_NoDHPX(WKDHPX(12));
    [attributedStr setColor:UIColorHex(#F0C59C) range:expireDateRang];
    
    if (model.days > 0) {
        NSString *days = @(model.days).stringValue;
        NSString *partStr = [NSString stringWithFormat:@"，还剩%@天", days];
        NSRange timeRang = [partStr rangeOfString:days];
        NSMutableAttributedString *text = [[NSMutableAttributedString alloc] initWithString:partStr];
        text.color = [UIColor whiteColor];
        text.font = kSystem_Font_NoDHPX(WKDHPX(12));
        [text setColor:UIColorHex(#F0C59C) range:timeRang];
        [attributedStr appendAttributedString:text];
    }
    
    return attributedStr;
}

/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/


@end
