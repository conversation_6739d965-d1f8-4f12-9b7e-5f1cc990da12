//
//  MRKAutoRenewController.m
//  Student_IOS
//
//  Created by merit on 2023/1/4.
//

#import "MRKAutoRenewController.h"
#import <MJRefresh/MJRefresh.h>
#import "UIWindow+SIUtils.h"
#import "MRKMemberOrderModel.h"
#import "MRKAutoRenewCancelAlert.h"


static NSString * const AutoRenewIdentifier = @"AutoRenewIdentifier";

@interface MRKAutoRenewController () <UITableViewDataSource, UITableViewDelegate>
@property (nonatomic, copy) void(^scrollCallback)(UIScrollView *scrollView);
@property (nonatomic, assign) NSInteger pageIndex;
@property (nonatomic, strong) NSMutableArray *dataSource;
@property (nonatomic, strong) NSMutableArray *titleArray;
@end

@implementation MRKAutoRenewController

- (void)dealloc {
    NSLog(@"ListViewController dealloced");
}

- (NSMutableArray *)dataSource {
    if (!_dataSource){
        _dataSource = [NSMutableArray array];
    }
    return _dataSource;
}

- (NSMutableArray *)titleArray {
    if (!_titleArray){
        _titleArray = [NSMutableArray arrayWithObjects:@"续费产品", @"下次续费金额", @"预计下次扣费时间", @"支付方式", nil];
    }
    return _titleArray;
}

- (void)viewDidLoad {
    self.tracePageId = @"page_profile_vip_renewal";
    [super viewDidLoad];

    _pageIndex = 1;
    
    _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
    _tableView.backgroundColor = [UIColor colorWithHexString:@"#F8F8FA"];
    _tableView.separatorStyle = UITableViewCellSeparatorStyleSingleLine;
    _tableView.separatorColor = LineColor;
    _tableView.delegate = self;
    _tableView.dataSource = self;
    _tableView.tableFooterView = [[UIView alloc] init];
//    [_tableView registerClass:[UITableViewCell class] forCellReuseIdentifier:AutoRenewIdentifier];
    //列表的contentInsetAdjustmentBehavior失效，需要自己设置底部inset
    _tableView.contentInset = UIEdgeInsetsMake(0, 0, UIApplication.sharedApplication.keyWindow.jx_layoutInsets.bottom, 0);
    [self.view addSubview:_tableView];
    
    MRKAutoRenewFooterView *footerView = [[MRKAutoRenewFooterView alloc] init];
    _tableView.tableFooterView = footerView;
}


- (void)setModel:(MRKAutoRenewModel *)model{
    _model = model;
    
    [self.tableView reloadData];
}


- (void)viewDidLayoutSubviews {
    [super viewDidLayoutSubviews];

    self.tableView.frame = self.view.bounds;
}



#pragma mark - UITableViewDataSource, UITableViewDelegate

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return 4;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return 57;
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section{
    return 0.1;
}

- (nullable UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section{
    return [[UIView alloc] initWithFrame:CGRectMake(0, 0, kScreenWidth, 0.1)];
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:AutoRenewIdentifier];
    if (!cell) {
        cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleValue1 reuseIdentifier:AutoRenewIdentifier];
        cell.selectionStyle = UITableViewCellSelectionStyleNone;
        cell.accessoryType = UITableViewCellAccessoryNone;
    }
    cell.textLabel.font = [UIFont systemFontOfSize:14];
    cell.textLabel.textColor = UIColorHex(#333333);
    cell.textLabel.text = self.titleArray[indexPath.row];
 
    cell.detailTextLabel.font = [UIFont systemFontOfSize:14];
    cell.detailTextLabel.textColor = UIColorHex(#666666);
    switch (indexPath.row) {
        case 0:
            cell.detailTextLabel.text = self.model.productName;
            break;
        case 1:{
            
            cell.detailTextLabel.attributedText = ({
                NSMutableAttributedString *attributedStr = [[NSMutableAttributedString alloc]initWithString:@""];
                
                NSMutableAttributedString *autoPriceUnit = [[NSMutableAttributedString alloc]initWithString:@"¥"];
                autoPriceUnit.color = UIColorHex(#FF5363);
                autoPriceUnit.font = [UIFont systemFontOfSize:12 weight:UIFontWeightMedium];
                [attributedStr appendAttributedString:autoPriceUnit];
                
                NSString *autoPriceStr = [NSString stringWithFormat:@"%@  ",self.model.autoPrice];
                NSMutableAttributedString *autoPrice = [[NSMutableAttributedString alloc]initWithString:autoPriceStr];
                autoPrice.color = UIColorHex(#FF5363);
                autoPrice.font = [UIFont systemFontOfSize:20 weight:UIFontWeightMedium];
                [attributedStr appendAttributedString:autoPrice];
                
                NSString *showPrice = [NSString stringWithFormat:@"原价¥ %@",self.model.showPrice];
                NSMutableAttributedString *normalPrice = [[NSMutableAttributedString alloc]initWithString:showPrice];
                normalPrice.color = UIColorHex(#666666);
                normalPrice.font = [UIFont systemFontOfSize:14];
                normalPrice.strikethroughStyle = NSUnderlineStyleSingle;
                normalPrice.strikethroughColor = UIColorHex(#666666);
                [attributedStr appendAttributedString:normalPrice];
                
                attributedStr;
            });
        }break;
        case 2:
            cell.detailTextLabel.text = self.model.autoDate;
            break;
        case 3:{
            NSString *payType = @"";
            switch (self.model.payType) {
                case 1:
                    payType = @"微信";
                    break;
                case 2:
                    payType = @"支付宝";
                    break;
                case 3:
                    payType = @"IAP支付";
                    break;
                case 4:
                    payType = @"PayPal";
                    break;
                default:
                    break;
            }
            
            cell.detailTextLabel.text = payType;
        } break;
        default:
            break;
    }
    
    return cell;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
}



- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    !self.scrollCallback ?: self.scrollCallback(scrollView);
}

#pragma mark - JXPagingViewListViewDelegate

- (UIView *)listView {
    return self.view;
}

- (UIScrollView *)listScrollView {
    return self.tableView;
}

- (void)listViewDidScrollCallback:(void (^)(UIScrollView *))callback {
    self.scrollCallback = callback;
}

- (void)listWillAppear {
    NSLog(@"%@:%@", self.title, NSStringFromSelector(_cmd));
}

- (void)listDidAppear {
    NSLog(@"%@:%@", self.title, NSStringFromSelector(_cmd));
}

- (void)listWillDisappear {
    NSLog(@"%@:%@", self.title, NSStringFromSelector(_cmd));
}

- (void)listDidDisappear {
    NSLog(@"%@:%@", self.title, NSStringFromSelector(_cmd));
}

@end






@interface MRKAutoRenewFooterView()
@property (nonatomic, strong) UILabel *titleLab;
@property (nonatomic, strong) YYLabel *contentLab;
@property (nonatomic, strong) UIButton *closeAutoBtn;
@end

@implementation MRKAutoRenewFooterView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        
        self.titleLab.frame = CGRectMake(DHPX(14), DHPX(20), DHPX(200), 22);
        [self addSubview:self.titleLab];
        
        self.contentLab.top = DHPX(20) + 22 + DHPX(14);
        [self addSubview:self.contentLab];
        
        NSMutableAttributedString *attributeText = [[NSMutableAttributedString alloc] initWithString:@"续订："];
        attributeText.color = UIColorHex(#333333);
        attributeText.font = [UIFont systemFontOfSize:13];
        
        NSMutableAttributedString *text1 = [[NSMutableAttributedString alloc] initWithString:@"自动续费商品包括“连续包月/连续包季/连续包年“，您确定购买后，会在您的会员到期前24小时通过您签约的支付方式自动发起续费扣款。扣费成功后，您的会员有效期自动延长一个周期。\n\n"];
        text1.color = UIColorHex(#999999);
        [attributeText appendAttributedString:text1];
        
        NSMutableAttributedString *text2 = [[NSMutableAttributedString alloc] initWithString:@"终止方法：\n"];
        text2.color = UIColorHex(#333333);
        [attributeText appendAttributedString:text2];
        
        NSMutableAttributedString *text3 = [[NSMutableAttributedString alloc] initWithString:@"苹果设备：“设置”>进入“iTunes Store/App Store” >点击“Apple ID”，在管理订阅中关闭自动续费的功能。\n\n"];
        text3.color = UIColorHex(#999999);
        [attributeText appendAttributedString:text3];
        
        NSMutableAttributedString *text4 = [[NSMutableAttributedString alloc] initWithString:@"温馨提示："];
        text4.color = UIColorHex(#333333);
        [attributeText appendAttributedString:text4];
        
        NSMutableAttributedString *text5 = [[NSMutableAttributedString alloc] initWithString:@"如未在会员到期前至少24小时关闭自动续费期，此服务将会自动续订。\n\n"];
        text5.color = UIColorHex(#999999);
        [attributeText appendAttributedString:text5];
        
        NSMutableAttributedString *text6 = [[NSMutableAttributedString alloc] initWithString:@"注："];
        text6.color = UIColorHex(#333333);
        [attributeText appendAttributedString:text6];
        
        NSMutableAttributedString *text7 = [[NSMutableAttributedString alloc] initWithString:@"虚拟商品购买后无法退换，敬请谅解！有任何疑问可添加福利官微信：merach88。"];
        text7.color = UIColorHex(#999999);
        [attributeText appendAttributedString:text7];

        YYTextContainer  *container = [YYTextContainer new];
        container.size = CGSizeMake(kScreenWidth - DHPX(14) *2, CGFLOAT_MAX);
        container.maximumNumberOfRows = 0;
        YYTextLayout *labLayout = [YYTextLayout layoutWithContainer:container text:attributeText];
        float labLayoutHeight = labLayout.textBoundingSize.height;
        
        self.contentLab.height = labLayoutHeight;
        self.contentLab.textLayout = labLayout;
        
        self.closeAutoBtn.top = self.contentLab.top + labLayoutHeight + 50;
        self.closeAutoBtn.left = (kScreenWidth -DHPX(280))/2;
        self.closeAutoBtn.size = CGSizeMake(DHPX(280), DHPX(44));
        [self addSubview:self.closeAutoBtn];
        
        
        self.height = self.closeAutoBtn.top + DHPX(44) + 20;
    }
    return self;
}

- (UILabel *)titleLab{
    if (!_titleLab){
        _titleLab = [[UILabel alloc] init];
        _titleLab.font = [UIFont systemFontOfSize:16 weight:UIFontWeightMedium];
        _titleLab.text = @"自动续费说明";
        _titleLab.textColor = UIColorHex(#333333);
    }
    return _titleLab;
}

- (YYLabel *)contentLab{
    if (!_contentLab){
        YYLabel *label = [[YYLabel alloc] init];
        label.textAlignment = NSTextAlignmentLeft;
        label.textVerticalAlignment = YYTextVerticalAlignmentTop;
        label.numberOfLines = 0;
        label.left = DHPX(14);
        label.width = kScreenWidth - DHPX(14)*2;
        label.displaysAsynchronously = YES;
        label.clearContentsBeforeAsynchronouslyDisplay = NO;
        label.preferredMaxLayoutWidth =  kScreenWidth - DHPX(14)*2;
        _contentLab = label;
    }
    return _contentLab;
}

- (UIButton *)closeAutoBtn{
    if (!_closeAutoBtn){
        _closeAutoBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        _closeAutoBtn.backgroundColor = UIColor.whiteColor;
        [_closeAutoBtn setTitleColor:UIColorHex(#999999) forState:UIControlStateNormal];
        [_closeAutoBtn setTitle:@"关闭自动续费服务" forState:UIControlStateNormal];
        _closeAutoBtn.titleLabel.font = [UIFont systemFontOfSize:14];
        _closeAutoBtn.layer.cornerRadius = DHPX(44)/2;
        _closeAutoBtn.layer.masksToBounds = YES;
        [_closeAutoBtn addTarget:self action:@selector(closeAutoRenew:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _closeAutoBtn;
}

- (void)closeAutoRenew:(UIButton *)sender{
    sender.traceEventId = @"btn_profile_vip_renewal_close";
    MRKAutoRenewCancelAlert *alert = [[MRKAutoRenewCancelAlert alloc] initWithAnimationStyle:MRKActionAlertViewTransitionStyleFade];
    [alert show];
}

- (void)layoutSubviews {
    [super layoutSubviews];
}

/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/


@end
