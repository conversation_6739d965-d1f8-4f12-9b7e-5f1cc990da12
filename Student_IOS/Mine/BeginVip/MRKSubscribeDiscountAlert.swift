//
//  MRKSubscribeDiscountAlert.swift
//  Student_IOS
//
//  Created by <PERSON><PERSON> on 2025/8/12.
//

import UIKit

/// 会员订阅折扣弹窗（Swift重构，布局用Masonry）
class MRKSubscribeDiscountAlert: MRKSwiftAlertView {
    
    private lazy var lottieView: MRKLottieView = {
        let view = MRKLottieView()
        view.animationName = "subscribe_animation"
        view.loopAnimationCount = 1
        return view
    }()
    
    private lazy var messageView: MRKSubscribeDiscountView = {
        let view = MRKSubscribeDiscountView()
        view.isHidden = true
        view.actionCloseSelectBlock = { [weak self] in
            self?.cancelAlertClick()
        }
        view.actionPurchaseSelectBlock = { [weak self] in
            self?.subscribePurchaseClick()
        }
        return view
    }()
    
    override func layoutContainerView() {
        self.isAutoHidden = true
        guard let baseSuperView = containerView.superview else { return }
        containerView.mas_makeConstraints { make in
            make?.centerY.equalTo()(baseSuperView.mas_centerY)
            make?.centerX.equalTo()(baseSuperView.mas_centerX)
            make?.width.mas_equalTo()(WKDHPX_S(295))
            make?.height.mas_equalTo()(WKDHPX_S(347))
        }
    }
    
    override func setupContainerViewAttributes() {
        containerView.backgroundColor = .clear
    }
    
    override func setupContainerSubViews() {
        containerView.addSubview(messageView)
        messageView.mas_makeConstraints { make in
            make?.centerX.equalTo()(containerView)
            make?.centerY.equalTo()(containerView)
            make?.size.mas_equalTo()(CGSize(width: WKDHPX_S(295), height: WKDHPX_S(347)))
        }
        
        containerView.addSubview(lottieView)
        lottieView.mas_makeConstraints { make in
            make?.centerX.equalTo()(containerView)
            make?.centerY.equalTo()(containerView)
            make?.size.mas_equalTo()(CGSize(width: WKDHPX_S(295), height: WKDHPX_S(257)))
        }
        
        

        ReportMrkLogParms(1, "会员中心抽奖弹窗曝光", "page_vip_center", nil, nil, 0, nil)
        disPlayLottieView()
    }
    
    
    /// 数据Model
    private var couponModel: MRKPurchaseCouponModel?
    
    /// 便利构造方法
    @objc public class func purchaseAlertView(withModel model: MRKPurchaseCouponModel) -> Self {
        let alert = self.init(animationStyle: .bounce)
        alert.opaqueness = 0.8
        alert.couponModel = model
        return alert
    }
    
    deinit {
        swapWorkItem?.cancel()
        TimerHandler.cancelTimer()
    }
    
    ///
    private func disPlayLottieView() {
        lottieView.isHidden = false
        if !lottieView.isAnimationPlaying, let actualCoupon = couponModel?.actualCoupon{
            let replacements = ["number" : actualCoupon]
            lottieView.replaceTexts(replacements as NSDictionary)
            lottieView.play { [weak self] in
                guard let self else { return }
                let workItem = DispatchWorkItem { [weak self] in
                    guard let self else { return }
                    self.swapView()
                }
                self.swapWorkItem = workItem
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.0, execute: workItem)
            }
        }
        
        messageView.couponModel = self.couponModel
        
        ///计算couponModel.expireTime 毫秒时间戳和当前时间的秒数差值来倒计时
        if let expireTimeStr = couponModel?.expireTime, let expireTimeMs = Double(expireTimeStr) {
            let now = Date().timeIntervalSince1970 // 当前秒级时间戳
            let expireSeconds = expireTimeMs / 1000.0
            let leftSeconds = Int(expireSeconds - now)
            if leftSeconds <= 0 {
                self.messageView.timerLab.isHidden = true
                return
            }
            
            /// 启动倒计时
            TimerHandler.startCountdown(totalSeconds: leftSeconds) { [weak self] seconds in
                guard let self else { return }
                // 格式化为 HH:mm:ss
                let hour = seconds / 3600
                let min = (seconds % 3600) / 60
                let sec = seconds % 60
                let timeStr = String(format: "%02d:%02d:%02d 后失效", hour, min, sec)
                self.messageView.timerLab.text = timeStr
                self.messageView.timerLab.isHidden = false
            } end: { [weak self] in
                guard let self else { return }
                self.messageView.timerLab.text = "00:00:00 后失效"
                self.messageView.timerLab.isHidden = true
            }
        }
    }
        
 
    private var swapWorkItem: DispatchWorkItem?
    
    ///旋转切换控件
    private func swapView() {
        UIView.transition(from: lottieView,
                          to: messageView,
                          duration: 0.5,
                          options: [.transitionFlipFromRight, .showHideTransitionViews]) { [weak self] finished in
            guard let self = self else { return }
            self.lottieView.isHidden = true
            self.messageView.isHidden = false
        }
    }
    
    override func layoutContainerViewSubViews() {
        self.layoutIfNeeded()
    }
    
    // MARK: - 事件
    func cancelAlertClick() {
        self.dismiss(animated: true)
    }
    
    func subscribePurchaseClick() {
        self.dismiss(animated: true)
    }
}

// MARK: - 折扣内容视图
class MRKSubscribeDiscountView: UIView {
    
    private lazy var titleLab: UILabel = {
        let label = UILabel()
        label.textColor = UIColor(hexString: "#FDE7C7")
        label.font = kMedium_PingFangSC(s: WKDHPX_S(40))
        label.text = "4折"
        return label
    }()
    
    private lazy var descripLab: UILabel = {
        let label = UILabel()
        label.textColor = UIColor(hexString: "#FDE7C7")
        label.font = kMedium_PingFangSC(s: WKDHPX_S(20))
        label.text = "恭喜抽中最低折扣！"
        return label
    }()
    
    private lazy var discountCardView: UIImageView = {
        let imgView = UIImageView()
        imgView.contentMode = .scaleAspectFit
        imgView.image = UIImage(named: "vip_price_discount_bg")
        imgView.isUserInteractionEnabled = true
        return imgView
    }()
    
    private lazy var subscribeTipLab: UILabel = {
        let label = UILabel()
        label.backgroundColor = UIColor.white.withAlphaComponent(0.16)
        label.textAlignment = .center
        label.textColor = UIColor(hexString: "#FFD5B6")
        label.font = kMedium_PingFangSC(s:WKDHPX_S(14))
        label.text = "包月立减"
        return label
    }()
    
    private lazy var priceLab: UILabel = {
        let label = UILabel()
        label.textAlignment = .center
        label.textColor = UIColor(hexString: "#FFD5B6")
        label.font = kBebasFont(s:WKDHPX_S(80))
        label.text = "¥25"
        return label
    }()
    
    public lazy var timerLab: UILabel = {
        let label = UILabel()
        label.backgroundColor = UIColor.white.withAlphaComponent(0.16)
        label.textAlignment = .center
        label.textColor = .white
        label.font = kRegular_PingFangSC(s:WKDHPX_S(12))
        label.text = "23:59:59 后失效"
        label.layer.cornerRadius = 4.0
        label.layer.masksToBounds = true
        return label
    }()
    
    private lazy var subscribeBtn: UIButton = {
        let btn = UIButton(type: .custom)
        btn.backgroundColor = UIColor(hexString: "#202121")
        btn.setTitle("立即使用", for: .normal)
        btn.setTitleColor(UIColor(hexString: "#FFD5B6"), for: .normal)
        btn.titleLabel?.font = kMedium_PingFangSC(s:WKDHPX_S(14))
        btn.layer.cornerRadius = WKDHPX_S(40)/2
        btn.layer.masksToBounds = true
        btn.addTarget(self, action: #selector(subscribeBtnClick(_:)), for: .touchUpInside)
        return btn
    }()
    
    private lazy var cancelBtn: UIButton = {
        let btn = UIButton(type: .custom)
        btn.setImage(UIImage(named: "icon_alert_close"), for: .normal)
        btn.addTarget(self, action: #selector(cancelBtnClick(_:)), for: .touchUpInside)
        return btn
    }()
    
    
    public var couponModel: MRKPurchaseCouponModel? {
        didSet {
            if let model = couponModel {
                titleLab.text = "\(model.actualCoupon ?? "")折"
                
                priceLab.attributedText = {
                    let amountString = model.amount ?? "--"
                    let str = NSMutableAttributedString(
                        string: "¥",
                        attributes: [.font: kBebasFont(s:WKDHPX_S(60))]
                    )
                    str.append(NSAttributedString(
                        string: amountString,
                        attributes: [.font: kBebasFont(s: WKDHPX_S(80))]
                    ))
                    return str
                }()
                
                
                if let packageType = model.packageType, packageType == "month" {
                    subscribeTipLab.text = "包月立减"
                } else if let packageType = model.packageType, packageType == "year" {
                    subscribeTipLab.text = "包年立减"
                } else {
                    subscribeTipLab.isHidden = true
                }
                
                subscribeBtn.setTitle("立即使用", for: .normal)
            }
        }
    }

    
    public var actionPurchaseSelectBlock : (() -> Void)?
    public var actionCloseSelectBlock : (() -> Void)?
    
    // MARK: - 初始化
    public override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    public required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }
    
    // MARK: - setupUI
    func setupUI() {
        addSubview(cancelBtn)
        cancelBtn.mas_makeConstraints { make in
            make?.centerX.equalTo()(self.mas_centerX)
            make?.bottom.equalTo()(self.mas_bottom)
            make?.width.height().mas_equalTo()(WKDHPX_S(36))
        }
        
        let backView = UIView()
        backView.backgroundColor = .clear
        addSubview(backView)
        backView.mas_makeConstraints { make in
            make?.centerX.equalTo()(self.mas_centerX)
            make?.top.equalTo()(self.mas_top)
            make?.height.mas_equalTo()(WKDHPX_S(64))
        }
        
        backView.addSubview(titleLab)
        titleLab.mas_makeConstraints { make in
            make?.centerY.equalTo()(backView.mas_centerY)
            make?.left.equalTo()(backView.mas_left)
            make?.height.mas_equalTo()(WKDHPX_S(64))
        }
        
        backView.addSubview(descripLab)
        descripLab.mas_makeConstraints { make in
            make?.centerY.equalTo()(backView.mas_centerY)
            make?.left.equalTo()(titleLab.mas_right)?.offset()(WKDHPX_S(5))
            make?.right.equalTo()(backView.mas_right)
            make?.height.mas_equalTo()(WKDHPX_S(64))
        }
        
        addSubview(discountCardView)
        discountCardView.mas_makeConstraints { make in
            make?.top.equalTo()(self.mas_top)?.offset()(WKDHPX_S(64))
            make?.left.equalTo()(self.mas_left)
            make?.right.equalTo()(self.mas_right)
            make?.height.mas_equalTo()(WKDHPX_S(206))
        }
        
        discountCardView.addSubview(subscribeTipLab)
        subscribeTipLab.mas_makeConstraints { make in
            make?.top.equalTo()(discountCardView.mas_top)
            make?.right.equalTo()(discountCardView.mas_right)?.offset()(-WKDHPX_S(7))
            make?.width.mas_equalTo()(WKDHPX_S(76))
            make?.height.mas_equalTo()(WKDHPX_S(25))
        }
        ///
        subscribeTipLab.layer.cornerRadius = WKDHPX_S(8)
        subscribeTipLab.layer.maskedCorners = [.layerMaxXMinYCorner, .layerMaxXMinYCorner]
        subscribeTipLab.clipsToBounds = true
        
        discountCardView.addSubview(priceLab)
        priceLab.mas_makeConstraints { make in
            make?.centerX.equalTo()(self.mas_centerX)
            make?.top.equalTo()(discountCardView.mas_top)
            make?.height.mas_equalTo()(WKDHPX_S(90))
        }
        
        discountCardView.addSubview(timerLab)
        timerLab.mas_makeConstraints { make in
            make?.top.equalTo()(priceLab.mas_bottom)
            make?.centerX.equalTo()(discountCardView.mas_centerX)
            make?.width.mas_equalTo()(WKDHPX_S(117))
            make?.height.mas_equalTo()(WKDHPX_S(20))
        }
        
        discountCardView.addSubview(subscribeBtn)
        subscribeBtn.mas_makeConstraints { make in
            make?.bottom.equalTo()(discountCardView.mas_bottom)?.offset()(-WKDHPX_S(10))
            make?.centerX.equalTo()(discountCardView.mas_centerX)
            make?.width.mas_equalTo()(WKDHPX_S(125))
            make?.height.mas_equalTo()(WKDHPX_S(40))
        }
    }
    
    // MARK: - 事件
    @objc private func cancelBtnClick(_ sender: UIButton) {
        actionCloseSelectBlock?()
        ReportMrkLogParms(1, "会员中心抽奖弹窗曝光", "page_vip_center", "btn_close_prize_popup", nil, 0, nil)
    }
    
    @objc private func subscribeBtnClick(_ sender: UIButton) {
        actionPurchaseSelectBlock?()
        ReportMrkLogParms(1, "会员中心抽奖弹窗曝光", "page_vip_center", "btn_use_prize_immediately", nil, 0, nil)
    }
}
