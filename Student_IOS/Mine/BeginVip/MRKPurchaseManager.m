//
//  MRKPurchaseManager.m
//  Student_IOS
//
//  Created by Junq on 2025/8/7.
//

#import "MRKPurchaseManager.h"

@implementation MRKPurchaseManager

- (instancetype)init
{
    self = [super init];
    if (self) {
        
    }
    return self;
}

/// Strategy 1:
///  - Step 1: Requests localized information about a product from the Apple App Store.
///  - Step 2: Adds payment of the product with the given product identifier.
- (void)fetchesPaymentCode:(NSString *)productId {
    ///You need to check whether the device is not able or allowed to make payments before requesting product.
    if (![DYFStore canMakePayments]) {
        [self showTipsMessage:@"当前手机设备不支持或者未授权购买"];
        return;
    }
    
    MLog(@"_开始内购 ====productId:%@ ", productId);
    [self showLoading:@"加载中..."];
    [DYFStore.defaultStore requestProductWithIdentifier:productId success:^(NSArray *products, NSArray *invalidIdentifiers) {
        [self hideLoading];
        if (products.count == 1) {
            NSString *productId = ((SKProduct *)products[0]).productIdentifier;
            [self addPayment:productId];
            MLog(@"_开始内购 block ====productId:%@ ",  productId);
        } else {
            [self showTipsMessage:@"暂无此项购买销售"];
            MLog(@"_内购失败 ==== 暂无此项购买销售 ");
        }
    } failure:^(NSError *error) {
        [self hideLoading];
        
        NSString *value = error.userInfo[NSLocalizedDescriptionKey];
        NSString *msg = value ?: error.localizedDescription;
        // This indicates that the product cannot be fetched, because an error was reported.
        [self sendNotice:[NSString stringWithFormat:@"出现错误, %zi, %@", error.code, msg]];
        
        MLog(@"_内购失败 ==== 出现错误, 错误码:%zi , %@ ", error.code, msg);
    }];
}

- (void)addPayment:(NSString *)productId{
    // Get account name from your own user system.
    NSString *userIdentifier = UserInfo.userId;
    
    // This algorithm is negotiated with server developer.
//    NSString *userIdentifier = DYF_SHA256_HashValue(accountName);
    DYFStoreLog(@"userIdentifier: %@", userIdentifier);
    [DYFStoreManager.shared addPayment:productId userIdentifier:userIdentifier];
}

- (void)sendNotice:(NSString *)message {
    [self showAlertWithTitle:@"提示"
                     message:message
           cancelButtonTitle:nil
                      cancel:NULL
          confirmButtonTitle:@"知道了"
                     execute:^(UIAlertAction *action) {
        DYFStoreLog(@"Alert action title: %@", action.title);
    }];
}

- (void)dealloc
{
    NSLog(@"MRKPurchaseManager ========= dealloc");
}

@end
