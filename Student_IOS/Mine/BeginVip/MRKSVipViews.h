//
//  MRKSVipViews.h
//  Student_IOS
//
//  Created by merit on 2023/8/28.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@class MRKVipPageModel;
@class MRKVipExclusiveItemModel;
@class MRKVipPackageView;

@interface MRKIAPMessageView : UIView
- (void)configPageModel:(MRKVipPageModel *)pageModel vipType:(NSString *)vipType;
@end



@interface MRKPurchaseView : UIView
@property (nonatomic, assign) int page;///标识购买索引
@property (nonatomic, strong) NSMutableArray *vipRulerArray;     ///会员购买项
@property (nonatomic, strong) NSAttributedString *tipPurchaseStr;
@property (nonatomic, copy) void(^vipProtocolClickBlock)(void);
@property (nonatomic, copy) void(^updateSvipTimeBlock)(void);

- (void)configPageModel:(MRKVipPageModel *)pageModel vipType:(NSString *)vipType;
@end



@interface MRKIAPBannerView : UIView
- (void)configPageModel:(MRKVipPageModel *)pageModel vipType:(NSString *)vipType;
@end



@interface MRKIAPImageView : UIView
- (void)configPageModel:(MRKVipPageModel *)pageModel vipType:(NSString *)vipType;
@end



@interface MRKIAPBottomView : UIView
@property (nonatomic, copy) void (^restorePurchaseBlock)(void); ///恢复购买
@property (nonatomic, copy) void (^updateSvipTimeBlock)(void); ///升级购买
@property (nonatomic, copy) void (^expandClickBlock)(BOOL isExpand); ///展开/收缩操作
- (void)configPageModel:(MRKVipPageModel *)pageModel vipType:(NSString *)vipType;
@end





NS_ASSUME_NONNULL_END
