//
//  MRKSVipViews.h
//  Student_IOS
//
//  Created by merit on 2023/8/28.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@class MRKVipPageModel;
@class MRKVipCardModel;
@class MRKVipExclusiveItemModel;
@class MRKVipPackageView;


@interface MRKIAPMessageView : UIView
- (void)configPageModel:(MRKVipPageModel *)pageModel vipType:(NSString *)vipType;
@end



typedef NS_ENUM(NSInteger, MRKPurchaseViewType) {
    MRKPurchaseViewTypeNormal,
    MRKPurchaseViewTypeAlert,
};
@interface MRKPurchaseView : UIView
///
- (instancetype)initWithFrame:(CGRect)frame viewType:(MRKPurchaseViewType)viewType;
///
@property (nonatomic, assign) MRKPurchaseViewType viewType;
@property (nonatomic, assign) int selectIndex; ///标识购买索引 [默认0]
@property (nonatomic, assign) BOOL hasAgreeProtocol; ///标识是否同意协议
@property (nonatomic, strong) NSMutableArray <MRKVipCardModel*>*vipRulerArray; ///会员购买项
@property (nonatomic, strong) NSAttributedString *tipPurchaseStr; ///购买提示语
@property (nonatomic, assign) BOOL isClaimed; ///标识弹窗是否领取优惠
@property (nonatomic, copy) void(^subscribeVipPuchaseBlock)(MRKVipCardModel*);
@property (nonatomic, copy) void(^subscribeProtocolBlock)(NSInteger);
///
- (void)configPageModel:(MRKVipPageModel *)pageModel vipType:(NSString *)vipType claimedAlert:(BOOL)claimed;
@end



@interface MRKIAPBannerView : UIView
- (void)configPageModel:(MRKVipPageModel *)pageModel vipType:(NSString *)vipType;
@end



@interface MRKIAPImageView : UIView
- (void)configPageModel:(MRKVipPageModel *)pageModel vipType:(NSString *)vipType;
@end



@interface MRKIAPBottomView : UIView
@property (nonatomic, copy) void (^restorePurchaseBlock)(void); ///恢复购买
@property (nonatomic, copy) void (^updateSvipTimeBlock)(void); ///升级购买
@property (nonatomic, copy) void (^expandClickBlock)(BOOL isExpand); ///展开/收缩操作
- (void)configPageModel:(MRKVipPageModel *)pageModel vipType:(NSString *)vipType;
@end

NS_ASSUME_NONNULL_END
