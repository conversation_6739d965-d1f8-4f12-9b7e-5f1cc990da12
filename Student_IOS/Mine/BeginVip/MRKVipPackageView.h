//
//  MRKVipPackageView.h
//  Student_IOS
//
//  Created by <PERSON><PERSON> on 2023/5/24.
//

#import <UIKit/UIKit.h>
#import "MRKVipCardModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface MRKPurchaseProtocolView : UIView
@property (nonatomic, copy) void(^userProtocolClickBlock)(void);
@property (nonatomic, copy) void(^privacyPolicyClickBlock)(void);
@property (nonatomic, copy) void(^vipProtocolClickBlock)(void);
@property (nonatomic, copy) void(^customerServiceBlock)(void);
@end



@interface MRKVipProtrolButton : UIButton
@property (nonatomic, copy) NSString *btnTitle;
@end



@interface MRKVipCardView : UIControl
@property (nonatomic, strong) MRKVipCardModel *model;
@property (nonatomic, assign) BOOL hasSelectCurrent;
@property (nonatomic, assign) BOOL showInAlert;

@property (nonatomic, assign) BOOL waitAlertDismiss;
@property (nonatomic, copy) void(^vipProductChangeBlock)(void);
@end




@interface MRKVipPurchaseBottomView : UIView
@property (nonatomic, assign) BOOL hasAgreeProtocol;          ///标识是否同意协议
@property (nonatomic, assign) BOOL isAutoproduct;             ///标识是否自动续费商品
@property (nonatomic, strong) NSAttributedString *tipPurchaseStr;

@property (nonatomic, copy) void(^agressProtocolBlock)(BOOL);          ///同意协议
@property (nonatomic, copy) void(^subscribeVipPuchaseBlock)(void);     ///购买
@property (nonatomic, copy) void(^subscribeProtocolBlock)(NSInteger);  ///选择协议
@end



@interface MRKPurchaseRightsItemView : UIControl
- (void)itemData:(MRKVipExclusiveItemModel *)model vipType:(NSString *)vipType;
@property (nonatomic, strong) MRKVipExclusiveItemModel *model;
@end


NS_ASSUME_NONNULL_END
