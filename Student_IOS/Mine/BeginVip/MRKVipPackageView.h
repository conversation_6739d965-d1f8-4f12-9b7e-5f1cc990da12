//
//  MRKVipPackageView.h
//  Student_IOS
//
//  Created by Jun<PERSON> on 2023/5/24.
//

#import <UIKit/UIKit.h>
#import "MRKVipPageModel.h"
#import "MRKVipCardModel.h"
#import "AdvertModel.h"
#import "MRKAutoRenewModel.h"

NS_ASSUME_NONNULL_BEGIN


@interface MRKVipBannerView : UIView
@property (nonatomic, copy) void(^bannerSelectBlock)(AdvertModel *model);
@property (nonatomic, strong) NSMutableArray *dataArray;
@end



@interface MRKVipCardProtocolView : UIView
@property (nonatomic, copy) void(^userProtocolClickBlock)(void);
@property (nonatomic, copy) void(^privacyPolicyClickBlock)(void);
@property (nonatomic, copy) void(^vipProtocolClickBlock)(void);
@property (nonatomic, copy) void(^customerServiceBlock)(void);
@end



@interface MRKVipProtrolButton : UIButton
@property (nonatomic, copy) NSString *btnTitle;
@end



@interface MRKVipCardView : UIControl
@property (nonatomic, strong) MRKVipCardModel *model;
@property (nonatomic, assign) BOOL hasSelectCurrent;
@property (nonatomic, assign) BOOL showInAlert;
@property (nonatomic, copy) void(^vipProductChangeBlock)(void);
@end




@interface MRKVipPurchaseBottomView : UIView
@property (nonatomic, assign) BOOL hasAgreeProtocol;///标识是否同意协议
@property (nonatomic, strong) NSAttributedString *tipPurchaseStr;
@property (nonatomic, copy) void(^subscribeBlock)(void);
@end



@interface MRKPurchaseRightsItemView : UIControl
@property (nonatomic, strong) MRKVipExclusiveItemModel *model;
@end


NS_ASSUME_NONNULL_END
