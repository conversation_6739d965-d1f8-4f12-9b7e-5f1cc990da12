//
//  MRKVipPackageView.h
//  Student_IOS
//
//  Created by Junq on 2023/5/24.
//

#import <UIKit/UIKit.h>
#import "MRKVipPageModel.h"
#import "MRKVipCardModel.h"
#import "AdvertModel.h"
#import "MRKAutoRenewModel.h"

NS_ASSUME_NONNULL_BEGIN




@interface MRKVipHeaderView : UIView
@property (nonatomic, strong) MRKVipPageModel *pageModel;
@end



@interface MRKVipBannerView : UIView
@property (nonatomic, copy) void(^bannerSelectBlock)(AdvertModel *model);
@property (nonatomic, strong) NSMutableArray *dataArray;
@end



@interface MRKVipCardProtocolView : UIView
@property (nonatomic, copy) void(^userProtocolClickBlock)(void);
@property (nonatomic, copy) void(^privacyPolicyClickBlock)(void);
@property (nonatomic, copy) void(^vipProtocolClickBlock)(void);
@property (nonatomic, copy) void(^customerServiceBlock)(void);
@end



@interface MRKVipProtrolButton : UIButton
@property (nonatomic, copy) NSString *btnTitle;
@end



@interface MRKVipCardView : UIControl
@property (nonatomic, strong) MRKVipCardModel *model;
@property (nonatomic, assign) BOOL shadowStatus;
@property (nonatomic, copy) void(^vipProductChangeBlock)(void);
@end




@interface MRKVipPurchaseBottomView : UIControl
@property (nonatomic, strong) NSAttributedString *tipPurchaseStr;
@property (nonatomic, copy) void(^subscribeBlock)(void);
@end



@interface MRKPurchaseRightsItemView : UIControl
@property (nonatomic, strong) MRKVipExclusiveItemModel *model;
@end


NS_ASSUME_NONNULL_END
