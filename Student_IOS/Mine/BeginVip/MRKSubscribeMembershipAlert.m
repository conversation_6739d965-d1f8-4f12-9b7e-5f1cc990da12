//
//  MRKSubscribeMembershipAlert.m
//  Student_IOS
//
//  Created by Jun<PERSON> on 2025/8/4.
//

#import "MRKSubscribeMembershipAlert.h"
#import "UIView+AZGradient.h"
#import "MRKSVipViews.h"
#import "MRKLoginTipAlert.h"

@interface MRKSubscribeMembershipAlert ()
@property (nonatomic, strong) UILabel *titleLab;
@property (nonatomic, strong) UILabel *descripLab;
@property (nonatomic, strong) UIButton *cancelBtn;

@property (nonatomic, strong) MRKSubscribeBackground *backgroundView;
@property (nonatomic, strong) MRKPurchaseView *purchaseView;

@property (nonatomic, strong) UIImageView *subscribeMiaImageView;
@property (nonatomic, strong) UIImageView *subscribeAIPlanImageView;

///展示购买项数据
@property (nonatomic, strong) NSMutableArray *vipRulerArray;

@property (nonatomic, assign) int currentPurchaseIndex; ///标识购买索引
@property (nonatomic, assign) BOOL isAgreeProtocol;
@end

@implementation MRKSubscribeMembershipAlert


#pragma mark - 布局方法

- (void)layoutContainerView{
    self.isAutoHidden = YES;
    UIView *baseSuperView = self.containerView.superview;
    [self.containerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_equalTo(baseSuperView.mas_bottom);
        make.centerX.mas_equalTo(baseSuperView.mas_centerX);
        make.width.mas_equalTo(kScreenWidth);
    }];
}

- (void)setupContainerViewAttributes{
    self.containerView.backgroundColor = [UIColor clearColor];
}

- (void)setupContainerSubViews{
    [self.containerView addSubview:self.cancelBtn];
    [self.cancelBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.containerView.mas_top);
        make.right.mas_equalTo(self.containerView.mas_right).offset(-WKDHPX(16));
        make.size.mas_equalTo(CGSizeMake(WKDHPX(40), WKDHPX(40)));
    }];
    
    [self.containerView addSubview:self.backgroundView];
    [self.backgroundView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.containerView.mas_top).offset(WKDHPX(60));
        make.left.mas_equalTo(self.containerView.mas_left);
        make.right.mas_equalTo(self.containerView.mas_right);
        make.bottom.mas_equalTo(self.containerView.mas_bottom);
    }];
    
    [self.containerView addSubview:self.subscribeMiaImageView];
    [self.subscribeMiaImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.backgroundView.mas_top).offset(-WKDHPX(25));
        make.right.mas_equalTo(self.containerView.mas_right).offset(-WKDHPX(8));
        make.size.mas_equalTo(CGSizeMake(WKDHPX(86), WKDHPX(81)));
    }];
    
    [self.containerView addSubview:self.subscribeAIPlanImageView];
    [self.subscribeAIPlanImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_equalTo(self.containerView.mas_right).offset(-WKDHPX(80));
        make.bottom.mas_equalTo(self.backgroundView.mas_top).offset(WKDHPX(8));
        make.size.mas_equalTo(CGSizeMake(WKDHPX(248), WKDHPX(47)));
    }];
    
    
    [self.containerView addSubview:self.titleLab];
    [self.titleLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.containerView.mas_top).offset(WKDHPX(24) + WKDHPX(60));
        make.left.mas_equalTo(self.containerView.mas_left).offset(WKDHPX(16));
    }];
    
    [self.containerView addSubview:self.descripLab];
    [self.descripLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.titleLab.mas_bottom).offset(WKDHPX(12));
        make.left.mas_equalTo(self.containerView.mas_left).offset(WKDHPX(16));
        make.right.mas_equalTo(self.containerView.mas_right).offset(-WKDHPX(16));
    }];
    
    [self.containerView addSubview:self.purchaseView];
    [self.purchaseView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.descripLab.mas_bottom);
        make.left.mas_equalTo(self.containerView.mas_left);
        make.right.mas_equalTo(self.containerView.mas_right);
        make.bottom.mas_equalTo(-SafeAreaBottom);
    }];
    
    ///
    int viewType = UserInfo.vipType;
    [MRKBaseRequest mrkRequestType:YTKRequestMethodGET
                               url:@"/user/member-product/vip-product"
                           andParm:@{@"vipType": @(viewType)}
          completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
     
        id data = [request.responseObject valueForKeyPath:@"data.ordinaries"];
        NSArray *arr = [NSArray modelArrayWithClass:[MRKVipCardModel class] json:data];
        self.vipRulerArray = arr.mutableCopy;
        
        self.purchaseView.vipRulerArray = self.vipRulerArray;
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
     
    }];
}

- (void)layoutContainerViewSubViews{
    [self layoutIfNeeded];
}


- (void)RACObserve{
    @weakify(self);
    [[[RACObserve(self.purchaseView, selectIndex) ignore:NULL] distinctUntilChanged] subscribeNext:^(id  _Nullable x) {
        @strongify(self);
        NSLog(@"self.vipPackageView.page === %@",x);
        self.currentPurchaseIndex = [x intValue];
    }];
    ///
    [[[RACObserve(self.purchaseView, hasAgreeProtocol) ignore:NULL] distinctUntilChanged] subscribeNext:^(id  _Nullable x) {
        @strongify(self);
        self.isAgreeProtocol = [x boolValue];
    }];
}

- (void)purchaseViewSelect {
    ///如果没有购买项
    if (self.vipRulerArray.count <= 0){
        return;
    }
    
    ///如果 self.page数值异常
    if (self.currentPurchaseIndex >= self.vipRulerArray.count){
        return;
    }
    
    /// 是否同意协议
    if (!self.isAgreeProtocol){
        @weakify(self);
        [self showTipActionAlertHandle:^(NSInteger index) {
            @strongify(self);
            if (index == 1){
                self.isAgreeProtocol = YES;
                [self purchaseViewSelect];
            }
        }];
        return;
    }
    
    NSArray *transactions = [SKPaymentQueue defaultQueue].transactions;
    DYFStoreLog(@"transactions ==== %@",transactions);
    
    MRKVipCardModel *model = [self.vipRulerArray objectAtIndex:self.currentPurchaseIndex];
    [self fetchesPaymentCode:model.appStoreCode ];
}

/// Strategy 1:
///  - Step 1: Requests localized information about a product from the Apple App Store.
///  - Step 2: Adds payment of the product with the given product identifier.
- (void)fetchesPaymentCode:(NSString *)productId {
    ///You need to check whether the device is not able or allowed to make payments before requesting product.
    if (![DYFStore canMakePayments]) {
        [self showTipsMessage:@"当前手机设备不支持或者未授权购买"];
        return;
    }
    
    MLog(@"_开始内购 ====productId:%@ ", productId);
    [self showLoading:@"加载中..."];
    [DYFStore.defaultStore requestProductWithIdentifier:productId success:^(NSArray *products, NSArray *invalidIdentifiers) {
        [self hideLoading];
        if (products.count == 1) {
            NSString *productId = ((SKProduct *)products[0]).productIdentifier;
            [self addPayment:productId];
            MLog(@"_开始内购 block ====productId:%@ ",  productId);
        } else {
            [self showTipsMessage:@"暂无此项购买销售"];
            MLog(@"_内购失败 ==== 暂无此项购买销售 ");
        }
    } failure:^(NSError *error) {
        
        [self hideLoading];
        
        NSString *value = error.userInfo[NSLocalizedDescriptionKey];
        NSString *msg = value ?: error.localizedDescription;
        // This indicates that the product cannot be fetched, because an error was reported.
        [self sendNotice:[NSString stringWithFormat:@"出现错误, %zi, %@", error.code, msg]];
        
        MLog(@"_内购失败 ==== 出现错误, 错误码:%zi , %@ ", error.code, msg);
    }];
}

- (void)addPayment:(NSString *)productId{
    // Get account name from your own user system.
    NSString *userIdentifier = UserInfo.userId;
    
    // This algorithm is negotiated with server developer.
//    NSString *userIdentifier = DYF_SHA256_HashValue(accountName);
    DYFStoreLog(@"userIdentifier: %@", userIdentifier);
    [DYFStoreManager.shared addPayment:productId userIdentifier:userIdentifier];
}


- (void)sendNotice:(NSString *)message {
    [self showAlertWithTitle:@"提示"
                     message:message
           cancelButtonTitle:nil
                      cancel:NULL
          confirmButtonTitle:@"知道了"
                     execute:^(UIAlertAction *action) {
        DYFStoreLog(@"Alert action title: %@", action.title);
    }];
}

- (void)showTipActionAlertHandle:(void (^)(NSInteger index))handle{
    @weakify(self);
    MRKLoginTipAlert *alert = [[MRKLoginTipAlert alloc] init];
    alert.alertType = LoginTipAlertStyleVip;
    alert.handle = handle;
    alert.routerHandle = ^(NSInteger index) {
        @strongify(self);
        if (index == 0) {
            [self userProtocol];
        }else{
            [self autoRenewProtocal];
        }
    };
    [alert showIn:self];
}


///会员协议
- (void)autoRenewProtocal {
    MRKBaseController *base = (MRKBaseController *)[UIViewController currentViewController];
    [[MRKTraceManager sharedInstance] manualUploadTraceType:2 pageTitle:base.navTitle pageId:base.tracePageId eventId:@"btn_profile_vip_services_vip" route:base.tracePageRoute duration:0 extendPara:@{}];
    
    WebViewViewController *vc = [WebViewViewController new];
    vc.htmlURL = MRKAppH5LinkCombine(MRKLinkAutoRenewalProtocol);
    vc.titleString = @"会员服务协议";
    MRKNavigationController *nav = [[MRKNavigationController alloc] initWithRootViewController:vc];
    nav.modalPresentationStyle = UIModalPresentationFullScreen;
    [[UIViewController currentViewController] presentViewController:nav animated:YES completion:nil];
}

///用户协议
- (void)userProtocol {
    WebViewViewController *vc = [WebViewViewController new];
    vc.htmlURL = MRKAppH5LinkCombine(MRKLinkUserProtocol);
    vc.titleString = @"用户协议";
    MRKNavigationController *nav = [[MRKNavigationController alloc] initWithRootViewController:vc];
    nav.modalPresentationStyle = UIModalPresentationFullScreen;
    [[UIViewController currentViewController] presentViewController:nav animated:YES completion:nil];
}

#pragma mark - 数据设置
- (void)setAlertType:(MembershipAlertType)alertType {
    _alertType = alertType;
    [self updateUIForAlertType];
}

- (void)updateUIForAlertType {
    switch (self.alertType) {
        case MembershipAlertTypeNormal:
            self.titleLab.text = @"";
            self.descripLab.text = @"";
            break;
        case MembershipAlertTypeAIPlan:
            self.titleLab.text = @"你的专属AI私教-Mia";
            self.descripLab.text = @"今日 AI 问询次数已用完，开通会员享更多次数";
            break;
        case MembershipAlertTypeCourse:
            self.titleLab.text = @"开通会员畅练海量会员课";
            self.descripLab.text = @"";
            break;
    }
}



#pragma mark - 懒加载

- (UILabel *)titleLab {
    if (!_titleLab) {
        UILabel *label = [[UILabel alloc] init];
        label.textAlignment = NSTextAlignmentLeft;
        label.textColor = UIColorHex(#333333);
        label.font = kMedium_PingFangSC(22);
        label.text = @"选择升级SVIP时长";
        _titleLab = label;
    }
    return _titleLab;
}

- (UILabel *)descripLab {
    if (!_descripLab) {
        UILabel *label = [[UILabel alloc] init];
        label.textAlignment = NSTextAlignmentLeft;
        label.textColor = UIColorHex(#BF7C08);
        label.font = kSystem_Font(12);
        label.numberOfLines = 0;
        label.text = @"今日 AI 问询次数已用完，开通会员享更多次数";
        _descripLab = label;
    }
    return _descripLab;
}

- (UIImageView *)subscribeMiaImageView {
    if (!_subscribeMiaImageView) {
        _subscribeMiaImageView = [[UIImageView alloc] init];
        _subscribeMiaImageView.contentMode = UIViewContentModeScaleAspectFit;
        _subscribeMiaImageView.image = [UIImage imageNamed:@"vip_alert_mia_cion"];;
    }
    return _subscribeMiaImageView;
}

- (UIImageView *)subscribeAIPlanImageView {
    if (!_subscribeAIPlanImageView) {
        _subscribeAIPlanImageView = [[UIImageView alloc] init];
        _subscribeAIPlanImageView.contentMode = UIViewContentModeScaleAspectFit;
        _subscribeAIPlanImageView.image = [UIImage imageNamed:@"vip_alert_aiplan_cion"];;
    }
    return _subscribeAIPlanImageView;
}

- (UIButton *)cancelBtn{
    if (!_cancelBtn) {
        UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
        UIImage *image = [[UIImage imageNamed:@"icon_close_o"] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
        [btn setImage:image forState:UIControlStateNormal];
        btn.imageView.tintColor = UIColorHex(#A05700);
        [btn addTarget:self action:@selector(cancelBtnClick:) forControlEvents:UIControlEventTouchUpInside];
        _cancelBtn = btn;
    }
    return _cancelBtn;
}

- (void)cancelBtnClick:(id)sender {
    [self dismissAnimated:YES];
}

- (MRKSubscribeBackground *)backgroundView {
    if (!_backgroundView) {
        _backgroundView = [[MRKSubscribeBackground alloc] init];
    }
    return _backgroundView;
}

- (MRKPurchaseView *)purchaseView {
    if (!_purchaseView) {
        _purchaseView = [[MRKPurchaseView alloc] initWithFrame:CGRectZero viewType:MRKPurchaseViewTypeAlert];
        @weakify(self);
        _purchaseView.subscribeVipPuchaseBlock = ^(MRKVipCardModel * _Nonnull model) {
            [self_weak_ purchaseViewSelect];
        };
    }
    return _purchaseView;
}

@end

