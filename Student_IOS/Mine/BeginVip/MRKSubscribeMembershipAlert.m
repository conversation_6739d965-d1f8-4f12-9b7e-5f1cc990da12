//
//  MRKSubscribeMembershipAlert.m
//  Student_IOS
//
//  Created by <PERSON><PERSON> on 2025/8/4.
//

#import "MRKSubscribeMembershipAlert.h"
#import "UIView+AZGradient.h"
#import "MRKSubscribeCardView.h"


@interface MRKSubscribeMembershipAlert ()
@property (nonatomic, strong) UILabel *titleLab;
@property (nonatomic, strong) UILabel *descripLab;
@property (nonatomic, strong) UIButton *cancelBtn;

@property (nonatomic, strong) MRKSubscribeCardView *subscribeView;
@property (nonatomic, strong) UILabel *subscribeTipLab;
@property (nonatomic, strong) UIButton *subscribeBtn;
@property (nonatomic, strong) YYLabel *protocolLab;
@property (nonatomic, strong) UIButton *agreementBtn;
@end

@implementation MRKSubscribeMembershipAlert

- (void)layoutContainerView{
    self.isAutoHidden = NO;
    UIView *baseSuperView = self.containerView.superview;
    [self.containerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_equalTo(baseSuperView.mas_bottom);
        make.centerX.mas_equalTo(baseSuperView.mas_centerX);
        make.width.mas_equalTo(kScreenWidth);
        make.height.mas_equalTo(DHPX(310));
    }];
}

- (void)setupContainerViewAttributes{
    self.containerView.backgroundColor = [UIColor whiteColor];
    MrkCornerMaskWithViewRadius(self.containerView, ViewRadiusMake(8, 8, 0, 0));
}

- (void)setupContainerSubViews{

    [self.containerView addSubview:self.cancelBtn];
    [self.cancelBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_equalTo(self.titleLab.mas_centerY);
        make.right.mas_equalTo(self.containerView.mas_right).offset(-DHPX(16));
        make.size.mas_equalTo(CGSizeMake(30, 30));
    }];
    
    
    [self.containerView addSubview:self.titleLab];
    [self.titleLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.containerView.mas_top).offset(DHPX(24));
        make.left.mas_equalTo(self.containerView.mas_left).offset(DHPX(16));
    }];
    
    [self.containerView addSubview:self.descripLab];
    [self.descripLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.titleLab.mas_bottom).offset(DHPX(12));
        make.left.mas_equalTo(self.containerView.mas_left).offset(DHPX(16));
        make.right.mas_equalTo(self.containerView.mas_right).offset(-DHPX(16));
    }];
    
    [self.containerView addSubview:self.subscribeView];
    [self.subscribeView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.mas_top).offset(DHPX(104));
        make.left.mas_equalTo(self.mas_left).offset(0);
        make.right.mas_equalTo(self.mas_right).offset(0);
        make.height.mas_equalTo(DHPX(74));
    }];
    
    
    [self.containerView addSubview:self.subscribeTipLab];
    [self.subscribeTipLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.subscribeView.mas_bottom).offset(DHPX(10));
        make.left.mas_equalTo(self.containerView.mas_left).offset(DHPX(16));
        make.height.mas_equalTo(DHPX(20));
    }];
    
    
    [self.containerView addSubview:self.subscribeBtn];
    [self.subscribeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.subscribeView.mas_bottom).offset(DHPX(38));
        make.left.mas_equalTo(self.containerView.mas_left).offset(DHPX(16));
        make.right.mas_equalTo(self.containerView.mas_right).offset(-DHPX(16));
        make.bottom.mas_equalTo(self.containerView.mas_bottom).offset(-DHPX(75));
        make.height.mas_equalTo(DHPX(48));
    }];
    
    
    
    //--------- 底部用户协议等 布局
    self.protocolLab = [[YYLabel alloc] init];
    self.protocolLab.textVerticalAlignment = YYTextVerticalAlignmentCenter;
    NSDictionary *attributes = @{NSFontAttributeName: [UIFont systemFontOfSize:12 weight:UIFontWeightRegular],
                                 NSForegroundColorAttributeName: [UIColor whiteColor]};
    NSMutableAttributedString *text = [[NSMutableAttributedString alloc] initWithString:@"我已阅读并同意MERIT《会员服务协议》和《自动续费服务协议》" attributes:attributes];
    [text setTextHighlightRange:[[text string] rangeOfString:@"《会员服务协议》"]
                          color:[UIColor colorWithHexString:@"#16D2E3"]
                backgroundColor:[UIColor clearColor]
                      tapAction:^(UIView * _Nonnull containerView, NSAttributedString * _Nonnull text, NSRange range, CGRect rect) {
     
    }];
    [text setTextHighlightRange:[[text string] rangeOfString:@"《自动续费服务协议》"]
                          color:[UIColor colorWithHexString:@"#16D2E3"]
                backgroundColor:[UIColor clearColor]
                      tapAction:^(UIView * _Nonnull containerView, NSAttributedString * _Nonnull text, NSRange range, CGRect rect) {
      
    }];
    self.protocolLab.attributedText = text;
    self.protocolLab.textAlignment = NSTextAlignmentLeft;
    [self.containerView addSubview: self.protocolLab];
    [self.protocolLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.mas_centerX).offset(10);
        make.bottom.mas_equalTo(-HDHPX(20 + kSafeArea_Bottom));
        make.height.mas_equalTo(25);
    }];
    
    self.agreementBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    self.agreementBtn.traceEventId = @"btn_login_agreement_click";
    [self.agreementBtn setImage:[UIImage imageNamed:@"protocol_selected"] forState:UIControlStateSelected];
    [self.agreementBtn setImage:[UIImage imageNamed:@"protocol_unSelected"] forState:UIControlStateNormal];
    [self.agreementBtn addTarget:self action:@selector(agreeButton:) forControlEvents:UIControlEventTouchUpInside];
    self.agreementBtn.contentMode = UIViewContentModeCenter;
    [self.containerView addSubview:self.agreementBtn];
    [self.agreementBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.protocolLab.mas_left).offset(0);
        make.centerY.equalTo(self.protocolLab.mas_centerY);
        make.size.mas_equalTo(CGSizeMake(30, 30));
    }];
 
}

- (void)layoutContainerViewSubViews{
    [self layoutIfNeeded];
}

- (UILabel *)titleLab {
    if (!_titleLab) {
        UILabel *label = [[UILabel alloc] init];
        label.textAlignment = NSTextAlignmentLeft;
        label.textColor = UIColorHex(#333333);
        label.font = kMedium_PingFangSC(17);
        label.text = @"选择升级SVIP时长";
        _titleLab = label;
    }
    return _titleLab;
}

- (UIButton *)cancelBtn{
    if (!_cancelBtn) {
        UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
        [btn setImage:[UIImage imageNamed:@"icon_close_o"] forState:UIControlStateNormal];
        [btn addTarget:self action:@selector(ensureBtnClick:) forControlEvents:UIControlEventTouchUpInside];
        _cancelBtn = btn;
    }
    return _cancelBtn;
}

- (void)ensureBtnClick:(UIButton *)sender{
    [self dismissAnimated:YES];
}

- (void)agreeButton:(UIButton *)sender{
    [self dismissAnimated:YES];
}


//- (void)setVipUpdateRulerArray:(NSMutableArray *)vipUpdateRulerArray {
//    _vipUpdateRulerArray = vipUpdateRulerArray;
//    
//    __block NSInteger index = 0;
//    [vipUpdateRulerArray enumerateObjectsUsingBlock:^(id  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
//        MRKVipCardModel *model = (MRKVipCardModel *)obj;
//        if ([self.defaultModel.appStoreCode isEqualToString:model.appStoreCode]) {
//            index = idx;
//            *stop = YES;
//        }
//    }];
//    self.defaultIndex = index;
//    
//    [self.tableView reloadData];
//}


- (UIButton *)subscribeBtn{
    if (!_subscribeBtn) {
        UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
        btn.backgroundColor = UIColorHex(#202121);
        [btn setTitle:@"立即开通" forState:UIControlStateNormal];
        [btn setTitleColor:[UIColor colorWithHexString:@"#FFD5B6"] forState:UIControlStateNormal];
        btn.titleLabel.font = kMedium_PingFangSC(15);
        [btn addTarget:self action:@selector(subscribeBtnClick:) forControlEvents:UIControlEventTouchUpInside];
        btn.layer.cornerRadius = WKDHPX(48)/2;
        btn.layer.masksToBounds = YES;
        _subscribeBtn = btn;
    }
    return _subscribeBtn;
}

- (void)subscribeBtnClick:(UIButton *)sender{
    [self dismissAnimated:YES];
}


/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/

@end

