//
//  MRKSubscribeMembershipAlert.m
//  Student_IOS
//
//  Created by <PERSON><PERSON> on 2025/8/4.
//

#import "MRKSubscribeMembershipAlert.h"
#import "UIView+AZGradient.h"
#import "MRKSubscribeCardView.h"

@interface MRKSubscribeMembershipAlert ()
@property (nonatomic, strong) UILabel *titleLab;
@property (nonatomic, strong) UILabel *descripLab;
@property (nonatomic, strong) UIButton *cancelBtn;

@property (nonatomic, strong) MRKSubscribeCardView *subscribeView;
@property (nonatomic, strong) UILabel *subscribeTipLab;
@property (nonatomic, strong) UIButton *subscribeBtn;
@property (nonatomic, strong) YYLabel *protocolLab;
@property (nonatomic, strong) UIButton *agreementBtn;
@end

@implementation MRKSubscribeMembershipAlert

#pragma mark - 便利构造方法

+ (instancetype)alertWithType:(MembershipAlertType)type vipModel:(MRKVipCardModel *)model {
    MRKSubscribeMembershipAlert *alert = [[self alloc] initWithAnimationStyle:MRKActionAlertViewTransitionStyleSlideFromBottom];
    alert.alertType = type;
    alert.vipCardModel = model;
    alert.isAgreedProtocol = NO;
    return alert;
}

#pragma mark - 生命周期

- (instancetype)initWithAnimationStyle:(MRKActionAlertViewTransitionStyle)style {
    if (self = [super initWithAnimationStyle:style]) {
        self.alertType = MembershipAlertTypeNormal;
        self.isAgreedProtocol = NO;
    }
    return self;
}

#pragma mark - 布局方法

- (void)layoutContainerView{
    self.isAutoHidden = NO;
    UIView *baseSuperView = self.containerView.superview;
    [self.containerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_equalTo(baseSuperView.mas_bottom);
        make.centerX.mas_equalTo(baseSuperView.mas_centerX);
        make.width.mas_equalTo(kScreenWidth);
    }];
}

- (void)setupContainerViewAttributes{
    self.containerView.backgroundColor = [UIColor whiteColor];
    MrkCornerMaskWithViewRadius(self.containerView, ViewRadiusMake(8, 8, 0, 0));
}

- (void)setupContainerSubViews{
    [self.containerView addSubview:self.cancelBtn];
    [self.cancelBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_equalTo(self.titleLab.mas_centerY);
        make.right.mas_equalTo(self.containerView.mas_right).offset(-DHPX(16));
        make.size.mas_equalTo(CGSizeMake(30, 30));
    }];
    
    [self.containerView addSubview:self.titleLab];
    [self.titleLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.containerView.mas_top).offset(DHPX(24));
        make.left.mas_equalTo(self.containerView.mas_left).offset(DHPX(16));
    }];
    
    [self.containerView addSubview:self.descripLab];
    [self.descripLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.titleLab.mas_bottom).offset(DHPX(12));
        make.left.mas_equalTo(self.containerView.mas_left).offset(DHPX(16));
        make.right.mas_equalTo(self.containerView.mas_right).offset(-DHPX(16));
    }];
    
    [self.containerView addSubview:self.subscribeView];
    [self.subscribeView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.descripLab.mas_bottom).offset(DHPX(16));
        make.left.mas_equalTo(self.containerView.mas_left).offset(DHPX(16));
        make.right.mas_equalTo(self.containerView.mas_right).offset(-DHPX(16));
        make.height.mas_equalTo(DHPX(74));
    }];
    
    
    [self.containerView addSubview:self.subscribeTipLab];
    [self.subscribeTipLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.subscribeView.mas_bottom).offset(DHPX(10));
        make.left.mas_equalTo(self.containerView.mas_left).offset(DHPX(16));
        make.height.mas_equalTo(DHPX(20));
    }];
    
    
    [self.containerView addSubview:self.subscribeBtn];
    [self.subscribeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.subscribeView.mas_bottom).offset(DHPX(38));
        make.left.mas_equalTo(self.containerView.mas_left).offset(DHPX(16));
        make.right.mas_equalTo(self.containerView.mas_right).offset(-DHPX(16));
        make.bottom.mas_equalTo(self.containerView.mas_bottom).offset(-DHPX(75));
        make.height.mas_equalTo(DHPX(48));
    }];
    
    [self.containerView addSubview: self.protocolLab];
    [self.protocolLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.containerView.mas_centerX).offset(10);
        make.top.mas_equalTo(self.subscribeBtn.mas_bottom).offset(DHPX(20));
        make.height.mas_equalTo(25);
    }];
    
    [self.containerView addSubview:self.agreementBtn];
    [self.agreementBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.protocolLab.mas_left).offset(0);
        make.centerY.equalTo(self.protocolLab.mas_centerY);
        make.size.mas_equalTo(CGSizeMake(30, 30));
    }];
 
}

- (void)layoutContainerViewSubViews{
    [self layoutIfNeeded];
}

#pragma mark - 数据设置

- (void)setVipCardModel:(MRKVipCardModel *)vipCardModel {
    _vipCardModel = vipCardModel;
    self.subscribeView.model = vipCardModel;
    [self updateUIForAlertType];
}

- (void)setAlertType:(MembershipAlertType)alertType {
    _alertType = alertType;
    [self updateUIForAlertType];
}

- (void)updateUIForAlertType {
    switch (self.alertType) {
        case MembershipAlertTypeNormal:
            self.titleLab.text = @"选择升级SVIP时长";
            self.descripLab.text = @"升级SVIP，解锁更多专属权益";
            break;
        case MembershipAlertTypeAIPlan:
            self.titleLab.text = @"你的专属AI私教-Mia";
            self.descripLab.text = @"今日 AI 问询次数已用完，开通会员享更多次数";
            break;
        case MembershipAlertTypeCourse:
            self.titleLab.text = @"开通会员畅练海量会员课";
            self.descripLab.text = @"";
            break;
    }

    // 更新订阅按钮状态
    [self updateSubscribeButtonState];
}

- (void)updateSubscribeButtonState {
    self.subscribeBtn.enabled = self.isAgreedProtocol;
    self.subscribeBtn.alpha = self.isAgreedProtocol ? 1.0 : 0.6;
}

#pragma mark - 懒加载

- (UILabel *)titleLab {
    if (!_titleLab) {
        UILabel *label = [[UILabel alloc] init];
        label.textAlignment = NSTextAlignmentLeft;
        label.textColor = UIColorHex(#333333);
        label.font = kMedium_PingFangSC(22);
        label.text = @"选择升级SVIP时长";
        _titleLab = label;
    }
    return _titleLab;
}

- (UILabel *)descripLab {
    if (!_descripLab) {
        UILabel *label = [[UILabel alloc] init];
        label.textAlignment = NSTextAlignmentLeft;
        label.textColor = UIColorHex(#BF7C08);
        label.font = kSystem_Font(12);
        label.numberOfLines = 0;
        label.text = @"今日 AI 问询次数已用完，开通会员享更多次数";
        _descripLab = label;
    }
    return _descripLab;
}

- (UIButton *)cancelBtn{
    if (!_cancelBtn) {
        UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
        [btn setImage:[UIImage imageNamed:@"icon_close_o"] forState:UIControlStateNormal];
        [btn addTarget:self action:@selector(ensureBtnClick:) forControlEvents:UIControlEventTouchUpInside];
        _cancelBtn = btn;
    }
    return _cancelBtn;
}

- (MRKSubscribeCardView *)subscribeView {
    if (!_subscribeView) {
        _subscribeView = [[MRKSubscribeCardView alloc] init];
        _subscribeView.backgroundColor = [UIColor colorWithHexString:@"#F8F9FA"];
        _subscribeView.layer.cornerRadius = 8;
        _subscribeView.layer.masksToBounds = YES;
        _subscribeView.shadowStatus = YES;
    }
    return _subscribeView;
}

- (UILabel *)subscribeTipLab {
    if (!_subscribeTipLab) {
        UILabel *label = [[UILabel alloc] init];
        label.textAlignment = NSTextAlignmentLeft;
        label.textColor = UIColorHex(#999999);
        label.font = kSystem_Font(12);
        label.text = @"订阅后可随时取消，无额外费用";
        _subscribeTipLab = label;
    }
    return _subscribeTipLab;
}

#pragma mark - 事件处理

- (void)ensureBtnClick:(UIButton *)sender{
    if ([self.membershipDelegate respondsToSelector:@selector(membershipAlertDidClickCancel:)]) {
        [self.membershipDelegate membershipAlertDidClickCancel:self];
    }
    [self dismissAnimated:YES];
}

- (void)agreeButton:(UIButton *)sender{
    sender.selected = !sender.selected;
    self.isAgreedProtocol = sender.selected;
    [self updateSubscribeButtonState];
}


//- (void)setVipUpdateRulerArray:(NSMutableArray *)vipUpdateRulerArray {
//    _vipUpdateRulerArray = vipUpdateRulerArray;
//    
//    __block NSInteger index = 0;
//    [vipUpdateRulerArray enumerateObjectsUsingBlock:^(id  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
//        MRKVipCardModel *model = (MRKVipCardModel *)obj;
//        if ([self.defaultModel.appStoreCode isEqualToString:model.appStoreCode]) {
//            index = idx;
//            *stop = YES;
//        }
//    }];
//    self.defaultIndex = index;
//    
//    [self.tableView reloadData];
//}


- (YYLabel *)protocolLab {
    if (!_protocolLab) {
        _protocolLab = [[YYLabel alloc] init];
        _protocolLab.textVerticalAlignment = YYTextVerticalAlignmentCenter;
        _protocolLab.attributedText = ({
            NSMutableAttributedString *protrolText = [[NSMutableAttributedString alloc] initWithString:@"我已阅读并同意MERIT"];
            protrolText.color = [UIColor colorWithHexString:@"#B3B5B9"];
            protrolText.font = [UIFont systemFontOfSize:WKDHPX(13)];

            NSMutableAttributedString *text = [[NSMutableAttributedString alloc] initWithString:@"《会员服务协议》"];
            text.font = [UIFont systemFontOfSize:WKDHPX(13)];
            text.color = UIColorHex(#17D2E3);
            text.underlineStyle = NSUnderlineStyleSingle;
            [text setTextHighlightRange:text.rangeOfAll
                                     color:[UIColor colorWithHexString:@"#17D2E3"]
                           backgroundColor:[UIColor clearColor]
                                 tapAction:^(UIView * _Nonnull containerView, NSAttributedString * _Nonnull text, NSRange range, CGRect rect) {
        
            }];
            [protrolText appendAttributedString:text];
            
            NSMutableAttributedString *andText= [[NSMutableAttributedString alloc] initWithString:@"和"];
            andText.color = [UIColor colorWithHexString:@"#B3B5B9"];
            andText.font = [UIFont systemFontOfSize:WKDHPX(13)];
            [protrolText appendAttributedString:andText];
            
            NSMutableAttributedString *renewText = [[NSMutableAttributedString alloc] initWithString:@"《自动续费服务协议》"];
            renewText.font = [UIFont systemFontOfSize:WKDHPX(13)];
            renewText.color = UIColorHex(#17D2E3);
            renewText.underlineStyle = NSUnderlineStyleSingle;
            [renewText setTextHighlightRange:renewText.rangeOfAll
                                     color:[UIColor colorWithHexString:@"#17D2E3"]
                           backgroundColor:[UIColor clearColor]
                                 tapAction:^(UIView * _Nonnull containerView, NSAttributedString * _Nonnull text, NSRange range, CGRect rect) {
        
            }];
            [protrolText appendAttributedString:renewText];
            protrolText;
        });
        _protocolLab.textAlignment = NSTextAlignmentCenter;
    }
    
    return _protocolLab;
}

- (UIButton *)agreementBtn {
    if (!_agreementBtn) {
        _agreementBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_agreementBtn setImage:[UIImage imageNamed:@"protocol_selected"] forState:UIControlStateSelected];
        [_agreementBtn setImage:[UIImage imageNamed:@"protocol_unSelected"] forState:UIControlStateNormal];
        [_agreementBtn addTarget:self action:@selector(agreeButton:) forControlEvents:UIControlEventTouchUpInside];
        _agreementBtn.contentMode = UIViewContentModeCenter;
    }
    return _agreementBtn;
}



- (UIButton *)subscribeBtn{
    if (!_subscribeBtn) {
        UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
        btn.backgroundColor = UIColorHex(#202121);
        [btn setTitle:@"立即开通" forState:UIControlStateNormal];
        [btn setTitleColor:[UIColor colorWithHexString:@"#FFD5B6"] forState:UIControlStateNormal];
        btn.titleLabel.font = kMedium_PingFangSC(15);
        [btn addTarget:self action:@selector(subscribeBtnClick:) forControlEvents:UIControlEventTouchUpInside];
        btn.layer.cornerRadius = WKDHPX(48)/2;
        btn.layer.masksToBounds = YES;
        _subscribeBtn = btn;
    }
    return _subscribeBtn;
}

- (void)subscribeBtnClick:(UIButton *)sender{
    if (!self.isAgreedProtocol) {
        // 可以添加提示用户需要同意协议的逻辑
        return;
    }

    if ([self.membershipDelegate respondsToSelector:@selector(membershipAlert:didClickSubscribeWithModel:)]) {
        [self.membershipDelegate membershipAlert:self didClickSubscribeWithModel:self.vipCardModel];
    }
    [self dismissAnimated:YES];
}

#pragma mark - 公共方法

- (void)showWithCompletion:(void(^)(void))completion {
    [self show];
    if (completion) {
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            completion();
        });
    }
}

- (void)updateVipCardModel:(MRKVipCardModel *)model {
    self.vipCardModel = model;
}

#pragma mark - 私有方法

- (void)setupInitialState {
    // 初始化时订阅按钮不可用
    [self updateSubscribeButtonState];
}

@end

