//
//  MyViewController.m
//  Student_IOS
//
//  Created by 闻飞 on 2021/2/24.
//


#import "MyViewController.h"
#import "MJDIYHeader.h"
#import "MRKHomeAlertManager.h"
#import "MRKUserPageModel.h"
#import "MRKGrowUpModel.h"
#import "MRKHealthMineModel.h"
#import "UIButton+ClickArea.h" //增大按钮点击的分类 --wk
#import "UIView+AZGradient.h"
#import "UIView+MRKBadge.h"

#import "MyAccountInfoTableViewCell.h"
#import "MyVipInfoTableViewCell.h"
#import "MRKUserBannerTableViewCell.h"
#import "MyDeviceTableViewCell.h"
#import "MyOtherTableViewCell.h"
#import "MyAbilityTestTableCell.h"

#import "MyFamilyViewController.h"
#import "MRKCourseCollectController.h"
#import "DesignViewController.h"
#import "FollowCoachController.h"
#import "GetNewsViewController.h"
#import "MyDeviceListViewController.h"
#import "MydetailViewController.h"
#import "WebViewViewController.h"
#import "MRKMyGrowUpController.h"
#import "MRKMedalController.h"
#import "DeviceSearchViewController.h"
#import "MRKMineEvaluateAlertView.h"
#import "MRKPopupManager.h"
#import "MRKMineEvaluateModel.h"
#import "MRKHealthListController.h"
#import "MRKXEnjoyVipAlert.h"
#import "MRKUserIAPController.h"
#import "MRKDeviceDetailController.h"
#import "AbilityTestResultController.h"
#import "MRKGeneralWebController.h"
#import "AbilityIntroduceVC.h"
#import "MRKAIPlanDoneController.h"
#import "MyVoiceViewController.h"
#import "TrainRecordController.h"



@interface MyViewController () <UITableViewDelegate, UITableViewDataSource>
@property (nonatomic, strong) UIImageView *navigationBarView;
@property (nonatomic, strong) UIImageView *bgImageView;
@property (nonatomic, strong) UITableView *myTableView;

@property (nonatomic, strong) UIButton *serviceBtn;
@property (nonatomic, strong) UIButton *messageBtn;
@property (nonatomic, strong) UIButton *settingBtn;
@property (nonatomic, strong) UIButton *qrcodeBtn;

@property (nonatomic, strong) User *myUser;/// 用户数据
@property (nonatomic, strong) MRKGrowUpUserModel *growUpModel;/// 用户成长 信息模型
@property (nonatomic, strong) MRKUserPageModel *pageModel;
@property (nonatomic, strong) MRKMineEvaluateAlertView *alert;

@property (nonatomic, strong) NSMutableArray *trainingDic;
@property (nonatomic, strong) NSMutableArray *shopDic;
@property (nonatomic, strong) NSMutableArray *serveDic;
@property (nonatomic, strong) NSMutableArray *aboutDic;
@end

static NSString *MyAccountInfoTableViewCellId = @"MyAccountInfoTableViewCellID";
static NSString *MyVipInfoTableViewCellId = @"MyVipInfoTableViewCellID";
static NSString *myAbilityTestCellId = @"myAbilityTestCellId";
static NSString *myUserBannerCellId = @"myUserBannerCellId";
static NSString *myDeviceInfoCellId = @"MyDeviceInfoCellID";


static NSString *MyTrainingCellID = @"MyTrainingCellID";
static NSString *MyShopCellID = @"MyShopCellID";
static NSString *MyServeCellID = @"MyServeCellID";
static NSString *MyAboutCellID = @"MyAboutCellID";


@implementation MyViewController

- (MRKUserPageModel *)pageModel{
    if(!_pageModel){
        _pageModel = [[MRKUserPageModel alloc] init];
    }
    return _pageModel;
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    
    /// 请求弹窗
    [[MRKHomeAlertManager shareManager] addTarget:self checkAlertCode:MRKUcenterPopupCode];
    
    /// 请求用户等级是否升级 2022-7-13 --wk
    [MRKGrowAlertManager postLevelAndMedalRequest];
    
    [self changeStateStyle];
}

- (void)viewDidLoad {
    self.tracePageId = @"page_profile";
    [super viewDidLoad];
    
    // 初始化本地数据
    [self initData];
    
    // 初始化视图
    [self initView];
    
    // 初始化自定义导航视图
    [self initNavigationBarView];
    
    [[MRKHomeAlertManager shareManager] addTarget:self requestAlertData:MRKUcenterPopupCode];
    
    [self requestDataSource];
    
    [self defaultSetting];
}

- (void)defaultSetting {
    self.view.backgroundColor = [UIColor colorWithHexString:@"#F3F7FB"];
    self.navigationBarView.lee_theme.LeeConfigBackgroundColor(@"mine_nav_background_color");
    self.bgImageView.lee_theme.LeeConfigImage(@"mine_nav_background_image");
    @weakify(self);
    self.view.lee_theme.LeeThemeChangingBlock(^(NSString * _Nonnull tag, UITabBar * _Nonnull item) {
        @strongify(self);
        [self.serviceBtn setTintColor:[LEETheme getValueWithTag:tag Identifier:@"mine_nav_icon_color"]];
        [self.messageBtn setTintColor:[LEETheme getValueWithTag:tag Identifier:@"mine_nav_icon_color"]];
        [self.settingBtn setTintColor:[LEETheme getValueWithTag:tag Identifier:@"mine_nav_icon_color"]];
        [self.qrcodeBtn setTintColor:[LEETheme getValueWithTag:tag Identifier:@"mine_nav_icon_color"]];
        [self changeStateStyle];
    });
}

- (UIStatusBarStyle)preferredStatusBarStyle {
    if ([[LEETheme currentThemeTag] isEqualToString:THEME_XENJOY]) {
        return UIStatusBarStyleLightContent;
    }else {
        return UIStatusBarStyleDefault;
    }
}

// 更换状态栏
- (void)changeStateStyle {
    if ([[LEETheme currentThemeTag] isEqualToString:THEME_XENJOY]) {
        self.statusBarStyle = UIStatusBarStyleLightContent;
    }else {
        self.statusBarStyle = UIStatusBarStyleDefault;
    }
}

- (void)requestDataSource {
    @weakify(self);
    [self.pageModel.userInfoSignal subscribeNext:^(id x) {
        @strongify(self);
        if (self.myTableView.mj_header.isRefreshing) {
            [self.myTableView.mj_header endRefreshing];
        }
        
        NSInteger type = [x integerValue];
        switch (type) {
            case 1:{ ///用户信息
                User *user = self.pageModel.userInfo;
                self.myUser = user;
                
                self.growUpModel = [MRKGrowUpUserModel modelWithJSON:[user.levelInfo modelToJSONObject]];
                self.growUpModel.nickName = user.basicInfo.nickname;
                self.growUpModel.avatarString = user.basicInfo.avatar;
                self.growUpModel.medalNum = [NSString stringWithFormat:@"%d", user.medalInfo.userMedalSize];
                
            } break;
            case 4:{ ///未读消息数
                [self.messageBtn showBadgeWithCount:self.pageModel.unreadNum];

            } break;
            case 8:
                self.qrcodeBtn.hidden = !self.pageModel.showScanQrcode;
                self.qrcodeBtn.enabled = self.pageModel.showScanQrcode;
            default:
                break;
        }
    
        [self.myTableView reloadData];
        
        ///checkAlert
        [self checkUserXEnjoyVIPTip];
    }];
}

///绝影会员权益弹窗
- (void)checkUserXEnjoyVIPTip {
    NSString *defaultsKey = [NSString stringWithFormat:@"UserXEnjoyVIPTip_%@", UserInfo.userId];
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    BOOL RegulateTip = [userDefaults boolForKey:defaultsKey];
    BOOL newXEnjoyVip = self.pageModel.userInfo.memberInfo.isMember && (self.pageModel.userInfo.memberInfo.vipType == 30);
    if (!RegulateTip && newXEnjoyVip){
        [userDefaults setBool:YES forKey:defaultsKey];
        [userDefaults synchronize];
           
        ///
        MRKXEnjoyVipAlert *alert = [[MRKXEnjoyVipAlert alloc] init];
        alert.XEnjoyVipClickBlock = ^{
            [[RouteManager sharedInstance] skipVIP];
        };
        [alert showIn:[UIViewController currentTabBarController].view];
    }
}



- (void)refresh {
    [self.pageModel refreshDataSource];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
    [self.pageModel refreshDataSource];
}

#pragma mark - init
- (void)initData {
    
    self.trainingDic = @[
        @{@"title":@"训练记录",
          @"image":@"icon_dataTrain"},
        @{@"title":@"我的收藏",
          @"image":@"icon_collect_mine"},
        @{@"title":@"关注的教练",
          @"image":@"icon_coach"}].mutableCopy;
    
    self.shopDic = @[
        @{@"title":@"商城",
          @"image":@"icon_shop"},
        @{@"title":@"活动赛事",
          @"image":@"icon_game"},
        @{@"title":@"进群领福利",
          @"image":@"icon_partner"}].mutableCopy;
    
    self.serveDic = @[
        @{@"title":@"第三方应用与服务",
          @"image":@"icon_datashare"},
        @{@"title":@"家庭成员",
          @"image":@"icon_home"},
        @{@"title":@"产品百科",
          @"image":@"icon_encyclopedia"}].mutableCopy;
    
    self.aboutDic = @[
        @{@"title":@"评价我们",
          @"image":@"icon_grade"},
        @{@"title":@"举报与反馈",
          @"image":@"icon_feedback"}].mutableCopy;
}

- (void)initView {
    
    float height = WKDHPX(76) +kNavBarHeight +(RealScreenWidth -WKDHPX(16)*2)*0.24 +WKDHPX(16)*2 +WKDHPX(17);
    self.bgImageView.frame = CGRectMake(0, 0, RealScreenWidth, height);
    [self.mrkContentView addSubview:self.bgImageView];
    
    [self.mrkContentView addSubview:self.myTableView];
    [self.myTableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsMake(0, 0, 0, 0));
    }];
    
    [self.mrkContentView addSubview:self.navigationBarView];
    [self.mrkContentView addSubview:self.messageBtn];
    [self.mrkContentView addSubview:self.serviceBtn];
    [self.mrkContentView addSubview:self.settingBtn];
    [self.mrkContentView addSubview:self.qrcodeBtn];
    
    [self registerTableViewCell];
}

- (void)registerTableViewCell {
    [self.myTableView registerClass:[MyAccountInfoTableViewCell class] forCellReuseIdentifier:MyAccountInfoTableViewCellId];
    [self.myTableView registerClass:[MyVipInfoTableViewCell class] forCellReuseIdentifier:MyVipInfoTableViewCellId];
    [self.myTableView registerClass:[MyAbilityTestTableCell class] forCellReuseIdentifier:myAbilityTestCellId];
    [self.myTableView registerClass:[MRKUserBannerTableViewCell class] forCellReuseIdentifier:myUserBannerCellId];
    [self.myTableView registerClass:[MyDeviceTableViewCell class] forCellReuseIdentifier:myDeviceInfoCellId];
    
    [self.myTableView registerClass:[MyOtherTableViewCell class] forCellReuseIdentifier:MyTrainingCellID];
    [self.myTableView registerClass:[MyOtherTableViewCell class] forCellReuseIdentifier:MyShopCellID];
    [self.myTableView registerClass:[MyOtherTableViewCell class] forCellReuseIdentifier:MyServeCellID];
    [self.myTableView registerClass:[MyOtherTableViewCell class] forCellReuseIdentifier:MyAboutCellID];
}

- (void)initNavigationBarView {
    [self.navigationBarView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.right.equalTo(self.mrkContentView);
        make.height.mas_equalTo(kNavBarHeight);
    }];
    
    [self.settingBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.navigationBarView.mas_right).offset(-15);
        make.centerY.equalTo(self.navigationBarView.mas_top).offset(kStatusBarHeight + 22);
    }];
    [self.settingBtn setEnlargeEdge:15];
    
    [self.messageBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.navigationBarView.mas_right).offset(-55);
        make.centerY.equalTo(self.navigationBarView.mas_top).offset(kStatusBarHeight + 22);
    }];
    [self.messageBtn setEnlargeEdge:15];
    
    [self.serviceBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.navigationBarView.mas_right).offset(-95);
        make.centerY.equalTo(self.navigationBarView.mas_top).offset(kStatusBarHeight + 22);
    }];
    [self.serviceBtn setEnlargeEdge:15];
    
    [self.qrcodeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.navigationBarView.mas_left).offset(15);
        make.centerY.equalTo(self.navigationBarView.mas_top).offset(kStatusBarHeight + 22);
    }];
    [self.qrcodeBtn setEnlargeEdge:15];
}


#pragma mark - UITableViewDelegate, UITableViewDataSource
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return 9;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    switch (section) {
        case 0: case 1: case 2:
            return 1;
            break;
        case 3:
            return self.pageModel.testModelList.count > 0 ? 1:0;
            break;
        case 4:
            return self.pageModel.mineAdverts.count > 0 ? 1:0;
            break;
        case 5:
            return self.trainingDic.count;
            break;
        case 6:
            return self.shopDic.count;
            break;
        case 7:
            return self.serveDic.count;
            break;
        case 8:
            return self.aboutDic.count;
            break;
        default:
            break;
    }
    return 0;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    switch (indexPath.section) {
        case 0:
            return WKDHPX(80)+ kNavBarHeight;
            break;
        case 1: {
            float mainWidth = RealScreenWidth - WKDHPX(16)*2;
            //343/84  为新的占位图宽高比
            return mainWidth *0.24 + WKDHPX(16)*2;
        }   break;
        case 2:
            return WKDHPX(120+12);
            break;
        case 3:
            return WKDHPX(50+12);
            break;
        case 4:
            return WKDHPX(115+12);
            break;
        default:
            break;
    }
    return WKDHPX(56);
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section{
    return 0.001;
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section{
    if (section == 5 || section == 6 || section == 7 || section == 8) {
        return 12;
    }
    return 0.001;
}

- (nullable UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section{
    UIView *v = [[UIView alloc] init];
    if (section > 1) {
        v.backgroundColor = [UIColor colorWithHexString:@"#F3F7FB"];
    } else {
        v.backgroundColor = UIColor.clearColor;
    }
    return v;
}

- (nullable UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section{
    UIView *v = [[UIView alloc] init];
    if (section > 1) {
        v.backgroundColor = [UIColor colorWithHexString:@"#F3F7FB"];
    } else {
        v.backgroundColor = UIColor.clearColor;
    }
    return v;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    switch (indexPath.section) {
        case 0:
            return [self returnAccountInfoCell:indexPath];///用户信息
            break;
        case 1:
            return [self returnVIPInfoCell:indexPath];///vip信息
            break;
        case 2:
            return [self returnDeviceInfoCell:indexPath tableView:tableView];///设备
            break;
        case 3:
            return [self returnAbilityTestCell:indexPath];
            break;
        case 4:
            return [self returnBannerInfoCell:indexPath];///banner
            break;
        case 5:
            return [self returnTrainingInfoCell:indexPath];
            break;
        case 6:
            return [self returnShopInfoCell:indexPath];
            break;
        case 7:
            return [self returnServeInfoCell:indexPath];
            break;
        case 8:
            return [self returnAboutInfoCell:indexPath];
            break;
        default:
            return [UITableViewCell new];
            break;
    }
}

- (UITableViewCell *)returnAccountInfoCell:(NSIndexPath *)indexPath {
    MyAccountInfoTableViewCell *cell = [self.myTableView dequeueReusableCellWithIdentifier:MyAccountInfoTableViewCellId forIndexPath:indexPath];
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    if (self.myUser) {
        cell.currentUser = self.myUser;
        cell.medalUrl = self.pageModel.medalUrl;
    }
    @weakify(self);
    cell.tapBlock = ^{
        @strongify(self);
        MydetailViewController *vc = [[MydetailViewController alloc]init];
        vc.hidesBottomBarWhenPushed = YES;
        [self.navigationController pushViewController:vc animated:YES];
    };
    cell.growUpBlock = ^{
        @strongify(self);
        MRKMyGrowUpController *vc = [[MRKMyGrowUpController alloc]init];
        vc.growUserModel = self.growUpModel;
        [self.navigationController pushViewController:vc animated:YES];
    };
    cell.medalBlock = ^{
        MRKMedalController *vc = [[MRKMedalController alloc]init];
        vc.growUserModel = self.growUpModel;
        [self.navigationController pushViewController:vc animated:YES];
    };
    return cell;
}

- (UITableViewCell *)returnVIPInfoCell:(NSIndexPath *)indexPath {
    MyVipInfoTableViewCell *cell = [self.myTableView dequeueReusableCellWithIdentifier:MyVipInfoTableViewCellId forIndexPath:indexPath];
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    if (self.myUser) {
        cell.user = self.myUser;
        cell.vipModel = self.pageModel.vipModel;
    }
    return cell;
}


- (UITableViewCell *)returnAbilityTestCell:(NSIndexPath *)indexPath {
    MyAbilityTestTableCell *cell = [self.myTableView dequeueReusableCellWithIdentifier:myAbilityTestCellId forIndexPath:indexPath];
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    [cell updateAbilityTestUI:self.pageModel.testModelList];
    return cell;
}

- (UITableViewCell *)returnDeviceInfoCell:(NSIndexPath *)indexPath tableView:(UITableView *)tableView {
    MyDeviceTableViewCell *cell = [self.myTableView dequeueReusableCellWithIdentifier:myDeviceInfoCellId forIndexPath:indexPath];
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    cell.deviceModel = self.pageModel.deviceModel;
    @weakify(self);
    cell.deviceMoreClickBlock = ^{
        @strongify(self);
        MyDeviceListViewController *vc = [[MyDeviceListViewController alloc] init];
        [self.navigationController pushViewController:vc animated:YES];
    };
    cell.deviceDetailClickBlock = ^(MRKDeviceModel * _Nonnull model) {
        @strongify(self);
        tableView.traceEventId = @"btn_details_click";
        if (model) {
            MRKDeviceDetailController *vc = [[MRKDeviceDetailController alloc] init];
            vc.model = model;
            [self.navigationController pushViewController:vc animated:YES];
        }else{
            [self  deviceSearchVC];
        }
    };
    return cell;
}

- (UITableViewCell *)returnBannerInfoCell:(NSIndexPath *)indexPath {
    MRKUserBannerTableViewCell *cell = [self.myTableView dequeueReusableCellWithIdentifier:myUserBannerCellId forIndexPath:indexPath];
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    cell.dataArray = self.pageModel.mineAdverts;
    cell.bannerSelectBlock = ^(AdvertModel * _Nonnull model) {
        ///banner点击
        [[NSNotificationCenter defaultCenter] postNotificationName:AdvertSkipToPageNotification object:@{@"model":model, @"position":model.bannerType ?: @(BannerPage)}];
        ReportMrkLogParms(2, @"我的banner点击", @"page_profile", @"btn_all_banner", nil, 0, nil);
    };
    return cell;
}

- (UITableViewCell *)returnTrainingInfoCell:(NSIndexPath *)indexPath {
    MyOtherTableViewCell *cell = [self.myTableView dequeueReusableCellWithIdentifier:MyTrainingCellID forIndexPath:indexPath];
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    [cell configWithItem:self.trainingDic[indexPath.row] indexPath:indexPath];
    cell.roundCorners = ({
        UIRectCorner corners = 0;
        BOOL isFirstRow = (indexPath.row == 0);
        BOOL isLastRow = (indexPath.row == self.trainingDic.count-1);
        if (isFirstRow) {///添加左上、右上圆角
            corners = corners | UIRectCornerTopLeft | UIRectCornerTopRight;
        }
        if (isLastRow) {///添加左下、右下圆角
            corners = corners | UIRectCornerBottomLeft | UIRectCornerBottomRight;
        }
        corners;
    });
    return cell;
}
- (UITableViewCell *)returnShopInfoCell:(NSIndexPath *)indexPath {
    MyOtherTableViewCell *cell = [self.myTableView dequeueReusableCellWithIdentifier:MyShopCellID forIndexPath:indexPath];
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    [cell configWithItem:self.shopDic[indexPath.row] indexPath:indexPath];
    cell.roundCorners = ({
        UIRectCorner corners = 0;
        BOOL isFirstRow = (indexPath.row == 0);
        BOOL isLastRow = (indexPath.row == self.shopDic.count-1);
        if (isFirstRow) {///添加左上、右上圆角
            corners = UIRectCornerTopLeft | UIRectCornerTopRight;
        }
        if (isLastRow) {///添加左下、右下圆角
            corners = UIRectCornerBottomLeft | UIRectCornerBottomRight;
        }
        corners;
    });
    return cell;
}
- (UITableViewCell *)returnServeInfoCell:(NSIndexPath *)indexPath {
    MyOtherTableViewCell *cell = [self.myTableView dequeueReusableCellWithIdentifier:MyServeCellID forIndexPath:indexPath];
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    [cell configWithItem:self.serveDic[indexPath.row] indexPath:indexPath];
    cell.roundCorners = ({
        UIRectCorner corners = 0;
        BOOL isFirstRow = (indexPath.row == 0);
        BOOL isLastRow = (indexPath.row == self.serveDic.count-1);
        if (isFirstRow) {///添加左上、右上圆角
            corners = UIRectCornerTopLeft | UIRectCornerTopRight;
        }
        if (isLastRow) {///添加左下、右下圆角
            corners = UIRectCornerBottomLeft | UIRectCornerBottomRight;
        }
        corners;
    });
    return cell;
}
- (UITableViewCell *)returnAboutInfoCell:(NSIndexPath *)indexPath {
    MyOtherTableViewCell *cell = [self.myTableView dequeueReusableCellWithIdentifier:MyAboutCellID forIndexPath:indexPath];
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    [cell configWithItem:self.aboutDic[indexPath.row] indexPath:indexPath];
    cell.roundCorners = ({
        UIRectCorner corners = 0;
        BOOL isFirstRow = (indexPath.row == 0);
        BOOL isLastRow = (indexPath.row == self.aboutDic.count - 1);
        if (isFirstRow) {///添加左上、右上圆角
            corners = UIRectCornerTopLeft | UIRectCornerTopRight;
        }
        if (isLastRow) {///添加左下、右下圆角
            corners = UIRectCornerBottomLeft | UIRectCornerBottomRight;
        }
        corners;
    });
    return cell;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    switch (indexPath.section) {
        case 0:{
            //个人信息
        }break;
        case 1:{
            //会员信息
            if (self.myUser.memberInfo.isMember == YES) {
                tableView.traceEventId = @"btn_profile_vip_right";
            } else {
                tableView.traceEventId = @"btn_profile_vip_open";
            }
            [DYFStoreManager shared].appPurchaseSource = TraceVipOpenSourceTypeMyInfo;
            [[RouteManager sharedInstance] skipVIP];///直接跳转到会员页
        }break;
        case 2:{
            
        }break;
        case 3:{
            tableView.traceEventId = @"btn_evaluate_question";
//            __block BOOL hasContains = NO;
//            [self.pageModel.testModelList enumerateObjectsUsingBlock:^(MRKAbilityTestModel * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
//                if (obj.isFinish){
//                    hasContains = YES;
//                    *stop = YES;
//                }
//            }];
//            if (hasContains){
//                AbilityTestResultController *vc = [[AbilityTestResultController alloc] init];
//                [self.navigationController pushViewController:vc animated:YES];
//            }else{
                AbilityIntroduceVC *vc = [[AbilityIntroduceVC alloc] init];
                [self.navigationController pushViewController:vc animated:YES];
//            }
        }break;
        case 4:{

        }break;
        case 5:{
            if (indexPath.row == 0) { //训练记录
                tableView.traceEventId = @"btn_profile_trainRecord";
                TrainRecordController *vc = [TrainRecordController new];
                vc.hidesBottomBarWhenPushed = YES;
                [self.navigationController pushViewController:vc animated:YES];
            }else if (indexPath.row == 1) { //收藏的课程
                tableView.traceEventId = @"btn_profile_savedclass";
                MRKCourseCollectController *vc = [[MRKCourseCollectController alloc]init];
                [self.navigationController pushViewController:vc animated:YES];
            }else if (indexPath.row == 2) {  //关注的教练
                tableView.traceEventId = @"btn_profile_followedcoach";
                FollowCoachController *vc = [[FollowCoachController alloc]init];
                [self.navigationController pushViewController:vc animated:YES];
            }
        }break;
        case 6:{
            if (indexPath.row == 0) {
                ///商城
                MRKGeneralWebController *vc = [[MRKGeneralWebController alloc] init];
                vc.httpUrl = MRKMeritShop;
                vc.hidesBottomBarWhenPushed = YES;
                [self.navigationController pushViewController:vc animated:YES];
                
            } else if (indexPath.row == 1) {
                ///活动赛事
                WebViewViewController *vc = [WebViewViewController new];
                vc.htmlURL = MRKAppH5LinkCombine(MRKActiveExercise);
                vc.isHiddenNav = YES;
                [self.navigationController pushViewController:vc animated:YES];
            } else if (indexPath.row == 2) {
               ///运动搭子
                WebViewViewController *vc = [WebViewViewController new];
                vc.htmlURL = [NSString stringWithFormat:@"%@%@",WebBase_URL,MRKTrainPart];
                vc.isHiddenNav = YES;
                [self.navigationController pushViewController:vc animated:YES];
            }
        }break;
        case 7:{
            if (indexPath.row == 0) { //第三方应用与服务
                tableView.traceEventId = @"btn_profile_thirdParty";
                MRKHealthListController *vc = [[MRKHealthListController alloc] init];
                [self.navigationController pushViewController:vc animated:YES];
                
            } else if (indexPath.row == 1) { //家庭成员
                tableView.traceEventId = @"btn_profile_roommate";
                MyFamilyViewController *vc = [[MyFamilyViewController alloc]init];
                [self.navigationController pushViewController:vc animated:YES];
                
            } else if (indexPath.row == 2) { //产品百科
                tableView.traceEventId = @"btn_profile_encyclopedias";
             
                WebViewViewController *vc = [WebViewViewController new];
                vc.isHiddenNav = YES;
                vc.htmlURL = MRKAppH5LinkCombine(MRKLinkProductEncyclopedia);
                [self.navigationController pushViewController:vc animated:YES];
            }
        }break;
        case 8:{
            if (indexPath.row == 0) {  //评价我们
                tableView.traceEventId = @"btn_profile_evaluation";
                [self requestEvaluateData];
            } else if (indexPath.row == 1) {//举报与反馈
                tableView.traceEventId = @"btn_profile_help_advice";
                [self pushReportingAndFeedback];
            }
        }break;
        default:
            break;
    }
}


- (void)deviceSearchVC{
    ///新用户链路拦截处理一下[默认是hasBind数据]
    [[MRKLinkRouterManager sharedInstance] newUserBuildComplete:^(BOOL hasBind) {
        if (hasBind){
            DeviceSearchViewController *vc = [DeviceSearchViewController new];
            [self.navigationController pushViewController:vc animated:YES];
        }
    }];
}

///评价我们数据
- (void)requestEvaluateData{
    @weakify(self);
    [MRKBaseRequest mrkRequestType:YTKRequestMethodPOST
                               url:@"/app/app-comment/get-app-commont"
                           andParm:@{}
          completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        id data = [request.responseObject valueForKeyPath:@"data"];
        NSMutableArray<MRKMineEvaluateModel *>* evaluateList = [NSMutableArray arrayWithArray:[NSArray modelArrayWithClass:[MRKMineEvaluateModel class] json:data]];
        [self_weak_ jumpToEvaluateVC:evaluateList];
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        [MBProgressHUD showError: request.error.userInfo.description];
    }];
}

- (void)jumpToEvaluateVC:(NSMutableArray<MRKMineEvaluateModel *>*)evaluateList {
    if (!self.alert.isShowOnWindow) {
        self.alert = [[MRKMineEvaluateAlertView alloc] initWithAnimationStyle:MRKActionAlertViewTransitionStyleSlideFromBottom];
    }
    self.alert.evaluateList = evaluateList;
    self.alert.opaquess = 0.6;
    [[MRKPopupManager sharedInstance] showAlertView:self.alert level:MRKPopupViewLevelHeight callback:nil];
}


#pragma mark - UIScrollViewDelegate
- (void)scrollViewDidScroll:(UIScrollView *)scrollView{
    if (scrollView.contentOffset.y > 64 + kStatusBarHeight) {
        self.navigationBarView.alpha = 1;
    } else {
        self.navigationBarView.alpha = scrollView.contentOffset.y / (64 + kStatusBarHeight);
    }
    
    float height = WKDHPX(76) +kNavBarHeight +(RealScreenWidth -WKDHPX(16)*2)*0.24 +WKDHPX(16)*2 +WKDHPX(17);
    self.bgImageView.frame = CGRectMake(0, -scrollView.contentOffset.y, RealScreenWidth, height);
}



#pragma mark 点击
- (void)personClick {
    MydetailViewController * detailView = [[MydetailViewController alloc]init];
    detailView.hidesBottomBarWhenPushed = YES;
    [self.navigationController pushViewController:detailView animated:YES];
}

#pragma mark - Button click
//七鱼意见反馈与建议 4.11 改为举报与反馈
- (void)serviceControllerAction:(UIButton *)sender {
    sender.traceEventId = @"btn_help_advice_wechatgroup";

    WebViewViewController *vc = [WebViewViewController new];
    vc.isHiddenNav = YES;
    vc.htmlURL = MRKAppH5LinkCombine(MRKUserHelpAndFeedback);
    [self.navigationController pushViewController:vc animated:YES];
}

- (void)pushReportingAndFeedback {
    MyVoiceViewController*vc = [[MyVoiceViewController alloc] init];
    vc.navTitle = @"举报与反馈";
    vc.type = 1;
    vc.hidesBottomBarWhenPushed = YES;
    [self.navigationController pushViewController:vc animated:YES];
}

- (void)pushToMessageControllerAction:(UIButton *)sender {
    sender.traceEventId = @"btn_profile_message";

//    GetNewsViewController *vc = [[GetNewsViewController alloc] init];
//    vc.hidesBottomBarWhenPushed = YES;
//    [self.navigationController pushViewController:vc animated:YES];
    
    MrkTestAlertView *alert = [[MrkTestAlertView alloc] initWithAnimationStyle:MRKAlertViewTransitionStyleSlideFromBottom];
    alert.isAutoHidden = YES;
//    [alert show];
    
    // 或者嵌入模式
     [alert showInView:self.view];
}

- (void)settingControllerAction:(UIButton *)sender {
    sender.traceEventId = @"btn_profile_setting";

    DesignViewController *vc = [[DesignViewController alloc]init];
    vc.hidesBottomBarWhenPushed = YES;
    [self.navigationController pushViewController:vc animated:YES];
}

- (void)qrcodeAction:(UIButton *)sender {
    [[RouteManager sharedInstance] jumpToScanQrcodeVC: nil];;
}

#pragma mark - lazy init
- (UIImageView *)navigationBarView {
    if (!_navigationBarView) {
        _navigationBarView = [[UIImageView alloc] init];
        _navigationBarView.alpha = 0;
        _navigationBarView.userInteractionEnabled = YES;
    }
    return _navigationBarView;
}

- (UIImageView *)bgImageView {
    if (!_bgImageView) {
        _bgImageView = [[UIImageView alloc] init];
        _bgImageView.contentMode = UIViewContentModeScaleToFill;
        _bgImageView.clipsToBounds = YES;
    }
    return _bgImageView;
}

- (UITableView *)myTableView {
    if (!_myTableView) {
        _myTableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStyleGrouped];
        _myTableView.backgroundColor = [UIColor clearColor];
        _myTableView.delegate = self;
        _myTableView.dataSource = self;
        _myTableView.showsVerticalScrollIndicator = NO;
        _myTableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _myTableView.tableFooterView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, RealScreenWidth, 20)];
        _myTableView.mj_header = [MJDIYHeader headerWithRefreshingTarget:self refreshingAction:@selector(refresh)];
        _myTableView.mj_header.automaticallyChangeAlpha = YES;
    }
    return _myTableView;
}

- (UIButton *)serviceBtn {
    if (!_serviceBtn) {
        _serviceBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_serviceBtn addTarget:self action:@selector(serviceControllerAction:) forControlEvents:UIControlEventTouchUpInside];
        [_serviceBtn setImage:[[UIImage imageNamed:@"icon_customer_service"] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate] forState:UIControlStateNormal];
    }
    return _serviceBtn;
}

- (UIButton *)messageBtn {
    if (!_messageBtn) {
        _messageBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_messageBtn setImage:[[UIImage imageNamed:@"icon_news"] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate] forState:UIControlStateNormal];
        [_messageBtn addTarget:self action:@selector(pushToMessageControllerAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _messageBtn;
}

- (UIButton *)settingBtn {
    if (!_settingBtn) {
        _settingBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_settingBtn addTarget:self action:@selector(settingControllerAction:) forControlEvents:UIControlEventTouchUpInside];
        [_settingBtn setImage:[[UIImage imageNamed:@"icon_nav_settings"] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate] forState:UIControlStateNormal];
    }
    return _settingBtn;
}

- (UIButton *)qrcodeBtn {
    if (!_qrcodeBtn) {
        _qrcodeBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_qrcodeBtn addTarget:self action:@selector(qrcodeAction:) forControlEvents:UIControlEventTouchUpInside];
        [_qrcodeBtn setImage:[[UIImage imageNamed:@"qrcode_black"] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate] forState:UIControlStateNormal];
        _qrcodeBtn.hidden = YES; // 默认关闭
    }
    return _qrcodeBtn;
}


@end








