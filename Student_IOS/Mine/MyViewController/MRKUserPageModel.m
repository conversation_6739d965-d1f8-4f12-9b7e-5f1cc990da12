//
//  MRKUserPageModel.m
//  Student_IOS
//
//  Created by merit on 2022/11/1.
//

#import "MRKUserPageModel.h"
#import "MRKUserInfoRequest.h"
#import "MRKHealthViewModel.h"
#import "MRKDeviceURLRequest.h"
#import "MRKMedalViewModel.h"

static NSString *const userInfoSiganlName = @"UserPageSiganlName";

@interface MRKUserPageModel ()
@property (nonatomic, strong) RACSubject *userInfoSignal;
@end

@implementation MRKUserPageModel

- (instancetype)init {
    self = [super init];
    if(self){
        self.userInfoSignal = [[RACSubject subject] setNameWithFormat:userInfoSiganlName];
        
        //会员开通刷新用户信息
        //有可能会员会延时到账
        [[NSNotificationCenter defaultCenter] addObserver:self
                                                 selector:@selector(updateUserInfo)
                                                     name:@"UpdateUserInfo"
                                                   object:nil];
    }
    return self;
}

- (void)refreshDataSource {
    @weakify(self)
    RACSignal *userAction = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self requestUserInfo:^{
            [subscriber sendNext:@1];
        }];
        return nil;
    }];

    RACSignal *readAction = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self requestUnreadData:^{
            [subscriber sendNext:@4];
        }];
        return nil;
    }];
    
    RACSignal *testAction = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self requestTestResult:^{
            [subscriber sendNext:@5];
        }];
        return nil;
    }];
    
    RACSignal *autoAction = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self getVipPageBanner:^{
            [subscriber sendNext:@7];
        }];
        return nil;
    }];
    
    RACSignal *deviceAction = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self requestDeviceListData:^{
            [subscriber sendNext:@8];
        }];
        return nil;
    }];
    
    RACSignal *medalAction = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self requestUserMedalData:^{
            [subscriber sendNext:@9];
        }];
        return nil;
    }];
    
    RACSignal *vipAction = [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self);
        [self requestVipInfo:^{
            [subscriber sendNext:@10];
        }];
        return nil;
    }];
    
    
    NSMutableArray *arr = [NSMutableArray arrayWithArray:@[userAction, readAction, testAction, autoAction, deviceAction, medalAction]];
    if (UserInfo.isNormalMember) {
        [arr appendObject:vipAction];
    }
    
    [[RACSignal merge:arr] subscribeNext:^(id x) {
        @strongify(self);
        [(RACSubject *)self.userInfoSignal sendNext:x];
    }];
}


- (void)updateUserInfo {
    @weakify(self);
    [self requestUserInfo:^{
        @strongify(self);
        
        if (UserInfo.isNormalMember) {
            [self requestVipInfo:nil];
        }
    }];
}

#pragma mark - 用户数据 -
- (void)requestUserInfo:(void(^)(void))completion {
    @weakify(self)
    MRKUserInfoRequest *infoRequest = [[MRKUserInfoRequest alloc] init];
    [infoRequest startWithCompletionBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        @strongify(self)
        id data = [request.responseObject valueForKeyPath:@"data"];
        NSLog(@"userInfo ==== %@", data);
        self.userInfo = [User modelWithJSON:data];
        if (completion) {
            completion();
        }
        
        ///触发本地绝影会员皮肤更换
        BOOL newXEnjoyVip = self.userInfo.memberInfo.isMember && self.userInfo.memberInfo.vipType == 30;
        if (!UserInfo.isXEnjoyVip && newXEnjoyVip) {
            [LEETheme startTheme:THEME_XENJOY];
        }

        [Login doLogin:data];
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        if (completion) {
            completion();
        }
    }];
}

#pragma mark - 会员数据 -
- (void)requestVipInfo:(void(^)(void))completion {
    @weakify(self)
    [MRKBaseRequest mrkRequestType:YTKRequestMethodGET
                               url:@"/user/member-product/get-cheapest-offer" // 获取最优价格产品报价
                           andParm:@{}
                      notShowError:YES
          completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        @strongify(self)
        id data = [request.responseObject valueForKeyPath:@"data"];
        MRKVipCheapestModel *item = [MRKVipCheapestModel modelWithJSON:data];
        self.vipModel = item;
        if (completion) {
            completion();
        }
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        
    }];
}

- (void)requestTestResult:(void(^)(void))completion{
    @weakify(self)
    [MRKBaseRequest mrkGetRequestUrl:@"/user/athleticismAssess/list"
                             andParm:nil
            completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        @strongify(self)
        id data = [request.responseObject objectForKey:@"data"];
        NSArray *tags = [NSArray modelArrayWithClass:[MRKAbilityTestModel class] json:data];
        self.testModelList = tags;
        completion();
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        completion();
    }];
}

#pragma mark - 设备列表数据 -
- (void)requestDeviceListData:(void(^)(void))completion{
    @weakify(self)
    NSDictionary *parms = @{ @"size" : @10, @"current" : @1};
    [MRKDeviceURLRequest requestMyDevice:parms success:^(NSArray *data) {
        @strongify(self)
        self.showScanQrcode = NO;
        if (data.count > 0){
            self.deviceModel = data.firstObject;
            for (int i = 0; i < data.count; i ++) {
                MRKDeviceModel *devi = data[i];
                if ([[MRKDeviceManager shareManager] isJudgeBlufiDevice:devi.communicationType]) {
                    self.showScanQrcode = YES;
                    break;
                }
            }
        }else{
            self.deviceModel = nil;
        }
        completion();
    } fail:^(NSError * data) {
        completion();
    }];
}

#pragma mark - Banner -
- (void)getVipPageBanner:(void(^)(void))completion{
    @weakify(self)
    [MRKAdvertManager mrkRequestPositionCode:MRKUcenterBannerCode
                    completeBlockWithSuccess:^(MRKAdvertDataModel * _Nullable model) {
        @strongify(self)
        self.mineAdverts = model.adverts.mutableCopy;
        completion();
    }];
}

#pragma mark - 三方应用授权数据 -
- (void)requestHealthAutoConnect:(void(^)(void))completion {
    @weakify(self)
    [MRKHealthViewModel requestHealthIsConnect:^(BOOL connect) {
        @strongify(self)
        self.healthAuto = connect;
        completion();
    }];
}

#pragma mark - 消息未读数据 -
- (void)requestUnreadData:(void(^)(void))completion {
    @weakify(self)
    [MRKBaseRequest mrkGetRequestUrl:@"/message/message/userMessageAssociated/getUnReadNum"
                             andParm:nil
            completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        @strongify(self)
        id data = [request.responseObject valueForKeyPath:@"data"];
        self.unreadNum = [data integerValue];
        completion();
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        completion();
    }];
}

#pragma mark - 勋章数据 -
- (void)requestUserMedalData:(void(^)(void))completion {
    @weakify(self)
    [MRKMedalViewModel requestMedalWall:^(NSArray<MRKMedalWallModel *> * _Nonnull data) {
        @strongify(self)
        if (data.count > 0){
            MRKMedalWallModel *model = data.firstObject;
            if (model.items.count > 0){
                MRKMedalWallItemModel *item = model.items.firstObject;
                self.medalUrl = item.cover;
            }
        }
        completion();
    } failure:^{
        completion();
    }];
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

@end

