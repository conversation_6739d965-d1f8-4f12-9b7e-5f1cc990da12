//
//  MyVipInfoTableViewCell.m
//  Student_IOS
//
//  Created by 麦瑞克 on 2021/9/16.
//

#import "MyVipInfoTableViewCell.h"
#import "Lottie_OC.h"
#import "UIView+AZGradient.h"

@interface MyVipInfoTableViewCell ()
@property (nonatomic, strong) UIImageView *backHolderImage;

@property (nonatomic, strong) UIImageView *backGroundImage;
@property (nonatomic, strong) UILabel *vipDescipLab;
@property (nonatomic, strong) LOTAnimationView *animationView;
@property (nonatomic, strong) UILabel *vipPriceLab;
@property (nonatomic, strong) UIButton *averageDailyVue;
@end

@implementation MyVipInfoTableViewCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.backgroundColor = [UIColor clearColor];
        
        [self.contentView addSubview:self.backHolderImage];
        [self.contentView addSubview:self.backGroundImage];
        [self.backGroundImage addSubview:self.vipDescipLab];
        [self.backGroundImage addSubview:self.vipPriceLab];
        [self.backGroundImage addSubview:self.averageDailyVue];
        
        [self.backHolderImage mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.contentView.mas_centerY);
            make.left.mas_equalTo(self.contentView.mas_left);
            make.right.mas_equalTo(self.contentView.mas_right);
            make.bottom.mas_equalTo(self.contentView.mas_bottom);
        }];
        
        float mainWidth = RealScreenWidth - WKDHPX(16)*2;
        float height = mainWidth  * 84 / 343;
        [self.backGroundImage mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.left.mas_equalTo(WKDHPX(16));
            make.right.mas_equalTo(-WKDHPX(16));
            make.height.mas_equalTo(height);
        }];
        
        [self.vipDescipLab mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.backGroundImage.mas_left).offset(16 / 343.0 *mainWidth);
            make.bottom.equalTo(self.backGroundImage.mas_bottom).offset(- 10 / 84.0 *height);
        }];
        
        [self.vipPriceLab mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.backGroundImage.mas_left).offset(16 / 343.0 *mainWidth);
            make.top.equalTo(self.backGroundImage.mas_top).offset(31 / 84.0 *height);
        }];
        
        [self.averageDailyVue mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(self.backGroundImage).offset(-12 / 343.0 *mainWidth);
            make.top.equalTo(self.backGroundImage.mas_top).offset(7 / 84.0 *height);
            make.height.mas_equalTo(18 / 84.0 *height);
        }];
        ///默认执行一遍
        self.user = [Login curLoginUser];
    }
    return self;
}

- (void)layoutSubviews{
    [super layoutSubviews];
    [self.averageDailyVue partCornerRadius:WKDHPX(8) byRoundingCorners:UIRectCornerTopLeft | UIRectCornerBottomRight];
}

- (void)setUser:(User *)user {
    _user = user;
    
    float mainWidth = RealScreenWidth - WKDHPX(16)*2;
    float height = mainWidth  * 84 / 343.0;
    self.vipPriceLab.hidden = YES;
    self.averageDailyVue.hidden = YES;
    
    self.vipDescipLab.font = kSystem_Font_NoDHPX(12 / 84.0 * height);
    self.vipPriceLab.font = kMedium_Font_NoDHPX(15 / 84.0 * height);
    self.averageDailyVue.titleLabel.font = kSystem_Font_NoDHPX(11 / 84.0 * height);
    
    self.vipDescipLab.text = @"";
    self.vipDescipLab.attributedText = [[NSMutableAttributedString alloc] initWithString:@""];
    
    BOOL isVip = user.memberInfo.isMember;
    BOOL needRenew = (user.memberInfo.days <= 15 && user.memberInfo.days > 0);
    
    if (user.memberInfo.vipType == 30 ) {
        self.backGroundImage.image = [UIImage imageNamed:({
            NSString *imageName = @"";
            if (!isVip) {
                imageName = @"mine_xenjoy_bg_unopen";
            }else {
                imageName = needRenew ? @"mine_xenjoy_animation_bg" : @"mine_xenjoy_bg_open";
            }
            imageName;
        })];
        
        self.vipDescipLab.hidden = !user.memberInfo.isMember;
        self.vipDescipLab.textColor = [UIColor colorWithHexString:@"#FAEBD8"];
        NSString *day = @"";
        if (needRenew) {
            day = [NSString stringWithFormat:@" 还剩 %d 天", user.memberInfo.days];
        }
        self.vipDescipLab.text = [NSString stringWithFormat:@"绝影VIP 至 %@%@", user.memberInfo.expireDate, day];
        
    } else {
        self.backGroundImage.image = [UIImage imageNamed:({
            NSString *imageName = @"";
            if (!isVip) {
                imageName = @"bg_vip_card_unopen";
            }else {
                imageName = needRenew ? @"mine_vip_animation_bg" : @"bg_vip_card_opened";
            }
            imageName;
        })];
        self.vipPriceLab.hidden = isVip;
        self.averageDailyVue.hidden = isVip;
        self.vipDescipLab.hidden = !isVip;
        self.vipDescipLab.textColor = [UIColor colorWithHexString: @"#EDFFFE"];
        self.vipDescipLab.attributedText = ({
            NSMutableAttributedString *dayStr = [[NSMutableAttributedString alloc] initWithString:@""];
            if (needRenew) {
                NSString *day = [NSString stringWithFormat:@"%d", user.memberInfo.days];
                NSString *tipDayStr = [NSString stringWithFormat:@" 仅剩 %@ 天", day];
                NSMutableAttributedString *attributeText = [[NSMutableAttributedString alloc] initWithString:tipDayStr];
                attributeText.color = UIColorHex(#363A44);
                attributeText.font = kSystem_Font_NoDHPX(12 / 84.0 * height);
                NSRange textRang = [tipDayStr rangeOfString:day];
                [attributeText setFont:kMedium_Font_NoDHPX(15 / 84.0 * height) range:textRang];
                dayStr = attributeText;
            }
         
            NSString *tipDateStr = [NSString stringWithFormat:@"您的会员有效期至 %@", user.memberInfo.expireDate];
            NSMutableAttributedString *attributeText = [[NSMutableAttributedString alloc] initWithString:tipDateStr];
            attributeText.color = UIColorHex(#363A44);
            attributeText.font = kSystem_Font_NoDHPX(12 / 84.0 * height);
            [attributeText appendAttributedString:dayStr];
            attributeText;
        });
    }
    
    if (self.animationView) {
        [self.animationView removeFromSuperview];
    }
    self.animationView.hidden = !needRenew;
    if (needRenew) {
        self.animationView = [LOTAnimationView animationNamed: user.memberInfo.vipType == 20 ? @"advent_motion_black" : @"advent_motion"];
        [self.backGroundImage addSubview:self.animationView];
        [self.animationView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(16/84.0*height);
            make.right.mas_equalTo(-WKDHPX(16));
            make.size.mas_equalTo(CGSizeMake(78/84.0*height, 40/84.0*height));
        }];
        [self.animationView play];
        [self.animationView setLoopAnimation:YES];
    }
}

- (void)setVipModel:(MRKVipCheapestModel *)vipModel {
    _vipModel = vipModel;
    self.vipPriceLab.text = [NSString stringWithFormat:@"¥%@开通%@", vipModel.discountPrice, vipModel.vipPackageTypeStr];
    [self.averageDailyVue setTitle:[NSString stringWithFormat:@" ¥%@起/天 ", vipModel.dailyStartPrice] forState:UIControlStateNormal];
}


- (void)UpdataUser:(User *)user andVipCheapest:(MRKVipCheapestModel *)vipModel{
    
}





#pragma mark - lazy init
- (UIImageView *)backGroundImage{
    if (!_backGroundImage) {
        _backGroundImage = [UIImageView new];
    }
    return _backGroundImage;
}

- (UILabel *)vipDescipLab {
    if (!_vipDescipLab) {
        _vipDescipLab = [[UILabel alloc] init];
    }
    return _vipDescipLab;
}

- (UILabel *)vipPriceLab {
    if (!_vipPriceLab) {
        _vipPriceLab = [[UILabel alloc] init];
        _vipPriceLab.textColor = [UIColor whiteColor];
        _vipPriceLab.hidden = YES;
    }
    return _vipPriceLab;
}

- (UIButton *)averageDailyVue {
    if (!_averageDailyVue) {
        _averageDailyVue = [UIButton buttonWithType:UIButtonTypeCustom];
        _averageDailyVue.userInteractionEnabled = NO;
        [_averageDailyVue setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        [_averageDailyVue az_setGradientBackgroundWithColors:@[[UIColor colorWithHexString:@"#EF3473"],[UIColor colorWithHexString:@"#FE2550"]]
                                                locations:@[@0,@0.5]
                                               startPoint:CGPointMake(0, 0.5)
                                                 endPoint:CGPointMake(1, 0.5)];
        _averageDailyVue.hidden = YES;
    }
    return _averageDailyVue;
}

- (UIImageView *)backHolderImage{
    if (!_backHolderImage) {
        _backHolderImage = [[UIImageView alloc] init];
        UIImage *image = [UIImage imageWithColor:[UIColor colorWithHexString:@"#F3F7FB"] size:CGSizeMake(RealScreenWidth, 100)];
        image = [image imageByRoundCornerRadius:8
                                        corners:UIRectCornerTopLeft|UIRectCornerTopRight
                                    borderWidth:0
                                    borderColor:nil
                                 borderLineJoin:kCGLineJoinMiter];
        _backHolderImage.image = image;
        _backHolderImage.contentMode = UIViewContentModeScaleToFill;
        _backHolderImage.clipsToBounds = YES;
    }
    return _backHolderImage;
}
@end
