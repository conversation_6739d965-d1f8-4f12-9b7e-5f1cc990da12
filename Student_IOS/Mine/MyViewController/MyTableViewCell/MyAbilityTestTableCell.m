//
//  MyAbilityTestTableCell.m
//  Student_IOS
//
//  Created by Junq on 2024/7/4.
//

#import "MyAbilityTestTableCell.h"
#import "MrkGradientView.h"

@interface MyAbilityTestTableCell ()
@property (nonatomic, strong) MrkGradientView *backGradientView;
@property (nonatomic, strong) UIImageView *iconImageView;
@property (nonatomic, strong) UILabel *testTitle;
@property (nonatomic, strong) UILabel *testDescrip;
@end

@implementation MyAbilityTestTableCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.backgroundColor = [UIColor clearColor];
        [self initView];
    }
    return self;
}

- (void)initView {
    [self.contentView addSubview:self.backGradientView];
    [self.backGradientView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentView.mas_top);
        make.left.equalTo(self.contentView.mas_left).offset(WKDHPX(16));
        make.right.equalTo(self.contentView.mas_right).offset(-WKDHPX(16));
        make.bottom.equalTo(self.contentView.mas_bottom).offset(-WKDHPX(12));
    }];
    
    [self.backGradientView addSubview:self.iconImageView];
    [self.backGradientView addSubview:self.testTitle];
    [self.backGradientView addSubview:self.testDescrip];
    [self.iconImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.backGradientView.mas_centerY);
        make.left.equalTo(self.backGradientView.mas_left).offset(WKDHPX(12));
        make.size.mas_equalTo(CGSizeMake(WKDHPX(28), WKDHPX(28)));
    }];
    [self.testTitle mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.iconImageView.mas_centerY);
        make.left.equalTo(self.iconImageView.mas_right).offset(WKDHPX(8));
    }];
    [self.testDescrip mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.iconImageView.mas_centerY);
        make.right.equalTo(self.backGradientView.mas_right).offset(-WKDHPX(16));
        make.size.mas_equalTo(CGSizeMake(WKDHPX(64), WKDHPX(26)));
    }];
}

- (MrkGradientView *)backGradientView {
    if (!_backGradientView) {
        _backGradientView = [[MrkGradientView alloc] init];
        _backGradientView.layer.cornerRadius = WKDHPX(8.0);
        _backGradientView.drawsGradientBackground = YES;
        _backGradientView.gradientStartPoint = CGPointMake(0, 0);
        _backGradientView.gradientEndPoint = CGPointMake(1, 0);
        _backGradientView.gradientLocations = @[@0, @1];
        _backGradientView.gradientLayerColors = @[(__bridge id)[UIColorHex(#E2FAFF) CGColor],
                                                  (__bridge id)[UIColorHex(#FFFFFF) CGColor]];
    }
    return _backGradientView;
}

- (UIImageView *)iconImageView {
    if (!_iconImageView) {
        UIImageView *imagev = [[UIImageView alloc] init];
        imagev.contentMode = UIViewContentModeScaleToFill;
        _iconImageView = imagev;
    }
    return _iconImageView;
}

- (UILabel *)testTitle {
    if (!_testTitle){
        UILabel *label = [[UILabel alloc] init];
        label.font = kSystem_Font_NoDHPX(WKDHPX(14));
        label.textColor = [UIColor colorWithHexString:@"#363A44"];
        _testTitle = label;
    }
    return _testTitle;
}
- (UILabel *)testDescrip {
    if (!_testDescrip){
        UILabel *label = [[UILabel alloc] init];
        label.font = kSystem_Font_NoDHPX(WKDHPX(14));
        label.textColor = [UIColor colorWithHexString:@"#16D2E3"];
        label.layer.cornerRadius = WKDHPX(26)/2;
        label.layer.masksToBounds = YES;
        label.layer.borderColor = [UIColor clearColor].CGColor;
        label.layer.borderWidth = CGFloatFromPixel(1.0);
        _testDescrip = label;
    }
    return _testDescrip;
}

- (void)updateAbilityTestUI:(NSArray<MRKAbilityTestModel *> *)arr{
//    __block BOOL hasContains = NO;
//    [arr enumerateObjectsUsingBlock:^(MRKAbilityTestModel * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
//        if (obj.isFinish){
//            hasContains = YES;
//            *stop = YES;
//        }
//    }];
    BOOL hasContains = NO;
    self.iconImageView.image = [UIImage imageNamed:hasContains ? @"home_test_finish" : @"img_test"];
    self.testTitle.text = hasContains ? @"快去查看你的测评报告吧～" : @"快去测测你的运动能力吧～";
    if (hasContains){
        self.testDescrip.textAlignment = NSTextAlignmentCenter;
        self.testDescrip.text = @"去查看";
        self.testDescrip.layer.borderColor = [UIColor colorWithHexString:@"#16D2E3"].CGColor;
    }else{
        self.testDescrip.textAlignment = NSTextAlignmentRight;
        self.testDescrip.text = @"测一测";
        self.testDescrip.layer.borderColor = [UIColor clearColor].CGColor;
    }
}



- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];
    // Configure the view for the selected state
}

@end
