//
//  MRKHeartRateAlert.m
//  Student_IOS
//
//  Created by merit on 2022/6/29.
//

#import "MRKHeartRateAlert.h"
#import "UIView+AZGradient.h"
#import "UIView+Effects.h"
#import "YYTextKeyboardManager.h"

@interface MRKHeartRateAlert ()<UITextFieldDelegate,YYTextKeyboardObserver>
@property (nonatomic, strong) UILabel *titleLab;
@property (nonatomic, strong) UIView *textView;
@property (nonatomic, strong) UITextField *textField;
@property (nonatomic, strong) UILabel *unitLab;
@property (nonatomic, strong) UIButton *ensureBtn;
@property (nonatomic, strong) UIButton *cancelBtn;
@end

@implementation MRKHeartRateAlert


- (void)dealloc {
    [[YYTextKeyboardManager defaultManager] removeObserver:self];
}

#pragma mark  - YYTextKeyboardObserver -
- (void)keyboardChangedWithTransition:(YYTextKeyboardTransition)transition {
    if (transition.toVisible) {
        
        NSLog(@"transition.toFrame=====%@",NSStringFromCGRect(transition.toFrame));
        CGRect toFrame = [[YYTextKeyboardManager defaultManager] convertRect:transition.toFrame toView:self];
        NSLog(@"toFrame=====%@",NSStringFromCGRect(toFrame));
        if (transition.animationDuration == 0) {
            self.containerView.bottom = CGRectGetMinY(toFrame) - 30;
        } else {
            [UIView animateWithDuration:transition.animationDuration
                                  delay:0
                                options:transition.animationOption | UIViewAnimationOptionBeginFromCurrentState
                             animations:^{
                self.containerView.bottom = CGRectGetMinY(toFrame) - 30;
            } completion:NULL];
        }
    } else {
        NSLog(@"=====%@",@"sssssss");
//        self.containerView.bottom = kScreenHeight/2 + WKDHPX(182)/2;
    }
}

- (void)touchesEnded:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event{
    [self.textField resignFirstResponder];
}

 
- (void)layoutContainerView{
    self.isAutoHidden = NO;
    
    self.containerView.frame = CGRectMake((MainWidth-WKDHPX(295))/2, (MainHeight-WKDHPX(182))/2, WKDHPX(295), WKDHPX(182));
//    [self.containerView mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.centerY.mas_equalTo(self.containerView.superview.mas_centerY);
//        make.centerX.mas_equalTo(self.containerView.superview.mas_centerX);
//        make.size.mas_equalTo(CGSizeMake(WKDHPX(295), WKDHPX(182)));
//    }];
}

- (void)setupContainerViewAttributes{
    //设置containerview的属性,比如切边啥的
    self.containerView.backgroundColor = [UIColor whiteColor];
    self.containerView.layer.cornerRadius = WKDHPX(8);
    self.containerView.layer.masksToBounds = YES;
    
    [[YYTextKeyboardManager defaultManager] addObserver:self];
}

- (void)setupContainerSubViews{
    [self.containerView addSubview:self.titleLab];
    [self.titleLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.containerView.mas_top).offset(WKDHPX(20));
        make.centerX.mas_equalTo(self.containerView);
        make.left.right.mas_equalTo(0);
        make.height.mas_equalTo(WKDHPX(26));
    }];
    
    [self.containerView addSubview:self.textView];
    [self.textView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.titleLab.mas_bottom).offset(WKDHPX(16));
        make.left.mas_equalTo(self.containerView.mas_left).offset(WKDHPX(20));
        make.right.mas_equalTo(self.containerView.mas_right).offset(-WKDHPX(20));
        make.height.mas_equalTo(WKDHPX(44));
    }];
    
    [self.textView addSubview:self.unitLab];
    [self.unitLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_equalTo(self.textView);
        make.right.mas_equalTo(self.textView.mas_right).offset(-WKDHPX(16));
    }];
    
    [self.textView addSubview:self.textField];
    [self.textField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_equalTo(self.textView);
        make.left.mas_equalTo(self.textView.mas_left).offset(WKDHPX(16));
        make.right.mas_equalTo(-WKDHPX(48));
        make.height.mas_equalTo(WKDHPX(48));
    }];

    UIView *lineView = [[UIView alloc] init];
    lineView.backgroundColor = [UIColor colorWithHexString:@"#F6F6F8"];
    [self.containerView addSubview:lineView];
    [lineView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.textView.mas_bottom).offset(WKDHPX(22));
        make.left.right.mas_equalTo(0);;
        make.height.mas_equalTo(1);
    }];
    
    UIView *lineView2 = [[UIView alloc] init];
    lineView2.backgroundColor = [UIColor colorWithHexString:@"#F6F6F8"];
    [self.containerView addSubview:lineView2];
    [lineView2 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(lineView.mas_bottom);
        make.bottom.mas_equalTo(0);
        make.width.mas_equalTo(1);
        make.centerX.mas_equalTo(0);
    }];
    
    [self.containerView addSubview:self.ensureBtn];
    [self.ensureBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(lineView.mas_bottom);
        make.left.mas_equalTo(lineView2.mas_right);
        make.right.bottom.mas_equalTo(0);
    }];
    
    [self.containerView addSubview:self.cancelBtn];
    [self.cancelBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(lineView.mas_bottom);
        make.right.mas_equalTo(lineView2.mas_left);
        make.left.bottom.mas_equalTo(0);
    }];
    

    @weakify(self);
    [[RACSignal merge:@[self.textField.rac_textSignal, RACObserve(self.textField, text)]] subscribeNext:^(NSString *text){
        @strongify(self);
        switch (self.type) {
            case AdjustTargetKcalType: {
                if (text.intValue >= 10000){
                    self.textField.text = @"9999";
                }
            }  break;
            case AdjustTargetTimeType: {
                if (text.intValue >= 1000){
                    self.textField.text = @"999";
                }
            }  break;
            default:
                break;
        }
    }];
}

- (void)layoutContainerViewSubViews{
    [self layoutIfNeeded];
}

- (void)becomeResponder{
//    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self.textField becomeFirstResponder];
//    });
}

#pragma mark - UITextFieldDelegate -

- (BOOL)textFieldShouldBeginEditing:(UITextField *)textField{
    return YES;
}

- (void)textFieldDidBeginEditing:(UITextField *)textField{
    
}

- (BOOL)textFieldShouldEndEditing:(UITextField *)textField{
    return YES;
}

- (void)textFieldDidEndEditing:(UITextField *)textField{
    
}

- (BOOL)textField:(UITextField *)textField shouldChangeCharactersInRange:(NSRange)range replacementString:(NSString *)string {
    NSMutableString *changedString = [[NSMutableString alloc] initWithString:textField.text];
    [changedString replaceCharactersInRange:range withString:string];
    return [self isValidAboutInputText:textField shouldChangeCharactersInRange:range replacementString:string decimalNumber:2];
}

- (BOOL)isValidAboutInputText:(UITextField *)textField shouldChangeCharactersInRange:(NSRange)range replacementString:(NSString *)string decimalNumber:(NSInteger)number {
    NSScanner *scanner = [NSScanner scannerWithString:string];
    NSCharacterSet *numbers;
    NSRange pointRange = [textField.text rangeOfString:@"."];
    if ((pointRange.length > 0) && (pointRange.location < range.location || pointRange.location > range.location + range.length)) {
        numbers = [NSCharacterSet characterSetWithCharactersInString:@"0123456789"];
    } else {
        numbers = [NSCharacterSet characterSetWithCharactersInString:@"0123456789."];
    }
    
    if ([textField.text isEqualToString:@""] && [string isEqualToString:@"."]) {
        return NO;
    }
    
    short remain = 1; // 保留 number位小数
    NSString *tempStr = [textField.text stringByAppendingString:string];
    NSUInteger strlen = [tempStr length];
    if(pointRange.length > 0 && pointRange.location > 0) { // 判断输入框内是否含有“.”。
        if([string isEqualToString:@"."]) { // 当输入框内已经含有“.”时，如果再输入“.”则被视为无效。
            return NO;
        }
        if(strlen > 0 && (strlen - pointRange.location) > remain+1) { // 当输入框内已经含有“.”，当字符串长度减去小数点前面的字符串长度大于需要要保留的小数点位数，则视当次输入无效。
            return NO;
        }
    }
    
    NSRange zeroRange = [textField.text rangeOfString:@"0"];
    if(zeroRange.length == 1 && zeroRange.location == 0) { // 判断输入框第一个字符是否为“0”
        if(![string isEqualToString:@"0"] && ![string isEqualToString:@"."] && [textField.text length] == 1) { // 当输入框只有一个字符并且字符为“0”时，再输入不为“0”或者“.”的字符时，则将此输入替换输入框的这唯一字符。
            textField.text = string;
            return NO;
        } else {
            if(pointRange.length == 0 && pointRange.location > 0) { // 当输入框第一个字符为“0”时，并且没有“.”字符时，如果当此输入的字符为“0”，则视当此输入无效。
                if([string isEqualToString:@"0"]) {
                    return NO;
                }
            }
        }
    }
    
    NSString *buffer;
    if (![scanner scanCharactersFromSet:numbers intoString:&buffer] && ([string length] != 0)) {
        return NO;
    } else {
        return YES;
    }
}




#pragma mark - UserInfoAlertType -

- (void)setType:(HeartRateAlertType)type{
    _type = type;
    
    switch (type) {
        case RestingHeartRateType:{
            
            self.titleLab.text = @"请输入静息心率";
            
            NSMutableAttributedString *placeholderStr = [[NSMutableAttributedString alloc] initWithString:@"静息心率 (40~100)"
                                                                                               attributes:@{NSForegroundColorAttributeName:DescribeTextColor,
                                                                                                            NSFontAttributeName:[UIFont systemFontOfSize:14]}];
            self.textField.attributedPlaceholder = placeholderStr;
            self.unitLab.text = @"bpm";
        }break;
            
        case MaxHeartRateType:{
            
            self.titleLab.text = @"请输入最大心率";
            
            NSMutableAttributedString *placeholderStr = [[NSMutableAttributedString alloc] initWithString:@"最大心率 (150~210)"
                                                                                               attributes:@{NSForegroundColorAttributeName:DescribeTextColor,
                                                                                                            NSFontAttributeName:[UIFont systemFontOfSize:14]}];
            self.textField.attributedPlaceholder = placeholderStr;
            self.unitLab.text = @"bpm";
        }break;
            
        case ReserveHeartRateType:{
            
            self.titleLab.text = @"请输入储备心率";
            
            NSMutableAttributedString *placeholderStr = [[NSMutableAttributedString alloc] initWithString:@"储备心率 (20~110)"
                                                                                               attributes:@{NSForegroundColorAttributeName:DescribeTextColor,
                                                                                                            NSFontAttributeName:[UIFont systemFontOfSize:14]}];
            self.textField.attributedPlaceholder = placeholderStr;
            self.unitLab.text = @"bpm";
        }break;
            
        case WarnHeartRateType:{
            
            self.titleLab.text = @"请输入心率预警值";
            
            NSMutableAttributedString *placeholderStr = [[NSMutableAttributedString alloc] initWithString:@"预警心率 (100~210)"
                                                                                               attributes:@{NSForegroundColorAttributeName:DescribeTextColor,
                                                                                                            NSFontAttributeName:[UIFont systemFontOfSize:14]}];
            self.textField.attributedPlaceholder = placeholderStr;
            self.unitLab.text = @"bpm";
        }break;
            
        case RestingBodyFatRateType:{
            
            self.titleLab.text = @"请输入体脂率";
            
            NSMutableAttributedString *placeholderStr = [[NSMutableAttributedString alloc] initWithString:@"体脂率 (0~100)"
                                                                                               attributes:@{NSForegroundColorAttributeName:DescribeTextColor,
                                                                                                            NSFontAttributeName:[UIFont systemFontOfSize:14]}];
            self.textField.attributedPlaceholder = placeholderStr;
            self.unitLab.text = @"%";
        }break;
            
        case AdjustTargetKcalType:{
            
            self.titleLab.text = @"调整运动消耗";
            
            NSMutableAttributedString *placeholderStr = [[NSMutableAttributedString alloc] initWithString:@"请输入目标消耗"
                                                                                               attributes:@{NSForegroundColorAttributeName:DescribeTextColor,
                                                                                                            NSFontAttributeName:[UIFont systemFontOfSize:14]}];
            self.textField.attributedPlaceholder = placeholderStr;
            self.unitLab.text = @"千卡";
        }break;
            
        case AdjustTargetTimeType:{
            
            self.titleLab.text = @"调整运动时长";
            
            NSMutableAttributedString *placeholderStr = [[NSMutableAttributedString alloc] initWithString:@"请输入目标时长"
                                                                                               attributes:@{NSForegroundColorAttributeName:DescribeTextColor,
                                                                                                            NSFontAttributeName:[UIFont systemFontOfSize:14]}];
            self.textField.attributedPlaceholder = placeholderStr;
            self.unitLab.text = @"分钟";
        }break;

            
        default:
            break;
    }
}

- (void)openChallengeBtnClick:(UIButton *)sender{
    NSInteger tag = sender.tag;
    if (tag == 3000) {
        if (self.closeBlock){
            self.closeBlock();
        }
        
        [self dismissAnimated:YES];
        if(self.closeBlock){
            self.closeBlock();
        }
        
    } else {
        NSString *text = self.textField.text;
        if ([text isEmpty]) {
            [MBProgressHUD showMessage:@"请先输入数据~" toView:nil];
            return;
        }
        
        switch (self.type) {
            case RestingHeartRateType:{
                if (text.intValue < 40 || text.intValue > 100) {
                    [MBProgressHUD showMessage:@"请输入正确的静息心率" toView:nil];
                    return;
                }
            }break;
                
            case MaxHeartRateType:{
                if (text.intValue < 150 || text.intValue > 210) {
                    [MBProgressHUD showMessage:@"请输入正确的最大心率" toView:nil];
                    return;
                }
            }break;
                
            case ReserveHeartRateType:{
                if (text.intValue < 20 || text.intValue > 110) {
                    [MBProgressHUD showMessage:@"请输入正确的储备心率" toView:nil];
                    return;
                }
            }break;

            case WarnHeartRateType:{
                if (text.intValue < 100 || text.intValue > 210) {
                    [MBProgressHUD showMessage:@"请输入正确的心率预警值" toView:nil];
                    return;
                }
            }break;
                
            case RestingBodyFatRateType:{
                if (text.intValue < 0 || text.intValue > 100) {
                    [MBProgressHUD showMessage:@"请输入正确的体脂率" toView:nil];
                    return;
                }
            }break;
                
            case AdjustTargetKcalType:{
                if (text.intValue <= 0 || text.intValue > 10000) {
                    [MBProgressHUD showMessage:@"请输入合适的运动消耗" toView:nil];
                    return;
                }
                
                if (![text isPureInt]) {
                    [MBProgressHUD showMessage:@"请输入整数" toView:nil];
                    return;
                }
            }break;
            
            case AdjustTargetTimeType:{
                if (text.intValue <= 0 || text.intValue > 1000) {
                    [MBProgressHUD showMessage:@"请输入合适的运动时长" toView:nil];
                    return;
                }
                
                if (![text isPureInt]) {
                    [MBProgressHUD showMessage:@"请输入整数" toView:nil];
                    return;
                }
            }break;
                
            default:
                break;
        }
        
        if (self.userInfoBlock) {
            self.userInfoBlock(text, self);
        }
        
        [self dismissAnimated:YES];
    }
}



#pragma mark - Lazy -

- (UILabel *)titleLab {
    if (!_titleLab) {
        UILabel *label = [[UILabel alloc] init];
        label.textAlignment = NSTextAlignmentCenter;
        label.textColor = MainTextColor;
        label.font = kMedium_Font_NoDHPX(WKDHPX(18));
        _titleLab = label;
    }
    return _titleLab;
}

- (UIView *)textView{
    if (!_textView) {
        _textView = [[UIView alloc] init];
        _textView.backgroundColor = [UIColor colorWithHexString:@"#F3F5F9"];
        _textView.layer.cornerRadius = WKDHPX(48)/2;
        _textView.layer.masksToBounds = YES;
    }
    return _textView;
}

- (UITextField *)textField{
    if (!_textField) {
        UITextField *textField = [[UITextField alloc] init];
        textField.delegate = self;
        textField.backgroundColor = [UIColor colorWithHexString:@"#F3F5F9"];
        textField.font = kSystem_Font_NoDHPX(WKDHPX(15));
        textField.keyboardType = UIKeyboardTypeNumberPad;
        textField.clearButtonMode = UITextFieldViewModeAlways;
        textField.textColor = [UIColor blackColor];
        _textField = textField;
    }
    return _textField;
}

- (UILabel *)unitLab {
    if (!_unitLab) {
        UILabel *label = [[UILabel alloc] init];
        label.textColor = MainTextColor;
        label.font = kMedium_Font_NoDHPX(WKDHPX(15));
        label.text = @"bpm";
        label.textAlignment = NSTextAlignmentCenter;
        _unitLab = label;
    }
    return _unitLab;
}

- (UIButton *)ensureBtn{
    if (!_ensureBtn) {
        _ensureBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_ensureBtn setTitleColor:[UIColor colorWithHexString:@"#16D2E3"] forState:UIControlStateNormal];
        [_ensureBtn setTitle:@"确认" forState:UIControlStateNormal];
        [_ensureBtn addTarget:self action:@selector(openChallengeBtnClick:) forControlEvents:UIControlEventTouchUpInside];
        _ensureBtn.titleLabel.font = kMedium_Font_NoDHPX(WKDHPX(14));
        _ensureBtn.showsTouchWhenHighlighted = YES;
        _ensureBtn.tag = 2000;
    }
    return _ensureBtn;
}

- (UIButton *)cancelBtn{
    if (!_cancelBtn) {
        _cancelBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_cancelBtn setTitleColor:[UIColor colorWithHexString:titleNormalColor1] forState:UIControlStateNormal];
        [_cancelBtn setTitle:@"取消" forState:UIControlStateNormal];
        [_cancelBtn addTarget:self action:@selector(openChallengeBtnClick:) forControlEvents:UIControlEventTouchUpInside];
        _cancelBtn.titleLabel.font = kMedium_Font_NoDHPX(WKDHPX(14));
        _cancelBtn.showsTouchWhenHighlighted = YES;
        _cancelBtn.tag = 3000;
    }
    return _cancelBtn;
}

@end
