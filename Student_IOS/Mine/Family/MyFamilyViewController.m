//
//  MyFamilyViewController.m
//  Student_IOS
//
//  Created by MacPro on 2021/3/30.
//

#import "MyFamilyViewController.h"
#import "FamilyTableViewCell.h"
#import "UIView+LXShadowPath.h"
#import "UIView+AZGradient.h"
#import <ShareSDK/ShareSDK.h>
#import "MJDIYHeader.h"
#import "BottomPartView.h"
#import "User.h"
#import "MRKFamilyModel.h"

@interface MyFamilyViewController ()<UITableViewDelegate,UITableViewDataSource>
@property(nonatomic, strong) UITableView *tableView;
@property(nonatomic, strong) User *user;
@property(nonatomic, strong) BottomPartView *bottomView;
@property(nonatomic, strong) MRKFamilyModel *familyModel;
@property(nonatomic, strong) FamilyBottomView *bottomFooterView;
@end

@implementation MyFamilyViewController

- (BottomPartView *)bottomView{
    if (!_bottomView) {
        _bottomView = [[BottomPartView alloc] init];
        _bottomView.hidden = YES;
        @weakify(self);
        _bottomView.clickdHandler = ^(NSString * _Nonnull title) {
            [self_weak_ buttonClickShow];
        };
    }
    return _bottomView;
}

- (FamilyBottomView *)bottomFooterView{
    if (!_bottomFooterView) {
        _bottomFooterView = [[FamilyBottomView alloc] init];
        _bottomFooterView.frame = CGRectMake(0, 0, kScreenWidth, 80);
        @weakify(self);
        _bottomFooterView.inviteActionBlock = ^{
            [self_weak_ invitePerson];
        };
    }
    return _bottomFooterView;
}

- (UITableView *)tableView{
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
//        _tableView.scrollEnabled = NO;
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.backgroundColor = [UIColor clearColor];
        _tableView.estimatedRowHeight = 0;
        _tableView.estimatedSectionHeaderHeight = 0;
        _tableView.estimatedSectionFooterHeight = 0;
    }
    return _tableView;
}

- (void)viewDidLoad {
    self.tracePageId = @"page_profile_roommate";
    [super viewDidLoad];
    self.view.backgroundColor = [UIColor colorWithHexString:@"#FAFAFC"];
    self.navTitle = @"家庭成员";
    
    self.user = [Login curLoginUser];
    // Do any additional setup after loading the view.
    
    self.tableView.delegate = self;
    self.tableView.dataSource = self;
    [self.mrkContentView addSubview:self.tableView];
    [self.mrkContentView addSubview:self.bottomView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.mrkContentView.mas_top).offset(kNavBarHeight);
        make.left.mas_equalTo(self.mrkContentView.mas_left);
        make.right.mas_equalTo(self.mrkContentView.mas_right);
        make.bottom.mas_equalTo(self.mrkContentView.mas_bottom);
    }];
    [self.bottomView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_equalTo(self.mrkContentView.mas_bottom);
        make.centerX.mas_equalTo(self.mrkContentView.mas_centerX);
        make.size.mas_equalTo(CGSizeMake(kScreenWidth, DHPX(50) +kSafeArea_Bottom));
    }];
    
    @weakify(self);
    MrkEmptyView *emptyView = [[MrkEmptyView alloc] init];
    emptyView.fullCoverSuperView = YES;
    emptyView.tapEmptyViewBlock = ^{
        [self_weak_ getDataFromNet];
    };
    emptyView.errorBtnClickBlock = ^{
        [self_weak_ getDataFromNet];
    };
    self.tableView.pageEmptyView = emptyView;
    
    
    self.tableView.alpha = 0;
    [self getDataFromNet];
}

- (void)viewDidLayoutSubviews {
    [super viewDidLayoutSubviews];
}


- (void)getDataFromNet {
    [self.view beginLoading];
    [MRKBaseRequest mrkGetRequestUrl:@"/user/user-family/v1"
                             andParm:@{@"type":@(1)}.mutableCopy
            completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        [self.view endLoading];
        [self.view hiddenEmptyView];
        
        self.familyModel = [MRKFamilyModel modelWithJSON:[request.responseObject objectForKey:@"data"]];
        if ([self.user.basicInfo.accountId isEqualToString:self.familyModel.admin.accountId?:@""]){
            self.familyModel.isAdmin = YES;
        }
        
        [self reloadBottomView];
        self.tableView.tableHeaderView = self.viewForHeader;
        [self.tableView reloadData];
        dispatch_async(dispatch_get_main_queue(), ^{
            [UIView animateWithDuration:0.3 animations:^{
                self.tableView.alpha = 1.0f;
            }];
        });
        
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        [self.view endLoading];
        if ([self.tableView tableViewIsEmptyData]){
            [self.tableView mrkShowNetworkErrorEmptyView];
        }
    }];
}

- (void)reloadBottomView {
    if (!self.familyModel.isAdmin && !self.user.memberInfo.isMember) {
        self.bottomView.titleColor = UIColorHex(#672F15);
        self.bottomView.type = BottomPartViewGradient;
        self.bottomView.hidden = NO;
        self.bottomView.titleStr = [self.user.memberInfo.expireDate isNotBlank] ? @"立即续费" : @"开通VIP会员";
    }else {
        self.bottomView.hidden = YES;
    }
}




- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return 92;
}
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.familyModel.items.count + 1;
}
- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return 0.01;
}
- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    if (self.familyModel.isAdmin && self.familyModel.items.count != 2) {
        return 80;
    }
    return self.viewForFooter.height;
}
- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    return self.familyModel.isAdmin ? self.bottomFooterView : self.viewForFooter;
}
- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    static NSString * storeResource = @"familystoreResource";
    FamilyTableViewCell * cell = [tableView dequeueReusableCellWithIdentifier:storeResource];
    if (!cell) {
        cell = [[FamilyTableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:storeResource];
        cell.selectionStyle = UITableViewCellSelectionStyleNone;
        cell.backgroundColor = [UIColor colorWithHexString:@"#FAFAFC"];
    }
    
    if (self.familyModel.isAdmin) {
        if (indexPath.row == 0) {
            int page = self.user.memberInfo.isMember ? 1 :2;
            MRKFamilyPeopleModel *model = self.familyModel.admin;
            [cell setData:model withLocation:page];
        } else {
            MRKFamilyPeopleModel *model = self.familyModel.items[(indexPath.row-1)];
            [cell setData:model withLocation:0];
        }
    } else {
        if (indexPath.row == self.familyModel.items.count) {
            MRKFamilyPeopleModel *model = self.familyModel.admin;
            [cell setData:model withLocation:1];
        } else {
            MRKFamilyPeopleModel *model = self.familyModel.items[indexPath.row];
            [cell setData:model withLocation:0];
        }
    }
    return cell;
}

- (BOOL)tableView:(UITableView *)tableView canEditRowAtIndexPath:(NSIndexPath *)indexPath {
    if (self.familyModel.isAdmin) {
        return indexPath.row != 0;
    }
    return false;
}
// 添加一个'删除'按钮,默认红色背景
- (NSArray *)tableView:(UITableView *)tableView editActionsForRowAtIndexPath:(NSIndexPath *)indexPath{
    return [self getRowActions:tableView indexPath:indexPath];
}
- (id)tableView:(UITableView *)tableView trailingSwipeActionsConfigurationForRowAtIndexPath:(NSIndexPath *)indexPath {
    return [self getRowActions:tableView indexPath:indexPath];
}

- (id)getRowActions:(UITableView *)tableView indexPath:(NSIndexPath *)indexPath {
    //iOS11之后右滑是图片之前是按钮
    if (@available(iOS 11, *)) {
        UIContextualAction *delete = [UIContextualAction contextualActionWithStyle:UIContextualActionStyleDestructive
                                                                             title:@""
                                                                           handler:^(UIContextualAction * _Nonnull action, __kindof UIView * _Nonnull sourceView, void (^ _Nonnull completionHandler)(BOOL)) {
            completionHandler(YES);
            
            MRKFamilyPeopleModel *model = self.familyModel.items[(indexPath.row - 1)];
            [self deleteObject:model.accountId];
        }];
        delete.backgroundColor = [UIColor colorWithHexString:@"#FAFAFC"];
        delete.image = [UIImage imageNamed:@"icon_delete-1"];
        
        UISwipeActionsConfiguration *swipeActionConfig = [UISwipeActionsConfiguration configurationWithActions:@[delete]];
        swipeActionConfig.performsFirstActionWithFullSwipe = NO;
        return swipeActionConfig;
        
    } else {
        UITableViewRowAction *deleteRowAction = [UITableViewRowAction rowActionWithStyle:UITableViewRowActionStyleDestructive
                                                                                   title:@"删除"
                                                                                 handler:^(UITableViewRowAction *action, NSIndexPath *indexPath) {
            MRKFamilyPeopleModel *model = self.familyModel.items[(indexPath.row - 1)];
            [self deleteObject:model.accountId];
        }];
        deleteRowAction.backgroundColor = [UIColor colorWithHexString:@"#FF5363"];;
        return @[deleteRowAction];
    }
}

- (void)deleteObject:(NSString *)userId {
    [self jxt_showAlertWithTitle:@"确定删除此成员？"
                         message:@"移除后，该成员账户会员时长会被清零，请谨慎移除！"
               appearanceProcess:^(JXTAlertController * _Nonnull alertMaker) {
        alertMaker.
        addActionCancelTitle(@"取消").
        addActionDefaultTitle(@"删除");
    } actionsBlock:^(NSInteger buttonIndex, UIAlertAction * _Nonnull action, JXTAlertController * _Nonnull alertSelf) {
        if (buttonIndex == 1) {
            [self deleteUser:userId];
        }
    }];
}

- (void)deleteUser:(NSString *)userId{
    [MBProgressHUD showLodingWithMessage:@"" view:self.view];
    [MRKBaseRequest mrkRequestType:YTKRequestMethodDELETE
                               url:@"/user/user-family/v1"
                           andParm:@{@"accountId":userId}.mutableCopy
          completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
        [MBProgressHUD hideHUDForView:self.view];
        
        [self getDataFromNet];
    } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
        [MBProgressHUD hideHUDForView:self.view];
    }];
}


- (UITableViewCellEditingStyle)tableView:(UITableView *)tableView editingStyleForRowAtIndexPath:(NSIndexPath *)indexPath {
    return UITableViewCellEditingStyleDelete;
}

- (void)tableView:(UITableView *)tableView commitEditingStyle:(UITableViewCellEditingStyle)editingStyle forRowAtIndexPath:(NSIndexPath *)indexPath {
    if (editingStyle ==UITableViewCellEditingStyleDelete) {
        
    }
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    
}





- (YYLabel *)viewForHeader {
    NSMutableAttributedString *attributedText = [[NSMutableAttributedString alloc] init];
    
    MemberInfoDataDTO *model = [Login curLoginUser].memberInfo;
    [attributedText appendAttributedString:[self setInfoModel:model]];
    
    if (self.familyModel.isAdmin)
    {
        [attributedText appendAttributedString:[[NSMutableAttributedString alloc] initWithString:@"\n\n"]];
        [attributedText appendAttributedString:({
            NSString *str = @"*所有家庭成员的会员有效期累加并共享，若将成员移出家庭，则主账户会员时长不变，被移出成员账户会员时长清零，请谨慎移除!";
            NSMutableAttributedString *attStr = [[NSMutableAttributedString alloc] initWithString:str];
            attStr.font = [UIFont systemFontOfSize:WKDHPX(12)];
            attStr.color = UIColorHex(#363A44);
            attStr.lineSpacing = 5;
            attStr;
        })];
        [attributedText appendAttributedString:[[NSMutableAttributedString alloc] initWithString:@"\n\n"]];
        [attributedText appendAttributedString:self.joinTipStr];
    }
    
    YYTextContainer *container = [YYTextContainer new];
    container.size = CGSizeMake(kScreenWidth, HUGE);
    container.insets = UIEdgeInsetsMake(16, 16, 16, 16);
    container.maximumNumberOfRows = 0;
    
    YYTextLayout *layout = [YYTextLayout layoutWithContainer:container text:attributedText];
    float height = layout.textBoundingSize.height;
    
    YYLabel *label = [YYLabel new];
    label.left = 0;
    label.width = kScreenWidth;
    label.numberOfLines = 0;
    label.textVerticalAlignment = YYTextVerticalAlignmentTop;
    label.displaysAsynchronously = YES;
    label.ignoreCommonProperties = YES;
    label.fadeOnAsynchronouslyDisplay = NO;
    label.fadeOnHighlight = NO;
    label.textAlignment = 0;
    
    label.textLayout = layout;
    label.height = height;
    return label;
}



- (NSMutableAttributedString *)setInfoModel:(MemberInfoDataDTO *)model {
    if (model.isMember) {
        MemberInfoDataDTO *enjoyModel = [self getInfoModelWithType:@"30" withModel:model];
        if (!enjoyModel.isExpire) {
            return [self getAttributedStrWith:enjoyModel];
        }
        
        ///累加换行
        NSMutableAttributedString *totalAttributedStr = [[NSMutableAttributedString alloc] init];
        MemberInfoDataDTO *svipModel = [self getInfoModelWithType:@"20" withModel:model];
        if (!svipModel.isExpire) {
            [totalAttributedStr appendAttributedString:[self getAttributedStrWith:svipModel]];
        }
        
        MemberInfoDataDTO *vipModel = [self getInfoModelWithType:@"10" withModel:model];
        if (!vipModel.isExpire) {
            if(totalAttributedStr.length > 0) {
                [totalAttributedStr appendAttributedString:[[NSAttributedString alloc] initWithString:@"\n"]];
            }
            [totalAttributedStr appendAttributedString:[self getAttributedStrWith:vipModel]];
        }
        return totalAttributedStr;
    }
    
    NSString *tipStr = [model.expireDate isNotBlank] ? @"会员已过期，续费后可邀请家庭成员加入" : @"还未开通VIP会员，开通后可邀请家庭成员加入";
    NSMutableAttributedString *attributedStr = [[NSMutableAttributedString alloc] initWithString:tipStr];
    attributedStr.color = UIColorHex(#363A44);
    attributedStr.font = kSystem_Font_NoDHPX(WKDHPX(15));
    return attributedStr;
}

- (MemberInfoDataDTO *)getInfoModelWithType:(NSString *)type withModel:(MemberInfoDataDTO *)model{
    __block MemberInfoDataDTO *infoModel = nil;
    [model.items enumerateObjectsUsingBlock:^(id  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        MemberInfoDataDTO *mod = (MemberInfoDataDTO *)obj;
        if (mod.vipType == type.intValue) {
            infoModel = mod;
            *stop = YES;
        }
    }];
    return infoModel;
}

- (NSMutableAttributedString *)getAttributedStrWith:(MemberInfoDataDTO *)model{
    NSString *part = @"";
    if (model.vipType == 30) {
        part = @"绝影VIP";
    } else if (model.vipType == 10) {
        part = @"VIP";
    }
    
    NSString *expireDate = model.expireDate?:@"";
    NSString *expireDateStr = [NSString stringWithFormat:@"%@有效期至: %@", part, model.expireDate];
    NSRange expireDateRang = [expireDateStr rangeOfString:expireDate];
    
    NSMutableAttributedString *attributedStr = [[NSMutableAttributedString alloc] initWithString:expireDateStr];
    attributedStr.color = UIColorHex(#363A44);
    attributedStr.font = kSystem_Font_NoDHPX(WKDHPX(15));
    [attributedStr setColor:UIColorHex(#F0C59C) range:expireDateRang];
    
    if (model.days > 0) {
        NSString *days = [NSString stringWithFormat:@"%d", model.days];
        NSString *partStr = [NSString stringWithFormat:@"，还剩%@天", days];
        NSRange timeRang = [partStr rangeOfString:days];
        NSMutableAttributedString *text = [[NSMutableAttributedString alloc] initWithString:partStr];
        text.color = UIColorHex(#363A44);
        text.font = kSystem_Font_NoDHPX(WKDHPX(15));
        [text setColor:UIColorHex(#F0C59C) range:timeRang];
        [attributedStr appendAttributedString:text];
    }
    
    return attributedStr;
}




//- (NSString *)vipTipStr{
//    if (!self.user.memberInfo.isMember) {
//        if ([self.user.memberInfo.expireDate isNotBlank]) {
//            return @"会员已过期，续费后可邀请家庭成员加入\n";
//        }else{
//            return @"还未开通VIP会员，开通后可邀请家庭成员加入\n";
//        }
//    }
//    return [NSString stringWithFormat:@"会员有效期：%@\n", self.user.memberInfo.expireDate];
//}

- (NSMutableAttributedString *)joinTipStr{
    NSMutableAttributedString *attStr = [[NSMutableAttributedString alloc] initWithString:@"温馨提示:\n"];
    attStr.font = [UIFont systemFontOfSize:WKDHPX(12) weight:UIFontWeightMedium];
    attStr.color = UIColorHex(#363A44);
    [attStr appendAttributedString:({
        NSMutableAttributedString *part = [[NSMutableAttributedString alloc] initWithString:@"1、最多可加入2位家庭成员\n"];
        part.font = [UIFont systemFontOfSize:WKDHPX(12)];
        part.color = UIColorHex(#363A44);
        part;
    })];
    [attStr appendAttributedString:({
        NSMutableAttributedString *part1 = [[NSMutableAttributedString alloc] initWithString:@"2、每年仅支持5次机会添加家庭成员，"];
        part1.font = [UIFont systemFontOfSize:WKDHPX(12)];
        part1.color = UIColorHex(#363A44);
        NSString *count = [NSString stringWithFormat:@"%@", self.familyModel.usedCount];
        NSString *partStr = [NSString stringWithFormat:@"已用 %@ 次", count];
        NSRange timeRang = [partStr rangeOfString:count];
        NSMutableAttributedString *part2 = [[NSMutableAttributedString alloc] initWithString:partStr];
        part2.color = UIColorHex(#363A44);
        part2.font = kSystem_Font_NoDHPX(WKDHPX(12));
        [part2 setColor:UIColorHex(#16D2E3) range:timeRang];
        [part1 appendAttributedString:part2];
        part1;
    })];
    attStr.lineSpacing = 5;
    //        attStr.paragraphSpacing = 5;
    return attStr;
}



- (YYLabel *)viewForFooter{
    YYLabel *label = [[YYLabel alloc] init];
    label.left = 0;
    label.width = kScreenWidth;
    label.numberOfLines = 0;
    label.textVerticalAlignment = YYTextVerticalAlignmentTop;
    label.displaysAsynchronously = YES;
    label.ignoreCommonProperties = YES;
    label.fadeOnAsynchronouslyDisplay = NO;
    label.fadeOnHighlight = NO;
    label.textAlignment = 0;
    
    NSString *str = nil;
    if (!self.familyModel.isAdmin) {
        if (self.user.memberInfo.isMember && [self.user.memberInfo.expireDate isNotBlank]) {
            str = @"所有家庭成员的会员有效期累加并共享，若被移出家庭成员，则主账户会员时长不变，被移出成员账户会员时长清零！";
        }
        
        if (!self.user.memberInfo.isMember && [self.user.memberInfo.expireDate isNotBlank]) {
            str = @"续费后所有家庭成员的会员有效期累加并共享，若被移出家庭成员，则主账户会员时长不变，被移出成员账户会员时长清零！";
        }
    }
    
    if ([str isNotBlank]) {
        NSMutableAttributedString *nameText = [[NSMutableAttributedString alloc] initWithString:str];
        nameText.color = UIColorHex(#999999);
        nameText.font = [UIFont systemFontOfSize:13];
        nameText.alignment = NSTextAlignmentLeft;
        
        UIImage *image = [UIImage imageNamed:@"error-warning-line"];
        NSMutableAttributedString *attachText = [NSMutableAttributedString attachmentStringWithContent:image
                                                                                           contentMode:UIViewContentModeCenter
                                                                                        attachmentSize:image.size
                                                                                           alignToFont:[UIFont systemFontOfSize:13]
                                                                                             alignment:YYTextVerticalAlignmentCenter];
        [nameText insertAttributedString:attachText atIndex:0];
        
        YYTextContainer *container = [YYTextContainer new];
        container.size = CGSizeMake(kScreenWidth, HUGE);
        container.insets = UIEdgeInsetsMake(5, 16, 16, 16);
        container.maximumNumberOfRows = 0;
        
        YYTextLayout *layout = [YYTextLayout layoutWithContainer:container text:nameText];
        float height = layout.textBoundingSize.height;
        label.textLayout = layout;
        label.height = height;
    }
    
    return label;
}

- (void)invitePerson {
    
    if (!self.user.memberInfo.isMember && [self.user.memberInfo.expireDate isNotBlank]){
        
        [MrkAlertManager showAlert:@"当前VIP会员已过期"
                           message:@"续费后可以继续邀请家庭成员加入"
                            ensure:@"知道了" handleIndex:^(NSInteger index) {
            
        }];
        
    }else if( !self.user.memberInfo.isMember && ![self.user.memberInfo.expireDate isNotBlank]){
        
        [MrkAlertManager showAlert:@"您还没有开通VIP"
                           message:@"开通VIP后可以邀请家庭成员加入"
                            ensure:@"知道了" handleIndex:^(NSInteger index) {
            
        }];
        
    }else{
        
        [MRKBaseRequest mrkGetRequestUrl:@"/user/user-family/v1/invite-share"
                                 andParm:nil
                completeBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
            NSLog(@"===== %@",request.responseObject);
            NSString *shareUrl = [request.responseObject objectForKey:@"data"];
            [self share:SSDKPlatformSubTypeWechatSession withString:shareUrl];
        } failure:^(__kindof YTKBaseRequest * _Nonnull request) {
            
        }];
    }
}














- (void)buttonClickShow {
    [[RouteManager sharedInstance] skipVIP];///直接跳转到会员页
}

- (void)refresh {
    [self getDataFromNet];
}

- (void)share:(int)SSDKPlatformType withString:(NSString *)urlPath {
    
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    NSString *title = [NSString stringWithFormat:@"%@邀请你成为家庭成员，共享VIP会员", self.user.basicInfo.nickname];
    [params SSDKSetupShareParamsByText:@"欢迎成为我的家庭成员，和我一起享受就瘦！"
                                images:[UIImage imageNamed:@"merit_logo_icon"]
                                   url:[NSURL URLWithString:urlPath]
                                 title:title
                                  type:SSDKContentTypeWebPage];
    [ShareSDK  share:SSDKPlatformSubTypeWechatSession
          parameters:params
      onStateChanged:^(SSDKResponseState state, NSDictionary *userData,
                       SSDKContentEntity *contentEntity, NSError *error) {
        switch (state) {
            case SSDKResponseStateSuccess:
                NSLog(@"成功");
                break;
            case SSDKResponseStateFail:
                NSLog(@"--%@",error.description);
                break;
            case SSDKResponseStateCancel:
                break;
            default:
                break;
        }
    }];
}

#pragma mark ---------Delegate -----------

- (BOOL)viewControllerIsNeedNavBar:(MRKBaseController *)viewController {
    return YES;
}

/*
 #pragma mark - Navigation
 
 // In a storyboard-based application, you will often want to do a little preparation before navigation
 - (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
 // Get the new view controller using [segue destinationViewController].
 // Pass the selected object to the new view controller.
 }
 */

@end




@interface FamilyBottomView()
@property (nonatomic, strong) UIButton *backContentView;
@property (nonatomic, strong) UIImageView *iconImageView;
@property (nonatomic, strong) UILabel *titleLab;
@property (nonatomic, strong) UILabel *descripLab;
@end

@implementation FamilyBottomView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        
        [self addSubview:self.backContentView];
        [self addSubview:self.iconImageView];
        [self addSubview:self.titleLab];
        [self addSubview:self.descripLab];
    }
    return self;
}

- (void)layoutSubviews {
    [super layoutSubviews];
    
    [self.backContentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self);
        make.height.mas_equalTo(80);
        make.width.mas_equalTo(MainWidth-28);
    }];
    
    [self.iconImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.mas_centerY);
        make.right.equalTo(self.mas_centerX).offset(-50);
        make.size.mas_equalTo(CGSizeMake(42, 42));
    }];
    
    [self.titleLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.mas_centerX).offset(-42);;
        make.bottom.equalTo(self.mas_centerY);
        make.height.mas_equalTo(20);
    }];
    
    [self.descripLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.mas_centerX).offset(-42);;
        make.top.equalTo(self.mas_centerY);
        make.height.mas_equalTo(20);
    }];
}

- (void)bottViewCkikc{
    if(self.inviteActionBlock){
        self.inviteActionBlock();
    }
}

- (UIButton *)backContentView {
    if(!_backContentView) {
        _backContentView = [UIButton buttonWithType:UIButtonTypeCustom];
        _backContentView.backgroundColor = [UIColor whiteColor];
        [_backContentView addTarget:self action:@selector(bottViewCkikc) forControlEvents:UIControlEventTouchUpInside];
        _backContentView.layer.cornerRadius = 4;
        _backContentView.layer.masksToBounds = YES;
    }
    return _backContentView;
}

- (UIImageView *)iconImageView {
    if (!_iconImageView) {
        _iconImageView = [[UIImageView alloc] init];
        _iconImageView.image = [UIImage imageNamed:@"icon_add_48x48"];
    }
    return  _iconImageView;
}

- (UILabel *)titleLab{
    if(!_titleLab) {
        _titleLab = [[UILabel alloc] init];
        _titleLab.text = @"邀请家庭成员加入";
        _titleLab.textColor = UIColorHex(#4C5362);
        _titleLab.font = [UIFont systemFontOfSize:14];
    }
    return _titleLab;
}

- (UILabel *)descripLab{
    if(!_descripLab) {
        _descripLab = [[UILabel alloc] init];
        _descripLab.text = @"直接发送链接至微信好友";
        _descripLab.textColor = UIColorHex(#6C7B8A);
        _descripLab.font = [UIFont systemFontOfSize:12];
    }
    return _descripLab;
}

@end
