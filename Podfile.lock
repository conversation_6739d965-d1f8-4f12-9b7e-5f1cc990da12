PODS:
  - AFNetworking (4.0.1):
    - AFNetworking/NSURLSession (= 4.0.1)
    - AFNetworking/Reachability (= 4.0.1)
    - AFNetworking/Security (= 4.0.1)
    - AFNetworking/Serialization (= 4.0.1)
    - AFNetworking/UIKit (= 4.0.1)
  - AFNetworking/NSURLSession (4.0.1):
    - AFNetworking/Reachability
    - AFNetworking/Security
    - AFNetworking/Serialization
  - AFNetworking/Reachability (4.0.1)
  - AFNetworking/Security (4.0.1)
  - AFNetworking/Serialization (4.0.1)
  - AFNetworking/UIKit (4.0.1):
    - AFNetworking/NSURLSession
  - AliPlayerSDK_iOS (6.10.0):
    - AliPlayerSDK_iOS/AliPlayerSDK (= 6.10.0)
  - AliPlayerSDK_iOS/AliPlayerSDK (6.10.0)
  - AliPlayerSDK_iOS_ARTC (6.10.0):
    - AliPlayerSDK_iOS_ARTC/AliPlayerSDK (= 6.10.0)
  - AliPlayerSDK_iOS_ARTC/AliPlayerSDK (6.10.0)
  - AliyunOSSiOS (2.11.0)
  - AvoidCrash (2.5.2)
  - AZTools (0.0.7)
  - Bugly (2.6.1)
  - CocoaLumberjack (3.8.5):
    - CocoaLumberjack/Core (= 3.8.5)
  - CocoaLumberjack/Core (3.8.5)
  - FLAnimatedImage (1.0.17)
  - FlyVerifyCSDK (1.0.3)
  - GTCommonSDK (*******-noidfa):
    - ZXSDK-Noidfa
  - GTSDK (*******):
    - GTCommonSDK (> *******)
  - HyCharts (1.0.4)
  - iOSDFULibrary (4.11.1):
    - ZIPFoundation (= 0.9.11)
  - JCore (4.6.2)
  - JVerification (3.2.1):
    - JCore (>= 2.1.6)
  - JXCategoryView (1.6.8)
  - JXPagingView/Pager (2.1.3)
  - lottie-ios (4.4.1)
  - Masonry (1.1.0)
  - MBProgressHUD (1.2.0)
  - merit_flutter_framework (0.4.1)
  - MJRefresh (3.7.9)
  - mob_sharesdk (4.4.26):
    - mob_sharesdk/ShareSDK (= 4.4.26)
    - MOBFoundation (>= 3.2.9)
  - mob_sharesdk/ShareSDK (4.4.26):
    - MOBFoundation (>= 3.2.9)
  - mob_sharesdk/ShareSDKPlatforms/Apple (4.4.26):
    - mob_sharesdk/ShareSDK
    - MOBFoundation (>= 3.2.9)
  - mob_sharesdk/ShareSDKPlatforms/QQ (4.4.26):
    - mob_sharesdk/ShareSDK
    - MOBFoundation (>= 3.2.9)
  - mob_sharesdk/ShareSDKPlatforms/SinaWeibo (4.4.26):
    - mob_sharesdk/ShareSDK
    - MOBFoundation (>= 3.2.9)
  - mob_sharesdk/ShareSDKPlatforms/WeChat (4.4.26):
    - mob_sharesdk/ShareSDK
    - MOBFoundation (>= 3.2.9)
  - MOBFoundation (20250217):
    - FlyVerifyCSDK (>= 0.0.7)
  - pop (1.0.12)
  - "POP+MCAnimate (2.0.1)":
    - pop (~> 1.0)
    - "POP+MCAnimate/Animations (= 2.0.1)"
    - "POP+MCAnimate/Group (= 2.0.1)"
    - "POP+MCAnimate/Internal (= 2.0.1)"
    - "POP+MCAnimate/Shorthand (= 2.0.1)"
    - "POP+MCAnimate/Velocity (= 2.0.1)"
  - "POP+MCAnimate/Animations (2.0.1)":
    - pop (~> 1.0)
    - "POP+MCAnimate/Group"
    - "POP+MCAnimate/Internal"
    - "POP+MCAnimate/Shorthand"
    - "POP+MCAnimate/Velocity"
  - "POP+MCAnimate/Group (2.0.1)":
    - pop (~> 1.0)
  - "POP+MCAnimate/Internal (2.0.1)":
    - pop (~> 1.0)
  - "POP+MCAnimate/Shorthand (2.0.1)":
    - pop (~> 1.0)
  - "POP+MCAnimate/Velocity (2.0.1)":
    - pop (~> 1.0)
    - "POP+MCAnimate/Internal"
  - PPBluetoothKit (1.1.2)
  - Reachability (3.7.6)
  - ReactiveCocoa (2.5.2):
    - ReactiveCocoa/UI (= 2.5.2)
  - ReactiveCocoa/Core (2.5.2):
    - ReactiveCocoa/no-arc
  - ReactiveCocoa/no-arc (2.5.2)
  - ReactiveCocoa/UI (2.5.2):
    - ReactiveCocoa/Core
  - Realm (10.36.0):
    - Realm/Headers (= 10.36.0)
  - Realm/Headers (10.36.0)
  - RtsSDK (2.6.2)
  - SDCycleScrollView (1.82):
    - SDWebImage (>= 5.0.0)
  - SDWebImage (5.13.5):
    - SDWebImage/Core (= 5.13.5)
  - SDWebImage/Core (5.13.5)
  - SDWebImageFLPlugin (0.6.0):
    - FLAnimatedImage (>= 1.0.11)
    - SDWebImage/Core (~> 5.10)
  - SnapKit (5.7.1)
  - SocketRocket (0.7.1)
  - SSZipArchive (2.4.3)
  - TZImagePickerController (3.8.8):
    - TZImagePickerController/Basic (= 3.8.8)
    - TZImagePickerController/Location (= 3.8.8)
  - TZImagePickerController/Basic (3.8.8)
  - TZImagePickerController/Location (3.8.8)
  - ZIPFoundation (0.9.11)
  - ZXSDK-Noidfa (3.3.2)

DEPENDENCIES:
  - AFNetworking
  - AliPlayerSDK_iOS (= 6.10.0)
  - AliPlayerSDK_iOS_ARTC (= 6.10.0)
  - AliyunOSSiOS (= 2.11.0)
  - AvoidCrash (~> 2.5.2)
  - AZTools
  - Bugly
  - CocoaLumberjack
  - GTCommonSDK (= *******-noidfa)
  - GTSDK
  - HyCharts
  - iOSDFULibrary (~> 4.11.0)
  - JCore (= 4.6.2)
  - JVerification (= 3.2.1)
  - JXCategoryView (~> 1.6.0)
  - JXPagingView/Pager (~> 2.1.2)
  - lottie-ios
  - Masonry
  - MBProgressHUD
  - merit_flutter_framework (from `https://codeup.aliyun.com/merach/merit-iOS/merit_flutter_framework.git`, tag `0.4.1`)
  - MJRefresh
  - mob_sharesdk (= 4.4.26)
  - mob_sharesdk/ShareSDKPlatforms/Apple
  - mob_sharesdk/ShareSDKPlatforms/QQ
  - mob_sharesdk/ShareSDKPlatforms/SinaWeibo
  - mob_sharesdk/ShareSDKPlatforms/WeChat
  - "POP+MCAnimate"
  - PPBluetoothKit (from `./PPBluetoothKit`)
  - Reachability
  - ReactiveCocoa (from `https://github.com/zhao0/ReactiveCocoa.git`, tag `2.5.2`)
  - Realm (= 10.36.0)
  - RtsSDK (= 2.6.2)
  - SDCycleScrollView
  - SDWebImage (~> 5.13.4)
  - SDWebImageFLPlugin
  - SnapKit
  - SocketRocket
  - SSZipArchive
  - TZImagePickerController

SPEC REPOS:
  https://github.com/CocoaPods/Specs.git:
    - AFNetworking
    - AliPlayerSDK_iOS
    - AliPlayerSDK_iOS_ARTC
    - AliyunOSSiOS
    - AvoidCrash
    - AZTools
    - Bugly
    - CocoaLumberjack
    - FLAnimatedImage
    - FlyVerifyCSDK
    - GTCommonSDK
    - GTSDK
    - HyCharts
    - iOSDFULibrary
    - JCore
    - JVerification
    - JXCategoryView
    - JXPagingView
    - lottie-ios
    - Masonry
    - MBProgressHUD
    - MJRefresh
    - mob_sharesdk
    - MOBFoundation
    - pop
    - "POP+MCAnimate"
    - Reachability
    - Realm
    - RtsSDK
    - SDCycleScrollView
    - SDWebImage
    - SDWebImageFLPlugin
    - SnapKit
    - SocketRocket
    - SSZipArchive
    - TZImagePickerController
    - ZIPFoundation
    - ZXSDK-Noidfa

EXTERNAL SOURCES:
  merit_flutter_framework:
    :git: https://codeup.aliyun.com/merach/merit-iOS/merit_flutter_framework.git
    :tag: 0.4.1
  PPBluetoothKit:
    :path: "./PPBluetoothKit"
  ReactiveCocoa:
    :git: https://github.com/zhao0/ReactiveCocoa.git
    :tag: 2.5.2

CHECKOUT OPTIONS:
  merit_flutter_framework:
    :git: https://codeup.aliyun.com/merach/merit-iOS/merit_flutter_framework.git
    :tag: 0.4.1
  ReactiveCocoa:
    :git: https://github.com/zhao0/ReactiveCocoa.git
    :tag: 2.5.2

SPEC CHECKSUMS:
  AFNetworking: 3bd23d814e976cd148d7d44c3ab78017b744cd58
  AliPlayerSDK_iOS: 707bd2a4b98987ab3ba14187f2bf2cdb2fa67a90
  AliPlayerSDK_iOS_ARTC: 7c5e50b5db8c6908aeb4e20d971adda1b5bcb81f
  AliyunOSSiOS: f0dba98b218e0611bf1a0b77d6d08d10bf6fb299
  AvoidCrash: 28c6916fe19bd2b84126b8dec7cbe61c9a12741d
  AZTools: 6ad488ac0975c0d67b7f3cf80b15efd418ab5623
  Bugly: 217ac2ce5f0f2626d43dbaa4f70764c953a26a31
  CocoaLumberjack: 6a459bc897d6d80bd1b8c78482ec7ad05dffc3f0
  FLAnimatedImage: bbf914596368867157cc71b38a8ec834b3eeb32b
  FlyVerifyCSDK: 9f71ecebbbdf29487fe6c87967c962cbad091abf
  GTCommonSDK: 33e165ceac3fa3e8ba8777a0298855771f3c4354
  GTSDK: 4392fb9f02e370c8dd3e77fa51ffc511b5ac1ac1
  HyCharts: 1a17bf5c77d1eb2f2d85f989dca3444b2ccf6780
  iOSDFULibrary: 1f4139c6c1bb2b2f1f7b127f67dc52504778f8be
  JCore: 024c73b04e110b496e93d8c127ea9cd1ed934e4c
  JVerification: 1053582fd534edd97544da85ba8a8345141069a0
  JXCategoryView: 262d503acea0b1278c79a1c25b7332ffaef4d518
  JXPagingView: afdd2e9af09c90160dd232b970d603cc6e7ddd0e
  lottie-ios: e047b1d2e6239b787cc5e9755b988869cf190494
  Masonry: 678fab65091a9290e40e2832a55e7ab731aad201
  MBProgressHUD: 3ee5efcc380f6a79a7cc9b363dd669c5e1ae7406
  merit_flutter_framework: ddc4b9fbe2219c2a382f94b7b732f815d1287a09
  MJRefresh: ff9e531227924c84ce459338414550a05d2aea78
  mob_sharesdk: 11d86f6a09ba5f0cb633b33b51dd865d7a05115e
  MOBFoundation: d0478ab16224b3aa31fe5323cd3e35f3f0f0b54f
  pop: d582054913807fd11fd50bfe6a539d91c7e1a55a
  "POP+MCAnimate": a99e209148c602512de487f1160a44bf78572024
  PPBluetoothKit: b81f0f9fd167d3157262347eca67f62b138ecb23
  Reachability: fd0ecd23705e2599e4cceeb943222ae02296cbc6
  ReactiveCocoa: 3ff25b1bd992ac79c5c79b26b6c0c1713b715bb2
  Realm: 3fd136cb4c83a927482a7f1612496d37beed3cf5
  RtsSDK: fb60a172aa07d608caf993ca09b520245c982bce
  SDCycleScrollView: a0d74c3384caa72bdfc81470bdbc8c14b3e1fbcf
  SDWebImage: 23d714cd599354ee7906dbae26dff89b421c4370
  SDWebImageFLPlugin: 72efd2cfbf565bc438421abb426f4bcf7b670754
  SnapKit: d612e99e678a2d3b95bf60b0705ed0a35c03484a
  SocketRocket: d4aabe649be1e368d1318fdf28a022d714d65748
  SSZipArchive: fe6a26b2a54d5a0890f2567b5cc6de5caa600aef
  TZImagePickerController: d084a7b97c82d387e7669dd86dc9a9057500aacf
  ZIPFoundation: b1f0de4eed33e74a676f76e12559ab6b75990197
  ZXSDK-Noidfa: d7b8b6efa9220851ac5a32699344243feaaccbc1

PODFILE CHECKSUM: 940cb4a458ab7cfd4d995b7e89ccf82eeda3206f

COCOAPODS: 1.15.2
