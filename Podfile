# Uncomment the next line to define a global platform for your project
platform :ios, '13.0'

# 忽略引入库的所有警告
inhibit_all_warnings!

# 源
source 'https://github.com/CocoaPods/Specs.git'
#source 'https://github.com/aliyun/aliyun-specs.git'
$flutter_debug = false

target 'Student_IOS' do
  #use_frameworks
  # Flutter端混编(debug联调引用本地，release引用pod私有库中framework)
  if $flutter_debug
    flutter_application_path = '../merit_flutter'
    load File.join(flutter_application_path, '.ios', 'Flutter', 'podhelper.rb')
    install_all_flutter_pods(flutter_application_path)
  else
    # 这里在自己联调时可以直接引用打出来的包，测试时命名为 版本号-dev，上线命名规则为 版本号-release
#    pod 'merit_flutter_framework', :git => 'https://codeup.aliyun.com/merach/merit-iOS/merit_flutter_framework.git'
    pod 'merit_flutter_framework', :git => 'https://codeup.aliyun.com/merach/merit-iOS/merit_flutter_framework.git', :tag => '0.4.1'
    #  pod 'XXXFlutterSDK', :path => '/Users/<USER>/Desktop/混编/flutter_lib'  //本地
    #  pod 'Flutter', :podspec => 'some/path/MyApp/Flutter/[build mode]/Flutter.podspec'
  end
  
  pod 'AFNetworking'
  pod 'Masonry'
  pod 'MBProgressHUD'
  pod 'SDWebImage', '~> 5.13.4'
  pod 'SDWebImageFLPlugin' ##sdwebimage 对 flanimatedimage 扩展
  pod 'MJRefresh'
  pod 'HyCharts'
  pod 'Bugly'
  pod 'AvoidCrash', '~> 2.5.2'
  pod 'TZImagePickerController'
  pod 'SDCycleScrollView'
  pod 'SocketRocket'
  pod 'ReactiveCocoa', :git => 'https://github.com/zhao0/ReactiveCocoa.git', :tag => '2.5.2'
  pod 'POP+MCAnimate'
  pod 'Reachability'
  pod 'Realm', '10.36.0'
  pod 'SSZipArchive'
  pod 'CocoaLumberjack' #日志
  pod 'lottie-ios' #json 动画
  pod 'AliyunOSSiOS', '2.11.0' #资源存储
  
  
  #swift
  pod 'SnapKit'
  
  #分享
  pod 'mob_sharesdk', '4.4.26'
  pod 'mob_sharesdk/ShareSDKPlatforms/QQ'
  pod 'mob_sharesdk/ShareSDKPlatforms/SinaWeibo'
  pod 'mob_sharesdk/ShareSDKPlatforms/WeChat' #（微信sdk不带支付的命令）
  pod 'mob_sharesdk/ShareSDKPlatforms/Apple' # apple 登录
  
  #个推推送
  pod 'GTSDK'
  pod 'GTCommonSDK', '*******-noidfa'

  #一键认证
  pod 'JCore', '4.6.2'
  pod 'JVerification', '3.2.1'
  
  #滚动视图
  pod 'JXPagingView/Pager', '~> 2.1.2'
  pod 'JXCategoryView', '~> 1.6.0'
  pod 'AZTools'
  
  ###AliPlayer
  pod 'AliPlayerSDK_iOS', '6.10.0'
  ##支持artc协议或播放低延迟直播（RTS）流
  pod 'AliPlayerSDK_iOS_ARTC', '6.10.0'
  pod 'RtsSDK', '2.6.2'
  
  ###蓝牙
  pod 'iOSDFULibrary', '~> 4.11.0'
  pod 'PPBluetoothKit', :path=>'./PPBluetoothKit'
  
  
  
  ###https://github.com/didi/DoKit/tree/master/iOS
#  #Core subspec作为核心，必须引入，其他几个subspec你可以按照你的需求选择性接入。
#  pod 'DoraemonKit/Core', '~> 3.0.7', :configurations => ['Debug'] #必选
#  #如果你的日志是基于CocoaLumberjack，那你也可以引入WithLogger subspec。
#  pod 'DoraemonKit/WithLogger', '~> 3.0.7', :configurations => ['Debug'] #可选
#  # #如果你要集成Load耗时检测的话，那就请接入WithLoad subspec。
#  pod 'DoraemonKit/WithLoad', '~> 3.0.7', :configurations => ['Debug'] #可选
#  #如果你要使用MLeaksFinder查找内存泄漏的问题的话，那就请接入WithMLeaksFinder subspec。
#  pod 'DoraemonKit/WithMLeaksFinder', '~> 3.0.7', :configurations => ['Debug'] #可选
end



need_otherlinkerflags_frameworks = []

#处理警告⚠️,勿删
post_install do |installer|
  if $flutter_debug
    flutter_post_install(installer) if defined?(flutter_post_install) #pod flutter模块
  end
  installer.pods_project.targets.each do |target|
    target.build_configurations.each do |config|
      if config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'].to_f < 13.0
        config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '13.0'
      end
      config.build_settings['EXPANDED_CODE_SIGN_IDENTITY'] = ""
      config.build_settings['CODE_SIGNING_REQUIRED'] = "NO"
      config.build_settings['CODE_SIGNING_ALLOWED'] = "NO"
      config.build_settings['CODE_SIGN_IDENTITY'] = ''
      config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= [
                '$(inherited)',
                ## dart: PermissionGroup.microphone
                'PERMISSION_MICROPHONE=1',
              ]
      if need_otherlinkerflags_frameworks.include?(target.name)
        config.build_settings['OTHER_LDFLAGS'] = '-Wl, -ld_classic'
      end
    end
  end
  
  bitcode_strip_path = `xcrun --find bitcode_strip`.chop!
  def strip_bitcode_from_framework(bitcode_strip_path, framework_relative_path)
    framework_path = File.join(Dir.pwd, framework_relative_path)
    command = "#{bitcode_strip_path} #{framework_path} -r -o #{framework_path}"
    puts "Stripping bitcode: #{command}"
    system(command)

  end
  framework_paths = [
  "/Pods/AliPlayerSDK_iOS/AliyunMediaDownloader.framework/AliyunMediaDownloader",
  "/Pods/AliPlayerSDK_iOS/AliyunPlayer.framework/AliyunPlayer",
  "/Pods/RtsSDK/RtsSDK.framework/RtsSDK",
  "/Pods/AliPlayerSDK_iOS/alivcffmpeg.framework/alivcffmpeg",
  "/Pods/AliPlayerSDK_iOS_ARTC/artcSource.framework/artcSource",
  ]

  framework_paths.each do |framework_relative_path|
    strip_bitcode_from_framework(bitcode_strip_path, framework_relative_path)
  end
  
  ## Fix for XCode 12.5
  find_and_replace("Pods/FBRetainCycleDetector/FBRetainCycleDetector/Layout/Classes/FBClassStrongLayout.mm",
                   "layoutCache[currentClass] = ivars;",
                   "layoutCache[(id<NSCopying>)currentClass] = ivars;")
end


def find_and_replace(dir, findstr, replacestr)
  Dir[dir].each do |name|
    FileUtils.chmod("+w", name) #add
    text = File.read(name)
    replace = text.gsub(findstr,replacestr)
    if text != replace
      puts "Fix: " + name
      File.open(name, "w") { |file| file.puts replace }
      STDOUT.flush
    end
  end
  Dir[dir + '*/'].each(&method(:find_and_replace))
end


