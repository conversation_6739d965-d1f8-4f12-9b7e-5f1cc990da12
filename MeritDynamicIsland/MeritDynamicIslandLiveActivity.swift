//
//  MeritDynamicIslandLiveActivity.swift
//  MeritDynamicIsland
//
//  Created by Junq on 2024/10/17.
//

import ActivityKit
import WidgetKit
import SwiftUI
import UIKit

enum TrainProductIdType: Int, CaseIterable {
    case none           =  0   //无器械
    case bicycle        =  1   //动感单车
    case treadmill      =  2   //跑步机
    case boat           =  5   //划船机
    case elliptical     =  6   //椭圆机
    case stairclimb     =  7   //爬楼机
    case massagegun     =  9   //筋膜枪
    case skip           =  10  //跳绳
    case power          =  11  //力量站
    case kettle         =  12  //壶铃
    case hoop           =  21  //呼啦圈
    case flsb           =  27  //飞力士棒
    case hand           =  32  //徒手
    
    
    var icon: String {
        switch self {
        case .bicycle:
            return "icon_workout_exer"
        case .treadmill:
            return "icon_workout_run"
        case .boat:
            return "icon_workout_rowing"
        case .elliptical:
            return "Icon_workout_elliptical"
        case .stairclimb:
            return "icon_workout_palouji"
        case .skip:
            return "icon_workout_rope"
        case .power:
            return "icon_workout_power"
        case .kettle:
            return "icon_workout_hl"
        case .hoop:
            return "icon_workout_hlq"
        case .flsb:
            return "icon_workout_flsb"
        default:
            return "icon_workout_none"
        }
    }
    
    var name: String {
        switch self {
        case .bicycle:
            return "动感单车"
        case .treadmill:
            return "跑步机"
        case .boat:
            return "划船机"
        case .elliptical:
            return "椭圆机"
        case .stairclimb:
            return "爬楼机"
        case .massagegun:
            return "筋膜枪"
        case .skip:
            return "跳绳"
        case .power:
            return "力量站"
        case .kettle:
            return "壶铃"
        case .hoop:
            return "呼啦圈"
        case .flsb:
            return "飞力士棒"
        default:
            return "徒手"
        }
    }
}

enum TrainModeType: Int, CaseIterable {
    case none           =  0   //无模型
    case distance       =  1   //定距练
    case time           =  2   //定时练
    
    var isSetTarget: Bool {
        return self != .none
    }
    
    var name: String {
        switch self {
        case .none:
            return "自由训练"
        case .distance:
            return "定距练"
        case .time:
            return "定时练"
        }
    }
}

enum TrainType: Int, CaseIterable {
    case course                =   1       // 课程训练
    case freedom               =   2       // 自由训练
    case realScene             =   6       // 实景视频
    case road                  =   7       // 路线练
    case smart                 =   10      // 智能控阻
    case live                  =   11      // 直播
    case battle                =   12      // 大乱斗课程
    case drama                 =   13      // 剧情课程
}


struct MeritDynamicIslandLiveActivity: Widget {
    var body: some WidgetConfiguration {
        ActivityConfiguration(for: MeritDynamicIslandAttributes.self) { context in
            // Lock screen/banner UI goes here
            LockScreenLiveActivityView(context: context).ignoresSafeArea()
                .onTapGesture {
                addTap(context)
            }
        } dynamicIsland: { context in
            DynamicIsland {
                // 点击灵动岛后展开的样式
                // Expanded UI goes here.  Compose the expanded UI through
                // various regions, like leading/trailing/center/bottom
                DynamicIslandExpandedRegion(.leading) {
                    Image((TrainProductIdType(rawValue: context.state.productId) ?? .none).icon).frame(width: 24, height: 24).onTapGesture {
                        addTap(context)
                    }
                }
                DynamicIslandExpandedRegion(.trailing) {
                    Image("icon_Merit_App")
                        .frame(width: 24, height: 24)
                        .padding(EdgeInsets(top: 0, leading: 0, bottom: 0, trailing: 4)).onTapGesture {
                            addTap(context)
                        }
                }
                DynamicIslandExpandedRegion(.bottom, priority: 1) {
                    let (_, _, isSetTarget, trainingTarget, current, total, isCompleteTarget) = handleData(context);
                    ZStack(alignment: .top){
                        VStack(alignment: .leading, spacing: 0.1) {
                            if isSetTarget {
                                HStack{
                                    ProgressView(value: current, total:total)
                                        .progressViewStyle(LinearProgressViewStyle(tint: Color(hex: "#16D2E3")))
                                        .frame(width: .infinity, height: 8)
                                        .foregroundColor(Color(hex: "#E7FAFC"))
                                        .cornerRadius(3)
                                    Text(trainingTarget)
                                        .font(.system(size: 12))
                                        .foregroundColor(.white)
                                        .frame(height: 20, alignment: .trailing)
                                }
                                .frame(height: 20)
                                .padding(EdgeInsets(top: 0, leading: 0, bottom: 0, trailing: 4))
                            }
                            DynamicIslandTrainDataView(context: context)
                                .frame(height: 74)
                                .padding(EdgeInsets(top: 0, leading: 4, bottom: 0, trailing: 0))
                        }.onTapGesture {
                            addTap(context)
                        }
                        if (isCompleteTarget) {
                            HStack(alignment: .top){
                                Spacer()
                                Image("img_target_end")
                                    .frame(width: 226, height: 97, alignment: .trailing)
                                    .padding(EdgeInsets(top: 0, leading: 0, bottom: 0, trailing: 0))
                            }
                        }
                    }
                }
            } compactLeading: {
                ZStack {
                    Image((TrainProductIdType(rawValue: context.state.productId) ?? .none).icon).frame(width: 24, height: 24)
                }.onTapGesture {
                    addTap(context)
                }
            } compactTrailing: {
                ZStack {
                    if context.state.isSendControl {
                        if context.state.controlType == "2" { // 坡度
                            ZStack {
                                Image("controlbg2")
                                    .frame(width: 24, height: 24)
                                Text(context.state.controlNumber)
                                    .font(.custom("Bebas", size: 13))
                                    .foregroundColor(Color(hex: "#16D2E3"))
                                    .frame(width: 24, height: 24)
                                    .padding(EdgeInsets(top: -5, leading: 0, bottom: 5, trailing: 0))
                            }
                        } else { // 阻力
                            ZStack {
                                Image("controlbg1")
                                    .frame(width: 24, height: 24)
                                Text(context.state.controlNumber)
                                    .font(.custom("Bebas", size: 13))
                                    .foregroundColor(Color(hex: "#16D2E3"))
                                    .frame(width: 24, height: 24)
                                    .padding(EdgeInsets(top: 0, leading: 0, bottom: 0, trailing: 0))
                            }
                        }
                    } else {
                        Text(context.state.trainTime)
                            .font(.system(size: 15))
                            .foregroundColor(.white)
                            .frame(height: 20)
                    }
                }.onTapGesture {
                    addTap(context)
                }
            } minimal: {
                ZStack {
                    Image((TrainProductIdType(rawValue: context.state.productId) ?? .none).icon).frame(width: 24, height: 24)
                }.onTapGesture {
                    addTap(context)
                }
            }
            .keylineTint(Color(red: 9, green: 84, blue: 91)) // #09545B
        }
    }
}

func addTap(_ context: ActivityViewContext<MeritDynamicIslandAttributes>) {
    let userActivity = NSUserActivity(activityType: "com.merit.app.openFromDynamicIsland")
    userActivity.webpageURL = URL(string: "https://itunes.apple.com/cn/app/id1547744135?mt=8") // 这个地址可配置成打开app的页面，目前没有用到
}

struct LockScreenLiveActivityView: View {
    let context: ActivityViewContext<MeritDynamicIslandAttributes>
    var body: some View {
        let (title, icon, isSetTarget, trainingTarget, current, total, isCompleteTarget) = handleData(context);
        VStack(alignment: .leading, spacing: 0.1) {
            HStack(alignment: .top, spacing: 0.1) {
                if (isSetTarget) {
                    VStack(alignment: .leading, spacing: 0.1){
                        HStack(alignment: .top, spacing: 3){
                            Image(icon)
                                .frame(width: 24, height: 24, alignment: .leading)
                                .padding(EdgeInsets(top: 16, leading: 16, bottom: 0, trailing: 0))
                            Text(title)
                                .font(.system(size: 15))
                                .foregroundColor(.white)
                                .frame(maxWidth: .infinity, alignment: .leading)
                                .padding(EdgeInsets(top: 16, leading: 0, bottom: 0, trailing: 0))
                        }
                        HStack(alignment: .center, spacing: 5) {
                            ProgressView(value: current, total: total)
                                .progressViewStyle(LinearProgressViewStyle(tint: Color(hex: "#16D2E3")))
                                .frame(width: 208, height: 8)
                                .foregroundColor(Color(hex: "#E7FAFC"))
                                .cornerRadius(3)
                            Text(trainingTarget)
                                .font(.system(size: 12))
                                .foregroundColor(.white)
                                .frame(height: 20)
                        }
                        .frame(height: 20)
                        .padding(EdgeInsets(top: 4, leading: 16, bottom: 0, trailing: 0))
                    }
                } else {
                    Image(icon)
                        .frame(width: 24, height: 24, alignment: .leading)
                        .padding(EdgeInsets(top: 16, leading: 16, bottom: 0, trailing: 0))
                    Text(title)
                        .font(.system(size: 15))
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .padding(EdgeInsets(top: 16, leading: 0, bottom: 0, trailing: 0))
                }
                Spacer()
                Image("icon_Merit_App")
                    .frame(width: 40, height: 40, alignment: .trailing).padding(EdgeInsets(top: 4, leading: 0, bottom: 0, trailing: 4))
            }
            .frame(height: isSetTarget ? 57 : 47)
            ZStack{
                DynamicIslandTrainDataView(context: context)
                    .frame(height: 97, alignment: .leading)
                    .padding(EdgeInsets(top: 0, leading: 16, bottom: 0, trailing: 16))
                if (isCompleteTarget) {
                    HStack(alignment: .top){
                        Spacer()
                        Image("img_target_end")
                            .frame(width: 226, height: 97, alignment: .trailing)
                            .padding(EdgeInsets(top: 0, leading: 0, bottom: 0, trailing: 0))
                    }
                }
            }.frame(height: 97, alignment: .leading)
        }
        .activityBackgroundTint(Color(hex: "#05182C"))
    }
        
}

func handleData(_ context: ActivityViewContext<MeritDynamicIslandAttributes>) -> (String, String, Bool, String, Double, Double, Bool) { // title, icon, isSetTarget, current, total, isCompleteTarget
    let productType = TrainProductIdType(rawValue: context.state.productId) ?? .none
    let trainType = TrainType(rawValue: context.state.trainingType) ?? .freedom
    let trainMode = TrainModeType(rawValue: context.state.trainingMode) ?? .none
    var isSetTarget = false
    var isCompleteTarget = false
    var current = 0.0
    var total = 0.0
    var trainingTarget: String = ""
    
    if trainType == .freedom { // 自由训练
        if trainMode != .none { // 定时练、定距练
            isSetTarget = true
            if trainMode == .time {
                let timeArr = context.state.trainingTargetCurrentNum.split(separator: ":");
                let timeCa = (Int(timeArr[0]) ?? 0) * 60 + (Int(timeArr[1]) ?? 0)
                current = Double(timeCa)
            } else {
                if (productType == .bicycle ||
                    productType == .treadmill ||
                    productType == .boat ||
                    productType == .elliptical ||
                    productType == .stairclimb){
                    current = Double(context.state.trainDistance) ?? 0.0
                } else {
                    current = Double(context.state.trainNum) ?? 0.0
                }
            }
            trainingTarget = "目标 \(context.state.trainingTarget)"
            total = Double(context.state.trainingTargetNum) ?? 0.0
            isCompleteTarget = context.state.isFinishTarget || (current >= total && total > 0)
        }
    } else if trainType == .course || trainType == .smart { // 录播、智能控组
        if context.state.trainTime != "--" { // 没有数据的时候进度条不展示
            isSetTarget = true
            let timeArr = context.state.trainingTargetCurrentNum.split(separator: ":");
            let timeCa = (Int(timeArr[0]) ?? 0) * 60 + (Int(timeArr[1]) ?? 0)
            current = Double(timeCa)
            total = Double(context.state.trainingTargetNum) ?? 0.0
            trainingTarget = "课程 \(context.state.trainingTarget)"
            isCompleteTarget = context.state.isFinishTarget || (current >= total && total > 0)
        }
    } else if trainType != .road { // 其余课程模式，也要展示课程已结束
        isCompleteTarget = context.state.isFinishTarget
    }
    
    var title: String = ""
    switch trainType {
    case .freedom:
        title = isCompleteTarget ? " 厉害👍 目标完成了！" : " Merit-\(productType.name)\(trainMode.name)"
    case .road:
        title = " Merit-\(productType.name)路线练"
   default:
        title = isCompleteTarget ? " 厉害👍 课程完成了！" : " \(context.state.courseName)"
    }
    return (title, productType.icon, isSetTarget, trainingTarget, current, total, isCompleteTarget)
}


func handleTrainData(_ context: ActivityViewContext<MeritDynamicIslandAttributes>) -> [[String: String]] { // title, icon, isSetTarget, current, total, isCompleteTarget
    let productType = TrainProductIdType(rawValue: context.state.productId) ?? .none
    var result:[[String: String]] = []
    switch productType {
    case .bicycle, .treadmill, .boat, .elliptical, .stairclimb:
        result = [["距离(公里)": context.state.trainDistance], ["消耗(千卡)": context.state.trainKcal]]
    case .skip, .kettle, .flsb:
        result = [["个数": context.state.trainNum], ["消耗(千卡)": context.state.trainKcal]]
    case .power:
        result = [["次数": context.state.trainNum], ["消耗(千卡)": context.state.trainKcal]]
    case .hoop:
        result = [["圈数": context.state.trainNum], ["消耗(千卡)": context.state.trainKcal]]
    default:
        result = []
    }
    result.append(["心率": (Double(context.state.trainRate) ?? 0) > 0 ? context.state.trainRate : "--" ])
    return result
}

struct DynamicIslandTrainDataView: View {
    let context: ActivityViewContext<MeritDynamicIslandAttributes>
    let trainDatas: [[String: String]]
    
    init(context: ActivityViewContext<MeritDynamicIslandAttributes>) {
        self.context = context
        self.trainDatas = handleTrainData(context)
    }
    
    var body: some View {
        HStack(alignment: .top) {
            VStack(alignment: .leading, spacing: 0.1) {
                Text(context.state.trainTime)
                    .bold(true)
                    .font(.custom("Bebas", size: 44))
                    .foregroundColor(Color(hex: "#16D2E3"))
                    .frame(width: 112, height: 53, alignment: .bottomLeading)
                Text("训练时长")
                    .opacity(0.5)
                    .font(.system(size: 12))
                    .foregroundColor(Color(hex: "#E7FAFC"))
                    .frame(width: 112, height: 16, alignment: .leading)
            }
            .padding(EdgeInsets(top: 7, leading: 0, bottom: 16, trailing: 0))
            HStack(alignment: .top, spacing: 0.1)  {
                ForEach(trainDatas, id: \.self) { trainData in
                    TrainDataItemView(data: trainData)
                    Spacer()
                }
            }.padding(EdgeInsets(top: 23, leading: 0, bottom: 16, trailing: 0))
        }
    }
}

struct TrainDataItemView: View {
    let data: [String: String]
    
    var body: some View {
        VStack(spacing: 3) {
            Text(data.values.first ?? "")
               .font(.custom("Bebas", size: 28))
               .foregroundColor(.white)
               .frame(height: 34, alignment: .bottom)
            Text(data.keys.first ?? "")
               .opacity(0.5)
               .font(.system(size: 12))
               .foregroundColor(Color(hex: "#E7FAFC"))
               .frame(height: 16, alignment: .center)
        }
    }
}

extension Color {
    init(hex: String) {
        let scanner = Scanner(string: hex)
        _ = scanner.scanString("#") // 跳过'#'字符
        
        var rgbValue: UInt64 = 0
        scanner.scanHexInt64(&rgbValue)
        
        let r = Double((rgbValue & 0xFF0000) >> 16) / 255.0
        let g = Double((rgbValue & 0xFF00) >> 8) / 255.0
        let b = Double(rgbValue & 0xFF) / 255.0
        
        self.init(red: r, green: g, blue: b)
    }
}
